!/bin/bash

'''
# Streamlit konténer kezelése
echo "<PERSON><PERSON>és a Streamlit konténerrel..."
docker-compose stop streamlit
docker-compose rm -f streamlit
docker-compose build streamlit
#docker-compose up -d streamlit
docker-compose up streamlit

# Backend konténer kezelése
echo "Kezelés a Backend konténerrel..."
docker-compose stop backend
docker-compose rm -f backend
docker-compose build backend
#docker-compose up -d backend
docker-compose up backend

echo "Műveletek befejezve!"
'''

# Konténerek leállítása és eltávolítása
echo "Konténerek leállítása és eltávolítása..."
docker-compose stop backend streamlit
docker-compose rm -f backend streamlit

# Konténerek újraépítése
echo "Konténerek újraépítése..."
docker-compose build backend streamlit

# Backend indítása először
echo "Backend indítása..."
docker-compose up -d backend

# <PERSON><PERSON> v<PERSON>, hogy a backend biztosan elinduljon
echo "Várakozás a backend indulására..."
sleep 5

# Streamlit indítása
echo "Streamlit indítása..."
docker-compose up -d streamlit

# Mindkét konténer logjainak követése
echo "Logok követése..."
docker-compose logs -f backend streamlit

echo "Műveletek befejezve!"
