"""
CRUD operations for user saved filters
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.user_saved_filter import UserSavedFilter
from app.schemas.saved_filter import SavedFilterCreate, SavedFilterUpdate


def get(db: Session, filter_id: int) -> Optional[UserSavedFilter]:
    """
    Get a saved filter by ID
    
    Args:
        db: Database session
        filter_id: ID of the filter to retrieve
        
    Returns:
        Optional[UserSavedFilter]: The saved filter or None if not found
    """
    return db.query(UserSavedFilter).filter(UserSavedFilter.id == filter_id).first()


def get_multi(
    db: Session,
    user_id: int,
    filter_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[UserSavedFilter]:
    """
    Get multiple saved filters for a user
    
    Args:
        db: Database session
        user_id: User ID
        filter_type: Optional filter type to filter by
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)
        
    Returns:
        List[UserSavedFilter]: List of saved filters
    """
    query = db.query(UserSavedFilter).filter(UserSavedFilter.user_id == user_id)
    
    if filter_type:
        query = query.filter(UserSavedFilter.filter_type == filter_type)
    
    return query.order_by(UserSavedFilter.name).offset(skip).limit(limit).all()


def get_default(db: Session, user_id: int, filter_type: str) -> Optional[UserSavedFilter]:
    """
    Get the default saved filter for a user and filter type
    
    Args:
        db: Database session
        user_id: User ID
        filter_type: Filter type
        
    Returns:
        Optional[UserSavedFilter]: The default saved filter or None if not found
    """
    return db.query(UserSavedFilter).filter(
        and_(
            UserSavedFilter.user_id == user_id,
            UserSavedFilter.filter_type == filter_type,
            UserSavedFilter.is_default == True
        )
    ).first()


def create(db: Session, obj_in: SavedFilterCreate, user_id: int) -> UserSavedFilter:
    """
    Create a new saved filter
    
    Args:
        db: Database session
        obj_in: Filter data to create
        user_id: User ID
        
    Returns:
        UserSavedFilter: The created saved filter
    """
    db_obj = UserSavedFilter(
        user_id=user_id,
        name=obj_in.name,
        description=obj_in.description,
        filter_type=obj_in.filter_type,
        is_default=obj_in.is_default,
        filter_data=obj_in.filter_data
    )
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session,
    db_obj: UserSavedFilter,
    obj_in: SavedFilterUpdate
) -> UserSavedFilter:
    """
    Update a saved filter
    
    Args:
        db: Database session
        db_obj: Existing saved filter to update
        obj_in: New filter data
        
    Returns:
        UserSavedFilter: The updated saved filter
    """
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def remove(db: Session, filter_id: int) -> Optional[UserSavedFilter]:
    """
    Remove a saved filter
    
    Args:
        db: Database session
        filter_id: ID of the filter to remove
        
    Returns:
        Optional[UserSavedFilter]: The removed filter or None if not found
    """
    db_obj = get(db, filter_id)
    if db_obj:
        db.delete(db_obj)
        db.commit()
    return db_obj


def set_as_default(db: Session, db_obj: UserSavedFilter) -> UserSavedFilter:
    """
    Set a saved filter as default and unset any other default filters of the same type
    
    Args:
        db: Database session
        db_obj: Saved filter to set as default
        
    Returns:
        UserSavedFilter: The updated saved filter
    """
    # Unset any existing default filters of the same type
    existing_defaults = db.query(UserSavedFilter).filter(
        and_(
            UserSavedFilter.user_id == db_obj.user_id,
            UserSavedFilter.filter_type == db_obj.filter_type,
            UserSavedFilter.is_default == True,
            UserSavedFilter.id != db_obj.id
        )
    ).all()
    
    for default_filter in existing_defaults:
        default_filter.is_default = False
        db.add(default_filter)
    
    # Set the current filter as default
    db_obj.is_default = True
    db.add(db_obj)
    
    db.commit()
    db.refresh(db_obj)
    return db_obj