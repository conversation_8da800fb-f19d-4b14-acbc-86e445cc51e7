from typing import List, Optional, Union, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.models.notification import Notification
from app.models.user import User
from app.schemas.notification import NotificationCreate, NotificationUpdate


def create_notification(db: Session, obj_in: NotificationCreate) -> Notification:
    """
    Új értesítés létrehozása.
    
    Args:
        db: <PERSON>z adatbázis session
        obj_in: Az új <PERSON>rte<PERSON>íté<PERSON> adatai
        
    Returns:
        Notification: Az <PERSON>jonnan létrehozott értesítés
    """
    # Kötelező validáció: related_entity_type mezőnek ki kell lennie töltve
    if not obj_in.related_entity_type or not str(obj_in.related_entity_type).strip():
        import logging
        logging.error("Értesítés létrehozása sikertelen: a related_entity_type mező kötelező!")
        raise ValueError("A related_entity_type mező kitöltése kötelező az értesítésekhez!")

    db_obj = Notification(
        user_id=obj_in.user_id,
        type=obj_in.type,
        message=obj_in.message,
        detail=obj_in.detail,
        target_roles=obj_in.target_roles,
        related_entity_type=obj_in.related_entity_type,
        related_entity_id=obj_in.related_entity_id,
        is_read=False
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_notification(db: Session, notification_id: int) -> Optional[Notification]:
    """
    Értesítés lekérése ID alapján.
    
    Args:
        db: Az adatbázis session
        notification_id: Az értesítés azonosítója
        
    Returns:
        Notification: A kért értesítés vagy None
    """
    return db.query(Notification).filter(Notification.id == notification_id).first()


def get_notifications_for_user(
    db: Session, 
    user_id: int, 
    skip: int = 0, 
    limit: int = 100,
    include_read: bool = False
) -> List[Notification]:
    """
    Felhasználóhoz tartozó értesítések lekérése.
    
    Args:
        db: Az adatbázis session
        user_id: A felhasználó azonosítója
        skip: Hány rekordot hagyjunk ki (lapozáshoz)
        limit: Maximum hány rekordot kérünk le (lapozáshoz)
        include_read: Olvastott értesítések megjelenjenek-e
        
    Returns:
        List[Notification]: Az értesítések listája
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return []
    
    # Lekérjük a felhasználó szerepkörét
    user_role = user.role
    
    query = db.query(Notification)
    
    # Szűrés: vagy a felhasználóhoz tartozik, vagy általános és a szerepkörhöz illeszkedik
    role_filter = Notification.target_roles.is_(None) | Notification.target_roles.contains(user_role)
    user_filter = (Notification.user_id == user_id) | (Notification.user_id.is_(None))
    
    query = query.filter(and_(role_filter, user_filter))
    
    # Csak olvasatlan értesítések, ha szükséges
    if not include_read:
        query = query.filter(Notification.is_read == False)
    
    # Rendezés és lapozás
    return query.order_by(Notification.created_at.desc()).offset(skip).limit(limit).all()


def get_all_notifications(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    include_read: bool = False
) -> List[Notification]:
    """
    Összes értesítés lekérése (admin felhasználóknak).
    
    Args:
        db: Az adatbázis session
        skip: Hány rekordot hagyjunk ki (lapozáshoz)
        limit: Maximum hány rekordot kérünk le (lapozáshoz)
        include_read: Olvastott értesítések megjelenjenek-e
        
    Returns:
        List[Notification]: Az értesítések listája
    """
    query = db.query(Notification)
    
    # Csak olvasatlan értesítések, ha szükséges
    if not include_read:
        query = query.filter(Notification.is_read == False)
    
    # Rendezés és lapozás
    return query.order_by(Notification.created_at.desc()).offset(skip).limit(limit).all()


def count_notifications_for_user(
    db: Session, 
    user_id: int,
    include_read: bool = False
) -> int:
    """
    Felhasználóhoz tartozó értesítések számának lekérése.
    
    Args:
        db: Az adatbázis session
        user_id: A felhasználó azonosítója
        include_read: Olvastott értesítések számoljanak-e
        
    Returns:
        int: Az értesítések száma
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return 0
    
    # Lekérjük a felhasználó szerepkörét
    user_role = user.role
    
    query = db.query(Notification)
    
    # Szűrés: vagy a felhasználóhoz tartozik, vagy általános és a szerepkörhöz illeszkedik
    role_filter = Notification.target_roles.is_(None) | Notification.target_roles.contains(user_role)
    user_filter = (Notification.user_id == user_id) | (Notification.user_id.is_(None))
    
    query = query.filter(and_(role_filter, user_filter))
    
    # Csak olvasatlan értesítések, ha szükséges
    if not include_read:
        query = query.filter(Notification.is_read == False)
    
    return query.count()


def update_notification(
    db: Session, 
    notification_id: int, 
    obj_in: Union[NotificationUpdate, Dict[str, Any]]
) -> Optional[Notification]:
    """
    Értesítés frissítése.
    
    Args:
        db: Az adatbázis session
        notification_id: Az értesítés azonosítója
        obj_in: A frissítendő adatok
        
    Returns:
        Notification: A frissített értesítés vagy None
    """
    db_obj = get_notification(db, notification_id)
    if not db_obj:
        return None
    
    # Konvertálás szótárrá, ha szükséges
    update_data = obj_in if isinstance(obj_in, dict) else obj_in.dict(exclude_unset=True)
    
    # Frissítés
    for field in update_data:
        if hasattr(db_obj, field):
            setattr(db_obj, field, update_data[field])
    
    db.commit()
    db.refresh(db_obj)
    return db_obj


def mark_notification_read(db: Session, notification_id: int) -> Optional[Notification]:
    """
    Értesítés olvasottnak jelölése.
    
    Args:
        db: Az adatbázis session
        notification_id: Az értesítés azonosítója
        
    Returns:
        Notification: A frissített értesítés vagy None
    """
    return update_notification(db, notification_id, NotificationUpdate(is_read=True))


def mark_all_notifications_read(db: Session, user_id: int) -> int:
    """
    Felhasználó összes értesítésének olvasottnak jelölése.
    
    Args:
        db: Az adatbázis session
        user_id: A felhasználó azonosítója
        
    Returns:
        int: A frissített értesítések száma
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return 0
    
    # Lekérjük a felhasználó szerepkörét
    user_role = user.role
    
    # Szűrés: vagy a felhasználóhoz tartozik, vagy általános és a szerepkörhöz illeszkedik
    role_filter = Notification.target_roles.is_(None) | Notification.target_roles.contains(user_role)
    user_filter = (Notification.user_id == user_id) | (Notification.user_id.is_(None))
    
    result = db.query(Notification).filter(
        and_(
            role_filter,
            user_filter,
            Notification.is_read == False
        )
    ).update({"is_read": True})
    
    db.commit()
    return result


def create_system_notification(
    db: Session,
    message: str,
    type: str = "info",
    detail: Optional[str] = None,
    target_roles: Optional[str] = None,
    related_entity_type: Optional[str] = None,
    related_entity_id: Optional[int] = None
) -> Notification:
    """
    Rendszerértesítés létrehozása (nincs konkrét felhasználóhoz kötve).
    
    Args:
        db: Az adatbázis session
        message: Az értesítés szövege
        type: Az értesítés típusa (info, success, warning, error, update)
        detail: Részletes leírás
        target_roles: Célzott szerepkörök (vesszővel elválasztva)
        related_entity_type: Kapcsolódó entitás típusa
        related_entity_id: Kapcsolódó entitás azonosítója
        
    Returns:
        Notification: Az újonnan létrehozott értesítés
    """
    notification = NotificationCreate(
        user_id=None,
        type=type,
        message=message,
        detail=detail,
        target_roles=target_roles,
        related_entity_type=related_entity_type,
        related_entity_id=related_entity_id
    )
    return create_notification(db, notification) 