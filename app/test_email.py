#!/usr/bin/env python3
"""Test email sending with current SMTP configuration"""

import asyncio
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

async def test_email():
    # SMTP configuration
    SMTP_HOST = "smtp.mail.me.com"
    SMTP_PORT = 587
    SMTP_USER = "<EMAIL>"
    SMTP_PASSWORD = "cznb-qmns-bzrx-nfrf"
    FROM_EMAIL = "<EMAIL>"
    TO_EMAIL = "<EMAIL>"
    
    # Create message
    message = MIMEMultipart("alternative")
    message["Subject"] = "Test Email from Termelo System"
    message["From"] = f"POM <{FROM_EMAIL}>"
    message["To"] = TO_EMAIL
    
    # Email content
    html_content = """
    <html>
    <body>
        <h2>Test Email</h2>
        <p>This is a test email from the Termelo system.</p>
        <p>If you receive this, the SMTP configuration is working correctly!</p>
        <p>Sent via iCloud SMTP.</p>
    </body>
    </html>
    """
    
    html_part = MIMEText(html_content, "html")
    message.attach(html_part)
    
    try:
        # Send email
        print(f"Connecting to {SMTP_HOST}:{SMTP_PORT}...")
        smtp_client = aiosmtplib.SMTP(
            hostname=SMTP_HOST,
            port=SMTP_PORT,
            use_tls=False,  # Don't use direct TLS
            start_tls=True  # Use STARTTLS
        )
        
        async with smtp_client:
            print("Connected! Authenticating...")
            await smtp_client.login(SMTP_USER, SMTP_PASSWORD)
            print("Authenticated! Sending email...")
            await smtp_client.send_message(message)
            print(f"✅ Email sent successfully to {TO_EMAIL}!")
            
    except Exception as e:
        print(f"❌ Error sending email: {type(e).__name__}: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_email())