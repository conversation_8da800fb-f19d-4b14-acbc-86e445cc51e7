"""
Termék<PERSON> kapcsolatos Pydantic sémák
"""
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field


# Termékkategória sémák

class ProductCategoryBase(BaseModel):
    """Termékkategória alap séma"""
    name: str = Field(..., description="Kategória neve")
    description: Optional[str] = Field(None, description="Leírás")


class ProductCategoryCreate(ProductCategoryBase):
    """Termékkategória létrehozási séma"""
    pass


class ProductCategoryUpdate(ProductCategoryBase):
    """Termékkategória frissítési séma"""
    name: Optional[str] = Field(None, description="Kategória neve")
    description: Optional[str] = Field(None, description="Leírás")


class ProductCategoryResponse(ProductCategoryBase):
    """Termékkategória válasz séma"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# Terméktípus sémák

class ProductTypeBase(BaseModel):
    """Terméktípus alap séma"""
    name: str = Field(..., description="Típus neve")
    description: Optional[str] = Field(None, description="Leírás")
    has_quality_grades: bool = Field(True, description="Van-e minőségi besorolás")


class ProductTypeCreate(ProductTypeBase):
    """Terméktípus létrehozási séma"""
    category_id: int = Field(..., description="Kategória azonosító")


class ProductTypeUpdate(ProductTypeBase):
    """Terméktípus frissítési séma"""
    name: Optional[str] = Field(None, description="Típus neve")
    description: Optional[str] = Field(None, description="Leírás")
    has_quality_grades: Optional[bool] = Field(None, description="Van-e minőségi besorolás")
    category_id: Optional[int] = Field(None, description="Kategória azonosító")


class ProductTypeResponse(ProductTypeBase):
    """Terméktípus válasz séma"""
    id: int
    category_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class ProductTypeDetailResponse(ProductTypeResponse):
    """Terméktípus részletes válasz séma (kategóriával együtt)"""
    category: ProductCategoryResponse
    
    class Config:
        orm_mode = True


# Minőségi besorolás sémák

class QualityGradeBase(BaseModel):
    """Minőségi besorolás alap séma"""
    name: str = Field(..., description="Besorolás neve")
    min_shoulder_diameter: Optional[float] = Field(None, description="Minimális vállátmérő (mm)")
    max_shoulder_diameter: Optional[float] = Field(None, description="Maximális vállátmérő (mm)")
    min_length: Optional[float] = Field(None, description="Minimális terméshossz (mm)")
    max_length: Optional[float] = Field(None, description="Maximális terméshossz (mm)")
    description: Optional[str] = Field(None, description="Leírás")


class QualityGradeCreate(QualityGradeBase):
    """Minőségi besorolás létrehozási séma"""
    product_type_id: int = Field(..., description="Terméktípus azonosító")


class QualityGradeUpdate(QualityGradeBase):
    """Minőségi besorolás frissítési séma"""
    name: Optional[str] = Field(None, description="Besorolás neve")
    min_shoulder_diameter: Optional[float] = Field(None, description="Minimális vállátmérő (mm)")
    max_shoulder_diameter: Optional[float] = Field(None, description="Maximális vállátmérő (mm)")
    min_length: Optional[float] = Field(None, description="Minimális terméshossz (mm)")
    max_length: Optional[float] = Field(None, description="Maximális terméshossz (mm)")
    description: Optional[str] = Field(None, description="Leírás")
    product_type_id: Optional[int] = Field(None, description="Terméktípus azonosító")


class QualityGradeResponse(QualityGradeBase):
    """Minőségi besorolás válasz séma"""
    id: int
    product_type_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class QualityGradeDetailResponse(QualityGradeResponse):
    """Minőségi besorolás részletes válasz séma (terméktípussal együtt)"""
    product_type: ProductTypeResponse
    
    class Config:
        orm_mode = True


# Összetett válasz sémák

class ProductCategoryWithTypesResponse(ProductCategoryResponse):
    """Termékkategória válasz séma a hozzá tartozó típusokkal"""
    product_types: List[ProductTypeResponse]
    
    class Config:
        orm_mode = True


class ProductTypeWithGradesResponse(ProductTypeResponse):
    """Terméktípus válasz séma a hozzá tartozó minőségi besorolásokkal"""
    quality_grades: List[QualityGradeResponse]
    
    class Config:
        orm_mode = True