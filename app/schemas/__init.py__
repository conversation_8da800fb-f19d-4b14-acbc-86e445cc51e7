"""
Schema importok exportálása
"""
from app.schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserChangePassword,
    UserPasswordReset, UserPasswordResetConfirm, UserRoleUpdate,
    UserDefaultSettingsCreate, UserDefaultSettingsResponse,
    Login, Token, TokenData
)

from app.schemas.product import (
    ProductCategoryCreate, ProductCategoryUpdate, ProductCategoryResponse,
    ProductTypeCreate, ProductTypeUpdate, ProductTypeResponse, ProductTypeDetailResponse,
    QualityGradeCreate, QualityGradeUpdate, QualityGradeResponse, QualityGradeDetailResponse,
    ProductCategoryWithTypesResponse, ProductTypeWithGradesResponse
)

from app.schemas.offer import (
    OfferCreate, OfferCreateForUser, OfferUpdate, OfferConfirm, OfferResponse, OfferDetailResponse,
    OfferLogCreate, OfferLogResponse, OfferFilter, CalendarOfferResponse, OfferStatistics
)

from app.schemas.saved_filter import (
    SavedFilterCreate, SavedFilterUpdate, SavedFilterResponse
)

# Exportáljuk az összes sémát
__all__ = [
    # User sémák
    "UserCreate", "UserUpdate", "UserResponse", "UserChangePassword",
    "UserPasswordReset", "UserPasswordResetConfirm", "UserRoleUpdate",
    "UserDefaultSettingsCreate", "UserDefaultSettingsResponse",
    "Login", "Token", "TokenData",
    
    # Product sémák
    "ProductCategoryCreate", "ProductCategoryUpdate", "ProductCategoryResponse",
    "ProductTypeCreate", "ProductTypeUpdate", "ProductTypeResponse", "ProductTypeDetailResponse",
    "QualityGradeCreate", "QualityGradeUpdate", "QualityGradeResponse", "QualityGradeDetailResponse",
    "ProductCategoryWithTypesResponse", "ProductTypeWithGradesResponse",
    
    # Offer sémák
    "OfferCreate", "OfferCreateForUser", "OfferUpdate", "OfferConfirm", "OfferResponse", "OfferDetailResponse",
    "OfferLogCreate", "OfferLogResponse", "OfferFilter", "CalendarOfferResponse", "OfferStatistics",
    
    # Saved Filter sémák
    "SavedFilterCreate", "SavedFilterUpdate", "SavedFilterResponse"
]