from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, validator


class NotificationBase(BaseModel):
    """Értesítés alapsémája"""
    type: str = Field(..., description="Értesítés típusa: info, success, warning, error, update")
    message: str = Field(..., description="Értesítés üzenete")
    detail: Optional[str] = Field(None, description="Részletes leírás")
    target_roles: Optional[str] = Field(None, description="Célzott szerepkörök (vesszővel elválasztva)")
    related_entity_type: Optional[str] = Field(None, description="Kapcsolódó entitás típusa")
    related_entity_id: Optional[int] = Field(None, description="Kapcsolódó entitás azonosítója")
    
    @validator('type')
    def validate_type(cls, v):
        """Értesítés típusának ellenőrzése"""
        valid_types = ["info", "success", "warning", "error", "update"]
        if v not in valid_types:
            raise ValueError(f"Az értesítés típusa csak a következők egyike lehet: {', '.join(valid_types)}")
        return v


class NotificationCreate(NotificationBase):
    """Értesítés létrehozásának sémája"""
    user_id: Optional[int] = Field(None, description="Címzett felhasználó azonosítója")


class NotificationUpdate(BaseModel):
    """Értesítés frissítésének sémája"""
    is_read: Optional[bool] = Field(None, description="Olvasottsági állapot")


class NotificationInDBBase(NotificationBase):
    """Adatbázisban tárolt értesítés sémája"""
    id: int
    user_id: Optional[int]
    is_read: bool = False
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class Notification(NotificationInDBBase):
    """Értesítés válasz sémája"""
    pass


class NotificationList(BaseModel):
    """Értesítések listája válasz séma"""
    items: List[Notification]
    total: int 