"""
Ajánlattal kapcsolatos Pydantic sémák
"""
from datetime import date, datetime
from decimal import Decimal
from typing import Optional, List, Any, Union

from pydantic import BaseModel, Field, validator

from app.schemas.user import UserResponse
from app.schemas.product import ProductTypeResponse, QualityGradeResponse


# Ajánlat sémák

class OfferBase(BaseModel):
    """Ajánlat alap séma"""
    product_type_id: int = Field(..., description="Terméktípus azonosító")
    quality_grade_id: Optional[int] = Field(None, description="Minőségi besorolás azonosító, ha alkalmazható")
    quantity_value: Decimal = Field(..., description="Mennyiség értéke")
    quantity_unit: str = Field(..., description="Mennyiségi egység (kg, tonna, db)")
    delivery_date: date = Field(..., description="Beszállítás tervezett dátuma")
    note: Optional[str] = Field(None, description="Megjegyzés")
    
    @validator('quantity_value')
    def check_positive_quantity(cls, v):
        """Pozitív mennyiség ellenőrzése"""
        if v <= 0:
            raise ValueError("A mennyiségnek pozitívnak kell lennie")
        return v
    
    @validator('quantity_unit')
    def check_valid_unit(cls, v):
        """Érvényes mennyiségi egység ellenőrzése"""
        valid_units = ['kg', 'tonna', 'db']
        if v not in valid_units:
            raise ValueError(f"A mennyiségi egységnek a következők egyikének kell lennie: {', '.join(valid_units)}")
        return v
    
    @validator('delivery_date')
    def check_future_date(cls, v):
        """Jövőbeli dátum ellenőrzése"""
        # A rendszeridő helyett csak az évet ellenőrizzük
        # Ha a dátum 2020 előtti, biztos, hogy múltbeli
        if v is not None and v.year < 2024:
            raise ValueError(f"A beszállítás dátuma nem lehet múltbeli (2024 előtti). Megadott: {v}")
        return v


class OfferCreate(OfferBase):
    """Ajánlat létrehozási séma"""
    pass


class OfferCreateForUser(OfferBase):
    """Ajánlat létrehozása másik felhasználó nevében (ügyintéző funkció)"""
    user_id: int = Field(..., description="Felhasználó azonosító, akinek a nevében létrehozzuk")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": 1,
                "product_type_id": 1,
                "quality_grade_id": 1,
                "quantity_value": 1000,
                "quantity_unit": "kg",
                "delivery_date": "2023-12-15",
                "note": "Minta ajánlat"
            }
        }


class OfferUpdate(BaseModel):
    """Ajánlat frissítési séma"""
    product_type_id: Optional[int] = Field(None, description="Terméktípus azonosító")
    quality_grade_id: Optional[int] = Field(None, description="Minőségi besorolás azonosító, ha alkalmazható")
    quantity_value: Optional[Decimal] = Field(None, description="Mennyiség értéke")
    quantity_unit: Optional[str] = Field(None, description="Mennyiségi egység (kg, tonna, db)")
    delivery_date: Optional[date] = Field(None, description="Beszállítás tervezett dátuma")
    note: Optional[str] = Field(None, description="Megjegyzés")
    
    @validator('quantity_value')
    def check_positive_quantity(cls, v):
        """Pozitív mennyiség ellenőrzése"""
        if v is not None and v <= 0:
            raise ValueError("A mennyiségnek pozitívnak kell lennie")
        return v
    
    @validator('quantity_unit')
    def check_valid_unit(cls, v):
        """Érvényes mennyiségi egység ellenőrzése"""
        if v is not None:
            valid_units = ['kg', 'tonna', 'db']
            if v not in valid_units:
                raise ValueError(f"A mennyiségi egységnek a következők egyikének kell lennie: {', '.join(valid_units)}")
        return v
    
    @validator('delivery_date')
    def check_future_date(cls, v):
        """Jövőbeli dátum ellenőrzése"""
        # A rendszeridő helyett csak az évet ellenőrizzük
        # Ha a dátum 2020 előtti, biztos, hogy múltbeli
        if v is not None and v.year < 2024:
            raise ValueError(f"A beszállítás dátuma nem lehet múltbeli (2024 előtti). Megadott: {v}")
        return v


class OfferConfirm(BaseModel):
    """Ajánlat visszaigazolás séma (ügyintéző által)"""
    confirmed_quantity: Decimal = Field(..., description="Visszaigazolt mennyiség kg-ban")
    confirmed_price: Decimal = Field(..., description="Visszaigazolt egységár Ft/kg-ban")
    note: Optional[str] = Field(None, description="Megjegyzés")
    
    @validator('confirmed_quantity')
    def check_positive_quantity(cls, v):
        """Pozitív mennyiség ellenőrzése"""
        if v <= 0:
            raise ValueError("A visszaigazolt mennyiségnek pozitívnak kell lennie")
        return v
    
    @validator('confirmed_price')
    def check_positive_price(cls, v):
        """Pozitív ár ellenőrzése"""
        if v <= 0:
            raise ValueError("A visszaigazolt árnak pozitívnak kell lennie")
        return v


class OfferResponse(OfferBase):
    """Ajánlat válasz séma"""
    id: int
    user_id: int
    created_by_user_id: int
    status: str
    confirmed_quantity: Optional[Decimal]
    confirmed_price: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    
    # Backward compatibility property
    @property
    def quantity_in_kg(self):
        """Backward compatibility for quantity_in_kg"""
        return self.quantity_value
    
    class Config:
        orm_mode = True


class OfferDetailResponse(OfferResponse):
    """Ajánlat részletes válasz séma"""
    user: UserResponse
    created_by_user: UserResponse
    product_type: ProductTypeResponse
    quality_grade: Optional[QualityGradeResponse]
    
    class Config:
        orm_mode = True


# Ajánlat napló sémák

class OfferLogBase(BaseModel):
    """Ajánlat napló alap séma"""
    offer_id: int
    old_status: Optional[str]
    new_status: str
    note: Optional[str]


class OfferLogCreate(OfferLogBase):
    """Ajánlat napló létrehozási séma"""
    changed_by: int


class OfferLogResponse(OfferLogBase):
    """Ajánlat napló válasz séma"""
    id: int
    changed_by: int
    created_at: datetime
    
    class Config:
        orm_mode = True


# Összetett válasz sémák és lekérdezési paraméterek

class OfferFilter(BaseModel):
    """Ajánlat szűrési paraméterek"""
    user_id: Optional[int] = Field(None, description="Termelő azonosítója")
    product_type_id: Optional[int] = Field(None, description="Terméktípus azonosítója")
    status: Optional[str] = Field(None, description="Ajánlat státusza")
    date_from: Optional[date] = Field(None, description="Beszállítás kezdő dátuma")
    date_to: Optional[date] = Field(None, description="Beszállítás záró dátuma")


class CalendarOfferResponse(BaseModel):
    """Naptári nézet ajánlat válasz séma"""
    date: date
    offers: List[OfferDetailResponse]
    
    class Config:
        orm_mode = True


class OfferStatistics(BaseModel):
    """Ajánlat statisztikák"""
    total_offers: int = Field(..., description="Összes ajánlat száma")
    total_quantity: Decimal = Field(..., description="Összes mennyiség kg-ban")
    total_value: Optional[Decimal] = Field(None, description="Összes érték Ft-ban")
    average_price: Optional[Decimal] = Field(None, description="Átlagos ár Ft/kg-ban")
    status_counts: dict[str, int] = Field(..., description="Státusz szerinti darabszám")
    status_summary: Optional[List[dict]] = Field(None, description="Státusz szerinti megoszlás részletes adatai")
    daily_summary: Optional[List[dict]] = Field(None, description="Napi összesített adatok")