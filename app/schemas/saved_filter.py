"""
Pydantic schemas for saved filters
"""
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel


class SavedFilterBase(BaseModel):
    """Base schema for saved filters with common fields"""
    name: str
    description: Optional[str] = None
    filter_type: str
    is_default: Optional[bool] = False
    filter_data: Dict[str, Any]


class SavedFilterCreate(SavedFilterBase):
    """Schema for creating a new saved filter"""
    pass


class SavedFilterUpdate(BaseModel):
    """Schema for updating an existing saved filter"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_default: Optional[bool] = None
    filter_data: Optional[Dict[str, Any]] = None


class SavedFilterResponse(SavedFilterBase):
    """Schema for saved filter response including database fields"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True