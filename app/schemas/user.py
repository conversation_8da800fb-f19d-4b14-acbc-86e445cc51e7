"""
Felhasz<PERSON><PERSON><PERSON><PERSON> kapcsolatos Pydantic sémák
"""
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, EmailStr, validator, Field


# Alaposzt<PERSON>ly, közös me<PERSON>
class UserBase(BaseModel):
    """Felhasználó alap séma"""
    email: EmailStr = Field(..., description="Email cím")
    company_name: Optional[str] = Field(None, description="Cégnév")
    tax_id: Optional[str] = Field(None, description="Adószám")
    contact_name: str = Field(..., description="Kapcsolattartó neve")
    phone_number: str = Field(..., description="Telefonszám")


# Felhasználó létrehozásához
class UserCreate(UserBase):
    """Felhasználó létrehozási séma"""
    password: str = Field(..., description="Je<PERSON><PERSON><PERSON>")
    
    @validator('password')
    def password_strength(cls, v):
        """
        <PERSON><PERSON><PERSON><PERSON> erősség ellenőrzése:
        - Legalább 8 karakter
        - Tartalmaz legalább egy számot
        - Tartalmaz legalább egy betűt
        """
        if len(v) < 8:
            raise ValueError('A jelszónak legalább 8 karakter hosszúnak kell lennie')
        if not any(char.isdigit() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy számot')
        if not any(char.isalpha() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy betűt')
        return v


# Felhasználó frissítéséhez
class UserUpdate(BaseModel):
    """Felhasználó frissítési séma"""
    company_name: Optional[str] = Field(None, description="Cégnév")
    tax_id: Optional[str] = Field(None, description="Adószám")
    contact_name: Optional[str] = Field(None, description="Kapcsolattartó neve")
    phone_number: Optional[str] = Field(None, description="Telefonszám")
    is_active: Optional[bool] = Field(None, description="Aktív felhasználó")


# Jelszóváltáshoz
class UserChangePassword(BaseModel):
    """Jelszóváltási séma"""
    current_password: str = Field(..., description="Jelenlegi jelszó")
    new_password: str = Field(..., description="Új jelszó")
    
    @validator('new_password')
    def password_strength(cls, v):
        """
        Jelszó erősség ellenőrzése:
        - Legalább 8 karakter
        - Tartalmaz legalább egy számot
        - Tartalmaz legalább egy betűt
        """
        if len(v) < 8:
            raise ValueError('A jelszónak legalább 8 karakter hosszúnak kell lennie')
        if not any(char.isdigit() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy számot')
        if not any(char.isalpha() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy betűt')
        return v


# Jelszó reset kéréshez
class UserPasswordReset(BaseModel):
    """Jelszó reset kérés séma"""
    email: EmailStr = Field(..., description="Email cím")


# Jelszó reset végrehajtásához
class UserPasswordResetConfirm(BaseModel):
    """Jelszó reset végrehajtás séma"""
    token: str = Field(..., description="Reset token")
    new_password: str = Field(..., description="Új jelszó")
    
    @validator('new_password')
    def password_strength(cls, v):
        """
        Jelszó erősség ellenőrzése:
        - Legalább 8 karakter
        - Tartalmaz legalább egy számot
        - Tartalmaz legalább egy betűt
        """
        if len(v) < 8:
            raise ValueError('A jelszónak legalább 8 karakter hosszúnak kell lennie')
        if not any(char.isdigit() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy számot')
        if not any(char.isalpha() for char in v):
            raise ValueError('A jelszónak tartalmaznia kell legalább egy betűt')
        return v


# Szerepkör váltáshoz (admin funkcióhoz)
class UserRoleUpdate(BaseModel):
    """Szerepkör frissítési séma"""
    role: str = Field(..., description="Szerepkör")
    
    @validator('role')
    def valid_role(cls, v):
        """Érvényes szerepkör ellenőrzése"""
        valid_roles = ["termelő", "ügyintéző", "admin"]
        if v not in valid_roles:
            raise ValueError(f'A szerepkörnek a következők egyikének kell lennie: {", ".join(valid_roles)}')
        return v


# Válaszként küldendő felhasználó adatok
class UserResponse(UserBase):
    """Felhasználói válasz séma"""
    id: int
    role: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# Alapértelmezett beállítások létrehozásához/frissítéséhez
class UserDefaultSettingsCreate(BaseModel):
    """Felhasználói alapbeállítások létrehozási séma"""
    default_product_type_id: Optional[int] = Field(None, description="Alapértelmezett terméktípus ID")
    default_quality_grade_id: Optional[int] = Field(None, description="Alapértelmezett minőségi besorolás ID")
    default_quantity_unit: Optional[str] = Field("kg", description="Alapértelmezett mennyiségi egység (kg vagy tonna)")
    default_product_type_name: Optional[str] = Field(None, description="Alapértelmezett terméktípus neve")
    default_quality_grade_name: Optional[str] = Field(None, description="Alapértelmezett minőségi besorolás neve")
    default_category_id: Optional[int] = Field(None, description="Alapértelmezett kategória ID")
    default_category_name: Optional[str] = Field(None, description="Alapértelmezett kategória neve") 
    has_quality_grades: Optional[bool] = Field(False, description="Van-e minőségi besorolás")
    
    @validator('default_quantity_unit')
    def valid_quantity_unit(cls, v):
        """Érvényes mennyiségi egység ellenőrzése"""
        valid_units = ["kg", "tonna", "db"]
        if v not in valid_units:
            raise ValueError(f'A mennyiségi egységnek a következők egyikének kell lennie: {", ".join(valid_units)}')
        return v


# Válaszként küldendő alapértelmezett beállítások
class UserDefaultSettingsResponse(UserDefaultSettingsCreate):
    """Felhasználói alapbeállítások válasz séma"""
    id: int
    user_id: int
    
    class Config:
        orm_mode = True


# Autentikációs sémák

# Bejelentkezéshez
class Login(BaseModel):
    """Bejelentkezési séma"""
    username: EmailStr
    password: str


# Token válasz
class Token(BaseModel):
    """Token válasz séma"""
    access_token: str
    token_type: str = "bearer"


# Token adat
class TokenData(BaseModel):
    """Token adat séma"""
    user_id: Optional[int] = None
    role: Optional[str] = None