"""
Termék modellek definíciói
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, Numeric
from sqlalchemy.orm import relationship
from app.db.base_class import Base  # base.py helyett base_class.py-t hasz<PERSON><PERSON>nk
from app.models.common import TimestampMixin

class ProductCategory(Base, TimestampMixin):
    """
    Termékkategória modell
    - Főkategóriák (Paprika, Paradicsom, stb.)
    """
    __tablename__ = "product_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    
    # Kapcsolatok
    product_types = relationship("ProductType", back_populates="category")


class ProductType(Base, TimestampMixin):
    """
    Terméktípus modell
    - Alkategóriák (TV paprika, Kápia paprika, stb.)
    """
    __tablename__ = "product_types"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, ForeignKey("product_categories.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    has_quality_grades = Column(Boolean, default=True, nullable=False)
    
    # Kapcsolatok
    category = relationship("ProductCategory", back_populates="product_types")
    quality_grades = relationship("QualityGrade", back_populates="product_type")
    offers = relationship("Offer", back_populates="product_type")
    
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )


class QualityGrade(Base, TimestampMixin):
    """
    Minőségi besorolás modell
    - Minőségi osztályok (Extra, I. osztály, II. osztály, stb.)
    """
    __tablename__ = "quality_grades"
    
    id = Column(Integer, primary_key=True, index=True)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    name = Column(String(50), nullable=False)
    min_shoulder_diameter = Column(Numeric(5, 2))
    max_shoulder_diameter = Column(Numeric(5, 2))
    min_length = Column(Numeric(5, 2))
    max_length = Column(Numeric(5, 2))
    description = Column(Text)
    
    # Kapcsolatok
    product_type = relationship("ProductType", back_populates="quality_grades")
    offers = relationship("Offer", back_populates="quality_grade")
    
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )
