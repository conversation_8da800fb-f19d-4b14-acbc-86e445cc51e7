from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.models.common import TimestampMixin

class Notification(Base, TimestampMixin):
    """
    Notification modell
    - Felhasználói értesítések tárolása
    """
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    type = Column(String(50), nullable=False)
    message = Column(String(255), nullable=False)
    detail = Column(Text)
    is_read = Column(Boolean, nullable=False, default=False)
    target_roles = Column(String(255))
    related_entity_type = Column(String(50))
    related_entity_id = Column(Integer)

    user = relationship("User", back_populates="notifications")
