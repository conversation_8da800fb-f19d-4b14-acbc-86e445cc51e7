"""
User saved filter model definitions
"""
from sqlalchemy import <PERSON><PERSON>n, Integer, String, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.common import TimestampMixin


class UserSavedFilter(Base, TimestampMixin):
    """
    User saved filter configurations for the offer management page
    """
    __tablename__ = "user_saved_filters"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    filter_type = Column(String(50), nullable=False)  # "offer", "product", etc.
    is_default = Column(Boolean, default=False, nullable=False)
    filter_data = Column(JSON, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="saved_filters")