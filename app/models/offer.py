"""
Ajánlat és ajánlat napló modellek definíciói
"""
from sqlalchemy import Column, Integer, String, Float, Text, Date, ForeignKey, Numeric, CheckConstraint
from sqlalchemy.orm import relationship
from app.db.base_class import Base  # base.py helyett base_class.py-t hasz<PERSON><PERSON>nk
from app.models.common import TimestampMixin

class Offer(Base, TimestampMixin):
    """
    Ajánlat modell
    - Termelők által leadott ajánlatok adatai
    """
    __tablename__ = "offers"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    
    quantity_value = Column(Numeric(10, 2), nullable=False)
    quantity_unit = Column(String(10), nullable=False, default="kg")
    delivery_date = Column(Date, nullable=False)
    
    status = Column(
        String(50), 
        nullable=False,
        default="CREATED",
    )
    
    confirmed_quantity = Column(Numeric(10, 2))
    confirmed_price = Column(Numeric(10, 2))
    note = Column(Text)
    
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Kapcsolatok
    user = relationship("User", foreign_keys=[user_id], back_populates="offers")
    created_by_user = relationship("User", foreign_keys=[created_by_user_id], back_populates="created_offers")
    product_type = relationship("ProductType", back_populates="offers")
    quality_grade = relationship("QualityGrade", back_populates="offers")
    logs = relationship("OfferLog", back_populates="offer")
    
    # Ellenőrzések
    __table_args__ = (
        CheckConstraint('quantity_value > 0', name='check_quantity_positive'),
        CheckConstraint(
            "quantity_unit IN ('kg', 'tonna', 'db')",
            name='check_quantity_unit_valid'
        ),
        CheckConstraint(
            "status IN ('CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED')",
            name='check_status_values'
        ),
        CheckConstraint('confirmed_quantity IS NULL OR confirmed_quantity > 0', name='check_confirmed_quantity'),
        CheckConstraint('confirmed_price IS NULL OR confirmed_price > 0', name='check_confirmed_price'),
        {'sqlite_autoincrement': True},
    )


class OfferLog(Base, TimestampMixin):
    """
    Ajánlat napló modell
    - Az ajánlatok státuszváltozásainak naplózása
    """
    __tablename__ = "offer_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    offer_id = Column(Integer, ForeignKey("offers.id"), nullable=False)
    old_status = Column(String(50))
    new_status = Column(String(50), nullable=False)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    note = Column(Text)
    
    # Kapcsolatok
    offer = relationship("Offer", back_populates="logs")
    user = relationship("User", foreign_keys=[changed_by], back_populates="offer_logs")
    
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )
