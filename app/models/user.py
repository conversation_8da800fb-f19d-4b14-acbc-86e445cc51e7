"""
<PERSON><PERSON><PERSON>z<PERSON>lói modell definíciók
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base  # base.py helyett base_class.py-t használunk
from app.models.common import TimestampMixin

class User(Base, TimestampMixin):
    """
    User modell
    - A rendszer különböző felhasználói (termelők, ügyintézők, adminok)
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)
    company_name = Column(String(255))
    tax_id = Column(String(50))  # Adószám
    contact_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=False, nullable=False)
    activation_token = Column(String(255))
    
    # Kapcsolatok közvetlenül itt definiálva
    offers = relationship("Offer", foreign_keys="Offer.user_id", back_populates="user")
    created_offers = relationship("Offer", foreign_keys="Offer.created_by_user_id", back_populates="created_by_user")
    default_settings = relationship("UserDefaultSettings", back_populates="user", uselist=False)
    password_reset_tokens = relationship("PasswordResetToken", back_populates="user")
    offer_logs = relationship("OfferLog", foreign_keys="OfferLog.changed_by", back_populates="user")
    notifications = relationship("Notification", back_populates="user")
    saved_filters = relationship("UserSavedFilter", back_populates="user", cascade="all, delete-orphan")


class UserDefaultSettings(Base, TimestampMixin):
    """
    Felhasználói alapbeállítások
    - A felhasználó által gyakran használt alapértékek
    """
    __tablename__ = "user_default_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    default_product_type_id = Column(Integer, ForeignKey("product_types.id"))
    default_quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    default_quantity_unit = Column(String(10), default="kg", nullable=False)  # 'kg' vagy 'tonna'
    default_product_type_name = Column(String(100), nullable=True)
    default_quality_grade_name = Column(String(100), nullable=True)
    default_category_id = Column(Integer, nullable=True)
    default_category_name = Column(String(100), nullable=True)
    has_quality_grades = Column(Boolean, default=False, nullable=False)
    
    # Kapcsolatok
    user = relationship("User", back_populates="default_settings")
    default_product_type = relationship("ProductType")
    default_quality_grade = relationship("QualityGrade")


class PasswordResetToken(Base, TimestampMixin):
    """
    Jelszó-visszaállítási token
    """
    __tablename__ = "password_reset_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), nullable=False, unique=True)
    expires_at = Column(DateTime, nullable=False, default=lambda: func.now())
    is_used = Column(Boolean, default=False, nullable=False)
    
    # Kapcsolatok
    user = relationship("User", back_populates="password_reset_tokens")
