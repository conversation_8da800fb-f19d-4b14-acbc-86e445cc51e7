"""
API függőségek
"""
from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import get_db
from app.models.user import User
from app.schemas.user import TokenData

# OAuth2 token endpoint és séma
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)


def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    Jelenlegi felhasználó lekérdezése a token alapján
    
    Args:
        db: Adatbázis session
        token: JWT token
        
    Returns:
        User: A jelenlegi felhasználó
        
    Raises:
        HTTPException: Ha a token érvénytelen vagy a felhasználó nem található
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Érvénytelen hitelesítési adatok",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Token dekódolása
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        
        # Token adat kinyerése
        token_data = TokenData(user_id=payload.get("user_id"), role=payload.get("role"))
        
        if token_data.user_id is None:
            raise credentials_exception
    
    except (JWTError, ValidationError):
        raise credentials_exception
    
    # Felhasználó lekérdezése
    user = db.query(User).filter(User.id == token_data.user_id).first()
    
    if user is None:
        raise credentials_exception
    
    # Ellenőrizzük, hogy a felhasználó aktív-e
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inaktív felhasználó"
        )
    
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Aktív felhasználó ellenőrzése
    
    Args:
        current_user: Jelenlegi felhasználó
        
    Returns:
        User: Az aktív felhasználó
        
    Raises:
        HTTPException: Ha a felhasználó nem aktív
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inaktív felhasználó"
        )
    
    return current_user


def get_current_operator(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Ügyintézői vagy admin jogosultság ellenőrzése
    
    Args:
        current_user: Jelenlegi felhasználó
        
    Returns:
        User: Az ügyintéző vagy admin felhasználó
        
    Raises:
        HTTPException: Ha a felhasználó nem ügyintéző vagy admin
    """
    if current_user.role not in ["ügyintéző", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs megfelelő jogosultság"
        )
    
    return current_user


def get_current_admin(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Admin jogosultság ellenőrzése
    
    Args:
        current_user: Jelenlegi felhasználó
        
    Returns:
        User: Az admin felhasználó
        
    Raises:
        HTTPException: Ha a felhasználó nem admin
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs megfelelő jogosultság"
        )
    
    return current_user