"""
API router beállítások
"""
from fastapi import APIRouter

from app.api.endpoints import auth, users, products, offers, admin, notifications, ai_compatibility, saved_filters

# API router létrehozása
api_router = APIRouter()

# Végpontok hozzáadása
api_router.include_router(
    auth.router, prefix="/auth", tags=["auth"]
)
api_router.include_router(
    users.router, prefix="/users", tags=["users"]
)
api_router.include_router(
    products.router, prefix="/products", tags=["products"]
)
api_router.include_router(
    offers.router, prefix="/offers", tags=["offers"]
)
api_router.include_router(
    admin.router, prefix="/admin", tags=["admin"]
)
api_router.include_router(
    notifications.router, prefix="/notifications", tags=["notifications"]
)
api_router.include_router(
    ai_compatibility.router, prefix="/ai", tags=["ai_compatibility"]
)
api_router.include_router(
    saved_filters.router, prefix="/saved-filters", tags=["saved_filters"]
)