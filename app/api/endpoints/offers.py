"""
Ajánlatokkal kapcsolatos végpontok
"""
from typing import Any, List, Optional, Dict
from datetime import date

from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks, status
from sqlalchemy.orm import Session

from app.api.dependencies import (
    get_db, get_current_user, get_current_operator, get_current_admin
)
from app.models.user import User
from app.schemas.offer import (
    OfferCreate, OfferCreateForUser, OfferUpdate, OfferConfirm, 
    OfferResponse, OfferDetailResponse, OfferLogResponse,
    OfferFilter, CalendarOfferResponse, OfferStatistics
)
from app.services.offer_service import (
    create_offer, create_offer_for_user, get_offer, get_offers,
    update_offer, delete_offer, confirm_offer, accept_offer, reject_offer, finalize_offer,
    get_offer_logs, get_calendar_offers, get_offer_statistics
)
from app.models.offer import Offer
from app.services.product_service import (
    get_product_type, get_quality_grade
)
from app.services.email_service import (
    send_offer_confirmation_email, send_offer_finalized_email
)
from app.services.offer_log_service import create_offer_log
from app.utils.logging import logger

router = APIRouter()


@router.post("", response_model=OfferResponse, status_code=status.HTTP_201_CREATED)
def create_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_in: OfferCreate,
) -> Any:
    """
    Új ajánlat létrehozása
    """
    try:
        return create_offer(db, current_user.id, offer_in)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/for-user", response_model=OfferResponse, status_code=status.HTTP_201_CREATED)
def create_offer_for_user_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_operator),
    offer_in: OfferCreateForUser,
) -> Any:
    """
    Új ajánlat létrehozása más felhasználó nevében (ügyintéző funkció)
    """
    try:
        return create_offer_for_user(db, current_user.id, offer_in)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


from sqlalchemy.orm import joinedload

@router.get("", response_model=List[OfferDetailResponse])
def read_offers(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 1000,
    user_id: Optional[int] = None,
    product_type_id: Optional[int] = None,
    status: Optional[str] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
) -> Any:
    """
    Ajánlatok listázása
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat, szűrési lehetőségekkel
    """
    # Termelő csak a saját ajánlatait láthatja
    if current_user.role == "termelő":
        user_id = current_user.id
    
    # Szűrési paraméterek összeállítása
    filters = OfferFilter(
        user_id=user_id,
        product_type_id=product_type_id,
        status=status,
        date_from=date_from,
        date_to=date_to
    )
    
    # Ajánlatok lekérdezése kapcsolt objektumokkal
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    )
    # Alkalmazzuk a szűrőket (ugyanúgy mint a get_offers-ben)
    if filters.user_id:
        query = query.filter(Offer.user_id == filters.user_id)
    if filters.product_type_id:
        query = query.filter(Offer.product_type_id == filters.product_type_id)
    if filters.status:
        query = query.filter(Offer.status == filters.status)
    if filters.date_from:
        query = query.filter(Offer.delivery_date >= filters.date_from)
    if filters.date_to:
        query = query.filter(Offer.delivery_date <= filters.date_to)
    query = query.order_by(Offer.delivery_date, Offer.id)
    offers = query.offset(skip).limit(limit).all()

    # Debug logging for user and product_type fields
    for offer in offers:
        logger.debug(f"Offer ID: {offer.id} | User: {getattr(offer.user, 'company_name', None)} | ProductType: {getattr(offer.product_type, 'name', None)}")
    return offers



@router.get("/my", response_model=List[OfferResponse])
def read_my_offers(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 1000,
    product_type_id: Optional[int] = None,
    status: Optional[str] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
) -> Any:
    """
    Saját ajánlatok listázása (minden felhasználó)
    """
    # Szűrési paraméterek összeállítása
    filters = OfferFilter(
        product_type_id=product_type_id,
        status=status,
        date_from=date_from,
        date_to=date_to
    )
    
    # Ajánlatok lekérdezése
    return get_offers(db, skip=skip, limit=limit, filters=filters, user_id=current_user.id)


@router.get("/calendar", response_model=Dict[str, List[OfferResponse]])
def read_calendar_offers(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    date_from: date = Query(..., description="Kezdő dátum"),
    date_to: date = Query(..., description="Záró dátum"),
) -> Any:
    """
    Naptári nézet ajánlatok
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat
    """
    # Termelő csak a saját ajánlatait láthatja
    user_id = current_user.id if current_user.role == "termelő" else None
    
    # Ajánlatok lekérdezése
    calendar_offers = get_calendar_offers(
        db, date_from=date_from, date_to=date_to, user_id=user_id
    )
    
    # Eredmény átalakítása a válaszhoz (dátumokat stringgé)
    result = {}
    for day, offers in calendar_offers.items():
        result[day.isoformat()] = offers
    
    return result


@router.get("/statistics", response_model=OfferStatistics)
def read_offer_statistics(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    product_type_id: Optional[int] = None,
    category_id: Optional[int] = None,
    quality_grade_id: Optional[int] = None,
) -> Any:
    """
    Ajánlat statisztikák
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat, szűrési lehetőségekkel
    """
    # Termelő csak a saját ajánlatait láthatja
    user_id = current_user.id if current_user.role == "termelő" else None
    
    # Statisztikák lekérdezése
    return get_offer_statistics(
        db, 
        date_from=date_from, 
        date_to=date_to, 
        user_id=user_id,
        product_type_id=product_type_id,
        category_id=category_id,
        quality_grade_id=quality_grade_id
    )


@router.get("/{offer_id}", response_model=OfferDetailResponse)
def read_offer_detail(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
) -> Any:
    """
    Ajánlat részleteinek lekérdezése
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat
    """
    # Ajánlat lekérdezése
    offer = get_offer(db, offer_id)
    
    if not offer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Az ajánlat nem található"
        )
    
    # Termelő csak a saját ajánlatait láthatja
    if current_user.role == "termelő" and offer.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs megfelelő jogosultság"
        )
    
    return offer


@router.put("/{offer_id}", response_model=OfferResponse)
def update_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
    offer_in: OfferUpdate,
) -> Any:
    """
    Ajánlat frissítése
    - Termelő: csak a saját ajánlatai
    - Ügyintéző: minden ajánlat
    """
    # Ajánlat lekérdezése
    offer = get_offer(db, offer_id)
    
    if not offer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Az ajánlat nem található"
        )
    
    # Termelő csak a saját ajánlatait frissítheti
    if current_user.role == "termelő" and offer.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs megfelelő jogosultság"
        )
    
    try:
        return update_offer(db, offer_id, offer_in, current_user.id)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{offer_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_offer(
    offer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Ajánlat törlése
    - Csak CREATED vagy REJECTED_BY_USER státuszú ajánlatok törölhetők
    - Csak admin vagy az ajánlat létrehozója törölheti
    """
    # Ajánlat lekérdezése
    offer = db.query(Offer).filter(Offer.id == offer_id).first()
    
    if not offer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Ajánlat nem található"
        )
    
    # Ellenőrizzük a jogosultságot
    if current_user.role != 'admin' and current_user.id != offer.created_by_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Nem jogosult az ajánlat törlésére"
        )
    
    # Ellenőrizzük a státuszt
    if offer.status not in ['CREATED', 'REJECTED_BY_USER']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"Az ajánlat nem törölhető, mert nem megfelelő státuszban van: {offer.status}"
        )
    
    try:
        # Törlés előtt létrehozzuk a naplóbejegyzést
        create_offer_log(
            db=db,
            offer_id=offer.id,
            old_status=offer.status,
            new_status="DELETED",
            changed_by=current_user.id,
            note="Ajánlat törölve"
        )
        
        # Törlés az adatbázisból
        db.delete(offer)
        db.commit()
        
        logger.info(f"Ajánlat törölve: {offer.id} (felhasználó: {current_user.id})")
        
        return None
    except Exception as e:
        db.rollback()
        logger.error(f"Hiba az ajánlat törlésekor: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Hiba az ajánlat törlésekor: {str(e)}"
        )


@router.post("/{offer_id}/confirm", response_model=OfferResponse)
async def confirm_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_operator),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
    confirm_data: OfferConfirm,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    Ajánlat visszaigazolása (ügyintéző funkció)
    """
    try:
        # Ajánlat visszaigazolása
        offer = confirm_offer(db, offer_id, confirm_data, current_user.id)
        
        if not offer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Az ajánlat nem található"
            )
        
        # Termék és besorolás lekérdezése az email-hez
        product_type = get_product_type(db, offer.product_type_id)
        product_name = product_type.name if product_type else "Ismeretlen termék"
        
        # Email küldése a termelőnek
        await send_offer_confirmation_email(
            background_tasks=background_tasks,
            email_to=offer.user.email,
            offer_id=offer.id,
            product_name=product_name,
            confirmed_quantity=float(offer.confirmed_quantity),
            confirmed_price=float(offer.confirmed_price)
        )
        
        return offer
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{offer_id}/accept", response_model=OfferResponse)
def accept_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
) -> Any:
    """
    Ajánlat elfogadása (termelő funkció)
    """
    try:
        offer = accept_offer(db, offer_id, current_user.id)
        
        if not offer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Az ajánlat nem található"
            )
        
        return offer
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{offer_id}/reject", response_model=OfferResponse)
def reject_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
    note: Optional[str] = None,
) -> Any:
    """
    Ajánlat elutasítása (termelő funkció)
    """
    try:
        offer = reject_offer(db, offer_id, current_user.id, note)
        
        if not offer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Az ajánlat nem található"
            )
        
        return offer
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{offer_id}/finalize", response_model=OfferResponse)
async def finalize_offer_endpoint(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_operator),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
    background_tasks: BackgroundTasks,
) -> Any:
    """
    Ajánlat véglegesítése (ügyintéző funkció)
    """
    try:
        # Ajánlat véglegesítése
        offer = finalize_offer(db, offer_id, current_user.id)
        
        if not offer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Az ajánlat nem található"
            )
        
        # Termék és besorolás lekérdezése az email-hez
        product_type = get_product_type(db, offer.product_type_id)
        product_name = product_type.name if product_type else "Ismeretlen termék"
        
        # Email küldése a termelőnek
        await send_offer_finalized_email(
            background_tasks=background_tasks,
            email_to=offer.user.email,
            offer_id=offer.id,
            product_name=product_name,
            confirmed_quantity=float(offer.confirmed_quantity),
            confirmed_price=float(offer.confirmed_price),
            delivery_date=offer.delivery_date.isoformat()
        )
        
        return offer
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{offer_id}/logs", response_model=List[OfferLogResponse])
def read_offer_logs(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    offer_id: int = Path(..., ge=1, description="Ajánlat azonosító"),
) -> Any:
    """
    Ajánlat naplójának lekérdezése
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat
    """
    # Ajánlat lekérdezése
    offer = get_offer(db, offer_id)
    
    if not offer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Az ajánlat nem található"
        )
    
    # Termelő csak a saját ajánlatait láthatja
    if current_user.role == "termelő" and offer.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs megfelelő jogosultság"
        )
    
    # Naplók lekérdezése
    return get_offer_logs(db, offer_id)


@router.get("/paginated", response_model=Dict)
def read_offers_paginated(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, ge=1, description="Oldal száma (1-től kezdődik)"),
    page_size: int = Query(50, ge=1, le=1000, description="Oldalankénti elemszám"),
    user_id: Optional[int] = None,
    product_type_id: Optional[int] = None,
    status: Optional[str] = None,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
) -> Any:
    """
    Ajánlatok listázása lapozható formában
    - Termelő: csak a saját ajánlatai
    - Ügyintéző és admin: minden ajánlat, szűrési lehetőségekkel
    
    Visszatérési érték:
    {
        "items": [ajánlat_objektumok_listája],
        "total": összes_ajánlat_száma,
        "page": aktuális_oldal,
        "page_size": oldalankénti_elemszám,
        "pages": összes_oldalak_száma
    }
    """
    # Napló a kérés debugolásához
    logger.debug(f"Paginated offers request - page: {page}, page_size: {page_size}, user_id: {user_id}, status: {status}, dates: {date_from}-{date_to}")
    
    # Termelő csak a saját ajánlatait láthatja
    if current_user.role == "termelő":
        user_id = current_user.id
    
    # Szűrési paraméterek összeállítása
    filters = OfferFilter(
        user_id=user_id,
        product_type_id=product_type_id,
        status=status,
        date_from=date_from,
        date_to=date_to
    )
    
    # Ajánlatok lekérdezése kapcsolt objektumokkal
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    )
    
    # Alkalmazzuk a szűrőket
    if filters.user_id:
        query = query.filter(Offer.user_id == filters.user_id)
    if filters.product_type_id:
        query = query.filter(Offer.product_type_id == filters.product_type_id)
    if filters.status:
        # Támogatja a vesszővel elválasztott státusz listát is
        if "," in str(filters.status):
            status_list = [s.strip() for s in filters.status.split(",")]
            query = query.filter(Offer.status.in_(status_list))
        else:
            query = query.filter(Offer.status == filters.status)
    if filters.date_from:
        query = query.filter(Offer.delivery_date >= filters.date_from)
    if filters.date_to:
        query = query.filter(Offer.delivery_date <= filters.date_to)
    
    # Rendezés dátum és azonosító szerint
    query = query.order_by(Offer.delivery_date.desc(), Offer.id.desc())
    
    # Összes találat számának lekérdezése (lapozási adatokhoz)
    total_count = query.count()
    
    # Lapozási adatok kiszámítása
    skip = (page - 1) * page_size
    offers = query.offset(skip).limit(page_size).all()
    
    # Oldalak teljes számának kiszámítása
    total_pages = (total_count + page_size - 1) // page_size  # Mennyezeti osztás
    
    # Válasz összeállítása
    response = {
        "items": offers,  # Ez egy lista az Offer objektumokkal
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "pages": total_pages
    }
    
    return response
