"""
Admin végpontok 
"""
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import delete, text

from app.api.dependencies import get_db, get_current_admin
from app.db.init_db import init_system_data
from app.models.user import User, UserDefaultSettings, PasswordResetToken
from app.models.product import ProductCategory, ProductType, QualityGrade
from app.models.offer import Offer, OfferLog
from app.models.notification import Notification
from app.models.user_saved_filter import UserSavedFilter
from app.schemas.user import UserDefaultSettingsCreate, UserDefaultSettingsResponse
from app.services.user_service import get_user, get_user_default_settings, upsert_user_default_settings

# API router létrehozása
router = APIRouter()


@router.post("/reset-database", status_code=status.HTTP_200_OK)
async def reset_database(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),  # Csak admin érheti el
) -> Any:
    """
    Az adatbázis visszaállítása alapállapotba.
    
    Ez a művelet MINDEN adatot töröl az adatbázisból, beleértve a felhasználókat, ajánlatokat,
    termékadatokat, stb. - majd visszaállítja az alapértelmezett termékkategóriákat,
    terméktípusokat és demo felhasználókat.
    
    FIGYELEM: Ez a művelet nem visszafordítható!
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett admin felhasználó
        
    Returns:
        Dict: Sikeres válasz egy üzenettel
    """
    try:
        # Aktuális admin felhasználó adatainak mentése
        admin_id = current_user.id
        
        # Az adatok törlése fordított sorrendben a külső kulcsok miatt
        # A függő kapcsolatokat először kell törölni
        
        # 1. Először a legmélyebb függőségeket töröljük
        db.execute(delete(OfferLog))
        db.execute(delete(Offer))
        
        # 2. Felhasználókhoz kapcsolódó táblák törlése
        db.execute(delete(Notification))  # Értesítések törlése
        db.execute(delete(UserSavedFilter))  # Mentett szűrők törlése
        db.execute(delete(UserDefaultSettings))
        db.execute(delete(PasswordResetToken))
        
        # 3. Termékhez kapcsolódó táblák törlése
        db.execute(delete(QualityGrade))
        db.execute(delete(ProductType))
        db.execute(delete(ProductCategory))
        
        # 4. Végül a felhasználók törlése a bejelentkezett admin kivételével
        # (hogy ne jelentkeztessük ki magunkat)
        db.execute(delete(User).where(User.id != admin_id))
        
        # Commit a törlésekhez
        db.commit()
        
        # Alapadatok újrainicializálása
        init_system_data(db)
        
        return {
            "message": "Az adatbázis sikeresen visszaállítva alapállapotba. Az alapértelmezett termékkategóriák, terméktípusok és demo felhasználók létrehozva."
        }
        
    except Exception as e:
        # Bármilyen hiba esetén rollback
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Hiba az adatbázis visszaállítása során: {str(e)}"
        )


@router.post("/reset-database-keep-users", status_code=status.HTTP_200_OK)
async def reset_database_keep_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),  # Csak admin érheti el
) -> Any:
    """
    Az adatbázis visszaállítása alapállapotba a felhasználók meghagyásával.
    
    Ez a művelet törli az ajánlatokat, ajánlat naplókat, értesítéseket és mentett szűrőket,
    de meghagyja a felhasználókat és azok alapbeállításait. A termékadatokat (kategóriák,
    típusok, minőségi besorolások) szintén újrainicializálja.
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett admin felhasználó
        
    Returns:
        Dict: Sikeres válasz egy üzenettel
    """
    try:
        # Az adatok törlése fordított sorrendben a külső kulcsok miatt
        # A függő kapcsolatokat először kell törölni
        
        # 1. Ajánlatokhoz kapcsolódó adatok törlése
        db.execute(delete(OfferLog))
        db.execute(delete(Offer))
        
        # 2. Felhasználói interakciók törlése (de a felhasználók és beállítások megmaradnak)
        db.execute(delete(Notification))  # Értesítések törlése
        db.execute(delete(UserSavedFilter))  # Mentett szűrők törlése
        db.execute(delete(PasswordResetToken))  # Régi jelszó reset tokenek törlése
        
        # 3. Termékhez kapcsolódó táblák törlése (hogy újrainicializálhassuk őket)
        db.execute(delete(QualityGrade))
        db.execute(delete(ProductType))
        db.execute(delete(ProductCategory))
        
        # FONTOS: A felhasználók (User) és azok alapbeállításai (UserDefaultSettings) megmaradnak!
        
        # Commit a törlésekhez
        db.commit()
        
        # Csak a termékadatok újrainicializálása (felhasználók nélkül)
        init_system_data(db)
        
        return {
            "message": "Az adatbázis sikeresen visszaállítva alapállapotba a felhasználók meghagyásával. Az ajánlatok és kapcsolódó adatok törölve, a termékkategóriák és terméktípusok újrainicializálva."
        }
        
    except Exception as e:
        # Bármilyen hiba esetén rollback
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Hiba az adatbázis visszaállítása során: {str(e)}"
        )


@router.put("/users/{user_id}/settings", response_model=UserDefaultSettingsResponse)
def update_user_settings(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),  # Csak admin érheti el
    user_id: int,
    settings_data: UserDefaultSettingsCreate,
) -> Any:
    """
    Felhasználó alapbeállításainak frissítése (admin jogosultsággal).
    
    Ez a végpont lehetővé teszi az adminok számára, hogy módosítsák
    bármely felhasználó alapbeállításait.
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett admin felhasználó
        user_id: A felhasználó azonosítója, akinek a beállításait módosítjuk
        settings_data: Az új beállítások adatai
    
    Returns:
        UserDefaultSettingsResponse: A frissített beállítások
    
    Raises:
        HTTPException: Ha a felhasználó nem található vagy egyéb hiba történt
    """
    # Ellenőrizzük, hogy a felhasználó létezik-e
    user = get_user(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="A felhasználó nem található"
        )
    
    try:
        # Beállítások frissítése vagy létrehozása
        updated_settings = upsert_user_default_settings(db, user_id, settings_data)
        return updated_settings
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Hiba a beállítások mentése során: {str(e)}"
        ) 