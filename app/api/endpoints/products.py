"""
Termé<PERSON><PERSON><PERSON> kap<PERSON> végpontok
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.api.dependencies import (
    get_db, get_current_user, get_current_operator, get_current_admin
)
from app.models.user import User
from app.schemas.product import (
    ProductCategoryCreate, ProductCategoryUpdate, ProductCategoryResponse,
    ProductTypeCreate, ProductTypeUpdate, ProductTypeResponse, ProductTypeDetailResponse,
    QualityGradeCreate, QualityGradeUpdate, QualityGradeResponse, QualityGradeDetailResponse,
    ProductCategoryWithTypesResponse, ProductTypeWithGradesResponse
)
from app.services.product_service import (
    create_product_category, get_product_category, get_product_categories,
    update_product_category, delete_product_category,
    create_product_type, get_product_type, get_product_types,
    update_product_type, delete_product_type,
    create_quality_grade, get_quality_grade, get_quality_grades,
    update_quality_grade, delete_quality_grade,
    get_product_categories_with_types, get_product_type_with_grades
)

router = APIRouter()


# Termékkategóriák

@router.post("/categories", response_model=ProductCategoryResponse, status_code=status.HTTP_201_CREATED)
def create_category(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    category_in: ProductCategoryCreate,
) -> Any:
    """
    Termékkategória létrehozása (csak admin)
    """
    try:
        return create_product_category(db, category_in)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/categories", response_model=List[ProductCategoryResponse])
def read_categories(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
) -> Any:
    """
    Termékkategóriák listázása
    """
    return get_product_categories(db, skip=skip, limit=limit, search=search)


@router.get("/categories/with-types", response_model=List[ProductCategoryWithTypesResponse])
def read_categories_with_types(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Termékkategóriák listázása a hozzájuk tartozó típusokkal együtt
    """
    categories_with_types = get_product_categories_with_types(db, skip=skip, limit=limit)
    return categories_with_types


@router.get("/categories/{category_id}", response_model=ProductCategoryResponse)
def read_category(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    category_id: int = Path(..., ge=1, description="Kategória azonosító"),
) -> Any:
    """
    Termékkategória lekérdezése azonosító alapján
    """
    category = get_product_category(db, category_id)
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="A kategória nem található"
        )
    
    return category


@router.put("/categories/{category_id}", response_model=ProductCategoryResponse)
def update_category(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    category_id: int = Path(..., ge=1, description="Kategória azonosító"),
    category_in: ProductCategoryUpdate,
) -> Any:
    """
    Termékkategória frissítése (csak admin)
    """
    try:
        category = update_product_category(db, category_id, category_in)
        
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="A kategória nem található"
            )
        
        return category
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_product_category(
    category_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Csak admin törölhet kategóriát
    if current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Csak adminisztrátor törölhet kategóriát"
        )
    
    # Kategória lekérdezése
    category = db.query(ProductCategory).filter(ProductCategory.id == category_id).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Kategória nem található"
        )
    
    # Ellenőrzés, hogy van-e hozzá kapcsolódó termék
    existing_products = db.query(ProductType).filter(ProductType.category_id == category_id).first()
    if existing_products:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="A kategóriához még tartoznak termékek, ezért nem törölhető"
        )
    
    db.delete(category)
    db.commit()
    
    return None  # Explicit None visszaadás 204-es státuszkódnál


# Terméktípusok

@router.post("/types", response_model=ProductTypeResponse, status_code=status.HTTP_201_CREATED)
def create_type(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    type_in: ProductTypeCreate,
) -> Any:
    """
    Terméktípus létrehozása (csak admin)
    """
    try:
        return create_product_type(db, type_in)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/types", response_model=List[ProductTypeResponse])
def read_types(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
) -> Any:
    """
    Terméktípusok listázása
    """
    return get_product_types(
        db, skip=skip, limit=limit, search=search, category_id=category_id
    )


@router.get("/types/{type_id}", response_model=ProductTypeDetailResponse)
def read_type(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    type_id: int = Path(..., ge=1, description="Típus azonosító"),
) -> Any:
    """
    Terméktípus lekérdezése azonosító alapján
    """
    product_type = get_product_type(db, type_id)
    
    if not product_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="A típus nem található"
        )
    
    return product_type


@router.get("/types/{type_id}/with-grades", response_model=ProductTypeWithGradesResponse)
def read_type_with_grades(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    type_id: int = Path(..., ge=1, description="Típus azonosító"),
) -> Any:
    """
    Terméktípus lekérdezése a hozzá tartozó minőségi besorolásokkal együtt
    """
    type_with_grades = get_product_type_with_grades(db, type_id)
    
    if not type_with_grades:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="A típus nem található"
        )
    
    return type_with_grades


@router.put("/types/{type_id}", response_model=ProductTypeResponse)
def update_type(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    type_id: int = Path(..., ge=1, description="Típus azonosító"),
    type_in: ProductTypeUpdate,
) -> Any:
    """
    Terméktípus frissítése (csak admin)
    """
    try:
        product_type = update_product_type(db, type_id, type_in)
        
        if not product_type:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="A típus nem található"
            )
        
        return product_type
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/types/{type_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_product_type(
    type_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Csak admin törölhet terméktípust
    if current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Csak adminisztrátor törölhet terméktípust"
        )
    
    # Terméktípus lekérdezése
    product_type = db.query(ProductType).filter(ProductType.id == type_id).first()
    
    if not product_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Terméktípus nem található"
        )
    
    # Ellenőrzés, hogy van-e hozzá kapcsolódó minőségi besorolás vagy ajánlat
    existing_quality_grades = db.query(QualityGrade).filter(QualityGrade.product_type_id == type_id).first()
    existing_offers = db.query(Offer).filter(Offer.product_type_id == type_id).first()
    
    if existing_quality_grades or existing_offers:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="A terméktípushoz még tartoznak minőségi besorolások vagy ajánlatok, ezért nem törölhető"
        )
    
    try:
        db.delete(product_type)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Hiba a terméktípus törlésekor: {str(e)}"
        )
    
    return None  # Explicit None visszaadás 204-es státuszkódnál


# Minőségi besorolások

@router.post("/grades", response_model=QualityGradeResponse, status_code=status.HTTP_201_CREATED)
def create_grade(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    grade_in: QualityGradeCreate,
) -> Any:
    """
    Minőségi besorolás létrehozása (csak admin)
    """
    try:
        return create_quality_grade(db, grade_in)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/grades", response_model=List[QualityGradeResponse])
def read_grades(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    product_type_id: Optional[int] = None,
) -> Any:
    """
    Minőségi besorolások listázása
    """
    return get_quality_grades(
        db, skip=skip, limit=limit, search=search, product_type_id=product_type_id
    )


@router.get("/grades/{grade_id}", response_model=QualityGradeDetailResponse)
def read_grade(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    grade_id: int = Path(..., ge=1, description="Besorolás azonosító"),
) -> Any:
    """
    Minőségi besorolás lekérdezése azonosító alapján
    """
    grade = get_quality_grade(db, grade_id)
    
    if not grade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="A minőségi besorolás nem található"
        )
    
    return grade


@router.put("/grades/{grade_id}", response_model=QualityGradeResponse)
def update_grade(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    grade_id: int = Path(..., ge=1, description="Besorolás azonosító"),
    grade_in: QualityGradeUpdate,
) -> Any:
    """
    Minőségi besorolás frissítése (csak admin)
    """
    try:
        grade = update_quality_grade(db, grade_id, grade_in)
        
        if not grade:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="A minőségi besorolás nem található"
            )
        
        return grade
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/grades/{grade_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
def delete_grade(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),
    grade_id: int = Path(ge=1, description="Besorolás azonosító"),
) -> None:
    """
    Minőségi besorolás törlése (csak admin)
    """
    try:
        result = delete_quality_grade(db, grade_id)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="A minőségi besorolás nem található"
            )
        
        return None
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
