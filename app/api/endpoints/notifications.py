from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from starlette import status

from app.api.dependencies import get_db, get_current_active_user, get_current_admin
from app.models.user import User
from app.schemas.notification import Notification, NotificationList, NotificationCreate
from app import crud

router = APIRouter()


@router.get("/me", response_model=NotificationList)
def get_my_notifications(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0, description="Hány elemtől kezdődjön a lekérdezés"),
    limit: int = Query(100, ge=1, le=500, description="Maximum hány elem legyen a válaszban"),
    include_read: bool = Query(False, description="Olvasott értesítéseket is tartalmazza"),
) -> Any:
    """
    Saját értesítések lekérése.
    
    Ez a végpont a bejelentkezett felhasználó számára releváns értesítéseket adja vissza.
    Az értesítések lehetnek kifejezetten a felhasználóhoz rendelve, vagy
    általános rendszerértesítések, amelyek a felhasználó szerepköréhez illeszkednek.
    
    Az értesítések időbélyeg szerint csökkenő sorrendben jelennek meg (legújabb elöl).
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett felhasználó
        skip: Hány elemet hagyjon ki (lapozáshoz)
        limit: Maximum hány elemet adjon vissza (lapozáshoz)
        include_read: Tartalmazza-e az olvasott értesítéseket is
    
    Returns:
        NotificationList: Az értesítések listája és a teljes darabszám
    """
    notifications = crud.notification.get_notifications_for_user(
        db, 
        current_user.id,
        skip=skip,
        limit=limit,
        include_read=include_read
    )
    total = crud.notification.count_notifications_for_user(
        db, 
        current_user.id,
        include_read=include_read
    )
    
    return {"items": notifications, "total": total}


@router.get("/all", response_model=NotificationList)
def get_all_notifications(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),  # Csak admin felhasználók
    skip: int = Query(0, ge=0, description="Hány elemtől kezdődjön a lekérdezés"),
    limit: int = Query(100, ge=1, le=500, description="Maximum hány elem legyen a válaszban"),
    include_read: bool = Query(False, description="Olvasott értesítéseket is tartalmazza"),
) -> Any:
    """
    Összes értesítés lekérése (admin).
    
    Ez a végpont az összes értesítést visszaadja a rendszerben, függetlenül attól,
    hogy melyik felhasználóhoz vagy szerepkörhöz tartozik.
    
    Az értesítések időbélyeg szerint csökkenő sorrendben jelennek meg (legújabb elöl).
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett admin felhasználó
        skip: Hány elemet hagyjon ki (lapozáshoz)
        limit: Maximum hány elemet adjon vissza (lapozáshoz)
        include_read: Tartalmazza-e az olvasott értesítéseket is
    
    Returns:
        NotificationList: Az értesítések listája és a teljes darabszám
    """
    notifications = crud.notification.get_all_notifications(
        db, 
        skip=skip,
        limit=limit,
        include_read=include_read
    )
    # A teljes számot számoljuk, nem csak a lekérdezett elemeket
    total = db.query(crud.models.Notification).count()
    
    return {"items": notifications, "total": total}


@router.post("/", response_model=Notification)
def create_notification(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin),  # Csak admin felhasználók
    notification_in: NotificationCreate,
) -> Any:
    """
    Új értesítés létrehozása (admin).
    
    Ez a végpont lehetővé teszi az adminok számára, hogy új értesítéseket hozzanak létre
    a rendszerben. Az értesítés lehet személyre szóló vagy általános (szerepkör alapú).
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett admin felhasználó
        notification_in: Az új értesítés adatai
    
    Returns:
        Notification: A létrehozott értesítés
    """
    notification = crud.notification.create_notification(db, notification_in)
    return notification


@router.put("/{notification_id}/read", response_model=Notification)
def mark_notification_read(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    notification_id: int = Path(..., ge=1, description="Értesítés azonosítója"),
) -> Any:
    """
    Értesítés olvasottnak jelölése.
    
    Ez a végpont lehetővé teszi a felhasználók számára, hogy egy értesítést
    olvasottnak jelöljenek.
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett felhasználó
        notification_id: Az értesítés azonosítója
    
    Returns:
        Notification: A frissített értesítés
    """
    notification = crud.notification.get_notification(db, notification_id)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Az értesítés nem található"
        )
    
    # Ellenőrizzük, hogy a felhasználó jogosult-e az értesítés olvasására
    # (vagy az övé, vagy általános és a szerepköréhez illeszkedik)
    user_role = current_user.role
    if (
        notification.user_id and notification.user_id != current_user.id and 
        not current_user.role == "admin"
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs jogosultsága ehhez az értesítéshez"
        )
    
    # Ha a szerepkörök meg vannak adva, ellenőrizzük
    if (
        notification.target_roles and 
        user_role not in notification.target_roles.split(",") and
        not current_user.role == "admin"
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Nincs jogosultsága ehhez az értesítéshez"
        )
    
    updated_notification = crud.notification.mark_notification_read(db, notification_id)
    return updated_notification


@router.put("/read-all", response_model=dict)
def mark_all_notifications_read(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Összes értesítés olvasottnak jelölése.
    
    Ez a végpont lehetővé teszi a felhasználók számára, hogy az összes
    releváns értesítést olvasottnak jelöljék.
    
    Args:
        db: Adatbázis session
        current_user: Bejelentkezett felhasználó
    
    Returns:
        dict: A frissített értesítések száma
    """
    count = crud.notification.mark_all_notifications_read(db, current_user.id)
    
    return {"count": count, "message": f"{count} értesítés olvasottnak jelölve"} 