"""
Felhasználókkal kapcsolatos végpontok
"""
from typing import Any, List, Optional
import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.api.dependencies import (
   get_db, get_current_user, get_current_operator, get_current_admin
)
from app.core.security import AuthenticationError
from app.models.user import User
from app.schemas.user import (
   UserResponse, UserUpdate, UserChangePassword, UserRoleUpdate,
   UserDefaultSettingsCreate, UserDefaultSettingsResponse
)
from app.services.user_service import (
   get_user, get_users, update_user, delete_user, change_user_password,
   update_user_role, get_user_default_settings, upsert_user_default_settings,
   deactivate_user, reactivate_user
)

# API router létrehozása
router = APIRouter()

# ========================================================
# BEJELENTKEZETT FELHASZNÁLÓ VÉGPONTOK (/me végpontok)
# ========================================================

@router.get("/me", response_model=UserResponse)
def read_current_user(
   current_user: User = Depends(get_current_user)
) -> Any:
   """
   Jelenlegi bejelentkezett felhasználó adatainak lekérdezése.
   
   Ez a végpont a JWT token alapján azonosítja a bejelentkezett felhasználót 
   és visszaadja annak adatait.
   
   Returns:
       UserResponse: A bejelentkezett felhasználó adatai
   """
   # Egyszerűen visszaadjuk a current_user-t, amit a get_current_user dependency biztosít
   return current_user


@router.put("/me", response_model=UserResponse)
def update_current_user_data(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_user),
   user_in: UserUpdate,
) -> Any:
   """
   Jelenlegi bejelentkezett felhasználó adatainak frissítése.
   
   Ez a végpont lehetővé teszi a felhasználók számára, hogy frissítsék a saját
   adataikat anélkül, hogy explicit módon meg kellene adniuk a felhasználói azonosítójukat.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó (JWT token alapján)
       user_in: Frissítendő felhasználói adatok
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   """
   # Az update_user service funkciót használjuk a bejelentkezett felhasználó azonosítójával
   return update_user(db, current_user.id, user_in)


@router.post("/me/change-password", response_model=UserResponse)
def update_current_user_password(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_user),
   password_data: UserChangePassword,
) -> Any:
   """
   Bejelentkezett felhasználó jelszavának módosítása.
   
   Ez a végpont lehetővé teszi a felhasználók számára, hogy módosítsák saját
   jelszavukat a felhasználói azonosító explicit megadása nélkül. A rendszer
   ellenőrzi a jelenlegi jelszó helyességét is.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó (JWT token alapján)
       password_data: A jelszóváltoztatási adatok (régi jelszó, új jelszó)
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   
   Raises:
       HTTPException: Ha a jelenlegi jelszó nem helyes, vagy egyéb hiba történt
   """
   try:
       # Jelszó változtatás a bejelentkezett felhasználó azonosítójával
       user = change_user_password(db, current_user.id, password_data)
       
       if not user:
           raise HTTPException(
               status_code=status.HTTP_404_NOT_FOUND,
               detail="A felhasználó nem található"
           )
       
       return user
   
   except AuthenticationError as e:
       # Ha a felhasználó által megadott jelenlegi jelszó nem helyes
       raise HTTPException(
           status_code=status.HTTP_400_BAD_REQUEST,
           detail=str(e)
       )


@router.get("/me/settings", response_model=UserDefaultSettingsResponse)
def read_current_user_settings(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Jelenlegi felhasználó alapbeállításainak lekérdezése.

    Ez a végpont visszaadja a bejelentkezett felhasználó alapbeállításait,
    amelyek befolyásolhatják az alkalmazás viselkedését vagy a felhasználói élményt.

    Ha még nincsenek beállítások, automatikusan létrehoz egy üres beállítást.

    Args:
        db: Adatbázis session
        current_user: A bejelentkezett felhasználó (JWT token alapján)

    Returns:
        UserDefaultSettingsResponse: A felhasználó alapbeállításai
    """
    try:
        settings = get_user_default_settings(db, current_user.id)

        if not settings:
            try:
                empty_settings = UserDefaultSettingsCreate()
                settings = upsert_user_default_settings(db, current_user.id, empty_settings)
            except Exception as e:
                logger.error(f"Nem sikerült létrehozni az alapbeállításokat: {str(e)}")
                # Visszaadunk egy alapértelmezett objektumot hiba esetén
                return {
                    "id": -1,
                    "user_id": current_user.id,
                    "default_product_type_id": None,
                    "default_quality_grade_id": None,
                    "default_quantity_unit": "kg"
                }

        return settings
    
    except Exception as e:
        logger.error(f"Hiba a beállítások lekérdezése során: {str(e)}")
        # Visszaadunk egy alapértelmezett objektumot hiba esetén
        return {
            "id": -1,
            "user_id": current_user.id,
            "default_product_type_id": None, 
            "default_quality_grade_id": None,
            "default_quantity_unit": "kg"
        }


@router.put("/me/settings", response_model=UserDefaultSettingsResponse)
def update_current_user_settings(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    settings_data: UserDefaultSettingsCreate,
) -> Any:
    """
    Jelenlegi felhasználó alapbeállításainak frissítése.

    Ez a végpont lehetővé teszi a felhasználók számára, hogy frissítsék
    az alapbeállításaikat (pl. alapértelmezett termékbeállítások).

    Args:
        db: Adatbázis session
        current_user: A bejelentkezett felhasználó (JWT token alapján)
        settings_data: Az új beállítások

    Returns:
        UserDefaultSettingsResponse: A frissített beállítások
    """
    try:
        # Részletes napló az érkező adatokról
        print("\n===== API ENDPOINT DEBUG - USER DEFAULT SETTINGS UPDATE =====")
        print(f"User ID: {current_user.id}")
        print(f"Received settings data: {settings_data}")
        
        # Ellenőrzés, hogy nincs-e "NULL" string a bejövő adatokban
        has_null_strings = False
        settings_dict = settings_data.dict()
        for key, value in settings_dict.items():
            if isinstance(value, str) and value.upper() == "NULL":
                print(f"WARNING: Found 'NULL' string in field {key}")
                has_null_strings = True
                
        print(f"Settings data clean: {not has_null_strings}")
        
        # Tranzakció indítása - ez a upsert funkcióban lesz kezelve
        updated_settings = upsert_user_default_settings(db, current_user.id, settings_data)
        
        # Ellenőrizzük az eredményt
        if updated_settings.id == -1:
            error_msg = getattr(updated_settings, 'error', 'Ismeretlen hiba a beállítások mentése során')
            print(f"ERROR from upsert operation: {error_msg}")
            
            # Hibás műveletnél is 200 OK státuszt adunk, de error mezővel jelezzük a problémát
            return {
                "id": -1,
                "user_id": current_user.id,
                "default_product_type_id": settings_data.default_product_type_id,
                "default_quality_grade_id": settings_data.default_quality_grade_id,
                "default_quantity_unit": settings_data.default_quantity_unit,
                "default_category_id": settings_data.default_category_id,
                "error": error_msg
            }
            
        # Ellenőrizzük, hogy a visszakapott object tartalmazza-e a mentett értékeket
        print("Settings saved successfully with ID:", updated_settings.id)
        print(f"- default_product_type_id: {updated_settings.default_product_type_id}")
        print(f"- default_quality_grade_id: {updated_settings.default_quality_grade_id}")
        print(f"- default_category_id: {updated_settings.default_category_id}")
        print(f"- default_quantity_unit: {updated_settings.default_quantity_unit}")
        
        # Ellenőrző API hívás a beállítások meglétének ellenőrzésére
        verification_settings = get_user_default_settings(db, current_user.id)
        if verification_settings:
            print("Verification query successful:")
            print(f"- default_product_type_id: {verification_settings.default_product_type_id}")
            print(f"- default_quality_grade_id: {verification_settings.default_quality_grade_id}")
            print(f"- default_category_id: {verification_settings.default_category_id}")
            print(f"- default_quantity_unit: {verification_settings.default_quantity_unit}")
        else:
            print("WARNING: Verification query returned no settings!")
        
        print("===== END OF API ENDPOINT DEBUG =====\n")
        
        return updated_settings

    except Exception as e:
        import traceback
        logger.error(f"Hiba a beállítások mentése során: {str(e)}")
        print(f"EXCEPTION in API endpoint: {str(e)}")
        print(traceback.format_exc())
        
        # Visszaadunk egy objektumot a kapott adatokkal, de -1 ID-vel jelezve a hibát
        return {
            "id": -1,
            "user_id": current_user.id,
            "default_product_type_id": settings_data.default_product_type_id,
            "default_quality_grade_id": settings_data.default_quality_grade_id,
            "default_quantity_unit": settings_data.default_quantity_unit,
            "default_category_id": settings_data.default_category_id,
            "error": str(e)
        }


# ========================================================
# MINDEN FELHASZNÁLÓ LEKÉRDEZÉSE (ADMIN/ÜGYINTÉZŐ)
# ========================================================

@router.get("", response_model=List[UserResponse])
def read_users(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_operator),  # Csak operátor vagy admin férhessen hozzá
   skip: int = 0,
   limit: int = 100,
   search: Optional[str] = None,
   role: Optional[str] = None,
) -> Any:
   """
   Felhasználók listázása (csak ügyintéző és admin számára).
   
   Ez a végpont lehetővé teszi az ügyintézők és adminok számára, hogy
   listázzák a rendszerben regisztrált felhasználókat, különböző szűrésekkel.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó (csak ügyintéző vagy admin)
       skip: Kihagyandó találatok száma (lapozáshoz)
       limit: Maximális találatok száma (lapozáshoz)
       search: Keresési kifejezés (név, email, stb.)
       role: Szerepkör szerinti szűrés
   
   Returns:
       List[UserResponse]: A felhasználók listája
   """
   # A get_users service a megfelelő szűrésekkel lekéri a felhasználókat
   users = get_users(db, skip=skip, limit=limit, search=search, role=role)
   return users


# ========================================================
# SPECIFIKUS FELHASZNÁLÓK KEZELÉSE (AZONOSÍTÓ ALAPJÁN)
# ========================================================

@router.get("/{user_id}", response_model=UserResponse)
def read_user(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_user),
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
) -> Any:
   """
   Felhasználó adatainak lekérdezése azonosító alapján.
   
   Ez a végpont lehetővé teszi:
   - A saját adatok lekérdezését minden felhasználó számára
   - Más felhasználók adatainak lekérdezését ügyintéző és admin számára
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó
       user_id: A lekérdezendő felhasználó azonosítója
   
   Returns:
       UserResponse: A lekérdezett felhasználó adatai
   
   Raises:
       HTTPException: Ha nincs jogosultság vagy a felhasználó nem található
   """
   # Ellenőrizzük, hogy a felhasználó saját magát vagy mást akar-e lekérdezni
   if current_user.id != user_id and current_user.role not in ["ügyintéző", "admin"]:
       raise HTTPException(
           status_code=status.HTTP_403_FORBIDDEN,
           detail="Nincs megfelelő jogosultság"
       )
   
   # Felhasználó lekérése az adatbázisból
   user = get_user(db, user_id)
   
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND,
           detail="A felhasználó nem található"
       )
   
   return user


@router.put("/{user_id}", response_model=UserResponse)
def update_user_data(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_user),
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
   user_in: UserUpdate,
) -> Any:
   """
   Felhasználó adatainak frissítése.
   
   Ez a végpont lehetővé teszi:
   - A saját adatok frissítését minden felhasználó számára
   - Más felhasználók adatainak frissítését csak admin számára
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó
       user_id: A frissítendő felhasználó azonosítója
       user_in: A frissítendő adatok
   
   Returns:
       UserResponse: A frissített felhasználó adatai
   
   Raises:
       HTTPException: Ha nincs jogosultság vagy a felhasználó nem található
   """
   # Ellenőrizzük, hogy a felhasználó saját magát vagy mást akar-e frissíteni
   if current_user.id != user_id and current_user.role != "admin":
       raise HTTPException(
           status_code=status.HTTP_403_FORBIDDEN,
           detail="Nincs megfelelő jogosultság"
       )
   
   # Ellenőrizzük, hogy a felhasználó létezik-e
   user = get_user(db, user_id)
   
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND,
           detail="A felhasználó nem található"
       )
   
   # Frissítés és eredmény visszaadása
   return update_user(db, user_id, user_in)


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT, response_model=None)
async def delete_user_endpoint(
   user_id: int, 
   current_user: User = Depends(get_current_user),
   db: Session = Depends(get_db)
):
   """
   Felhasználó törlése.
   
   Ez a végpont lehetővé teszi:
   - Admin számára bármely felhasználó törlését
   - Felhasználók számára saját fiókjuk törlését
   
   Args:
       user_id: A törlendő felhasználó azonosítója
       current_user: A bejelentkezett felhasználó
       db: Adatbázis session
   
   Returns:
       None: 204 No Content státuszkóddal
   
   Raises:
       HTTPException: Ha nincs jogosultság vagy a felhasználó nem található
   """
   # Ellenőrzés, hogy van-e jogosultsága törölni
   if current_user.role != 'admin' and current_user.id != user_id:
       raise HTTPException(
           status_code=status.HTTP_403_FORBIDDEN, 
           detail="Nem jogosult a felhasználó törlésére"
       )
   
   # Felhasználó lekérése
   user = db.query(User).filter(User.id == user_id).first()
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND, 
           detail="Felhasználó nem található"
       )
   
   # Felhasználó törlése
   db.delete(user)
   db.commit()
   
   # 204 No Content státuszkód (nincs visszatérési érték)
   return None


@router.post("/{user_id}/change-password", response_model=UserResponse)
def update_user_password(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_user),
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
   password_data: UserChangePassword,
) -> Any:
   """
   Felhasználó jelszavának módosítása (csak saját jelszó).
   
   Ez a végpont lehetővé teszi a felhasználóknak a saját jelszavuk módosítását.
   A jelszó módosításához szükséges a jelenlegi jelszó ismerete is.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett felhasználó
       user_id: A felhasználó azonosítója, akinek a jelszavát módosítani kell
       password_data: A jelszóváltoztatási adatok (régi jelszó, új jelszó)
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   
   Raises:
       HTTPException: Ha nincs jogosultság, a felhasználó nem található vagy a jelszó nem helyes
   """
   # Csak a saját jelszavát változtathatja meg
   if current_user.id != user_id:
       raise HTTPException(
           status_code=status.HTTP_403_FORBIDDEN,
           detail="Csak a saját jelszavát változtathatja meg"
       )
   
   try:
       # Jelszó változtatás
       user = change_user_password(db, user_id, password_data)
       
       if not user:
           raise HTTPException(
               status_code=status.HTTP_404_NOT_FOUND,
               detail="A felhasználó nem található"
           )
       
       return user
   
   except AuthenticationError as e:
       # Ha a jelenlegi jelszó nem helyes
       raise HTTPException(
           status_code=status.HTTP_400_BAD_REQUEST,
           detail=str(e)
       )


# ========================================================
# SZEREPKÖR ÉS FELHASZNÁLÓI ÁLLAPOT KEZELÉS
# ========================================================

@router.put("/{user_id}/role", response_model=UserResponse)
def update_user_role_endpoint(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_admin),  # Csak admin érheti el
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
   role_data: UserRoleUpdate,
) -> Any:
   """
   Felhasználó szerepkörének módosítása (csak admin).
   
   Ez a végpont lehetővé teszi az adminok számára, hogy módosítsák
   más felhasználók szerepkörét (termelő, ügyintéző, admin).
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett admin
       user_id: A módosítandó felhasználó azonosítója
       role_data: Az új szerepkör
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   
   Raises:
       HTTPException: Ha az admin a saját szerepkörét próbálja módosítani, vagy a felhasználó nem található
   """
   # Admin nem változtathatja meg saját szerepkörét
   if current_user.id == user_id:
       raise HTTPException(
           status_code=status.HTTP_400_BAD_REQUEST,
           detail="Nem változtathatja meg saját szerepkörét"
       )
   
   # Szerepkör változtatás
   user = update_user_role(db, user_id, role_data)
   
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND,
           detail="A felhasználó nem található"
       )
   
   return user


@router.put("/{user_id}/activate", response_model=UserResponse)
def activate_user_endpoint(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_admin),  # Csak admin érheti el
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
) -> Any:
   """
   Felhasználó aktiválása (csak admin).
   
   Ez a végpont lehetővé teszi az adminok számára, hogy aktiváljanak
   egy deaktivált felhasználót, így az újra be tud jelentkezni a rendszerbe.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett admin
       user_id: Az aktiválandó felhasználó azonosítója
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   
   Raises:
       HTTPException: Ha a felhasználó nem található
   """
   # Aktiválás
   user = reactivate_user(db, user_id)
   
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND,
           detail="A felhasználó nem található"
       )
   
   return user


@router.put("/{user_id}/deactivate", response_model=UserResponse)
def deactivate_user_endpoint(
   *,
   db: Session = Depends(get_db),
   current_user: User = Depends(get_current_admin),  # Csak admin érheti el
   user_id: int = Path(..., ge=1, description="Felhasználó azonosító"),
) -> Any:
   """
   Felhasználó deaktiválása (csak admin).
   
   Ez a végpont lehetővé teszi az adminok számára, hogy deaktiváljanak
   egy felhasználót, így az nem tud többé bejelentkezni a rendszerbe.
   Ez egy alternatíva a felhasználó törlése helyett.
   
   Args:
       db: Adatbázis session
       current_user: A bejelentkezett admin
       user_id: A deaktiválandó felhasználó azonosítója
   
   Returns:
       UserResponse: A frissített felhasználói adatok
   
   Raises:
       HTTPException: Ha az admin a saját fiókját próbálja deaktiválni, vagy a felhasználó nem található
   """
   # Admin nem deaktiválhatja saját fiókját
   if current_user.id == user_id:
       raise HTTPException(
           status_code=status.HTTP_400_BAD_REQUEST,
           detail="Nem deaktiválhatja saját fiókját"
       )
   
   # Deaktiválás
   user = deactivate_user(db, user_id)
   
   if not user:
       raise HTTPException(
           status_code=status.HTTP_404_NOT_FOUND,
           detail="A felhasználó nem található"
       )
   
   return user
