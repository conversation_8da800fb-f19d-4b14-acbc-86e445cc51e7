"""
API endpoints for user saved filters
"""
from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.orm import Session

from app.api.dependencies import get_db, get_current_user
from app.models.user import User
from app.schemas.saved_filter import SavedFilterCreate, SavedFilterUpdate, SavedFilterResponse
from app.services import user_saved_filter_service

# Create router
router = APIRouter()


@router.get("", response_model=List[SavedFilterResponse])
def list_saved_filters(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_type: Optional[str] = Query(None, description="Filter by type (e.g., 'offer')"),
    skip: int = Query(0, description="Number of records to skip"),
    limit: int = Query(100, description="Maximum number of records to return")
) -> Any:
    """
    List saved filters for the current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_type: Optional filter type to filter by
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)
        
    Returns:
        List of saved filters
    """
    return user_saved_filter_service.get_user_saved_filters(
        db=db,
        current_user_id=current_user.id,
        filter_type=filter_type,
        skip=skip,
        limit=limit
    )


@router.get("/{filter_id}", response_model=SavedFilterResponse)
def get_saved_filter(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_id: int = Path(..., description="ID of the filter to retrieve")
) -> Any:
    """
    Get a specific saved filter by ID.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_id: ID of the filter to retrieve
        
    Returns:
        The saved filter
    """
    # Check if user is admin
    is_admin = current_user.role == "admin"
    
    return user_saved_filter_service.get_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user.id,
        is_admin=is_admin
    )


@router.post("", response_model=SavedFilterResponse, status_code=status.HTTP_201_CREATED)
def create_saved_filter(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_in: SavedFilterCreate
) -> Any:
    """
    Create a new saved filter.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_in: Filter data to create
        
    Returns:
        The created saved filter
    """
    return user_saved_filter_service.create_user_saved_filter(
        db=db,
        filter_in=filter_in,
        current_user_id=current_user.id
    )


@router.put("/{filter_id}", response_model=SavedFilterResponse)
def update_saved_filter(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_id: int = Path(..., description="ID of the filter to update"),
    filter_in: SavedFilterUpdate
) -> Any:
    """
    Update an existing saved filter.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_id: ID of the filter to update
        filter_in: New filter data
        
    Returns:
        The updated saved filter
    """
    # Check if user is admin
    is_admin = current_user.role == "admin"
    
    return user_saved_filter_service.update_user_saved_filter(
        db=db,
        filter_id=filter_id,
        filter_in=filter_in,
        current_user_id=current_user.id,
        is_admin=is_admin
    )


@router.delete("/{filter_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_saved_filter(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_id: int = Path(..., description="ID of the filter to delete")
) -> None:
    """
    Delete a saved filter.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_id: ID of the filter to delete
    """
    # Check if user is admin
    is_admin = current_user.role == "admin"
    
    user_saved_filter_service.delete_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user.id,
        is_admin=is_admin
    )


@router.post("/{filter_id}/set-default", response_model=SavedFilterResponse)
def set_default_filter(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    filter_id: int = Path(..., description="ID of the filter to set as default")
) -> Any:
    """
    Set a filter as the default for the user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        filter_id: ID of the filter to set as default
        
    Returns:
        The updated saved filter
    """
    # Check if user is admin
    is_admin = current_user.role == "admin"
    
    return user_saved_filter_service.set_default_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user.id,
        is_admin=is_admin
    )