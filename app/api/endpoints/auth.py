"""
Autentikációs végpontok
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api.dependencies import get_db, get_current_user
from app.core.config import settings
from app.models.user import User
from app.schemas.user import (
    Token, UserCreate, UserResponse, 
    UserPasswordReset, UserPasswordResetConfirm
)
from app.services.user_service import (
    create_user, authenticate_user, create_user_token,
    activate_user, create_password_reset_token, reset_password_with_token,
    get_user_by_email
)
from app.services.email_service import (
    send_registration_email, send_password_reset_email
)

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate,
    background_tasks: BackgroundTasks
) -> Any:
    """
    <PERSON>j felhasználó regisztrálása
    """
    try:
        # Új felhasználó létrehozása
        user = create_user(db, user_in)
        
        # Aktivációs email küldése
        await send_registration_email(
            background_tasks=background_tasks,
            email_to=user.email,
            activation_token=user.activation_token,
            user_name=user.contact_name,
            username=user.email,  # Using email as username since there's no username field
            role=user.role
        )
        
        return user
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/login", response_model=Token)
def login(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 kompatibilis token bejelentkezés, JWT token visszaadása
    """
    # Felhasználó autentikálása
    user = authenticate_user(db, form_data.username, form_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Helytelen email cím vagy jelszó",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Ellenőrizzük, hogy a felhasználó aktív-e
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="A felhasználó még nincs aktiválva"
        )
    
    # JWT token létrehozása
    access_token = create_user_token(user)
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.get("/activate-account")
def activate_account(
    *,
    db: Session = Depends(get_db),
    token: str
) -> Any:
    """
    Felhasználói fiók aktiválása token alapján
    """
    user = activate_user(db, token)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Érvénytelen vagy lejárt aktivációs token"
        )
    
    return {"message": "Felhasználói fiók sikeresen aktiválva"}


@router.post("/password-reset-request")
async def password_reset_request(
    *,
    db: Session = Depends(get_db),
    reset_data: UserPasswordReset,
    background_tasks: BackgroundTasks
) -> Any:
    """
    Jelszó visszaállítási kérelem
    """
    user = get_user_by_email(db, reset_data.email)
    
    # Mindig térjünk vissza sikerrel, hogy ne lehessen kitalálni, mely email címek vannak regisztrálva
    if not user:
        return {"message": "Ha a megadott email cím regisztrálva van a rendszerben, akkor elküldtük a jelszó-visszaállítási linket"}
    
    # Token létrehozása
    token = create_password_reset_token(db, reset_data.email)
    
    if token:
        # Email küldése
        await send_password_reset_email(
            background_tasks=background_tasks,
            email_to=reset_data.email,
            reset_token=token,
            user_name=user.contact_name if user else None
        )
    
    return {"message": "Ha a megadott email cím regisztrálva van a rendszerben, akkor elküldtük a jelszó-visszaállítási linket"}


@router.post("/password-reset-confirm")
def password_reset_confirm(
    *,
    db: Session = Depends(get_db),
    reset_data: UserPasswordResetConfirm
) -> Any:
    """
    Jelszó visszaállítása token alapján
    """
    user = reset_password_with_token(db, reset_data.token, reset_data.new_password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Érvénytelen vagy lejárt jelszó-visszaállítási token"
        )
    
    return {"message": "Jelszó sikeresen visszaállítva"}


@router.get("/me", response_model=UserResponse)
def read_current_user(
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Jelenlegi bejelentkezett felhasználó adatainak lekérdezése
    """
    return current_user