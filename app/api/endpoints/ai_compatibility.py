"""
AI kompatibilitási végpontok
Ezek a végpontok az AI API kliensek kéréseinek kezelésére szolgálnak
"""
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from app.utils.logging import logger

router = APIRouter()

@router.get("/models")
async def list_models():
    """
    OpenAI-kompatibilis végpont a elérhető modellek lekérdezéséhez.
    Ennek a végpontnak a célja, hogy kezelje az AI kliensek váratlan kéréseit
    anélkül, hogy 404-es hibákat generálna.
    """
    # Alapvető információk egy mezőgazdasági rendszerről
    # A valódi OpenAI kompatibilis választ utánozza
    return {
        "object": "list",
        "data": [
            {
                "id": "termelo-agricultural-api",
                "object": "model",
                "created": 1683900000,
                "owned_by": "termelo-system",
                "permission": [],
                "root": "termelo-agricultural-api",
                "parent": None
            }
        ],
        "system_message": "Ez egy POM APP API-ja, nem AI szolgáltatás."
    }

@router.post("/completions")
@router.post("/chat/completions")
async def completions(request: Request):
    """
    OpenAI-kompatibilis végpont a szövegkérésekhez.
    Ennek a végpontnak a célja, hogy kezelje az AI kliensek váratlan kéréseit
    anélkül, hogy 404-es hibákat generálna.
    """
    try:
        # Naplózás alacsonyabb szinten, hogy ne zavarjon
        logger.debug("AI kompatibilitási végpontot hívtak meg")
        
        return {
            "id": "agricultural-response",
            "object": "text_completion",
            "created": 1683900000,
            "model": "termelo-agricultural-api",
            "choices": [
                {
                    "text": "Ez az API nem támogat AI szöveggenerálást. Ez egy POM APP API-ja.",
                    "index": 0,
                    "logprobs": None,
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
    except Exception as e:
        logger.error(f"Hiba az AI kompatibilitási végpont feldolgozásakor: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={"error": "Érvénytelen kérés a AI kompatibilitási végponthoz"}
        )