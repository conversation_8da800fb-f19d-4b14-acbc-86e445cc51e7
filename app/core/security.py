from datetime import datetime, timedelta
from typing import Any, Optional, Union

from jose import jwt
from passlib.context import CryptContext

from app.core.config import settings

# Jelsz<PERSON> titkosítás konfigurálása
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Hitelesítési (JWT) kivétel
class AuthenticationError(Exception):
    """Hitelesítési hiba kivétel"""
    pass


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """JWT token létrehozása a felhasználó azonosításához"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    # <PERSON><PERSON>rizzük a subject típusát és megfelelően kezeljük
    if isinstance(subject, dict):
        # Ha dictionary, a mezőket közvetlenül a token payload-ba helyezzük
        to_encode = {"exp": expire, **subject}
    else:
        # Egyéb esetben a sub mezőbe tesszük
        to_encode = {"exp": expire, "sub": str(subject)}
    
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Ellenőrzi, hogy a megadott jelszó megegyezik-e a hash-elt jelszóval
    
    Args:
        plain_password: A tiszta szöveges jelszó
        hashed_password: A hash-elt jelszó
        
    Returns:
        bool: True ha a jelszavak megegyeznek, False ha nem
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Jelszó hash létrehozása
    
    Args:
        password: A tiszta szöveges jelszó
        
    Returns:
        str: A hash-elt jelszó
    """
    return pwd_context.hash(password)
