"""
Alkalmazás indítási és leállítási események
"""
import logging
from typing import Callable

from fastapi import FastAPI
from sqlalchemy.engine import Engine
from sqlalchemy import event

# Logger beállítása
logger = logging.getLogger(__name__)


# SQLite esetén a foreign key constraints engedélyezése
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    SQLite esetén a foreign key constraints engedélyezése
    """
    cursor = dbapi_connection.cursor()
    cursor.execute("PRAGMA foreign_keys=ON")
    cursor.close()


def create_start_app_handler(app: FastAPI) -> Callable:
    """
    Alkalmazás indításakor futó esemény
    
    Args:
        app: FastAPI alkalmazás
        
    Returns:
        Callable: Az eseménykezelő függvény
    """
    async def start_app() -> None:
        """
        Az alkalmazás indításakor futó függvény.
        - Adatbázis inicializálása
        - Egyéb indítási feladatok
        """
        # Itt lehet további inicializációs lépéseket végrehajtani
        logger.info("Alkalmazás indítása...")
    
    return start_app


def create_stop_app_handler(app: FastAPI) -> Callable:
    """
    Alkalmazás leállításakor futó esemény
    
    Args:
        app: FastAPI alkalmazás
        
    Returns:
        Callable: Az eseménykezelő függvény
    """
    async def stop_app() -> None:
        """
        Az alkalmazás leállításakor futó függvény.
        - Kapcsolatok lezárása
        - Erőforrások felszabadítása
        """
        # Itt lehet a kapcsolatok lezárását és erőforrások felszabadítását végrehajtani
        logger.info("Alkalmazás leállítása...")
    
    return stop_app