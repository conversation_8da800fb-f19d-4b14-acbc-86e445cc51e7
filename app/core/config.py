# app/core/config.py
import os
import secrets
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, EmailStr, PostgresDsn, validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """
    Alkalmazás be<PERSON>llítások, környezeti változókból vagy .env fájlból betöltve
    """
    # Alap alkalmazás beállítások
    APP_NAME: str
    DEBUG: bool = False
    ENVIRONMENT: str
    API_V1_STR: str = "/api/v1"
    
    # Backend beállítások
    BACKEND_HOST: str
    BACKEND_PORT: int
    
    # CORS beállítások hozzáadása
    CORS_ORIGINS: List[str] = [
        "http://localhost",
        "http://localhost:8000",
        "http://localhost:8501",
        "http://127.0.0.1:8000",
        "http://127.0.0.1:8501"
    ]
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Adatbázis beállítások
    DATABASE_URL: Optional[PostgresDsn] = None
    DB_HOST: str
    DB_PORT: int
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str) and v:
            return v
            
        # Biztonságos értékek használata
        host = values.get("DB_HOST", "db")
        port = values.get("DB_PORT", 5432)
        user = values.get("DB_USER", "postgres")
        password = values.get("DB_PASSWORD", "postgres")
        db = values.get("DB_NAME", "termelo_db")
        
        return f"postgresql://{user}:{password}@{host}:{port}/{db}"
    
    # JWT beállítások
    SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Email beállítások
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # Streamlit beállítások
    STREAMLIT_HOST: str
    STREAMLIT_PORT: int
    STREAMLIT_BROWSER_SERVER_ADDRESS: str
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Beállítások példány létrehozása, a környezeti változók vagy .env fájl alapján
settings = Settings()
