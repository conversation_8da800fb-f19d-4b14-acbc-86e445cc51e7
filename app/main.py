# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy import inspect

from .api.api import api_router
from .api.endpoints import ai_compatibility
from .core.config import settings
from .db.init_db import init_db
from .db.session import SessionLocal, engine
from .db.base import Base
from .utils.logging import logger

app = FastAPI(
    title="Mezőgazdasági Termékkezelő API",
    description="Termelői ajánlatok kezelése",
    version="1.0.0"
)

# CORS beállítások
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API router hozzáadása
app.include_router(api_router, prefix="/api")

# OpenAI-kompatibilis végpontok a v1 prefixszel érkező AI kérésekhez
app.include_router(ai_compatibility.router, prefix="/v1", tags=["ai_compatibility"])

def create_tables_if_not_exist():
    """
    Ellenőrzi az összes szükséges tábla meglétét, 
    és létrehozza őket, ha bármelyik hiányzik.
    """
    try:
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        # Kinyerjük az összes modellhez tartozó tábla nevét
        model_tables = [table.name for table in Base.metadata.tables.values()]
        
        # Ellenőrizzük, hogy minden szükséges tábla létezik-e
        missing_tables = set(model_tables) - set(existing_tables)
        
        if missing_tables:
            logger.info(f"Hiányzó táblák: {missing_tables}")
            logger.info("Adatbázis táblák létrehozása...")
            Base.metadata.create_all(bind=engine)
            logger.info("Adatbázis táblák sikeresen létrehozva!")
        else:
            logger.info("Minden adatbázis tábla már létezik.")
    except Exception as e:
        logger.error(f"Hiba az adatbázis táblák létrehozásakor: {str(e)}")

@app.get("/")
async def root():
    return JSONResponse(
        content={
            "message": "Mezőgazdasági Termékkezelő API",
            "docs": "/docs",
            "status": "running"
        }
    )

@app.on_event("startup")
def startup_event():
    """
    Alkalmazás indításakor végrehajtandó műveletek.
    
    Ez a függvény akkor fut le, amikor az alkalmazás elindul.
    Ellenőrzi és létrehozza a hiányzó adatbázis táblákat,
    majd betölti a kezdeti adatokat.
    """
    # Először ellenőrizzük és létrehozzuk a hiányzó táblákat
    create_tables_if_not_exist()
    
    # Kezdeti adatok betöltése
    init_db()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
