"""
Alembic migr<PERSON><PERSON><PERSON> környezet
"""
import os
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

from app.core.config import settings
from app.db.base import Base
from app.models import __all__ as models

# Adatbázis URL a .env fájlból
config = context.config
config.set_main_option("sqlalchemy.url", str(settings.DATABASE_URL))

# Logging beállítása
fileConfig(config.config_file_name)

# Modell metaadatok hozzáadása a migrációkhoz
target_metadata = Base.metadata


def run_migrations_offline():
    """
    Migrációk futtatása offline módban - SQL szkript generálás.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """
    Migrációk futtatása online módban - közvetlen adatbázis kapcsolattal.
    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()