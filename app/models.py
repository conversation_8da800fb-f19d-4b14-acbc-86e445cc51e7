# app/models.py
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Numeric, Text, Date, CheckConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base

# Közös Mixin az időbélyegekhez
class TimestampMixin:
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)


# User modellek
class User(Base, TimestampMixin):
    """User modell - A rendszer kü<PERSON>önböző felhasz<PERSON>lói (termelők, ügyintézők, adminok)"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)
    company_name = Column(String(255))
    tax_id = Column(String(50))  # Adószám
    contact_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=False, nullable=False)
    activation_token = Column(String(255))


class UserDefaultSettings(Base, TimestampMixin):
    """Felhasználói alapbeállítások - A felhasználó által gyakran használt alapértékek"""
    __tablename__ = "user_default_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    default_product_type_id = Column(Integer, ForeignKey("product_types.id"))
    default_quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    default_quantity_unit = Column(String(10), default="kg", nullable=False)  # 'kg' vagy 'tonna'


class PasswordResetToken(Base, TimestampMixin):
    """Jelszó-visszaállítási token"""
    __tablename__ = "password_reset_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), nullable=False, unique=True)
    expires_at = Column(DateTime, nullable=False, default=lambda: func.now())
    is_used = Column(Boolean, default=False, nullable=False)


# Termék modellek
class ProductCategory(Base, TimestampMixin):
    """Termékkategória modell"""
    __tablename__ = "product_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)


class ProductType(Base, TimestampMixin):
    """Terméktípus modell"""
    __tablename__ = "product_types"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, ForeignKey("product_categories.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    has_quality_grades = Column(Boolean, default=True, nullable=False)
    
    # Kapcsolatok
    category = relationship("ProductCategory")
    quality_grades = relationship("QualityGrade", back_populates="product_type")


class QualityGrade(Base, TimestampMixin):
    """Minőségi besorolás modell"""
    __tablename__ = "quality_grades"
    
    id = Column(Integer, primary_key=True, index=True)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    name = Column(String(50), nullable=False)
    min_shoulder_diameter = Column(Numeric(5,2))
    max_shoulder_diameter = Column(Numeric(5,2))
    min_length = Column(Numeric(5,2))
    max_length = Column(Numeric(5,2))
    description = Column(Text)
    
    # Kapcsolatok
    product_type = relationship("ProductType", back_populates="quality_grades")


# Ajánlat modellek
class Offer(Base, TimestampMixin):
    """Ajánlat modell - Termelők által leadott ajánlatok adatai"""
    __tablename__ = "offers"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    
    quantity_in_kg = Column(Numeric(10, 2), nullable=False)
    delivery_date = Column(Date, nullable=False)
    
    status = Column(
        String(50), 
        nullable=False,
        default="CREATED",
    )
    
    confirmed_quantity = Column(Numeric(10, 2))
    confirmed_price = Column(Numeric(10, 2))
    note = Column(Text)
    
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Ellenőrzések
    __table_args__ = (
        CheckConstraint('quantity_in_kg > 0', name='check_quantity_positive'),
        CheckConstraint(
            "status IN ('CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED')",
            name='check_status_values'
        ),
        CheckConstraint('confirmed_quantity IS NULL OR confirmed_quantity > 0', name='check_confirmed_quantity'),
        CheckConstraint('confirmed_price IS NULL OR confirmed_price > 0', name='check_confirmed_price'),
        {'sqlite_autoincrement': True},
    )


class OfferLog(Base, TimestampMixin):
    """Ajánlat napló modell - Az ajánlatok státuszváltozásainak naplózása"""
    __tablename__ = "offer_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    offer_id = Column(Integer, ForeignKey("offers.id"), nullable=False)
    old_status = Column(String(50))
    new_status = Column(String(50), nullable=False)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    note = Column(Text)
    
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )

# Kapcsolatok definiálása külön
User.offers = relationship("Offer", foreign_keys=[Offer.user_id], backref="user")
User.created_offers = relationship("Offer", foreign_keys=[Offer.created_by_user_id], backref="created_by_user")
User.default_settings = relationship("UserDefaultSettings", backref="user", uselist=False)
User.password_reset_tokens = relationship("PasswordResetToken", backref="user")

ProductType.offers = relationship("Offer", backref="product_type")
QualityGrade.offers = relationship("Offer", backref="quality_grade")
Offer.logs = relationship("OfferLog", backref="offer")
OfferLog.user = relationship("User", foreign_keys=[OfferLog.changed_by])
