<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> véglegesítve</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4CAF50;
            margin: 0;
        }
        .status-finalized {
            background-color: #1976d2;
            color: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
        }
        .status-accepted {
            background-color: #4CAF50;
            color: white;
        }
        .status-rejected {
            background-color: #f44336;
            color: white;
        }
        .offer-summary {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .offer-summary h3 {
            color: #333;
            margin-top: 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .important-info {
            background-color: #e3f2fd;
            border: 1px solid #1976d2;
            color: #0d47a1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .button:hover {
            background-color: #45a049;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>POM APP</h1>
            <p>Ajánlat véglegesítve</p>
        </div>
        
        <p>Kedves {{ producer_name }}!</p>
        
        <div class="status-finalized {{ 'status-accepted' if final_status == 'ACCEPTED' else 'status-rejected' }}">
            <h2>📋 Ajánlata véglegesítve lett!</h2>
            <p>Végső státusz: {{ 'ELFOGADVA' if final_status == 'ACCEPTED' else 'ELUTASÍTVA' }}</p>
        </div>
        
        <p>Értesítjük, hogy az alábbi ajánlata lezárásra került:</p>
        
        <div class="offer-summary">
            <h3>Ajánlat összegzése:</h3>
            <div class="detail-row">
                <strong>Ajánlat azonosító:</strong>
                <span>{{ offer_id }}</span>
            </div>
            <div class="detail-row">
                <strong>Termék:</strong>
                <span>{{ product_name }}</span>
            </div>
            <div class="detail-row">
                <strong>Mennyiség:</strong>
                <span>{{ quantity }} {{ unit }}</span>
            </div>
            <div class="detail-row">
                <strong>Egységár:</strong>
                <span>{{ price }} Ft/{{ unit }}</span>
            </div>
            <div class="detail-row">
                <strong>Összérték:</strong>
                <span>{{ total_value }} Ft</span>
            </div>
            <div class="detail-row">
                <strong>Szállítási dátum:</strong>
                <span>{{ delivery_date }}</span>
            </div>
            <div class="detail-row">
                <strong>Véglegesítés dátuma:</strong>
                <span>{{ finalized_date }}</span>
            </div>
        </div>
        
        {% if final_status == 'ACCEPTED' %}
        <div class="important-info">
            <h3>🎉 Gratulálunk! Ajánlatát elfogadtuk!</h3>
            <p>Kérjük, készüljön fel a termék szállítására a megadott időpontban.</p>
            <ul>
                <li>Gondoskodjon a termék megfelelő csomagolásáról</li>
                <li>Készítse elő a szükséges dokumentumokat</li>
                <li>Tartsa be a megbeszélt szállítási határidőt</li>
            </ul>
        </div>
        {% else %}
        <div class="important-info">
            <h3>ℹ️ Sajnáljuk, ajánlatát ezúttal nem tudtuk elfogadni</h3>
            <p>{{ rejection_reason|default('További részletekért kérjük, lépjen kapcsolatba velünk.') }}</p>
            <p>Bízunk benne, hogy a jövőben sikeresebb együttműködést alakíthatunk ki!</p>
        </div>
        {% endif %}
        
        <div style="text-align: center;">
            <a href="{{ offer_url }}" class="button">Ajánlat részleteinek megtekintése</a>
        </div>
        
        <p>Köszönjük, hogy rendszerünket használja!</p>
        
        <div class="footer">
            <p>Ha kérdése van, kérjük forduljon ügyfélszolgálatunkhoz.</p>
            <p>Ez egy automatikusan generált e-mail, kérjük ne válaszoljon rá.</p>
            <p>&copy; 2025 POM APP. Minden jog fenntartva.</p>
        </div>
    </div>
</body>
</html>