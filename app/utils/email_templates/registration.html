<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> re<PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333 !important;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff !important;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4CAF50 !important;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            color: #666666 !important;
            font-size: 16px;
        }
        p {
            color: #333333 !important;
            font-size: 14px;
            margin: 10px 0;
        }
        .welcome-box {
            background-color: #e8f5e9 !important;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .welcome-box h2 {
            color: #2e7d32 !important;
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .welcome-box p {
            color: #2e7d32 !important;
            font-weight: bold;
            font-size: 16px;
        }
        .info-section {
            background-color: #f5f5f5 !important;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info-section h3 {
            color: #333333 !important;
            margin-top: 0;
        }
        .info-section p {
            color: #333333 !important;
            margin: 5px 0;
        }
        .info-section strong {
            color: #2e7d32 !important;
        }
        .activation-section {
            background-color: #fff3e0 !important;
            border: 2px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .activation-section h3 {
            color: #e65100 !important;
            margin-top: 0;
            font-size: 18px;
        }
        .activation-section p {
            color: #bf360c !important;
            font-weight: bold;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #4CAF50 !important;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
        }
        .button:hover {
            background-color: #45a049 !important;
        }
        .activation-button {
            background-color: #ff9800 !important;
            color: #ffffff !important;
            font-size: 18px;
            padding: 15px 35px;
        }
        .activation-button:hover {
            background-color: #f57c00 !important;
        }
        ul {
            color: #333333 !important;
        }
        li {
            color: #333333 !important;
            margin: 5px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dddddd;
            text-align: center;
            font-size: 12px;
            color: #666666 !important;
        }
        .footer p {
            color: #666666 !important;
            font-size: 12px;
        }
        /* Dark mode prevention */
        @media (prefers-color-scheme: dark) {
            body, .container {
                background-color: #ffffff !important;
            }
            p, .header p, .welcome-box p, .info-section p, .activation-section p {
                color: #333333 !important;
            }
            .footer p {
                color: #666666 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>POM APP</h1>
            <p>Üdvözöljük!</p>
        </div>
        
        <div class="welcome-box">
            <h2>Kedves {{ user_name }}!</h2>
            <p>Sikeresen regisztrált a POM APP-ba!</p>
        </div>
        
        <div class="activation-section">
            <h3>🔐 Fiók aktiválás szükséges!</h3>
            <p>A rendszer használatához először aktiválnia kell a fiókját.</p>
            <p>Kattintson az alábbi gombra a fiók aktiválásához:</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <a href="{{ activation_url }}" class="button activation-button">Fiók aktiválása</a>
            </div>
        </div>
        
        <p>Örömmel értesítjük, hogy fiókja létrehozása sikeresen megtörtént. A fiók aktiválása után használhatja rendszerünk minden funkcióját.</p>
        
        <div class="info-section">
            <h3>Fiók adatai:</h3>
            <p><strong>Felhasználónév:</strong> {{ username }}</p>
            <p><strong>E-mail cím:</strong> {{ email }}</p>
            <p><strong>Szerepkör:</strong> {{ role }}</p>
            <p><strong>Regisztráció dátuma:</strong> {{ registration_date }}</p>
        </div>
        
        <p>A rendszerbe való belépéshez használja a regisztráció során megadott felhasználónevet és jelszót.</p>
        
        <div style="text-align: center;">
            <a href="{{ login_url }}" class="button">Belépés a rendszerbe</a>
        </div>
        
        <h3>Első lépések:</h3>
        <ul>
            <li>Jelentkezzen be a fiókjába</li>
            <li>Töltse ki a profil adatait</li>
            <li>Fedezze fel a rendelkezésre álló funkciókat</li>
            <li>Kezdje el használni a rendszert</li>
        </ul>
        
        <div class="footer">
            <p>Ha bármilyen kérdése van, forduljon hozzánk bizalommal.</p>
            <p>Ez egy automatikusan generált e-mail, kérjük ne válaszoljon rá.</p>
            <p>&copy; 2025 POM APP. Minden jog fenntartva.</p>
        </div>
    </div>
</body>
</html>