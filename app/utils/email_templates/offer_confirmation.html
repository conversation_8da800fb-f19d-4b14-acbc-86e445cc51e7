<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> v<PERSON>gazo<PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4CAF50;
            margin: 0;
        }
        .status-confirmed {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
        }
        .offer-details {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .offer-details h3 {
            color: #4CAF50;
            margin-top: 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .button:hover {
            background-color: #45a049;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .next-steps {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>POM APP</h1>
            <p>Ajánlat visszaigazolás</p>
        </div>
        
        <p>Kedves {{ producer_name }}!</p>
        
        <div class="status-confirmed">
            <h2>✓ Ajánlata sikeresen visszaigazolva!</h2>
        </div>
        
        <p>Örömmel értesítjük, hogy az alábbi ajánlatát visszaigazoltuk és feldolgozás alatt áll:</p>
        
        <div class="offer-details">
            <h3>Ajánlat részletei:</h3>
            <div class="detail-row">
                <strong>Ajánlat azonosító:</strong>
                <span>{{ offer_id }}</span>
            </div>
            <div class="detail-row">
                <strong>Termék:</strong>
                <span>{{ product_name }}</span>
            </div>
            <div class="detail-row">
                <strong>Mennyiség:</strong>
                <span>{{ quantity }} {{ unit }}</span>
            </div>
            <div class="detail-row">
                <strong>Ár:</strong>
                <span>{{ price }} Ft/{{ unit }}</span>
            </div>
            <div class="detail-row">
                <strong>Szállítási dátum:</strong>
                <span>{{ delivery_date }}</span>
            </div>
            <div class="detail-row">
                <strong>Létrehozás dátuma:</strong>
                <span>{{ created_date }}</span>
            </div>
            <div class="detail-row">
                <strong>Visszaigazolás dátuma:</strong>
                <span>{{ confirmed_date }}</span>
            </div>
        </div>
        
        <div class="next-steps">
            <h3>Következő lépések:</h3>
            <ul>
                <li>Ajánlatát továbbítottuk a feldolgozó részlegnek</li>
                <li>Hamarosan értesítjük az ajánlat elfogadásáról vagy elutasításáról</li>
                <li>Kérjük, kísérje figyelemmel az ajánlat státuszát a rendszerben</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="{{ offer_url }}" class="button">Ajánlat megtekintése</a>
        </div>
        
        <p>Ha bármilyen kérdése van az ajánlattal kapcsolatban, kérjük lépjen kapcsolatba velünk.</p>
        
        <div class="footer">
            <p>Ez egy automatikusan generált e-mail, kérjük ne válaszoljon rá.</p>
            <p>&copy; 2025 POM APP. Minden jog fenntartva.</p>
        </div>
    </div>
</body>
</html>