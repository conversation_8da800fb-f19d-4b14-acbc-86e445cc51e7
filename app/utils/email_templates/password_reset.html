<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333 !important;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff !important;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4CAF50 !important;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            color: #666666 !important;
            font-size: 16px;
        }
        p {
            color: #333333 !important;
            font-size: 14px;
            margin: 10px 0;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #4CAF50 !important;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
            font-size: 16px;
        }
        .button:hover {
            background-color: #45a049 !important;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dddddd;
            text-align: center;
            font-size: 12px;
            color: #666666 !important;
        }
        .footer p {
            color: #666666 !important;
            font-size: 12px;
        }
        .warning {
            background-color: #fff3cd !important;
            border: 1px solid #ffeaa7;
            color: #856404 !important;
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning strong {
            color: #856404 !important;
        }
        /* Dark mode prevention */
        @media (prefers-color-scheme: dark) {
            body, .container {
                background-color: #ffffff !important;
            }
            p, .header p {
                color: #333333 !important;
            }
            .footer p {
                color: #666666 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>POM APP</h1>
            <p>Jelszó visszaállítás</p>
        </div>
        
        <p>Kedves {{ user_name }}!</p>
        
        <p>Jelszó visszaállítási kérelmet kaptunk az Ön fiókjához kapcsolódóan a POM APP-ban.</p>
        
        <p>Az új jelszó beállításához kattintson az alábbi gombra:</p>
        
        <div style="text-align: center;">
            <a href="{{ reset_link }}" class="button">Jelszó visszaállítása</a>
        </div>
        
        <div class="warning">
            <strong>Figyelem!</strong> Ez a link {{ expiry_hours }} órán keresztül érvényes. Ezt követően új jelszó visszaállítási kérelmet kell indítania.
        </div>
        
        <p>Ha nem Ön kérte a jelszó visszaállítást, akkor nyugodtan hagyja figyelmen kívül ezt az e-mailt. A jelszava nem fog megváltozni.</p>
        
        <p>Biztonsági okokból ez a link csak egyszer használható fel.</p>
        
        <div class="footer">
            <p>Ez egy automatikusan generált e-mail, kérjük ne válaszoljon rá.</p>
            <p>Ha problémája van a gomb használatával, másolja be az alábbi linket a böngészőjébe:</p>
            <p style="word-break: break-all;">{{ reset_link }}</p>
            <p>&copy; 2025 POM APP. Minden jog fenntartva.</p>
        </div>
    </div>
</body>
</html>