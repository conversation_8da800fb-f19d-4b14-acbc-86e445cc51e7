import logging

# Configure the root logger
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create a logger instance
logger = logging.getLogger("app")
logger.setLevel(logging.DEBUG)

# Ensure all child loggers inherit the DEBUG level
for name in logging.root.manager.loggerDict:
    logging.getLogger(name).setLevel(logging.DEBUG)

# Configure the uvicorn.access logger to reduce noise from AI API requests
uvicorn_access_logger = logging.getLogger("uvicorn.access")
uvicorn_access_logger.setLevel(logging.WARNING)

# Helper function to check if a log record is from an AI API request
def is_ai_api_request(record):
    if hasattr(record, 'args') and len(record.args) >= 3:
        path = record.args[2] if isinstance(record.args, tuple) else record.args.get('uri', '')
        if isinstance(path, str) and path.startswith('/v1/models'):
            return True
    return False

# Custom filter to reduce noisy AI API logs
class AIAPIFilter(logging.Filter):
    def filter(self, record):
        # If it's an AI API request, only log at DEBUG level
        if is_ai_api_request(record):
            return record.levelno <= logging.DEBUG
        # Otherwise, log normally
        return True

# Apply the filter to uvicorn access logs
uvicorn_access_logger.addFilter(AIAPIFilter())
