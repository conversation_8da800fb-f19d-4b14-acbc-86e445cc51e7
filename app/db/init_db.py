import logging
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import get_password_hash
from app.schemas.user import UserCreate
from app.services.user_service import create_user
from app.db.session import SessionLocal
from app.models.user import User, UserDefaultSettings, PasswordResetToken
from app.models.product import ProductCategory, ProductType, QualityGrade
from app.models.offer import Offer, OfferLog
from app.utils.logging import logger

# Logger beállítása
logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger(__name__)


def init_db() -> None:
    """
    Adatbázis inicializálása: az alapvető rendszeradatok betöltése
    - Alap admin felhasználó létrehozása, ha még nem létezik
    - Alap termékkategóriák és típusok, ha még nem léteznek
    """
    db = SessionLocal()
    init_system_data(db)
    db.close()


def init_system_data(db: Session) -> None:
    """
    Rendszeradatok inicializálása
    
    Args:
        db: SQLAlchemy session objektum
    """
    # Admin felhasználó létrehozása
    create_admin_user(db)

    # Alap termelő és ügyintéző felhasználó létrehozása
    create_default_users(db)
    
    # Alap termékkategóriák, típusok és minőségbesorolások létrehozása
    create_product_data(db)


def create_admin_user(db: Session) -> None:
    """
    Admin felhasználó létrehozása, ha még nem létezik
    
    Args:
        db: SQLAlchemy session objektum
    """
    from app.models.user import User
    
    # Ellenőrizzük, hogy van-e már admin felhasználó
    admin = db.query(User).filter(User.role == "admin").first()
    
    if not admin:
        logger.info("Admin felhasználó létrehozása")
        admin_data = UserCreate(
            email="<EMAIL>",
            password="admin12345",  # Titkosítva lesz elmentve
            company_name="Mezőgazdasági Kft.",
            tax_id="12345678-1-42",
            contact_name="Admin Felhasználó",
            phone_number="+36201234567",
        )
        
        # Admin létrehozása és szerepkör beállítása
        admin_user = create_user(db, admin_data)
        admin_user.role = "admin"
        admin_user.is_active = True  # Aktiválás nélkül is aktív
        db.add(admin_user)
        db.commit()
        logger.info(f"Admin felhasználó létrehozva: {admin_user.email}")

def create_default_users(db: Session) -> None:
    """
    Alapértelmezett termelő és ügyintéző felhasználó létrehozása, ha még nem léteznek

    Args:
        db: SQLAlchemy session objektum
    """
    from app.models.user import User

    default_users = [
        {
            "email": "<EMAIL>",
            "password": "teszt12345",
            "company_name": "Termelői Gazdaság Kft.",
            "tax_id": "98765432-1-42",
            "contact_name": "Kiss Péter",
            "phone_number": "+36209876543",
            "role": "termelő",
        },
        {
            "email": "<EMAIL>",
            "password": "teszt12345",
            "company_name": "Mezőgazdasági Kft.",
            "tax_id": "87654321-1-42",
            "contact_name": "Nagy Anna",
            "phone_number": "+36208765432",
            "role": "ügyintéző",
        }
    ]

    for user_data in default_users:
        existing_user = db.query(User).filter(User.email == user_data["email"]).first()

        if not existing_user:
            logger.info(f"{user_data['role'].capitalize()} felhasználó létrehozása")

            user_create_data = UserCreate(
                email=user_data["email"],
                password=user_data["password"],
                company_name=user_data["company_name"],
                tax_id=user_data["tax_id"],
                contact_name=user_data["contact_name"],
                phone_number=user_data["phone_number"],
            )

            new_user = create_user(db, user_create_data)
            new_user.role = user_data["role"]
            new_user.is_active = True
            db.add(new_user)
            db.commit()
            logger.info(f"{user_data['role'].capitalize()} létrehozva: {new_user.email}")



def create_product_data(db: Session) -> None:
    """
    Alap termékkategóriák, típusok és minőségbesorolások létrehozása, 
    ha még nem léteznek
    
    Args:
        db: SQLAlchemy session objektum
    """
    # Ellenőrizzük, hogy vannak-e már termékkategóriák
    categories = db.query(ProductCategory).count()
    
    if categories == 0:
        logger.info("Termékkategóriák, típusok és minőségbesorolások létrehozása")
        
        # Kategóriák létrehozása
        paprika = ProductCategory(name="Paprika", description="Különböző paprikafélék")
        paradicsom = ProductCategory(name="Paradicsom", description="Különböző paradicsomfajták")
        kigyo_uborka = ProductCategory(name="Kígyóuborka", description="Kígyóuborka típusok")
        egyeb = ProductCategory(name="Egyéb", description="Egyéb zöldségfélék")
        
        db.add_all([paprika, paradicsom, kigyo_uborka, egyeb])
        db.commit()
        
        # Paprika típusok
        tv_paprika = ProductType(
            category_id=paprika.id,
            name="TV paprika",
            has_quality_grades=True,
            description="Tölteni való paprika"
        )
        
        kapia_paprika = ProductType(
            category_id=paprika.id,
            name="Kápia paprika",
            has_quality_grades=True,
            description="Kápia paprika"
        )
        
        palermo_paprika = ProductType(
            category_id=paprika.id,
            name="Palermo paprika",
            has_quality_grades=True,
            description="Palermo paprika"
        )
        
        snack_paprika = ProductType(
            category_id=paprika.id,
            name="Snack paprika",
            has_quality_grades=False,
            description="Snack paprika"
        )
        
        kaliforniai_paprika = ProductType(
            category_id=paprika.id,
            name="Kaliforniai paprika",
            has_quality_grades=True,
            description="Kaliforniai paprika"
        )
        
        hegyes_eros_paprika = ProductType(
            category_id=paprika.id,
            name="Hegyes erős paprika",
            has_quality_grades=True,
            description="Hegyes erős paprika"
        )
        
        pritamin_paprika = ProductType(
            category_id=paprika.id,
            name="Pritamin paprika",
            has_quality_grades=True,
            description="Pritamin paprika"
        )
        
        db.add_all([
            tv_paprika, kapia_paprika, palermo_paprika, snack_paprika,
            kaliforniai_paprika, hegyes_eros_paprika, pritamin_paprika
        ])
        db.commit()
        
        # Paradicsom típusok
        furtos_paradicsom = ProductType(
            category_id=paradicsom.id,
            name="Fürtös paradicsom",
            has_quality_grades=True,
            description="Fürtös paradicsom"
        )
        
        koktel_paradicsom = ProductType(
            category_id=paradicsom.id,
            name="Koktélparadicsom",
            has_quality_grades=True,
            description="Koktélparadicsom"
        )
        
        sherry_paradicsom = ProductType(
            category_id=paradicsom.id,
            name="Sherry paradicsom",
            has_quality_grades=True,
            description="Sherry paradicsom"
        )
        
        bogyos_paradicsom = ProductType(
            category_id=paradicsom.id,
            name="Bogyós paradicsom",
            has_quality_grades=True,
            description="Bogyós paradicsom"
        )
        
        db.add_all([
            furtos_paradicsom, koktel_paradicsom, sherry_paradicsom, bogyos_paradicsom
        ])
        db.commit()
        
        # Minőségi besorolások (csak a TV paprikára példaként)
        tv_extra = QualityGrade(
            product_type_id=tv_paprika.id,
            name="Extra",
            min_shoulder_diameter=60.0,
            min_length=100.0,
            description="Extra minőségű TV paprika"
        )
        
        tv_elso = QualityGrade(
            product_type_id=tv_paprika.id,
            name="I. Osztály",
            min_shoulder_diameter=50.0,
            max_shoulder_diameter=59.0,
            min_length=80.0,
            max_length=99.0,
            description="I. osztályú TV paprika"
        )
        
        tv_masodik = QualityGrade(
            product_type_id=tv_paprika.id,
            name="II. Osztály",
            min_shoulder_diameter=40.0,
            max_shoulder_diameter=49.0,
            min_length=70.0,
            max_length=79.0,
            description="II. osztályú TV paprika"
        )
        
        tv_lecso = QualityGrade(
            product_type_id=tv_paprika.id,
            name="Lecsó",
            max_shoulder_diameter=39.0,
            max_length=69.0,
            description="Lecsó minőségű TV paprika"
        )
        
        db.add_all([tv_extra, tv_elso, tv_masodik, tv_lecso])
        db.commit()
        
        # Kápia paprika minőségi besorolások
        kapia_extra = QualityGrade(
            product_type_id=kapia_paprika.id,
            name="Extra",
            min_shoulder_diameter=60.0,
            min_length=100.0,
            description="Extra minőségű Kápia paprika"
        )
        
        kapia_elso = QualityGrade(
            product_type_id=kapia_paprika.id,
            name="I. Osztály",
            min_shoulder_diameter=50.0,
            max_shoulder_diameter=59.0,
            min_length=80.0,
            max_length=99.0,
            description="I. osztályú Kápia paprika"
        )
        
        kapia_masodik = QualityGrade(
            product_type_id=kapia_paprika.id,
            name="II. Osztály",
            min_shoulder_diameter=40.0,
            max_shoulder_diameter=49.0,
            min_length=70.0,
            max_length=79.0,
            description="II. osztályú Kápia paprika"
        )
        
        kapia_lecso = QualityGrade(
            product_type_id=kapia_paprika.id,
            name="Lecsó",
            max_shoulder_diameter=39.0,
            max_length=69.0,
            description="Lecsó minőségű Kápia paprika"
        )
        
        db.add_all([kapia_extra, kapia_elso, kapia_masodik, kapia_lecso])
        db.commit()

        # Palermo paprika minőségi besorolások
        palermo_elso = QualityGrade(
            product_type_id=palermo_paprika.id,
            name="I. Osztály",
            min_length=150.0,
            description="I. osztályú Palermo paprika"
        )
        palermo_masodik = QualityGrade(
            product_type_id=palermo_paprika.id,
            name="II. Osztály",
            max_length=149.0,
            description="II. osztályú Palermo paprika"
        )
        db.add_all([palermo_elso, palermo_masodik])
        db.commit()
        
        # Hegyes erős paprika minőségi besorolások
        hegyes_extra = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="Extra",
            min_length=210.0,
            description="Extra minőségű Hegyes erős paprika"
        )
        hegyes_elso = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="I. Osztály",
            min_length=190.0,
            max_length=200.0,
            description="I. osztályú Hegyes erős paprika"
        )
        hegyes_masodik = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="II. Osztály",
            min_length=170.0,
            max_length=189.0,
            description="II. osztályú Hegyes erős paprika"
        )
        hegyes_harmadik = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="III. Osztály",
            min_length=150.0,
            max_length=169.0,
            description="III. osztályú Hegyes erős paprika"
        )
        hegyes_negyedik = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="IV. Osztály",
            min_length=120.0,
            max_length=149.0,
            description="IV. osztályú Hegyes erős paprika"
        )
        hegyes_lecso = QualityGrade(
            product_type_id=hegyes_eros_paprika.id,
            name="Lecsó",
            max_length=119.0,
            description="Lecsó minőségű Hegyes erős paprika"
        )
        db.add_all([hegyes_extra, hegyes_elso, hegyes_masodik, hegyes_harmadik, hegyes_negyedik, hegyes_lecso])
        db.commit()
        
        # Kaliforniai paprika minőségi besorolások
        kaliforniai_elso = QualityGrade(
            product_type_id=kaliforniai_paprika.id,
            name="I. Osztály",
            min_length=80.0,
            description="I. osztályú Kaliforniai paprika"
        )
        kaliforniai_masodik = QualityGrade(
            product_type_id=kaliforniai_paprika.id,
            name="II. Osztály",
            max_length=79.0,
            description="II. osztályú Kaliforniai paprika"
        )
        db.add_all([kaliforniai_elso, kaliforniai_masodik])
        db.commit()
        
        # Pritamin paprika minőségi besorolások
        pritamin_elso = QualityGrade(
            product_type_id=pritamin_paprika.id,
            name="I. Osztály",
            min_length=80.0,
            description="I. osztályú Pritamin paprika"
        )
        pritamin_masodik = QualityGrade(
            product_type_id=pritamin_paprika.id,
            name="II. Osztály",
            max_length=79.0,
            description="II. osztályú Pritamin paprika"
        )
        db.add_all([pritamin_elso, pritamin_masodik])
        db.commit()
        
        logger.info("Alapadatok sikeresen inicializálva")
