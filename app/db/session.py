from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# Adatbázis motor létrehozása a konfigurált URL-ből
engine = create_engine(
    str(settings.DATABASE_URL),
    pool_pre_ping=True,  # <PERSON><PERSON><PERSON><PERSON> a "connection has gone away" hibákat
)

# Session factory létrehozása
# autocommit=False: <PERSON><PERSON><PERSON><PERSON><PERSON> kell commit-álni a tranzakciókat
# autoflush=False: Nem flush-ol automatikusan minden lekérdezés előtt
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """
    Adatbázis session dependency függvény a FastAPI útvonalakhoz
    Gondoskodik a session bezárásáról a kérés befejezése után
    
    Yields:
        SQLAlchemy session objektum
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()