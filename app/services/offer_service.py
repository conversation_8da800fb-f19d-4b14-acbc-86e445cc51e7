"""
Ajánlatokkal kapcsolatos szolgáltatási funkciók
"""
import logging
from datetime import date, timedelta, datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any, Union

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, distinct, and_, or_, cast, Date, case

from app.models.offer import Offer, OfferLog
from app.models.user import User
from app.models.product import ProductType, QualityGrade
from app.schemas.offer import (
    OfferCreate, OfferCreateForUser, OfferUpdate, OfferConfirm, OfferFilter
)
from app.services.offer_log_service import create_offer_log
from app.utils.logging import logger
from app.crud.notification import create_notification
from app.schemas.notification import NotificationCreate

# Logger beállítása
# logger = logging.getLogger(__name__)


# Ajánlat létrehozása
def create_offer(db: Session, user_id: int, offer_data: OfferCreate) -> Offer:
    """
    Ajánlat létrehozása
    
    Args:
        db: Adatbázis session
        user_id: Felhasz<PERSON><PERSON>ó azon<PERSON>ító
        offer_data: Aj<PERSON>lat adatok
        
    Returns:
        Offer: Létrehozott ajánlat
        
    Raises:
        ValueError: Ha nem létezik a megadott terméktípus vagy minőségi besorolás
    """
    # Ellenőrizzük a terméktípus létezését
    product_type = db.query(ProductType).filter(
        ProductType.id == offer_data.product_type_id
    ).first()
    
    if not product_type:
        raise ValueError(f"Nem létezik a megadott terméktípus: {offer_data.product_type_id}")
    
    # Ellenőrizzük a minőségi besorolás létezését (ha van megadva)
    if offer_data.quality_grade_id:
        # Ellenőrizzük, hogy a terméktípus támogatja-e a minőségi besorolásokat
        if not product_type.has_quality_grades:
            raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
        
        quality_grade = db.query(QualityGrade).filter(
            QualityGrade.id == offer_data.quality_grade_id,
            QualityGrade.product_type_id == offer_data.product_type_id
        ).first()
        
        if not quality_grade:
            raise ValueError(f"Nem létezik a megadott minőségi besorolás: {offer_data.quality_grade_id}")
    
    # Ajánlat létrehozása
    db_offer = Offer(
        user_id=user_id,
        created_by_user_id=user_id,  # A létrehozó ugyanaz a felhasználó
        status="CREATED",
        **offer_data.dict()
    )
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=None,
        new_status="CREATED",
        changed_by=user_id,
        note="Ajánlat létrehozva"
    )
    
    logger.info(f"Ajánlat létrehozva: {db_offer.id} (felhasználó: {user_id})")

    # Automatikus értesítés létrehozása az ajánlat létrejöttéről
    notification = NotificationCreate(
        user_id=user_id,
        type="info",
        message="Új ajánlatot hozott létre",
        detail=f"Ajánlat azonosító: {db_offer.id}",
        target_roles=None,
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat létrehozása más felhasználó nevében (ügyintéző funkció)
def create_offer_for_user(
    db: Session, 
    operator_id: int, 
    offer_data: OfferCreateForUser
) -> Offer:
    """
    Ajánlat létrehozása más felhasználó nevében (ügyintéző funkció)
    
    Args:
        db: Adatbázis session
        operator_id: Ügyintéző azonosító
        offer_data: Ajánlat adatok a célfelhasználó azonosítójával
        
    Returns:
        Offer: Létrehozott ajánlat
        
    Raises:
        ValueError: Ha nem létezik a megadott felhasználó, terméktípus vagy minőségi besorolás
    """
    # Ellenőrizzük a felhasználó létezését
    user = db.query(User).filter(User.id == offer_data.user_id).first()
    
    if not user:
        raise ValueError(f"Nem létezik a megadott felhasználó: {offer_data.user_id}")
    
    # Ellenőrizzük a terméktípus létezését
    product_type = db.query(ProductType).filter(
        ProductType.id == offer_data.product_type_id
    ).first()
    
    if not product_type:
        raise ValueError(f"Nem létezik a megadott terméktípus: {offer_data.product_type_id}")
    
    # Ellenőrizzük a minőségi besorolás létezését (ha van megadva)
    if offer_data.quality_grade_id:
        # Ellenőrizzük, hogy a terméktípus támogatja-e a minőségi besorolásokat
        if not product_type.has_quality_grades:
            raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
        
        quality_grade = db.query(QualityGrade).filter(
            QualityGrade.id == offer_data.quality_grade_id,
            QualityGrade.product_type_id == offer_data.product_type_id
        ).first()
        
        if not quality_grade:
            raise ValueError(f"Nem létezik a megadott minőségi besorolás: {offer_data.quality_grade_id}")
    
    # Ajánlat létrehozása
    db_offer = Offer(
        user_id=offer_data.user_id,
        created_by_user_id=operator_id,  # A létrehozó az ügyintéző
        status="CREATED",
        product_type_id=offer_data.product_type_id,
        quality_grade_id=offer_data.quality_grade_id,
        quantity_value=offer_data.quantity_value,
        quantity_unit=offer_data.quantity_unit,
        delivery_date=offer_data.delivery_date,
        note=offer_data.note
    )
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=None,
        new_status="CREATED",
        changed_by=operator_id,
        note=f"Ajánlat létrehozva ügyintéző által a felhasználó nevében: {user.email}"
    )
    
    logger.info(f"Ajánlat létrehozva felhasználó nevében: {db_offer.id} (felhasználó: {offer_data.user_id}, ügyintéző: {operator_id})")

    # Automatikus értesítés létrehozása a felhasználónak
    notification = NotificationCreate(
        user_id=offer_data.user_id,
        type="info",
        message="Új ajánlatot hozott létre Ön helyett egy ügyintéző",
        detail=f"Ajánlat azonosító: {db_offer.id}",
        target_roles=None,
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat lekérdezése azonosító alapján
def get_offer(db: Session, offer_id: int) -> Optional[Offer]:
    """
    Ajánlat lekérdezése azonosító alapján
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        
    Returns:
        Optional[Offer]: Ajánlat vagy None, ha nem található
    """
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    ).filter(Offer.id == offer_id).first()
    
    if query:
        logger.debug(f"Offer found: {query.id}")
        logger.debug(f"Offer user: {query.user}")
        logger.debug(f"Offer created_by_user: {query.created_by_user}")
        logger.debug(f"Offer product_type: {query.product_type}")
        logger.debug(f"Offer quality_grade: {query.quality_grade}")
    else:
        logger.debug(f"Offer not found: {offer_id}")
    
    return query


# Ajánlatok szűrése
def get_offers(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    filters: Optional[OfferFilter] = None,
    user_id: Optional[int] = None
) -> List[Offer]:
    """
    Ajánlatok szűrése és listázása
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        filters: Szűrési feltételek
        user_id: Felhasználó azonosító (csak a saját ajánlatok lekérdezéséhez)
        
    Returns:
        List[Offer]: Ajánlatok listája
    """
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    )
    
    # Alapértelmezett szűrési feltételek
    if filters is None:
        filters = OfferFilter()
    
    # Felhasználó szerinti szűrés
    if user_id:
        query = query.filter(Offer.user_id == user_id)
    elif filters.user_id:
        query = query.filter(Offer.user_id == filters.user_id)
    
    # Terméktípus szerinti szűrés
    if filters.product_type_id:
        query = query.filter(Offer.product_type_id == filters.product_type_id)
    
    # Státusz szerinti szűrés
    if filters.status:
        query = query.filter(Offer.status == filters.status)
    
    # Dátum szerinti szűrés
    if filters.date_from:
        # Konvertáljuk az ISO formátumú dátumot datetime objektummá
        if isinstance(filters.date_from, str):
            filters.date_from = datetime.fromisoformat(filters.date_from.replace('Z', '+00:00'))
        query = query.filter(Offer.delivery_date >= filters.date_from)
    
    if filters.date_to:
        # Konvertáljuk az ISO formátumú dátumot datetime objektummá
        if isinstance(filters.date_to, str):
            filters.date_to = datetime.fromisoformat(filters.date_to.replace('Z', '+00:00'))
        query = query.filter(Offer.delivery_date <= filters.date_to)
    
    # Rendezés dátum és azonosító szerint
    query = query.order_by(Offer.delivery_date, Offer.id)
    
    # Lapozás és eredmény visszaadása
    return query.offset(skip).limit(limit).all()


# Ajánlat frissítése
def update_offer(
    db: Session, 
    offer_id: int, 
    offer_data: OfferUpdate, 
    user_id: int
) -> Optional[Offer]:
    """
    Ajánlat frissítése
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        offer_data: Frissítési adatok
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[Offer]: Frissített ajánlat vagy None, ha nem található
        
    Raises:
        ValueError: Ha nem frissíthető az ajánlat (már nem CREATED státuszban van),
                   vagy ha nem létezik a megadott terméktípus vagy minőségi besorolás
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Ellenőrizzük, hogy frissíthető-e az ajánlat
    if db_offer.status != "CREATED":
        raise ValueError(f"Az ajánlat nem frissíthető, mert már nem 'CREATED' státuszban van: {db_offer.status}")
    
    # Adatok frissítése
    update_data = offer_data.dict(exclude_unset=True)
    
    # Ha terméktípust váltunk, ellenőrizzük, hogy létezik-e
    if "product_type_id" in update_data:
        product_type = db.query(ProductType).filter(
            ProductType.id == update_data["product_type_id"]
        ).first()
        
        if not product_type:
            raise ValueError(f"Nem létezik a megadott terméktípus: {update_data['product_type_id']}")
        
        # Ha minőségi besorolást is váltunk, vagy már van beállítva, ellenőrizzük, hogy kompatibilis-e
        if "quality_grade_id" in update_data:
            if update_data["quality_grade_id"]:
                # Ellenőrizzük, hogy a terméktípus támogatja-e a minőségi besorolásokat
                if not product_type.has_quality_grades:
                    raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
                
                quality_grade = db.query(QualityGrade).filter(
                    QualityGrade.id == update_data["quality_grade_id"],
                    QualityGrade.product_type_id == update_data["product_type_id"]
                ).first()
                
                if not quality_grade:
                    raise ValueError(f"Nem létezik a megadott minőségi besorolás: {update_data['quality_grade_id']}")
        elif db_offer.quality_grade_id:
            # Ha már van beállítva minőségi besorolás, ellenőrizzük, hogy kompatibilis-e az új terméktípussal
            if not product_type.has_quality_grades:
                # Ha az új terméktípus nem támogatja a minőségi besorolásokat, töröljük a besorolást
                update_data["quality_grade_id"] = None
            else:
                # Ellenőrizzük, hogy létezik-e a besorolás az új terméktípushoz
                quality_grade = db.query(QualityGrade).filter(
                    QualityGrade.id == db_offer.quality_grade_id,
                    QualityGrade.product_type_id == update_data["product_type_id"]
                ).first()
                
                if not quality_grade:
                    # Ha nem létezik, töröljük a besorolást
                    update_data["quality_grade_id"] = None
    
    # Ha csak minőségi besorolást váltunk, ellenőrizzük, hogy kompatibilis-e a terméktípussal
    elif "quality_grade_id" in update_data:
        if update_data["quality_grade_id"]:
            # Lekérjük a terméktípust
            product_type = db.query(ProductType).filter(
                ProductType.id == db_offer.product_type_id
            ).first()
            
            # Ellenőrizzük, hogy a terméktípus támogatja-e a minőségi besorolásokat
            if not product_type.has_quality_grades:
                raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
            
            quality_grade = db.query(QualityGrade).filter(
                QualityGrade.id == update_data["quality_grade_id"],
                QualityGrade.product_type_id == db_offer.product_type_id
            ).first()
            
            if not quality_grade:
                raise ValueError(f"Nem létezik a megadott minőségi besorolás: {update_data['quality_grade_id']}")
    
    # Adatok frissítése
    for key, value in update_data.items():
        setattr(db_offer, key, value)
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=db_offer.status,
        new_status=db_offer.status,
        changed_by=user_id,
        note="Ajánlat frissítve"
    )
    
    logger.info(f"Ajánlat frissítve: {db_offer.id} (felhasználó: {user_id})")
    
    return db_offer


# Ajánlat törlése
def delete_offer(db: Session, offer_id: int, user_id: int) -> bool:
    """
    Ajánlat törlése
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        user_id: Felhasználó azonosító
        
    Returns:
        bool: Sikeres volt-e a törlés
        
    Raises:
        ValueError: Ha nem törölhető az ajánlat (már nem CREATED státuszban van)
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return False
    
    # Ellenőrizzük, hogy törölhető-e az ajánlat
    if db_offer.status != "CREATED":
        raise ValueError(f"Az ajánlat nem törölhető, mert már nem 'CREATED' státuszban van: {db_offer.status}")
    
    # Törlés előtt létrehozzuk a naplóbejegyzést
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=db_offer.status,
        new_status="DELETED",
        changed_by=user_id,
        note="Ajánlat törölve"
    )
    
    # Törlés az adatbázisból
    db.delete(db_offer)
    db.commit()
    
    logger.info(f"Ajánlat törölve: {db_offer.id} (felhasználó: {user_id})")
    
    return True


# Ajánlat visszaigazolása (ügyintéző funkció)
def confirm_offer(
    db: Session, 
    offer_id: int, 
    confirm_data: OfferConfirm, 
    operator_id: int
) -> Optional[Offer]:
    """
    Ajánlat visszaigazolása (ügyintéző funkció)
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        confirm_data: Visszaigazolási adatok
        operator_id: Ügyintéző azonosító
        
    Returns:
        Optional[Offer]: Visszaigazolt ajánlat vagy None, ha nem található
        
    Raises:
        ValueError: Ha nem igazolható vissza az ajánlat (nem CREATED státuszban van)
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Ellenőrizzük, hogy visszaigazolható-e az ajánlat
    if db_offer.status != "CREATED":
        raise ValueError(f"Az ajánlat nem igazolható vissza, mert nem 'CREATED' státuszban van: {db_offer.status}")
    
    # Adatok frissítése
    old_status = db_offer.status
    db_offer.status = "CONFIRMED_BY_COMPANY"
    db_offer.confirmed_quantity = confirm_data.confirmed_quantity
    db_offer.confirmed_price = confirm_data.confirmed_price
    
    if confirm_data.note:
        db_offer.note = confirm_data.note
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=old_status,
        new_status=db_offer.status,
        changed_by=operator_id,
        note=f"Ajánlat visszaigazolva: {confirm_data.confirmed_quantity} kg, {confirm_data.confirmed_price} Ft/kg"
    )
    
    logger.info(f"Ajánlat visszaigazolva: {db_offer.id} (ügyintéző: {operator_id})")

    # Értesítés a felhasználónak az ajánlat visszaigazolásáról
    notification = NotificationCreate(
        user_id=db_offer.user_id,
        type="success",
        message="Ajánlatát visszaigazolta az ügyintéző",
        detail=f"Ajánlat azonosító: {db_offer.id}, visszaigazolt mennyiség: {db_offer.confirmed_quantity} kg, ár: {db_offer.confirmed_price} Ft/kg",
        target_roles=None,
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat elfogadása (termelő funkció)
def accept_offer(db: Session, offer_id: int, user_id: int) -> Optional[Offer]:
    """
    Ajánlat elfogadása (termelő funkció)
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[Offer]: Elfogadott ajánlat vagy None, ha nem található
        
    Raises:
        ValueError: Ha nem fogadható el az ajánlat (nem CONFIRMED_BY_COMPANY státuszban van)
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Ellenőrizzük, hogy elfogadható-e az ajánlat
    if db_offer.status != "CONFIRMED_BY_COMPANY":
        raise ValueError(f"Az ajánlat nem fogadható el, mert nem 'CONFIRMED_BY_COMPANY' státuszban van: {db_offer.status}")
    
    # Ellenőrizzük, hogy a felhasználó a tulajdonosa-e az ajánlatnak
    if db_offer.user_id != user_id:
        raise ValueError(f"Az ajánlat nem fogadható el, mert nem a felhasználóhoz tartozik: {user_id}")
    
    # Adatok frissítése
    old_status = db_offer.status
    db_offer.status = "ACCEPTED_BY_USER"
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=old_status,
        new_status=db_offer.status,
        changed_by=user_id,
        note="Ajánlat elfogadva"
    )
    
    logger.info(f"Ajánlat elfogadva: {db_offer.id} (felhasználó: {user_id})")

    # Értesítés az ügyintézőknek az ajánlat elfogadásáról (target_roles = 'operator')
    notification = NotificationCreate(
        user_id=None,
        type="success",
        message="A termelő elfogadta az ajánlatot",
        detail=f"Ajánlat azonosító: {db_offer.id}",
        target_roles="ügyintéző",
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat elutasítása (termelő funkció)
def reject_offer(db: Session, offer_id: int, user_id: int, note: Optional[str] = None) -> Optional[Offer]:
    """
    Ajánlat elutasítása (termelő funkció)
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        user_id: Felhasználó azonosító
        note: Opcionális megjegyzés
        
    Returns:
        Optional[Offer]: Elutasított ajánlat vagy None, ha nem található
        
    Raises:
        ValueError: Ha nem utasítható el az ajánlat (nem CONFIRMED_BY_COMPANY státuszban van)
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Ellenőrizzük, hogy elutasítható-e az ajánlat
    if db_offer.status != "CONFIRMED_BY_COMPANY":
        raise ValueError(f"Az ajánlat nem utasítható el, mert nem 'CONFIRMED_BY_COMPANY' státuszban van: {db_offer.status}")
    
    # Ellenőrizzük, hogy a felhasználó a tulajdonosa-e az ajánlatnak
    if db_offer.user_id != user_id:
        raise ValueError(f"Az ajánlat nem utasítható el, mert nem a felhasználóhoz tartozik: {user_id}")
    
    # Adatok frissítése
    old_status = db_offer.status
    db_offer.status = "REJECTED_BY_USER"
    
    if note:
        db_offer.note = note
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=old_status,
        new_status=db_offer.status,
        changed_by=user_id,
        note=f"Ajánlat elutasítva{f': {note}' if note else ''}"
    )
    
    logger.info(f"Ajánlat elutasítva: {db_offer.id} (felhasználó: {user_id})")

    # Értesítés az ügyintézőknek az ajánlat elutasításáról (target_roles = 'operator')
    notification = NotificationCreate(
        user_id=None,
        type="info",
        message="A termelő elutasította az ajánlatot",
        detail=f"Ajánlat azonosító: {db_offer.id}{f', megjegyzés: {note}' if note else ''}",
        target_roles="ügyintéző",
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat véglegesítése (ügyintéző funkció)
def finalize_offer(db: Session, offer_id: int, operator_id: int) -> Optional[Offer]:
    """
    Ajánlat véglegesítése (ügyintéző funkció)
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        operator_id: Ügyintéző azonosító
        
    Returns:
        Optional[Offer]: Véglegesített ajánlat vagy None, ha nem található
        
    Raises:
        ValueError: Ha nem véglegesíthető az ajánlat (nem ACCEPTED_BY_USER státuszban van)
    """
    # Ajánlat lekérdezése
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Ellenőrizzük, hogy véglegesíthető-e az ajánlat
    if db_offer.status != "ACCEPTED_BY_USER":
        raise ValueError(f"Az ajánlat nem véglegesíthető, mert nem 'ACCEPTED_BY_USER' státuszban van: {db_offer.status}")
    
    # Adatok frissítése
    old_status = db_offer.status
    db_offer.status = "FINALIZED"
    
    # Mentés az adatbázisba
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Napló létrehozása
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=old_status,
        new_status=db_offer.status,
        changed_by=operator_id,
        note="Ajánlat véglegesítve"
    )
    
    logger.info(f"Ajánlat véglegesítve: {db_offer.id} (ügyintéző: {operator_id})")

    # Értesítés a felhasználónak az ajánlat véglegesítéséről
    notification = NotificationCreate(
        user_id=db_offer.user_id,
        type="success",
        message="Ajánlatát véglegesítettük",
        detail=f"Ajánlat azonosító: {db_offer.id}",
        target_roles=None,
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)

    return db_offer


# Ajánlat naplók lekérdezése
def get_offer_logs(db: Session, offer_id: int) -> List[OfferLog]:
    """
    Ajánlat naplók lekérdezése
    
    Args:
        db: Adatbázis session
        offer_id: Ajánlat azonosító
        
    Returns:
        List[OfferLog]: Ajánlat naplók listája
    """
    return db.query(OfferLog).filter(OfferLog.offer_id == offer_id).order_by(OfferLog.created_at.desc()).all()


# Összetett funkciók

# Naptári nézet adatok lekérdezése
def get_calendar_offers(
    db: Session,
    date_from: date,
    date_to: date,
    user_id: Optional[int] = None
) -> Dict[date, List[Offer]]:
    """
    Naptári nézet adatok lekérdezése
    
    Args:
        db: Adatbázis session
        date_from: Kezdő dátum
        date_to: Záró dátum
        user_id: Felhasználó azonosító (csak a saját ajánlatok lekérdezéséhez)
        
    Returns:
        Dict[date, List[Offer]]: Ajánlatok dátum szerint csoportosítva
    """
    # Lekérdezés előkészítése
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    ).filter(
        Offer.delivery_date >= date_from,
        Offer.delivery_date <= date_to,
        Offer.status.in_(["ACCEPTED_BY_USER", "FINALIZED"])
    )
    
    # Felhasználó szerinti szűrés
    if user_id:
        query = query.filter(Offer.user_id == user_id)
    
    # Ajánlatok lekérdezése
    offers = query.order_by(Offer.delivery_date, Offer.id).all()
    
    # Ajánlatok csoportosítása dátum szerint
    result = {}
    for offer in offers:
        if offer.delivery_date not in result:
            result[offer.delivery_date] = []
        
        result[offer.delivery_date].append(offer)
    
    return result


# Statisztikák lekérdezése
def get_offer_statistics(
    db: Session,
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    user_id: Optional[int] = None,
    product_type_id: Optional[int] = None,
    category_id: Optional[int] = None,
    quality_grade_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Statisztikák lekérdezése
    
    Args:
        db: Adatbázis session
        date_from: Kezdő dátum
        date_to: Záró dátum
        user_id: Felhasználó azonosító
        product_type_id: Terméktípus azonosító
        category_id: Termékkategória azonosító
        quality_grade_id: Minőségi besorolás azonosító
        
    Returns:
        Dict[str, Any]: Statisztikai adatok
    """
    # Lekérdezés előkészítése
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    )
    
    # Szűrési feltételek
    if date_from:
        # Konvertáljuk az ISO formátumú dátumot datetime objektummá
        if isinstance(date_from, str):
            date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        query = query.filter(Offer.delivery_date >= date_from)
    if date_to:
        # Konvertáljuk az ISO formátumú dátumot datetime objektummá
        if isinstance(date_to, str):
            date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
        query = query.filter(Offer.delivery_date <= date_to)
    if user_id:
        query = query.filter(Offer.user_id == user_id)
    if product_type_id:
        query = query.filter(Offer.product_type_id == product_type_id)
    if category_id:
        # Termékkategória szerinti szűrés
        query = query.join(Offer.product_type).filter(ProductType.category_id == category_id)
    if quality_grade_id:
        query = query.filter(Offer.quality_grade_id == quality_grade_id)
    
    # Alap statisztikák
    total_offers = query.count()
    
    # Státusz szerinti darabszám
    status_counts = {}
    for status in ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]:
        status_counts[status] = query.filter(Offer.status == status).count()
    
    # Összes mennyiség
    total_quantity = query.with_entities(func.sum(Offer.quantity_value)).scalar() or 0
    
    # Visszaigazolt ajánlatok értéke - minden ajánlat ami rendelkezik confirmed_price-szal
    # és nem lett elutasítva a felhasználó által
    confirmed_query = query.filter(
        Offer.status.in_(["CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "FINALIZED"]),
        Offer.confirmed_price.isnot(None),
        Offer.confirmed_price > 0
    )
    total_value = confirmed_query.with_entities(
        func.sum(Offer.confirmed_quantity * Offer.confirmed_price)
    ).scalar() or 0
    
    # Átlagos ár
    average_price = confirmed_query.with_entities(
        func.avg(Offer.confirmed_price)
    ).scalar() or 0
    
    # Visszaigazolt ajánlatok száma (átlagár számításban használt)
    confirmed_offers_count = confirmed_query.count()
    
    # Státusz szerinti összesítés
    status_summary = []
    for status, count in status_counts.items():
        status_summary.append({
            "status": status,
            "count": count
        })
    
    # Napi összesítés
    daily_summary = []
    if date_from and date_to:
        daily_query = db.query(
            Offer.delivery_date,
            func.count(Offer.id).label('count'),
            func.sum(Offer.quantity_value).label('quantity'),
            func.sum(case(
                (Offer.status.in_(["ACCEPTED_BY_USER", "FINALIZED"]), 
                Offer.confirmed_quantity * Offer.confirmed_price),
                else_=0
            )).label('value')
        ).group_by(Offer.delivery_date)
        
        if user_id:
            daily_query = daily_query.filter(Offer.user_id == user_id)
        if product_type_id:
            daily_query = daily_query.filter(Offer.product_type_id == product_type_id)
        if category_id:
            daily_query = daily_query.join(Offer.product_type).filter(ProductType.category_id == category_id)
        if quality_grade_id:
            daily_query = daily_query.filter(Offer.quality_grade_id == quality_grade_id)
            
        daily_query = daily_query.filter(
            Offer.delivery_date >= date_from,
            Offer.delivery_date <= date_to
        ).order_by(Offer.delivery_date)
        
        for row in daily_query.all():
            daily_summary.append({
                "date": row.delivery_date.strftime("%Y-%m-%d"),
                "count": row.count,
                "quantity": float(row.quantity) if row.quantity else 0,
                "value": float(row.value) if row.value else 0
            })
    
    # Eredmény összeállítása
    return {
        "total_offers": total_offers,
        "confirmed_offers_count": confirmed_offers_count,
        "total_quantity": total_quantity,
        "total_value": total_value,
        "average_price": average_price,
        "status_counts": status_counts,
        "status_summary": status_summary,
        "daily_summary": daily_summary,
        # Debug információk
        "calculation_details": {
            "offers_used_for_average": confirmed_offers_count,
            "total_offers_found": total_offers,
            "filtering_criteria": ["CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "FINALIZED"],
            "excluded_statuses": ["CREATED", "REJECTED_BY_USER"]
        }
    }