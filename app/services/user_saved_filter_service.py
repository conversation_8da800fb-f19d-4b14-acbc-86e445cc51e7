"""
Service layer for user saved filters
"""
from typing import List, Optional, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
import json

from app.crud import crud_user_saved_filter
from app.models.user_saved_filter import UserSavedFilter
from app.schemas.saved_filter import Saved<PERSON>ilt<PERSON><PERSON><PERSON>, SavedFilterUpdate


def get_user_saved_filter(db: Session, filter_id: int, current_user_id: int, is_admin: bool = False) -> UserSavedFilter:
    """
    Get a saved filter by ID with permission check
    
    Args:
        db: Database session
        filter_id: ID of the filter to retrieve
        current_user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        UserSavedFilter: The saved filter
        
    Raises:
        HTTPException: If filter not found or user doesn't have permission
    """
    saved_filter = crud_user_saved_filter.get(db, filter_id)
    if not saved_filter:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Saved filter not found"
        )
    
    # Check if user has permission to access this filter
    if saved_filter.user_id != current_user_id and not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to access this filter"
        )
    
    return saved_filter


def get_user_saved_filters(
    db: Session,
    current_user_id: int,
    filter_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[UserSavedFilter]:
    """
    Get saved filters for a user
    
    Args:
        db: Database session
        current_user_id: ID of the current user
        filter_type: Optional filter type to filter by
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return (pagination)
        
    Returns:
        List[UserSavedFilter]: List of saved filters
    """
    return crud_user_saved_filter.get_multi(
        db=db,
        user_id=current_user_id,
        filter_type=filter_type,
        skip=skip,
        limit=limit
    )


def create_user_saved_filter(
    db: Session,
    filter_in: SavedFilterCreate,
    current_user_id: int
) -> UserSavedFilter:
    """
    Create a new saved filter
    
    Args:
        db: Database session
        filter_in: Filter data to create
        current_user_id: ID of the current user
        
    Returns:
        UserSavedFilter: The created saved filter
        
    Raises:
        HTTPException: If filter data validation fails
    """
    # Validate filter data
    validate_filter_data(filter_in.filter_data, filter_in.filter_type)
    
    # Handle default filter logic
    if filter_in.is_default:
        # Unset any existing default filters of the same type
        existing_default = crud_user_saved_filter.get_default(
            db=db,
            user_id=current_user_id,
            filter_type=filter_in.filter_type
        )
        
        if existing_default:
            existing_default.is_default = False
            db.add(existing_default)
            db.commit()
    
    # Create the new filter
    return crud_user_saved_filter.create(
        db=db,
        obj_in=filter_in,
        user_id=current_user_id
    )


def update_user_saved_filter(
    db: Session,
    filter_id: int,
    filter_in: SavedFilterUpdate,
    current_user_id: int,
    is_admin: bool = False
) -> UserSavedFilter:
    """
    Update a saved filter
    
    Args:
        db: Database session
        filter_id: ID of the filter to update
        filter_in: New filter data
        current_user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        UserSavedFilter: The updated saved filter
        
    Raises:
        HTTPException: If filter not found, user doesn't have permission, or validation fails
    """
    # Get the filter and check permissions
    saved_filter = get_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user_id,
        is_admin=is_admin
    )
    
    # Validate filter data if provided
    if filter_in.filter_data is not None:
        validate_filter_data(filter_in.filter_data, saved_filter.filter_type)
    
    # Handle default filter logic
    if filter_in.is_default and filter_in.is_default != saved_filter.is_default:
        # Unset any existing default filters of the same type
        existing_default = crud_user_saved_filter.get_default(
            db=db,
            user_id=saved_filter.user_id,
            filter_type=saved_filter.filter_type
        )
        
        if existing_default and existing_default.id != saved_filter.id:
            existing_default.is_default = False
            db.add(existing_default)
            db.commit()
    
    # Update the filter
    return crud_user_saved_filter.update(
        db=db,
        db_obj=saved_filter,
        obj_in=filter_in
    )


def delete_user_saved_filter(
    db: Session,
    filter_id: int,
    current_user_id: int,
    is_admin: bool = False
) -> UserSavedFilter:
    """
    Delete a saved filter
    
    Args:
        db: Database session
        filter_id: ID of the filter to delete
        current_user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        UserSavedFilter: The deleted saved filter
        
    Raises:
        HTTPException: If filter not found or user doesn't have permission
    """
    # Get the filter and check permissions
    saved_filter = get_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user_id,
        is_admin=is_admin
    )
    
    # Delete the filter
    return crud_user_saved_filter.remove(db=db, filter_id=filter_id)


def set_default_user_saved_filter(
    db: Session,
    filter_id: int,
    current_user_id: int,
    is_admin: bool = False
) -> UserSavedFilter:
    """
    Set a saved filter as default
    
    Args:
        db: Database session
        filter_id: ID of the filter to set as default
        current_user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        UserSavedFilter: The updated saved filter
        
    Raises:
        HTTPException: If filter not found or user doesn't have permission
    """
    # Get the filter and check permissions
    saved_filter = get_user_saved_filter(
        db=db,
        filter_id=filter_id,
        current_user_id=current_user_id,
        is_admin=is_admin
    )
    
    # Set as default
    return crud_user_saved_filter.set_as_default(db=db, db_obj=saved_filter)


def validate_filter_data(filter_data: Dict[str, Any], filter_type: str) -> None:
    """
    Validate filter data structure
    
    Args:
        filter_data: Filter data to validate
        filter_type: Type of filter
        
    Raises:
        HTTPException: If validation fails
    """
    # Basic validation - check if it's a valid JSON object
    try:
        # Try to serialize and deserialize to ensure it's valid JSON
        json.loads(json.dumps(filter_data))
    except (TypeError, ValueError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid filter data format: {str(e)}"
        )
    
    # For offer filters, perform more specific validation
    if filter_type == "offer":
        # Check for required top-level keys
        required_keys = ["basic_filters"]
        for key in required_keys:
            if key not in filter_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required key in filter data: {key}"
                )
        
        # Validate basic_filters structure if present
        if "basic_filters" in filter_data:
            if not isinstance(filter_data["basic_filters"], dict):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="basic_filters must be an object"
                )
        
        # Validate column_filters structure if present
        if "column_filters" in filter_data:
            if not isinstance(filter_data["column_filters"], list):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="column_filters must be an array"
                )
        
        # Validate complex_filters structure if present
        if "complex_filters" in filter_data:
            if not isinstance(filter_data["complex_filters"], dict):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="complex_filters must be an object"
                )
            
            # Check for required complex_filters keys
            if "operator" not in filter_data["complex_filters"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="complex_filters must have an operator key"
                )
            
            if "conditions" not in filter_data["complex_filters"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="complex_filters must have a conditions key"
                )
            
            if not isinstance(filter_data["complex_filters"]["conditions"], list):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="complex_filters.conditions must be an array"
                )
        
        # Validate sort_fields structure if present
        if "sort_fields" in filter_data:
            if not isinstance(filter_data["sort_fields"], list):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="sort_fields must be an array"
                )
            
            for sort_field in filter_data["sort_fields"]:
                if not isinstance(sort_field, dict):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Each sort field must be an object"
                    )
                
                if "field" not in sort_field or "direction" not in sort_field:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Each sort field must have field and direction keys"
                    )