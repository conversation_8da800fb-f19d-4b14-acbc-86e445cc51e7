from sqlalchemy.orm import Session
from app.models.offer import OfferLog
from app.utils.logging import logger

def create_offer_log(
    db: Session,
    offer_id: int,
    old_status: str,
    new_status: str,
    changed_by: int,
    note: str = None
) -> OfferLog:
    """
    Create a new offer log entry.
    
    Args:
        db (Session): Database session
        offer_id (int): ID of the offer being logged
        old_status (str): Previous status of the offer
        new_status (str): New status of the offer
        changed_by (int): User ID who made the change
        note (str, optional): Additional note about the change
        
    Returns:
        OfferLog: The created log entry
    """
    try:
        # Create new log entry
        log_entry = OfferLog(
            offer_id=offer_id,
            old_status=old_status,
            new_status=new_status,
            changed_by=changed_by,
            note=note
        )
        
        # Add to database
        db.add(log_entry)
        db.commit()
        db.refresh(log_entry)
        
        logger.info(f"Created offer log: offer_id={offer_id}, old_status={old_status}, new_status={new_status}")
        
        return log_entry
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating offer log: {str(e)}")
        raise 