"""
Fe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kezel<PERSON>ével kapcsolatos szolgáltatási funkciók
"""
import logging
from datetime import datetime, timedelta
import secrets
from typing import Optional, List, Union

from sqlalchemy.orm import Session
from sqlalchemy import or_, text

from app.models.user import User, UserDefaultSettings, PasswordResetToken
from app.schemas.user import UserCreate, UserUpdate, UserChangePassword, UserRoleUpdate
from app.schemas.user import UserDefaultSettingsCreate
from app.core.security import get_password_hash, verify_password, create_access_token, AuthenticationError

# Logger beállítása
logger = logging.getLogger(__name__)


# Felhasz<PERSON><PERSON>ó létrehoz<PERSON>a
def create_user(db: Session, user_data: UserCreate) -> User:
    """
    Új felhasználó létrehozása
    
    Args:
        db: Adatbázis session
        user_data: <PERSON><PERSON><PERSON>z<PERSON><PERSON><PERSON> l<PERSON>trehozási adatok
        
    Returns:
        User: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> felhasználó
        
    Raises:
        ValueError: Ha a felhasz<PERSON>ó már létezik
    """
    # Ellenőrizzük, hogy létezik-e már a felhasználó
    db_user = db.query(User).filter(User.email == user_data.email).first()
    if db_user:
        raise ValueError(f"A felhasználó már létezik: {user_data.email}")
    
    # Hozzuk létre a felhasználót hash-elt jelszóval
    hashed_password = get_password_hash(user_data.password)
    
    # Aktivációs token generálása
    activation_token = secrets.token_urlsafe(32)
    
    # Új felhasználó létrehozása
    db_user = User(
        email=user_data.email,
        password_hash=hashed_password,
        role="termelő",  # Alapértelmezett szerepkör
        company_name=user_data.company_name,
        tax_id=user_data.tax_id,
        contact_name=user_data.contact_name,
        phone_number=user_data.phone_number,
        is_active=False,  # Kezdetben nem aktív
        activation_token=activation_token
    )
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Felhasználó létrehozva: {db_user.email}")
    
    return db_user


# Felhasználó lekérdezése azonosító alapján
def get_user(db: Session, user_id: int) -> Optional[User]:
    """
    Felhasználó lekérdezése azonosító alapján
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[User]: Felhasználó vagy None, ha nem található
    """
    return db.query(User).filter(User.id == user_id).first()


# Felhasználó lekérdezése email alapján
def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    Felhasználó lekérdezése email alapján
    
    Args:
        db: Adatbázis session
        email: Email cím
        
    Returns:
        Optional[User]: Felhasználó vagy None, ha nem található
    """
    # Use case-insensitive comparison and strip whitespace
    return db.query(User).filter(User.email.ilike(email.strip())).first()


# Felhasználó keresése email vagy felhasználónév alapján
def get_user_by_email_or_username(db: Session, username: str) -> Optional[User]:
    """
    Felhasználó keresése email vagy felhasználónév alapján
    
    Args:
        db: Adatbázis session
        username: Email cím vagy felhasználónév
        
    Returns:
        Optional[User]: Felhasználó vagy None, ha nem található
    """
    # Use case-insensitive comparison and strip whitespace
    return db.query(User).filter(User.email.ilike(username.strip())).first()


# Felhasználók listázása
def get_users(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None,
    role: Optional[str] = None
) -> List[User]:
    """
    Felhasználók listázása
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        search: Keresési feltétel
        role: Szerepkör szerinti szűrés
        
    Returns:
        List[User]: Felhasználók listája
    """
    query = db.query(User)
    
    # Keresés név vagy email alapján
    if search:
        query = query.filter(
            or_(
                User.email.ilike(f"%{search}%"),
                User.company_name.ilike(f"%{search}%"),
                User.contact_name.ilike(f"%{search}%")
            )
        )
    
    # Szerepkör szerinti szűrés
    if role:
        query = query.filter(User.role == role)
    
    # Lapozás és eredmény visszaadása
    return query.order_by(User.email).offset(skip).limit(limit).all()


# Felhasználó frissítése
def update_user(db: Session, user_id: int, user_data: UserUpdate) -> Optional[User]:
    """
    Felhasználó frissítése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        user_data: Frissítési adatok
        
    Returns:
        Optional[User]: Frissített felhasználó vagy None, ha nem található
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Adatok frissítése
    update_data = user_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_user, key, value)
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Felhasználó frissítve: {db_user.email}")
    
    return db_user


# Felhasználó törlése
def delete_user(db: Session, user_id: int) -> bool:
    """
    Felhasználó törlése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        
    Returns:
        bool: Sikeres volt-e a törlés
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return False
    
    # Törlés az adatbázisból
    db.delete(db_user)
    db.commit()
    
    logger.info(f"Felhasználó törölve: {db_user.email}")
    
    return True


# Felhasználó jelszavának frissítése
def change_user_password(
    db: Session, user_id: int, password_data: UserChangePassword
) -> Optional[User]:
    """
    Felhasználó jelszavának frissítése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        password_data: Jelszó adatok
        
    Returns:
        Optional[User]: Frissített felhasználó vagy None, ha nem található
        
    Raises:
        AuthenticationError: Ha a jelenlegi jelszó nem megfelelő
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Jelenlegi jelszó ellenőrzése
    if not verify_password(password_data.current_password, db_user.password_hash):
        raise AuthenticationError("A jelenlegi jelszó nem megfelelő")
    
    # Új jelszó beállítása
    db_user.password_hash = get_password_hash(password_data.new_password)
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Jelszó frissítve: {db_user.email}")
    
    return db_user


# Felhasználó szerepkörének frissítése (admin funkció)
def update_user_role(
    db: Session, user_id: int, role_data: UserRoleUpdate
) -> Optional[User]:
    """
    Felhasználó szerepkörének frissítése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        role_data: Szerepkör adatok
        
    Returns:
        Optional[User]: Frissített felhasználó vagy None, ha nem található
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Szerepkör frissítése
    db_user.role = role_data.role
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Szerepkör frissítve: {db_user.email} -> {role_data.role}")
    
    return db_user


# Felhasználó aktiválása
def activate_user(db: Session, token: str) -> Optional[User]:
    """
    Felhasználó aktiválása token alapján
    
    Args:
        db: Adatbázis session
        token: Aktivációs token
        
    Returns:
        Optional[User]: Aktivált felhasználó vagy None, ha nem található
    """
    # Felhasználó lekérdezése
    db_user = db.query(User).filter(User.activation_token == token).first()
    if not db_user or db_user.is_active:
        return None
    
    # Aktiválás
    db_user.is_active = True
    db_user.activation_token = None
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Felhasználó aktiválva: {db_user.email}")
    
    return db_user

# Felhasználó deaktiválása (admin funkció)
def deactivate_user(db: Session, user_id: int) -> Optional[User]:
    """
    Felhasználó deaktiválása (az is_active mezőt False-ra állítja)
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[User]: Deaktivált felhasználó vagy None, ha nem található
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Deaktiválás
    db_user.is_active = False
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Felhasználó deaktiválva: {db_user.email}")
    
    return db_user

# Felhasználó (újra)aktiválása admin által
def reactivate_user(db: Session, user_id: int) -> Optional[User]:
    """
    Felhasználó aktiválása (az is_active mezőt True-ra állítja)
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[User]: Aktivált felhasználó vagy None, ha nem található
    """
    # Felhasználó lekérdezése
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    # Aktiválás
    db_user.is_active = True
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Felhasználó aktiválva: {db_user.email}")
    
    return db_user

# Jelszó visszaállítási token létrehozása
def create_password_reset_token(db: Session, email: str, expiry_hours: int = 24) -> Optional[str]:
    """
    Jelszó visszaállítási token létrehozása
    
    Args:
        db: Adatbázis session
        email: Email cím
        expiry_hours: Token érvényességi ideje órában
        
    Returns:
        Optional[str]: Token vagy None, ha nem található a felhasználó
    """
    # Felhasználó lekérdezése
    db_user = get_user_by_email(db, email)
    if not db_user:
        return None
    
    # Lejárati idő számítása
    expires_at = datetime.utcnow() + timedelta(hours=expiry_hours)
    
    # Token generálása
    token = secrets.token_urlsafe(32)
    
    # Régi tokenek érvénytelenítése
    db.query(PasswordResetToken).filter(
        PasswordResetToken.user_id == db_user.id,
        PasswordResetToken.is_used == False
    ).update({"is_used": True})
    
    # Új token létrehozása
    db_token = PasswordResetToken(
        user_id=db_user.id,
        token=token,
        expires_at=expires_at,
        is_used=False
    )
    
    # Mentés az adatbázisba
    db.add(db_token)
    db.commit()
    
    logger.info(f"Jelszó visszaállítási token létrehozva: {db_user.email}")
    
    return token


# Jelszó visszaállítása token alapján
def reset_password_with_token(db: Session, token: str, new_password: str) -> Optional[User]:
    """
    Jelszó visszaállítása token alapján
    
    Args:
        db: Adatbázis session
        token: Visszaállítási token
        new_password: Új jelszó
        
    Returns:
        Optional[User]: Frissített felhasználó vagy None, ha nem található/érvénytelen a token
    """
    # Token lekérdezése
    db_token = db.query(PasswordResetToken).filter(
        PasswordResetToken.token == token,
        PasswordResetToken.is_used == False,
        PasswordResetToken.expires_at > datetime.utcnow()
    ).first()
    
    if not db_token:
        return None
    
    # Felhasználó lekérdezése
    db_user = get_user(db, db_token.user_id)
    if not db_user:
        return None
    
    # Jelszó frissítése
    db_user.password_hash = get_password_hash(new_password)
    
    # Token használtként jelölése
    db_token.is_used = True
    
    # Mentés az adatbázisba
    db.add(db_user)
    db.add(db_token)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"Jelszó visszaállítva token alapján: {db_user.email}")
    
    return db_user


# Felhasználói alapbeállítások

# Alapbeállítások lekérdezése
def get_user_default_settings(db: Session, user_id: int) -> Optional[UserDefaultSettings]:
    """
    Felhasználó alapbeállításainak lekérdezése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        
    Returns:
        Optional[UserDefaultSettings]: Alapbeállítások vagy None, ha nem található
    """
    try:
        # SQL lekérdezés
        query = """
            SELECT id, user_id, default_product_type_id, default_quality_grade_id, default_category_id, default_quantity_unit, 
                  created_at, updated_at
            FROM user_default_settings
            WHERE user_id = :user_id
            LIMIT 1
        """
        
        # Nyers SQL lekérdezéssel olvassuk ki a biztonságosan elérhető mezőket
        result = db.execute(
            text(query), 
            {"user_id": user_id}
        )
        
        row = result.fetchone()
        
        if row is None:
            return None
        
        # Manuálisan létrehozzuk az objektumot
        settings = UserDefaultSettings()
        settings.id = row[0]
        settings.user_id = row[1]
        
        # Biztosítjuk, hogy a "NULL" stringek helyett None-t adjunk vissza
        # default_product_type_id feldolgozása
        settings.default_product_type_id = None if row[2] == "NULL" or row[2] is None else row[2]
            
        # default_quality_grade_id feldolgozása
        settings.default_quality_grade_id = None if row[3] == "NULL" or row[3] is None else row[3]
        
        # default_category_id feldolgozása
        settings.default_category_id = None if row[4] == "NULL" or row[4] is None else row[4]
        
        # default_quantity_unit feldolgozása - alapértelmezett érték: "kg"
        settings.default_quantity_unit = "kg" if row[5] == "NULL" or row[5] is None else row[5]
        
        # Ezek a mezők nem szerepelnek az adatbázisban, de pótoljuk dummy értékekkel
        settings.default_product_type_name = ''
        settings.default_quality_grade_name = ''
        settings.default_category_name = ''
        settings.has_quality_grades = False
        
        return settings
        
    except Exception as e:
        import traceback
        logger.error(f"Hiba a felhasználói beállítások lekérdezése során: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Fallback - minimális objektum létrehozása
        dummy_settings = UserDefaultSettings()
        dummy_settings.id = -1
        dummy_settings.user_id = user_id
        dummy_settings.default_product_type_id = None
        dummy_settings.default_quality_grade_id = None
        dummy_settings.default_category_id = None
        dummy_settings.default_quantity_unit = "kg"
        
        return dummy_settings


# Alapbeállítások létrehozása/frissítése
def upsert_user_default_settings(
    db: Session, user_id: int, settings_data: UserDefaultSettingsCreate
) -> UserDefaultSettings:
    """
    Felhasználó alapbeállításainak létrehozása vagy frissítése
    
    Args:
        db: Adatbázis session
        user_id: Felhasználó azonosító
        settings_data: Alapbeállítások adatok
        
    Returns:
        UserDefaultSettings: Létrehozott/frissített alapbeállítások
    """
    # Felhasználó létezésének ellenőrzése
    db_user = get_user(db, user_id)
    if not db_user:
        raise ValueError(f"Nem létező felhasználó: {user_id}")
    
    # Tranzakció kezelés - explicit módon indítunk tranzakciót
    try:
        print("\n===== USER DEFAULT SETTINGS UPSERT DEBUG =====")
        print(f"User ID: {user_id}")
        
        # Converziók: None értékek kezelése
        product_type_id = settings_data.default_product_type_id
        quality_grade_id = settings_data.default_quality_grade_id
        category_id = settings_data.default_category_id
        quantity_unit = settings_data.default_quantity_unit or "kg"
        has_quality_grades = settings_data.has_quality_grades
        
        print(f"Converted input values:")
        print(f"- product_type_id: {product_type_id} ({type(product_type_id)})")
        print(f"- quality_grade_id: {quality_grade_id} ({type(quality_grade_id)})")
        print(f"- category_id: {category_id} ({type(category_id)})")
        print(f"- quantity_unit: {quantity_unit} ({type(quantity_unit)})")
        print(f"- has_quality_grades: {has_quality_grades} ({type(has_quality_grades)})")
        
        # Ellenőrizzük, hogy van-e már meglévő beállítás
        check_result = db.execute(
            text("""
                SELECT id FROM user_default_settings WHERE user_id = :user_id LIMIT 1
            """), 
            {"user_id": user_id}
        )
        
        existing_id = check_result.scalar()
        print(f"Existing ID found: {existing_id}")
        
        # Adatok integritásának ellenőrzése SQL művelet előtt
        if str(product_type_id).lower() == "null":
            product_type_id = None
        if str(quality_grade_id).lower() == "null":
            quality_grade_id = None
        if str(category_id).lower() == "null":
            category_id = None
        
        # has_quality_grades nem lehet NULL - default érték beállítása, ha NULL lenne
        if has_quality_grades is None:
            has_quality_grades = False
            print(f"WARNING: has_quality_grades was None, setting default value: False")
        
        result_id = None
        
        if existing_id:
            # SQL összeállítása nyomkövetéshez
            update_sql = """
                UPDATE user_default_settings
                SET default_product_type_id = :product_type_id,
                    default_quality_grade_id = :quality_grade_id,
                    default_category_id = :category_id,
                    default_quantity_unit = :quantity_unit,
                    has_quality_grades = :has_quality_grades,
                    updated_at = now()
                WHERE user_id = :user_id
                RETURNING id
            """
            
            update_params = {
                "user_id": user_id,
                "product_type_id": product_type_id,
                "quality_grade_id": quality_grade_id,
                "category_id": category_id,
                "quantity_unit": quantity_unit,
                "has_quality_grades": has_quality_grades
            }
            
            print(f"UPDATE SQL: {update_sql}")
            print(f"UPDATE params: {update_params}")
            
            # Frissítés nyers SQL-lel
            result = db.execute(
                text(update_sql), 
                update_params
            )
            
            # Ellenőrzés hogy sikeres volt-e
            result_id = result.scalar()
            if not result_id:
                result_id = existing_id  # Ha nincs visszaadott ID, használjuk a meglévőt
            
            print(f"UPDATE result ID: {result_id}")
            
        else:
            # SQL összeállítása nyomkövetéshez
            insert_sql = """
                INSERT INTO user_default_settings
                (user_id, default_product_type_id, default_quality_grade_id, default_category_id, default_quantity_unit, has_quality_grades, created_at, updated_at)
                VALUES (:user_id, :product_type_id, :quality_grade_id, :category_id, :quantity_unit, :has_quality_grades, now(), now())
                RETURNING id
            """
            
            insert_params = {
                "user_id": user_id,
                "product_type_id": product_type_id,
                "quality_grade_id": quality_grade_id,
                "category_id": category_id,
                "quantity_unit": quantity_unit,
                "has_quality_grades": has_quality_grades
            }
            
            print(f"INSERT SQL: {insert_sql}")
            print(f"INSERT params: {insert_params}")
            
            # Beszúrás nyers SQL-lel
            result = db.execute(
                text(insert_sql), 
                insert_params
            )
            
            # Beszúrás után lekérdezzük az ID-t
            result_id = result.scalar()
            print(f"INSERT result ID: {result_id}")
            
        # Commit az adatbázis tranzakciót
        db.commit()
        print("Transaction committed successfully")
        
        if not result_id:
            print("WARNING: Operation didn't return an ID")
            raise ValueError("Nem sikerült menteni a beállításokat - nincs visszaadott ID")
        
        # Ellenőrizzük, hogy tényleg mentve lett-e
        verify_result = db.execute(
            text("""
                SELECT id, user_id, default_product_type_id, default_quality_grade_id, default_category_id, default_quantity_unit, has_quality_grades
                FROM user_default_settings 
                WHERE id = :id
            """),
            {"id": result_id}
        )
        
        verify_row = verify_result.fetchone()
        if verify_row:
            print("Verification successful - data was saved:")
            print(f"- ID: {verify_row[0]}")
            print(f"- User ID: {verify_row[1]}")
            print(f"- Product Type ID: {verify_row[2]} ({type(verify_row[2])})")
            print(f"- Quality Grade ID: {verify_row[3]} ({type(verify_row[3])})")
            print(f"- Category ID: {verify_row[4]} ({type(verify_row[4])})")
            print(f"- Quantity Unit: {verify_row[5]} ({type(verify_row[5])})")
            print(f"- Has Quality Grades: {verify_row[6]} ({type(verify_row[6])})")
        else:
            print("WARNING: Verification failed - data may not have been saved!")
            raise ValueError("Beállítások mentése után nem találhatók az adatok az adatbázisban")
        
        # Létrehozzuk a válaszobjektumot
        settings = UserDefaultSettings()
        settings.id = result_id
        settings.user_id = user_id
        settings.default_product_type_id = verify_row[2]
        settings.default_quality_grade_id = verify_row[3]
        settings.default_category_id = verify_row[4] 
        settings.default_quantity_unit = verify_row[5]
        settings.has_quality_grades = verify_row[6]
        
        # Ezek az adatok a sémában vannak, de az adatbázisban még nem
        # A Pydantic model számára adjuk át őket
        if hasattr(settings_data, 'default_product_type_name'):
            settings.default_product_type_name = settings_data.default_product_type_name
        if hasattr(settings_data, 'default_quality_grade_name'):
            settings.default_quality_grade_name = settings_data.default_quality_grade_name
        if hasattr(settings_data, 'default_category_name'):
            settings.default_category_name = settings_data.default_category_name
        
        print("Final settings object created with ID:", settings.id)
        print("===== END OF DEBUG =====\n")
        
        return settings
    
    except Exception as e:
        db.rollback()
        import traceback
        print(f"EXCEPTION during user settings upsert: {str(e)}")
        print(traceback.format_exc())
        logger.error(f"Hiba a felhasználói beállítások mentése során: {str(e)}")
        
        # Létrehozunk egy minimális beállítás objektumot hibajelzésként
        min_settings = UserDefaultSettings()
        min_settings.id = -1
        min_settings.user_id = user_id
        min_settings.default_quantity_unit = "kg"
        
        # Hibaüzenet hozzáadása a válaszhoz
        min_settings.error = str(e)
        
        print("Created error response object with ID -1")
        print("===== END OF DEBUG (ERROR) =====\n")
        
        return min_settings


# Autentikáció

# Felhasználó autentikálása
def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """
    Felhasználó autentikálása
    
    Args:
        db: Adatbázis session
        email: Email cím
        password: Jelszó
        
    Returns:
        Optional[User]: Autentikált felhasználó vagy None
    """
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.password_hash):
        return None
    return user


# JWT token létrehozása
def create_user_token(user: User) -> str:
    """
    JWT token létrehozása
    
    Args:
        user: Felhasználó
        
    Returns:
        str: JWT token
    """
    # Token adat
    token_data = {"user_id": user.id, "role": user.role}
    
    # Token létrehozása
    return create_access_token(token_data)
