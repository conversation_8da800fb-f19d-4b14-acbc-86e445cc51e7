"""
Termékekkel kapcsolatos szolgáltatási funkciók
"""
import logging
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.models.product import ProductCategory, ProductType, QualityGrade
from app.schemas.product import (
    ProductCategoryCreate, ProductCategoryUpdate,
    ProductTypeCreate, ProductTypeUpdate,
    QualityGradeCreate, QualityGradeUpdate
)

# Logger beállítása
logger = logging.getLogger(__name__)


# Termékkategória funkciók

def create_product_category(db: Session, category_data: ProductCategoryCreate) -> ProductCategory:
    """
    Termékkategória létrehozása
    
    Args:
        db: Adatbázis session
        category_data: Kategória adatok
        
    Returns:
        ProductCategory: Létrehozott kategória
        
    Raises:
        ValueError: Ha már létezik ilyen nevű kategória
    """
    # Ellen<PERSON>rizzük, hogy létezik-e már ilyen nevű kategória
    db_category = db.query(ProductCategory).filter(
        ProductCategory.name == category_data.name
    ).first()
    
    if db_category:
        raise ValueError(f"Már létezik ilyen nevű kategória: {category_data.name}")
    
    # Új kategória létrehozása
    db_category = ProductCategory(**category_data.dict())
    
    # Mentés az adatbázisba
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    logger.info(f"Termékkategória létrehozva: {db_category.name}")
    
    return db_category


def get_product_category(db: Session, category_id: int) -> Optional[ProductCategory]:
    """
    Termékkategória lekérdezése azonosító alapján
    
    Args:
        db: Adatbázis session
        category_id: Kategória azonosító
        
    Returns:
        Optional[ProductCategory]: Kategória vagy None, ha nem található
    """
    return db.query(ProductCategory).filter(ProductCategory.id == category_id).first()


def get_product_category_by_name(db: Session, name: str) -> Optional[ProductCategory]:
    """
    Termékkategória lekérdezése név alapján
    
    Args:
        db: Adatbázis session
        name: Kategória neve
        
    Returns:
        Optional[ProductCategory]: Kategória vagy None, ha nem található
    """
    return db.query(ProductCategory).filter(ProductCategory.name == name).first()


def get_product_categories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None
) -> List[ProductCategory]:
    """
    Termékkategóriák listázása
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        search: Keresési feltétel (név alapján)
        
    Returns:
        List[ProductCategory]: Kategóriák listája
    """
    query = db.query(ProductCategory)
    
    # Keresés név alapján
    if search:
        query = query.filter(ProductCategory.name.ilike(f"%{search}%"))
    
    # Lapozás és eredmény visszaadása
    return query.order_by(ProductCategory.name).offset(skip).limit(limit).all()


def update_product_category(
    db: Session, 
    category_id: int, 
    category_data: ProductCategoryUpdate
) -> Optional[ProductCategory]:
    """
    Termékkategória frissítése
    
    Args:
        db: Adatbázis session
        category_id: Kategória azonosító
        category_data: Frissítési adatok
        
    Returns:
        Optional[ProductCategory]: Frissített kategória vagy None, ha nem található
        
    Raises:
        ValueError: Ha már létezik ilyen nevű kategória
    """
    # Kategória lekérdezése
    db_category = get_product_category(db, category_id)
    if not db_category:
        return None
    
    # Adatok frissítése
    update_data = category_data.dict(exclude_unset=True)
    
    # Ha új nevet adunk meg, ellenőrizzük, hogy létezik-e már
    if "name" in update_data and update_data["name"] != db_category.name:
        existing_category = get_product_category_by_name(db, update_data["name"])
        if existing_category:
            raise ValueError(f"Már létezik ilyen nevű kategória: {update_data['name']}")
    
    # Adatok frissítése
    for key, value in update_data.items():
        setattr(db_category, key, value)
    
    # Mentés az adatbázisba
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    logger.info(f"Termékkategória frissítve: {db_category.name}")
    
    return db_category


def delete_product_category(db: Session, category_id: int) -> bool:
    """
    Termékkategória törlése
    
    Args:
        db: Adatbázis session
        category_id: Kategória azonosító
        
    Returns:
        bool: Sikeres volt-e a törlés
    """
    # Kategória lekérdezése
    db_category = get_product_category(db, category_id)
    if not db_category:
        return False
    
    # Ellenőrizzük, hogy van-e hozzá tartozó terméktípus
    types_count = db.query(ProductType).filter(
        ProductType.category_id == category_id
    ).count()
    
    if types_count > 0:
        raise ValueError(f"A kategória nem törölhető, mert {types_count} terméktípus tartozik hozzá")
    
    # Törlés az adatbázisból
    db.delete(db_category)
    db.commit()
    
    logger.info(f"Termékkategória törölve: {db_category.name}")
    
    return True


# Terméktípus funkciók

def create_product_type(db: Session, type_data: ProductTypeCreate) -> ProductType:
    """
    Terméktípus létrehozása
    
    Args:
        db: Adatbázis session
        type_data: Típus adatok
        
    Returns:
        ProductType: Létrehozott típus
        
    Raises:
        ValueError: Ha már létezik ilyen nevű típus a kategórián belül,
                   vagy ha nem létezik a megadott kategória
    """
    # Ellenőrizzük, hogy létezik-e a kategória
    category = get_product_category(db, type_data.category_id)
    if not category:
        raise ValueError(f"Nem létezik a megadott kategória: {type_data.category_id}")
    
    # Ellenőrizzük, hogy létezik-e már ilyen nevű típus a kategórián belül
    db_type = db.query(ProductType).filter(
        ProductType.name == type_data.name,
        ProductType.category_id == type_data.category_id
    ).first()
    
    if db_type:
        raise ValueError(f"Már létezik ilyen nevű típus a kategórián belül: {type_data.name}")
    
    # Új típus létrehozása
    db_type = ProductType(**type_data.dict())
    
    # Mentés az adatbázisba
    db.add(db_type)
    db.commit()
    db.refresh(db_type)
    
    logger.info(f"Terméktípus létrehozva: {db_type.name}")
    
    return db_type


def get_product_type(db: Session, type_id: int) -> Optional[ProductType]:
    """
    Terméktípus lekérdezése azonosító alapján
    
    Args:
        db: Adatbázis session
        type_id: Típus azonosító
        
    Returns:
        Optional[ProductType]: Típus vagy None, ha nem található
    """
    return db.query(ProductType).filter(ProductType.id == type_id).first()


def get_product_types(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None,
    category_id: Optional[int] = None
) -> List[ProductType]:
    """
    Terméktípusok listázása
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        search: Keresési feltétel (név alapján)
        category_id: Kategória azonosító (szűréshez)
        
    Returns:
        List[ProductType]: Típusok listája
    """
    query = db.query(ProductType)
    
    # Keresés név alapján
    if search:
        query = query.filter(ProductType.name.ilike(f"%{search}%"))
    
    # Szűrés kategória alapján
    if category_id:
        query = query.filter(ProductType.category_id == category_id)
    
    # Lapozás és eredmény visszaadása
    return query.order_by(ProductType.name).offset(skip).limit(limit).all()


def update_product_type(
    db: Session, 
    type_id: int, 
    type_data: ProductTypeUpdate
) -> Optional[ProductType]:
    """
    Terméktípus frissítése
    
    Args:
        db: Adatbázis session
        type_id: Típus azonosító
        type_data: Frissítési adatok
        
    Returns:
        Optional[ProductType]: Frissített típus vagy None, ha nem található
        
    Raises:
        ValueError: Ha már létezik ilyen nevű típus a kategórián belül,
                   vagy ha nem létezik a megadott kategória
    """
    # Típus lekérdezése
    db_type = get_product_type(db, type_id)
    if not db_type:
        return None
    
    # Adatok frissítése
    update_data = type_data.dict(exclude_unset=True)
    
    # Ha kategóriát váltunk, ellenőrizzük, hogy létezik-e
    if "category_id" in update_data:
        category = get_product_category(db, update_data["category_id"])
        if not category:
            raise ValueError(f"Nem létezik a megadott kategória: {update_data['category_id']}")
    
    # Ha nevet vagy kategóriát váltunk, ellenőrizzük, hogy létezik-e már ilyen nevű típus a kategórián belül
    if ("name" in update_data and update_data["name"] != db_type.name) or \
       ("category_id" in update_data and update_data["category_id"] != db_type.category_id):
        
        category_id = update_data.get("category_id", db_type.category_id)
        name = update_data.get("name", db_type.name)
        
        existing_type = db.query(ProductType).filter(
            ProductType.name == name,
            ProductType.category_id == category_id,
            ProductType.id != type_id
        ).first()
        
        if existing_type:
            raise ValueError(f"Már létezik ilyen nevű típus a kategórián belül: {name}")
    
    # Adatok frissítése
    for key, value in update_data.items():
        setattr(db_type, key, value)
    
    # Mentés az adatbázisba
    db.add(db_type)
    db.commit()
    db.refresh(db_type)
    
    logger.info(f"Terméktípus frissítve: {db_type.name}")
    
    return db_type


def delete_product_type(db: Session, type_id: int) -> bool:
    """
    Terméktípus törlése
    
    Args:
        db: Adatbázis session
        type_id: Típus azonosító
        
    Returns:
        bool: Sikeres volt-e a törlés
    """
    # Típus lekérdezése
    db_type = get_product_type(db, type_id)
    if not db_type:
        return False
    
    # Ellenőrizzük, hogy van-e hozzá tartozó minőségi besorolás
    grades_count = db.query(QualityGrade).filter(
        QualityGrade.product_type_id == type_id
    ).count()
    
    if grades_count > 0:
        raise ValueError(f"A típus nem törölhető, mert {grades_count} minőségi besorolás tartozik hozzá")
    
    # Törlés az adatbázisból
    db.delete(db_type)
    db.commit()
    
    logger.info(f"Terméktípus törölve: {db_type.name}")
    
    return True


# Minőségi besorolás funkciók

def create_quality_grade(db: Session, grade_data: QualityGradeCreate) -> QualityGrade:
    """
    Minőségi besorolás létrehozása
    
    Args:
        db: Adatbázis session
        grade_data: Besorolás adatok
        
    Returns:
        QualityGrade: Létrehozott besorolás
        
    Raises:
        ValueError: Ha már létezik ilyen nevű besorolás a terméktípuson belül,
                   vagy ha nem létezik a megadott terméktípus
    """
    # Ellenőrizzük, hogy létezik-e a terméktípus
    product_type = get_product_type(db, grade_data.product_type_id)
    if not product_type:
        raise ValueError(f"Nem létezik a megadott terméktípus: {grade_data.product_type_id}")
    
    # Ellenőrizzük, hogy támogatja-e a terméktípus a minőségi besorolásokat
    if not product_type.has_quality_grades:
        raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
    
    # Ellenőrizzük, hogy létezik-e már ilyen nevű besorolás a terméktípuson belül
    db_grade = db.query(QualityGrade).filter(
        QualityGrade.name == grade_data.name,
        QualityGrade.product_type_id == grade_data.product_type_id
    ).first()
    
    if db_grade:
        raise ValueError(f"Már létezik ilyen nevű besorolás a terméktípuson belül: {grade_data.name}")
    
    # Új besorolás létrehozása
    db_grade = QualityGrade(**grade_data.dict())
    
    # Mentés az adatbázisba
    db.add(db_grade)
    db.commit()
    db.refresh(db_grade)
    
    logger.info(f"Minőségi besorolás létrehozva: {db_grade.name}")
    
    return db_grade


def get_quality_grade(db: Session, grade_id: int) -> Optional[QualityGrade]:
    """
    Minőségi besorolás lekérdezése azonosító alapján
    
    Args:
        db: Adatbázis session
        grade_id: Besorolás azonosító
        
    Returns:
        Optional[QualityGrade]: Besorolás vagy None, ha nem található
    """
    return db.query(QualityGrade).filter(QualityGrade.id == grade_id).first()


def get_quality_grades(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None,
    product_type_id: Optional[int] = None
) -> List[QualityGrade]:
    """
    Minőségi besorolások listázása
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        search: Keresési feltétel (név alapján)
        product_type_id: Terméktípus azonosító (szűréshez)
        
    Returns:
        List[QualityGrade]: Besorolások listája
    """
    query = db.query(QualityGrade)
    
    # Keresés név alapján
    if search:
        query = query.filter(QualityGrade.name.ilike(f"%{search}%"))
    
    # Szűrés terméktípus alapján
    if product_type_id:
        query = query.filter(QualityGrade.product_type_id == product_type_id)
    
    # Lapozás és eredmény visszaadása
    return query.order_by(QualityGrade.name).offset(skip).limit(limit).all()


def update_quality_grade(
    db: Session, 
    grade_id: int, 
    grade_data: QualityGradeUpdate
) -> Optional[QualityGrade]:
    """
    Minőségi besorolás frissítése
    
    Args:
        db: Adatbázis session
        grade_id: Besorolás azonosító
        grade_data: Frissítési adatok
        
    Returns:
        Optional[QualityGrade]: Frissített besorolás vagy None, ha nem található
        
    Raises:
        ValueError: Ha már létezik ilyen nevű besorolás a terméktípuson belül,
                   vagy ha nem létezik a megadott terméktípus
    """
    # Besorolás lekérdezése
    db_grade = get_quality_grade(db, grade_id)
    if not db_grade:
        return None
    
    # Adatok frissítése
    update_data = grade_data.dict(exclude_unset=True)
    
    # Ha terméktípust váltunk, ellenőrizzük, hogy létezik-e és támogatja-e a minőségi besorolásokat
    if "product_type_id" in update_data:
        product_type = get_product_type(db, update_data["product_type_id"])
        if not product_type:
            raise ValueError(f"Nem létezik a megadott terméktípus: {update_data['product_type_id']}")
        
        if not product_type.has_quality_grades:
            raise ValueError(f"A terméktípus nem támogatja a minőségi besorolásokat: {product_type.name}")
    
    # Ha nevet vagy terméktípust váltunk, ellenőrizzük, hogy létezik-e már ilyen nevű besorolás a terméktípuson belül
    if ("name" in update_data and update_data["name"] != db_grade.name) or \
       ("product_type_id" in update_data and update_data["product_type_id"] != db_grade.product_type_id):
        
        product_type_id = update_data.get("product_type_id", db_grade.product_type_id)
        name = update_data.get("name", db_grade.name)
        
        existing_grade = db.query(QualityGrade).filter(
            QualityGrade.name == name,
            QualityGrade.product_type_id == product_type_id,
            QualityGrade.id != grade_id
        ).first()
        
        if existing_grade:
            raise ValueError(f"Már létezik ilyen nevű besorolás a terméktípuson belül: {name}")
    
    # Adatok frissítése
    for key, value in update_data.items():
        setattr(db_grade, key, value)
    
    # Mentés az adatbázisba
    db.add(db_grade)
    db.commit()
    db.refresh(db_grade)
    
    logger.info(f"Minőségi besorolás frissítve: {db_grade.name}")
    
    return db_grade


def delete_quality_grade(db: Session, grade_id: int) -> bool:
    """
    Minőségi besorolás törlése
    
    Args:
        db: Adatbázis session
        grade_id: Besorolás azonosító
        
    Returns:
        bool: Sikeres volt-e a törlés
    """
    # Besorolás lekérdezése
    db_grade = get_quality_grade(db, grade_id)
    if not db_grade:
        return False
    
    # Törlés az adatbázisból
    db.delete(db_grade)
    db.commit()
    
    logger.info(f"Minőségi besorolás törölve: {db_grade.name}")
    
    return True


# Összetett lekérdezések

def get_product_categories_with_types(
    db: Session, 
    skip: int = 0, 
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Termékkategóriák lekérdezése a hozzájuk tartozó típusokkal együtt
    
    Args:
        db: Adatbázis session
        skip: Kihagyott elemek száma (lapozáshoz)
        limit: Maximális elemszám (lapozáshoz)
        
    Returns:
        List[Dict[str, Any]]: Kategóriák listája a hozzájuk tartozó típusokkal
    """
    categories = get_product_categories(db, skip, limit)
    
    result = []
    for category in categories:
        types = get_product_types(db, category_id=category.id)
        result.append({
            "category": category,
            "types": types
        })
    
    return result


def get_product_type_with_grades(db: Session, type_id: int) -> Optional[Dict[str, Any]]:
    """
    Terméktípus lekérdezése a hozzá tartozó minőségi besorolásokkal együtt
    
    Args:
        db: Adatbázis session
        type_id: Terméktípus azonosító
        
    Returns:
        Optional[Dict[str, Any]]: Terméktípus a hozzá tartozó minőségi besorolásokkal
                                  vagy None, ha nem található
    """
    product_type = get_product_type(db, type_id)
    if not product_type:
        return None
    
    grades = get_quality_grades(db, product_type_id=type_id)
    
    return {
        "product_type": product_type,
        "quality_grades": grades
    }