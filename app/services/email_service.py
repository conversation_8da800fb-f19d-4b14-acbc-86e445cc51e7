"""
Email küldéssel kapcsolatos szolgáltatási funkciók
"""
import logging
from typing import List, Optional
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from fastapi import BackgroundTasks
from pydantic import EmailStr
from jinja2 import Environment, select_autoescape, FileSystemLoader

try:
    import aiosmtplib
    SMTP_AVAILABLE = True
except ImportError:
    SMTP_AVAILABLE = False
    logging.warning("aiosmtplib not installed. Email sending will be logged only.")

from app.core.config import settings

# Logger beállítása
logger = logging.getLogger(__name__)

# Jinja2 környezet a sablon rendereléshez
templates_path = Path(__file__).parent.parent / "utils" / "email_templates"
env = Environment(
    loader=FileSystemLoader(templates_path),
    autoescape=select_autoescape(['html', 'xml'])
)


async def send_email(
    background_tasks: BackgroundTasks,
    email_to: List[EmailStr],
    subject: str,
    template_name: str,
    template_data: dict,
):
    """
    Email küldése háttérfolyamatban
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Címzett email címek
        subject: Tárgy
        template_name: Sablon neve (.html kiterjesztés nélkül)
        template_data: Sablon adatok
    """
    background_tasks.add_task(
        _send_email_task,
        email_to=email_to,
        subject=subject,
        template_name=template_name,
        template_data=template_data
    )


async def _send_email_task_async(
    email_to: List[EmailStr],
    subject: str,
    template_name: str,
    template_data: dict,
):
    """
    Async email küldési feladat végrehajtása SMTP-vel
    
    Args:
        email_to: Címzett email címek
        subject: Tárgy
        template_name: Sablon neve (.html kiterjesztés nélkül)
        template_data: Sablon adatok
    """
    try:
        # Sablon betöltése és renderelése
        template = env.get_template(f"{template_name}.html")
        html_content = template.render(**template_data)
        
        # Check if SMTP is configured
        if not all([settings.SMTP_HOST, settings.SMTP_PORT, settings.SMTP_USER, settings.SMTP_PASSWORD]):
            logger.warning("SMTP not configured. Email would be sent to: %s", email_to)
            logger.info(f"Email küldés (NO SMTP CONFIG): {subject} -> {email_to}")
            logger.debug(f"Email tartalom: {html_content}")
            return
        
        # Create message
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL}>"
        message["To"] = ", ".join(email_to)
        
        # Add HTML content
        html_part = MIMEText(html_content, "html")
        message.attach(html_part)
        
        # Send email via SMTP
        smtp_client = aiosmtplib.SMTP(
            hostname=settings.SMTP_HOST,
            port=settings.SMTP_PORT,
            use_tls=False,  # Don't use direct TLS for port 587
            start_tls=settings.SMTP_TLS  # Use STARTTLS instead
        )
        
        async with smtp_client:
            await smtp_client.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            await smtp_client.send_message(message)
        
        logger.info(f"Email sikeresen elküldve SMTP-n keresztül: {subject} -> {email_to}")
    
    except Exception as e:
        logger.error(f"Hiba az SMTP email küldés során: {str(e)}")
        raise


def _send_email_task(
    email_to: List[EmailStr],
    subject: str,
    template_name: str,
    template_data: dict,
):
    """
    Email küldési feladat végrehajtása
    
    Args:
        email_to: Címzett email címek
        subject: Tárgy
        template_name: Sablon neve (.html kiterjesztés nélkül)
        template_data: Sablon adatok
    """
    try:
        # Sablon betöltése és renderelése
        template = env.get_template(f"{template_name}.html")
        html_content = template.render(**template_data)
        
        # Egyszerű megoldás fejlesztési környezetben: csak naplózzuk az email adatokat
        if settings.ENVIRONMENT == "development" and not all([settings.SMTP_HOST, settings.SMTP_USER]):
            logger.info(f"Email küldés (DEVELOPMENT): {subject} -> {email_to}")
            logger.debug(f"Email tartalom: {html_content}")
            return
        
        # Ha van SMTP konfiguráció és aiosmtplib elérhető, használjuk az async verziót
        if SMTP_AVAILABLE and all([settings.SMTP_HOST, settings.SMTP_PORT, settings.SMTP_USER, settings.SMTP_PASSWORD]):
            # Az async függvényt szinkron kontextusból hívjuk
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            loop.run_until_complete(_send_email_task_async(email_to, subject, template_name, template_data))
        else:
            # Ha nincs SMTP konfiguráció, csak loggoljuk
            logger.info(f"Email küldés (NO SMTP): {subject} -> {email_to}")
            logger.debug(f"Email tartalom: {html_content}")
    
    except Exception as e:
        logger.error(f"Hiba az email küldés során: {str(e)}")
        raise


async def send_registration_email(
    background_tasks: BackgroundTasks,
    email_to: EmailStr,
    activation_token: str,
    user_name: Optional[str] = None,
    username: Optional[str] = None,
    role: Optional[str] = None,
):
    """
    Regisztrációs email küldése
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Címzett email cím
        activation_token: Aktivációs token
        user_name: Felhasználó neve
        username: Felhasználónév
        role: Szerepkör
    """
    from datetime import datetime
    
    subject = f"{settings.APP_NAME} - Regisztráció megerősítése"
    
    activation_url = f"{settings.STREAMLIT_BROWSER_SERVER_ADDRESS}/activate?token={activation_token}"
    
    template_data = {
        "app_name": settings.APP_NAME,
        "activation_url": activation_url,
        "login_url": f"{settings.STREAMLIT_BROWSER_SERVER_ADDRESS}/auth_login",
        "user_name": user_name or username or email_to.split('@')[0],
        "username": username or email_to,
        "email": email_to,
        "role": role or "felhasználó",
        "registration_date": datetime.now().strftime("%Y-%m-%d %H:%M"),
    }
    
    await send_email(
        background_tasks=background_tasks,
        email_to=[email_to],
        subject=subject,
        template_name="registration",
        template_data=template_data
    )


async def send_password_reset_email(
    background_tasks: BackgroundTasks,
    email_to: EmailStr,
    reset_token: str,
    user_name: Optional[str] = None,
):
    """
    Jelszó visszaállítási email küldése
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Címzett email cím
        reset_token: Visszaállítási token
        user_name: Felhasználó neve (opcionális)
    """
    subject = f"{settings.APP_NAME} - Jelszó visszaállítása"
    
    reset_url = f"{settings.STREAMLIT_BROWSER_SERVER_ADDRESS}/reset-password?token={reset_token}"
    
    template_data = {
        "app_name": settings.APP_NAME,
        "reset_link": reset_url,  # A template ezt várja
        "user_name": user_name or email_to.split('@')[0],  # Ha nincs név, használjuk az email elejét
        "expiry_hours": 24,  # Token érvényességi idő órában
    }
    
    await send_email(
        background_tasks=background_tasks,
        email_to=[email_to],
        subject=subject,
        template_name="password_reset",
        template_data=template_data
    )


async def send_offer_confirmation_email(
    background_tasks: BackgroundTasks,
    email_to: EmailStr,
    offer_id: int,
    product_name: str,
    confirmed_quantity: float,
    confirmed_price: float,
):
    """
    Ajánlat visszaigazolás email küldése
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Címzett email cím
        offer_id: Ajánlat azonosító
        product_name: Termék neve
        confirmed_quantity: Visszaigazolt mennyiség
        confirmed_price: Visszaigazolt ár
    """
    subject = f"{settings.APP_NAME} - Ajánlat visszaigazolása (#{offer_id})"
    
    total_value = confirmed_quantity * confirmed_price
    
    template_data = {
        "app_name": settings.APP_NAME,
        "offer_id": offer_id,
        "product_name": product_name,
        "confirmed_quantity": confirmed_quantity,
        "confirmed_price": confirmed_price,
        "total_value": total_value,
        "offer_url": f"{settings.STREAMLIT_BROWSER_SERVER_ADDRESS}/offers/{offer_id}",
    }
    
    await send_email(
        background_tasks=background_tasks,
        email_to=[email_to],
        subject=subject,
        template_name="offer_confirmation",
        template_data=template_data
    )


async def send_offer_finalized_email(
    background_tasks: BackgroundTasks,
    email_to: EmailStr,
    offer_id: int,
    product_name: str,
    confirmed_quantity: float,
    confirmed_price: float,
    delivery_date: str,
):
    """
    Ajánlat véglegesítés email küldése
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Címzett email cím
        offer_id: Ajánlat azonosító
        product_name: Termék neve
        confirmed_quantity: Visszaigazolt mennyiség
        confirmed_price: Visszaigazolt ár
        delivery_date: Beszállítás dátuma
    """
    subject = f"{settings.APP_NAME} - Ajánlat véglegesítve (#{offer_id})"
    
    total_value = confirmed_quantity * confirmed_price
    
    template_data = {
        "app_name": settings.APP_NAME,
        "offer_id": offer_id,
        "product_name": product_name,
        "confirmed_quantity": confirmed_quantity,
        "confirmed_price": confirmed_price,
        "total_value": total_value,
        "delivery_date": delivery_date,
        "offer_url": f"{settings.STREAMLIT_BROWSER_SERVER_ADDRESS}/offers/{offer_id}",
    }
    
    await send_email(
        background_tasks=background_tasks,
        email_to=[email_to],
        subject=subject,
        template_name="offer_finalized",
        template_data=template_data
    )