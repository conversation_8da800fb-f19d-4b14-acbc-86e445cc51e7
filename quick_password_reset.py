#!/usr/bin/env python3
"""
Quick Password Reset Request Script
Sends a password reset request without confirmation
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:8000/api"
EMAIL = "racz.j<PERSON><PERSON>@frissenszeretem.hu"

# For Docker environment, you might need to use the backend service name
# API_BASE_URL = "http://backend:8000/api/v1"

def main():
    endpoint = f"{API_BASE_URL}/auth/password-reset-request"
    
    print(f"Sending password reset request for: {EMAIL}")
    print(f"API Endpoint: {endpoint}")
    
    try:
        response = requests.post(
            endpoint,
            json={"email": EMAIL},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Password reset request sent!")
            data = response.json()
            print(f"Response: {data.get('message', data)}")
        else:
            print("❌ ERROR: Request failed")
            try:
                error = response.json()
                print(f"Error: {error.get('detail', error)}")
            except:
                print(f"Response: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Cannot connect to API")
        print("Try: http://backend:8000/api/v1 if running inside Docker")
    except Exception as e:
        print(f"❌ ERROR: {type(e).__name__}: {str(e)}")

if __name__ == "__main__":
    main()