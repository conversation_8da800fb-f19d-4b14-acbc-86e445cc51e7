---
inclusion: always
---

# CONTEXT7 Implementation Rule

## Mandatory Pre-Implementation Analysis

**CRITICAL RULE**: Before implementing ANY code changes or new features, you MUST analyze the existing codebase and understand the current context using available tools (CONTEXT7 MCP server when available, or manual codebase analysis).

### Required Steps Before Implementation:

1. **Codebase Analysis**: Use CONTEXT7 to analyze the relevant parts of the codebase
   - Understand existing patterns and conventions
   - Identify related components and dependencies
   - Check for similar implementations that can be referenced

2. **Context Gathering**: Use CONTEXT7 to gather context about:
   - Current architecture patterns
   - Existing data models and their usage
   - API endpoints and their structure
   - UI component patterns and styling

3. **Code Search**: Use CONTEXT7 to search for:
   - Similar functionality already implemented
   - Existing utilities that can be reused
   - Patterns to follow or avoid
   - Integration points with existing systems

### Implementation Workflow:

```
1. Read task requirements
2. → ANALYZE relevant codebase sections (CONTEXT7 or manual analysis)
3. → SEARCH for existing patterns and implementations
4. → UNDERSTAND integration points with existing systems
5. Plan implementation based on gathered context
6. Implement following existing patterns and conventions
7. Test and validate
```

### Analysis Tools to Use:

**When CONTEXT7 MCP is available:**
- `mcp_Context7_resolve_library_id` - For finding relevant libraries and documentation
- `mcp_Context7_get_library_docs` - For getting specific library documentation and patterns

**When CONTEXT7 MCP is not available (fallback):**
- Use `readFile` and `readMultipleFiles` to analyze existing code
- Use `grepSearch` to find patterns and similar implementations
- Use `listDirectory` to understand project structure
- Use `fileSearch` to locate relevant components

### Why This Rule Exists:

- **Consistency**: Ensures new code follows existing patterns
- **Efficiency**: Avoids reinventing existing functionality
- **Quality**: Leverages proven patterns and approaches
- **Integration**: Ensures proper integration with existing systems
- **Maintenance**: Makes code easier to maintain by following conventions

### Enforcement:

This rule applies to ALL implementation tasks in the simplified-offer-management spec and any future development work. No exceptions.

**Remember**: Understanding the existing codebase context is crucial for creating maintainable, consistent, and well-integrated solutions.