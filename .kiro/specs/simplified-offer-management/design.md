# Design Document

## Overview

Az egyszerűsített ajánlatke<PERSON> rendszer egy tis<PERSON>ta, moduláris architektú<PERSON><PERSON><PERSON> követ, amely a jelenlegi túlbonyolított megoldást egy karbantartható és hatékony rendszerrel helyettesíti. A design fókusza a egyszerűség, teljesítmény és felhasználói élmény.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Streamlit Frontend                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Filter Panel  │  │   Offer Table   │  │  Offer Detail   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Filter Manager  │  │  Data Manager   │  │ Action Manager  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      API Client Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   API Client    │  │  Cache Manager  │  │ Error Handler   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     FastAPI Backend                         │
└─────────────────────────────────────────────────────────────┘
```

### Component Interaction Flow

```mermaid
graph TD
    A[User Interaction] --> B[Filter Panel]
    B --> C[Filter Manager]
    C --> D[Data Manager]
    D --> E[API Client]
    E --> F[Backend API]
    F --> G[Database]
    G --> F
    F --> E
    E --> H[Cache Manager]
    H --> D
    D --> I[Offer Table]
    I --> J[User Interface]
    
    K[User Action] --> L[Action Manager]
    L --> E
    L --> M[Offer Detail]
    M --> J
```

## Components and Interfaces

### 1. Filter Panel Component

**Responsibility:** Felhasználói szűrési felület kezelése

**Interface:**
```python
class FilterPanel:
    def render() -> Dict[str, Any]
    def get_current_filters() -> FilterState
    def reset_filters() -> None
    def apply_quick_filter(filter_type: str, value: Any) -> None
```

**Key Features:**
- Egyszerű, intuitív szűrő mezők
- Valós idejű szűrés 500ms debounce-szal
- Mentett szűrők támogatása
- Mobil-optimalizált elrendezés

### 2. Data Manager Component

**Responsibility:** Adatok betöltése, cache-elése és kezelése

**Interface:**
```python
class DataManager:
    def load_offers(filters: FilterState) -> List[Offer]
    def get_offer_details(offer_id: int) -> Offer
    def update_offer(offer_id: int, updates: Dict) -> bool
    def get_cached_data(cache_key: str) -> Optional[Any]
```

**Key Features:**
- Intelligens cache-elés
- Batch adatbetöltés
- Optimistic updates
- Error recovery

### 3. Offer Table Component

**Responsibility:** Ajánlatok táblázatos megjelenítése

**Interface:**
```python
class OfferTable:
    def render(offers: List[Offer]) -> None
    def handle_sort(column: str, direction: str) -> None
    def handle_selection(offer_ids: List[int]) -> None
    def render_pagination(total: int, current_page: int) -> None
```

**Key Features:**
- Virtualizált táblázat nagy adatmennyiséghez
- Sortable oszlopok
- Bulk selection
- Responsive design

### 4. Offer Detail Component

**Responsibility:** Ajánlat részletek megjelenítése és szerkesztése

**Interface:**
```python
class OfferDetail:
    def render(offer: Offer) -> None
    def handle_edit_mode() -> None
    def validate_changes(changes: Dict) -> ValidationResult
    def save_changes(offer_id: int, changes: Dict) -> bool
```

**Key Features:**
- Inline szerkesztés
- Valós idejű validáció
- Audit trail
- Státusz workflow kezelés

### 5. Action Manager Component

**Responsibility:** Felhasználói műveletek kezelése

**Interface:**
```python
class ActionManager:
    def execute_action(action: str, offer_ids: List[int]) -> ActionResult
    def get_available_actions(offer: Offer) -> List[Action]
    def validate_action(action: str, offer: Offer) -> bool
    def execute_bulk_action(action: str, offer_ids: List[int]) -> BulkResult
```

**Key Features:**
- Workflow-alapú műveletek
- Bulk műveletek
- Undo/Redo támogatás
- Progress tracking

## Data Models

### Core Data Structures

```python
@dataclass
class FilterState:
    producer_id: Optional[int] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    status_filters: List[str] = field(default_factory=list)
    product_category_id: Optional[int] = None
    search_term: Optional[str] = None
    sort_column: str = "created_at"
    sort_direction: str = "desc"
    page: int = 1
    page_size: int = 50

@dataclass
class Offer:
    id: int
    producer_name: str
    producer_id: int
    product_name: str
    product_category: str
    quantity_value: Decimal
    quantity_unit: str
    delivery_date: date
    status: str
    confirmed_price: Optional[Decimal]
    confirmed_quantity: Optional[Decimal]
    created_at: datetime
    updated_at: datetime

@dataclass
class ActionResult:
    success: bool
    message: str
    updated_offers: List[Offer]
    errors: List[str] = field(default_factory=list)
```

### State Management

```python
class AppState:
    current_filters: FilterState
    selected_offers: List[int]
    current_view: str  # "list" | "detail"
    selected_offer_id: Optional[int]
    loading_states: Dict[str, bool]
    error_messages: List[str]
    cache: Dict[str, Any]
```

## Error Handling

### Error Categories

1. **Network Errors**: API kapcsolat problémák
2. **Validation Errors**: Felhasználói input hibák
3. **Business Logic Errors**: Workflow szabályok megsértése
4. **System Errors**: Váratlan rendszerhibák

### Error Handling Strategy

```python
class ErrorHandler:
    def handle_api_error(error: APIError) -> UserMessage
    def handle_validation_error(error: ValidationError) -> UserMessage
    def handle_network_error(error: NetworkError) -> UserMessage
    def show_error_toast(message: str, error_type: str) -> None
```

**Error Recovery Patterns:**
- Automatic retry exponential backoff-fal
- Graceful degradation offline módban
- User-friendly hibaüzenetek
- Error logging és monitoring

## Testing Strategy

### Unit Testing

- **Filter Manager**: Szűrési logika tesztelése
- **Data Manager**: Cache és API hívások tesztelése
- **Action Manager**: Műveletek és validációk tesztelése
- **Components**: UI komponensek izolált tesztelése

### Integration Testing

- **API Integration**: Backend kapcsolat tesztelése
- **Component Integration**: Komponensek közötti kommunikáció
- **State Management**: Session state kezelés tesztelése

### E2E Testing

- **User Workflows**: Teljes felhasználói folyamatok
- **Performance Testing**: Nagy adatmennyiség kezelése
- **Mobile Testing**: Responsive design tesztelése

### Test Structure

```python
# Unit Test Example
def test_filter_manager_apply_filters():
    filter_manager = FilterManager()
    filters = FilterState(producer_id=1, status_filters=["CREATED"])
    
    result = filter_manager.build_api_params(filters)
    
    assert result["producer_id"] == 1
    assert result["status"] == ["CREATED"]

# Integration Test Example  
def test_data_manager_load_offers():
    data_manager = DataManager(api_client=MockAPIClient())
    filters = FilterState(producer_id=1)
    
    offers = data_manager.load_offers(filters)
    
    assert len(offers) > 0
    assert all(offer.producer_id == 1 for offer in offers)
```

## Performance Considerations

### Optimization Strategies

1. **Data Loading**
   - Lazy loading nagy listák esetén
   - Pagination 50 elemmel
   - Virtualizált táblázat 100+ elemnél

2. **Caching**
   - Session-level cache szűrési eredményekhez
   - Producer és termék listák cache-elése
   - Smart cache invalidation

3. **UI Responsiveness**
   - Debounced szűrés (500ms)
   - Optimistic updates
   - Loading states minden hosszú műveletnél

4. **Memory Management**
   - Komponens cleanup
   - Cache size limitek
   - Memory leak prevention

### Performance Metrics

- **Page Load Time**: < 2 seconds
- **Filter Response Time**: < 500ms
- **Detail View Load**: < 1 second
- **Save Operation**: < 3 seconds
- **Memory Usage**: < 100MB session-enként

## Security Considerations

### Authentication & Authorization

- JWT token alapú authentikáció
- Role-based access control (RBAC)
- Session timeout kezelés

### Data Protection

- Input sanitization minden felhasználói inputnál
- SQL injection védelem (ORM használat)
- XSS védelem (Streamlit built-in protection)

### API Security

- Rate limiting
- Request validation
- Error message sanitization

## Deployment Strategy

### Development Environment

```yaml
# docker-compose.dev.yml
services:
  streamlit-dev:
    build: ./streamlit_app
    volumes:
      - ./streamlit_app:/app
    environment:
      - DEBUG=true
      - API_BASE_URL=http://backend:8000/api
```

### Production Environment

```yaml
# docker-compose.prod.yml  
services:
  streamlit-prod:
    build: ./streamlit_app
    environment:
      - DEBUG=false
      - API_BASE_URL=https://api.production.com/api
    restart: always
```

### Monitoring & Logging

- Structured logging minden komponensben
- Performance metrics collection
- Error tracking és alerting
- User analytics (anonymized)

## Migration Strategy

### Phase 1: Core Components
- Filter Panel és Data Manager implementáció
- Alapvető Offer Table
- API Client réteg

### Phase 2: Advanced Features  
- Offer Detail szerkesztés
- Action Manager műveletek
- Cache optimalizáció

### Phase 3: Polish & Optimization
- Mobile optimization
- Performance tuning
- Advanced error handling

### Rollback Plan

- Feature flags minden új komponenshez
- A/B testing lehetőség
- Gyors rollback mechanizmus
- Monitoring és alerting