# Requirements Document

## Introduction

Az egyszerűsített ajánlatkezel<PERSON> rendszer célja, hogy a jelenlegi túlbonyolított és nehezen karbantartható megoldást egy tiszta, hatékony és felhasználóbarát rendszerrel helyettesítse. A rendszer az ügyintézők számára biztosít egy egyszerű, de hatékony felületet a mezőgazdasági ajánlatok kezelésére.

## Requirements

### Requirement 1

**User Story:** Mint ügyintéző, szeretnék egy egyszerű és gyors szűrési felületet, hogy könnyen megtaláljam a releváns ajánlatokat.

#### Acceptance Criteria

1. WHEN az ügyintéző megnyitja az ajánlatkezelő oldalt THEN a rendszer SHALL megjeleníteni egy egyszerű szűrő panelt
2. WHEN az ügyintéző termelő szerint szűr THEN a rendszer SHALL dropdown listában megjeleníteni az elérhető termelőket
3. WHEN az ügyintéző dátum szerint szűr THEN a rendszer SHALL dátumválasztó mezőket biztosítani
4. WHEN az ügyintéző státusz szerint szűr THEN a rendszer SHALL checkbox opciókat megjeleníteni az elérhető státuszokhoz
5. WHEN az ügyintéző termék szerint szűr THEN a rendszer SHALL hierarchikus termékkategória választót biztosítani
6. WHEN szűrési feltételek változnak THEN a rendszer SHALL automatikusan frissíteni az eredményeket 500ms késleltetéssel

### Requirement 2

**User Story:** Mint ügyintéző, szeretném az ajánlatokat áttekinthető táblázatos formában látni, hogy gyorsan áttekinthetem az információkat.

#### Acceptance Criteria

1. WHEN ajánlatok betöltődnek THEN a rendszer SHALL táblázatos formában megjeleníteni őket
2. WHEN a táblázat megjelenik THEN a rendszer SHALL tartalmazni a következő oszlopokat: ID, Termelő, Termék, Mennyiség, Szállítási dátum, Státusz
3. WHEN az ügyintéző oszlopfejlécre kattint THEN a rendszer SHALL rendezni a táblázatot az adott oszlop szerint
4. WHEN több mint 50 ajánlat van THEN a rendszer SHALL lapozást biztosítani
5. WHEN az ügyintéző ajánlatra kattint THEN a rendszer SHALL megnyitni a részletes nézetet

### Requirement 3

**User Story:** Mint ügyintéző, szeretném az ajánlatok részleteit megtekinteni és szerkeszteni, hogy feldolgozni tudjam őket.

#### Acceptance Criteria

1. WHEN az ügyintéző ajánlatra kattint THEN a rendszer SHALL megnyitni egy részletes nézetet
2. WHEN a részletes nézet megnyílik THEN a rendszer SHALL megjeleníteni az összes ajánlat adatot
3. WHEN az ügyintéző státuszt módosít THEN a rendszer SHALL validálni a státusz változást
4. WHEN az ügyintéző mennyiséget vagy árat módosít THEN a rendszer SHALL numerikus validációt alkalmazni
5. WHEN az ügyintéző mentést kezdeményez THEN a rendszer SHALL elmenteni a változásokat és visszatérni a lista nézethez
6. WHEN hiba történik mentés közben THEN a rendszer SHALL hibaüzenetet megjeleníteni

### Requirement 4

**User Story:** Mint ügyintéző, szeretnék gyors műveleteket végrehajtani az ajánlatokon, hogy hatékonyan dolgozhassak.

#### Acceptance Criteria

1. WHEN az ügyintéző ajánlatot kiválaszt THEN a rendszer SHALL megjeleníteni a gyors műveletek gombokat
2. WHEN az ügyintéző "Elfogad" gombra kattint THEN a rendszer SHALL megerősítést kérni
3. WHEN az ügyintéző "Elutasít" gombra kattint THEN a rendszer SHALL indoklást kérni
4. WHEN az ügyintéző tömeges műveletet választ THEN a rendszer SHALL lehetővé tenni több ajánlat kiválasztását
5. WHEN tömeges művelet végrehajtódik THEN a rendszer SHALL progress bar-t megjeleníteni

### Requirement 5

**User Story:** Mint ügyintéző, szeretnék valós idejű visszajelzést kapni a rendszer állapotáról, hogy tudjam mi történik.

#### Acceptance Criteria

1. WHEN adatok betöltődnek THEN a rendszer SHALL loading indikátort megjeleníteni
2. WHEN művelet sikeresen végrehajtódik THEN a rendszer SHALL sikeres üzenetet megjeleníteni
3. WHEN hiba történik THEN a rendszer SHALL érthető hibaüzenetet megjeleníteni
4. WHEN hosszú művelet fut THEN a rendszer SHALL progress indikátort megjeleníteni
5. WHEN hálózati kapcsolat megszakad THEN a rendszer SHALL offline üzenetet megjeleníteni

### Requirement 6

**User Story:** Mint ügyintéző, szeretném, hogy a rendszer gyorsan reagáljon, hogy hatékonyan dolgozhassak.

#### Acceptance Criteria

1. WHEN oldal betöltődik THEN a rendszer SHALL 2 másodpercen belül megjeleníteni az alapvető felületet
2. WHEN szűrők változnak THEN a rendszer SHALL 500ms-en belül reagálni
3. WHEN ajánlat részleteket nyitok meg THEN a rendszer SHALL 1 másodpercen belül betölteni
4. WHEN mentést végzek THEN a rendszer SHALL 3 másodpercen belül visszajelzést adni
5. WHEN 100+ ajánlat van THEN a rendszer SHALL virtualizált táblázatot használni a teljesítmény fenntartásához

### Requirement 7

**User Story:** Mint ügyintéző, szeretném, hogy a rendszer mobilon is használható legyen, hogy rugalmasan dolgozhassak.

#### Acceptance Criteria

1. WHEN mobileszközön nyitom meg THEN a rendszer SHALL reszponzív elrendezést használni
2. WHEN tablet méretű képernyőn használom THEN a rendszer SHALL optimalizált elrendezést megjeleníteni
3. WHEN érintőképernyőn használom THEN a rendszer SHALL megfelelő gomb méreteket biztosítani
4. WHEN mobilon szűrök THEN a rendszer SHALL összecsukható szűrő panelt megjeleníteni