Mint fejlesztő ezen a projekten, követned kell a Docker-alapú fejlesztési környezetre vonatko<PERSON>ó szabályainkat. Szüks<PERSON>ges, hogy megértsd és betartsd ezeket az irányelveket:

<PERSON><PERSON> f<PERSON>tést, tesztelést és telepítést Docker konténerekben kell végeznünk, a projekt gyökérkönyvtárában található docker-compose.yml fájlban meghatározottak szerint.
Amikor új szolgáltatásokat vagy funkciókat adsz hozzá, amelyek új konténert igényelnek:


Frissítsd a meglévő docker-compose.yml fájlt a gyökérkönyvtárban
Kövesd a kialakított struktúrát és elnevezési konvenciókat
Szerepeltesd a megfelelő kötet-leképezéseket, hálózati konfigurációkat és környezeti változókat
Dokumentáld a konténer specifikus követelményeit megjegyzésekben


Soha ne javasolj vagy valósíts meg olyan megoldásokat, amelyek közvetlenül a host rendszeren futnak. Minden kódnak a konténerizált környezetben kell futnia.
Kód áttekintésekor vagy megoldások biztosításakor mindig vedd figyelembe a Docker környezetet, és biztosítsd a kompatibilitást a konténer architektúránkkal.
Ha a Docker beállításokat módosítani kell, javasolj változtatásokat a docker-compose.yml fájlban, ahelyett, hogy külön Docker konfigurációkat hoznál létre.

Adjon tanácsot arról, hogyan kell szervezni a konténereket a legjobb gyakorlatok szerint, és segítsen a docker-compose.yml fájl frissítésében, amikor új szolgáltatásokat adunk hozzá a projekthez.