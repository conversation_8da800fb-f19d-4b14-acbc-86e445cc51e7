#!/usr/bin/env python3
"""
Test script for Confirmation Dialog
Tesztelő script a Visszaigazolás funkcióhoz
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Visszaigazolás Teszt", 
    layout="wide", 
    initial_sidebar_state="expanded"
)

st.title("✅ Visszaigazolás Funkció Teszt")

# Test data
test_offer = {
    'id': 123,
    'status': 'CREATED',
    'quantity_in_kg': 1000,
    'price': 250,
    'product_type': {
        'name': 'Alma - Golden Delicious',
        'category': {'name': 'Gyümölcs'}
    },
    'user': {
        'contact_name': 'Kovács János',
        'company_name': 'Alma Farm Kft.',
        'email': '<EMAIL>',
        'phone': '+36301234567'
    },
    'delivery_date': '2025-06-15'
}

st.info("""
### 📋 Visszaigazolás Funkció Jellemzők:
- **Mennyiség módosítás**: Az eredeti mennyiség ±10%-os eltéréssel módosítható
- **Ár módosítás**: Szabadon módosítható az egységár
- **Automatikus számítások**: Százalékos változás és összérték kalkuláció
- **Validáció**: Nem lehet 0 vagy negatív értéket megadni
- **Megjegyzés**: Opcionális megjegyzés hozzáadása
- **Vizuális visszajelzés**: Színkódolt változás indikátorok
""")

# Sidebar settings
st.sidebar.markdown("### ⚙️ Teszt Beállítások")

# Modify test data
test_offer['quantity_in_kg'] = st.sidebar.number_input(
    "Eredeti mennyiség (kg):",
    min_value=1,
    value=1000,
    step=100
)

test_offer['price'] = st.sidebar.number_input(
    "Eredeti ár (Ft/kg):",
    min_value=1,
    value=250,
    step=10
)

# Test modes
test_mode = st.sidebar.radio(
    "Teszt mód:",
    ["Standalone Dialog", "With Quick Action Bar", "Integration Test"]
)

st.markdown("---")

if test_mode == "Standalone Dialog":
    st.markdown("### 🔧 Standalone Confirmation Dialog Test")
    
    # Import and test the dialog directly
    try:
        from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
        
        # Show the dialog
        confirmed, quantity, price = render_confirmation_dialog(test_offer, test_offer['id'])
        
        # Show results
        if confirmed:
            st.success(f"""
            ### ✅ Visszaigazolás sikeres!
            - **Mennyiség**: {quantity:,.0f} kg
            - **Egységár**: {price:,.0f} Ft/kg
            - **Összérték**: {(quantity * price):,.0f} Ft
            """)
            
            # Show note if any
            note_key = f"confirmation_note_{test_offer['id']}"
            if note_key in st.session_state:
                st.info(f"**Megjegyzés**: {st.session_state[note_key]}")
                del st.session_state[note_key]
                
        elif confirmed is False:
            st.info("ℹ️ Visszaigazolás megszakítva")
            
    except Exception as e:
        st.error(f"Error loading confirmation dialog: {e}")
        st.exception(e)

elif test_mode == "With Quick Action Bar":
    st.markdown("### 🎯 Quick Action Bar Integration Test")
    
    # Set user as operator to show confirm button
    st.session_state["user"] = {
        "id": 1,
        "role": "operator",
        "name": "Test Operator"
    }
    
    # Import and render quick action bar
    try:
        from pages.operator.offer_management.quick_action_bar import render_quick_action_bar_native
        
        action = render_quick_action_bar_native(test_offer, test_offer['id'], key_prefix="test")
        
        if action == "confirm":
            st.markdown("### 📊 Visszaigazolás Dialógus")
            
            from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
            
            confirmed, quantity, price = render_confirmation_dialog(test_offer, test_offer['id'])
            
            if confirmed:
                st.success(f"""
                ### ✅ Visszaigazolás sikeres!
                - **Mennyiség**: {quantity:,.0f} kg
                - **Egységár**: {price:,.0f} Ft/kg
                - **Összérték**: {(quantity * price):,.0f} Ft
                
                **API hívás szimulálva**: `update_offer_status({test_offer['id']}, "CONFIRMED_BY_COMPANY", {{"confirmed_quantity": {quantity}, "confirmed_price": {price}}})`
                """)
                
    except Exception as e:
        st.error(f"Error in quick action bar test: {e}")

elif test_mode == "Integration Test":
    st.markdown("### 🔗 Full Integration Test")
    
    # Show different status scenarios
    tab1, tab2, tab3 = st.tabs(["CREATED → CONFIRMED", "Partial Confirmation", "Price Change"])
    
    with tab1:
        st.markdown("#### Teljes mennyiség visszaigazolása")
        st.write("Eredeti ajánlat:", test_offer)
        
        if st.button("Start Confirmation Process", key="full_confirm"):
            st.session_state["show_full_confirm"] = True
        
        if st.session_state.get("show_full_confirm", False):
            from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
            
            confirmed, quantity, price = render_confirmation_dialog(test_offer, 1001)
            
            if confirmed:
                st.success("✅ Státusz változás: CREATED → CONFIRMED_BY_COMPANY")
                st.json({
                    "offer_id": 1001,
                    "new_status": "CONFIRMED_BY_COMPANY",
                    "confirmed_quantity": quantity,
                    "confirmed_price": price,
                    "total_value": quantity * price
                })
                del st.session_state["show_full_confirm"]
    
    with tab2:
        st.markdown("#### Részleges visszaigazolás (80%)")
        modified_offer = test_offer.copy()
        modified_offer['id'] = 1002
        
        if st.button("Start Partial Confirmation", key="partial_confirm"):
            st.session_state["show_partial_confirm"] = True
        
        if st.session_state.get("show_partial_confirm", False):
            from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
            
            # Pre-set to 80%
            st.info("💡 Tipp: Állítsd be a mennyiséget 800 kg-ra (80%)")
            
            confirmed, quantity, price = render_confirmation_dialog(modified_offer, 1002)
            
            if confirmed:
                percentage = (quantity / modified_offer['quantity_in_kg']) * 100
                st.warning(f"⚠️ Részleges visszaigazolás: {percentage:.1f}%")
                st.json({
                    "offer_id": 1002,
                    "original_quantity": modified_offer['quantity_in_kg'],
                    "confirmed_quantity": quantity,
                    "percentage": f"{percentage:.1f}%"
                })
                del st.session_state["show_partial_confirm"]
    
    with tab3:
        st.markdown("#### Árváltozás visszaigazolás")
        price_offer = test_offer.copy()
        price_offer['id'] = 1003
        
        if st.button("Start Price Change Confirmation", key="price_confirm"):
            st.session_state["show_price_confirm"] = True
        
        if st.session_state.get("show_price_confirm", False):
            from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
            
            st.info("💡 Tipp: Módosítsd az árat 300 Ft/kg-ra (+20%)")
            
            confirmed, quantity, price = render_confirmation_dialog(price_offer, 1003)
            
            if confirmed:
                price_change = ((price - price_offer['price']) / price_offer['price']) * 100
                if price_change > 0:
                    st.success(f"📈 Ár emelkedés: +{price_change:.1f}%")
                elif price_change < 0:
                    st.warning(f"📉 Ár csökkenés: {price_change:.1f}%")
                    
                st.json({
                    "offer_id": 1003,
                    "original_price": price_offer['price'],
                    "confirmed_price": price,
                    "price_change": f"{price_change:+.1f}%"
                })
                del st.session_state["show_price_confirm"]

# Feature comparison
st.markdown("---")
st.markdown("### 🔍 Funkció Összehasonlítás")

col1, col2 = st.columns(2)

with col1:
    st.markdown("""
    #### Előző Megoldás:
    - ❌ Csak státusz váltás
    - ❌ Nincs mennyiség/ár módosítás
    - ❌ Egyszerű megerősítő dialógus
    - ❌ Nincs validáció
    - ❌ Nincs vizuális visszajelzés
    """)

with col2:
    st.markdown("""
    #### Új Visszaigazolás Funkció:
    - ✅ Mennyiség és ár módosítás
    - ✅ Automatikus kalkulációk
    - ✅ Százalékos változás kijelzés
    - ✅ Input validáció
    - ✅ Megjegyzés hozzáadása
    - ✅ Színkódolt visszajelzések
    - ✅ Összérték számítás
    """)

# Technical details
with st.expander("📝 Technikai Részletek", expanded=False):
    st.markdown("""
    ### API Integration:
    ```python
    # Visszaigazolás adatok
    confirmation_data = {
        "confirmed_quantity": 800.0,  # kg
        "confirmed_price": 300.0,     # Ft/kg
        "note": "Minőségi kifogás miatt csökkentett mennyiség"
    }
    
    # API hívás
    update_offer_status(
        offer_id=123,
        new_status="CONFIRMED_BY_COMPANY",
        confirmation_data=confirmation_data
    )
    ```
    
    ### Validation Rules:
    - **Mennyiség**: 0 < quantity <= original * 1.1 (max 10% túllépés)
    - **Ár**: 0 < price (nincs felső limit)
    - **Megjegyzés**: Opcionális, max 500 karakter
    
    ### UI Components:
    - `st.number_input()` - Mennyiség és ár bevitel
    - `st.metric()` - Összérték és változás kijelzés
    - `st.text_area()` - Megjegyzés
    - Color coding: 🟢 Növekedés, 🟡 Csökkenés, 🔵 Változatlan
    """)

# Footer
st.markdown("---")
st.markdown("""
**🎯 Eredmény**: A Visszaigazolás funkció teljes körű megoldást nyújt az ajánlatok 
cég általi visszaigazolására, lehetővé téve a mennyiség és ár módosítását validációval és vizuális visszajelzésekkel.
""")