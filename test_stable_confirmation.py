#!/usr/bin/env python3
"""
Test script for Stable Confirmation Dialog
Tesztelő script a stabil visszaigazolás funkcióhoz
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Stabil Visszaigazolás Teszt", 
    layout="wide", 
    initial_sidebar_state="expanded"
)

st.title("✅ Stabil Visszaigazolás Funkció Teszt")
st.markdown("### 🎯 Ez a verzió nem tűnik el értékváltoztatáskor!")

# Test data
test_offer = {
    'id': 123,
    'status': 'CREATED',
    'quantity_in_kg': 1000,
    'price': 250,
    'product_type': {
        'name': 'Alma - Golden Delicious',
        'category': {'name': 'Gyümölcs'}
    },
    'user': {
        'contact_name': '<PERSON><PERSON><PERSON><PERSON>',
        'company_name': 'Alma Farm Kft.',
        'email': '<EMAIL>',
        'phone': '+36301234567'
    },
    'delivery_date': '2025-06-15'
}

st.info("""
### 📋 Stabil Visszaigazolás Funkció Előnyei:
- ✅ **Nem tűnik el** amikor értéket változtatsz
- ✅ **Session state használat** az értékek megőrzéséhez
- ✅ **Inline megjelenítés** form nélkül
- ✅ **Azonnali visszajelzés** a változásokról
- ✅ **Egyszerű kezelőfelület** tiszta gombokkal
""")

# Sidebar settings
st.sidebar.markdown("### ⚙️ Teszt Beállítások")

# Modify test data
test_offer['quantity_in_kg'] = st.sidebar.number_input(
    "Eredeti mennyiség (kg):",
    min_value=1,
    value=1000,
    step=100
)

test_offer['price'] = st.sidebar.number_input(
    "Eredeti ár (Ft/kg):",
    min_value=1,
    value=250,
    step=10
)

# Test modes
test_mode = st.sidebar.radio(
    "Teszt mód:",
    ["Stable Inline Dialog", "Modal Overlay Demo", "Form-based Dialog", "Comparison Mode"]
)

st.markdown("---")

if test_mode == "Stable Inline Dialog":
    st.markdown("### 🔧 Stabil Inline Dialógus Teszt")
    st.markdown("**Próbáld ki:** Változtass meg bármilyen értéket - a dialógus nem fog eltűnni!")
    
    # Import and test the stable dialog
    try:
        from pages.operator.offer_management.stable_confirmation_dialog import render_inline_confirmation_form
        
        # Always show the dialog
        confirmed, quantity, price = render_inline_confirmation_form(test_offer, test_offer['id'])
        
        # Show results
        if confirmed:
            st.success(f"""
            ### ✅ Visszaigazolás sikeres!
            - **Mennyiség**: {quantity:,.0f} kg
            - **Egységár**: {price:,.0f} Ft/kg
            - **Összérték**: {(quantity * price):,.0f} Ft
            """)
            
            # Show note if any
            note_key = f"confirmation_note_{test_offer['id']}"
            if note_key in st.session_state:
                st.info(f"**Megjegyzés**: {st.session_state[note_key]}")
                
        elif confirmed is False:
            st.info("ℹ️ Visszaigazolás megszakítva")
            
    except Exception as e:
        st.error(f"Error loading stable confirmation dialog: {e}")
        st.exception(e)

elif test_mode == "Modal Overlay Demo":
    st.markdown("### 🖼️ Modal Overlay Teszt")
    st.markdown("**Próbáld ki:** Modal overlay-el teljes képernyős hatás")
    
    # Import and test modal functionality
    try:
        from pages.operator.offer_management.stable_confirmation_dialog import (
            show_confirmation_modal, 
            handle_confirmation_action,
            render_stable_confirmation_dialog
        )
        
        # Button to trigger modal
        if st.button("🔄 Modal visszaigazolás megnyitása", type="primary"):
            st.session_state[f"show_confirmation_modal_{test_offer['id']}"] = True
        
        # Check if modal should be shown
        if show_confirmation_modal(test_offer['id']):
            st.info("Modal overlay aktív - háttér elmosódva")
            
            # Show the stable dialog in modal mode
            confirmed, quantity, price = render_stable_confirmation_dialog(test_offer, test_offer['id'])
            
            if confirmed:
                st.success(f"""
                ### ✅ Modal visszaigazolás sikeres!
                - **Mennyiség**: {quantity:,.0f} kg
                - **Egységár**: {price:,.0f} Ft/kg
                - **Összérték**: {(quantity * price):,.0f} Ft
                """)
                # Clear modal state
                if f"show_confirmation_modal_{test_offer['id']}" in st.session_state:
                    del st.session_state[f"show_confirmation_modal_{test_offer['id']}"]
                
            elif confirmed is False:
                st.info("ℹ️ Modal visszaigazolás megszakítva")
                # Clear modal state
                if f"show_confirmation_modal_{test_offer['id']}" in st.session_state:
                    del st.session_state[f"show_confirmation_modal_{test_offer['id']}"]
        else:
            st.info("Kattints a gombra a modal megnyitásához")
            
    except Exception as e:
        st.error(f"Error loading modal functionality: {e}")

elif test_mode == "Form-based Dialog":
    st.markdown("### 📝 Form-alapú Dialógus Teszt")
    st.warning("**Figyelem:** Ez a verzió eltűnhet értékváltoztatáskor!")
    
    # Import and test the original dialog
    try:
        from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
        
        # Show the dialog
        confirmed, quantity, price = render_confirmation_dialog(test_offer, test_offer['id'] + 1000)
        
        # Show results
        if confirmed:
            st.success(f"""
            ### ✅ Visszaigazolás sikeres!
            - **Mennyiség**: {quantity:,.0f} kg
            - **Egységár**: {price:,.0f} Ft/kg
            - **Összérték**: {(quantity * price):,.0f} Ft
            """)
            
        elif confirmed is False:
            st.info("ℹ️ Visszaigazolás megszakítva")
            
    except Exception as e:
        st.error(f"Error loading form-based dialog: {e}")

elif test_mode == "Comparison Mode":
    st.markdown("### 🔍 Összehasonlító Teszt")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📝 Form-alapú (Régi)")
        st.warning("Eltűnhet értékváltoztatáskor")
        
        try:
            from pages.operator.offer_management.confirmation_dialog import render_confirmation_dialog
            confirmed1, quantity1, price1 = render_confirmation_dialog(test_offer, 2001)
            
            if confirmed1:
                st.success(f"Visszaigazolva: {quantity1:,.0f} kg @ {price1:,.0f} Ft/kg")
        except Exception as e:
            st.error(f"Error: {e}")
    
    with col2:
        st.markdown("#### 🎯 Stabil Inline (Új)")
        st.success("Nem tűnik el értékváltoztatáskor")
        
        try:
            from pages.operator.offer_management.stable_confirmation_dialog import render_inline_confirmation_form
            confirmed2, quantity2, price2 = render_inline_confirmation_form(test_offer, 2002)
            
            if confirmed2:
                st.success(f"Visszaigazolva: {quantity2:,.0f} kg @ {price2:,.0f} Ft/kg")
        except Exception as e:
            st.error(f"Error: {e}")

# Technical comparison
st.markdown("---")
st.markdown("### 🔧 Technikai Összehasonlítás")

col1, col2 = st.columns(2)

with col1:
    st.markdown("""
    #### Form-alapú Megoldás:
    ```python
    with st.form(key="confirmation_form"):
        quantity = st.number_input(...)
        price = st.number_input(...)
        
        submitted = st.form_submit_button()
        cancelled = st.form_submit_button()
    ```
    
    **Hátrányok:**
    - Form submit után újrarenderel
    - Eltűnhet a dialógus
    - Komplex state kezelés
    """)

with col2:
    st.markdown("""
    #### Stabil Inline Megoldás:
    ```python
    # Session state használat
    if f"conf_quantity_{id}" not in st.session_state:
        st.session_state[f"conf_quantity_{id}"] = original
    
    quantity = st.number_input(
        value=st.session_state[f"conf_quantity_{id}"]
    )
    st.session_state[f"conf_quantity_{id}"] = quantity
    ```
    
    **Előnyök:**
    - Értékek megőrzése session state-ben
    - Nem tűnik el változtatáskor
    - Egyszerűbb kezelés
    """)

# Footer
st.markdown("---")
st.markdown("""
**🎯 Eredmény**: A Stabil Visszaigazolás funkció megoldja a felhasználó problémáját - 
a dialógus nem tűnik el amikor értékeket változtat, és a gombok egy helyen maradnak.
""")

# Debug info
with st.expander("🔍 Session State Debug", expanded=False):
    relevant_keys = [k for k in st.session_state.keys() if 'conf' in k or 'inline' in k]
    if relevant_keys:
        st.write("**Kapcsolódó session state kulcsok:**")
        for key in relevant_keys:
            st.write(f"- {key}: {st.session_state[key]}")
    else:
        st.write("Nincs kapcsolódó session state")