version: '3.8'
services:
  db:
    image: postgres:15
    container_name: termelo-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: termelo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - termelo-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      APP_NAME: POM APP
      ENVIRONMENT: production
      BACKEND_HOST: 0.0.0.0
      BACKEND_PORT: 8000
      DATABASE_URL: **************************************/termelo_db
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: termelo_db
      SECRET_KEY: your-secret-key-here
      JWT_ALGORITHM: HS256
      ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      STREAMLIT_HOST: localhost
      STREAMLIT_PORT: 8501
      STREAMLIT_BROWSER_SERVER_ADDRESS: http://dns72.com:8501
      API_AUTH_TOKEN: "JWT_TOKEN"
      # SMTP settings - configure these with your SMTP server details
      SMTP_HOST: smtp.mail.me.com
      SMTP_PORT: 587
      SMTP_TLS: "true"
      SMTP_USER: <EMAIL>
      SMTP_PASSWORD: cznb-qmns-bzrx-nfrf
      EMAILS_FROM_EMAIL: <EMAIL>
      EMAILS_FROM_NAME: "POM"
    volumes:
      - ./app:/app/app
      - ./migrations:/app/migrations
      - ./init-db.sh:/app/init-db.sh
      - ./tests:/app/tests
    depends_on:
      db:
        condition: service_healthy
    networks:
      - termelo-network
    command: >
      sh -c "chmod +x /app/init-db.sh && /app/init-db.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"

  streamlit:
    build:
      context: ./streamlit_app
      dockerfile: Dockerfile
    container_name: streamlit
    restart: always
    ports:
      - "8501:8501"
    environment:
      API_HOST: http://backend:8000
      API_BASE_URL: http://backend:8000/api
      APP_NAME: POM APP
      COMPANY_NAME: Zöldség Világ Kft.
      API_AUTH_TOKEN: "PASTE_IDE_AZ_ADMIN_TOKEN"
    volumes:
      - ./streamlit_app:/app
      - ./streamlit_app/.streamlit:/app/.streamlit
    working_dir: /app
    command: streamlit run main.py
    depends_on:
      - backend
    networks:
      - termelo-network

  debug:
    build:
      context: ./debug
      dockerfile: Dockerfile
    container_name: debug-tools
    restart: "no"
    volumes:
      - ./debug:/app/debug
      - /var/run/docker.sock:/var/run/docker.sock
      - ./logs:/app/logs
    environment:
      CONTAINER_STREAMLIT: streamlit
      CONTAINER_BACKEND: backend
      CONTAINER_DB: termelo-db
      DOCKER_HOST: unix:///var/run/docker.sock
      DEBUG_CONFIG_FILE: /app/debug/config/log_paths.json
    depends_on:
      - streamlit
      - backend
      - db
    networks:
      - termelo-network
    tty: true
    stdin_open: true
    user: "0:0"

networks:
  termelo-network:
    driver: bridge

volumes:
  postgres_data:
