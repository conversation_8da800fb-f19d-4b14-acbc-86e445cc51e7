from typing import Any, Dict, Optional, Union, Callable
import requests
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
import logging
from functools import wraps
import time
import json
from .app_config import API_CLIENT_CONFIG
import streamlit as st

logger = logging.getLogger(__name__)

class APIClient:
    """API kliens osztály a HTTP kérések kezeléséhez."""
    
    def __init__(self, base_url: str):
        """
        Inicializálja az API klienst.
        
        Args:
            base_url: Az API alap URL-je
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Retry stratégia beállítása
        retry_strategy = Retry(
            total=API_CLIENT_CONFIG["retry_count"],
            backoff_factor=API_CLIENT_CONFIG["retry_backoff_factor"],
            status_forcelist=API_CLIENT_CONFIG["retry_status_forcelist"],
            allowed_methods=API_CLIENT_CONFIG["retry_allowed_methods"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Végrehajt egy HTTP kérést.
        
        Args:
            method: HTTP metódus (GET, POST, stb.)
            endpoint: API végpont
            data: Kérés törzse
            params: URL paraméterek
            headers: HTTP fejlécek
            
        Returns:
            A válasz JSON formátumban
            
        Raises:
            APIError: Ha az API hibaüzenetet küld
            ConnectionError: Ha nem sikerül kapcsolódni az API-hoz
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                headers=headers,
                timeout=API_CLIENT_CONFIG["timeout"]
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API hiba: {str(e)}")
            raise APIError(f"API hiba: {str(e)}") from e

    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """GET kérés végrehajtása."""
        return self._make_request("GET", endpoint, params=params)
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """POST kérés végrehajtása."""
        return self._make_request("POST", endpoint, data=data)
    
    def put(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """PUT kérés végrehajtása."""
        return self._make_request("PUT", endpoint, data=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """DELETE kérés végrehajtása."""
        return self._make_request("DELETE", endpoint)

# Cache dekorátor
def cacheable(ttl: int = 300):
    """
    Dekorátor az API válaszok gyorsítótárazásához.
    
    Args:
        ttl: A gyorsítótár élettartama másodpercben
    """
    def decorator(func):
        cache = {}
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < ttl:
                    return result
            
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
            
        return wrapper
    return decorator

class APIError(Exception):
    """API hibák kezelésére szolgáló kivétel osztály."""
    pass

# Gyorsítótár törlése
def clear_api_cache():
    """Törli az API gyorsítótárat."""
    global _api_cache
    _api_cache = {}

# ----- Új és továbbfejlesztett API funkciók -----

def handle_api_error(error, operation_type):
    """
    API hibák egységes kezelése részletes hibaüzenetekkel és felhasználói visszajelzéssel.
    
    Args:
        error (str): A hibaüzenet.
        operation_type (str): A művelet típusa (pl. "ajánlatok betöltése").
    """
    error_message = str(error)
    
    # Hibaüzenet naplózása
    logger.error(f"API error during {operation_type}: {error_message}")
    
    # 401/403 hibakódok kezelése (hitelesítési hibák)
    if "401" in error_message or "Unauthorized" in error_message or "403" in error_message:
        try:
            from pages.operator.offer_management.ui_components import show_inline_error
            show_inline_error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        except ImportError:
            st.error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        # Clear auth token
        if 'auth_token' in st.session_state:
            st.session_state.auth_token = None
        st.rerun()
    
    # 404 hibakódok kezelése (nem található erőforrás)
    elif "404" in error_message or "Not Found" in error_message:
        try:
            from pages.operator.offer_management.ui_components import show_inline_error
            show_inline_error(f"A kért erőforrás nem található. ({operation_type})")
        except ImportError:
            st.error(f"A kért erőforrás nem található. ({operation_type})")
    
    # Kapcsolódási problémák kezelése
    elif "Connection" in error_message or "timeout" in error_message.lower():
        try:
            from pages.operator.offer_management.ui_components import show_inline_error
            show_inline_error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
        except ImportError:
            st.error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
    
    # Egyéb hibák kezelése
    else:
        try:
            from pages.operator.offer_management.ui_components import show_inline_error
            show_inline_error(f"Váratlan hiba történt: {error_message} ({operation_type})")
        except ImportError:
            st.error(f"Váratlan hiba történt: {error_message} ({operation_type})")

def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """
    Biztonságos API hívás végrehajtása egységes hibakezeléssel.
    
    Ez a függvény egy API hívást próbál végrehajtani, és egységesen kezeli a lehetséges hibákat.
    Hasznos az ismétlődő try-except blokkok elkerülésére az alkalmazásban.
    
    Args:
        api_function (callable): Az API funkció, amelyet meg kell hívni.
        error_operation_name (str): A művelet neve hibaüzenetekhez.
        *args: További pozíciós argumentumok az API funkcióhoz.
        **kwargs: További kulcsszó argumentumok az API funkcióhoz.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az API hívás eredményét vagy a hibaüzenetet tartalmazza.
    """
    try:
        logger.info(f"Calling API function: {api_function.__name__} with args: {args}, kwargs: {kwargs}")
        success, result = api_function(*args, **kwargs)
        
        if not success:
            logger.error(f"API returned error for {error_operation_name}: {result}")
            handle_api_error(result, error_operation_name)
        
        return success, result
    except Exception as e:
        logger.error(f"Exception in {error_operation_name}: {str(e)}")
        handle_api_error(e, error_operation_name)
        return False, str(e)

def lazy_load_cache(cache_key, data_loader_func, cache_ttl=300):
    """
    Adatok betöltése gyorsítótár segítségével.
    
    Args:
        cache_key (str): A gyorsítótár kulcsa
        data_loader_func (callable): Az adatbetöltő függvény, amelyet meg kell hívni, ha az adat nincs a gyorsítótárban
        cache_ttl (int, optional): A gyorsítótár élettartama másodpercben. Alapértelmezett: 300 (5 perc)
        
    Returns:
        tuple: (success, result) Az adatbetöltés eredménye
    """
    # Globális gyorsítótár inicializálása, ha még nem létezik
    if "api_lazy_cache" not in st.session_state:
        st.session_state.api_lazy_cache = {}
    
    # Gyorsítótárból való kiolvasás ellenőrzése
    if cache_key in st.session_state.api_lazy_cache:
        cached_data, timestamp = st.session_state.api_lazy_cache[cache_key]
        # Ha az adat még érvényes, visszaadjuk
        if time.time() - timestamp < cache_ttl:
            logger.debug(f"Cache hit for key: {cache_key}")
            return cached_data
    
    # Ha az adat nincs a gyorsítótárban vagy lejárt, újra betöltjük
    logger.debug(f"Cache miss for key: {cache_key}, loading from source")
    result = data_loader_func()
    
    # Az eredményt a gyorsítótárba helyezzük
    st.session_state.api_lazy_cache[cache_key] = (result, time.time())
    
    return result

def get_paginated_data(api_func, params, page_size=None, mobile_page_size=None, tablet_page_size=None):
    """
    Eszközmérethez igazított lapozási segédfüggvény API hívásokhoz.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        page_size (int, optional): Alapértelmezett lapozási méret. Defaults to None.
        mobile_page_size (int, optional): Mobil eszközökön használt lapozási méret. Defaults to None.
        tablet_page_size (int, optional): Tablet eszközökön használt lapozási méret. Defaults to None.
        
    Returns:
        tuple: (success, data, total_items)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Eszközmérethez igazított lapozási méret
    if is_mobile and mobile_page_size is not None:
        effective_page_size = mobile_page_size
    elif is_tablet and tablet_page_size is not None:
        effective_page_size = tablet_page_size
    elif page_size is not None:
        effective_page_size = page_size
    else:
        # Alapértelmezett értékek eszköz szerint
        effective_page_size = 5 if is_mobile else (10 if is_tablet else 20)
    
    # Aktuális oldal lekérése a session state-ből
    current_page = st.session_state.get("current_page", 1)
    
    # Lapozási paraméterek hozzáadása
    pagination_params = {
        "offset": (current_page - 1) * effective_page_size,
        "limit": effective_page_size
    }
    
    # Paraméterek összeolvasztása
    merged_params = {**params, **pagination_params} if params else pagination_params
    
    # API hívás
    try:
        success, result = api_func(merged_params)
        
        if success:
            # Ellenőrizzük, hogy a válasz tartalmaz-e meta információkat a teljes elemszámról
            if isinstance(result, dict) and "meta" in result and "total" in result["meta"]:
                total_items = result["meta"]["total"]
                data = result.get("data", [])
                return success, data, total_items
            
            # Ha a válasz csak egy lista, akkor a teljes elemszámot nem ismerjük
            if isinstance(result, list):
                return success, result, len(result)
            
            # Egyéb esetekben próbáljunk a result-ból adatokat kinyerni
            if isinstance(result, dict):
                data = result.get("data", result)
                total = result.get("total", len(data) if isinstance(data, list) else 0)
                return success, data, total
            
            # Visszatérünk az eredeti eredménnyel
            return success, result, 0
            
        else:
            logger.error(f"API hívás sikertelen: {result}")
            return False, result, 0
            
    except Exception as e:
        logger.error(f"Hiba az API hívás során: {str(e)}")
        return False, str(e), 0

def get_important_keys(is_mobile=False, is_tablet=False):
    """
    Fontosabb mezők listája az eszköz mérete alapján.
    
    Args:
        is_mobile (bool, optional): Mobil eszköz esetén. Defaults to False.
        is_tablet (bool, optional): Tablet eszköz esetén. Defaults to False.
        
    Returns:
        list: Fontosabb mezők listája
    """
    # Minimális mezők mobilon
    if is_mobile:
        return [
            "id", "name", "title", "status", "created_at", "quantity", "quantity_in_kg", 
            "price", "confirmed_price", "delivery_date", "product_type_id", "user_id"
        ]
    
    # Bővebb mezők tableten
    if is_tablet:
        return [
            "id", "name", "title", "description", "status", "created_at", "updated_at",
            "quantity", "quantity_in_kg", "price", "confirmed_price", "delivery_date", 
            "product_type_id", "user_id", "note", "tags"
        ]
    
    # Minden mező asztali eszközökön
    return None  # None esetén nem szűrünk mezőket

def get_responsive_data(api_func, params, simplify_for_mobile=True):
    """
    Reszponzív adatlekérés, amely eszközmérethez igazított adatszerkezetet ad vissza.
    Mobil eszközökön egyszerűsíti az adatszerkezetet a gyorsabb betöltés érdekében.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        simplify_for_mobile (bool, optional): Mobilon egyszerűsítse-e az adatszerkezetet. Defaults to True.
        
    Returns:
        tuple: (success, data)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # API hívás
    try:
        success, result = api_func(params)
        
        if not success:
            return False, result
        
        # Mobil esetén egyszerűsítés, ha a flag engedélyezi
        if (is_mobile or is_tablet) and simplify_for_mobile:
            # Adatszerkezet egyszerűsítése eszköz szerint
            if isinstance(result, list):
                simplified_result = []
                for item in result:
                    if isinstance(item, dict):
                        # Csak a fontosabb mezőket tartjuk meg
                        simplified_item = {}
                        
                        # Kulcsok kiválasztása az eszköz alapján
                        important_keys = get_important_keys(is_mobile, is_tablet)
                        
                        # Fontos mezők megőrzése
                        for key in important_keys:
                            if key in item:
                                simplified_item[key] = item[key]
                        
                        # Beágyazott objektumok egyszerűsítése
                        for key, value in item.items():
                            if isinstance(value, dict) and not key in simplified_item:
                                # A beágyazott objektumból csak az id és name mezőket tartjuk meg
                                if "id" in value:
                                    simplified_item[f"{key}_id"] = value["id"]
                                if "name" in value:
                                    simplified_item[f"{key}_name"] = value["name"]
                        
                        simplified_result.append(simplified_item)
                    else:
                        simplified_result.append(item)
                
                return True, simplified_result
                
            elif isinstance(result, dict):
                simplified_result = {}
                
                # Ha van data kulcs, akkor azon alkalmazzuk az egyszerűsítést
                if "data" in result and isinstance(result["data"], list):
                    simplified_result["data"] = get_responsive_data(lambda _: (True, result["data"]), None, True)[1]
                    
                    # Meta adatok megtartása
                    if "meta" in result:
                        simplified_result["meta"] = result["meta"]
                        
                    return True, simplified_result
                
                # Fontos mezők megőrzése
                important_keys = get_important_keys(is_mobile, is_tablet)
                for key in important_keys:
                    if key in result:
                        simplified_result[key] = result[key]
                
                return True, simplified_result
        
        # Ha nincs egyszerűsítés, vagy nem mobil/tablet, visszaadjuk az eredeti adatokat
        return True, result
    
    except Exception as e:
        logger.error(f"Hiba a reszponzív adatlekérés során: {str(e)}")
        return False, str(e)

def fetch_data_with_progress(api_func, operation_name, params=None, with_spinner=True):
    """
    Adatok betöltése folyamatjelzővel.
    
    Ez a függvény egy Streamlit folyamatjelzőt jelenít meg, miközben
    betölti az adatokat az API-ból, majd visszaadja az eredményt.
    
    Args:
        api_func (callable): Az API funkció, amelyet meg kell hívni
        operation_name (str): A művelet neve, amit a felhasználónak és a hibaüzenetekben megjelenítünk
        params (dict, optional): Az API funkciónak átadandó paraméterek. Defaults to None.
        with_spinner (bool, optional): Használjon-e spinner jelzőt. Defaults to True.
        
    Returns:
        tuple: (success, result) ahol success egy boolean értéket és result az API hívás eredményét vagy a hibaüzenetet tartalmazza.
    """
    # Képernyőméret információk lekérése (ha reszponzív működés kell)
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Kompakt megjelenítés kisebb képernyőkön
    if is_mobile or is_tablet:
        if with_spinner:
            with st.spinner(f"{operation_name.capitalize()} folyamatban..."):
                return safe_api_call(api_func, operation_name, params)
        else:
            return safe_api_call(api_func, operation_name, params)
    
    # Részletesebb folyamatjelző nagyobb képernyőkön
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Folyamatjelző inicializálása
        status_text.text(f"{operation_name.capitalize()} folyamatban...")
        progress_bar.progress(30)
        
        # API hívás
        success, result = safe_api_call(api_func, operation_name, params)
        
        # Folyamatjelző frissítése
        progress_bar.progress(100)
        if success:
            status_text.text(f"{operation_name.capitalize()} sikeres!")
        else:
            status_text.text(f"{operation_name.capitalize()} sikertelen: {result}")
        
        # Kis késleltetés, hogy a felhasználó láthassa az eredményt
        time.sleep(0.5)
        
        # Folyamatjelző és státusz törlése
        progress_bar.empty()
        status_text.empty()
        
        return success, result
        
    except Exception as e:
        # Hiba esetén töröljük a folyamatjelzőt
        progress_bar.empty()
        status_text.empty()
        
        logger.error(f"Hiba a {operation_name} során: {str(e)}")
        return False, str(e)