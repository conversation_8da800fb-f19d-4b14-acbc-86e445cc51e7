FROM python:3.9-slim

WORKDIR /app

# Telepítjük a szükséges csomagokat
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Másoljuk a teljes alkalmazást
COPY . .

# Portot nyitunk a Streamlit számára
EXPOSE 8501

# Környezeti változók beállítása
ENV PYTHONPATH=/app
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0

# Indítjuk a demó alkalmazást
CMD ["streamlit", "run", "pages/operator/simplified_offer_management/demo_filter_panel.py"]