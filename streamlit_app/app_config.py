"""
Konfigur<PERSON><PERSON><PERSON> beállítások a Streamlit alkalmazáshoz.
"""
import os
from typing import Dict, Any

# API beállítások
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")

# API kliens konfiguráció
API_CLIENT_CONFIG = {
    "timeout": 10,  # Időkorlát másodpercben
    "retry_count": 3,  # Újrapróbálkozások száma
    "retry_backoff_factor": 0.5,  # Exponenciális visszalépés faktor
    "retry_status_forcelist": [408, 429, 500, 502, 503, 504],  # Újrapróbálkoz<PERSON> ezeken a státuszkódokon
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE"]  # Mely HTTP metódusoknál próbáljon újra
}

# Debug mód beállítása
DEBUG_MODE = os.getenv("DEBUG_MODE", "false").lower() in ("true", "1", "yes")

# Debug: Kiírjuk a konfiguráció betöltését
print(f"Config loaded: API_BASE_URL={API_BASE_URL}")

# Alkalmazás beállítások
APP_NAME = os.getenv("APP_NAME", "POM APP")
COMPANY_NAME = os.getenv("COMPANY_NAME", "Zöldség Világ Kft.")

# Felhasználói szerepkörök
USER_ROLES = {
    "termelő": "Termelő",
    "ügyintéző": "Ügyintéző",
    "admin": "Adminisztrátor"
}

# Ajánlat státuszok
OFFER_STATUSES: Dict[str, Dict[str, Any]] = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",  # narancs
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    "CONFIRMED_BY_COMPANY": {
        "name": "Cég által visszaigazolva",
        "color": "#4682B4",  # acélkék
        "description": "A cég visszaigazolta az ajánlatot, várja a termelő jóváhagyását."
    },
    "ACCEPTED_BY_USER": {
        "name": "Termelő által elfogadva",
        "color": "#2E8B57",  # tengerzöld
        "description": "A termelő elfogadta a cég visszaigazolását."
    },
    "REJECTED_BY_USER": {
        "name": "Termelő által elutasítva",
        "color": "#CD5C5C",  # indián vörös
        "description": "A termelő elutasította a cég visszaigazolását."
    },
    "FINALIZED": {
        "name": "Véglegesítve",
        "color": "#228B22",  # erdőzöld
        "description": "Az ajánlat véglegesítésre került, a beszállítás tervezhető."
    }
}

# Mértékegységek
UNITS = {
    "kg": "kg",
    "tonna": "tonna",
    "db": "db"
}

# Munkamenet beállítások
SESSION_VARS = {
    "user": "user",
    "token": "auth_token",  # Changed to match utils/session.py
    "authenticated": "authenticated",
}
