# TOVÁBB Demo oldal
"""
Többlépéses űrlap és "TOVÁBB" gomb bemutató oldal.
"""
import streamlit as st
from components.multi_step_form import render_multi_step_form
from utils.page_utils import set_page_config
from utils.validators import validate_required, validate_phone, validate_length

# Oldal beállítások
set_page_config("TOVÁBB Demo", "➡️")

# Oldal tartalma
st.title("Többlépéses űrlap és TOVÁBB gomb Demo")

st.markdown("""
Ez az oldal bemutatja a többlépéses űrlap és a "TOVÁBB" gomb használatát.

A példa egy egyszerű regisztrációs űrlapot mutat be, amely három lépésből áll:
1. Sze<PERSON>lyes adatok
2. Kapcsolati adatok
3. <PERSON><PERSON><PERSON><PERSON> beállítása

Az űrlap menthető és később folytatható a "Mentett űrlapok" oldalon.
""")

# Mentett űrlap betöltése, ha van
if "form_data" in st.session_state and st.session_state.form_data:
    st.info("Egy mentett űrlap betöltve. Folytathatja a kitöltést.")
    
    # Mentett űrlap törlése gomb
    if st.button("Mentett űrlap törlése"):
        st.session_state.form_data = {}
        st.session_state.current_step = 0
        st.rerun()

# Lépés függvények definiálása
def step_personal_data(form_data):
    """Személyes adatok lépés"""
    st.subheader("1. Személyes adatok")
    
    # Form mezők
    first_name = st.text_input("Keresztnév", value=form_data.get("first_name", ""))
    last_name = st.text_input("Vezetéknév", value=form_data.get("last_name", ""))
    birth_date = st.date_input("Születési dátum", value=form_data.get("birth_date", None))
    
    # Adatok visszaadása
    return {
        "first_name": first_name,
        "last_name": last_name,
        "birth_date": birth_date
    }

def step_contact_data(form_data):
    """Kapcsolati adatok lépés"""
    st.subheader("2. Kapcsolati adatok")
    
    # Form mezők
    email = st.text_input("E-mail cím", value=form_data.get("email", ""))
    phone = st.text_input("Telefonszám", value=form_data.get("phone", ""))
    address = st.text_area("Cím", value=form_data.get("address", ""))
    
    # Adatok visszaadása
    return {
        "email": email,
        "phone": phone,
        "address": address
    }

def step_password(form_data):
    """Jelszó beállítása lépés"""
    st.subheader("3. Jelszó beállítása")
    
    # Form mezők
    password = st.text_input("Jelszó", type="password", value=form_data.get("password", ""))
    confirm_password = st.text_input("Jelszó megerősítése", type="password", value=form_data.get("confirm_password", ""))
    
    # Adatok visszaadása
    return {
        "password": password,
        "confirm_password": confirm_password
    }

# Validációs függvények definiálása
def validate_personal_data(form_data):
    """Személyes adatok validálása"""
    errors = []
    
    # Keresztnév ellenőrzése
    is_valid, error = validate_required(form_data.get("first_name"), "Keresztnév")
    if not is_valid:
        errors.append(error)
    
    # Vezetéknév ellenőrzése
    is_valid, error = validate_required(form_data.get("last_name"), "Vezetéknév")
    if not is_valid:
        errors.append(error)
    
    # Születési dátum ellenőrzése
    if not form_data.get("birth_date"):
        errors.append("Születési dátum megadása kötelező.")
    
    return len(errors) == 0, errors

def validate_contact_data(form_data):
    """Kapcsolati adatok validálása"""
    errors = []
    
    # E-mail ellenőrzése
    is_valid, error = validate_required(form_data.get("email"), "E-mail cím")
    if not is_valid:
        errors.append(error)
    elif "@" not in form_data.get("email", ""):
        errors.append("Érvénytelen e-mail cím formátum.")
    
    # Telefonszám ellenőrzése
    if form_data.get("phone"):
        is_valid, error = validate_phone(form_data.get("phone"))
        if not is_valid:
            errors.append(error)
    
    # Cím ellenőrzése
    is_valid, error = validate_required(form_data.get("address"), "Cím")
    if not is_valid:
        errors.append(error)
    
    return len(errors) == 0, errors

def validate_password_data(form_data):
    """Jelszó adatok validálása"""
    errors = []
    
    # Jelszó ellenőrzése
    is_valid, error = validate_required(form_data.get("password"), "Jelszó")
    if not is_valid:
        errors.append(error)
    else:
        # Jelszó hossz ellenőrzése
        is_valid, error = validate_length(form_data.get("password"), "Jelszó", min_length=8)
        if not is_valid:
            errors.append(error)
    
    # Jelszó megerősítés ellenőrzése
    is_valid, error = validate_required(form_data.get("confirm_password"), "Jelszó megerősítése")
    if not is_valid:
        errors.append(error)
    elif form_data.get("password") != form_data.get("confirm_password"):
        errors.append("A jelszavak nem egyeznek.")
    
    return len(errors) == 0, errors

# Lépések listája
steps = [
    step_personal_data,
    step_contact_data,
    step_password
]

# Validátorok listája
validators = [
    validate_personal_data,
    validate_contact_data,
    validate_password_data
]

# Mentés callback függvény
def on_save(form_id, form_data):
    """Mentés callback függvény"""
    st.info(f"Az űrlap mentve: {form_id}")
    
    # Mentett űrlap link
    st.markdown(f"[Mentett űrlapok kezelése](/saved_forms)")

# Többlépéses űrlap megjelenítése
current_step, form_data = render_multi_step_form(
    steps, 
    validators, 
    form_id="registration_form",
    on_save=on_save
)

# Űrlap adatok megjelenítése (csak fejlesztési célokra)
if st.checkbox("Űrlap adatok megjelenítése (fejlesztői mód)"):
    st.json(form_data) 