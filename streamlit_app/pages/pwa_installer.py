"""
PWA Telepítő oldal
"""
import streamlit as st
import os

# Set page config
st.set_page_config(
    page_title="POM APP Telepítő",
    page_icon="📱",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Read the HTML file
html_file_path = os.path.join(os.path.dirname(__file__), "..", "static", "pwa_installer.html")

try:
    with open(html_file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Display the HTML content
    st.components.v1.html(html_content, height=800, scrolling=True)
    
except FileNotFoundError:
    st.error("POM APP telepítő fájl nem található.")
    st.markdown("""
    ## 📱 POM APP Telepítő
    
    A POM APP alkalmazást telepítheti eszközére az alábbi lépések követésével:
    
    ### Android eszközökön:
    1. Nyissa meg a Chrome böngészőt
    2. Látogassa meg ezt az oldalt: http://dns72.com
    3. Nyomja meg a menü gombot (⋮)
    4. Válassza a "Hozzáadás a kezdőképernyőhöz" opciót
    
    ### iOS eszközökön:
    1. Nyissa meg a Safari böngészőt  
    2. Látogassa meg ezt az oldalt: http://dns72.com
    3. Nyomja meg a megosztás gombot (⬆️)
    4. Válassza a "Hozzáadás a kezdőképernyőhöz" opciót
    
    ### Direkt link:
    [🌐 POM APP Portál megnyitása](http://dns72.com)
    """)