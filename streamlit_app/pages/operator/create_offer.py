"""
Ügyintézői ajánlat létrehozása oldal (termelő nevében).
"""
import streamlit as st
import datetime
from api import products as products_api
from api import users as users_api
from api import offers as offers_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.validators import validate_required, validate_numeric, validate_date
import app_config as config

def show_create_offer():
    """
    Ügyintézői ajánlat létrehozása oldal megjelenítése.
    """
    st.title("Ajánlat létrehozása termelő nevében")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Fe<PERSON><PERSON>z<PERSON><PERSON><PERSON> adatainak lekérése
    user = get_current_user()
    
    # Ellen<PERSON>rizzük, hogy ügyintéző vagy admin
    if user.get("role") not in ["ügyintéző", "admin"]:
        show_error("Az oldal megtekintéséhez ügyintézői jogosultság szükséges.")
        return
    
    # Termelő kiválasztása
    st.subheader("Termelő kiválasztása")
    
    # Termelők lekérése
    success, producers_result = users_api.get_users(params={"role": "termelő"})
    
    if not success:
        show_error(f"Hiba a termelők lekérésekor: {producers_result}")
        return
    
    producers = producers_result
    
    # Ha vannak termelők, akkor termelő kiválasztása
    if producers:
        producer_options = [f"{p.get('id')} - {p.get('contact_name')} ({p.get('email')})" for p in producers]
        selected_producer = st.selectbox(
            "Válasszon termelőt",
            options=producer_options,
            format_func=lambda x: x
        )
        
        # Termelő azonosító kinyerése
        producer_id = int(selected_producer.split(" - ")[0])
        
        # Kiválasztott termelő
        selected_producer_data = next((p for p in producers if p.get("id") == producer_id), None)
        
        if selected_producer_data:
            # Termelő adatainak megjelenítése
            st.info(f"""
            **Kiválasztott termelő adatai:**
            - Név: {selected_producer_data.get('contact_name', '')}
            - E-mail: {selected_producer_data.get('email', '')}
            - Telefonszám: {selected_producer_data.get('phone_number', '')}
            - Cégnév: {selected_producer_data.get('company_name', 'Nincs')}
            """)
            
            # Termékadatok betöltése
            categories, product_types, _ = load_product_data()
            
            # Kategória választó
            if categories:
                selected_category = st.selectbox(
                    "Termékkategória *",
                    options=[cat["name"] for cat in categories],
                    key="category_select"
                )
                
                # Terméktípusok frissítése a kiválasztott kategória alapján
                types, error = update_types_by_category(selected_category, categories)
                if error:
                    st.error(error)
                
                # Terméktípusok megjelenítése
                type_options = [typ["name"] for typ in types] if types else []
                
                print(f"==== PRODUCT TYPE OPTIONS ====")
                print(f"Type options count: {len(type_options)}")
                print(f"Type options: {type_options}")
                
                if not type_options and selected_category:
                    error_msg = f"Nem találhatók terméktípusok a(z) '{selected_category}' kategóriában."
                    print(f"Error: {error_msg}")
                    st.error(error_msg)
                    type_options = [""]
                
                selected_type_index = 0
                if type_options and "type_select" in st.session_state and st.session_state.type_select in type_options:
                    selected_type_index = type_options.index(st.session_state.type_select)
                    print(f"Selected type index: {selected_type_index}")
                
                print(f"Final type options: {type_options}")
                print("============================")
                
                selected_type = st.selectbox(
                    "Terméktípus *",
                    options=type_options,
                    index=selected_type_index,
                    key="type_select"
                )
                
                # Minőségi besorolások lekérése csak akkor, ha van kiválasztott típus
                quality_grades = []
                if selected_type and types:
                    selected_type_id = types[type_options.index(selected_type)].get("id")
                    success, grades_result = products_api.get_quality_grades(selected_type_id)
                    if success:
                        quality_grades = grades_result
                    else:
                        st.error(f"Hiba a minőségi besorolások betöltésekor: {grades_result}")
            else:
                st.error("Nem sikerült betölteni a termékkategóriákat.")
            
            # Ajánlat létrehozása űrlap
            with st.form("create_offer_form"):
                st.subheader("Ajánlat adatai")
                
                # A holnapi dátum alapértelmezetten
                tomorrow = datetime.date.today() + datetime.timedelta(days=1)
                
                # Dátum választó
                delivery_date = st.date_input(
                    "Beszállítás tervezett dátuma *",
                    value=tomorrow,
                    min_value=tomorrow,
                    max_value=tomorrow + datetime.timedelta(days=30),
                    help="Válassza ki a tervezett beszállítás dátumát (legfeljebb 30 nap)"
                )
                
                # Minőségi besorolás választó (ha releváns)
                selected_grade_id = None
                
                if quality_grades and selected_type:
                    filtered_grades = [g for g in quality_grades if g.get("product_type_id") == types[type_options.index(selected_type)].get("id")]
                    
                    if filtered_grades:
                        selected_grade = st.selectbox(
                            "Minőségi besorolás *",
                            options=[g.get("name", "") for g in filtered_grades],
                            key="grade_select"
                        )
                        
                        for g in filtered_grades:
                            if g.get("name") == selected_grade:
                                selected_grade_id = g.get("id")
                                break
                    
                    # Mértékegység választó
                    unit = st.radio(
                        "Mértékegység",
                        options=list(config.UNITS.keys()),
                        horizontal=True
                    )
                    
                    # Mennyiség beviteli mező
                    quantity = st.number_input(
                        f"Mennyiség ({unit}) *",
                        min_value=0.01 if unit == "kg" else 0.001,
                        step=0.1 if unit == "kg" else 0.01,
                        format="%.2f"
                    )
                    
                    # Mennyiség konvertálása kg-ra
                    quantity_in_kg = quantity
                    if unit == "tonna":
                        quantity_in_kg = quantity * 1000
                    
                    # Egyidejű visszaigazolás
                    st.subheader("Egyidejű visszaigazolás (opcionális)")
                    confirm_immediately = st.checkbox(
                        "Egyidejűleg visszaigazolom az ajánlatot",
                        help="Ha bejelöli, akkor az ajánlat létrehozása után azonnal visszaigazolásra kerül"
                    )
                    
                    if confirm_immediately:
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            # Visszaigazolt ár
                            confirmed_price = st.number_input(
                                "Visszaigazolt ár (Ft/kg)",
                                min_value=0.0,
                                value=1200.0,  # Példa érték
                                step=10.0,
                                format="%.2f"
                            )
                        
                        with col2:
                            # Visszaigazolt mennyiség
                            confirmed_quantity = st.number_input(
                                "Visszaigazolt mennyiség (kg)",
                                min_value=0.0,
                                max_value=float(quantity_in_kg),
                                value=float(quantity_in_kg),
                                step=10.0,
                                format="%.2f"
                            )
                    
                    # Megjegyzés
                    note = st.text_area(
                        "Megjegyzés (opcionális)",
                        placeholder="Ide írhatja az ajánlattal kapcsolatos megjegyzéseit...",
                        max_chars=500
                    )
                else:
                    st.warning("Nincsenek terméktípusok a kiválasztott kategóriához.")

                # Mentés gomb - mindig megjelenítjük, függetlenül attól, hogy vannak-e terméktípusok
                submit = st.form_submit_button("Ajánlat létrehozása", type="primary", use_container_width=True)

            # Form submission handling
            if submit:
                # Csak akkor dolgozzuk fel az adatokat, ha vannak terméktípusok
                if not types:
                    show_error("Nincsenek terméktípusok a kiválasztott kategóriához. Kérjük, válasszon másik kategóriát vagy hozzon létre terméktípusokat ehhez a kategóriához.")
                else:
                    # Adatok validálása
                    validation_errors = []
                    
                    # Dátum ellenőrzése
                    delivery_date_str = delivery_date.strftime("%Y-%m-%d") if delivery_date else None
                    today = datetime.date.today()
                    min_date = today + datetime.timedelta(days=1)
                    max_date = today + datetime.timedelta(days=30)
                    is_valid, error = validate_date(delivery_date, "Beszállítás dátuma", min_date=min_date, max_date=max_date)
                    if not is_valid:
                        validation_errors.append(error)
                    
                    # Kategória és típus ellenőrzése
                    if not selected_category:
                        validation_errors.append("Termékkategória kiválasztása kötelező.")
                    
                    if not selected_type:
                        validation_errors.append("Terméktípus kiválasztása kötelező.")
                    
                    # Minőségi besorolás ellenőrzése, ha van
                    if not selected_grade_id:
                        validation_errors.append("Minőségi besorolás kiválasztása kötelező.")
                    
                    # Mennyiség ellenőrzése
                    is_valid, error = validate_numeric(quantity_in_kg, "Mennyiség", min_value=0.01)
                    if not is_valid:
                        validation_errors.append(error)
                    
                    # Visszaigazolási adatok ellenőrzése, ha egyidejű visszaigazolás
                    if confirm_immediately:
                        is_valid, error = validate_numeric(confirmed_price, "Visszaigazolt ár", min_value=0.01)
                        if not is_valid:
                            validation_errors.append(error)
                        
                        is_valid, error = validate_numeric(confirmed_quantity, "Visszaigazolt mennyiség", min_value=0.01)
                        if not is_valid:
                            validation_errors.append(error)
                    
                    # Ha van validációs hiba, megjelenítjük
                    if validation_errors:
                        for error in validation_errors:
                            show_error(error)
                    else:
                        # Ajánlat adatainak összeállítása
                        offer_data = {
                            "product_type_id": types[type_options.index(selected_type)].get("id"),
                            "quality_grade_id": selected_grade_id if selected_grade_id else None,
                            "quantity_in_kg": quantity_in_kg,
                            "delivery_date": delivery_date_str,
                            "note": note if note else None
                        }
                        
                        # Ajánlat létrehozása API hívással, a kiválasztott termelő nevében
                        success, result = offers_api.create_offer(offer_data, on_behalf_of_user_id=producer_id)
                        
                        if success:
                            created_offer = result
                            created_offer_id = created_offer.get("id")
                            
                            show_success("Ajánlat sikeresen létrehozva a termelő nevében!")
                            
                            # Ha egyidejű visszaigazolás is kell
                            if confirm_immediately and created_offer_id:
                                # Visszaigazolási adatok összeállítása
                                confirmation_data = {
                                    "confirmed_quantity": confirmed_quantity,
                                    "confirmed_price": confirmed_price
                                }
                                
                                # Ajánlat visszaigazolása API hívással
                                confirm_success, confirm_result = offers_api.confirm_offer(created_offer_id, confirmation_data)
                                
                                if confirm_success:
                                    show_success("Ajánlat sikeresen visszaigazolva!")
                                else:
                                    show_error(f"Hiba az ajánlat visszaigazolásakor: {confirm_result}")
                            
                            # Felajánljuk a lehetőséget egy újabb ajánlat létrehozására
                            st.success(f"Szeretne új ajánlatot létrehozni {selected_producer_data.get('contact_name')} nevében?")
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                if st.button("Új ajánlat létrehozása", type="primary", use_container_width=True):
                                    st.rerun()
                            
                            with col2:
                                if st.button("Ajánlatok kezelése", use_container_width=True):
                                    st.switch_page("pages/operator_offers.py")
                        else:
                            show_error(f"Hiba az ajánlat létrehozásakor: {result}")
    else:
        st.warning("Nincsenek termelők a rendszerben. Először regisztráljon legalább egy termelőt.")

def load_product_data():
    """
    Termékadatok betöltése a backenddől.
    
    Returns:
        tuple: (categories, product_types, quality_grades)
    """
    print(f"==== LOAD PRODUCT DATA ====")
    
    # Termékkategóriák lekérése
    success, categories_result = products_api.get_product_categories()
    if not success:
        error_msg = f"Hiba a termékkategóriák betöltésekor: {categories_result}"
        print(f"Error: {error_msg}")
        st.error(error_msg)
        return [], [], []
    categories = categories_result
    print(f"Categories loaded: {len(categories)}")
    
    # Terméktípusok lekérése - egyelőre kategória nélkül
    success, types_result = products_api.get_product_types()
    if not success:
        error_msg = f"Hiba a terméktípusok betöltésekor: {types_result}"
        print(f"Error: {error_msg}")
        st.error(error_msg)
        return categories, [], []
    product_types = types_result
    print(f"Product types loaded: {len(product_types)}")
    
    # Minőségi besorolások nem kerülnek előre betöltésre, csak később, amikor már van kiválasztott terméktípus
    print("Quality grades will be loaded when needed")
    
    print("============================")
    return categories, product_types, []

def update_types_by_category(selected_category, categories):
    """
    Terméktípusok frissítése a kiválasztott kategória alapján
    
    Args:
        selected_category (str): Kiválasztott kategória neve
        categories (list): Kategóriák listája
        
    Returns:
        tuple: (list, str) - Terméktípusok listája és hibaüzenet (ha van)
    """
    print(f"==== UPDATE TYPES BY CATEGORY ====")
    print(f"Selected category: {selected_category}")
    print(f"Categories count: {len(categories)}")
    
    if not selected_category:
        print("No category selected")
        return [], None
    
    category = next((cat for cat in categories if cat["name"] == selected_category), None)
    print(f"Found category: {category}")
    
    if not category:
        print("Category not found")
        return [], None
    
    st.session_state.selected_category_id = category["id"]
    print(f"Set selected_category_id: {category['id']}")
    
    success, types = products_api.get_product_types(category_id=category["id"])
    print(f"API call result - Success: {success}, Types count: {len(types) if success else 0}")
    
    if success:
        st.session_state.product_types = types
        return types, None
    else:
        error_msg = f"Hiba a típusok betöltésekor: {types}"
        print(f"Error: {error_msg}")
        return [], error_msg

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Ajánlat létrehozása termelő nevében - {config.APP_NAME}",
        page_icon="📦",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük az ajánlat létrehozása oldalt
    show_create_offer()
