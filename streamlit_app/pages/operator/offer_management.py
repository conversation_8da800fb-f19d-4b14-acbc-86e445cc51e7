#!/usr/bin/env python3
"""
Refaktorált Ajánlatok kezelése - Modular Architecture Implementation
Step 1: Filter System Fix and Modular Architecture Implementation

A dokumentáció-alapú megközelítéssel átstrukturált moduláris architektúra,
ami tiszta szeparációt biztosít az UI, az üzleti logika és az adatelérési rétegek között.
"""
import streamlit as st
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any

# Core imports
from utils.session import is_authenticated, get_current_user
from components.sidebar import render_sidebar

# New modular architecture imports
from pages.operator.offer_management.integrated_state import OfferStateManager
from pages.operator.offer_management.data_coordinator import DataCoordinator
from pages.operator.offer_management.enhanced_filter_panel import EnhancedFilterPanel
from pages.operator.offer_management.filter_diagnostic_tool import FilterDiagnosticTool

# Fallback imports for display components
try:
    from pages.operator.offer_management.modern_ui_simple import (
        inject_modern_styles,
        render_page_header,
        render_active_filters,
        render_statistics_card,
        render_offers_display
    )
    HAS_MODERN_UI = True
except ImportError:
    logger.warning("Could not import modern UI components, using fallbacks")
    HAS_MODERN_UI = False

logger = logging.getLogger(__name__)

def toggle_debug_mode():
    """Toggle debug mode on/off"""
    current_state = st.session_state.get('debug_mode', False)
    st.session_state.debug_mode = not current_state
    
    # Log the change
    logger.info(f"Debug mode toggled: {st.session_state.debug_mode}")
    
    # Clear diagnostic caches if turning off debug
    if not st.session_state.debug_mode:
        # Clear any diagnostic related session state
        keys_to_clear = [k for k in st.session_state.keys() 
                        if 'diagnostic' in k or 'debug' in k and k != 'debug_mode']
        for key in keys_to_clear:
            del st.session_state[key]
        logger.info(f"Debug mode disabled, cleared {len(keys_to_clear)} diagnostic keys")

def initialize_modular_architecture():
    """Initialize the new modular architecture with session state singleton pattern"""
    
    # Use session state for singleton pattern instead of global variables
    if 'modular_architecture_initialized' not in st.session_state:
        st.session_state.modular_architecture_initialized = True
        st.session_state.state_manager = OfferStateManager()
        st.session_state.data_coordinator = DataCoordinator(st.session_state.state_manager)
        st.session_state.filter_panel = EnhancedFilterPanel(st.session_state.state_manager.filter_manager)
        
        # ÚJ: Diagnostic Tool inicializálása
        st.session_state.diagnostic_tool = FilterDiagnosticTool(
            state_manager=st.session_state.state_manager,
            data_coordinator=st.session_state.data_coordinator
        )
        
        logger.info("Modular architecture with diagnostic tool initialized")
    else:
        logger.debug("Modular architecture already initialized, reusing existing instances")
    
    return (
        st.session_state.state_manager,
        st.session_state.data_coordinator, 
        st.session_state.filter_panel
    )

def detect_device_type():
    """Egyszerű eszköz típus meghatározás"""
    return st.session_state.get('device_type', 'desktop')

def render_fallback_ui():
    """Fallback UI when modern components are not available"""
    st.title("🪙 Ajánlatok Kezelése")
    st.info("Modern UI komponensek nem érhetők el, alapértelmezett megjelenítés használatban.")
    
    # Basic filter form
    with st.form("basic_filters"):
        col1, col2 = st.columns(2)
        
        with col1:
            search_term = st.text_input("Keresés:")
            
        with col2:
            submitted = st.form_submit_button("🔍 Keresés")
        
        if submitted:
            st.info(f"Keresés: {search_term}")
    
    st.info("Az új moduláris architektúra aktív, de a megjelenítő komponensek hiányoznak.")

def render_producer_filter_status():
    """
    Real-time producer filter status display
    """
    if 'producer_filter_override' in st.session_state:
        override = st.session_state.producer_filter_override
        
        if override.get('enabled', False):
            st.info(f"🔧 **Producer szűrő optimalizálva**: {override.get('source', 'unknown')}")
            
            if st.button("❌ Override törlése", key="clear_producer_override"):
                del st.session_state.producer_filter_override
                st.rerun()
    
    # Show current filter effectiveness
    if 'data_coordinator' in st.session_state:
        last_results = st.session_state.data_coordinator.get_filter_traces()
        if last_results:
            # Get the latest trace result
            latest_trace = last_results[-1] if last_results else None
            
            if latest_trace and latest_trace.get('validation_result'):
                validation = latest_trace['validation_result']
                
                # Check for producer filter issues
                if validation.get('critical_issues'):
                    st.error(f"⚠️ **Producer szűrő probléma**: {len(validation['critical_issues'])} kritikus hiba")
                    
                    if st.button("🔧 Automatikus javítás", key="auto_fix_producer"):
                        run_producer_filter_optimization()
                
                elif 'producer_id' in validation.get('filter_matches', {}):
                    producer_match = validation['filter_matches']['producer_id']
                    match_rate = producer_match.get('match_rate', 0)
                    
                    if match_rate < 0.5:
                        st.warning(f"⚠️ Producer szűrő hatékonyság alacsony: {match_rate:.1%}")
                        if st.button("🔧 Optimalizálás", key="optimize_producer"):
                            run_producer_filter_optimization()

def run_producer_filter_optimization():
    """Producer szűrő optimalizálás futtatása"""
    
    if 'diagnostic_tool' not in st.session_state:
        st.error("Diagnosztikai eszköz nem érhető el")
        return
    
    # Get current filters
    if 'filter_panel' in st.session_state:
        ui_filters = st.session_state.filter_panel.get_current_filters()
        
        if 'producer_filter' in ui_filters and ui_filters['producer_filter']:
            producer_data = ui_filters['producer_filter']
            
            if isinstance(producer_data, tuple) and len(producer_data) == 2:
                name, producer_id = producer_data
                
                with st.spinner("Producer szűrő optimalizálása..."):
                    try:
                        # Get recommendation from diagnostic tool
                        recommendation = st.session_state.diagnostic_tool.recommend_producer_parameters(int(producer_id))
                        
                        if recommendation.get('validated') and recommendation.get('expected_results', 0) > 0:
                            # Store override in session state
                            st.session_state.producer_filter_override = {
                                'enabled': True,
                                'source': 'auto_optimization',
                                'recommended_params': recommendation['parameters'],
                                'expected_results': recommendation['expected_results'],
                                'confidence': recommendation['confidence'],
                                'timestamp': datetime.now().isoformat()
                            }
                            
                            st.success(f"✅ Producer szűrő optimalizálva! Várható: {recommendation['expected_results']} eredmény")
                            st.rerun()
                        else:
                            st.warning("⚠️ Nem találtunk jobb optimalizálási lehetőséget")
                            
                    except Exception as e:
                        st.error(f"Optimalizálás sikertelen: {str(e)}")
                        logger.error(f"Producer filter optimization failed: {e}")
            else:
                st.error("Érvénytelen producer szűrő formátum")
        else:
            st.warning("Nincs aktív producer szűrő az optimalizáláshoz")
    else:
        st.error("Filter panel nem érhető el")

def render_debug_ui():
    """Debug UI session state producer keys megjelenítése"""
    
    with st.expander("🔍 **DEBUG: Session State Producer Keys**", expanded=False):
        st.write("**🔍 Session State Producer Analysis**")
        
        producer_keys = [k for k in st.session_state.keys() if 'producer' in k.lower()]
        
        if producer_keys:
            st.write(f"**Found {len(producer_keys)} producer-related keys:**")
            
            for key in producer_keys:
                value = st.session_state[key]
                st.write(f"- **{key}**: `{value}` (type: `{type(value).__name__}`)")
        else:
            st.warning("⚠️ **No producer-related keys found in session state**")
        
        st.divider()
        
        # Filter Chain Test
        st.write("**🔗 Filter Chain Test**")
        
        if st.button("🧪 Test Filter Conversion Flow", key="test_filter_conversion"):
            with st.spinner("Testing filter conversion..."):
                try:
                    # Test filter conversion flow
                    if 'filter_panel' in st.session_state and 'data_coordinator' in st.session_state:
                        ui_filters = st.session_state.filter_panel.get_current_filters()
                        api_params = st.session_state.data_coordinator.convert_filters_to_api_params(ui_filters)
                        
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.write("**UI Filters:**")
                            st.json(ui_filters)
                        
                        with col2:
                            st.write("**API Params:**")
                            st.json(api_params)
                        
                        # CRITICAL CHECK - Backend user_id paramétert vár producer szűréshez
                        if 'producer_filter' in ui_filters and not api_params:
                            st.error("🚨 **FILTER CONVERSION FAILED!**")
                        elif 'producer_filter' in ui_filters and 'user_id' not in api_params:
                            st.error("🚨 **PRODUCER FILTER LOST IN CONVERSION! (Expected user_id parameter)**")
                        elif 'producer_filter' in ui_filters and 'user_id' in api_params:
                            st.success("✅ **Filter conversion successful! (producer_filter → user_id)**")
                            producer_data = ui_filters['producer_filter']
                            user_id = api_params['user_id']
                            if isinstance(producer_data, tuple) and len(producer_data) == 2:
                                name, ui_id = producer_data
                                if str(ui_id) == str(user_id):
                                    st.success(f"✅ **ID consistency check passed**: UI={ui_id} == API user_id={user_id}")
                                else:
                                    st.error(f"❌ **ID mismatch**: UI={ui_id} != API user_id={user_id}")
                        else:
                            st.info("ℹ️ No producer filter to test")
                            
                    else:
                        st.error("Filter panel or data coordinator not available")
                        
                except Exception as e:
                    st.error(f"Filter conversion test failed: {str(e)}")
                    logger.error(f"Filter conversion test error: {e}")
        
        st.divider()
        
        # Session State Overview
        st.write("**📊 Full Session State Overview**")
        
        if st.button("📋 Show All Session State Keys", key="show_all_keys"):
            all_keys = list(st.session_state.keys())
            st.write(f"**Total keys: {len(all_keys)}**")
            
            # Group by categories
            filter_keys = [k for k in all_keys if any(pattern in k.lower() for pattern in ['filter', 'producer', 'search', 'status', 'date'])]
            ui_keys = [k for k in all_keys if any(pattern in k.lower() for pattern in ['modern_', 'ui_', 'panel'])]
            system_keys = [k for k in all_keys if any(pattern in k.lower() for pattern in ['state_manager', 'data_coordinator', 'diagnostic'])]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**Filter/UI Keys:**")
                for key in filter_keys:
                    st.write(f"- {key}")
            
            with col2:
                st.write("**UI Component Keys:**")
                for key in ui_keys:
                    st.write(f"- {key}")
            
            with col3:
                st.write("**System Keys:**")
                for key in system_keys:
                    st.write(f"- {key}")
            
            # Show remaining keys
            remaining_keys = [k for k in all_keys if k not in filter_keys + ui_keys + system_keys]
            if remaining_keys:
                st.write("**Other Keys:**")
                for key in remaining_keys[:10]:  # Show first 10
                    st.write(f"- {key}")
                
                if len(remaining_keys) > 10:
                    st.write(f"... and {len(remaining_keys) - 10} more keys")

def show_architecture_debug():
    """Display architecture debug information"""
    
    with st.expander("🏗️ Modular Architecture Debug", expanded=False):
        st.subheader("Architecture Status")
        
        # Component status using session state
        components = {
            "OfferStateManager": 'state_manager' in st.session_state,
            "DataCoordinator": 'data_coordinator' in st.session_state, 
            "EnhancedFilterPanel": 'filter_panel' in st.session_state,
            "Modern UI Components": HAS_MODERN_UI,
            "Architecture Initialized": st.session_state.get('modular_architecture_initialized', False)
        }
        
        for component, status in components.items():
            status_icon = "✅" if status else "❌"
            st.write(f"{status_icon} {component}")
        
        # State summary
        if 'state_manager' in st.session_state:
            st.subheader("State Summary")
            state_summary = st.session_state.state_manager.get_state_summary()
            st.json(state_summary)
        
        # Performance metrics
        if 'state_manager' in st.session_state:
            st.subheader("Performance Metrics")
            metrics = st.session_state.state_manager.get_performance_metrics()
            st.json(metrics)
        
        # Export debug data
        if st.button("📥 Export Debug Data", key="export_debug"):
            if 'data_coordinator' in st.session_state:
                debug_data = st.session_state.data_coordinator.export_debug_data()
                st.download_button(
                    "💾 Download Debug Data",
                    data=str(debug_data),
                    file_name=f"offer_management_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

def show_diagnostic_panel():
    """Enhanced diagnostic panel megjelenítése - FIXED: No nested expanders"""
    
    if 'diagnostic_tool' not in st.session_state:
        st.warning("Diagnostic tool not initialized")
        return
        
    diagnostic_tool = st.session_state.diagnostic_tool
    
    # HEADER SECTION - No expander, just direct display
    st.subheader("🚨 **KRITIKUS SZŰRŐ DIAGNOSZTIKA**")
    
    # Control buttons - Enhanced with automatic producer testing
    col1, col2, col3, col4 = st.columns([1, 1, 1, 1])
    
    with col1:
        if st.button("🔍 **AZONNALI DIAGNÓZIS**", type="primary", use_container_width=True):
            st.session_state.show_immediate_diagnosis = True
            
    with col2:
        if st.button("📊 Részletes Elemzés", use_container_width=True):
            st.session_state.show_detailed_analysis = True
            
    with col3:
        if st.button("🔧 **AUTOMATIKUS PRODUCER TESZT**", type="secondary", use_container_width=True):
            st.session_state.show_automatic_producer_test = True
            
    with col4:
        if st.button("🧹 Tiszta Start", use_container_width=True):
            # Clear all diagnostic states
            for key in list(st.session_state.keys()):
                if key.startswith('show_'):
                    del st.session_state[key]
            st.rerun()
    
    # Live filter status - container based
    with st.container():
        st.write("**🔴 Jelenlegi Szűrő Állapot**")
        current_filters = get_current_filter_summary()
        
        if current_filters.get('has_producer_filter'):
            producer_info = current_filters['producer_info']
            
            # Critical status display
            status_color = "🔴" if current_filters.get('potential_issues') else "🟢"
            st.markdown(f"**{status_color} Producer Szűrő:** `{producer_info['name']}` (ID: `{producer_info['id']}`)")
            
            if current_filters.get('potential_issues'):
                for issue in current_filters['potential_issues']:
                    st.error(f"⚠️ {issue}")
    
    # Conditional display sections
    if st.session_state.get('show_immediate_diagnosis', False):
        run_immediate_diagnosis_fixed()
    
    if st.session_state.get('show_detailed_analysis', False):
        run_detailed_analysis_fixed()
    
    if st.session_state.get('show_automatic_producer_test', False):
        run_automatic_producer_test()
    
    # ÚJ: API Data Diagnostic Section
    render_api_data_diagnostic_section()

def get_current_filter_summary():
    """Jelenlegi szűrők összefoglalása"""
    
    summary = {
        'has_producer_filter': False,
        'producer_info': {},
        'potential_issues': [],
        'filter_count': 0
    }
    
    try:
        if 'modern_producer_filter' in st.session_state:
            producer_data = st.session_state.modern_producer_filter
            
            if producer_data and producer_data != ('', ''):
                summary['has_producer_filter'] = True
                
                if isinstance(producer_data, tuple) and len(producer_data) == 2:
                    name, producer_id = producer_data
                    summary['producer_info'] = {
                        'name': name,
                        'id': producer_id,
                        'type': 'tuple'
                    }
                    
                    # Check for issues
                    if not name or not producer_id:
                        summary['potential_issues'].append("Producer tuple contains empty values")
                else:
                    summary['potential_issues'].append(f"Producer filter has unexpected format: {type(producer_data)}")
        
        # Count other active filters
        active_filters = 0
        for key in st.session_state:
            if key.startswith('modern_') and st.session_state[key] not in [None, '', [], False]:
                active_filters += 1
                
        summary['filter_count'] = active_filters
        
    except Exception as e:
        summary['potential_issues'].append(f"Error reading filters: {str(e)}")
        
    return summary

def run_immediate_diagnosis_fixed():
    """Azonnali diagnózis futtatása - FIXED: No nested expanders"""
    
    diagnostic_tool = st.session_state.diagnostic_tool
    
    st.divider()  # Visual separator
    st.subheader("🔍 AZONNALI DIAGNÓZIS EREDMÉNYEI")
    
    with st.spinner("🔍 Szűrő lánc diagnosztizálása..."):
        try:
            # Get current state - javított hívás
            ui_filters = st.session_state.filter_panel.get_current_filters()
            
            # Convert to API params
            api_params = st.session_state.data_coordinator.convert_filters_to_api_params(ui_filters)
            
            # DEBUG output - CONTAINER alapú megjelenítés
            with st.container():
                st.write("**🔍 Debug Információk**")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**UI Filters:**")
                    st.json(ui_filters)
                    
                with col2:
                    st.write("**API Params:**")
                    st.json(api_params)
                
                # Producer debug - CRITICAL TRACE
                if 'producer_filter' in ui_filters:
                    producer_ui = ui_filters['producer_filter']
                    producer_api = api_params.get('producer_id', 'MISSING!')
                    
                    st.info(f"🎯 Producer: UI={producer_ui} → API producer_id={producer_api}")
                    
                    # CRITICAL: Check conversion consistency
                    if isinstance(producer_ui, tuple) and len(producer_ui) == 2:
                        name, expected_id = producer_ui
                        if str(expected_id) != str(producer_api):
                            st.error(f"🚨 **CONVERSION ERROR**: UI ID={expected_id} vs API ID={producer_api}")
                        else:
                            st.success(f"✅ Producer conversion OK: {expected_id}")
                    else:
                        st.warning(f"⚠️ Producer format issue: {type(producer_ui)} = {producer_ui}")
            
            # Get results and validate
            with st.container():
                st.write("**📊 API Call & Results**")
                
                results = st.session_state.data_coordinator.load_offers(ui_filters)
                st.info(f"📊 Results: {len(results)} ajánlat")
                
                # CRITICAL: Validate producer in results if we have a producer filter
                if 'producer_filter' in ui_filters and results:
                    validate_producer_results_fixed(ui_filters, api_params, results)
                
                # REAL-TIME STATUS CHECK
                if 'diagnostic_tool' in st.session_state:
                    real_time_status = st.session_state.diagnostic_tool.get_real_time_producer_status()
                    
                    st.write("**🔴 Real-time Producer Status:**")
                    
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        status_icon = {"PASS": "✅", "FAIL": "❌", "NO_DATA": "⚪", "ERROR": "🚨"}.get(real_time_status['consistency_check'], "❓")
                        st.metric("Status", f"{status_icon} {real_time_status['consistency_check']}")
                    
                    with col2:
                        if real_time_status['ui_producer_filter'] and isinstance(real_time_status['ui_producer_filter'], tuple):
                            ui_id = real_time_status['ui_producer_filter'][1]
                            st.metric("UI Producer ID", ui_id)
                        else:
                            st.metric("UI Producer ID", "N/A")
                    
                    with col3:
                        api_id = real_time_status.get('api_producer_id', 'N/A')
                        st.metric("API Producer ID", api_id)
                    
                    if real_time_status['errors']:
                        for error in real_time_status['errors']:
                            st.error(f"⚠️ {error}")
                    
                    if real_time_status['validation_metadata']:
                        if st.button("📋 Show Validation Metadata", key="show_validation_metadata"):
                            st.json(real_time_status['validation_metadata'])
            
            # Run comprehensive diagnosis
            diagnosis = diagnostic_tool.diagnose_filter_chain(ui_filters, api_params, results)
            
            # Display critical results - NO EXPANDERS
            display_critical_diagnosis_fixed(diagnosis)
            
        except Exception as e:
            st.error(f"Diagnózis hiba: {str(e)}")
            st.exception(e)  # Show full traceback
            logger.error(f"Diagnosis error: {e}", exc_info=True)

def validate_producer_results_fixed(ui_filters, api_params, results):
    """Producer eredmények validálása - helper function"""
    
    expected_producer_id = api_params.get('producer_id')
    
    if expected_producer_id:
        # Eredményekben található producer ID-k
        found_producer_ids = []
        found_producer_names = []
        
        for offer in results:
            # Try different possible field names for producer ID
            result_producer_id = (
                offer.get('producer_id') or 
                offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None or
                offer.get('user_id')
            )
            
            # Try different possible field names for producer name
            result_producer_name = (
                offer.get('producer_name') or 
                offer.get('user', {}).get('contact_name') if isinstance(offer.get('user'), dict) else None or
                offer.get('user', {}).get('company_name') if isinstance(offer.get('user'), dict) else None or
                offer.get('user_name')
            )
            
            if result_producer_id:
                found_producer_ids.append(result_producer_id)
            if result_producer_name:
                found_producer_names.append(result_producer_name)
        
        # Remove duplicates
        found_producer_ids = list(set(found_producer_ids))
        found_producer_names = list(set(found_producer_names))
        
        st.write("**🔍 Producer Validation:**")
        st.write(f"- Várt Producer ID: `{expected_producer_id}`")
        st.write(f"- Talált Producer ID-k: `{found_producer_ids}`")
        st.write(f"- Talált Producer nevek: `{found_producer_names}`")
        
        # Convert to strings for comparison to handle int/string mismatches
        expected_id_str = str(expected_producer_id)
        found_ids_str = [str(id) for id in found_producer_ids]
        
        if expected_id_str not in found_ids_str:
            st.error("🚨 **KRITIKUS HIBA**: A szűrő nem egyezik az eredményekkel!")
            
            # Show sample results for debugging
            if results:
                st.write("**📋 Sample Result Structure:**")
                sample = results[0]
                sample_keys = list(sample.keys())
                st.write(f"Available keys: {sample_keys}")
                
                # Show the user/producer related fields
                if 'user' in sample:
                    st.write(f"User object: {sample['user']}")
                    
            # API call raw debug
            st.write("**🔧 Debug API Call**")
            if st.button("🔍 Test API Call Directly", key="test_api_call"):
                debug_api_call_fixed(api_params)
        else:
            st.success("✅ Producer szűrő konzisztens az eredményekkel")

def debug_api_call_fixed(api_params):
    """API hívás közvetlen debuggolása"""
    
    try:
        # Direct API call import and test
        from pages.operator.offer_management.api_client import get_offers
        
        with st.spinner("API hívás tesztelése..."):
            success, raw_result = get_offers(api_params)
            
            st.write(f"**API Success:** {success}")
            
            if success:
                if isinstance(raw_result, list):
                    st.write(f"**API Result Count:** {len(raw_result)}")
                    if raw_result:
                        st.write("**First Result Sample:**")
                        st.json(raw_result[0])
                else:
                    st.write(f"**API Result Type:** {type(raw_result)}")
                    st.json(raw_result)
            else:
                st.error(f"**API Error:** {raw_result}")
                
    except Exception as e:
        st.error(f"API test hiba: {str(e)}")
        st.exception(e)

def display_critical_diagnosis_fixed(diagnosis):
    """Kritikus diagnózis eredmények megjelenítése - FIXED: No expanders"""
    
    st.divider()
    
    # Status header
    status = diagnosis['validation_status']
    status_icon = "🔴" if status == 'failed' else "🟢"
    issue_count = len(diagnosis['issues_found'])
    
    st.markdown(f"### {status_icon} Diagnózis Eredmény: **{status.upper()}** ({issue_count} hiba)")
    
    # Critical issues
    if diagnosis['issues_found']:
        with st.container():
            st.error("**TALÁLT HIBÁK:**")
            for i, issue in enumerate(diagnosis['issues_found'], 1):
                st.error(f"{i}. {issue}")
    
    # Producer analysis - KRITIKUS RÉSZ
    if 'producer_analysis' in diagnosis:
        with st.container():
            st.write("**🎯 Producer Szűrő Elemzés**")
            
            producer_analysis = diagnosis['producer_analysis']
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "UI Producer ID", 
                    producer_analysis.get('ui_producer_id', 'N/A'),
                    delta=None
                )
                
            with col2:
                st.metric(
                    "API Producer ID", 
                    producer_analysis.get('api_producer_id', 'N/A'),
                    delta=None
                )
                
            with col3:
                consistency = producer_analysis.get('id_consistency', False)
                st.metric(
                    "ID Consistency", 
                    "✅ OK" if consistency else "❌ HIBA",
                    delta=None
                )
    
    # Result validation - KRITIKUS RÉSZ  
    if 'result_validation' in diagnosis:
        with st.container():
            st.write("**📊 Eredmény Validáció**")
            
            result_val = diagnosis['result_validation']
            
            if 'producer_validation' in result_val:
                prod_val = result_val['producer_validation']
                
                st.markdown("**Producer Matching:**")
                st.write(f"- Várt Producer ID: `{prod_val.get('expected_producer_id')}`")
                st.write(f"- Talált Producer ID-k: `{prod_val.get('found_producer_ids')}`")
                st.write(f"- Talált Producer nevek: `{prod_val.get('found_producer_names')}`")
                
                match_status = prod_val.get('id_match', False)
                if not match_status:
                    st.error("🚨 **KRITIKUS HIBA**: A szűrő nem egyezik az eredményekkel!")
    
    # Raw data megjelenítés - BUTTON-alapú toggle
    if st.button("📋 Teljes Diagnózis JSON", key="show_full_diagnosis"):
        st.session_state.show_diagnosis_json = not st.session_state.get('show_diagnosis_json', False)
        
    if st.session_state.get('show_diagnosis_json', False):
        with st.container():
            st.write("**🔧 Teljes Diagnózis Adatok:**")
            st.json(diagnosis)

def run_detailed_analysis_fixed():
    """Részletes elemzés futtatása - FIXED: No expanders"""
    
    diagnostic_tool = st.session_state.diagnostic_tool
    
    st.divider()
    st.subheader("📊 RÉSZLETES ELEMZÉS")
    
    with st.spinner("📊 Részletes elemzés futtatása..."):
        try:
            # Get current filters without using the problematic render_diagnostic_ui
            ui_filters = st.session_state.filter_panel.get_current_filters()
            
            # Show current filter state in container
            with st.container():
                st.write("**Current Filter State:**")
                st.json(ui_filters)
            
            # Manual diagnostic trigger
            if st.button("🚨 Run Full Diagnostic", type="primary", key="run_full_diagnostic_detailed"):
                
                with st.spinner("Running filter chain diagnosis..."):
                    try:
                        # Get API params
                        api_params = st.session_state.data_coordinator.convert_filters_to_api_params(ui_filters)
                        
                        # Get current results
                        results = st.session_state.data_coordinator.load_offers(ui_filters)
                        
                        # Run diagnosis
                        diagnosis = diagnostic_tool.diagnose_filter_chain(ui_filters, api_params, results)
                        
                        # Display results in container
                        with st.container():
                            display_critical_diagnosis_fixed(diagnosis)
                            
                    except Exception as e:
                        st.error(f"Diagnostic failed: {str(e)}")
                        st.exception(e)
                        logger.error(f"Diagnostic error: {e}", exc_info=True)
            
        except Exception as e:
            st.error(f"Részletes elemzés hiba: {str(e)}")
            st.exception(e)

def run_automatic_producer_test():
    """Automatikus producer szűrő teszt futtatása - LÉPÉS 4 implementáció"""
    
    st.divider()
    st.subheader("🔧 **AUTOMATIKUS PRODUCER SZŰRŐ TESZT**")
    
    if 'diagnostic_tool' not in st.session_state:
        st.error("Diagnostic tool nincs inicializálva")
        return
    
    # Jelenlegi szűrők lekérése
    try:
        ui_filters = st.session_state.filter_panel.get_current_filters()
    except Exception as e:
        st.error(f"Hiba a szűrők lekérésekor: {str(e)}")
        return
    
    if 'producer_filter' not in ui_filters:
        st.warning("⚠️ Nincs producer szűrő beállítva a teszteléshez")
        st.info("Kérlek állíts be egy producer szűrőt a UI-ban, majd futtasd újra a tesztet.")
        return
    
    producer_data = ui_filters['producer_filter']
    if not isinstance(producer_data, tuple) or len(producer_data) != 2:
        st.error(f"❌ Érvénytelen producer szűrő formátum: {producer_data}")
        return
    
    name, producer_id = producer_data
    
    with st.spinner(f"🔧 Producer szűrő tesztelése különböző API paraméterekkel: {name} (ID: {producer_id})..."):
        
        # Komprehenzív teszt futtatása
        test_results = st.session_state.diagnostic_tool.run_comprehensive_producer_test(ui_filters)
        
        # Eredmények megjelenítése
        display_producer_test_results(test_results)

def display_producer_test_results(results):
    """Producer teszt eredmények megjelenítése Enhanced UI-val"""
    
    st.write("**🔍 AUTOMATIKUS TESZT EREDMÉNYEI**")
    
    if 'error' in results:
        st.error(f"❌ Teszt hiba: {results['error']}")
        if 'ui_filters' in results:
            st.write("**Debug - UI Filters:**")
            st.json(results['ui_filters'])
        return
    
    # Producer alapadatok megjelenítése
    producer_info = results.get('producer_info', {})
    current_situation = results.get('current_situation', {})
    recommendation = results.get('recommendation', {})
    
    with st.container():
        st.write("**📋 Tesztelt Producer Információk**")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Producer Név", producer_info.get('name', 'N/A'))
        with col2:
            st.metric("Producer ID", producer_info.get('id', 'N/A'))
        with col3:
            current_matches = current_situation.get('matching_offers', 0)
            st.metric("Jelenlegi Egyező Ajánlatok", current_matches)
    
    # Jelenlegi API paraméterek
    st.write("**🔧 Jelenlegi API Paraméterek:**")
    current_api_params = current_situation.get('api_params', {})
    if 'error' in current_api_params:
        st.error(f"API paraméter hiba: {current_api_params['error']}")
    else:
        st.json(current_api_params)
    
    # Variant teszt eredmények megjelenítése
    variant_results = results.get('variant_test_results', {})
    if 'test_cases' in variant_results:
        st.write("**🧪 API Paraméter Változatok Tesztelése:**")
        
        test_cases = variant_results['test_cases']
        summary = variant_results['summary']
        
        # Summary metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Összes Teszt", summary.get('total_tests', 0))
        with col2:
            successful_tests = summary.get('successful_tests', 0)
            st.metric("Sikeres Tesztek", successful_tests, 
                     delta=successful_tests - summary.get('failed_tests', 0))
        with col3:
            best_test = summary.get('best_test')
            best_score = best_test.get('matching_offers', 0) if best_test else 0
            st.metric("Legjobb Eredmény", best_score)
        
        # Detailed test results
        for test_name, test_result in test_cases.items():
            success_icon = "✅" if test_result.get('test_success') else "❌"
            api_success_icon = "🔗" if test_result.get('api_success') else "💥"
            
            with st.expander(f"{success_icon}{api_success_icon} {test_name}: {test_result.get('params', {})}"):
                if test_result.get('test_success'):
                    matching = test_result.get('matching_offers', 0)
                    total = test_result.get('total_offers', 0)
                    match_rate = test_result.get('match_rate', 0)
                    
                    st.success(f"✅ Sikeres! {matching} egyező ajánlat {total} összesből ({match_rate:.1%})")
                    
                    if matching > current_matches:
                        improvement = matching - current_matches
                        st.info(f"🎯 Javulás: +{improvement} ajánlat a jelenlegihez képest!")
                        
                elif test_result.get('api_success'):
                    st.warning("⚠️ API hívás sikeres, de nincs egyező ajánlat")
                    st.write(f"Összes eredmény: {test_result.get('total_offers', 0)}")
                else:
                    if 'error' in test_result:
                        st.error(f"❌ API hiba: {test_result['error']}")
                    elif 'exception' in test_result:
                        st.error(f"🚨 Kivétel: {test_result['exception']}")
                    else:
                        st.error("❌ Ismeretlen hiba")
                
                # Show sample offer fields if available
                if test_result.get('sample_offer_fields'):
                    st.write("**Elérhető mezők az ajánlatokban:**")
                    st.write(", ".join(test_result['sample_offer_fields']))
    
    # Field analysis megjelenítése
    field_analysis = current_situation.get('field_analysis', {})
    if field_analysis and field_analysis.get('field_usage'):
        st.write("**📊 Mező Használat Elemzése a Jelenlegi Eredményekben:**")
        
        field_usage = field_analysis['field_usage']
        most_common_fields = field_analysis.get('most_common_fields', [])
        
        if most_common_fields:
            st.write("**Leggyakrabban használt producer/user mezők:**")
            for field, count in most_common_fields:
                st.write(f"- **{field}**: {count} ajánlat")
        
        # User ID distribution
        user_id_dist = field_analysis.get('user_id_distribution', {})
        if user_id_dist:
            st.write("**User ID értékek eloszlása:**")
            for field, values in user_id_dist.items():
                if values:
                    st.write(f"- **{field}**: {dict(list(values.items())[:5])}")  # Show first 5
    
    # RECOMMENDATION SECTION - Ez a legfontosabb rész!
    st.write("**🎯 AJÁNLOTT JAVÍTÁS:**")
    
    status = recommendation.get('status', 'unknown')
    message = recommendation.get('message', 'Nincs információ')
    
    if status == 'fix_available':
        st.success("🎉 **MEGOLDÁS TALÁLVA!**")
        st.success(message)
        
        recommended_params = recommendation.get('recommended_params', {})
        expected_results = recommendation.get('expected_results', 0)
        improvement = recommendation.get('improvement', 0)
        confidence = recommendation.get('confidence', 'unknown')
        
        col1, col2 = st.columns(2)
        with col1:
            st.write("**Ajánlott API paraméterek:**")
            st.json(recommended_params)
        with col2:
            st.metric("Várható javulás", f"+{improvement} ajánlat")
            st.metric("Megbízhatóság", confidence.upper())
        
        if st.button("✅ **JAVÍTÁS ALKALMAZÁSA**", type="primary", key="apply_producer_fix"):
            apply_producer_filter_fix(recommended_params)
            
    elif status == 'alternative_available':
        st.info("💡 **ALTERNATÍV MEGOLDÁS ELÉRHETŐ**")
        st.info(message)
        
        recommended_params = recommendation.get('recommended_params', {})
        st.write("**Alternatív API paraméterek:**")
        st.json(recommended_params)
        
    elif status == 'current_working':
        st.success("✅ **A JELENLEGI SZŰRŐ MEGFELELŐEN MŰKÖDIK**")
        st.success(message)
        
    else:
        st.error("❌ **NINCS MŰKÖDŐ MEGOLDÁS TALÁLVA**")
        st.error(message)
        
        # Show troubleshooting tips
        st.write("**🔧 Hibaelhárítási javaslatok:**")
        st.write("1. Ellenőrizd hogy a Producer ID helyes-e")
        st.write("2. Próbálj más producer-t kiválasztani")
        st.write("3. Ellenőrizd az API kapcsolatot")
        st.write("4. Nézd meg a részletes log üzeneteket")

def apply_producer_filter_fix(recommended_params):
    """Ajánlott producer szűrő javítás alkalmazása"""
    
    st.success("🔧 **Producer szűrő javítás alkalmazása...**")
    
    # Show what we're applying
    st.write("**Alkalmazott paraméterek:**")
    st.json(recommended_params)
    
    # Store the recommendation in session state for the API parameter converter to use
    if 'producer_filter_override' not in st.session_state:
        st.session_state.producer_filter_override = {}
    
    st.session_state.producer_filter_override = {
        'enabled': True,
        'recommended_params': recommended_params,
        'timestamp': datetime.now().isoformat(),
        'source': 'automatic_producer_test'
    }
    
    st.success("✅ **Javítás alkalmazva!**")
    st.info("🔄 Az API paraméter konverter mostantól a javasolt paramétereket fogja használni.")
    st.info("💡 Próbáld ki újra a szűrést a fő felületen!")
    
    # Optionally, trigger a reload
    if st.button("🔄 **Azonnali Újratöltés**", key="reload_after_fix"):
        # Clear the diagnostic states to go back to main view
        for key in list(st.session_state.keys()):
            if key.startswith('show_'):
                del st.session_state[key]
        st.rerun()

def show_offer_list_modular():
    """
    New modular offer list implementation using the new architecture
    """
    
    # Initialize architecture with session state singleton pattern
    state_manager, data_coordinator, filter_panel = initialize_modular_architecture()
    
    # Render page header
    if HAS_MODERN_UI:
        try:
            inject_modern_styles()
            render_page_header()
        except Exception as e:
            logger.error(f"Error rendering modern UI header: {e}")
            st.title("🪙 Ajánlatok Kezelése - Modular")
    else:
        st.title("🪙 Ajánlatok Kezelése - Modular")
    
    # Show architecture debug info (csak debug módban)
    if st.session_state.get("debug_mode", False):
        show_architecture_debug()
    
    # ÚJ: Producer szűrő állapot megjelenítése
    render_producer_filter_status()
    
    # ÚJ: Debug UI - Session State Producer Keys (csak debug módban)
    if st.session_state.get("debug_mode", False):
        render_debug_ui()
    
    # ÚJ: Diagnostic Tool Panel (csak debug módban)
    if st.session_state.get("debug_mode", False):
        show_diagnostic_panel()
    
    # Render enhanced filter panel
    try:
        ui_filters = filter_panel.render()
        
        # Display filter summary
        if HAS_MODERN_UI:
            render_active_filters(ui_filters)
        else:
            filter_panel.render_filter_summary(ui_filters)
        
    except Exception as e:
        logger.error(f"Error rendering filter panel: {e}")
        st.error(f"Hiba a szűrőpanel megjelenítésekor: {e}")
        ui_filters = {}
    
    # Load offers using data coordinator
    try:
        loading_state = data_coordinator.get_loading_state()
        
        if loading_state == 'loading':
            st.info("⏳ Ajánlatok betöltése...")
        
        with st.spinner('Ajánlatok betöltése...'):
            offers = data_coordinator.load_offers(ui_filters)
            
        # Debug information about the data loading process
        if st.session_state.get("debug_mode", False):
            st.write("🔧 **Debug Mód Aktív** - Fejlesztői információk")
            with st.expander("🔧 Data Coordinator Debug", expanded=False):
                # Show filter conversion
                api_params = data_coordinator.convert_filters_to_api_params(ui_filters)
                col1, col2 = st.columns(2)
                with col1:
                    st.write("**UI Filters:**")
                    st.json(ui_filters)
                with col2:
                    st.write("**API Parameters:**")
                    st.json(api_params)
                
                # Show diagnostic tool results if available
                if 'diagnostic_tool' in st.session_state:
                    st.write("**Filter Diagnostic:**")
                    diagnosis = st.session_state.diagnostic_tool.diagnose_filter_chain(
                        ui_filters, api_params, offers
                    )
                    
                    if diagnosis.get('issues_found'):
                        st.error(f"❌ {len(diagnosis['issues_found'])} issues found")
                        for issue in diagnosis['issues_found'][:3]:  # Show first 3
                            st.warning(f"⚠️ {issue}")
                    else:
                        st.success("✅ No diagnostic issues found")
        
        # Display results
        if offers:
            # Show statistics
            if HAS_MODERN_UI:
                try:
                    render_statistics_card(offers)
                except Exception as e:
                    logger.error(f"Error rendering statistics: {e}")
                    st.metric("Ajánlatok száma", len(offers))
            else:
                st.metric("Ajánlatok száma", len(offers))
            
            # Show offers
            device_type = detect_device_type()
            
            if HAS_MODERN_UI:
                try:
                    render_offers_display(offers, device_type)
                except Exception as e:
                    logger.error(f"Error rendering offers display: {e}")
                    st.dataframe(offers, use_container_width=True)
            else:
                st.dataframe(offers, use_container_width=True)
        else:
            st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
            
        # Show filter summary
        filter_summary = data_coordinator.get_filter_summary(ui_filters)
        if filter_summary != "Nincs aktív szűrő":
            st.caption(f"Aktív szűrők: {filter_summary}")
            
    except Exception as e:
        logger.error(f"Error in data loading: {e}", exc_info=True)
        st.error(f"Hiba az adatok betöltése során: {e}")
        
        # Fallback error display
        if 'data_coordinator' in st.session_state:
            error_messages = st.session_state.data_coordinator.state_manager.get_error_messages()
            if error_messages:
                with st.expander("❌ Hibaüzenetek", expanded=False):
                    for error in error_messages[-5:]:  # Show last 5 errors
                        st.error(f"{error['timestamp']}: {error['message']}")
    
    # Performance info (csak debug módban)
    if st.session_state.get("debug_mode", False) and 'state_manager' in st.session_state:
        metrics = st.session_state.state_manager.get_performance_metrics()
        if metrics.get('api_calls_count', 0) > 0:
            st.caption(f"API hívások: {metrics['api_calls_count']}, "
                      f"Cache találatok: {metrics.get('cache_hits', 0)}, "
                      f"Átlagos idő: {metrics.get('average_load_time', 0):.2f}s")


def render_api_data_diagnostic_section():
    """API Data Diagnostic integration into the main diagnostic panel"""
    
    st.divider()
    st.subheader("🔬 **API ADAT DIAGNOSZTIKA**")
    
    # Get current producer info
    current_filters = get_current_filter_summary()
    
    if current_filters.get('has_producer_filter'):
        producer_info = current_filters['producer_info']
        producer_id = int(producer_info['id'])
        
        # Get date range from current filters
        try:
            ui_filters = st.session_state.filter_panel.get_current_filters()
            date_from = ui_filters.get('from_date', '2025-04-22')
            date_to = ui_filters.get('to_date', '2025-05-22')
            
            # Convert date objects to strings if necessary
            if hasattr(date_from, 'strftime'):
                date_from = date_from.strftime('%Y-%m-%d')
            if hasattr(date_to, 'strftime'):
                date_to = date_to.strftime('%Y-%m-%d')
        except Exception as e:
            st.warning(f"Could not get date filters: {e}")
            date_from = '2025-04-22'
            date_to = '2025-05-22'
        
        # Import and render API diagnostic UI
        try:
            from pages.operator.offer_management.api_data_diagnostic import render_api_data_diagnostic_ui
            
            st.info(f"🎯 **Producer**: {producer_info['name']} (ID: {producer_id})")
            st.info(f"📅 **Date Range**: {date_from} - {date_to}")
            
            # Render the API diagnostic interface
            render_api_data_diagnostic_ui(producer_id, date_from, date_to)
            
        except ImportError as e:
            st.error(f"❌ Could not import API diagnostic tool: {e}")
            st.error("Make sure api_data_diagnostic.py is properly installed")
        except Exception as e:
            st.error(f"❌ API diagnostic tool error: {e}")
            logger.error(f"API diagnostic error: {e}", exc_info=True)
    
    else:
        st.info("⚠️ **API diagnosztikához állíts be egy producer szűrőt**")
        st.write("1. Válassz ki egy producer-t a szűrő panelben")
        st.write("2. Ezután itt megjelenik az API adat diagnosztika")


def show_offer_list():
    """
    Improved offer list display using modular architecture
    """
    # Authentication check
    if not is_authenticated():
        st.warning("Kérjük, jelentkezzen be!")
        return
    
    # Use the new modular implementation
    try:
        show_offer_list_modular()
    except Exception as e:
        logger.error(f"Error in modular implementation: {e}", exc_info=True)
        st.error("A moduláris implementáció hibája miatt alapértelmezett nézetre váltunk.")
        
        # Fallback to basic UI
        if HAS_MODERN_UI:
            render_fallback_ui()
        else:
            st.title("🪙 Ajánlatok Kezelése")
            st.error("Sem a moduláris, sem a modern UI komponensek nem érhetők el.")
            
            # Very basic fallback
            st.info("Alap funkciók:")
            if st.button("🔄 Újratöltés"):
                st.rerun()

# Backward compatibility - meglévő interface-ek megtartása
def show_enhanced_offer_interface():
    """Továbbfejlesztett interface - átirányítás az új verzióra"""
    return show_offer_list()

def show_modern_offer_interface_fallback():
    """Modern interface fallback - átirányítás az új verzióra"""
    return show_offer_list()

# Ha ezt közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(
        page_title="Ajánlatok kezelése - Modern",
        page_icon="🪙",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    render_sidebar()
    
    # View selection toggle
    with st.sidebar:
        st.markdown("### 🎯 Nézet kiválasztása")
        view_options = ["📋 Lista nézet", "🎯 Dashboard nézet"]
        current_view = st.session_state.get('offer_management_view', view_options[0])
        
        selected_view = st.radio(
            "Válassz nézetet:",
            options=view_options,
            index=view_options.index(current_view),
            key="view_selector"
        )
        
        if selected_view != current_view:
            st.session_state.offer_management_view = selected_view
            st.rerun()
    
    # Debug mód kapcsoló és státusz admin számára
    if st.session_state.get("debug_mode", False):
        st.sidebar.warning("🛠️ Debug mód AKTÍV!", icon="⚠️")

    with st.sidebar.expander("🐞 Fejlesztői beállítások", expanded=False):
        debug_status = "Bekapcsolva ✅" if st.session_state.get("debug_mode", False) else "Kikapcsolva ❌"
        if st.button(f"Debug mód: {debug_status}", key="sidebar_debug_toggle", use_container_width=True):
            toggle_debug_mode()
            st.rerun()
        
        if st.session_state.get("debug_mode", False):
            st.write("**🔧 Aktív debug funkciók:**")
            st.write("• Session State analysis")
            st.write("• Filter conversion diagnostic")
            st.write("• API parameter validation")
            st.write("• Performance metrics")
            st.write("• Architecture debug info")
    
    # Kiválasztott ajánlat kezelése (meglévő logika megtartása)
    if "selected_offer_id" in st.session_state:
        # Itt hívnánk a meglévő offer detail komponenst
        try:
            from pages.operator.offer_management.offer_detail import show_offer_detail
            show_offer_detail(st.session_state.selected_offer_id)
        except ImportError:
            st.error("Ajánlat részletek nem érhetők el")
            if st.button("Vissza a listához"):
                del st.session_state["selected_offer_id"]
                st.rerun()
    else:
        # Route between different views based on selection
        current_view = st.session_state.get('offer_management_view', '📋 Lista nézet')
        
        if current_view == '🎯 Dashboard nézet':
            # Render unified dashboard
            try:
                from pages.operator.offer_management.unified_dashboard import render_unified_dashboard
                render_unified_dashboard()
            except ImportError as e:
                st.error(f"❌ Dashboard nem elérhető: {e}")
                st.info("🔄 Visszaváltás lista nézetre...")
                st.session_state.offer_management_view = '📋 Lista nézet'
                if st.button("🔄 Lista nézet betöltése"):
                    st.rerun()
            except Exception as e:
                st.error(f"❌ Dashboard hiba: {e}")
                st.exception(e)
                st.info("🔄 Visszaváltás lista nézetre...")
                if st.button("🔄 Lista nézet betöltése"):
                    st.session_state.offer_management_view = '📋 Lista nézet'
                    st.rerun()
        else:
            # Default list view
            show_offer_list()