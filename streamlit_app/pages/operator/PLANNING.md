# PLANNING.md - Aj<PERSON><PERSON><PERSON><PERSON><PERSON> Modul Fejlesztési Stratégia

## Áttekintés és Vízió

### Projekt Vízió
Az "Ajánlatok kezelése" és "Ajánlatok szűrése" modulok átalakítása egy modern, hatékony és felhasználóbará<PERSON> rends<PERSON>, amely jelent<PERSON>sen javítja az operátorok munkavégzési hatékonyságát, mik<PERSON>zben vizuálisan vonzó és intuitív felhasználói élményt nyújt.

### Célkitűzések
1. A jelenlegi funkcionalitás vizuális megjelenésének jelentős fejlesztése
2. A felhasználói élmény optimalizálása minden eszközön (desktop, tablet, mobil)
3. Az adatkezelés és adatvizualizáció hatékonyságának növelése
4. <PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> ka<PERSON> kódstruktúra kialakítása
5. A rendszer performanciájának és reagálási idejének javítása

## Architektúra és Tervezési Döntések

### Architektúrális Áttekintés
Az ajánlatkezelő rendszer megtartja a jelenlegi többrétegű architektúrát, de modernizált komponens-alapú megközelítéssel:

```
┌─────────────────────────────────────────────────────────┐
│                  Felhasználói Felület                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │  Szűrőpanel │ │ Adatnézetek │ │ Részletes nézet │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└───────────────────────────┬─────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────┐
│                    Állapotkezelés                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │Session State│ │ Gyorsítótár │ │ Eseménykezelés  │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└───────────────────────────┬─────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────┐
│                    Adatkezelés                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │ API Kliens  │ │Adatformázás │ │ Adatvalidáció   │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└───────────────────────────┬─────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────┐
│                       Backend API                       │
└─────────────────────────────────────────────────────────┘
```

### Kulcs Tervezési Döntések

1. **Komponens-alapú megközelítés**: Minden UI elem jól definiált, újrafelhasználható komponensekbe szervezve
2. **Reszponzív First Design**: Mobilról induló tervezés, ami fokozatosan terjed ki nagyobb képernyőkre
3. **Állapotkezelés centralizálása**: Az alkalmazás állapotának kezelése központosított, áttekinthető módon
4. **Moduláris CSS**: Komponens-specifikus stílusok használata a globális stílusok helyett
5. **Aszinkron Adatkezelés**: Az adatbetöltés és feldolgozás aszinkron módon történik a felhasználói élmény javításáért
6. **Intelligens Gyorsítótárazás**: A gyakran használt adatok és lekérdezések eredményeinek gyorsítótárazása

### Komponens Struktúra
A rendszer következő fő komponensekből épül fel:

1. **FilterPanel**: Az összes szűrési funkciót tartalmazó komponens
   - `FilterCard`: Vizuálisan elkülönített szűrőkártya
   - `FilterBadges`: Aktív szűrők megjelenítése címkékkel
   - `DateRangeSlider`: Dátumtartomány kiválasztó csúszka
   - `MultiSelect`: Többszörös kiválasztást biztosító komponens

2. **DataViews**: Különböző nézetek az ajánlatok megjelenítésére
   - `TableView`: Táblázatos megjelenítés fejlett oszlopkezeléssel
   - `CardView`: Kártyás megjelenítés mobilra optimalizálva
   - `StatisticsView`: Statisztikai áttekintés és grafikonok

3. **DetailView**: Részletes ajánlat nézet és szerkesztés
   - `ActionButtons`: Státuszváltás és egyéb műveletek
   - `EditForm`: Ajánlat szerkesztési űrlap
   - `HistoryTimeline`: Ajánlat történetének időrendi megjelenítése

## Technológiai Stack és Eszközök

### Front-end

- **Streamlit**: Az alap keretrendszer, amelyre építünk
- **Custom CSS/HTML**: Egyedi stílusok és komponensek kialakításához
- **JavaScript injections**: Fejlett UI interakciók és dinamikus viselkedés megvalósításához
- **Plotly/Altair**: Adatvizualizációk és grafikonok készítéséhez
- **Streamlit Components**: Fejlettebb interaktív elemekhez

### Back-end

- **FastAPI**: A meglévő API rendszerrel való kommunikáció
- **SQLAlchemy ORM**: Adatbázis integrációhoz (meglévő rendszer)
- **PostgreSQL**: Adattárolás (meglévő rendszer)

### Fejlesztési Eszközök

- **Git**: Verziókezelés
- **Docker/Docker Compose**: Fejlesztési és tesztkörnyezet
- **Streamlit DevTools**: Streamlit alkalmazás fejlesztés és hibakeresés
- **Visual Studio Code**: Ajánlott fejlesztőkörnyezet
- **Postman**: API tesztelés

## Korlátok és Előfeltételek

### Rendszer Korlátok

1. **Streamlit Korlátok**: Figyelembe kell venni a Streamlit keretrendszer korlátait
   - Minden oldalbetöltés újrafuttatja a teljes scriptet
   - Korlátozott DOM manipuláció és kliens-oldali állapotkezelés
   - Korlátozott események kezelése

2. **API Korlátok**:
   - A meglévő API végpontok struktúrája nem változtatható jelentősen
   - Figyelembe kell venni az API teljesítményét nagy adatmennyiség esetén

3. **Kompatibilitási Követelmények**:
   - A rendszernek működnie kell minden modern böngészőben
   - Támogatnia kell az érintőképernyős eszközöket
   - Biztosítania kell a visszafelé kompatibilitást a régebbi adatstruktúrákkal

### Előfeltételek

1. **API Hozzáférés**: A fejlesztéshez szükséges az összes releváns API végponthoz való hozzáférés
2. **Tesztadatok**: Megfelelő mennyiségű és minőségű tesztadat a különböző nézetek és funkciók teszteléséhez
3. **Design Assets**: Ikonok, színpaletták és egyéb vizuális elemek a UI fejlesztéséhez

## Tervezési Alapelvek és Mintázatok

### Felhasználói Élmény Alapelvek

1. **Egyszerűség**: Minden funkció a lehető legegyszerűbb módon érhető el
2. **Következetesség**: Egységes megjelenés és működés az egész alkalmazásban
3. **Vizuális Hierarchia**: Fontos információk és műveletek kiemelése
4. **Azonnali Visszajelzés**: Minden felhasználói interakcióra azonnali visszajelzés
5. **Progresszív Felfedezés**: Alapvető funkciók könnyen elérhetők, haladó funkciók fokozatosan fedezhetők fel

### Kód Szervezési Minták

1. **Komponens Mintázat**: UI elemek újrafelhasználható komponensekbe szervezése
2. **MVC Mintázat**: Modell-Nézet-Vezérlő szétválasztás a kód szervezésében
3. **Factory Mintázat**: Dinamikus komponens létrehozás a megjelenítési igények alapján
4. **Observer Mintázat**: Események és állapotváltozások kezelése
5. **Repository Mintázat**: Adathozzáférés absztrakciója

### Streamlit-Specifikus Minták

1. **Callback-alapú Interakciók**: Streamlit callback rendszerének optimális használata
2. **Session State Menedzsment**: Konzisztens állapotkezelés a Streamlit session state-tel
3. **Lazy Loading**: Adatok és komponensek fokozatos betöltése
4. **Komponens Újrahasználat**: Komponensek okos újrahasználata az újrarenderelések minimalizálására
5. **CSS Injection**: Egyedi stílusok beillesztése az alapértelmezett Streamlit stílusok kiterjesztésére

## Fejlesztési Metodológia

### Agilis Fejlesztési Folyamat

1. **Inkrementális Fejlesztés**: Kis, működő egységekben történő fejlesztés
2. **Iteratív Tesztelés**: Folyamatos tesztelés és visszacsatolás
3. **Folyamatos Integráció**: Rendszeres kódösszefésülés és tesztelés
4. **Felhasználói Visszajelzés**: Korai és gyakori felhasználói visszajelzések gyűjtése

### Fejlesztési Fázisok

1. **Prototípus Fázis**: Kulcsfunkciók vázlatos implementálása
2. **Alapozó Fázis**: Komponens könyvtár és alapvető infrastruktúra kiépítése
3. **Funkcionális Fázis**: Kulcsfunkciók teljes implementálása
4. **Vizuális Fázis**: UI finomhangolás és vizuális elemek tökéletesítése
5. **Optimalizációs Fázis**: Teljesítmény finomhangolás és bugfixek

### Tesztelési Stratégia

1. **Komponens Tesztelés**: Minden komponens egyedi tesztelése
2. **Integrációs Tesztelés**: Komponensek együttműködésének tesztelése
3. **End-to-End Tesztelés**: Teljes felhasználói folyamatok tesztelése
4. **Teljesítmény Tesztelés**: Nagy adatmennyiségekkel való teljesítmény mérése
5. **Felhasználói Tesztelés**: Valós felhasználókkal történő tesztelés

## Implementációs Irányelvek

### Kódolási Szabályok

1. **PEP 8 Követés**: Python kód PEP 8 szabványnak megfelelően
2. **Dokumentáció**: Minden komponens és funkció megfelelő dokumentálása
3. **Típus Annotációk**: Python típus annotációk használata
4. **Hibakezelés**: Robusztus hibakezelés minden kritikus ponton
5. **Tesztelhetőség**: Kód írása a könnyű tesztelhetőség szem előtt tartásával

### Frontend Fejlesztési Irányelvek

1. **Modularitás**: Funkcionalitás logikai komponensekbe szervezése
2. **Reszponzivitás**: Minden komponens reszponzív viselkedésének biztosítása
3. **Akadálymentesség**: WCAG 2.1 AA szintű akadálymentesség biztosítása
4. **Performancia**: CPU és memória használat optimalizálása
5. **Cross-Browser**: Minden funkció tesztelése különböző böngészőkben

### Backend Integrációs Irányelvek

1. **API Absztrakció**: Egységes interfész az API hívásokhoz
2. **Hibatűrés**: Robusztus hibakezselés az API hívások során
3. **Gyorsítótárazás**: Optimális gyorsítótárazási stratégia
4. **Batch Műveletek**: Nagy adatmennyiségek hatékony kezelése
5. **Aszinkronitás**: Aszinkron adatkezelés blokkoló műveletek elkerülésére

## Jövőbeli Kiterjeszthetőség

### Tervezett Jövőbeli Fejlesztések

1. **Fejlett Analitika**: Prediktív elemzések és mintázatfelismerés
2. **Több Nézet Mód**: Kanban nézet, naptár nézet, stb.
3. **Automatizált Jelentések**: Egyéni jelentések és kimutatások generálása
4. **AI Asszisztens**: Intelligens javaslatok és automatikus kategorizálás
5. **Offline Mód**: Korlátozott offline funkcionalitás

### Architektúrális Előkészítés

1. **Plugin Rendszer**: Moduláris bővíthetőség külső komponensekkel
2. **API Verzionálás**: Zökkenőmentes API frissítések támogatása
3. **Konfiguráció Menedzsment**: Környezetfüggő konfigurációs rendszer
4. **Teljesítmény Monitoring**: Teljesítmény metrikák gyűjtése és elemzése
5. **A/B Tesztelés**: Új funkciók és UI változtatások tesztelési keretrendszere


### UI Javaslatok
1. Modern Szűrőpanel
┌─────────────────────────────────────────────────────────┐
│ 🔍 Ajánlatok szűrése                           🔄 [-]   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  Termelő: [Válasszon termelőt ▼]                       │
│                                                         │
│  Státusz:  [✓] Létrehozva   [✓] Megerősítve            │
│            [  ] Elfogadva   [  ] Elutasítva            │
│            [  ] Véglegesítve                           │
│                                                         │
│  Időszak:  [====●==========●====]                      │
│            2023.01.01      2023.12.31                  │
│                                                         │
│  Termék:   [Válasszon termék típust ▼]                 │
│                                                         │
│  Keresés:  [________________________]  🔍 Keresés      │
│                                                         │
│  Mentett szűrők: [Mai ajánlatok ▼]     💾 Mentés       │
│                                                         │
└─────────────────────────────────────────────────────────┘

 Aktív szűrők:  [✕ Termelő: Kovács János]  [✕ Státusz: Létrehozva, Megerősítve]
                [✕ Időszak: 2023.01.01 - 2023.12.31]

┌─────────────────────────────────────────────────────────┐
│ Találatok: 42 ajánlat                                   │
└─────────────────────────────────────────────────────────┘
2. Továbbfejlesztett Adatmegjelenítés
┌───────────────────────────────────────────────────────────────────────┐
│ 📊 Statisztika                                                        │
├───────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  Összes mennyiség: 1,250 kg   Átlagár: 230 Ft/kg   Összérték: 287,500 Ft  │
│                                                                       │
│  [Grafikon: Státuszok megoszlása]      [Grafikon: Terméktípusok]     │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────────────┐
│ 📋 Ajánlatok listája                           Nézet: 📊 📅 📱 🗂️       │
├───────────────────────────────────────────────────────────────────────┤
│ ▶️ ID    📆 Dátum      👤 Termelő        🍎 Termék   📦 Mennyiség  💰 Ár  │
├───────────────────────────────────────────────────────────────────────┤
│ #1001  2023.05.12  Kovács János  🍎 Alma     500 kg     220 Ft/kg 🟢 │
│ #1002  2023.05.13  Szabó Béla    🍇 Szőlő    200 kg     350 Ft/kg 🟡 │
│ #1003  2023.05.14  Nagy István   🥔 Burgonya 550 kg     180 Ft/kg 🔴 │
└───────────────────────────────────────────────────────────────────────┘
3. Mobil Nézet Optimalizálás
┌───────────────────────┐
│   Ajánlatok kezelése  │
│   🔍 Szűrők  [↓]      │
└───────────────────────┘

 Aktív szűrők: 3 [↓]

┌───────────────────────┐
│ #1001 - Kovács János  │
│ 🍎 Alma               │
│ 500 kg @ 220 Ft/kg    │
│ 🟢 Létrehozva         │
└───────────────────────┘

┌───────────────────────┐
│ #1002 - Szabó Béla    │
│ 🍇 Szőlő              │
│ 200 kg @ 350 Ft/kg    │
│ 🟡 Megerősítve        │
└───────────────────────┘

---

Ez a dokumentum szolgál az "Ajánlatok kezelése" és "Ajánlatok szűrése" modulok fejlesztésének alapjául. A fejlesztési döntéseket és az implementációt a fenti irányelvek és stratégiák mentén kell végezni.

*Utolsó frissítés: 2025.05.21.*