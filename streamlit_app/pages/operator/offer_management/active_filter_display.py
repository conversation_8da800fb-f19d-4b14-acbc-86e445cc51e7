"""
Aktív szűrők vizuális megjelenítése.

Ez a modul különálló komponenseket biztosít az aktív szűrők vizuális
megjelenítéséhez címk<PERSON>k (badge-ek) form<PERSON>j<PERSON><PERSON>, egyérintéses törlési
lehetőséggel és összesítő számlálókkal.
"""
import streamlit as st
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union, Tuple

# Az egyedi CSS keretrendszer importálása
try:
    from streamlit_app.pages.operator.offer_management.custom_css_framework import (
        inject_filter_styles,
        COLORS
    )
except ImportError:
    try:
        from pages.operator.offer_management.custom_css_framework import (
            inject_filter_styles,
            COLORS
        )
    except ImportError:
        try:
            from offer_management.custom_css_framework import (
                inject_filter_styles,
                COLORS
            )
        except ImportError:
            try:
                from custom_css_framework import (
                    inject_filter_styles,
                    COLORS
                )
            except ImportError:
                logging.error("Nem sikerült importálni a CSS keretrendszert")
                # Fallback minimális implementációk
                def inject_filter_styles():
                    pass
                COLORS = {"primary": "#3584e4"}


# Konfiguráljuk a naplózást
logger = logging.getLogger(__name__)


def inject_active_filter_styles():
    """
    Egyedi stílusok injektálása az aktív szűrők megjelenítéséhez.
    
    Ezek a stílusok kiegészítik az alap CSS keretrendszert specifikus
    aktív szűrő stílusokkal.
    """
    # Először az alap szűrő stílusokat injektáljuk
    inject_filter_styles()
    
    # Majd hozzáadjuk az aktív szűrő specifikus stílusokat
    filter_css = """
    <style>
    /* Aktív szűrők konténer */
    .active-filters-container {
        margin: 10px 0;
        padding: 10px;
        border-radius: var(--radius-lg);
        background-color: rgba(53, 132, 228, 0.05);
        border: 1px solid rgba(53, 132, 228, 0.1);
    }
    
    /* Aktív szűrők fejléc */
    .active-filters-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .active-filters-title {
        font-size: var(--font-size-md);
        font-weight: 600;
        color: var(--color-darkgray);
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .active-filters-counter {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 22px;
        height: 22px;
        padding: 0 6px;
        background-color: var(--color-primary);
        color: white;
        font-size: var(--font-size-xs);
        font-weight: 600;
        border-radius: var(--radius-full);
    }
    
    .active-filters-actions {
        display: flex;
        gap: 8px;
    }
    
    .active-filters-clear-all {
        font-size: var(--font-size-sm);
        color: var(--color-primary);
        cursor: pointer;
        transition: all var(--animation-fast);
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .active-filters-clear-all:hover {
        color: var(--color-danger);
        text-decoration: underline;
    }
    
    /* Fejlett címke stílusok */
    .filter-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .filter-badge {
        display: inline-flex;
        align-items: center;
        background-color: white;
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-full);
        padding: 4px 12px;
        font-size: var(--font-size-sm);
        color: var(--color-darkgray);
        transition: all var(--animation-fast);
        box-shadow: var(--shadow-small);
        animation: badge-appear 0.3s ease;
    }
    
    @keyframes badge-appear {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes badge-disappear {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-10px);
        }
    }
    
    .filter-badge.removing {
        animation: badge-disappear 0.3s ease forwards;
    }
    
    .filter-badge:hover {
        box-shadow: var(--shadow-medium);
        transform: translateY(-1px);
    }
    
    .filter-badge-label {
        font-weight: 600;
        margin-right: 6px;
        color: var(--color-primary);
    }
    
    .filter-badge-value {
        font-weight: 400;
    }
    
    .filter-badge-clear {
        margin-left: 8px;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--color-midgray);
        color: white;
        border-radius: 50%;
        font-size: 10px;
        cursor: pointer;
        transition: all var(--animation-fast);
    }
    
    .filter-badge-clear:hover {
        background-color: var(--color-danger);
        transform: scale(1.1);
    }
    
    /* Kategorizált címkék */
    .filter-badge-category-status {
        border-left: 3px solid #ffc107;
    }
    
    .filter-badge-category-date {
        border-left: 3px solid #4caf50;
    }
    
    .filter-badge-category-producer {
        border-left: 3px solid #2196f3;
    }
    
    .filter-badge-category-product {
        border-left: 3px solid #ff5722;
    }
    
    .filter-badge-category-quantity {
        border-left: 3px solid #9c27b0;
    }
    
    /* Üres állapot */
    .active-filters-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        font-size: var(--font-size-sm);
        color: var(--color-gray);
        font-style: italic;
    }
    
    /* Animációk és átmenetek */
    .active-filters-container {
        transition: all var(--animation-normal);
    }
    
    .active-filters-container:hover {
        box-shadow: var(--shadow-small);
    }
    
    /* Reszponzív stílusok */
    @media (max-width: 768px) {
        .filter-badge {
            font-size: var(--font-size-xs);
            padding: 3px 8px;
        }
        
        .filter-badges {
            gap: 5px;
        }
        
        .filter-badge-clear {
            width: 16px;
            height: 16px;
            margin-left: 6px;
        }
        
        .active-filters-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }
        
        .active-filters-title {
            font-size: var(--font-size-sm);
        }
    }
    </style>
    """
    
    try:
        st.markdown(filter_css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba az aktív szűrő stílusok injektálásakor: {str(e)}")


def get_category_for_key(key: str) -> str:
    """
    Meghatározza a szűrő kategóriáját a kulcs alapján.
    
    Args:
        key: A szűrő kulcsneve
        
    Returns:
        str: A kategória neve
    """
    # Kategória mappingek
    category_mappings = {
        "status": "status",
        "from_date": "date",
        "to_date": "date",
        "delivery_date": "date",
        "created_at": "date",
        "updated_at": "date",
        "producer": "producer",
        "producer_id": "producer",
        "user_id": "producer",
        "product": "product",
        "product_type": "product",
        "product_id": "product",
        "product_type_id": "product",
        "quantity": "quantity",
        "min_quantity": "quantity",
        "max_quantity": "quantity",
        "price": "quantity",
        "min_price": "quantity",
        "max_price": "quantity"
    }
    
    # A kulcs átalakítása snake_case-re a mappinghez
    normalized_key = key.lower().replace(" ", "_")
    
    # Alapértelmezett kategória, ha nincs specifikus mapping
    return category_mappings.get(normalized_key, "other")


def format_filter_value(key: str, value: Any) -> str:
    """
    A szűrő értékét felhasználóbarát formátumra alakítja.
    
    Args:
        key: A szűrő kulcsa
        value: A szűrő értéke
        
    Returns:
        str: A formázott érték
    """
    if value is None:
        return "-"
    
    # Státusz formázás
    if key == "status" or "status" in key.lower():
        try:
            from utils.formatting import format_status
            return format_status(value)
        except ImportError:
            # Egyszerű formázás, ha a formázó függvény nem érhető el
            return str(value).replace("_", " ").title()
    
    # Dátum formázás
    if isinstance(value, datetime) or "date" in key.lower():
        try:
            if isinstance(value, datetime):
                return value.strftime("%Y-%m-%d")
            elif hasattr(value, "strftime"):
                return value.strftime("%Y-%m-%d")
        except Exception:
            pass
    
    # Mennyiség formázás
    if "quantity" in key.lower():
        try:
            if isinstance(value, (int, float)):
                return f"{value:,} kg".replace(",", " ")
        except Exception:
            pass
    
    # Ár formázás
    if "price" in key.lower():
        try:
            if isinstance(value, (int, float)):
                return f"{value:,} Ft".replace(",", " ")
        except Exception:
            pass
    
    # Alapértelmezett string konverzió
    return str(value)


def format_filter_key(key: str) -> str:
    """
    A szűrő kulcsát felhasználóbarát formátumra alakítja.
    
    Args:
        key: A szűrő kulcsa
        
    Returns:
        str: A formázott kulcs
    """
    # Kulcs mappingek
    key_mappings = {
        "producer": "Termelő",
        "producer_id": "Termelő",
        "user_id": "Termelő",
        "status": "Státusz",
        "from_date": "Kezdő dátum",
        "to_date": "Végső dátum",
        "delivery_date": "Szállítás",
        "created_at": "Létrehozva",
        "updated_at": "Frissítve",
        "product": "Termék",
        "product_type": "Terméktípus",
        "product_id": "Termék",
        "product_type_id": "Terméktípus",
        "quantity": "Mennyiség",
        "min_quantity": "Min. mennyiség",
        "max_quantity": "Max. mennyiség",
        "price": "Ár",
        "min_price": "Min. ár",
        "max_price": "Max. ár"
    }
    
    # Ha van közvetlen mapping, használjuk azt
    if key in key_mappings:
        return key_mappings[key]
    
    # Egyébként próbáljuk a kulcsot formázni
    return key.replace("_", " ").title()


def render_active_filter_badges(
    filters: Dict[str, Any],
    prefix: str = "",
    on_remove: Optional[Callable[[str], None]] = None,
    categorize: bool = True
) -> int:
    """
    Aktív szűrők megjelenítése címkék formájában.
    
    Args:
        filters: A szűrők szótára {kulcs: érték} formában
        prefix: Előtag a session state kulcsokhoz
        on_remove: Függvény a címke törlésekor (paraméter: szűrő kulcsa)
        categorize: Kategorizálja-e a címkéket a típus szerint
        
    Returns:
        int: Az aktív szűrők száma
    """
    # Először szűrjük a nem üres értékeket
    active_filters = {k: v for k, v in filters.items() if v is not None and v != ""}
    
    # Ha nincsenek aktív szűrők, nem jelenítünk meg semmit
    if not active_filters:
        return 0
    
    # Címkék HTML generálása
    badges_html = '<div class="filter-badges">'
    
    # Címkék generálása
    for idx, (key, value) in enumerate(active_filters.items()):
        badge_id = f"badge_{prefix}_{key}_{idx}"
        formatted_key = format_filter_key(key)
        formatted_value = format_filter_value(key, value)
        
        # Kategória meghatározása
        category = get_category_for_key(key) if categorize else ""
        category_class = f"filter-badge-category-{category}" if category else ""
        
        # A címke HTML-je
        badges_html += f"""
        <div class="filter-badge {category_class}" id="{badge_id}">
            <span class="filter-badge-label">{formatted_key}:</span>
            <span class="filter-badge-value">{formatted_value}</span>
            <span class="filter-badge-clear" data-key="{key}">✕</span>
        </div>
        """
    
    badges_html += '</div>'
    
    # Címkék megjelenítése
    st.markdown(badges_html, unsafe_allow_html=True)
    
    # JavaScript a címkék eltávolításához
    if on_remove:
        js_code = f"""
        <script>
        document.addEventListener('DOMContentLoaded', () => {{
            const clearButtons = document.querySelectorAll('.filter-badge-clear');
            
            clearButtons.forEach(button => {{
                button.addEventListener('click', (event) => {{
                    event.stopPropagation();
                    const key = button.getAttribute('data-key');
                    const badge = button.closest('.filter-badge');
                    
                    if (key && badge) {{
                        // Animáció hozzáadása az eltávolításhoz
                        badge.classList.add('removing');
                        
                        // Rövid késleltetés az animáció befejeződéséhez
                        setTimeout(() => {{
                            // Megfelelő rejtett gomb megtalálása és kattintás
                            document.getElementById('clear_filter_{prefix}_' + key).click();
                        }}, 300);
                    }}
                }});
            }});
        }});
        </script>
        """
        st.markdown(js_code, unsafe_allow_html=True)
        
        # Rejtett gombok minden címkéhez
        for key in active_filters.keys():
            if st.button("Clear", key=f"clear_filter_{prefix}_{key}", label_visibility="collapsed"):
                if on_remove:
                    on_remove(key)
                st.rerun()
    
    # Az aktív szűrők számának visszaadása
    return len(active_filters)


def render_active_filters_panel(
    filters: Dict[str, Any],
    prefix: str = "",
    title: str = "Aktív szűrők",
    icon: str = "🔍",
    on_remove: Optional[Callable[[str], None]] = None,
    on_clear_all: Optional[Callable[[], None]] = None,
    categorize: bool = True,
    container_id: Optional[str] = None
) -> int:
    """
    Teljes aktív szűrők panelt jelenít meg, címkékkel és fejléccel.
    
    Args:
        filters: A szűrők szótára {kulcs: érték} formában
        prefix: Előtag a session state kulcsokhoz
        title: A panel címe
        icon: A panel ikonja
        on_remove: Függvény a címke törlésekor (paraméter: szűrő kulcsa)
        on_clear_all: Függvény az összes szűrő törlésekor
        categorize: Kategorizálja-e a címkéket a típus szerint
        container_id: Egyedi azonosító a konténerhez
        
    Returns:
        int: Az aktív szűrők száma
    """
    # Stílusok injektálása
    inject_active_filter_styles()
    
    # Először szűrjük a nem üres értékeket
    active_filters = {k: v for k, v in filters.items() if v is not None and v != ""}
    filter_count = len(active_filters)
    
    # Egyedi azonosító a konténerhez
    if not container_id:
        container_id = f"active_filters_{prefix}_{str(uuid.uuid4())[:8]}"
    
    # Ne jelenítse meg, ha nincs aktív szűrő
    if not active_filters:
        return 0
    
    # Panel HTML generálása
    panel_html = f"""
    <div class="active-filters-container" id="{container_id}">
        <div class="active-filters-header">
            <div class="active-filters-title">
                {icon} {title} <span class="active-filters-counter">{filter_count}</span>
            </div>
            <div class="active-filters-actions">
                <div class="active-filters-clear-all" id="clear_all_{container_id}">🗑️ Összes törlése</div>
            </div>
        </div>
    """
    
    # Panel megjelenítése
    st.markdown(panel_html, unsafe_allow_html=True)
    
    # Címkék megjelenítése
    active_count = render_active_filter_badges(
        filters=active_filters,
        prefix=prefix,
        on_remove=on_remove,
        categorize=categorize
    )
    
    # Panel lezárása
    st.markdown('</div>', unsafe_allow_html=True)
    
    # JavaScript az "Összes törlése" gombhoz
    if on_clear_all:
        js_code = f"""
        <script>
        document.addEventListener('DOMContentLoaded', () => {{
            const clearAllBtn = document.getElementById('clear_all_{container_id}');
            
            if (clearAllBtn) {{
                clearAllBtn.addEventListener('click', () => {{
                    // Az összes címke animálása
                    const badges = document.querySelectorAll('.filter-badge');
                    badges.forEach(badge => {{
                        badge.classList.add('removing');
                    }});
                    
                    // Rövid késleltetés az animáció befejeződéséhez
                    setTimeout(() => {{
                        // Rejtett gomb aktiválása
                        document.getElementById('clear_all_filters_{prefix}').click();
                    }}, 300);
                }});
            }}
        }});
        </script>
        """
        st.markdown(js_code, unsafe_allow_html=True)
        
        # Rejtett gomb az összes szűrő törléséhez
        if st.button("Clear All", key=f"clear_all_filters_{prefix}", label_visibility="collapsed"):
            if on_clear_all:
                on_clear_all()
            st.rerun()
    
    return active_count


def render_filter_summary(
    filters: Dict[str, Any],
    prefix: str = "",
    show_empty: bool = False
) -> None:
    """
    Egyszerű szöveges összefoglaló az aktív szűrőkről.
    
    Args:
        filters: A szűrők szótára {kulcs: érték} formában
        prefix: Előtag a session state kulcsokhoz
        show_empty: Megjelenítsen-e üzenetet, ha nincs aktív szűrő
    """
    # Először szűrjük a nem üres értékeket
    active_filters = {k: v for k, v in filters.items() if v is not None and v != ""}
    filter_count = len(active_filters)
    
    if not active_filters:
        if show_empty:
            st.info("Nincsenek aktív szűrők.")
        return
    
    # Összefoglaló szöveg
    summary_text = f"**{filter_count} aktív szűrő:** "
    
    # Formázott szűrők összeállítása
    formatted_filters = []
    for key, value in active_filters.items():
        formatted_key = format_filter_key(key)
        formatted_value = format_filter_value(key, value)
        formatted_filters.append(f"{formatted_key}: {formatted_value}")
    
    # Összefűzés vessző+szóköz elválasztóval
    summary_text += ", ".join(formatted_filters)
    
    # Megjelenítés
    st.markdown(summary_text)


# Példa használat, ha ezt a modult közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(page_title="Aktív Szűrő Megjelenítés Demó", layout="wide")
    
    st.title("Aktív Szűrők Vizuális Megjelenítése - Demó")
    
    st.markdown("""
    Ez a demó oldal az aktív szűrők vizuális megjelenítését mutatja be, amely a TASK-1.3 feladat részeként készült.
    A komponens a következő jellemzőket tartalmazza:
    
    - ✅ Címke (badge) rendszer az aktív szűrők megjelenítésére
    - ✅ Egyérintéses törlési funkció a címkékhez
    - ✅ Animációk a címkék megjelenéséhez és eltűnéséhez
    - ✅ Összesítő számláló a szűrőpanel fejlécében
    - ✅ Kategorizált címkék a szűrő típusa szerint
    - ✅ Reszponzív design
    """)
    
    # Demo szűrők létrehozása
    if "demo_filters" not in st.session_state:
        st.session_state.demo_filters = {
            "status": "CONFIRMED_BY_COMPANY",
            "producer_id": 1001,
            "from_date": datetime.now().date(),
            "to_date": None,
            "product_type_id": 42,
            "min_quantity": 100,
            "max_quantity": 500
        }
    
    # Szűrő törlés kezelése
    def handle_remove_filter(key):
        if key in st.session_state.demo_filters:
            st.session_state.demo_filters[key] = None
            st.success(f"{key} szűrő törölve!")
    
    # Összes szűrő törlése
    def handle_clear_all_filters():
        for key in st.session_state.demo_filters:
            st.session_state.demo_filters[key] = None
        st.success("Minden szűrő törölve!")
    
    # Szűrők beállítása Demo UI-hoz
    with st.expander("Szűrők beállítása a demóhoz"):
        col1, col2 = st.columns(2)
        
        with col1:
            # Státusz
            status_options = [None, "CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            st.session_state.demo_filters["status"] = st.selectbox(
                "Státusz:",
                options=status_options,
                format_func=lambda x: "Válasszon státuszt..." if x is None else x,
                index=status_options.index(st.session_state.demo_filters["status"]) if st.session_state.demo_filters["status"] in status_options else 0
            )
            
            # Termelő ID
            producer_id = st.number_input(
                "Termelő ID:",
                min_value=0,
                value=st.session_state.demo_filters["producer_id"] or 0
            )
            st.session_state.demo_filters["producer_id"] = producer_id if producer_id > 0 else None
            
            # Terméktípus ID
            product_type_id = st.number_input(
                "Terméktípus ID:",
                min_value=0,
                value=st.session_state.demo_filters["product_type_id"] or 0
            )
            st.session_state.demo_filters["product_type_id"] = product_type_id if product_type_id > 0 else None
        
        with col2:
            # Dátumok
            from_date = st.date_input(
                "Kezdő dátum:",
                value=st.session_state.demo_filters["from_date"]
            )
            st.session_state.demo_filters["from_date"] = from_date
            
            to_date = st.date_input(
                "Végső dátum:",
                value=st.session_state.demo_filters["to_date"] or datetime.now().date()
            )
            st.session_state.demo_filters["to_date"] = to_date
            
            # Mennyiségek
            min_quantity = st.number_input(
                "Min. mennyiség (kg):",
                min_value=0,
                value=st.session_state.demo_filters["min_quantity"] or 0
            )
            st.session_state.demo_filters["min_quantity"] = min_quantity if min_quantity > 0 else None
            
            max_quantity = st.number_input(
                "Max. mennyiség (kg):",
                min_value=0,
                value=st.session_state.demo_filters["max_quantity"] or 0
            )
            st.session_state.demo_filters["max_quantity"] = max_quantity if max_quantity > 0 else None
    
    # Komponens megjelenítése
    st.subheader("1. Teljes aktív szűrők panel")
    filter_count = render_active_filters_panel(
        filters=st.session_state.demo_filters,
        prefix="demo",
        title="Aktív szűrők",
        icon="🔍",
        on_remove=handle_remove_filter,
        on_clear_all=handle_clear_all_filters,
        categorize=True
    )
    st.caption(f"*A panel {filter_count} aktív szűrőt jelenít meg.*")
    
    st.subheader("2. Csak címkék (panel nélkül)")
    badge_count = render_active_filter_badges(
        filters=st.session_state.demo_filters,
        prefix="demo_badges",
        on_remove=handle_remove_filter,
        categorize=True
    )
    st.caption(f"*A komponens {badge_count} címkét jelenít meg.*")
    
    st.subheader("3. Szöveges összefoglaló")
    render_filter_summary(
        filters=st.session_state.demo_filters,
        prefix="demo_summary",
        show_empty=True
    )
    
    # Fejlesztői információk
    with st.expander("Fejlesztői információk"):
        st.markdown("""
        **Modul:** active_filter_display.py
        
        **Fő funkciók:**
        - `render_active_filters_panel`: Teljes szűrőpanel megjelenítése címkékkel és számlálóval
        - `render_active_filter_badges`: Csak a címkék megjelenítése
        - `render_filter_summary`: Szöveges összefoglaló az aktív szűrőkről
        - `format_filter_value` és `format_filter_key`: Formázó függvények a szűrők felhasználóbarát megjelenítéséhez
        - `get_category_for_key`: Kategória meghatározás a szűrőkhöz
        
        **Függőségek:**
        - custom_css_framework.py: CSS komponensek és stílusok
        
        A komponens teljes mértékben moduláris és újrafelhasználható különböző kontextusokban is.
        """)