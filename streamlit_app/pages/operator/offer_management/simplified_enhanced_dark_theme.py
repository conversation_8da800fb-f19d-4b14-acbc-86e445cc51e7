"""
Simplified Enhanced Dark Theme - Based on Working Patterns
Egyszerűsített enhanced dark theme a működő minták alapján
"""
import streamlit as st
import streamlit.components.v1 as components
from datetime import datetime, timedelta
import plotly.graph_objects as go
from typing import Dict, Any
import pandas as pd

def inject_minimal_styles():
    """Minimális CSS csak a szükséges elemekkel - working patterns alapján"""
    st.markdown("""
    <style>
        /* Alap háttér - Sötét theme */
        .stApp {
            background-color: #0e1117;
        }
        
        /* Main container és sidebar */
        .main .block-container {
            background-color: #0e1117;
            padding-top: 1rem;
        }
        
        /* Sidebar */
        section[data-testid="stSidebar"] {
            background-color: #0e1117;
        }
        
        /* Streamlit alapvető konténerek */
        .stMarkdown, .stText, .stSelectbox, .stTextInput {
            background-color: transparent;
        }
        
        /* Oszlopok háttere */
        .element-container {
            background-color: transparent;
        }
        
        /* Működő offer card minta - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> */
        .offer-card {
            background-color: transparent;
            color: var(--text-color, #e0e0e0);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #0099e0;
            border: 1px solid var(--border-color, #2a2a2a);
        }
        
        /* Egyszerű státusz indikátor */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        /* Egyszerű panel header */
        .panel-header {
            background: transparent;
            color: var(--text-color, white);
            padding: 1rem;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            border-left: 4px solid var(--accent-color, #0099e0);
            border: 1px solid var(--border-color, #2a2a2a);
        }
        
        /* Panel content */
        .panel-content {
            background: transparent;
            color: var(--text-color, #e0e0e0);
            padding: 1rem;
            border-radius: 0 0 8px 8px;
            border-left: 4px solid var(--accent-color, #0099e0);
            border: 1px solid var(--border-color, #2a2a2a);
        }
        
        /* Egyszerű grid layout */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .info-label {
            color: #a0a0a0;
            font-weight: 500;
        }
        
        .info-value {
            color: #ffffff;
            font-weight: 600;
        }
        
        /* Streamlit komponensek testreszabása - Theme aware */
        .stButton > button {
            background-color: transparent;
            border: 1px solid var(--border-color, #2a2a2a);
            color: var(--text-color, white);
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .stButton > button:hover {
            background-color: var(--hover-color, rgba(42, 42, 42, 0.3));
            border-color: var(--border-hover-color, #3a3a3a);
            transform: translateY(-1px);
        }
        
        /* Unified Action Panel Dark Theme Integration */
        .unified-action-panel {
            background: transparent !important;
            border: 1px solid #3a3a3a !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        }
        
        .action-panel-header {
            background: transparent !important;
            color: #0099e0 !important;
            border: 1px solid #0099e0 !important;
            box-shadow: none !important;
        }
        
        .action-button-container {
            background: transparent !important;
            border: 1px solid #2a2a2a !important;
            color: #e0e0e0 !important;
        }
        
        .action-button-container:hover {
            background: rgba(42, 42, 42, 0.3) !important;
            border-color: #3a3a3a !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
        }
        
        .action-quick-info {
            background: transparent !important;
            border: 1px solid #0099e0 !important;
            color: #e0e0e0 !important;
        }
        
        .action-status-info {
            background: transparent !important;
            border: 1px solid #4caf50 !important;
            color: #4caf50 !important;
        }
        
        /* Metrics */
        [data-testid="metric-container"] {
            background-color: transparent;
            border: 1px solid var(--border-color, #2a2a2a);
            padding: 1rem;
            border-radius: 8px;
        }
        
        [data-testid="metric-container"] [data-testid="metric-label"] {
            color: #808080 !important;
        }
        
        [data-testid="metric-container"] [data-testid="metric-value"] {
            color: #ffffff !important;
        }
    </style>
    """, unsafe_allow_html=True)

def debug_html_css():
    """Debug függvény a HTML/CSS teszteléshez"""
    st.write("### 🔍 Debug: HTML/CSS Test")
    
    # 1. Alapvető HTML
    st.markdown('<div style="color: red; font-weight: bold;">✅ Basic HTML Test - This should be red</div>', 
                unsafe_allow_html=True)
    
    # 2. CSS osztály
    st.markdown("""
    <style>.debug-green { color: green; font-weight: bold; }</style>
    <div class="debug-green">✅ CSS Class Test - This should be green</div>
    """, unsafe_allow_html=True)
    
    # 3. Complex div structure test
    st.markdown("""
    <div style="background: transparent; color: var(--text-color, white); padding: 1rem; border-radius: 8px; margin: 1rem 0;
                border: 1px solid var(--border-color, #2a2a2a);">
        <h4 style="margin: 0 0 0.5rem 0; color: #0099e0;">✅ Complex Structure Test</h4>
        <p style="margin: 0; color: var(--text-color, #e0e0e0);">If you can see this styled box, complex HTML works!</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.info("💡 Nyisd meg a böngésző Developer Tools-t (F12) és nézd meg a Console és Elements füleket.")

def render_status_indicator(status):
    """Egyszerű státusz megjelenítés - working pattern alapján"""
    status_configs = {
        "CREATED": {"color": "#FFA07A", "bg": "rgba(255, 160, 122, 0.2)", "icon": "🆕", "text": "Létrehozva"},
        "CONFIRMED_BY_COMPANY": {"color": "#FFD700", "bg": "rgba(255, 215, 0, 0.2)", "icon": "✅", "text": "Visszaigazolva"},
        "ACCEPTED_BY_USER": {"color": "#98FB98", "bg": "rgba(152, 251, 152, 0.2)", "icon": "✔️", "text": "Elfogadva"},
        "REJECTED_BY_USER": {"color": "#FFB6C1", "bg": "rgba(255, 182, 193, 0.2)", "icon": "❌", "text": "Elutasítva"},
        "FINALIZED": {"color": "#87CEEB", "bg": "rgba(135, 206, 235, 0.2)", "icon": "🔒", "text": "Véglegesítve"}
    }
    
    config = status_configs.get(status, {"color": "#6c757d", "bg": "rgba(108, 117, 125, 0.2)", "icon": "❓", "text": status})
    
    return f"""
    <div style="display: inline-flex; align-items: center; gap: 0.5rem; 
                padding: 0.5rem 1rem; border-radius: 20px; font-weight: 600;
                background: {config['bg']}; border: 1px solid {config['color']};">
        <span style="font-size: 1.2rem;">{config['icon']}</span>
        <span style="color: {config['color']};">{config['text']}</span>
    </div>
    """

def render_simple_panel_header(title, icon="📋", accent_color="#0099e0"):
    """Panel header - egyszerű megjelenítés"""
    return f"""
    <div style="background: #1e2230; color: white; padding: 1rem; 
                border-radius: 8px 8px 0 0; font-weight: bold;
                border-left: 4px solid {accent_color}; margin-bottom: 0;
                border: 1px solid #3a3a3a;">
        <span style="font-size: 1.2rem; margin-right: 0.5rem;">{icon}</span>
        {title}
    </div>
    """


def render_simplified_enhanced_dark_theme_offer(offer):
    """Egyszerűsített enhanced dark theme renderelés - working patterns alapján"""
    
    # CSS injektálás
    inject_minimal_styles()
    
    # Debug opció
    if st.sidebar.checkbox("🔍 Debug HTML/CSS", value=False, key="debug_enhanced_dark"):
        debug_html_css()
        st.markdown("---")
    
    # Header with status - working pattern
    status_html = render_status_indicator(offer.get('status', 'CREATED'))
    
    header_html = f"""
    <div style="background: #1e2230; 
                border: 1px solid #3a3a3a; border-radius: 8px; 
                padding: 1.5rem; margin-bottom: 1.5rem;
                border-top: 4px solid #0099e0;">
        <h2 style="margin: 0 0 0.5rem 0; color: white;">Ajánlat #{offer.get('id', 'N/A')}</h2>
        <p style="color: #808080; margin: 0 0 1rem 0;">
            Utoljára módosítva: {_format_datetime(offer.get('updated_at') or offer.get('created_at'))}
        </p>
        {status_html}
    </div>
    """
    
    st.markdown(header_html, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Main content in columns
    col1, col2 = st.columns(2)
    
    with col1:
        # Offer info panel - enhanced with category and quality grade
        product = offer.get('product_type', {})
        
        # Kategória név meghatározása a category_id alapján
        category_id = product.get('category_id')
        category_name = "N/A"
        if category_id is not None:
            category_map = {
                1: "Paprika",
                2: "Paradicsom",
                3: "Uborka",
                4: "Gyökérzöldségek",
                5: "Káposztafélék"
            }
            category_name = category_map.get(category_id, f"Kategória #{category_id}")
        
        # Minőségi osztály kinyerese
        quality_grade = offer.get('quality_grade', {}) or {}
        quality_name = quality_grade.get('name', 'N/A') if quality_grade else 'N/A'
        
        # Panel header
        st.markdown(render_simple_panel_header("Ajánlat adatai", "📋", "#0099e0"), unsafe_allow_html=True)
        
        # Panel content - using components.html for complex content
        offer_html = f"""
        <div style="background: #1a1a1a; color: #e0e0e0; padding: 1rem; 
                    border-radius: 0 0 8px 8px; border-left: 4px solid #0099e0;
                    margin-top: -1px; margin-bottom: 1.5rem;
                    border: 1px solid #3a3a3a;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                <div style="color: #a0a0a0; font-weight: 500;">Azonosító:</div>
                <div style="color: #ffffff; font-weight: 600;">{offer.get('id', 'N/A')}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Beszállítás:</div>
                <div style="color: #ffffff; font-weight: 600;">{_format_date(offer.get('delivery_date'))}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Termék:</div>
                <div style="color: #ffffff; font-weight: 600;">{product.get('name', 'N/A')}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Kategória:</div>
                <div style="color: #ffffff; font-weight: 600;">{category_name}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Minőségi osztály:</div>
                <div style="color: #ffffff; font-weight: 600;">{quality_name}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Mennyiség:</div>
                <div style="color: #ffffff; font-weight: 600;">{_format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))} {offer.get('quantity_unit', 'kg')}</div>
            </div>
        </div>
        """
        components.html(offer_html, height=220)
        
        # Delivery info panel
        delivery_date = offer.get('delivery_date')
        delivery_status = _get_delivery_status(delivery_date)
        
        # Panel header
        st.markdown(render_simple_panel_header("Szállítási információk", "🚚", "#10dc60"), unsafe_allow_html=True)
        
        # Panel content
        delivery_html = f"""
        <div style="background: #1a1a1a; color: #e0e0e0; padding: 1rem; 
                    border-radius: 0 0 8px 8px; border-left: 4px solid #10dc60;
                    margin-top: -1px; margin-bottom: 1.5rem;
                    border: 1px solid #3a3a3a;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                <div style="color: #a0a0a0; font-weight: 500;">Szállítási dátum:</div>
                <div style="color: #ffffff; font-weight: 600;">{_format_date(delivery_date)}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Státusz:</div>
                <div style="color: #ffffff; font-weight: 600;">{delivery_status}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Szállítási cím:</div>
                <div style="color: #ffffff; font-weight: 600;">Központi raktár</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Szállítási mód:</div>
                <div style="color: #ffffff; font-weight: 600;">Saját szállítás</div>
            </div>
        </div>
        """
        components.html(delivery_html, height=160)
    
    with col2:
        # Confirmation panel with native Streamlit metrics
        st.markdown("### 📊 Visszaigazolás")
        quantity = _to_float(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))
        confirmed_quantity = _to_float(offer.get('confirmed_quantity', quantity))
        
        # Átlagos ajánlott ár meghatározása
        recommended_price = get_recommended_price(offer)
        confirmed_price = _to_float(offer.get('confirmed_price', recommended_price))
        progress = (confirmed_quantity / quantity) if quantity > 0 else 0
        
        # Debug információ a UI szintjén
        if st.session_state.get("debug_mode", False):
            st.write(f"🔍 **UI Debug**: recommended_price={recommended_price}, type={type(recommended_price)}")
            st.write(f"🔍 **UI Debug**: _format_price(recommended_price)={_format_price(recommended_price)}")
            
            # Timestamp hozzáadása a cache ellenőrzéshez
            from datetime import datetime
            current_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            st.write(f"🔍 **Cache Check**: {current_time}")
            st.write(f"🔍 **Offer Status**: {offer.get('status', 'UNKNOWN')}")
            
            # Hierarchia debug info
            product_type = offer.get('product_type', {}) or {}
            quality_grade = offer.get('quality_grade', {}) or {}
            st.write(f"🔍 **Pricing Hierarchy**: product_id={product_type.get('id')}, category_id={product_type.get('category_id')}, quality_id={quality_grade.get('id')}")
            
            # Summary of average price calculation
            st.info(f"💡 **Átlagár összegzés**: A {recommended_price:.2f} Ft/kg érték a product_id={product_type.get('id')} és quality_id={quality_grade.get('id')} kombinációra épül")
            
            # Explicit API teszt hívás
            if st.button("🔄 Frissítsd az átlagárat most!", key="refresh_avg_price"):
                # Clear any potential session state cache
                keys_to_clear = [k for k in st.session_state.keys() if 'price' in k.lower() or 'cache' in k.lower()]
                for key in keys_to_clear:
                    if key != "refresh_avg_price":  # Don't clear our button state
                        del st.session_state[key]
                st.rerun()
        
        # Metrics
        col_a, col_b = st.columns(2)
        with col_a:
            st.metric("Eredeti mennyiség", f"{_format_quantity(quantity)} kg")
            st.metric("Átlagos ajánlott ár", f"{_format_price(recommended_price)}/kg")
        
        with col_b:
            st.metric("Visszaigazolt", f"{_format_quantity(confirmed_quantity)} kg", f"{(progress*100):.0f}%")
            st.metric("Visszaigazolt ár", f"{_format_price(confirmed_price)}/kg")
        
        # Progress bar
        st.progress(progress)
        st.markdown(f"<p style='text-align: center; color: #808080;'>{progress*100:.1f}% teljesítve</p>", 
                   unsafe_allow_html=True)
        
        # Producer info panel
        user = offer.get('user', {})
        
        # Panel header
        st.markdown(render_simple_panel_header("Termelő adatai", "👤", "#ff8c1a"), unsafe_allow_html=True)
        
        # Panel content
        producer_html = f"""
        <div style="background: #1a1a1a; color: #e0e0e0; padding: 1rem; 
                    border-radius: 0 0 8px 8px; border-left: 4px solid #ff8c1a;
                    margin-top: -1px; margin-bottom: 1.5rem;
                    border: 1px solid #3a3a3a;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                <div style="color: #a0a0a0; font-weight: 500;">Név:</div>
                <div style="color: #ffffff; font-weight: 600;">{user.get('contact_name', 'N/A')}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Cégnév:</div>
                <div style="color: #ffffff; font-weight: 600;">{user.get('company_name', 'N/A')}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Telefon:</div>
                <div style="color: #ffffff; font-weight: 600;">{user.get('phone', 'N/A')}</div>
                
                <div style="color: #a0a0a0; font-weight: 500;">Email:</div>
                <div style="color: #ffffff; font-weight: 600;">{user.get('email', 'N/A')}</div>
            </div>
        </div>
        """
        components.html(producer_html, height=160)
    
    # Simple timeline with expander
    with st.expander("📅 Állapotváltozási napló", expanded=False):
        events = [
            {'date': offer.get('created_at'), 'title': 'Létrehozva', 'user': 'Rendszer'},
            {'date': offer.get('confirmed_at'), 'title': 'Cég által visszaigazolva', 'user': 'Kiss Péter'},
            {'date': offer.get('accepted_at'), 'title': 'Elfogadva', 'user': user.get('contact_name', 'N/A')}
        ]
        
        for event in events:
            if event['date']:
                st.markdown(f"**{event['title']}**  \n_{_format_datetime(event['date'])} • {event['user']}_")
    
    # Simple price chart with Plotly
    with st.expander("📈 Ár összehasonlítás", expanded=False):
        fig = go.Figure()
        
        # Átlagos ajánlott ár használata az eredeti helyett
        recommended_price = get_recommended_price(offer)
        categories = ['Ajánlott', 'Visszaigazolt']
        values = [
            recommended_price,
            _to_float(offer.get('confirmed_price', recommended_price))
        ]
        
        fig.add_trace(go.Bar(
            x=categories,
            y=values,
            marker_color=['#ff8c1a', '#10dc60'],
            text=[f'{v:,.0f} Ft' for v in values],
            textposition='outside',
            textfont=dict(color='#ffffff', size=14)
        ))
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#ffffff'),
            showlegend=False,
            height=300,
            margin=dict(l=20, r=20, t=30, b=20),
            yaxis=dict(gridcolor='#2a2a2a', title='Egységár (Ft/kg)'),
            xaxis=dict(gridcolor='#2a2a2a')
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    # Debug információk EXPANDER ELŐTT (ha debug mód be van kapcsolva)
    if st.session_state.get("debug_mode", False):
        st.info("🔍 Debug információk az árelemzéshez")
        product_type = offer.get('product_type', {}) or {}
        quality_grade = offer.get('quality_grade', {}) or {}
        col1, col2 = st.columns(2)
        with col1:
            st.write("**Termék ID:**", product_type.get('id'))
            st.write("**Kategória ID:**", product_type.get('category_id'))
        with col2:
            st.write("**Minőségi osztály ID:**", quality_grade.get('id'))
            st.write("**Ajánlat státusz:**", offer.get('status'))

    # Advanced Price Trend Analysis V2
    with st.expander("📈 Fejlett Ártrend Elemzés", expanded=False):
        # Toggle between simple and advanced view
        analysis_mode = st.radio(
            "Elemzés típusa:",
            ["🚀 Egyszerű (gyors)", "🔬 Fejlett (részletes)"],
            horizontal=True,
            key=f"analysis_mode_{offer.get('id', 0)}"
        )
        
        if analysis_mode == "🚀 Egyszerű (gyors)":
            # Simple optimized version (existing)
            st.markdown("**Gyors áttekintés az elmúlt időszakra**")
            
            # Performance info
            if st.checkbox("🚀 Performance infó", value=False, key=f"perf_info_{offer.get('id', 0)}"):
                st.info("""
                **Optimalizált betöltés:**
                - Egyetlen API hívás a teljes időszakra
                - Cache-elt adatok 5 percig
                - Timeout védelem (5 mp)
                """)
            
            # Időszak, felbontás és dátum típus választók
            col1, col2, col3 = st.columns(3)
            
            with col1:
                months_back = st.selectbox(
                    "Időszak:",
                    options=[3, 6, 12],  # Limitáljuk 12 hónapra
                    index=0,  # Default: 3 hónap
                    format_func=lambda x: f"{x} hónap",
                    key=f"price_trend_months_{offer.get('id', 0)}"
                )
            
            with col2:
                resolution = st.selectbox(
                    "Felbontás:",
                    options=["havi", "heti"],  # Napi opció eltávolítva
                    index=0,  # Default: havi
                    key=f"price_trend_resolution_{offer.get('id', 0)}"
                )
            
            with col3:
                date_type_simple = st.selectbox(
                    "📅 Dátum alapja:",
                    ["delivery_date", "confirmed_at", "created_at"],
                    index=0,  # Default: delivery_date
                    format_func=lambda x: {
                        "delivery_date": "🚚 Beszállítás",
                        "confirmed_at": "✅ Visszaigazolás", 
                        "created_at": "📝 Létrehozás"
                    }.get(x, x),
                    key=f"price_trend_date_type_{offer.get('id', 0)}"
                )
            
            # Frissítés gomb
            if st.button("🔄 Frissítés", key=f"refresh_price_trend_{offer.get('id', 0)}"):
                # Cache törlése erre az adatra
                get_price_trend_data.clear()
                st.rerun()
            
            # Intelligens időszak javaslat megjelenítése
            if st.session_state.get("debug_mode", False):
                try:
                    from .advanced_price_trend_v2 import analyze_offer_dates_for_trend
                    smart_suggestion = analyze_offer_dates_for_trend(offer)
                    if smart_suggestion:
                        st.info(f"🎯 **Intelligens javaslat**: {smart_suggestion['delivery_year']} évvel próbáld")
                        if smart_suggestion.get('has_date_mismatch'):
                            st.warning(f"⚠️ Dátum eltérés: Beszállítás {smart_suggestion['delivery_year']}, Létrehozás {smart_suggestion['created_year']}")
                except ImportError:
                    pass  # Skip if advanced module not available
            
            # Debug információk (debug módban)
            if st.session_state.get("debug_mode", False):
                st.info("🔍 Debug információk az árelemzéshez")
                
                # Alapadatok
                product_type = offer.get('product_type', {}) or {}
                quality_grade = offer.get('quality_grade', {}) or {}
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.write("**Termék ID:**", product_type.get('id'))
                    st.write("**Kategória ID:**", product_type.get('category_id'))
                with col2:
                    st.write("**Minőségi osztály ID:**", quality_grade.get('id'))
                    st.write("**Ajánlat státusz:**", offer.get('status'))
                with col3:
                    st.write("**Dátum mező:**", date_type_simple)
                    st.write("**Időszak:**", f"{months_back} hónap")
                    st.write("**Felbontás:**", resolution)
                
                # API hívás előkészítés infó
                st.markdown("**📡 API hívás paraméterei:**")
                api_params = {
                    'date_from': (datetime.now() - timedelta(days=months_back * 30)).strftime('%Y-%m-%d'),
                    'date_to': datetime.now().strftime('%Y-%m-%d'),
                    'product_type_id': product_type.get('id') if product_type.get('id') else None,
                    'quality_grade_id': quality_grade.get('id') if quality_grade.get('id') else None,
                    'category_id': product_type.get('category_id') if not product_type.get('id') else None
                }
                
                # Csak a valóban használt paramétereket mutassuk
                active_params = {k: v for k, v in api_params.items() if v is not None}
                
                # FIXED: Show actual API behavior and limitations
                if date_type_simple == 'delivery_date':
                    api_request_info = {
                        'api_params': active_params,
                        'filtering_method': 'API native (delivery_date)',
                        'explanation': f"Az API natívan támogatja a delivery_date szűrést"
                    }
                    filter_info = f"""
                    ✅ **API natív szűrés**: A backend API közvetlenül szűr delivery_date alapján.
                    
                    API végpont: `/api/offers/statistics`
                    Támogatott paraméterek: date_from, date_to (delivery_date-re vonatkozik)
                    
                    Ajánlat dátum mezői:
                    - `delivery_date`: {offer.get('delivery_date', 'N/A')}
                    - `created_at`: {offer.get('created_at', 'N/A')[:10] if offer.get('created_at') else 'N/A'}
                    - `updated_at`: {offer.get('updated_at', 'N/A')[:10] if offer.get('updated_at') else 'N/A'}
                    """
                else:
                    api_request_info = {
                        'api_params': active_params,
                        'filtering_method': f'Delivery_date only ({date_type_simple} not supported)',
                        'explanation': f"⚠️ API csak delivery_date szűrést támogat, {date_type_simple} figyelmen kívül hagyva"
                    }
                    filter_info = f"""
                    ⚠️ **FONTOS**: Az API NEM támogatja a {date_type_simple} szerinti szűrést!
                    
                    Jelenlegi megoldás: Az API delivery_date alapján szűr (date_from, date_to paraméterek)
                    
                    Javaslat: Használj delivery_date opciót a pontos eredményekhez, vagy fejlett módot manuális szűréshez.
                    
                    Ajánlat dátum mezői:
                    - `delivery_date`: {offer.get('delivery_date', 'N/A')} ✅ (API támogatja)
                    - `created_at`: {offer.get('created_at', 'N/A')[:10] if offer.get('created_at') else 'N/A'} ❌ (API nem támogatja)
                    - `confirmed_at`: {offer.get('confirmed_at', 'N/A')[:10] if offer.get('confirmed_at') else 'N/A'} ❌ (API nem támogatja)
                    """
                
                st.json(api_request_info)
                st.info(filter_info)
                
                # Időszakos aggregáció magyarázat
                st.markdown("**📊 Időszakos Aggregáció Logika:**")
                st.write(f"""
                **{resolution.upper()} felbontás** - {months_back} hónap időszakra:
                
                1. **Időszak felosztás**: Az elmúlt {months_back} hónap {resolution} időszakokra bontva
                2. **Ajánlatok gyűjtése**: Minden időszakban a `{date_type_simple}` alapján
                3. **Aggregáció per időszak**: 
                   - Ajánlatok száma időszakonként
                   - Átlagár számítása (súlyozott vagy egyszerű)
                   - Mennyiség alapú súlyozás
                4. **Trend kirajzolása**: Időszakok összekapcsolása
                
                **Példa**: Ha `delivery_date = 2024-04-08`, akkor az a 2024. április {resolution} időszakhoz tartozik.
                """)
                
                # Várható találatok számának becslése
                st.markdown("**📊 Adatszűrés előrejelzés:**")
                with st.spinner("Várható találatok számának becslése..."):
                    try:
                        # VAGY alternatív megközelítés: lekérjük az első nap adatait
                        test_params = active_params.copy()
                        test_params['date_to'] = test_params['date_from']
                        
                        # Egyszerű becslés logika - ezt később ki lehet fejleszteni
                        st.metric(
                            "Becsült időszak", 
                            f"{months_back} hónap",
                            help="API hívás időszaka"
                        )
                        
                        # Mutatjuk a szűrési hierarchiát
                        st.write("**Szűrési hierarchia:**")
                        if product_type.get('id'):
                            st.write("🎯 **Specifikus termék** (product_type_id)")
                            if quality_grade and quality_grade.get('id'):
                                st.write("  └── 🏅 **+ Minőségi osztály** (quality_grade_id)")
                            else:
                                st.write("  └── ⚪ **Minőségi osztály**: NULL/nincs")
                        elif product_type.get('category_id'):
                            st.write("📦 **Kategória szint** (category_id)")
                        else:
                            st.warning("⚠️ **Általános szűrés** - minden termék")
                        
                        # API Response struktúra debug - FIXED: Convert from nested expander to checkbox
                        st.markdown("**🔍 API Response Debug:**")
                        show_api_debug = st.checkbox("Mutasd az API Response debug adatokat", key=f"show_api_debug_{offer.get('id', 0)}")
                        
                        if show_api_debug:
                            st.write("**Offer objektum struktúra:**")
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                st.write("**Alapadatok:**")
                                basic_fields = {
                                    'id': offer.get('id'),
                                    'status': offer.get('status'),
                                    'status_display': offer.get('status_display'),
                                    'user_id': offer.get('user_id'),
                                    'product_type_id': offer.get('product_type_id'),
                                    'quality_grade_id': offer.get('quality_grade_id'),
                                    'delivery_date': offer.get('delivery_date'),
                                    'confirmed_price': offer.get('confirmed_price'),
                                    'confirmed_quantity': offer.get('confirmed_quantity')
                                }
                                st.json(basic_fields)
                            
                            with col2:
                                st.write("**Nested objektumok:**")
                                nested_info = {
                                    'user_keys': list(offer.get('user', {}).keys()) if offer.get('user') else None,
                                    'product_type_keys': list(offer.get('product_type', {}).keys()) if offer.get('product_type') else None,
                                    'quality_grade_keys': list(offer.get('quality_grade', {}).keys()) if offer.get('quality_grade') else None,
                                    'user_name_source': offer.get('user_name'),
                                    'product_name_source': offer.get('product_name')
                                }
                                st.json(nested_info)
                            
                    except Exception as e:
                        st.error(f"Debug info hiba: {str(e)}")
            
            # Grafikon renderelés
            render_price_trend_chart(offer, months_back, resolution, date_type_simple)
            
        else:
            # Advanced analysis V2
            st.markdown("**Teljes körű ártrend elemzés hierarchikus adatokkal**")
            
            try:
                # Import advanced price trend module
                from .advanced_price_trend_v2 import render_advanced_price_trend_analysis
                
                # Render advanced analysis
                render_advanced_price_trend_analysis(offer)
                
            except ImportError as e:
                st.error("🔧 A fejlett elemzés modul betöltése sikertelen")
                st.info("📄 Használd az egyszerű módot, vagy ellenőrizd a rendszer konfigurációt")
                
                # Debug info
                if st.checkbox("🔍 Debug infó", value=False, key=f"debug_advanced_{offer.get('id', 0)}"):
                    st.code(f"Import error: {str(e)}")
                    
            except Exception as e:
                st.error(f"Hiba a fejlett elemzés során: {str(e)}")
                st.info("📄 Használd az egyszerű módot")
                
                # Debug info
                if st.checkbox("🔍 Debug infó", value=False, key=f"debug_error_{offer.get('id', 0)}"):
                    st.code(f"Error details: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())

@st.cache_data(ttl=300)  # 5 perces cache
def get_price_trend_data(offer_id: int, product_id: int, category_id: int, 
                        quality_id: int, months_back: int = 6, 
                        resolution: str = "heti", date_type: str = "delivery_date") -> Dict[str, Any]:
    """
    Ártrend adatok lekérése - OPTIMALIZÁLT VERZIÓ
    Egyetlen API hívással lekéri az összes adatot, majd kliens oldalon dolgozza fel
    """
    from datetime import datetime, timedelta
    import logging
    
    logger = logging.getLogger(__name__)
    
    # Dátum tartomány
    end_date = datetime.now()
    start_date = end_date - timedelta(days=months_back * 30)
    
    # Egyetlen API hívás a teljes időszakra
    params = {
        'date_from': start_date.strftime('%Y-%m-%d'),
        'date_to': end_date.strftime('%Y-%m-%d')
        # REMOVED date_field parameter - API does not support it!
    }
    
    # Warning if trying to use non-delivery_date filtering
    if date_type != 'delivery_date':
        logger.warning(f"API doesn't support filtering by {date_type}, using delivery_date instead")
    
    # Termék/kategória paraméterek
    if product_id and quality_id:
        params['product_type_id'] = product_id
        params['quality_grade_id'] = quality_id
    elif product_id:
        params['product_type_id'] = product_id
    elif category_id:
        params['category_id'] = category_id
    
    # API hívás
    logger.info(f"API hívás paraméterek: {params}")
    
    try:
        stats_data = call_statistics_api_silent(params)
        
        if not stats_data:
            logger.warning("Nincs válasz az API-tól")
            return {'dates': [], 'prices': [], 'counts': [], 'labels': [], 'resolution': resolution}
        
        # Ha van daily_summary, azt használjuk
        if stats_data.get('daily_summary'):
            return process_daily_summary(stats_data['daily_summary'], resolution, start_date, end_date)
        
        # Ha nincs daily_summary, csak az átlagárat tudjuk megjeleníteni
        if stats_data.get('average_price') and stats_data.get('total_offers', 0) > 0:
            # Egyetlen adatpont a teljes időszakra
            avg_price = float(stats_data['average_price'])
            total_offers = stats_data['total_offers']
            
            return {
                'dates': [start_date, end_date],
                'prices': [avg_price, avg_price],
                'counts': [total_offers, total_offers],
                'labels': [start_date.strftime('%Y.%m'), end_date.strftime('%Y.%m')],
                'resolution': resolution,
                'single_value': True  # Jelezzük, hogy csak átlag van
            }
        
        logger.warning("Nincs feldolgozható adat")
        return {'dates': [], 'prices': [], 'counts': [], 'labels': [], 'resolution': resolution}
        
    except Exception as e:
        logger.error(f"Hiba az API hívás során: {str(e)}")
        return {'dates': [], 'prices': [], 'counts': [], 'labels': [], 'resolution': resolution}


def process_daily_summary(daily_data: list, resolution: str, start_date, end_date) -> Dict[str, Any]:
    """
    Napi adatok feldolgozása a kívánt felbontásra
    """
    from datetime import datetime, timedelta
    from collections import defaultdict
    
    if not daily_data:
        return {'dates': [], 'prices': [], 'counts': [], 'labels': [], 'resolution': resolution}
    
    # Adatok csoportosítása felbontás szerint
    grouped_data = defaultdict(lambda: {'total_value': 0, 'total_quantity': 0, 'count': 0})
    
    for day in daily_data:
        try:
            date = datetime.strptime(day.get('date', ''), '%Y-%m-%d')
            
            # Határozzuk meg a periódus kulcsot
            if resolution == "napi":
                period_key = date.strftime('%Y-%m-%d')
            elif resolution == "heti":
                # Hét kezdete (hétfő)
                week_start = date - timedelta(days=date.weekday())
                period_key = week_start.strftime('%Y-%m-%d')
            else:  # havi
                period_key = date.strftime('%Y-%m')
            
            # Adatok aggregálása
            if 'average_price' in day and 'total_quantity' in day:
                avg_price = float(day.get('average_price', 0))
                quantity = float(day.get('total_quantity', 0))
                
                if avg_price > 0 and quantity > 0:
                    grouped_data[period_key]['total_value'] += avg_price * quantity
                    grouped_data[period_key]['total_quantity'] += quantity
                    grouped_data[period_key]['count'] += day.get('total_offers', 1)
                    
        except Exception as e:
            continue
    
    # Rendezett lista készítése
    trend_data = {
        'dates': [],
        'prices': [],
        'counts': [],
        'labels': [],
        'resolution': resolution
    }
    
    for period_key in sorted(grouped_data.keys()):
        data = grouped_data[period_key]
        if data['total_quantity'] > 0:
            avg_price = data['total_value'] / data['total_quantity']
            
            # Dátum konvertálása
            if resolution == "napi":
                date = datetime.strptime(period_key, '%Y-%m-%d')
                label = date.strftime('%m.%d')
            elif resolution == "heti":
                date = datetime.strptime(period_key, '%Y-%m-%d')
                label = date.strftime('%m.%d')
            else:  # havi
                date = datetime.strptime(period_key + '-01', '%Y-%m-%d')
                label = date.strftime('%Y.%m')
            
            trend_data['dates'].append(date)
            trend_data['prices'].append(avg_price)
            trend_data['counts'].append(data['count'])
            trend_data['labels'].append(label)
    
    return trend_data


def call_statistics_api_silent(params: dict) -> dict:
    """
    Statistics API hívás SILENT módban - TIMEOUT-tal
    """
    import requests
    import streamlit as st
    import logging
    
    logger = logging.getLogger(__name__)
    
    try:
        # Config - use the same URL as the working API calls
        API_BASE_URL = "http://backend:8000"  # Docker container service name
        
        # Auth headers - use the same pattern as working API calls
        headers = {}
        
        # Import auth headers function like other API calls do
        try:
            from ...api.offers import get_auth_headers
            headers.update(get_auth_headers())
        except ImportError:
            try:
                from ..api.offers import get_auth_headers
                headers.update(get_auth_headers())
            except ImportError:
                try:
                    from streamlit_app.api.offers import get_auth_headers
                    headers.update(get_auth_headers())
                except ImportError:
                    # Fallback - try session state
                    if hasattr(st, 'session_state') and 'access_token' in st.session_state:
                        headers['Authorization'] = f"Bearer {st.session_state.access_token}"
        
        logger.info(f"Optimized API call to {API_BASE_URL}/api/offers/statistics with params: {params}")
        
        # API hívás rövidebb timeout-tal
        response = requests.get(
            f"{API_BASE_URL}/api/offers/statistics",
            params=params,
            headers=headers,
            timeout=5  # 5 másodperc timeout
        )
        
        logger.info(f"Optimized API response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Optimized API response keys: {list(result.keys()) if result else 'empty'}")
            return result
        else:
            logger.warning(f"Optimized API call failed with status {response.status_code}")
            return {}
            
    except requests.exceptions.Timeout:
        logger.error("Optimized API call timeout")
        st.error("⏱️ Az API nem válaszol időben. Próbáld újra később.")
        return {}
    except Exception as e:
        logger.error(f"Optimized API call error: {str(e)}")
        return {}

def render_price_trend_chart(offer: Dict[str, Any], months_back: int = 6, 
                           resolution: str = "heti", date_type: str = "delivery_date"):
    """
    Ártrend grafikon megjelenítése - OPTIMALIZÁLT
    """
    # Loading indicator
    with st.spinner('Ártrend adatok betöltése...'):
        # Ajánlat adatok kinyerése
        offer_id = offer.get('id', 0)
        product_type = offer.get('product_type', {}) or {}
        quality_grade = offer.get('quality_grade', {}) or {}
        
        product_id = product_type.get('id')
        category_id = product_type.get('category_id')
        quality_id = quality_grade.get('id')
        
        # Ártrend adatok lekérése - EZ MÁR CACHE-ELT!
        trend_data = get_price_trend_data(
            offer_id=offer_id,
            product_id=product_id,
            category_id=category_id,
            quality_id=quality_id,
            months_back=months_back,
            resolution=resolution,
            date_type=date_type
        )
    
    # Ha csak egyetlen átlagérték van
    if trend_data.get('single_value'):
        st.info("ℹ️ Csak összesített átlagár érhető el erre az időszakra")
        
        # Átlagár megjelenítése
        if trend_data.get('prices'):
            avg_price = trend_data['prices'][0]
            offer_count = trend_data['counts'][0] if trend_data.get('counts') else 0
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric(
                    f"Átlagár ({months_back} hónap)", 
                    f"{avg_price:,.0f} Ft/kg"
                )
            with col2:
                st.metric("Összes ajánlat", f"{offer_count} db")
        
        # Alternatív javaslatok
        st.info("💡 **Próbáld ki:**")
        st.write("- Rövidebb időszakkal (3 hónap)")
        st.write("- Más termék típussal")
        st.write("- Kategória szintű kereséssel")
        
        return
    
    # Ha nincs elég adat
    if not trend_data.get('dates') or len(trend_data['dates']) < 2:
        st.info("📈 Nincs elegendő történeti adat az ártrend megjelenítéséhez")
        
        # Alternatív megjelenítés
        show_current_price_info(offer)
        
        return
    
    # Grafikon létrehozása (változatlan)
    import plotly.graph_objects as go
    
    fig = go.Figure()
    
    # Ártrend vonal
    fig.add_trace(go.Scatter(
        x=trend_data['dates'],
        y=trend_data['prices'],
        mode='lines+markers',
        name='Átlagár',
        line=dict(color='#0099e0', width=3),
        marker=dict(size=8, color='#0099e0'),
        text=[f"{label}<br>{count} ajánlat" 
              for label, count in zip(trend_data['labels'], trend_data['counts'])],
        hovertemplate='<b>%{text}</b><br>Átlagár: %{y:,.0f} Ft/kg<extra></extra>'
    ))
    
    # Aktuális ajánlott ár vonal
    current_price = get_recommended_price(offer)
    if current_price > 0:
        fig.add_hline(
            y=current_price,
            line_dash="dash",
            line_color="#ff8c1a",
            annotation_text=f"Jelenlegi ajánlott ár: {current_price:,.0f} Ft/kg",
            annotation_position="bottom right"
        )
    
    # Grafikon formázása
    product_name = product_type.get('name', 'Termék')
    
    # Dátum típus jelzése
    date_type_labels = {
        "delivery_date": "beszállítási dátum",
        "confirmed_at": "visszaigazolási dátum", 
        "created_at": "létrehozási dátum"
    }
    date_label = date_type_labels.get(date_type, date_type)
    
    fig.update_layout(
        title=dict(
            text=f"📈 {product_name} - Átlagár változás ({resolution} bontás, {months_back} hónap, {date_label})",
            font=dict(color='#ffffff', size=16)
        ),
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font=dict(color='#ffffff'),
        showlegend=False,
        height=400,
        margin=dict(l=50, r=50, t=60, b=60),
        xaxis=dict(
            gridcolor='#2a2a2a',
            title='Időszak',
            tickangle=-45 if resolution == "napi" and len(trend_data['dates']) > 20 else 0
        ),
        yaxis=dict(
            gridcolor='#2a2a2a',
            title='Egységár (Ft/kg)',
            tickformat=',.0f'
        ),
        hovermode='x unified'
    )
    
    # Grafikon megjelenítése
    st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    # Statisztikák (változatlan)
    if len(trend_data['prices']) >= 2:
        show_price_statistics(trend_data['prices'])

def show_current_price_comparison(offer: Dict[str, Any]):
    """
    Aktuális ár összehasonlítás megjelenítése, amikor nincs elég történeti adat
    """
    current_price = get_recommended_price(offer)
    confirmed_price = _to_float(offer.get('confirmed_price', current_price))
    
    st.info("💡 **Aktuális árak összehasonlítása**:")
    
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Ajánlott átlagár", f"{current_price:,.0f} Ft/kg")
    with col2:
        st.metric("Visszaigazolt ár", f"{confirmed_price:,.0f} Ft/kg")
    
    # Egyszerű oszlopdiagram az aktuális árakról
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=['Ajánlott', 'Visszaigazolt'],
        y=[current_price, confirmed_price],
        marker_color=['#ff8c1a', '#10dc60'],
        text=[f'{current_price:,.0f} Ft', f'{confirmed_price:,.0f} Ft'],
        textposition='outside'
    ))
    
    fig.update_layout(
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font=dict(color='#ffffff'),
        showlegend=False,
        height=200,
        margin=dict(l=20, r=20, t=20, b=20),
        yaxis=dict(gridcolor='#2a2a2a', title='Egységár (Ft/kg)'),
        xaxis=dict(gridcolor='#2a2a2a')
    )
    
    st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})

def try_show_any_historical_data(offer: Dict[str, Any], months_back: int):
    """
    Próbálkozás bármilyen történeti adat megjelenítésével.
    """
    try:
        # Próbáljunk lekérni minden ajánlatot erre a termékre/kategóriára
        product_type = offer.get('product_type', {}) or {}
        product_id = product_type.get('id')
        category_id = product_type.get('category_id')
        
        st.write("🔍 **Keresés történeti adatok után...**")
        
        # Import API
        try:
            from .api_client import get_statistics_average_price
        except ImportError:
            try:
                from api_client import get_statistics_average_price
            except ImportError:
                st.error("Nem sikerült betölteni az API klienst")
                return
        
        # Próbáljunk nagy időtartományt minden státusszal
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365 * 2)  # 2 év
        
        # Termék szintű keresés minden státusszal
        if product_id:
            success, avg_price = get_statistics_average_price(
                product_type_id=product_id,
                date_from=start_date.strftime('%Y-%m-%d'),
                date_to=end_date.strftime('%Y-%m-%d')
            )
            
            if success and avg_price is not None:
                st.success(f"✅ **Termék szintű historikus átlag** (2 év): {avg_price:,.0f} Ft/kg")
            else:
                st.warning(f"⚠️ **Termék szintű adat**: Nincs adat product_id={product_id}-re")
        
        # Kategória szintű keresés
        if category_id:
            success, avg_price = get_statistics_average_price(
                category_id=category_id,
                date_from=start_date.strftime('%Y-%m-%d'),
                date_to=end_date.strftime('%Y-%m-%d')
            )
            
            if success and avg_price is not None:
                st.success(f"✅ **Kategória szintű historikus átlag** (2 év): {avg_price:,.0f} Ft/kg")
            else:
                st.warning(f"⚠️ **Kategória szintű adat**: Nincs adat category_id={category_id}-re")
        
        # Általános információ
        st.info("💡 **Tipp**: Az ártrend grafikonhoz szükség van legalább 2 különböző időpontból származó visszaigazolt ajánlatra.")
        
    except Exception as e:
        st.error(f"Hiba a történeti adatok keresésekor: {str(e)}")

def get_statistics_price_silent(product_type_id=None, category_id=None, quality_grade_id=None, date_from=None, date_to=None):
    """
    Átlagár lekérése SILENT módban - nem használ Streamlit UI elemeket.
    
    Returns:
        float or None: Az átlagár vagy None ha nincs adat
    """
    try:
        # Import streamlit first to avoid scope issues
        import streamlit as st
        
        # Debug check (temporary) - FIXED: import st first
        debug_mode = hasattr(st, 'session_state') and st.session_state.get("debug_mode", False)
        
        # SKIP API_CLIENT - it has UI output that causes expander nesting issues
        # Go directly to fallback implementation
        
        # DIRECT HTTP API call (completely bypass UI-enabled offers.py)
        try:
            import requests
            
            # Import config for API URL
            try:
                import app_config as config
            except ImportError:
                try:
                    from streamlit_app import app_config as config
                except ImportError:
                    # Fallback config
                    class FallbackConfig:
                        API_BASE_URL = "http://api:8000"  # Docker container service name
                    config = FallbackConfig()
            
            # Import auth headers function
            try:
                from ..api.offers import get_auth_headers
            except ImportError:
                try:
                    from api.offers import get_auth_headers
                except ImportError:
                    try:
                        from streamlit_app.api.offers import get_auth_headers
                    except ImportError:
                        def get_auth_headers():
                            return {}
            
            # Build query parameters
            query_params = {}
            if product_type_id is not None:
                query_params['product_type_id'] = product_type_id
            if category_id is not None:
                query_params['category_id'] = category_id
            if quality_grade_id is not None:
                query_params['quality_grade_id'] = quality_grade_id
            if date_from is not None:
                query_params['date_from'] = date_from
            if date_to is not None:
                query_params['date_to'] = date_to
            
            # FONTOS: Explicit státusz szűrés a visszaigazolt ajánlatokra
            query_params['status'] = 'CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED'
            
            # Direct HTTP call to avoid UI code
            full_url = f"{config.API_BASE_URL}/offers/statistics"
            
            response = requests.get(
                full_url,
                params=query_params,
                headers=get_auth_headers(),
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data and isinstance(data, dict):
                    # Extract average_price from statistics
                    average_price_str = data.get('average_price')
                    total_offers = data.get('total_offers', 0)
                    
                    if average_price_str and total_offers > 0:
                        try:
                            average_price = float(average_price_str)
                            return average_price
                        except (ValueError, TypeError):
                            return None
                    elif total_offers > 0:
                        # Product exists but no average price
                        return 0.0
            
            return None
            
        except Exception:
            return None
        
    except Exception:
        # Silent mode - just return None, don't show errors
        return None

def get_recommended_price(offer: Dict[str, Any]) -> float:
    """
    Átlagos ajánlott ár meghatározása a korábbi visszaigazolások alapján.
    
    A hierarchia: kategória -> termék -> minőségi osztály
    Az ár számításánál figyelembe veszi az eddig visszaigazolt összes ajánlatot.
    
    FRISSÍTÉS: Explicit státusz szűrést alkalmazunk az API hívásoknál, hogy
    az újonnan véglegesített ajánlatok azonnal beszámítsanak az átlagárba.
    
    Args:
        offer: Az ajánlat adatai
        
    Returns:
        float: Az átlagos ajánlott ár
    """
    # Alap információk kinyerése az ajánlatból
    product_type = offer.get('product_type', {}) or {}
    quality_grade = offer.get('quality_grade', {}) or {}
    
    product_id = product_type.get('id')
    category_id = product_type.get('category_id')
    quality_id = quality_grade.get('id')
    
    # Itt következik a tényleges lekérdezés az API-n keresztül
    # Használjuk a meglévő API-t az átlagárak lekérdezéséhez
    
    # Próbáljuk meg importálni az API klienst
    get_average_confirmed_price = None
    try:
        from .api_client import get_average_confirmed_price
    except ImportError:
        try:
            from api_client import get_average_confirmed_price
        except ImportError:
            pass
    
    # Ha sikerült az import, próbáljuk meg az API hívásokat
    if get_average_confirmed_price is not None:
        try:
            # Debug information
            import streamlit as st
            if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                st.write(f"🔍 **get_recommended_price() Debug**")
                st.write(f"product_id={product_id}, category_id={category_id}, quality_id={quality_id}")
            
            # 1. Először próbáljuk meg a legspecifikusabb szintet - termék + minőségi osztály
            if product_id is not None and quality_id is not None:
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 Átlagár keresése: product_id={product_id}, quality_grade_id={quality_id}")
                
                success, avg_price = get_average_confirmed_price(
                    product_id=product_id, 
                    quality_grade_id=quality_id
                )
                
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 API eredmény (termék+minőség): success={success}, avg_price={avg_price}")
                
                if success:
                    if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                        if avg_price and avg_price > 0:
                            st.write(f"✅ **Sikeres átlagár**: {avg_price:.2f} Ft/kg (termék+minőség kombináció)")
                        else:
                            st.write(f"✅ **Termék+minőség létezik, de átlagár 0**: {avg_price} Ft/kg (termék+minőség kombináció)")
                    return avg_price if avg_price is not None else 0.0
            
            # 2. Ha nincs eredmény, próbáljuk csak a termék alapján
            if product_id is not None:
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 Átlagár keresése: product_id={product_id}")
                
                success, avg_price = get_average_confirmed_price(product_id=product_id)
                
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 API eredmény (termék): success={success}, avg_price={avg_price}")
                
                if success:
                    if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                        if avg_price and avg_price > 0:
                            st.write(f"✅ **Sikeres átlagár**: {avg_price:.2f} Ft/kg (csak termék)")
                        else:
                            st.write(f"✅ **Termék létezik, de átlagár 0**: {avg_price} Ft/kg (csak termék)")
                    return avg_price if avg_price is not None else 0.0
            
            # 3. Ha nincs eredmény, próbáljuk a kategória alapján
            if category_id is not None:
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 Átlagár keresése: category_id={category_id}")
                
                success, avg_price = get_average_confirmed_price(category_id=category_id)
                
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.write(f"🔍 API eredmény (kategória): success={success}, avg_price={avg_price}")
                
                if success and avg_price and avg_price > 0:
                    if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                        st.write(f"✅ **Sikeres átlagár**: {avg_price:.2f} Ft/kg (csak kategória)")
                    return avg_price
        except Exception as e:
            # Ha hiba van az API hívásokban, folytatjuk a fallback logikával
            try:
                import streamlit as st
                if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                    st.error(f"🔍 API hiba: {str(e)}")
            except:
                pass
    
    # 4. Ha egyik szinten sem találtunk átlagárat, használjuk az ajánlat saját árát
    # Ellenőrizzük különböző ár mezőket
    for price_field in ['price', 'confirmed_price', 'unit_price', 'base_price']:
        if price_field in offer and offer[price_field] is not None:
            try:
                price_value = float(offer[price_field])
                if price_value > 0:
                    return price_value
            except (ValueError, TypeError):
                continue
    
    # 5. Ha az ajánlatban sincs ár, utolsó lehetőségként 0-t adunk vissza
    return 0.0


# Helper functions
def _to_float(value):
    """Érték biztonságos float konvertálása"""
    try:
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
            return float(cleaned) if cleaned else 0.0
        return 0.0
    except (ValueError, TypeError):
        return 0.0

def _format_quantity(value):
    """Mennyiség formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f}"

def _format_price(value):
    """Ár formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f} Ft"

def _format_date(value):
    """Dátum formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d.")
        return "-"
    except:
        return "-"

def _format_datetime(value):
    """Dátum és idő formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d. %H:%M")
        return "-"
    except:
        return "-"

def _get_delivery_status(delivery_date):
    """Szállítási státusz meghatározása"""
    if not delivery_date:
        return "Nincs megadva"
    
    try:
        if isinstance(delivery_date, str):
            delivery_dt = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
        else:
            delivery_dt = delivery_date
        
        days_until = (delivery_dt - datetime.now()).days
        
        if days_until < 0:
            return f"Lejárt ({abs(days_until)} napja)"
        elif days_until == 0:
            return "Ma esedékes"
        elif days_until == 1:
            return "Holnap esedékes"
        else:
            return f"{days_until} nap múlva esedékes"
    except:
        return "Nincs megadva"

# Test function
def test_simplified_enhanced_theme():
    """Test function for the simplified theme"""
    st.title("🧪 Simplified Enhanced Dark Theme Test")
    
    # Test data
    test_offer = {
        'id': 14,
        'status': 'CONFIRMED_BY_COMPANY',
        'quantity_in_kg': 6000,
        'price': 555,
        'confirmed_quantity': 5555,
        'confirmed_price': 555,
        'created_at': '2025-05-22T10:24:00Z',
        'confirmed_at': '2025-05-23T08:15:00Z',
        'delivery_date': '2025-05-23T00:00:00Z',
        'updated_at': '2025-05-28T14:13:00Z',
        'user': {
            'contact_name': 'Szabó Gábor',
            'company_name': 'Gabor TÉSZ',
            'email': '<EMAIL>',
            'phone': None
        },
        'product_type': {
            'name': 'Hegyes erős paprika',
            'category': {'name': None}
        }
    }
    
    render_simplified_enhanced_dark_theme_offer(test_offer)


def show_current_price_info(offer: Dict[str, Any]):
    """
    Egyszerűsített árinfó megjelenítés
    """
    st.markdown("### 💰 Aktuális árinformációk")
    
    current_price = get_recommended_price(offer)
    if current_price > 0:
        st.metric("Ajánlott egységár", f"{current_price:,.0f} Ft/kg")
    
    st.info("""
    💡 **Tipp**: Az ártrend megjelenítéséhez több történeti adat szükséges.
    
    Próbálkozz:
    - Más termék típussal
    - Rövidebb időszakkal
    - Kategória szintű kereséssel
    """)


def show_price_statistics(prices: list):
    """
    Ár statisztikák megjelenítése
    """
    if len(prices) < 2:
        return
        
    first_price = prices[0]
    last_price = prices[-1]
    min_price = min(prices)
    max_price = max(prices)
    avg_price = sum(prices) / len(prices)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Kezdő ár", f"{first_price:,.0f} Ft/kg")
    
    with col2:
        st.metric("Záró ár", f"{last_price:,.0f} Ft/kg")
    
    with col3:
        change_percent = ((last_price - first_price) / first_price) * 100
        st.metric("Változás", f"{change_percent:+.1f}%")
    
    with col4:
        st.metric("Min-Max", f"{min_price:,.0f} - {max_price:,.0f}")