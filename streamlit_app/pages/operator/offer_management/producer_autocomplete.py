"""
Term<PERSON>ő (létrehozó) keresőmező autocomplete funkcionalitással.

Ez a modul egy fejlett, kereshető autocomplete komponenst biztosít a termelők kiválasztásához.
Azonnali keresési javaslatokkal és felhasználóbarát interakcióval segíti a termelők hatékony megtalálását.
"""
import streamlit as st
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# Logging beállítása
logger = logging.getLogger(__name__)

# Import API client with fallback for producers
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.api_client import get_producers
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.api_client import get_producers
    except ImportError:
        try:
            # Try direct local import
            from api_client import get_producers
        except ImportError:
            # Fallback implementation
            logger.warning("Could not import get_producers, using fallback")
            
            def get_producers():
                """Fallback producer API"""
                return False, "API client not available"

# Streamlit UI komponenseket importáljuk a hibajelzésekhez
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
        except ImportError:
            # Basic fallback implementations
            logger.warning("Could not import enhanced UI components, using fallbacks")
            show_inline_error = lambda msg: st.error(msg)
            show_inline_warning = lambda msg: st.warning(msg)
            show_inline_success = lambda msg: st.success(msg)
            show_inline_info = lambda msg: st.info(msg)

def inject_producer_autocomplete_styles():
    """
    Termelő autocomplete egyedi stílusainak injektálása.
    """
    # CSS stílusok a producer autocomplete-hez
    css = """
    <style>
    /* Producer autocomplete container */
    .producer-autocomplete-container {
        position: relative;
        margin: 10px 0;
        width: 100%;
    }
    
    /* Input mező */
    .producer-autocomplete-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 0.95em;
        transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .producer-autocomplete-input:focus {
        border-color: #3584e4;
        box-shadow: 0 0 0 2px rgba(53, 132, 228, 0.2);
        outline: none;
    }
    
    /* Eredmények konténer */
    .producer-autocomplete-results {
        position: absolute;
        width: 100%;
        max-height: 250px;
        overflow-y: auto;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        z-index: 100;
        margin-top: 2px;
    }
    
    /* Egyéni scrollbar stílusok */
    .producer-autocomplete-results::-webkit-scrollbar {
        width: 6px;
    }
    
    .producer-autocomplete-results::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    .producer-autocomplete-results::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }
    
    .producer-autocomplete-results::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* Találati elem */
    .producer-autocomplete-item {
        padding: 10px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f1f1f1;
        transition: background-color 0.2s;
    }
    
    .producer-autocomplete-item:last-child {
        border-bottom: none;
    }
    
    .producer-autocomplete-item:hover {
        background-color: rgba(53, 132, 228, 0.1);
    }
    
    .producer-autocomplete-item.selected {
        background-color: rgba(53, 132, 228, 0.15);
    }
    
    /* Producer adatok */
    .producer-name {
        font-weight: 500;
        margin-bottom: 2px;
    }
    
    .producer-email {
        font-size: 0.85em;
        color: #666;
    }
    
    .producer-contact {
        font-size: 0.85em;
        color: #666;
    }
    
    /* Kiemelés a keresett szövegben */
    .producer-autocomplete-highlight {
        font-weight: bold;
        color: #3584e4;
        background-color: rgba(53, 132, 228, 0.1);
        padding: 0 2px;
        border-radius: 2px;
    }
    
    /* Betöltés indikátor */
    .producer-autocomplete-loading {
        text-align: center;
        padding: 10px;
        color: #666;
        font-style: italic;
    }
    
    /* Nincs találat üzenet */
    .producer-autocomplete-no-results {
        text-align: center;
        padding: 12px;
        color: #666;
        font-style: italic;
    }
    
    /* Clear button */
    .producer-autocomplete-clear {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #999;
        font-size: 16px;
        visibility: hidden;
    }
    
    .producer-autocomplete-input:hover + .producer-autocomplete-clear,
    .producer-autocomplete-clear:hover {
        visibility: visible;
    }
    
    .producer-autocomplete-input:not(:placeholder-shown) + .producer-autocomplete-clear {
        visibility: visible;
    }
    </style>
    """
    
    # JavaScript a producer autocomplete működéséhez
    js = """
    <script>
    // Termelő autocomplete funkció inicializálása DOM betöltés után
    document.addEventListener('DOMContentLoaded', function() {
        // Termelő autocomplete komponensek keresése
        const containers = document.querySelectorAll('[id^="producer_autocomplete_container_"]');
        
        containers.forEach(container => {
            setupProducerAutocomplete(container);
        });
        
        // MutationObserver a dinamikus komponensek kezeléséhez
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && 
                            node.id && 
                            node.id.startsWith('producer_autocomplete_container_')) {
                            setupProducerAutocomplete(node);
                        }
                    });
                }
            });
        });
        
        // Az egész body figyelése a dinamikusan hozzáadott elemekhez
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Termelő autocomplete inicializálása
        function setupProducerAutocomplete(container) {
            const containerId = container.id;
            const componentId = containerId.replace('producer_autocomplete_container_', '');
            
            // Elemek kiválasztása
            const input = container.querySelector('.producer-autocomplete-input');
            const resultsContainer = container.querySelector('.producer-autocomplete-results');
            const clearButton = container.querySelector('.producer-autocomplete-clear');
            
            // Producereket a data attribútumból olvassa
            let producers = [];
            try {
                const producersJson = container.getAttribute('data-producers');
                if (producersJson) {
                    producers = JSON.parse(producersJson);
                }
            } catch (e) {
                console.error('Error parsing producers data:', e);
            }
            
            let selectedIndex = -1;
            let resultsVisible = false;
            
            // Input eseménykezelő
            if (input) {
                // Keresési esemény
                input.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length >= 1) {
                        showResults(query);
                    } else {
                        hideResults();
                    }
                    
                    // Frissítjük a keresőszöveget Streamlit-ben
                    updateSearchText(query);
                });
                
                // Focus és blur események
                input.addEventListener('focus', function() {
                    if (this.value.trim().length > 0) {
                        showResults(this.value.trim());
                    }
                });
                
                // Billentyűkezelés
                input.addEventListener('keydown', function(e) {
                    // Ha eredmények láthatók
                    if (resultsVisible) {
                        const items = resultsContainer.querySelectorAll('.producer-autocomplete-item');
                        
                        if (e.key === 'ArrowDown') {
                            e.preventDefault();
                            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                            highlightSelectedItem();
                            scrollToSelectedItem();
                        } else if (e.key === 'ArrowUp') {
                            e.preventDefault();
                            selectedIndex = Math.max(selectedIndex - 1, 0);
                            highlightSelectedItem();
                            scrollToSelectedItem();
                        } else if (e.key === 'Enter' && selectedIndex >= 0 && items.length > 0) {
                            e.preventDefault();
                            selectItem(items[selectedIndex]);
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            hideResults();
                        }
                    }
                });
            }
            
            // Clear button eseménykezelő
            if (clearButton) {
                clearButton.addEventListener('click', function() {
                    if (input) {
                        input.value = '';
                        hideResults();
                        
                        // Értesítjük Streamlit-et a törlésről
                        const hiddenInput = document.getElementById(`producer_autocomplete_selected_${componentId}`);
                        if (hiddenInput) {
                            hiddenInput.value = JSON.stringify({ value: null, label: '' });
                            hiddenInput.dispatchEvent(new Event('change'));
                        }
                        
                        // Frissítjük a keresőszöveget
                        updateSearchText('');
                    }
                });
            }
            
            // Kattintás kezelése a dokumentumon
            document.addEventListener('click', function(e) {
                // Ha kívül kattintottak, elrejtjük az eredményeket
                if (resultsVisible && !container.contains(e.target)) {
                    hideResults();
                }
            });
            
            // Keresési eredmények megjelenítése
            function showResults(query) {
                resultsVisible = true;
                resultsContainer.style.display = 'block';
                query = query.toLowerCase();
                
                // Eredmények szűrése
                const filteredProducers = producers.filter(producer => {
                    // Több mezőben is keres
                    const searchIn = [
                        producer.name || '',
                        producer.company_name || '',
                        producer.contact_name || '',
                        producer.full_name || '',
                        producer.email || '',
                        producer.phone || ''
                    ].map(field => field.toLowerCase());
                    
                    // Ha bármelyik mezőben szerepel a keresett szöveg
                    return searchIn.some(field => field.includes(query));
                }).slice(0, 10);  // Maximum 10 találat
                
                // Eredmények megjelenítése
                if (filteredProducers.length > 0) {
                    let resultsHtml = '';
                    
                    filteredProducers.forEach((producer, index) => {
                        const displayName = getProducerDisplayName(producer);
                        const displayContact = getProducerContactInfo(producer);
                        const email = producer.email || '';
                        
                        // Keresett szöveg kiemelése
                        const highlightedName = highlightText(displayName, query);
                        const highlightedContact = highlightText(displayContact, query);
                        const highlightedEmail = highlightText(email, query);
                        
                        resultsHtml += `
                        <div class="producer-autocomplete-item" data-index="${index}" 
                            data-id="${producer.id}" data-name="${displayName}">
                            <div class="producer-name">${highlightedName}</div>
                            ${displayContact ? `<div class="producer-contact">${highlightedContact}</div>` : ''}
                            ${email ? `<div class="producer-email">${highlightedEmail}</div>` : ''}
                        </div>`;
                    });
                    
                    resultsContainer.innerHTML = resultsHtml;
                    
                    // Eseménykezelők hozzáadása
                    const items = resultsContainer.querySelectorAll('.producer-autocomplete-item');
                    items.forEach(item => {
                        item.addEventListener('click', function() {
                            selectItem(this);
                        });
                        
                        item.addEventListener('mouseover', function() {
                            selectedIndex = parseInt(this.getAttribute('data-index'));
                            highlightSelectedItem();
                        });
                    });
                    
                    // Az első elem kiválasztása alapértelmezetten
                    selectedIndex = 0;
                    highlightSelectedItem();
                } else {
                    resultsContainer.innerHTML = '<div class="producer-autocomplete-no-results">Nincs találat</div>';
                    selectedIndex = -1;
                }
            }
            
            // Eredmények elrejtése
            function hideResults() {
                resultsContainer.style.display = 'none';
                resultsVisible = false;
            }
            
            // Kiválasztott elem kiemelése
            function highlightSelectedItem() {
                const items = resultsContainer.querySelectorAll('.producer-autocomplete-item');
                
                items.forEach((item, index) => {
                    if (index === selectedIndex) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });
            }
            
            // Scroll a kiválasztott elemhez
            function scrollToSelectedItem() {
                const items = resultsContainer.querySelectorAll('.producer-autocomplete-item');
                if (selectedIndex >= 0 && selectedIndex < items.length) {
                    const selectedItem = items[selectedIndex];
                    const containerTop = resultsContainer.scrollTop;
                    const containerBottom = containerTop + resultsContainer.clientHeight;
                    const itemTop = selectedItem.offsetTop;
                    const itemBottom = itemTop + selectedItem.clientHeight;
                    
                    if (itemTop < containerTop) {
                        resultsContainer.scrollTop = itemTop;
                    } else if (itemBottom > containerBottom) {
                        resultsContainer.scrollTop = itemBottom - resultsContainer.clientHeight;
                    }
                }
            }
            
            // Elem kiválasztása
            function selectItem(item) {
                const producerId = item.getAttribute('data-id');
                const producerName = item.getAttribute('data-name');
                
                // Beállítjuk az input értékét
                if (input) {
                    input.value = producerName;
                }
                
                // Elrejtjük az eredményeket
                hideResults();
                
                // Értesítjük Streamlit-et a kiválasztásról
                const hiddenInput = document.getElementById(`producer_autocomplete_selected_${componentId}`);
                if (hiddenInput) {
                    hiddenInput.value = JSON.stringify({ id: producerId, name: producerName });
                    hiddenInput.dispatchEvent(new Event('change'));
                }
            }
            
            // Keresőszöveg frissítése Streamlit-ben
            function updateSearchText(text) {
                const searchInput = document.getElementById(`producer_autocomplete_search_${componentId}`);
                if (searchInput && searchInput.value !== text) {
                    searchInput.value = text;
                    searchInput.dispatchEvent(new Event('change'));
                }
            }
            
            // Termelő megjelenítendő nevének meghatározása
            function getProducerDisplayName(producer) {
                return producer.contact_name || 
                       producer.company_name || 
                       producer.full_name || 
                       producer.name || 
                       `Termelő #${producer.id}`;
            }
            
            // Termelő kapcsolattartási infójának meghatározása
            function getProducerContactInfo(producer) {
                if (producer.phone) {
                    return producer.phone;
                } else if (producer.address) {
                    return producer.address;
                } else if (producer.company_name && producer.contact_name && producer.company_name !== producer.contact_name) {
                    return producer.company_name;
                }
                return '';
            }
            
            // Szövegrész kiemelése
            function highlightText(text, query) {
                if (!text || !query) return text || '';
                
                // Escape special regex characters
                const escapeRegExp = (string) => {
                    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                };
                
                const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
                return text.replace(regex, '<span class="producer-autocomplete-highlight">$1</span>');
            }
        }
    });
    </script>
    """
    
    try:
        st.markdown(css + js, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a producer autocomplete stílusok injektálásakor: {str(e)}")


def render_producer_autocomplete(
    producers: Optional[List[Dict]] = None,
    default_id: Optional[Union[int, str]] = None,
    placeholder: str = "Termelő keresése...",
    key: Optional[str] = None,
    include_all_option: bool = True,
    label: Optional[str] = None
) -> Optional[Union[int, str]]:
    """
    Termelő autocomplete komponens renderelése.
    
    Ez a funkció egy kereshető autocomplete komponenst jelenít meg a termelők kiválasztásához.
    Ha a producers lista nincs megadva, automatikusan lekéri a termelőket az API-ból.
    
    Args:
        producers: Termelők listája (opcionális, ha nincs megadva, lekéri az API-ból)
        default_id: Alapértelmezetten kiválasztott termelő ID (None = nincs kiválasztva)
        placeholder: Placeholder szöveg a keresőmezőben
        key: Egyedi azonosító a komponenshez
        include_all_option: Legyen-e "Minden termelő" opció
        label: Opcionális címke a komponens fölött
        
    Returns:
        Optional[Union[int, str]]: A kiválasztott termelő azonosítója vagy None
    """
    # Egyedi azonosító
    component_id = key or f"producer_autocomplete_{str(uuid.uuid4())[:8]}"
    
    # Session state kulcsok
    selected_key = f"{component_id}_selected"
    search_key = f"{component_id}_search"
    
    # Session state inicializálása
    if selected_key not in st.session_state:
        st.session_state[selected_key] = default_id
    
    if search_key not in st.session_state:
        st.session_state[search_key] = ""
    
    # Ha nincs megadva producers lista, lekérjük az API-ból
    if producers is None:
        success, result = get_producers()
        if success and isinstance(result, list):
            producers = result
        else:
            show_inline_warning("Nem sikerült betölteni a termelők listáját.")
            producers = []
    
    # Ha van "Minden termelő" opció, hozzáadjuk
    if include_all_option:
        # Megkeressük, hogy van-e már "Minden termelő" opció
        all_option_exists = any(p.get("id") is None for p in producers)
        
        if not all_option_exists:
            all_option = {
                "id": None,
                "name": "Minden termelő",
                "contact_name": "Minden termelő",
                "email": ""
            }
            producers = [all_option] + producers
    
    # Keressük meg a kiválasztott termelőt
    selected_producer = next((p for p in producers if str(p.get("id")) == str(st.session_state[selected_key])), None)
    selected_name = ""
    if selected_producer:
        selected_name = (
            selected_producer.get("contact_name") or 
            selected_producer.get("company_name") or 
            selected_producer.get("full_name") or 
            selected_producer.get("name") or 
            f"Termelő #{selected_producer.get('id')}"
        )
    
    # Címke megjelenítése, ha van
    if label:
        st.write(label)
    
    # Styles injektálása
    inject_producer_autocomplete_styles()
    
    # A komponens HTML
    st.markdown(f"""
    <div id="producer_autocomplete_container_{component_id}" class="producer-autocomplete-container"
         data-producers='{json.dumps(producers)}'>
        <input type="text" class="producer-autocomplete-input" id="producer_autocomplete_input_{component_id}"
               placeholder="{placeholder}" value="{selected_name}">
        <span class="producer-autocomplete-clear" id="producer_autocomplete_clear_{component_id}">×</span>
        <div class="producer-autocomplete-results" id="producer_autocomplete_results_{component_id}" style="display: none;">
            <!-- Az eredmények dinamikusan töltődnek be -->
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Rejtett input mezők a Streamlit állapot kezeléséhez
    selected_json = st.text_input(
        "Producer Selected",
        value="",
        key=f"producer_autocomplete_selected_{component_id}",
        label_visibility="collapsed"
    )
    
    search_text = st.text_input(
        "Producer Search",
        value=st.session_state[search_key],
        key=f"producer_autocomplete_search_{component_id}",
        label_visibility="collapsed"
    )
    
    # Keresőszöveg frissítése session state-ben
    if search_text != st.session_state[search_key]:
        st.session_state[search_key] = search_text
    
    # Kiválasztott termelő frissítése
    if selected_json:
        try:
            data = json.loads(selected_json)
            producer_id = data.get("id")
            
            # String ID átalakítása számmá, ha lehetséges (API kompatibilitás miatt)
            if producer_id is not None and producer_id != "null" and producer_id != "":
                try:
                    producer_id = int(producer_id)
                except (ValueError, TypeError):
                    pass
            else:
                producer_id = None
            
            # Frissítjük a session state-t
            st.session_state[selected_key] = producer_id
            
            # Rerun szükséges a kiválasztás frissítéséhez
            st.rerun()
        except Exception as e:
            logger.error(f"Hiba a kiválasztott termelő feldolgozása során: {str(e)}")
    
    return st.session_state[selected_key]


# Példa használat, ha ezt a modult közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(page_title="Termelő Autocomplete Komponens", layout="wide")
    
    st.title("Termelő Autocomplete Komponens Demó")
    
    # Példa termelők
    sample_producers = [
        {"id": 1, "name": "Kovács János", "contact_name": "Kovács János", "email": "<EMAIL>", "phone": "+36301234567"},
        {"id": 2, "name": "Nagy Kft.", "company_name": "Nagy Kft.", "contact_name": "Nagy Péter", "email": "<EMAIL>", "phone": "+36201234567"},
        {"id": 3, "name": "Példa Zrt.", "company_name": "Példa Zrt.", "contact_name": "Példa Béla", "email": "<EMAIL>"},
        {"id": 4, "name": "Kiss Balázs", "contact_name": "Kiss Balázs", "email": "<EMAIL>"},
        {"id": 5, "name": "Agrár Kft.", "company_name": "Agrár Kft.", "contact_name": "Szabó Gábor", "email": "<EMAIL>", "phone": "+36701234567"},
        {"id": 6, "name": "Mezőgazdasági Bt.", "company_name": "Mezőgazdasági Bt.", "contact_name": "Molnár József", "email": "<EMAIL>"},
        {"id": 7, "name": "Tóth Zsolt", "contact_name": "Tóth Zsolt", "email": "<EMAIL>", "phone": "+36501234567"},
        {"id": 8, "name": "Zöldség-Gyümölcs Kft.", "company_name": "Zöldség-Gyümölcs Kft.", "contact_name": "Varga László", "email": "<EMAIL>"},
        {"id": 9, "name": "Szántó János", "contact_name": "Szántó János", "email": "<EMAIL>"},
        {"id": 10, "name": "Termelő Kft.", "company_name": "Termelő Kft.", "contact_name": "Fekete Zoltán", "email": "<EMAIL>", "phone": "+36601234567"},
    ]
    
    # Komponens kipróbálása
    st.subheader("Alapértelmezett termelő autocomplete")
    selected_producer_id = render_producer_autocomplete(
        producers=sample_producers,
        default_id=None,
        placeholder="Termelő kiválasztása...",
        key="demo_producer",
        label="Válasszon termelőt:"
    )
    
    # Eredmény megjelenítése
    if selected_producer_id is not None:
        selected_producer = next((p for p in sample_producers if p["id"] == selected_producer_id), None)
        if selected_producer:
            st.success(f"""
            Kiválasztott termelő:  
            **ID:** {selected_producer_id}  
            **Név:** {selected_producer.get('contact_name') or selected_producer.get('name')}  
            **Email:** {selected_producer.get('email', '-')}
            """)
    else:
        st.info("Minden termelő kiválasztva (nincs szűrés)")
    
    # Keresési tippek
    st.subheader("Keresési tippek")
    st.markdown("""
    Hatékony kereséséhez:
    
    - Kezdje el begépelni a termelő nevét vagy kapcsolattartó nevét
    - Kereshet email cím vagy telefonszám alapján is
    - A nyilakkal mozoghat a találatok között
    - Enter billentyűvel választhatja ki a kijelölt termelőt
    - A "×" gombbal törölheti a kiválasztást
    """)
    
    # További példa komponens
    st.subheader("Termelő autocomplete alapértelmezett kiválasztással")
    selected_producer_id2 = render_producer_autocomplete(
        producers=sample_producers,
        default_id=5,
        placeholder="Kezdjen el gépelni...",
        key="demo_producer2",
        include_all_option=False
    )
    
    # Eredmény megjelenítése
    if selected_producer_id2 is not None:
        selected_producer = next((p for p in sample_producers if p["id"] == selected_producer_id2), None)
        if selected_producer:
            st.success(f"""
            Kiválasztott termelő:  
            **ID:** {selected_producer_id2}  
            **Név:** {selected_producer.get('contact_name') or selected_producer.get('name')}  
            **Email:** {selected_producer.get('email', '-')}
            """)
    else:
        st.info("Nincs termelő kiválasztva")
    
    # Fejlesztői információk
    with st.expander("Fejlesztői információk"):
        st.markdown("""
        **Modul:** producer_autocomplete.py
        
        **Fő függvények:**
        - `render_producer_autocomplete`: Interaktív termelő autocomplete komponens
        - `inject_producer_autocomplete_styles`: CSS és JavaScript injektálása
        
        **Technológiai megoldások:**
        - Egyedi CSS és JavaScript a fejlett autocomplete működéshez
        - Session state használata az állapot kezeléséhez
        - Rejtett inputok a JavaScript-Streamlit kommunikációhoz
        - Rugalmas API integráció a termelők lekéréséhez
        
        **Komponens funkciók:**
        - Valós idejű keresés a termelő adatokban
        - Keresett szövegrész kiemelése a találatokban
        - Billentyűzet navigáció a találatok között
        - Responsive design, működik mobil eszközökön is
        - Egyszerű törlés és újraválasztás
        """)