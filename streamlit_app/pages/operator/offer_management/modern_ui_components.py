#!/usr/bin/env python3
"""
Modern UI Components for Offer Management
Egyszerűsített, valós API-t használó UI komponensek
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, date, timedelta
import logging

from .data_models import Offer, FilterParams, OfferStatistics, Producer, Product
from .api_service import get_api_service

logger = logging.getLogger(__name__)

class ModernFilterPanel:
    """
    Modern szűrőpanel valós API adatokkal
    Implementálja a ui.md javaslatokat
    """
    
    def __init__(self):
        self.api_service = get_api_service()
        self._inject_modern_styles()
    
    def _inject_modern_styles(self):
        """Modern CSS stílusok injektálása"""
        st.markdown("""
        <style>
        .modern-filter-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            overflow: hidden;
            animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .filter-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.25rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .filter-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .filter-body {
            padding: 1.5rem;
            background: #fafbfc;
        }
        
        .filter-section {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e8eaed;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .filter-section:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .active-filter-badge {
            display: inline-flex;
            align-items: center;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            margin: 0.25rem 0.25rem 0.25rem 0;
            border: 1px solid #bbdefb;
            font-weight: 500;
            animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInFromTop {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def render(self) -> FilterParams:
        """
        Szűrőpanel renderelése és aktív szűrők visszaadása
        
        Returns:
            FilterParams: Aktuális szűrési paraméterek
        """
        st.markdown('<div class="modern-filter-panel">', unsafe_allow_html=True)
        
        # Header
        self._render_header()
        
        # Ellenőrizzük, hogy a panel nyitva van-e
        is_expanded = st.session_state.get('filter_panel_expanded', True)
        
        if is_expanded:
            st.markdown('<div class="filter-body">', unsafe_allow_html=True)
            
            # Szűrő űrlap
            filters = self._render_filter_form()
            
            # Aktív szűrők megjelenítése
            self._render_active_filters(filters)
            
            st.markdown('</div>', unsafe_allow_html=True)
        else:
            # Összecsukott állapotban is visszaadjuk a session state-ből a szűrőket
            filters = FilterParams.from_session_state(st.session_state)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        return filters
    
    def _render_header(self):
        """Szűrőpanel header renderelése"""
        is_expanded = st.session_state.get('filter_panel_expanded', True)
        expand_icon = "🔽" if is_expanded else "▶️"
        
        header_html = f"""
        <div class="filter-header" onclick="toggleFilterPanel()">
            <span>{expand_icon} 🔍 Ajánlatok szűrése</span>
            <span>🔄</span>
        </div>
        """
        
        st.markdown(header_html, unsafe_allow_html=True)
        
        # JavaScript a panel toggle-höz
        st.markdown("""
        <script>
        function toggleFilterPanel() {
            // Streamlit callback trigger
            const expandedInput = document.getElementById('filter_panel_toggle');
            if (expandedInput) {
                expandedInput.value = expandedInput.value === 'true' ? 'false' : 'true';
                expandedInput.dispatchEvent(new Event('change'));
            }
        }
        </script>
        """, unsafe_allow_html=True)
        
        # Hidden input a toggle state kezeléséhez
        current_state = st.session_state.get('filter_panel_expanded', True)
        toggle_state = st.text_input(
            "Panel toggle",
            value=str(current_state).lower(),
            key="filter_panel_toggle_input",
            label_visibility="collapsed"
        )
        
        # State update
        if toggle_state != str(current_state).lower():
            st.session_state['filter_panel_expanded'] = toggle_state == 'true'
            st.rerun()
    
    def _render_filter_form(self) -> FilterParams:
        """Szűrő űrlap renderelése"""
        
        # API opciók betöltése
        filter_options = self._get_filter_options()
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Termelő szűrő
            st.markdown('<div class="filter-section">', unsafe_allow_html=True)
            st.markdown("**👤 Termelő**")
            
            producer_options = [""] + [p['name'] for p in filter_options['producers']]
            selected_producer_name = st.selectbox(
                "Válasszon termelőt:",
                producer_options,
                index=self._get_current_producer_index(producer_options),
                key="producer_filter_modern",
                label_visibility="collapsed"
            )
            
            # Producer ID meghatározása
            selected_producer_id = None
            if selected_producer_name:
                for producer in filter_options['producers']:
                    if producer['name'] == selected_producer_name:
                        selected_producer_id = producer['id']
                        break
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Időszak szűrő
            st.markdown('<div class="filter-section">', unsafe_allow_html=True)
            st.markdown("**📅 Időszak**")
            
            date_col1, date_col2 = st.columns(2)
            with date_col1:
                from_date = st.date_input(
                    "Kezdő dátum:",
                    value=st.session_state.get('from_date_filter_om', date.today() - timedelta(days=30)),
                    key="from_date_filter_modern"
                )
            
            with date_col2:
                to_date = st.date_input(
                    "Végső dátum:",
                    value=st.session_state.get('to_date_filter_om', date.today()),
                    key="to_date_filter_modern"
                )
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Keresés
            st.markdown('<div class="filter-section">', unsafe_allow_html=True)
            st.markdown("**🔍 Keresés**")
            
            search_term = st.text_input(
                "Keresési kifejezés:",
                value=st.session_state.get('search_filter_om', ''),
                placeholder="Termék neve, termelő, megjegyzés...",
                key="search_filter_modern",
                label_visibility="collapsed"
            )
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col2:
            # Státusz szűrő
            st.markdown('<div class="filter-section">', unsafe_allow_html=True)
            st.markdown("**📊 Státusz**")
            
            status_options = {opt['value']: opt['label'] for opt in filter_options['status_options']}
            
            # Checkbox layout a ui.md szerint
            status_col1, status_col2 = st.columns(2)
            
            selected_statuses = []
            current_status = st.session_state.get('status_filter_om')
            
            status_list = list(status_options.items())
            mid_point = len(status_list) // 2
            
            with status_col1:
                for status_value, status_label in status_list[:mid_point + 1]:
                    if st.checkbox(
                        f"✓ {status_label}",
                        value=current_status == status_value,
                        key=f"status_{status_value}_modern"
                    ):
                        selected_statuses.append(status_value)
            
            with status_col2:
                for status_value, status_label in status_list[mid_point + 1:]:
                    if st.checkbox(
                        f"✓ {status_label}",
                        value=current_status == status_value,
                        key=f"status_{status_value}_modern"
                    ):
                        selected_statuses.append(status_value)
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Termék szűrő
            st.markdown('<div class="filter-section">', unsafe_allow_html=True)
            st.markdown("**🌾 Termék**")
            
            product_options = [""] + [p['name'] for p in filter_options['products']]
            selected_product_name = st.selectbox(
                "Válasszon termék típust:",
                product_options,
                index=self._get_current_product_index(product_options),
                key="product_filter_modern",
                label_visibility="collapsed"
            )
            
            # Product ID meghatározása
            selected_product_id = None
            if selected_product_name:
                for product in filter_options['products']:
                    if product['name'] == selected_product_name:
                        selected_product_id = product['id']
                        break
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Session state frissítése
        self._update_session_state(
            producer_id=selected_producer_id,
            producer_name=selected_producer_name,
            product_id=selected_product_id,
            product_name=selected_product_name,
            status=selected_statuses[0] if selected_statuses else None,
            from_date=from_date,
            to_date=to_date,
            search_term=search_term
        )
        
        # FilterParams objektum visszaadása
        return FilterParams(
            producer_id=selected_producer_id,
            producer_name=selected_producer_name,
            product_id=selected_product_id,
            product_name=selected_product_name,
            status=selected_statuses[0] if selected_statuses else None,
            statuses=selected_statuses,
            from_date=from_date,
            to_date=to_date,
            search_term=search_term
        )
    
    def _get_filter_options(self) -> Dict[str, Any]:
        """Szűrési opciók betöltése API-ból"""
        try:
            return self.api_service.get_filter_options()
        except Exception as e:
            logger.error(f"Error loading filter options: {e}")
            return {
                'producers': [],
                'products': [],
                'status_options': [
                    {'value': 'CREATED', 'label': 'Létrehozva'},
                    {'value': 'CONFIRMED_BY_COMPANY', 'label': 'Megerősítve'},
                    {'value': 'ACCEPTED_BY_USER', 'label': 'Elfogadva'},
                    {'value': 'REJECTED_BY_USER', 'label': 'Elutasítva'},
                    {'value': 'FINALIZED', 'label': 'Véglegesítve'}
                ]
            }
    
    def _get_current_producer_index(self, producer_options: List[str]) -> int:
        """Jelenlegi termelő index meghatározása"""
        current_producer = st.session_state.get('producer_filter_om')
        if current_producer and current_producer in producer_options:
            return producer_options.index(current_producer)
        return 0
    
    def _get_current_product_index(self, product_options: List[str]) -> int:
        """Jelenlegi termék index meghatározása"""
        current_product = st.session_state.get('product_filter_om')
        if current_product and current_product in product_options:
            return product_options.index(current_product)
        return 0
    
    def _update_session_state(self, **kwargs):
        """Session state frissítése a szűrő értékekkel"""
        mapping = {
            'producer_id': 'producer_filter_om',
            'producer_name': 'producer_filter_om',  # Name-et használjuk ID helyett
            'product_id': 'product_filter_om',
            'product_name': 'product_filter_om',     # Name-et használjuk ID helyett
            'status': 'status_filter_om',
            'from_date': 'from_date_filter_om',
            'to_date': 'to_date_filter_om',
            'search_term': 'search_filter_om'
        }
        
        for key, value in kwargs.items():
            if key in mapping and value is not None:
                # Producer és product esetében a name-et tároljuk
                if key in ['producer_name', 'product_name']:
                    st.session_state[mapping[key]] = value if value else None
                else:
                    st.session_state[mapping[key]] = value
    
    def _render_active_filters(self, filters: FilterParams):
        """Aktív szűrők megjelenítése"""
        active_filters = filters.get_active_filters_summary()
        
        if active_filters:
            st.markdown("**🏷️ Aktív szűrők:**")
            
            filter_html = '<div style="margin: 0.5rem 0;">'
            for filter_desc in active_filters:
                filter_html += f'<span class="active-filter-badge">{filter_desc}</span>'
            filter_html += '</div>'
            
            st.markdown(filter_html, unsafe_allow_html=True)
            
            # Szűrők törlése gomb
            if st.button("🗑️ Összes szűrő törlése", key="clear_all_filters_modern"):
                self._clear_all_filters()
                st.rerun()
    
    def _clear_all_filters(self):
        """Összes szűrő törlése"""
        filter_keys = [
            'producer_filter_om',
            'product_filter_om',
            'status_filter_om',
            'search_filter_om'
        ]
        
        for key in filter_keys:
            if key in st.session_state:
                st.session_state[key] = None
        
        # Dátumok alapértékre állítása
        st.session_state['from_date_filter_om'] = date.today() - timedelta(days=30)
        st.session_state['to_date_filter_om'] = date.today()

class StatisticsCard:
    """
    Statisztikai kártya komponens valós adatokkal
    """
    
    def __init__(self):
        self._inject_styles()
    
    def _inject_styles(self):
        """CSS stílusok injektálása"""
        st.markdown("""
        <style>
        .stats-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            overflow: hidden;
            animation: slideInFromBottom 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .stats-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .stats-body {
            padding: 1.5rem;
        }
        
        .metrics-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .metric-item {
            text-align: center;
            padding: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
        }
        
        .metric-item:hover {
            transform: translateY(-2px) scale(1.02);
            background: rgba(26, 115, 232, 0.05);
            box-shadow: 0 4px 12px rgba(26, 115, 232, 0.1);
        }
        
        .metric-value {
            font-size: 1.75rem;
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 0.25rem;
            display: block;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: #666;
            font-weight: 500;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def render(self, offers: List[Offer]):
        """
        Statisztikai kártya renderelése
        
        Args:
            offers: Ajánlatok listája
        """
        stats = OfferStatistics.from_offers(offers)
        
        st.markdown('<div class="stats-card">', unsafe_allow_html=True)
        st.markdown('<div class="stats-header">📊 Statisztika</div>', unsafe_allow_html=True)
        st.markdown('<div class="stats-body">', unsafe_allow_html=True)
        
        # Főbb mutatók
        st.markdown(f'''
        <div class="metrics-row">
            <div class="metric-item">
                <span class="metric-value">{stats.formatted_total_quantity}</span>
                <span class="metric-label">Összes mennyiség</span>
            </div>
            <div class="metric-item">
                <span class="metric-value">{stats.formatted_average_price}</span>
                <span class="metric-label">Átlagár</span>
            </div>
            <div class="metric-item">
                <span class="metric-value">{stats.formatted_total_value}</span>
                <span class="metric-label">Összérték</span>
            </div>
        </div>
        ''', unsafe_allow_html=True)
        
        # Grafikonok
        if stats.status_distribution or stats.product_distribution:
            chart_col1, chart_col2 = st.columns(2)
            
            with chart_col1:
                if stats.status_distribution:
                    st.markdown("**Státuszok megoszlása**")
                    self._render_status_chart(stats.status_distribution)
            
            with chart_col2:
                if stats.product_distribution:
                    st.markdown("**Terméktípusok**")
                    self._render_product_chart(stats.product_distribution)
        
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_status_chart(self, status_distribution: Dict[str, int]):
        """Státusz megoszlás grafikon"""
        try:
            fig = px.pie(
                values=list(status_distribution.values()),
                names=list(status_distribution.keys()),
                title="",
                color_discrete_sequence=px.colors.qualitative.Set3
            )
            fig.update_layout(
                showlegend=True,
                height=300,
                margin=dict(t=0, b=0, l=0, r=0)
            )
            st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            logger.error(f"Error rendering status chart: {e}")
            st.info("Grafikon nem elérhető")
    
    def _render_product_chart(self, product_distribution: Dict[str, int]):
        """Termék megoszlás grafikon"""
        try:
            fig = px.bar(
                x=list(product_distribution.values()),
                y=list(product_distribution.keys()),
                orientation='h',
                title="",
                color_discrete_sequence=['#1a73e8']
            )
            fig.update_layout(
                showlegend=False,
                height=300,
                margin=dict(t=0, b=0, l=0, r=0),
                yaxis={'categoryorder': 'total ascending'}
            )
            st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            logger.error(f"Error rendering product chart: {e}")
            st.info("Grafikon nem elérhető")

class OfferTable:
    """
    Ajánlatok táblázata asztali nézethez
    """
    
    def render(self, offers: List[Offer]):
        """
        Ajánlatok táblázatának renderelése
        
        Args:
            offers: Ajánlatok listája
        """
        if not offers:
            st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
            return
        
        # DataFrame készítése
        table_data = []
        for offer in offers:
            table_data.append({
                'ID': f"#{offer.id}",
                'Dátum': offer.formatted_date,
                'Termelő': offer.producer_name,
                'Termék': f"🌾 {offer.product_name}",
                'Mennyiség': offer.formatted_quantity,
                'Ár': offer.formatted_price,
                'Érték': offer.formatted_total_value,
                'Státusz': f"{offer.status_indicator} {offer.status_display}"
            })
        
        df = pd.DataFrame(table_data)
        
        # Táblázat megjelenítése
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True
        )

class MobileOfferCards:
    """
    Ajánlatok kártyái mobil nézethez
    """
    
    def render(self, offers: List[Offer]):
        """
        Ajánlatok kártyáinak renderelése
        
        Args:
            offers: Ajánlatok listája
        """
        if not offers:
            st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
            return
        
        st.markdown("### 📱 Mobil nézet")
        
        for offer in offers:
            self._render_offer_card(offer)
    
    def _render_offer_card(self, offer: Offer):
        """Egyedi ajánlat kártya renderelése"""
        card_html = f"""
        <div style="
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            padding: 1.25rem;
            margin: 1rem 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        ">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <div style="font-weight: 600; font-size: 1.1rem; color: #1a73e8;">
                    #{offer.id}
                </div>
                <div style="font-size: 0.875rem; color: #666;">
                    📅 {offer.formatted_date}
                </div>
            </div>
            
            <div style="margin-bottom: 0.75rem;">
                <div style="font-weight: 500; color: #333; margin-bottom: 0.25rem;">
                    👤 {offer.producer_name}
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <span style="margin-right: 0.5rem; font-size: 1.1rem;">🌾</span>
                    <span style="font-weight: 500;">{offer.product_name}</span>
                </div>
            </div>
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <div style="background: #f8f9fa; padding: 0.5rem; border-radius: 8px; flex: 1; margin-right: 0.5rem;">
                    <div style="font-size: 0.75rem; color: #666; margin-bottom: 0.25rem;">Mennyiség</div>
                    <div style="font-weight: 600; color: #1a73e8;">{offer.formatted_quantity}</div>
                </div>
                <div style="background: #f8f9fa; padding: 0.5rem; border-radius: 8px; flex: 1; margin-left: 0.5rem;">
                    <div style="font-size: 0.75rem; color: #666; margin-bottom: 0.25rem;">Ár</div>
                    <div style="font-weight: 600; color: #1a73e8;">{offer.formatted_price}</div>
                </div>
            </div>
            
            <div style="
                display: flex; 
                align-items: center; 
                background: #f8f9fa; 
                padding: 0.75rem; 
                border-radius: 8px;
                border-left: 4px solid #1a73e8;
            ">
                <span style="margin-right: 0.5rem; font-size: 1.1rem;">{offer.status_indicator}</span>
                <span style="font-weight: 500;">{offer.status_display}</span>
            </div>
        </div>
        """
        
        st.markdown(card_html, unsafe_allow_html=True)