"""
Complex filtering functionality for the offer management page.
Provides support for combining filter conditions with logical operators (AND, OR, NOT).
"""
import streamlit as st
import logging
import uuid
import pandas as pd
from typing import List, Dict, Any, Tuple, Callable, Optional, Union

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
    from streamlit_app.pages.operator.offer_management.column_filters import (
        get_nested_value, 
        COLUMN_FILTERS, 
        OPERATORS,
        apply_filter_condition
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
        from pages.operator.offer_management.column_filters import (
            get_nested_value, 
            COLUMN_FILTERS, 
            OPERATORS,
            apply_filter_condition
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            from column_filters import (
                get_nested_value, 
                COLUMN_FILTERS, 
                OPERATORS,
                apply_filter_condition
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in complex_filters.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
            
            # Fallback column filters implementations
            def get_nested_value(obj, path):
                """Get a value from a nested object using a dot-separated path."""
                try:
                    parts = path.split('.')
                    value = obj
                    for part in parts:
                        if isinstance(value, dict):
                            value = value.get(part)
                        else:
                            return None
                    return value
                except (AttributeError, KeyError, TypeError):
                    return None
            
            # Minimal COLUMN_FILTERS and OPERATORS definitions
            COLUMN_FILTERS = {
                "id": {
                    "display_name": "Azonosító",
                    "field": "id",
                    "filter_type": "numeric",
                    "operators": ["equals", "not_equals", "greater_than", "less_than"]
                },
                "status": {
                    "display_name": "Státusz",
                    "field": "status",
                    "filter_type": "categorical",
                    "operators": ["equals", "not_equals"]
                }
            }
            
            OPERATORS = {
                "equals": {
                    "display_name": "=",
                    "full_name": "Egyenlő",
                    "function": lambda field, value: field == value
                },
                "not_equals": {
                    "display_name": "≠",
                    "full_name": "Nem egyenlő",
                    "function": lambda field, value: field != value
                },
                "greater_than": {
                    "display_name": ">",
                    "full_name": "Nagyobb mint",
                    "function": lambda field, value: field > value
                },
                "less_than": {
                    "display_name": "<",
                    "full_name": "Kisebb mint",
                    "function": lambda field, value: field < value
                }
            }
            
            def apply_filter_condition(offer, condition):
                """Apply a filter condition to an offer."""
                column, operator, value = condition
                field_value = get_nested_value(offer, column)
                operator_func = OPERATORS.get(operator, {}).get("function")
                if not operator_func:
                    return True
                try:
                    return operator_func(field_value, value)
                except:
                    return True

# Logger setup
logger = logging.getLogger(__name__)

# Define logical operators
LOGICAL_OPERATORS = {
    "AND": {
        "display_name": "ÉS",
        "symbol": "∧",
        "description": "Minden feltételnek teljesülnie kell",
        "function": lambda results: all(results)
    },
    "OR": {
        "display_name": "VAGY",
        "symbol": "∨",
        "description": "Legalább egy feltételnek teljesülnie kell",
        "function": lambda results: any(results)
    },
    "NOT": {
        "display_name": "NEM",
        "symbol": "¬",
        "description": "A feltétel ellenkezőjének kell teljesülnie",
        "function": lambda results: not results[0] if results else True,
        "is_unary": True
    }
}

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

class FilterCondition:
    """
    Represents a single filter condition (column, operator, value).
    """
    def __init__(self, column: str, operator: str, value: Any):
        """
        Initialize a filter condition.
        
        Args:
            column: Column field path
            operator: Operator key
            value: Filter value
        """
        self.column = column
        self.operator = operator
        self.value = value
    
    def apply(self, offer: Dict[str, Any]) -> bool:
        """
        Apply this filter condition to an offer.
        
        Args:
            offer: Offer data
            
        Returns:
            True if offer passes the filter, False otherwise
        """
        return apply_filter_condition(offer, (self.column, self.operator, self.value))
    
    def to_tuple(self) -> Tuple[str, str, Any]:
        """
        Convert to tuple representation.
        
        Returns:
            Tuple of (column, operator, value)
        """
        return (self.column, self.operator, self.value)
    
    def to_display_string(self) -> str:
        """
        Get a human-readable representation of this condition.
        
        Returns:
            String representation of the condition
        """
        column_info = next((info for key, info in COLUMN_FILTERS.items() 
                           if info["field"] == self.column), None)
        
        if not column_info:
            return f"{self.column} {self.operator} {self.value}"
        
        column_display = column_info["display_name"]
        operator_display = OPERATORS.get(self.operator, {}).get("full_name", self.operator)
        
        # Format value based on type
        if self.operator == "between":
            min_val, max_val = self.value
            value_display = f"{min_val} - {max_val}"
        elif self.operator == "in_list":
            value_display = f"{len(self.value)} kiválasztva"
        else:
            value_display = str(self.value)
        
        return f"{column_display} {operator_display} {value_display}"
    
    @classmethod
    def from_tuple(cls, tuple_condition: Tuple[str, str, Any]) -> 'FilterCondition':
        """
        Create a FilterCondition from a tuple.
        
        Args:
            tuple_condition: Tuple of (column, operator, value)
            
        Returns:
            FilterCondition instance
        """
        column, operator, value = tuple_condition
        return cls(column, operator, value)

class FilterGroup:
    """
    Represents a group of filter conditions combined with a logical operator.
    """
    def __init__(self, operator: str = "AND", conditions: List[Union['FilterGroup', FilterCondition]] = None):
        """
        Initialize a filter group.
        
        Args:
            operator: Logical operator ("AND", "OR", "NOT")
            conditions: List of conditions or subgroups
        """
        self.operator = operator
        self.conditions = conditions or []
    
    def add_condition(self, condition: Union['FilterGroup', FilterCondition]) -> None:
        """
        Add a condition or subgroup to this group.
        
        Args:
            condition: Filter condition or subgroup to add
        """
        self.conditions.append(condition)
    
    def remove_condition(self, index: int) -> None:
        """
        Remove a condition by index.
        
        Args:
            index: Index of the condition to remove
        """
        if 0 <= index < len(self.conditions):
            self.conditions.pop(index)
    
    def apply(self, offer: Dict[str, Any]) -> bool:
        """
        Apply this filter group to an offer.
        
        Args:
            offer: Offer data
            
        Returns:
            True if offer passes the filter group, False otherwise
        """
        if not self.conditions:
            return True
        
        if self.operator == "NOT":
            # NOT operator only applies to the first condition
            if len(self.conditions) > 0:
                return not self.conditions[0].apply(offer)
            return True
        
        # AND or OR operator
        results = [condition.apply(offer) for condition in self.conditions]
        return LOGICAL_OPERATORS[self.operator]["function"](results)
    
    def to_display_string(self, level: int = 0) -> str:
        """
        Get a human-readable representation of this filter group.
        
        Args:
            level: Nesting level for indentation
            
        Returns:
            String representation of the filter group
        """
        if not self.conditions:
            return "Nincs feltétel"
        
        indent = "  " * level
        op_display = LOGICAL_OPERATORS[self.operator]["display_name"]
        
        if self.operator == "NOT":
            # NOT only applies to the first condition
            if len(self.conditions) > 0:
                condition_str = self.conditions[0].to_display_string(level + 1)
                return f"{indent}NEM ({condition_str})"
            return f"{indent}NEM ()"
        
        # Format all conditions for AND/OR
        condition_strs = [c.to_display_string(level + 1) for c in self.conditions]
        joined = f"\n{indent}".join(condition_strs)
        
        return f"{indent}({op_display}:\n{indent}{joined}\n{indent})"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation for serialization.
        
        Returns:
            Dictionary representation
        """
        conditions_dict = []
        for condition in self.conditions:
            if isinstance(condition, FilterGroup):
                conditions_dict.append({
                    "type": "group",
                    "data": condition.to_dict()
                })
            else:
                conditions_dict.append({
                    "type": "condition",
                    "data": {
                        "column": condition.column,
                        "operator": condition.operator,
                        "value": condition.value
                    }
                })
        
        return {
            "operator": self.operator,
            "conditions": conditions_dict
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FilterGroup':
        """
        Create a FilterGroup from a dictionary.
        
        Args:
            data: Dictionary representation
            
        Returns:
            FilterGroup instance
        """
        group = cls(operator=data["operator"])
        
        for condition_data in data.get("conditions", []):
            condition_type = condition_data["type"]
            
            if condition_type == "group":
                # Recursive creation of subgroups
                subgroup = FilterGroup.from_dict(condition_data["data"])
                group.add_condition(subgroup)
            else:
                # Regular condition
                condition = FilterCondition(
                    column=condition_data["data"]["column"],
                    operator=condition_data["data"]["operator"],
                    value=condition_data["data"]["value"]
                )
                group.add_condition(condition)
        
        return group
    
    @classmethod
    def from_condition_list(cls, conditions: List[Tuple[str, str, Any]], operator: str = "AND") -> 'FilterGroup':
        """
        Create a FilterGroup from a list of condition tuples.
        
        Args:
            conditions: List of (column, operator, value) tuples
            operator: Logical operator to combine conditions
            
        Returns:
            FilterGroup instance
        """
        group = cls(operator=operator)
        for condition_tuple in conditions:
            condition = FilterCondition.from_tuple(condition_tuple)
            group.add_condition(condition)
        return group

def render_operator_selection(key_suffix: str = None) -> str:
    """
    Render UI for selecting a logical operator.
    
    Args:
        key_suffix: Optional suffix for the widget key
        
    Returns:
        Selected operator key
    """
    key = generate_unique_key("logical_operator", key_suffix)
    
    operators = list(LOGICAL_OPERATORS.keys())
    
    # Format operators for display
    def format_operator(op):
        op_info = LOGICAL_OPERATORS[op]
        return f"{op_info['symbol']} {op_info['display_name']}"
    
    selected = st.selectbox(
        "Logikai művelet",
        options=operators,
        format_func=format_operator,
        key=key
    )
    
    # Show description of the selected operator
    op_info = LOGICAL_OPERATORS[selected]
    st.caption(op_info["description"])
    
    return selected

def render_condition_ui(offers: List[Dict[str, Any]], 
                       on_add: Callable[[FilterCondition], None] = None,
                       key_suffix: str = None) -> None:
    """
    Render UI for creating a filter condition.
    
    Args:
        offers: List of offer dictionaries
        on_add: Callback for when a condition is added
        key_suffix: Optional suffix for widget keys
    """
    from column_filters import (
        render_filter_selection_ui,
        render_operator_selection as render_column_operator_selection,
        render_value_input,
        extract_unique_values
    )
    
    # Use grid layout for more compact UI
    col1, col2 = st.columns([1, 1])
    
    with col1:
        # Column selection
        selected_column_key = render_filter_selection_ui()
        column_config = COLUMN_FILTERS.get(selected_column_key)
    
    with col2:
        if column_config:
            # Operator selection based on filter type
            selected_operator = render_column_operator_selection(
                column_config["filter_type"], 
                column_config["operators"]
            )
    
    # Value input based on filter type and operator
    if column_config:
        # For categorical fields, extract unique values
        column_values = None
        if column_config["filter_type"] in ["categorical", "text"] and len(offers) > 0:
            column_values = extract_unique_values(offers, column_config["field"])
        
        selected_value = render_value_input(
            column_config["filter_type"],
            selected_operator,
            column_values
        )
        
        # Add condition button
        if st.button("Feltétel hozzáadása", key=generate_unique_key("add_condition", key_suffix)):
            # Create filter condition
            condition = FilterCondition(
                column_config["field"],
                selected_operator,
                selected_value
            )
            
            # Call the callback if provided
            if on_add:
                on_add(condition)
            
            # Show success message
            show_inline_success("Feltétel hozzáadva!")
            
            # Force refresh
            st.rerun()

def render_group_badge(group: FilterGroup, index: int) -> str:
    """
    Render a visual badge for a filter group.
    
    Args:
        group: FilterGroup to render
        index: Index for unique identification
        
    Returns:
        HTML for the filter group badge
    """
    operator = group.operator
    operator_info = LOGICAL_OPERATORS[operator]
    conditions_count = len(group.conditions)
    
    # Different colors for different operators
    colors = {
        "AND": "#E3F2FD",
        "OR": "#FFF8E1",
        "NOT": "#FFEBEE"
    }
    
    symbol_colors = {
        "AND": "#1976D2",
        "OR": "#FFA000",
        "NOT": "#E53935"
    }
    
    bg_color = colors.get(operator, "#F5F5F5")
    symbol_color = symbol_colors.get(operator, "#757575")
    
    # Generate HTML for the badge
    badge_html = f"""
    <div class="filter-group-badge filter-group-{index}" 
         style="display: inline-flex; align-items: center; background-color: {bg_color}; 
                border-radius: 4px; padding: 4px 8px; margin: 4px; font-size: 0.85em;">
        <span style="color: {symbol_color}; font-weight: bold; margin-right: 4px;">
            {operator_info['symbol']}
        </span>
        <span style="font-weight: 500; margin-right: 4px;">
            {operator_info['display_name']}
        </span>
        <span style="color: #757575;">
            ({conditions_count} feltétel)
        </span>
        <button class="remove-group-{index}" 
                style="background: none; border: none; color: #E53935; margin-left: 6px; 
                       cursor: pointer; font-weight: bold; padding: 0 4px;"
                onclick="removeGroup('{index}')">×</button>
    </div>
    """
    
    return badge_html

def render_condition_badge(condition: FilterCondition, index: int) -> str:
    """
    Render a visual badge for a filter condition.
    
    Args:
        condition: FilterCondition to render
        index: Index for unique identification
        
    Returns:
        HTML for the filter condition badge
    """
    display_text = condition.to_display_string()
    
    # Generate HTML for the badge
    badge_html = f"""
    <div class="filter-condition-badge filter-condition-{index}" 
         style="display: inline-flex; align-items: center; background-color: #f0f2f6; 
                border-radius: 4px; padding: 4px 8px; margin: 4px; font-size: 0.85em;">
        <span style="font-style: italic;">{display_text}</span>
        <button class="remove-condition-{index}" 
                style="background: none; border: none; color: #E53935; margin-left: 6px; 
                       cursor: pointer; font-weight: bold; padding: 0 4px;"
                onclick="removeCondition('{index}')">×</button>
    </div>
    """
    
    return badge_html

def render_filter_builder_ui(offers: List[Dict[str, Any]], 
                            root_group: FilterGroup,
                            on_update: Callable[[FilterGroup], None] = None) -> FilterGroup:
    """
    Render UI for building complex filter conditions.
    
    Args:
        offers: List of offer dictionaries
        root_group: Root filter group
        on_update: Callback when filter group is updated
        
    Returns:
        Updated root filter group
    """
    st.markdown("### Összetett szűrési feltételek (ÉS, VAGY, NEM)")
    
    # Local state tracking
    if "complex_filter_state" not in st.session_state:
        st.session_state["complex_filter_state"] = {
            "current_group": root_group,
            "add_group_open": False,
            "add_condition_open": False
        }
    
    # Create tabs for different actions
    tab1, tab2, tab3 = st.tabs(["Aktív szűrőcsoportok", "Feltétel hozzáadása", "Csoport létrehozása"])
    
    with tab1:
        current_group = st.session_state["complex_filter_state"]["current_group"]
        
        if not current_group.conditions:
            st.info("Nincsenek aktív szűrőfeltételek.")
        else:
            # Display the current filter structure
            st.markdown("#### Logikai struktúra")
            st.code(current_group.to_display_string(), language="text")
            
            # Create JS for removing groups and conditions
            js = """
            <script>
            function removeGroup(index) {
                const input = document.getElementById('remove_group_' + index);
                if (input) {
                    input.value = 'true';
                    input.dispatchEvent(new Event('change'));
                }
            }
            
            function removeCondition(index) {
                const input = document.getElementById('remove_condition_' + index);
                if (input) {
                    input.value = 'true';
                    input.dispatchEvent(new Event('change'));
                }
            }
            </script>
            """
            
            # Start HTML for filter display
            html = js + """
            <div style="margin: 10px 0;">
                <div style="font-weight: 500; margin-bottom: 5px;">Feltételek és csoportok:</div>
                <div style="display: flex; flex-wrap: wrap;">
            """
            
            # Recursively add badges for all conditions and groups
            def add_badges(group, prefix=""):
                nonlocal html
                for i, item in enumerate(group.conditions):
                    if isinstance(item, FilterGroup):
                        html += render_group_badge(item, f"{prefix}{i}")
                    else:
                        html += render_condition_badge(item, f"{prefix}{i}")
            
            add_badges(current_group)
            
            # Close container HTML
            html += """
                </div>
            </div>
            """
            
            # Render the HTML
            st.markdown(html, unsafe_allow_html=True)
            
            # Add hidden inputs for removal detection
            for i in range(len(current_group.conditions)):
                group_key = f"remove_group_{i}"
                condition_key = f"remove_condition_{i}"
                
                if st.text_input(f"Remove group {i}", value="false", key=group_key, label_visibility="collapsed") == "true":
                    # Handle group removal
                    if isinstance(current_group.conditions[i], FilterGroup):
                        current_group.remove_condition(i)
                        if on_update:
                            on_update(root_group)
                        st.rerun()
                
                if st.text_input(f"Remove condition {i}", value="false", key=condition_key, label_visibility="collapsed") == "true":
                    # Handle condition removal
                    current_group.remove_condition(i)
                    if on_update:
                        on_update(root_group)
                    st.rerun()
    
    with tab2:
        st.markdown("#### Új feltétel hozzáadása")
        
        # Callback for condition creation
        def add_condition(condition):
            current_group = st.session_state["complex_filter_state"]["current_group"]
            current_group.add_condition(condition)
            if on_update:
                on_update(root_group)
        
        # Render condition creation UI
        render_condition_ui(offers, on_add=add_condition)
    
    with tab3:
        st.markdown("#### Új szűrőcsoport létrehozása")
        
        # Operator selection for new group
        selected_operator = render_operator_selection(key_suffix="new_group")
        
        # Create new group button
        if st.button("Csoport létrehozása", key="create_group_button"):
            new_group = FilterGroup(operator=selected_operator)
            
            # Add to current group
            current_group = st.session_state["complex_filter_state"]["current_group"]
            current_group.add_condition(new_group)
            
            # Update state to select the new group
            st.session_state["complex_filter_state"]["current_group"] = new_group
            
            # Call update callback
            if on_update:
                on_update(root_group)
            
            # Show success message
            show_inline_success("Új csoport létrehozva!")
            
            # Force refresh
            st.rerun()
    
    # Additional controls for navigating groups
    st.markdown("---")
    st.markdown("#### Navigálás a csoportok között")
    
    # Function to find parent group
    def find_parent_group(root, target, path=None):
        if path is None:
            path = []
        
        for i, condition in enumerate(root.conditions):
            if condition is target:
                return root, path + [i]
            
            if isinstance(condition, FilterGroup):
                parent, found_path = find_parent_group(condition, target, path + [i])
                if parent:
                    return parent, found_path
        
        return None, None
    
    # Navigate up button (to parent group)
    current_group = st.session_state["complex_filter_state"]["current_group"]
    if current_group is not root_group:
        parent, _ = find_parent_group(root_group, current_group)
        if parent and st.button("↑ Fel (Szülő csoport)", key="up_to_parent"):
            st.session_state["complex_filter_state"]["current_group"] = parent
            st.rerun()
    
    # Root navigation button
    if current_group is not root_group:
        if st.button("⟰ Vissza a gyökérhez", key="back_to_root"):
            st.session_state["complex_filter_state"]["current_group"] = root_group
            st.rerun()
    
    # List subgroups for navigation
    st.markdown("#### Alcsoportok")
    subgroups = [c for c in current_group.conditions if isinstance(c, FilterGroup)]
    
    if not subgroups:
        st.info("Nincsenek alcsoportok ebben a csoportban.")
    else:
        for i, subgroup in enumerate(subgroups):
            op_info = LOGICAL_OPERATORS[subgroup.operator]
            if st.button(f"{i+1}. {op_info['display_name']} csoport ({len(subgroup.conditions)} feltétel)", key=f"subgroup_{i}"):
                st.session_state["complex_filter_state"]["current_group"] = subgroup
                st.rerun()
    
    return root_group

def apply_filter_group(offers: List[Dict[str, Any]], group: FilterGroup) -> List[Dict[str, Any]]:
    """
    Apply a filter group to offers.
    
    Args:
        offers: List of offer dictionaries
        group: FilterGroup to apply
        
    Returns:
        Filtered offers
    """
    if not offers or not group or not group.conditions:
        return offers
    
    return [offer for offer in offers if group.apply(offer)]

def apply_complex_filters(offers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Apply complex filtering with logical operators to offers.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        Filtered offers
    """
    # Initialize the root filter group if not in session state
    if "complex_filter_root" not in st.session_state:
        st.session_state["complex_filter_root"] = FilterGroup(operator="AND")
    
    # Store original offers count
    total_count = len(offers)
    
    # Create styled container
    st.markdown("""
    <style>
    .complex-filter-container {
        margin: 15px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }
    .complex-filter-title {
        font-weight: 500;
        margin-bottom: 10px;
        color: #333;
    }
    </style>
    <div class="complex-filter-container">
        <div class="complex-filter-title">Összetett szűrők (ÉS, VAGY, NEM logikai műveletekkel)</div>
    """, unsafe_allow_html=True)
    
    # Get current root group
    root_group = st.session_state["complex_filter_root"]
    
    # Callback for when filter group is updated
    def on_update(updated_group):
        st.session_state["complex_filter_root"] = updated_group
    
    # Render the filter builder UI
    render_filter_builder_ui(offers, root_group, on_update)
    
    # Close the container div
    st.markdown("</div>", unsafe_allow_html=True)
    
    # Apply the filters
    filtered_offers = apply_filter_group(offers, root_group)
    
    # Show result count if any filters applied
    if root_group.conditions:
        filtered_count = len(filtered_offers)
        percentage = int(filtered_count / total_count * 100) if total_count > 0 else 0
        
        st.markdown(
            f"""
            <div style="margin: 10px 0; padding: 8px 12px; background-color: #E8F5E9; border-radius: 4px; 
                        border-left: 4px solid #4CAF50;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <span style="font-weight: 500;">Összetett szűrés eredménye</span> 
                    </div>
                    <div>
                        <span style="font-weight: 500;">{filtered_count} találat</span>
                        <span style="color: #666; margin-left: 5px;">({percentage}%)</span>
                    </div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )
    
    return filtered_offers

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Complex Filter Test", layout="wide")
    
    st.title("Complex Filter Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"contact_name": "Mezőgazda Márton", "email": "<EMAIL>"},
            "description": "Kiváló minőségű, zamatos körte."
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-04-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Nagy mennyiségű étkezési alma, hosszú tárolhatósággal."
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-03-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"contact_name": "Gyümölcsös Gábor", "email": "<EMAIL>"},
            "description": "Befőzésre alkalmas szilva, magas cukortartalommal."
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-04-25T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"contact_name": "Almás Anna", "email": "<EMAIL>"},
            "description": "Friss nyári körte, egyenesen a fáról."
        }
    ]
    
    # Apply complex filters
    filtered_offers = apply_complex_filters(sample_offers)
    
    # Display filtered data
    st.markdown("### Szűrt ajánlatok")
    
    # Convert to DataFrame for display
    df = pd.DataFrame([
        {
            "ID": offer["id"],
            "Státusz": format_status(offer["status"]),
            "Beszállítás": format_date(offer["delivery_date"]),
            "Termék": offer["product_type"]["name"],
            "Mennyiség": f"{offer['quantity_in_kg']} kg",
            "Ár": f"{offer['price']} Ft/kg",
            "Termelő": offer["user"]["contact_name"]
        }
        for offer in filtered_offers
    ])
    
    st.dataframe(
        df,
        hide_index=True,
        use_container_width=True
    )
    
    # Show example filter structures
    st.sidebar.markdown("### Példa szűrők")
    st.sidebar.markdown("""
    **Példa 1**: Alma ÉS (CREATED VAGY ACCEPTED_BY_USER)
    ```
    (AND:
      Termék Egyenlő Alma
      (OR:
        Státusz Egyenlő CREATED
        Státusz Egyenlő ACCEPTED_BY_USER
      )
    )
    ```
    
    **Példa 2**: NEM (Ár > 400 ÉS Mennyiség < 500)
    ```
    (NOT:
      (AND:
        Ár Nagyobb mint 400
        Mennyiség Kisebb mint 500
      )
    )
    ```
    
    **Példa 3**: (Körte VAGY Szilva) ÉS NEM (Létrehozva előtte 2025-04-01)
    ```
    (AND:
      (OR:
        Termék Egyenlő Körte
        Termék Egyenlő Szilva
      )
      (NOT:
        Létrehozva Korábbi mint 2025-04-01
      )
    )
    ```
    """)