"""
Tesztmodu<PERSON> a fejlett szűrési komponensek teszteléséhez.

Ez a modul bemutatja a TASK-1.2 feladat keretében fejlesztett
fejlett szűrési komponenseket, többe<PERSON> kö<PERSON> a csúszkás dátumtartomány
<PERSON>, multiple-select stá<PERSON>z szűrőt és gyorsszűrőket.
"""
import streamlit as st
from datetime import datetime, timedelta
import logging
import pandas as pd
import numpy as np

try:
    from streamlit_app.pages.operator.offer_management.advanced_filters import (
        inject_advanced_filter_styles,
        render_date_range_slider,
        render_multiple_select,
        render_quick_filter_chips,
        render_product_autocomplete,
        render_range_slider,
        render_full_status_filter,
        render_quick_date_filters,
        render_quick_status_filters,
        render_complete_advanced_filter_form
    )
    from streamlit_app.pages.operator.offer_management.custom_css_framework import (
        inject_base_styles
    )
    from streamlit_app.pages.operator.offer_management.active_filter_display import (
        render_active_filters_panel
    )
except ImportError:
    try:
        from pages.operator.offer_management.advanced_filters import (
            inject_advanced_filter_styles,
            render_date_range_slider,
            render_multiple_select,
            render_quick_filter_chips,
            render_product_autocomplete,
            render_range_slider,
            render_full_status_filter,
            render_quick_date_filters,
            render_quick_status_filters,
            render_complete_advanced_filter_form
        )
        from pages.operator.offer_management.custom_css_framework import (
            inject_base_styles
        )
        from pages.operator.offer_management.active_filter_display import (
            render_active_filters_panel
        )
    except ImportError:
        try:
            from advanced_filters import (
                inject_advanced_filter_styles,
                render_date_range_slider,
                render_multiple_select,
                render_quick_filter_chips,
                render_product_autocomplete,
                render_range_slider,
                render_full_status_filter,
                render_quick_date_filters,
                render_quick_status_filters,
                render_complete_advanced_filter_form
            )
            from custom_css_framework import (
                inject_base_styles
            )
            from active_filter_display import (
                render_active_filters_panel
            )
        except ImportError:
            st.error("Nem sikerült importálni a szükséges modulokat!")
            st.stop()

# Logging beállítása
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Oldal konfigurálása
st.set_page_config(
    page_title="Fejlett Szűrési Komponensek Teszt",
    page_icon="🔍",
    layout="wide"
)

# Stílusok betöltése
inject_base_styles()
inject_advanced_filter_styles()

# Példa termékek az autocomplete-hez
sample_products = [
    {"id": 1, "name": "Alma", "category": "Gyümölcs"},
    {"id": 2, "name": "Körte", "category": "Gyümölcs"},
    {"id": 3, "name": "Banán", "category": "Gyümölcs"},
    {"id": 4, "name": "Búza", "category": "Gabona"},
    {"id": 5, "name": "Kukorica", "category": "Gabona"},
    {"id": 6, "name": "Árpa", "category": "Gabona"},
    {"id": 7, "name": "Dinnye", "category": "Gyümölcs"},
    {"id": 8, "name": "Szőlő", "category": "Gyümölcs"},
    {"id": 9, "name": "Burgonya", "category": "Zöldség"},
    {"id": 10, "name": "Paradicsom", "category": "Zöldség"},
]

# Főcím és leírás
st.title("Fejlett Szűrési Komponensek")
st.markdown("""
Ez a demo a fejlett szűrési komponenseket mutatja be, amelyek a TASK-1.2 feladat részeként készültek.
A komponensek kihasználják az előzőleg létrehozott egyedi CSS keretrendszert és reszponzív design megoldásokat.

**Implementált komponensek:**
1. Csúszkás dátumtartomány szűrő
2. Multiple-select státusz szűrő
3. Termék típus szerinti szűrési lehetőség
4. Gyorsszűrők előre definiált beállításokkal
""")

# Aktív szűrők inicializálása
if "active_filters" not in st.session_state:
    st.session_state.active_filters = {
        "statuses": [],
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_id": None,
        "product_name": None,
        "min_quantity": None,
        "max_quantity": None,
        "producer_id": None
    }

# Szűrési példa adatok
if "sample_data" not in st.session_state:
    # Kezdő és végdátumok
    start_date = datetime.now().date() - timedelta(days=60)
    end_date = datetime.now().date() + timedelta(days=30)
    date_range = (end_date - start_date).days
    
    # Random dátumok generálása a mintaadatokhoz
    np.random.seed(42)  # Reprodukálhatóság
    
    # Lehetséges státuszok
    statuses = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
    
    # Mintaadatok generálása
    data = []
    for i in range(1, 101):  # 100 rekord
        # Random termék és termelő
        product_idx = np.random.randint(0, len(sample_products))
        product = sample_products[product_idx]
        
        # Random dátum
        days_offset = np.random.randint(0, date_range)
        item_date = start_date + timedelta(days=days_offset)
        
        # Random státusz
        status = statuses[np.random.randint(0, len(statuses))]
        
        # Random mennyiség
        quantity = np.random.randint(10, 5000)
        
        # Rekord összeállítása
        data.append({
            "id": i,
            "product_id": product["id"],
            "product_name": product["name"],
            "producer_id": f"PROD_{np.random.randint(1, 6)}",  # 5 különböző termelő
            "producer_name": f"Termelő {np.random.randint(1, 6)}",
            "status": status,
            "quantity": quantity,
            "created_at": item_date,
            "delivery_date": item_date + timedelta(days=np.random.randint(7, 30))
        })
    
    # DataFrame létrehozása
    st.session_state.sample_data = pd.DataFrame(data)

# Eseménykezelő függvények
def handle_filter_update(filters):
    """Szűrők frissítése és adatok szűrése."""
    st.session_state.active_filters.update(filters)
    st.success("Szűrők frissítve!")

def handle_filter_reset():
    """Szűrők alaphelyzetbe állítása."""
    st.session_state.active_filters = {
        "statuses": [],
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_id": None,
        "product_name": None,
        "min_quantity": None,
        "max_quantity": None,
        "producer_id": None
    }
    st.success("Szűrők alaphelyzetbe állítva!")

def handle_filter_remove(key):
    """Egy szűrő törlése az aktív szűrők közül."""
    if key in st.session_state.active_filters:
        if key in ["from_date", "to_date"]:
            # Dátum szűrőknél visszaállítjuk az alapértelmezett értékeket
            if key == "from_date":
                st.session_state.active_filters["from_date"] = datetime.now().date() - timedelta(days=30)
            elif key == "to_date":
                st.session_state.active_filters["to_date"] = datetime.now().date()
        else:
            # Egyéb szűrőknél None-ra vagy üres listára állítjuk
            if key == "statuses":
                st.session_state.active_filters[key] = []
            else:
                st.session_state.active_filters[key] = None

def apply_filters_to_data():
    """Szűrők alkalmazása a mintaadatokra."""
    df = st.session_state.sample_data
    filters = st.session_state.active_filters
    
    # Kezdeti DataFrame
    filtered_df = df.copy()
    
    # Státusz szűrés
    if filters["statuses"] and len(filters["statuses"]) > 0:
        filtered_df = filtered_df[filtered_df["status"].isin(filters["statuses"])]
    
    # Dátum szűrés
    if filters["from_date"]:
        filtered_df = filtered_df[filtered_df["created_at"] >= filters["from_date"]]
    
    if filters["to_date"]:
        filtered_df = filtered_df[filtered_df["created_at"] <= filters["to_date"]]
    
    # Termék szűrés
    if filters["product_id"]:
        filtered_df = filtered_df[filtered_df["product_id"] == filters["product_id"]]
    
    # Termelő szűrés
    if filters["producer_id"]:
        filtered_df = filtered_df[filtered_df["producer_id"] == filters["producer_id"]]
    
    # Mennyiség szűrés
    if filters["min_quantity"]:
        filtered_df = filtered_df[filtered_df["quantity"] >= filters["min_quantity"]]
    
    if filters["max_quantity"]:
        filtered_df = filtered_df[filtered_df["quantity"] <= filters["max_quantity"]]
    
    return filtered_df

# Demo vezérlése tab-okkal
tab1, tab2 = st.tabs(["Komponensek bemutatása", "Teljes szűrő alkalmazás"])

with tab1:
    st.header("Egyedi szűrő komponensek")
    
    st.markdown("""
    Ebben a részben külön-külön mutatjuk be az egyes szűrési komponenseket.
    Minden komponens teljesen egyedi, és kifejezetten az ajánlatkezelő alkalmazáshoz készült.
    """)
    
    component_tabs = st.tabs([
        "Csúszkás dátumszűrő", 
        "Multiple-select", 
        "Gyorsszűrők",
        "Autocomplete",
        "Tartomány csúszka"
    ])
    
    with component_tabs[0]:
        st.subheader("Csúszkás dátumtartomány szűrő")
        
        st.markdown("""
        Ez a komponens egy vizuálisan vonzó, interaktív csúszkát biztosít a dátumtartomány kiválasztásához.
        
        **Főbb jellemzők:**
        - Két csúszka fogantyú a kezdő és végdátum beállításához
        - Vizuális visszajelzés a kiválasztott tartományról
        - Tooltip a pontos dátumok megjelenítésére
        - JavaScript-tel kiegészített működés a gördülékeny felhasználói élményért
        """)
        
        # Dátumok a demonstrációhoz
        today = datetime.now().date()
        min_date = today - timedelta(days=365)  # 1 évvel ezelőtt
        max_date = today + timedelta(days=365)   # 1 évvel később
        start_date = today - timedelta(days=30)
        end_date = today
        
        # Komponens renderelése
        selected_start, selected_end = render_date_range_slider(
            min_date=datetime.combine(min_date, datetime.min.time()),
            max_date=datetime.combine(max_date, datetime.min.time()),
            start_date=datetime.combine(start_date, datetime.min.time()),
            end_date=datetime.combine(end_date, datetime.min.time()),
            key="tab1_date_slider"
        )
        
        # Eredmény megjelenítése
        st.success(f"Kiválasztott tartomány: {selected_start.date()} - {selected_end.date()}")
    
    with component_tabs[1]:
        st.subheader("Multiple-select státusz szűrő")
        
        st.markdown("""
        Ez a komponens egy fejlett, kereshető multiple-select megoldás, amely lehetővé teszi több elem egyidejű kiválasztását.
        
        **Főbb jellemzők:**
        - Keresőmező a gyors szűréshez
        - "Mind kijelölése" és "Törlés" gombok
        - Kategóriák szerinti csoportosítás
        - Checkbox-alapú kiválasztás
        """)
        
        # Státusz adatok
        status_options = [
            {"id": "CREATED", "name": "Létrehozva", "group": "Kezdeti státuszok"},
            {"id": "CONFIRMED_BY_COMPANY", "name": "Megerősítve cég által", "group": "Kezdeti státuszok"},
            {"id": "ACCEPTED_BY_USER", "name": "Elfogadva felhasználó által", "group": "Végső státuszok"},
            {"id": "REJECTED_BY_USER", "name": "Elutasítva felhasználó által", "group": "Végső státuszok"},
            {"id": "FINALIZED", "name": "Véglegesítve", "group": "Végső státuszok"}
        ]
        
        # Komponens renderelése
        selected_values = render_multiple_select(
            options=status_options,
            value_key="id",
            label_key="name",
            default_selected=["CREATED", "CONFIRMED_BY_COMPANY"],
            title="Státusz kiválasztása",
            placeholder="Státusz keresése...",
            key="tab1_multiple_select"
        )
        
        # Eredmény megjelenítése
        if selected_values:
            st.success(f"Kiválasztott státuszok: {', '.join(selected_values)}")
        else:
            st.info("Nincs kiválasztott státusz")
    
    with component_tabs[2]:
        st.subheader("Gyorsszűrők")
        
        st.markdown("""
        A gyorsszűrők előre definiált, egy kattintással aktiválható szűrőbeállítások.
        
        **Főbb jellemzők:**
        - Chip design (kártyás/címkés megjelenés)
        - Vizuális visszajelzés az aktív szűrőről
        - Ikonok a jobb felismerhetőségért
        - Toggle működés: újra kattintás kikapcsolja a szűrőt
        """)
        
        # Státusz gyorsszűrők
        st.markdown("#### Státusz gyorsszűrők")
        
        selected_status_filter = render_quick_status_filters(
            key="tab1_status_quick"
        )
        
        if selected_status_filter:
            st.success(f"""
            **Kiválasztott státusz szűrő:**  
            {selected_status_filter['label']} ({', '.join(selected_status_filter['filters']['statuses'])})
            """)
        else:
            st.info("Nincs kiválasztott státusz gyorsszűrő")
        
        # Dátum gyorsszűrők
        st.markdown("#### Dátum gyorsszűrők")
        
        selected_date_filter = render_quick_date_filters(
            key="tab1_date_quick"
        )
        
        if selected_date_filter:
            st.success(f"""
            **Kiválasztott dátum szűrő:**  
            {selected_date_filter['label']} 
            ({selected_date_filter['filters']['from_date']} - {selected_date_filter['filters']['to_date']})
            """)
        else:
            st.info("Nincs kiválasztott dátum gyorsszűrő")
    
    with component_tabs[3]:
        st.subheader("Termék autocomplete")
        
        st.markdown("""
        Az autocomplete komponens lehetővé teszi a gyors keresést és kiválasztást egy hosszú listából.
        
        **Főbb jellemzők:**
        - Valós idejű szűrés gépelés közben
        - A keresett kifejezés kiemelése a találatokban
        - Billentyűkezelés (le/fel nyilak, enter, escape)
        - Maximum 10 találat megjelenítése a jobb áttekinthetőségért
        """)
        
        # Komponens renderelése
        selected_product = render_product_autocomplete(
            products=sample_products,
            value_key="id",
            label_key="name",
            placeholder="Termék keresése...",
            key="tab1_autocomplete"
        )
        
        # Eredmény megjelenítése
        if selected_product:
            selected_name = next((p["name"] for p in sample_products if p["id"] == selected_product), None)
            st.success(f"Kiválasztott termék: {selected_name} (ID: {selected_product})")
        else:
            st.info("Nincs kiválasztott termék")
    
    with component_tabs[4]:
        st.subheader("Tartomány csúszka")
        
        st.markdown("""
        A tartomány csúszka komponens lehetővé teszi egy minimum és maximum érték kiválasztását,
        ideális árak, mennyiségek vagy egyéb numerikus adatok szűréséhez.
        
        **Főbb jellemzők:**
        - Két végpont egy csúszkán
        - Formázott értékmegjelenítés
        - Testreszabható lépésköz
        """)
        
        # Komponens renderelése
        quantity_range = render_range_slider(
            min_value=0,
            max_value=5000,
            default_values=(100, 2000),
            label="Mennyiség (kg):",
            format_func=lambda x: f"{int(x):,} kg".replace(",", " "),
            key="tab1_range_slider",
            step=50
        )
        
        # Eredmény megjelenítése
        st.success(f"Kiválasztott mennyiségtartomány: {quantity_range[0]} - {quantity_range[1]} kg")

with tab2:
    st.header("Teljes szűrő alkalmazás")
    
    st.markdown("""
    Ez a demo bemutatja, hogyan működnek együtt a fejlett szűrési komponensek egy teljes alkalmazásban.
    
    Az alkalmazás két részből áll:
    1. **Szűrőpanel**: Az összes szűrési komponenst tartalmazza
    2. **Eredmények nézet**: A szűrt adatok megjelenítése táblázatban
    """)
    
    # Oldalt oszlopokra osztjuk
    col1, col2 = st.columns([1, 3])
    
    with col1:
        st.subheader("Szűrők")
        
        # A teljes szűrőpanel renderelése
        filter_data = render_complete_advanced_filter_form(
            products=sample_products,
            on_filter=handle_filter_update,
            on_reset=handle_filter_reset,
            default_values=st.session_state.active_filters,
            key="tab2_complete"
        )
    
    with col2:
        st.subheader("Szűrési eredmények")
        
        # Aktív szűrők megjelenítése
        render_active_filters_panel(
            filters=st.session_state.active_filters,
            prefix="app_demo",
            title="Aktív szűrők",
            icon="🏷️",
            on_remove=handle_filter_remove,
            on_clear_all=handle_filter_reset,
            categorize=True
        )
        
        # Szűrt adatok
        filtered_data = apply_filters_to_data()
        
        # Eredmények számának kijelzése
        st.markdown(f"### Összesen {len(filtered_data)} találat")
        
        # Táblázat formázása
        formatted_df = filtered_data.copy()
        
        # Egyszerűsített nézet a táblázathoz
        display_cols = [
            "id", "product_name", "producer_name", "status", 
            "quantity", "created_at", "delivery_date"
        ]
        
        # Oszlopok átnevezése
        rename_map = {
            "id": "Azonosító",
            "product_name": "Termék",
            "producer_name": "Termelő",
            "status": "Státusz",
            "quantity": "Mennyiség (kg)",
            "created_at": "Létrehozva",
            "delivery_date": "Szállítás"
        }
        
        # Táblázat megjelenítése
        if len(filtered_data) > 0:
            st.dataframe(
                formatted_df[display_cols].rename(columns=rename_map),
                use_container_width=True,
                height=400
            )
        else:
            st.info("Nincs a szűrési feltételeknek megfelelő találat.")
            
        # Találati statisztikák
        if len(filtered_data) > 0:
            st.subheader("Statisztikák")
            
            stat_col1, stat_col2, stat_col3 = st.columns(3)
            
            with stat_col1:
                # Státusz szerinti eloszlás
                status_counts = filtered_data["status"].value_counts()
                st.markdown("#### Státusz szerinti eloszlás")
                st.bar_chart(status_counts)
            
            with stat_col2:
                # Termék szerinti eloszlás
                product_counts = filtered_data["product_name"].value_counts().head(5)
                st.markdown("#### Top 5 termék")
                st.bar_chart(product_counts)
            
            with stat_col3:
                # Mennyiség statisztikák
                st.markdown("#### Mennyiségi statisztikák")
                st.metric("Átlagos mennyiség", f"{filtered_data['quantity'].mean():.1f} kg")
                st.metric("Összes mennyiség", f"{filtered_data['quantity'].sum():,} kg".replace(",", " "))
                st.metric("Maximum mennyiség", f"{filtered_data['quantity'].max():,} kg".replace(",", " "))

# Fejlesztői információk
with st.expander("Fejlesztői információk"):
    st.markdown("""
    **Modul:** advanced_filters.py
    
    **Fő függvények:**
    - `render_date_range_slider`: Csúszkás dátumtartomány szűrő
    - `render_multiple_select`: Multiple-select státusz szűrő
    - `render_product_autocomplete`: Termék típus autocomplete
    - `render_quick_filter_chips`: Gyorsszűrő chipek
    - `render_range_slider`: Tartomány csúszka
    - `render_complete_advanced_filter_form`: Teljes szűrőpanel
    
    **Integráció az előző feladatokkal:**
    - A TASK-3.3.1 keretében létrehozott CSS keretrendszer felhasználása
    - A TASK-1.1 modern kártyás keresőpanel stílusjegyeinek továbbvitele
    - A TASK-1.3 aktív szűrő megjelenítés integrálása
    - A TASK-1.4 reszponzív design megoldások alkalmazása
    
    **Fejlesztési megközelítés:**
    - Komponens-alapú, újrafelhasználható elemek
    - JavaScript kiegészítés a jobb felhasználói élményért
    - Hibatűrő implementáció fallback megoldásokkal
    - Session state használata az állapotkövetéshez
    """)