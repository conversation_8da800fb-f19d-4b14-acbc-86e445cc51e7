"""
React-like component system for Streamlit
Implements component patterns similar to React for better code organization and reusability.
"""
import streamlit as st
import uuid
import logging
from typing import Dict, Any, Optional, Callable, List, Union
from abc import ABC, abstractmethod
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ComponentState:
    """
    Manages component-level state similar to React's useState hook.
    """
    def __init__(self, component_id: str):
        self.component_id = component_id
        self._state_key = f"component_state_{component_id}"
        
    def get_state(self, key: str, default: Any = None) -> Any:
        """Get a state value for this component."""
        if self._state_key not in st.session_state:
            st.session_state[self._state_key] = {}
        return st.session_state[self._state_key].get(key, default)
    
    def set_state(self, key: str, value: Any) -> None:
        """Set a state value for this component."""
        if self._state_key not in st.session_state:
            st.session_state[self._state_key] = {}
        st.session_state[self._state_key][key] = value
        
    def update_state(self, updates: Dict[str, Any]) -> None:
        """Update multiple state values at once."""
        if self._state_key not in st.session_state:
            st.session_state[self._state_key] = {}
        st.session_state[self._state_key].update(updates)
        
    def clear_state(self) -> None:
        """Clear all state for this component."""
        if self._state_key in st.session_state:
            del st.session_state[self._state_key]

class ComponentProps:
    """
    Manages component props similar to React props.
    """
    def __init__(self, **kwargs):
        self._props = kwargs
        
    def get(self, key: str, default: Any = None) -> Any:
        """Get a prop value."""
        return self._props.get(key, default)
        
    def __getitem__(self, key: str) -> Any:
        """Allow dict-like access to props."""
        return self._props[key]
        
    def __contains__(self, key: str) -> bool:
        """Check if prop exists."""
        return key in self._props
        
    def to_dict(self) -> Dict[str, Any]:
        """Get all props as dictionary."""
        return self._props.copy()

class BaseComponent(ABC):
    """
    Abstract base class for all React-like components.
    """
    def __init__(self, component_id: Optional[str] = None, **props):
        self.component_id = component_id or f"component_{uuid.uuid4().hex[:8]}"
        self.props = ComponentProps(**props)
        self.state = ComponentState(self.component_id)
        self._hooks = {}
        
    @abstractmethod
    def render(self) -> None:
        """
        Render the component. Must be implemented by subclasses.
        """
        pass
        
    def use_state(self, initial_value: Any, key: str = "default") -> tuple:
        """
        React-like useState hook.
        Returns (current_value, setter_function).
        """
        current_value = self.state.get_state(key, initial_value)
        
        def setter(new_value: Any):
            self.state.set_state(key, new_value)
            
        return current_value, setter
        
    def use_effect(self, effect: Callable, dependencies: List[Any] = None, key: str = "default") -> None:
        """
        React-like useEffect hook.
        Runs effect when dependencies change.
        """
        deps_key = f"effect_deps_{key}"
        effect_key = f"effect_ran_{key}"
        
        prev_deps = self.state.get_state(deps_key)
        
        # If no dependencies provided, run effect every time
        if dependencies is None:
            should_run = True
        # If dependencies changed, run effect
        elif prev_deps != dependencies:
            should_run = True
            self.state.set_state(deps_key, dependencies)
        else:
            should_run = False
            
        if should_run:
            try:
                effect()
                self.state.set_state(effect_key, True)
            except Exception as e:
                logger.error(f"Error in useEffect for component {self.component_id}: {e}")
                
    def use_memo(self, factory: Callable, dependencies: List[Any], key: str = "default") -> Any:
        """
        React-like useMemo hook.
        Memoizes expensive computations.
        """
        deps_key = f"memo_deps_{key}"
        value_key = f"memo_value_{key}"
        
        prev_deps = self.state.get_state(deps_key)
        
        if prev_deps != dependencies:
            try:
                new_value = factory()
                self.state.set_state(deps_key, dependencies)
                self.state.set_state(value_key, new_value)
                return new_value
            except Exception as e:
                logger.error(f"Error in useMemo for component {self.component_id}: {e}")
                return None
        else:
            return self.state.get_state(value_key)
            
    def use_callback(self, callback: Callable, dependencies: List[Any], key: str = "default") -> Callable:
        """
        React-like useCallback hook.
        Memoizes callback functions.
        """
        return self.use_memo(lambda: callback, dependencies, key)
        
    def component_did_mount(self) -> None:
        """
        Called when component is first rendered.
        Override in subclasses if needed.
        """
        pass
        
    def component_will_unmount(self) -> None:
        """
        Called when component is about to be destroyed.
        Override in subclasses if needed.
        """
        pass
        
    def should_component_update(self, new_props: Dict[str, Any]) -> bool:
        """
        Determine if component should re-render.
        Override in subclasses for performance optimization.
        """
        return True
        
    def get_derived_state_from_props(self, props: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update state based on props changes.
        Override in subclasses if needed.
        """
        return {}

class FunctionalComponent:
    """
    Factory for creating functional components similar to React functional components.
    """
    def __init__(self, render_func: Callable, component_id: Optional[str] = None):
        self.render_func = render_func
        self.component_id = component_id or f"func_component_{uuid.uuid4().hex[:8]}"
        self.state = ComponentState(self.component_id)
        
    def __call__(self, **props):
        """Render the functional component."""
        component_props = ComponentProps(**props)
        
        # Create context for hooks
        context = {
            'use_state': self._use_state,
            'use_effect': self._use_effect,
            'use_memo': self._use_memo,
            'use_callback': self._use_callback,
            'props': component_props,
            'component_id': self.component_id
        }
        
        return self.render_func(context)
        
    def _use_state(self, initial_value: Any, key: str = "default") -> tuple:
        """useState implementation for functional components."""
        current_value = self.state.get_state(key, initial_value)
        
        def setter(new_value: Any):
            self.state.set_state(key, new_value)
            
        return current_value, setter
        
    def _use_effect(self, effect: Callable, dependencies: List[Any] = None, key: str = "default") -> None:
        """useEffect implementation for functional components."""
        deps_key = f"effect_deps_{key}"
        prev_deps = self.state.get_state(deps_key)
        
        if dependencies is None or prev_deps != dependencies:
            try:
                effect()
                if dependencies is not None:
                    self.state.set_state(deps_key, dependencies)
            except Exception as e:
                logger.error(f"Error in useEffect for functional component {self.component_id}: {e}")
                
    def _use_memo(self, factory: Callable, dependencies: List[Any], key: str = "default") -> Any:
        """useMemo implementation for functional components."""
        deps_key = f"memo_deps_{key}"
        value_key = f"memo_value_{key}"
        
        prev_deps = self.state.get_state(deps_key)
        
        if prev_deps != dependencies:
            try:
                new_value = factory()
                self.state.set_state(deps_key, dependencies)
                self.state.set_state(value_key, new_value)
                return new_value
            except Exception as e:
                logger.error(f"Error in useMemo for functional component {self.component_id}: {e}")
                return None
        else:
            return self.state.get_state(value_key)
            
    def _use_callback(self, callback: Callable, dependencies: List[Any], key: str = "default") -> Callable:
        """useCallback implementation for functional components."""
        return self._use_memo(lambda: callback, dependencies, key)

class ComponentRegistry:
    """
    Registry for managing component instances and their lifecycle.
    """
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._components = {}
            cls._instance._mounted_components = set()
        return cls._instance
        
    def register_component(self, component: BaseComponent) -> None:
        """Register a component instance."""
        self._components[component.component_id] = component
        
    def get_component(self, component_id: str) -> Optional[BaseComponent]:
        """Get a component by ID."""
        return self._components.get(component_id)
        
    def mount_component(self, component_id: str) -> None:
        """Mark component as mounted."""
        if component_id not in self._mounted_components:
            self._mounted_components.add(component_id)
            component = self.get_component(component_id)
            if component:
                component.component_did_mount()
                
    def unmount_component(self, component_id: str) -> None:
        """Unmount a component."""
        if component_id in self._mounted_components:
            self._mounted_components.remove(component_id)
            component = self.get_component(component_id)
            if component:
                component.component_will_unmount()
                
    def cleanup_unused_components(self) -> None:
        """Clean up components that are no longer used."""
        # This would typically be called periodically to clean up orphaned components
        # Implementation depends on specific use case
        pass

def create_component(render_func: Callable, component_id: Optional[str] = None):
    """
    Decorator for creating functional components.
    
    Usage:
    @create_component
    def MyComponent(ctx):
        value, set_value = ctx['use_state'](0)
        # ... component logic
        st.write(f"Value: {value}")
    """
    return FunctionalComponent(render_func, component_id)

def with_props(**default_props):
    """
    Higher-order component decorator that provides default props.
    
    Usage:
    @with_props(title="Default Title", show_header=True)
    @create_component
    def MyComponent(ctx):
        # props will have default values merged with passed props
        title = ctx['props'].get('title')
        st.write(title)
    """
    def decorator(component_func):
        def wrapper(**props):
            merged_props = {**default_props, **props}
            return component_func(**merged_props)
        return wrapper
    return decorator

# Example components using the new system

class FilterCard(BaseComponent):
    """
    Example class-based component for a filter card.
    """
    def render(self):
        title = self.props.get('title', 'Filter')
        collapsible = self.props.get('collapsible', True)
        children = self.props.get('children', lambda: None)
        
        expanded, set_expanded = self.use_state(
            self.props.get('initial_expanded', True), 
            'expanded'
        )
        
        if collapsible:
            with st.expander(title, expanded=expanded):
                children()
        else:
            st.subheader(title)
            children()

@create_component
def ButtonComponent(ctx):
    """
    Example functional component for a button.
    """
    label = ctx['props'].get('label', 'Click me')
    on_click = ctx['props'].get('on_click', lambda: None)
    variant = ctx['props'].get('variant', 'primary')
    disabled = ctx['props'].get('disabled', False)
    
    # Use state to track click count for demo
    click_count, set_click_count = ctx['use_state'](0)
    
    # Custom styling based on variant
    button_style = {
        'primary': 'background-color: #007bff; color: white;',
        'secondary': 'background-color: #6c757d; color: white;',
        'success': 'background-color: #28a745; color: white;',
        'danger': 'background-color: #dc3545; color: white;'
    }.get(variant, 'background-color: #007bff; color: white;')
    
    if disabled:
        button_style += ' opacity: 0.5; cursor: not-allowed;'
    
    if st.button(
        f"{label} ({click_count})",
        disabled=disabled,
        key=f"{ctx['component_id']}_button"
    ):
        set_click_count(click_count + 1)
        on_click()

@create_component  
def StatCard(ctx):
    """
    Example functional component for displaying statistics.
    """
    title = ctx['props'].get('title', 'Statistic')
    value = ctx['props'].get('value', 0)
    icon = ctx['props'].get('icon', '📊')
    color = ctx['props'].get('color', '#007bff')
    
    st.markdown(f"""
    <div style="
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid {color};
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    ">
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <span style="font-size: 1.5rem;">{icon}</span>
            <div>
                <div style="font-size: 0.875rem; color: #666; text-transform: uppercase; font-weight: 500;">
                    {title}
                </div>
                <div style="font-size: 1.5rem; font-weight: bold; color: {color};">
                    {value}
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Global registry instance
component_registry = ComponentRegistry()