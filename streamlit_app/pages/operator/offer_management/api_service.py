#!/usr/bin/env python3
"""
API Service Layer for Offer Management
Valós API hívások és adatbázis kapcsolatok kezelése
"""
import logging
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, date

logger = logging.getLogger(__name__)

class OfferAPIService:
    """
    Ajánlatok API szolgáltatás - valós API hívásokkal
    Egységes interfész az összes ajánlat-kapcsolatos API művelethez
    """
    
    def __init__(self):
        """API service inicializálása"""
        self.api_available = True
        self._init_api_connections()
    
    def _init_api_connections(self):
        """API kapcsolatok inicializálása"""
        try:
            # Meglévő API függvények importálása
            from streamlit_app.api.offers import get_offers, get_offer_by_id, update_offer_status
            from streamlit_app.api.users import get_users
            from streamlit_app.api.products import get_products
            
            self.get_offers_api = get_offers
            self.get_offer_by_id_api = get_offer_by_id
            self.update_offer_status_api = update_offer_status
            self.get_users_api = get_users
            self.get_products_api = get_products
            
            logger.info("API connections initialized successfully")
            
        except ImportError as e:
            logger.warning(f"Could not import API functions: {e}")
            self.api_available = False
            self._init_fallback_methods()
    
    def _init_fallback_methods(self):
        """Fallback methods ha az API nem elérhető"""
        logger.warning("Using fallback API methods")
        
        def fallback_get_offers(filters=None):
            return True, []
        
        def fallback_get_users():
            return True, []
            
        def fallback_get_products():
            return True, []
            
        def fallback_update_status(offer_id, status):
            return True, {"id": offer_id, "status": status}
            
        self.get_offers_api = fallback_get_offers
        self.get_users_api = fallback_get_users
        self.get_products_api = fallback_get_products
        self.update_offer_status_api = fallback_update_status
    
    def get_offers_with_filters(self, filters: Dict[str, Any]) -> List[Dict]:
        """
        Szűrt ajánlatok lekérése a valós API-ból
        
        Args:
            filters: Szűrési paraméterek dictionary
            
        Returns:
            List[Dict]: Ajánlatok listája
        """
        try:
            logger.info(f"Loading offers with filters: {filters}")
            
            # API hívás a meglévő get_offers függvénnyel
            success, result = self.get_offers_api(filters)
            
            if success:
                logger.info(f"Successfully loaded {len(result)} offers from API")
                return self._normalize_offers_data(result)
            else:
                logger.error(f"API error loading offers: {result}")
                return []
                
        except Exception as e:
            logger.error(f"Exception in get_offers_with_filters: {e}")
            return []
    
    def get_producers_for_filter(self) -> List[Dict[str, Any]]:
        """
        Termelők listája szűrőhöz - valós API adatokból
        
        Returns:
            List[Dict]: Termelők listája {'id': int, 'name': str} formátumban
        """
        try:
            # Először próbáljuk meg a users API-t (termelők)
            success, users = self.get_users_api()
            
            if success and users:
                # Csak a termelőket szűrjük ki
                producers = [
                    {
                        'id': user.get('id'),
                        'name': user.get('name', 'Ismeretlen termelő'),
                        'email': user.get('email', '')
                    }
                    for user in users 
                    if user.get('role') == 'producer' or user.get('user_type') == 'producer'
                ]
                
                if producers:
                    logger.info(f"Loaded {len(producers)} producers from users API")
                    return producers
            
            # Ha nincs külön users API vagy üres, akkor ajánlatokból nyerjük ki
            logger.info("Falling back to extracting producers from offers")
            success, offers = self.get_offers_api()
            
            if success and offers:
                # Egyedi termelők kinyerése az ajánlatokból
                producers_set = set()
                producers_list = []
                
                for offer in offers:
                    producer_name = offer.get('producer_name', '').strip()
                    producer_id = offer.get('producer_id')
                    
                    if producer_name and producer_name not in producers_set:
                        producers_set.add(producer_name)
                        producers_list.append({
                            'id': producer_id or len(producers_list) + 1,
                            'name': producer_name,
                            'email': ''
                        })
                
                logger.info(f"Extracted {len(producers_list)} unique producers from offers")
                return sorted(producers_list, key=lambda x: x['name'])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting producers: {e}")
            return []
    
    def get_products_for_filter(self) -> List[Dict[str, Any]]:
        """
        Termékek listája szűrőhöz - valós API adatokból
        
        Returns:
            List[Dict]: Termékek listája {'id': int, 'name': str} formátumban
        """
        try:
            # Először próbáljuk meg a products API-t
            success, products = self.get_products_api()
            
            if success and products:
                products_list = [
                    {
                        'id': product.get('id'),
                        'name': product.get('name', 'Ismeretlen termék'),
                        'category': product.get('category', ''),
                        'unit': product.get('unit', 'kg')
                    }
                    for product in products
                ]
                
                if products_list:
                    logger.info(f"Loaded {len(products_list)} products from products API")
                    return products_list
            
            # Ha nincs külön products API, ajánlatokból nyerjük ki
            logger.info("Falling back to extracting products from offers")
            success, offers = self.get_offers_api()
            
            if success and offers:
                # Egyedi termékek kinyerése az ajánlatokból
                products_set = set()
                products_list = []
                
                for offer in offers:
                    product_name = offer.get('product_name', '').strip()
                    product_id = offer.get('product_id')
                    
                    if product_name and product_name not in products_set:
                        products_set.add(product_name)
                        products_list.append({
                            'id': product_id or len(products_list) + 1,
                            'name': product_name,
                            'category': '',
                            'unit': 'kg'
                        })
                
                logger.info(f"Extracted {len(products_list)} unique products from offers")
                return sorted(products_list, key=lambda x: x['name'])
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting products: {e}")
            return []
    
    def update_offer_status(self, offer_id: int, new_status: str) -> Tuple[bool, str]:
        """
        Ajánlat státusz frissítése a valós API-ban
        
        Args:
            offer_id: Ajánlat azonosító
            new_status: Új státusz
            
        Returns:
            Tuple[bool, str]: (success, message/error)
        """
        try:
            logger.info(f"Updating offer {offer_id} status to {new_status}")
            
            success, result = self.update_offer_status_api(offer_id, new_status)
            
            if success:
                logger.info(f"Successfully updated offer {offer_id} status")
                return True, "Státusz sikeresen frissítve"
            else:
                error_msg = f"API error: {result}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Exception updating offer status: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_offer_statistics(self, offers: List[Dict]) -> Dict[str, Any]:
        """
        Ajánlatok statisztikáinak kiszámítása valós adatokból
        
        Args:
            offers: Ajánlatok listája
            
        Returns:
            Dict: Statisztikai adatok
        """
        if not offers:
            return {
                'total_count': 0,
                'total_quantity': 0,
                'average_price': 0,
                'total_value': 0,
                'status_distribution': {},
                'product_distribution': {},
                'producer_distribution': {}
            }
        
        try:
            # Alapvető statisztikák
            total_count = len(offers)
            total_quantity = sum(offer.get('quantity_in_kg', 0) for offer in offers)
            total_value = sum(
                offer.get('quantity_in_kg', 0) * offer.get('price', 0) 
                for offer in offers
            )
            average_price = total_value / total_quantity if total_quantity > 0 else 0
            
            # Státusz megoszlás
            status_distribution = {}
            for offer in offers:
                status = offer.get('status', 'UNKNOWN')
                status_display = self._get_status_display_name(status)
                status_distribution[status_display] = status_distribution.get(status_display, 0) + 1
            
            # Termék megoszlás
            product_distribution = {}
            for offer in offers:
                product = offer.get('product_name', 'Ismeretlen')
                product_distribution[product] = product_distribution.get(product, 0) + 1
            
            # Termelő megoszlás
            producer_distribution = {}
            for offer in offers:
                producer = offer.get('producer_name', 'Ismeretlen')
                producer_distribution[producer] = producer_distribution.get(producer, 0) + 1
            
            return {
                'total_count': total_count,
                'total_quantity': total_quantity,
                'average_price': average_price,
                'total_value': total_value,
                'status_distribution': status_distribution,
                'product_distribution': product_distribution,
                'producer_distribution': producer_distribution
            }
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            return {
                'total_count': 0,
                'total_quantity': 0,
                'average_price': 0,
                'total_value': 0,
                'status_distribution': {},
                'product_distribution': {},
                'producer_distribution': {}
            }
    
    def _normalize_offers_data(self, offers: List[Dict]) -> List[Dict]:
        """
        API válasz normalizálása egységes formátumra
        
        Args:
            offers: Nyers API válasz
            
        Returns:
            List[Dict]: Normalizált ajánlatok
        """
        normalized_offers = []
        
        for offer in offers:
            try:
                normalized_offer = {
                    'id': offer.get('id'),
                    'producer_id': offer.get('producer_id'),
                    'producer_name': offer.get('producer_name', 'Ismeretlen termelő'),
                    'product_id': offer.get('product_id'),  
                    'product_name': offer.get('product_name', 'Ismeretlen termék'),
                    'quantity_in_kg': float(offer.get('quantity_in_kg', 0)),
                    'price': float(offer.get('price', 0)),
                    'status': offer.get('status', 'UNKNOWN'),
                    'created_at': offer.get('created_at', ''),
                    'updated_at': offer.get('updated_at', ''),
                    'description': offer.get('description', ''),
                    'notes': offer.get('notes', '')
                }
                
                # Dátumok normalizálása
                for date_field in ['created_at', 'updated_at']:
                    if normalized_offer[date_field]:
                        try:
                            if isinstance(normalized_offer[date_field], str):
                                # ISO formátum kezelése
                                normalized_offer[date_field] = datetime.fromisoformat(
                                    normalized_offer[date_field].replace('Z', '+00:00')
                                )
                        except Exception as e:
                            logger.warning(f"Could not parse date {normalized_offer[date_field]}: {e}")
                            normalized_offer[date_field] = None
                
                normalized_offers.append(normalized_offer)
                
            except Exception as e:
                logger.warning(f"Could not normalize offer {offer}: {e}")
                continue
        
        return normalized_offers
    
    def _get_status_display_name(self, status: str) -> str:
        """
        Státusz kód átfordítása megjelenítési névre
        
        Args:
            status: Státusz kód (pl. 'CREATED')
            
        Returns:
            str: Megjelenítési név (pl. 'Létrehozva')
        """
        status_mapping = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve',
            'CANCELLED': 'Törölve',
            'PENDING': 'Függőben'
        }
        
        return status_mapping.get(status, status)
    
    def get_filter_options(self) -> Dict[str, Any]:
        """
        Összes szűrési opció lekérése egy hívásban
        
        Returns:
            Dict: Összes szűrési opció
        """
        try:
            producers = self.get_producers_for_filter()
            products = self.get_products_for_filter()
            
            # Státusz opciók (statikus, de konfigurálható)
            status_options = [
                {'value': 'CREATED', 'label': 'Létrehozva'},
                {'value': 'CONFIRMED_BY_COMPANY', 'label': 'Megerősítve'},
                {'value': 'ACCEPTED_BY_USER', 'label': 'Elfogadva'},
                {'value': 'REJECTED_BY_USER', 'label': 'Elutasítva'},
                {'value': 'FINALIZED', 'label': 'Véglegesítve'}
            ]
            
            return {
                'producers': producers,
                'products': products,
                'status_options': status_options,
                'loaded_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting filter options: {e}")
            return {
                'producers': [],
                'products': [],
                'status_options': [],
                'loaded_at': datetime.now().isoformat()
            }

# Singleton instance a komponensek számára
_api_service_instance = None

def get_api_service() -> OfferAPIService:
    """
    API service singleton instance
    
    Returns:
        OfferAPIService: API service instance
    """
    global _api_service_instance
    if _api_service_instance is None:
        _api_service_instance = OfferAPIService()
    return _api_service_instance