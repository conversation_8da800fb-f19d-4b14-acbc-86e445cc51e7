"""
Enhanced table display with customizable columns for the offer management page.
"""
import streamlit as st
import logging
import uuid
from typing import List, Dict, Any, Optional
import pandas as pd

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
    from streamlit_app.pages.operator.offer_management.column_customization import (
        render_column_customization_ui,
        prepare_display_data,
        get_visible_columns
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
        from pages.operator.offer_management.column_customization import (
            render_column_customization_ui,
            prepare_display_data,
            get_visible_columns
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            from column_customization import (
                render_column_customization_ui,
                prepare_display_data,
                get_visible_columns
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in customizable_table.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
            
            # Minimal implementations of column customization functions
            def render_column_customization_ui():
                st.warning("Column customization is not available")
            
            def prepare_display_data(offers):
                # Basic display data preparation
                display_data = []
                for offer in offers:
                    row = {
                        "ID": offer.get("id"),
                        "Státusz": offer.get("status"),
                        "Termék": offer.get("product_type", {}).get("name", "-"),
                        "Termelő": offer.get("user", {}).get("contact_name", "-"),
                        "Mennyiség": f"{offer.get('quantity_in_kg', 0)} kg",
                        "Beszállítás": offer.get("delivery_date", "-")
                    }
                    display_data.append(row)
                return display_data
            
            def get_visible_columns():
                # Default columns
                return {
                    "id": {"display_name": "ID", "field": "id", "order": 0},
                    "status": {"display_name": "Státusz", "field": "status", "order": 1},
                    "product_name": {"display_name": "Termék", "field": "product_type.name", "order": 2},
                    "producer_name": {"display_name": "Termelő", "field": "user.contact_name", "order": 3},
                    "quantity": {"display_name": "Mennyiség", "field": "quantity_in_kg", "order": 4},
                    "delivery_date": {"display_name": "Beszállítás", "field": "delivery_date", "order": 5}
                }

# Logger setup
logger = logging.getLogger(__name__)

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def render_pagination_controls(current_page: int, total_pages: int, on_page_change: callable):
    """
    Render pagination controls for table navigation.
    
    Args:
        current_page: Current page number (1-based)
        total_pages: Total number of pages
        on_page_change: Callback function for page change
    """
    if total_pages <= 1:
        return
    
    st.markdown("---")
    
    # Create columns for pagination controls
    cols = st.columns([1, 1, 3, 1, 1])
    
    # First page button
    with cols[0]:
        first_disabled = current_page <= 1
        if st.button("⏮️ Első", disabled=first_disabled, key=generate_unique_key("first_page")):
            on_page_change(1)
    
    # Previous page button
    with cols[1]:
        prev_disabled = current_page <= 1
        if st.button("◀️ Előző", disabled=prev_disabled, key=generate_unique_key("prev_page")):
            on_page_change(current_page - 1)
    
    # Page indicator
    with cols[2]:
        st.markdown(f"<div style='text-align: center; padding: 8px 0;'>{current_page} / {total_pages}</div>", unsafe_allow_html=True)
    
    # Next page button
    with cols[3]:
        next_disabled = current_page >= total_pages
        if st.button("▶️ Következő", disabled=next_disabled, key=generate_unique_key("next_page")):
            on_page_change(current_page + 1)
    
    # Last page button
    with cols[4]:
        last_disabled = current_page >= total_pages
        if st.button("⏭️ Utolsó", disabled=last_disabled, key=generate_unique_key("last_page")):
            on_page_change(total_pages)

def display_customizable_table(offers: List[Dict[str, Any]], pagination: bool = True):
    """
    Display offers in a customizable table with pagination.
    
    Args:
        offers: List of offer dictionaries
        pagination: Whether to enable pagination
    """
    if not offers:
        st.info("Nincsenek találatok a keresési feltételekkel.")
        return
    
    # Show column customization UI
    render_column_customization_ui()
    
    # Oldaltöréses adatok
    total_offers = len(offers)
    page_size = 10
    
    # Aktuális oldal állapot kezelése
    if "current_page" not in st.session_state:
        st.session_state.current_page = 1
    
    # Oldalak száma
    total_pages = (total_offers + page_size - 1) // page_size
    
    # Ensure current page is valid
    current_page = st.session_state.current_page
    if current_page < 1 or current_page > total_pages:
        current_page = 1
        st.session_state.current_page = current_page
    
    # Jelenlegi oldal adatai
    start_idx = (current_page - 1) * page_size
    end_idx = min(start_idx + page_size, total_offers)
    paged_offers = offers[start_idx:end_idx]
    
    # Get formatted data using customizable columns
    display_data = prepare_display_data(paged_offers)
    
    # Convert to DataFrame for display
    df = pd.DataFrame(display_data)
    
    # Show the table
    if not df.empty:
        st.dataframe(df, use_container_width=True)
    else:
        st.info("Nincs megjeleníthető adat az aktuális oszlopbeállításokkal.")
    
    # Lapozási kontrollok
    if pagination and total_pages > 1:
        render_pagination_controls(
            current_page, 
            total_pages,
            lambda page: setattr(st.session_state, 'current_page', page) or st.rerun()
        )

def display_offer_table_with_actions_customizable(offers: List[Dict[str, Any]], pagination: bool = True):
    """
    Display offers in a customizable table with actions.
    Enhanced version of display_offer_table_with_actions that supports column customization.
    
    Args:
        offers: List of offer dictionaries
        pagination: Whether to enable pagination
    """
    if not offers:
        st.info("Nincsenek találatok a keresési feltételekkel.")
        return
    
    # Display the customizable table
    display_customizable_table(offers, pagination=pagination)
    
    # Oldaltöréses adatok
    total_offers = len(offers)
    page_size = 10
    
    # Oldalak száma
    total_pages = (total_offers + page_size - 1) // page_size
    
    # Jelenlegi oldal adatai
    current_page = st.session_state.get("current_page", 1)
    start_idx = (current_page - 1) * page_size
    end_idx = min(start_idx + page_size, total_offers)
    paged_offers = offers[start_idx:end_idx]
    
    # Műveletek a kiválasztott ajánlaton
    if paged_offers:
        st.write("### Műveletek")
        
        # Ajánlat azonosítók listája a kiválasztáshoz
        offer_ids = [offer.get("id") for offer in paged_offers]
        offer_names = [f"#{offer.get('id')} - {offer.get('product_type', {}).get('name', 'Ismeretlen termék')}" for offer in paged_offers]
        
        # Kiválasztó widget
        selected_index = st.selectbox(
            "Válasszon ajánlatot a műveletekhez:",
            range(len(offer_ids)),
            format_func=lambda i: offer_names[i]
        )
        
        # Kiválasztott ajánlat és státusz lekérése
        selected_offer = paged_offers[selected_index]
        selected_id = selected_offer.get("id")
        status = selected_offer.get("status", "")
        
        # Gombokat oszlopokban jelenítjük meg
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("Részletek", key=f"view_{selected_id}", use_container_width=True):
                st.session_state.selected_offer_id = selected_id
                st.rerun()
        
        with col2:
            disabled = status not in ["CREATED", "CONFIRMED_BY_COMPANY", "pending", "approved"]
            if st.button("Szerkesztés", key=f"edit_{selected_id}", use_container_width=True, disabled=disabled):
                st.session_state.selected_offer_id = selected_id
                st.session_state.edit_mode = True
                st.rerun()
        
        with col3:
            disabled = status not in ["CONFIRMED_BY_COMPANY", "approved"]
            if st.button("Elfogadás", key=f"accept_{selected_id}", use_container_width=True, disabled=disabled):
                try:
                    # Try multiple import paths for API client
                    try:
                        from streamlit_app.pages.operator.offer_management.api_client import update_offer_status
                    except ImportError:
                        try:
                            from pages.operator.offer_management.api_client import update_offer_status
                        except ImportError:
                            from api_client import update_offer_status
                    
                    success, result = update_offer_status(selected_id, "ACCEPTED_BY_USER")
                    if success:
                        st.success("Ajánlat elfogadva!")
                        st.rerun()
                    else:
                        st.error(f"Hiba: {result}")
                except Exception as e:
                    logger.error(f"Error accepting offer: {e}")
                    st.error(f"Hiba az ajánlat elfogadásakor: {str(e)}")
        
        with col4:
            if st.button("Törlés", key=f"delete_{selected_id}", use_container_width=True, type="secondary"):
                if st.checkbox(f"Biztosan törli ezt az ajánlatot: #{selected_id}?", key=f"confirm_delete_{selected_id}"):
                    try:
                        # Try multiple import paths for API client
                        try:
                            from streamlit_app.pages.operator.offer_management.api_client import update_offer_status
                        except ImportError:
                            try:
                                from pages.operator.offer_management.api_client import update_offer_status
                            except ImportError:
                                from api_client import update_offer_status
                        
                        success, result = update_offer_status(selected_id, "canceled")
                        if success:
                            st.success("Ajánlat törölve!")
                            st.rerun()
                        else:
                            st.error(f"Hiba: {result}")
                    except Exception as e:
                        logger.error(f"Error deleting offer: {e}")
                        st.error(f"Hiba az ajánlat törlésekor: {str(e)}")

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Customizable Table Test", layout="wide")
    
    st.title("Customizable Table Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "updated_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quality_grade": {"name": "I. osztály"},
            "quantity_in_kg": 500,
            "price": 350,
            "confirmed_quantity": None,
            "confirmed_price": None,
            "user": {"contact_name": "Termelő Tamás"},
            "created_by_user": {"contact_name": "Admin User"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "updated_at": "2025-04-07T09:15:00",
            "product_type": {"name": "Körte"},
            "quality_grade": {"name": "I. osztály"},
            "quantity_in_kg": 300,
            "price": 450,
            "confirmed_quantity": 250,
            "confirmed_price": 430,
            "user": {"contact_name": "Mezőgazda Márton"},
            "created_by_user": {"contact_name": "Admin User"},
            "description": "Kiváló minőségű, zamatos körte."
        }
    ]
    
    # Generate more sample data for pagination testing
    import copy
    extended_offers = []
    for i in range(25):
        offer = copy.deepcopy(sample_offers[i % 2])
        offer["id"] = i + 1
        extended_offers.append(offer)
    
    # Display with customizable table and actions
    display_offer_table_with_actions_customizable(extended_offers)