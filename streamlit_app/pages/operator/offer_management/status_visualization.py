"""
Status visualization components for the offer management page.
Provides enhanced status display with color-coding and icons.
"""
import streamlit as st
import logging
import uuid
from datetime import datetime

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error, 
        show_inline_warning, 
        show_inline_success, 
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        from utils.formatting import format_status
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            # Fallback formatting function
            format_status = lambda x: x
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in status_visualization.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x

# Logger setup
logger = logging.getLogger(__name__)

# Status configuration with colors and icons
STATUS_CONFIG = {
    "CREATED": {
        "color": "#64B5F6",  # Light Blue
        "bg_color": "#E3F2FD",
        "icon": "⭐",  # Star
        "description": "Az ajánlat létrehozva, de még nem került megerősítésre",
        "category": "Kezdeti státuszok"
    },
    "CONFIRMED_BY_COMPANY": {
        "color": "#FFA726",  # Orange
        "bg_color": "#FFF3E0",
        "icon": "✓",  # Check
        "description": "Az ajánlat megerősítve a vállalat által, várakozás a termelő visszajelzésére",
        "category": "Folyamatban lévő státuszok"
    },
    "ACCEPTED_BY_USER": {
        "color": "#66BB6A",  # Green
        "bg_color": "#E8F5E9",
        "icon": "👍",  # Thumbs up
        "description": "A termelő elfogadta az ajánlatot",
        "category": "Folyamatban lévő státuszok"
    },
    "REJECTED_BY_USER": {
        "color": "#EF5350",  # Red
        "bg_color": "#FFEBEE",
        "icon": "❌",  # Cross mark
        "description": "A termelő elutasította az ajánlatot",
        "category": "Végső státuszok" 
    },
    "FINALIZED": {
        "color": "#8D6E63",  # Brown
        "bg_color": "#EFEBE9",
        "icon": "🏁",  # Checkered flag
        "description": "Az ajánlat véglegesítve, a folyamat lezárult",
        "category": "Végső státuszok"
    },
    "MODIFIED": {
        "color": "#9575CD",  # Purple
        "bg_color": "#EDE7F6",
        "icon": "✏️",  # Pencil
        "description": "Az ajánlat módosítva lett",
        "category": "Folyamatban lévő státuszok"
    }
}

# Default values for unrecognized statuses
DEFAULT_STATUS_CONFIG = {
    "color": "#BDBDBD",  # Gray
    "bg_color": "#F5F5F5",
    "icon": "❓",  # Question mark
    "description": "Ismeretlen státusz",
    "category": "Egyéb státuszok"
}

def get_status_config(status):
    """
    Get the configuration for a specific status.
    
    Args:
        status (str): The status code
        
    Returns:
        dict: Configuration dictionary with color, icon, and other properties
    """
    if not status:
        return DEFAULT_STATUS_CONFIG
    
    # Standardize the status code to uppercase
    status_code = status.upper() if isinstance(status, str) else status
    
    # Return the configuration or the default if not found
    return STATUS_CONFIG.get(status_code, DEFAULT_STATUS_CONFIG)

def render_status_badge(status, size="medium", tooltip=True):
    """
    Render a status badge with color-coding and an icon.
    
    Args:
        status (str): The status to display
        size (str, optional): Badge size (small, medium, large). Defaults to "medium".
        tooltip (bool, optional): Whether to show the description tooltip. Defaults to True.
        
    Returns:
        str: HTML for the status badge
    """
    # Get configuration for the status
    config = get_status_config(status)
    
    # Set CSS based on the requested size
    if size == "small":
        font_size = "0.7rem"
        padding = "2px 6px"
        icon_size = "0.7rem"
    elif size == "large":
        font_size = "1rem"
        padding = "6px 12px"
        icon_size = "1.1rem"
    else:  # medium (default)
        font_size = "0.85rem"
        padding = "4px 8px"
        icon_size = "0.9rem"
    
    # Generate a unique ID for the badge
    badge_id = f"status_badge_{uuid.uuid4().hex[:8]}"
    
    # Create the tooltip attributes if enabled
    tooltip_attrs = f'title="{config["description"]}"' if tooltip else ""
    
    # Build the HTML for the badge
    html = f"""
    <div id="{badge_id}" class="status-badge" style="
        display: inline-flex;
        align-items: center;
        background-color: {config['bg_color']};
        color: {config['color']};
        border: 1px solid {config['color']};
        border-radius: 14px;
        padding: {padding};
        font-size: {font_size};
        font-weight: 500;
        white-space: nowrap;
        " {tooltip_attrs}>
        <span style="margin-right: 4px; font-size: {icon_size};">{config['icon']}</span>
        {format_status(status)}
    </div>
    """
    
    return html

def render_status_pill(status, size="medium", with_icon=True, with_text=True):
    """
    Render a pill-style status indicator that can be used in tables and cards.
    
    Args:
        status (str): The status to display
        size (str, optional): Pill size (small, medium, large). Defaults to "medium".
        with_icon (bool, optional): Whether to include the status icon. Defaults to True.
        with_text (bool, optional): Whether to include the status text. Defaults to True.
        
    Returns:
        str: HTML for the status pill
    """
    # Get configuration for the status
    config = get_status_config(status)
    
    # Set CSS based on the requested size
    if size == "small":
        height = "16px"
        font_size = "0.65rem"
        icon_size = "0.65rem"
        padding = with_text and with_icon and "0 6px" or "0 4px"
    elif size == "large":
        height = "28px" 
        font_size = "0.9rem"
        icon_size = "0.9rem"
        padding = with_text and with_icon and "0 12px" or "0 8px"
    else:  # medium (default)
        height = "22px"
        font_size = "0.75rem"
        icon_size = "0.75rem"
        padding = with_text and with_icon and "0 8px" or "0 6px"
    
    # Generate a unique ID for the pill
    pill_id = f"status_pill_{uuid.uuid4().hex[:8]}"
    
    # Determine content based on parameters
    content = ""
    if with_icon:
        content += f'<span style="margin-right: {4 if with_text else 0}px; font-size: {icon_size};">{config["icon"]}</span>'
    
    if with_text:
        content += format_status(status)
    
    # Build the HTML for the pill
    html = f"""
    <div id="{pill_id}" class="status-pill" title="{config['description']}" style="
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: {config['bg_color']};
        color: {config['color']};
        border: 1px solid {config['color']};
        border-radius: {height};
        height: {height};
        padding: {padding};
        font-size: {font_size};
        font-weight: 500;
        white-space: nowrap;
        ">
        {content}
    </div>
    """
    
    return html

def render_status_chips_legend():
    """
    Render a legend showing all possible status chips with their meanings.
    
    Returns:
        None: Displays the legend directly via st.markdown
    """
    # Group statuses by category
    categories = {}
    for status, config in STATUS_CONFIG.items():
        category = config["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append((status, config))
    
    # Create legend HTML
    legend_html = """
    <style>
    .status-legend {
        margin-bottom: 20px;
    }
    .status-category {
        margin-bottom: 10px;
    }
    .status-category-title {
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 0.9rem;
        color: #555;
    }
    .status-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    .legend-description {
        margin-left: 8px;
        font-size: 0.85rem;
        color: #666;
    }
    </style>
    <div class="status-legend">
    """
    
    # Add each category and its statuses
    for category, statuses in categories.items():
        legend_html += f"""
        <div class="status-category">
            <div class="status-category-title">{category}</div>
            <div class="status-items">
        """
        
        for status, config in statuses:
            legend_html += f"""
            <div class="legend-item">
                {render_status_pill(status, size="medium")}
                <span class="legend-description">{config["description"]}</span>
            </div>
            """
        
        legend_html += """
            </div>
        </div>
        """
    
    legend_html += "</div>"
    
    # Display the legend
    st.markdown(legend_html, unsafe_allow_html=True)

def render_status_progress_bar(current_status):
    """
    Render a workflow progress bar visualizing the status journey.
    
    Args:
        current_status (str): The current status of the offer
        
    Returns:
        None: Displays the progress bar directly via st.markdown
    """
    # Define the standard workflow steps
    workflow_steps = [
        "CREATED", 
        "CONFIRMED_BY_COMPANY", 
        "ACCEPTED_BY_USER", 
        "FINALIZED"
    ]
    
    # Handle rejected status (branch)
    is_rejected = current_status == "REJECTED_BY_USER"
    
    # Standardize the status code
    current_status_code = current_status.upper() if isinstance(current_status, str) else current_status
    
    # Determine current step index
    if is_rejected:
        current_step_index = workflow_steps.index("CONFIRMED_BY_COMPANY") + 0.5  # Put rejection between confirmed and accepted
    else:
        try:
            current_step_index = workflow_steps.index(current_status_code)
        except ValueError:
            # Status not in standard workflow, use default value
            current_step_index = -1
    
    # Generate a unique ID for the progress bar
    progress_id = f"status_progress_{uuid.uuid4().hex[:8]}"
    
    # Create HTML for the progress bar
    progress_html = f"""
    <style>
    .workflow-progress-{progress_id} {{
        margin: 20px 0;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
    }}
    .progress-line-{progress_id} {{
        position: absolute;
        height: 3px;
        background-color: #e0e0e0;
        top: 50%;
        left: 5%;
        right: 5%;
        transform: translateY(-50%);
        z-index: 1;
    }}
    .progress-line-filled-{progress_id} {{
        position: absolute;
        height: 3px;
        background-color: #4CAF50;
        top: 50%;
        left: 5%;
        transform: translateY(-50%);
        z-index: 2;
        transition: width 0.3s ease;
    }}
    .rejected-line-{progress_id} {{
        position: absolute;
        height: 3px;
        background-color: #EF5350;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        transition: width 0.3s ease;
    }}
    .step-{progress_id} {{
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #ffffff;
        border: 2px solid #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 3;
    }}
    .step-active-{progress_id} {{
        border-color: #4CAF50;
        background-color: #E8F5E9;
    }}
    .step-completed-{progress_id} {{
        border-color: #4CAF50;
        background-color: #4CAF50;
        color: white;
    }}
    .step-rejected-{progress_id} {{
        border-color: #EF5350;
        background-color: #FFEBEE;
    }}
    .step-label-{progress_id} {{
        position: absolute;
        font-size: 0.7rem;
        white-space: nowrap;
        top: 35px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        color: #757575;
    }}
    .step-label-active-{progress_id} {{
        font-weight: bold;
        color: #333333;
    }}
    </style>
    
    <div class="workflow-progress-{progress_id}">
        <div class="progress-line-{progress_id}"></div>
    """
    
    # Add filled progress line
    if current_step_index >= 0:
        step_count = len(workflow_steps)
        if is_rejected:
            # Main progress line (to confirmation)
            progress_html += f"""
            <div class="progress-line-filled-{progress_id}" style="width: {((current_step_index - 0.5) / (step_count - 1)) * 90}%;"></div>
            """
            # Rejected branch line
            progress_html += f"""
            <div class="rejected-line-{progress_id}" style="width: 5%; left: {((current_step_index - 0.5) / (step_count - 1)) * 90 + 5}%;"></div>
            """
        else:
            # Normal progress line
            progress_html += f"""
            <div class="progress-line-filled-{progress_id}" style="width: {(current_step_index / (step_count - 1)) * 90}%;"></div>
            """
    
    # Add step markers for main workflow
    for i, step in enumerate(workflow_steps):
        config = get_status_config(step)
        status_name = format_status(step)
        
        # Determine step status
        if is_rejected and i > 1:  # After confirmation, all steps are inactive
            step_class = ""
            icon = ""
        elif current_step_index >= i:
            if i == current_step_index:
                step_class = f"step-active-{progress_id}"
                icon = config["icon"]
            else:
                step_class = f"step-completed-{progress_id}"
                icon = "✓"
        else:
            step_class = ""
            icon = ""
        
        # Add step marker
        progress_html += f"""
        <div class="step-{progress_id} {step_class}" style="color: {config['color']};">
            {icon}
            <div class="step-label-{progress_id} {f'step-label-active-{progress_id}' if i == current_step_index else ''}">
                {status_name}
            </div>
        </div>
        """
    
    # Add rejection branch if applicable
    if is_rejected:
        rejected_config = get_status_config("REJECTED_BY_USER")
        rejection_position = ((workflow_steps.index("CONFIRMED_BY_COMPANY") + 0.5) / (len(workflow_steps) - 1)) * 90 + 10
        
        progress_html += f"""
        <div class="step-{progress_id} step-rejected-{progress_id}" 
             style="color: {rejected_config['color']}; position: absolute; left: {rejection_position}%; top: -15px;">
            {rejected_config['icon']}
            <div class="step-label-{progress_id} step-label-active-{progress_id}">
                {format_status("REJECTED_BY_USER")}
            </div>
        </div>
        """
    
    # Close the progress bar div
    progress_html += "</div>"
    
    # Display the progress bar
    st.markdown(progress_html, unsafe_allow_html=True)

def display_status_transition_history(logs):
    """
    Display a timeline of status transitions with icons and colors.
    
    Args:
        logs (list): List of status change log entries
        
    Returns:
        None: Displays the timeline directly via st.markdown
    """
    if not logs:
        st.info("Nincs státuszváltozási előzmény")
        return
    
    # Sort logs by timestamp (most recent first)
    try:
        sorted_logs = sorted(logs, key=lambda x: datetime.fromisoformat(x.get("timestamp").replace("Z", "+00:00")), reverse=True)
    except (ValueError, AttributeError):
        # Fallback if timestamp parsing fails
        sorted_logs = logs
    
    # Create the timeline HTML
    timeline_html = """
    <style>
    .status-timeline {
        position: relative;
        margin: 20px 0;
        padding-left: 30px;
    }
    .timeline-line {
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e0e0e0;
        z-index: 1;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
        padding-bottom: 5px;
    }
    .timeline-dot {
        position: absolute;
        left: -30px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
    }
    .timeline-content {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 10px 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .timeline-time {
        font-size: 0.75rem;
        color: #757575;
        margin-bottom: 5px;
    }
    .timeline-status {
        font-weight: 600;
        margin-bottom: 5px;
    }
    .timeline-meta {
        font-size: 0.85rem;
        color: #555;
    }
    </style>
    
    <div class="status-timeline">
        <div class="timeline-line"></div>
    """
    
    # Add each log entry to the timeline
    for log in sorted_logs:
        # Get data from log
        try:
            timestamp = datetime.fromisoformat(log.get("timestamp").replace("Z", "+00:00"))
            formatted_time = timestamp.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, AttributeError):
            formatted_time = log.get("timestamp", "Ismeretlen időpont")
        
        new_status = log.get("new_status", "Ismeretlen")
        old_status = log.get("old_status", "Ismeretlen")
        user_name = log.get("user_name", "Rendszer")
        
        # Get configuration for the status
        config = get_status_config(new_status)
        
        # Add the timeline item
        timeline_html += f"""
        <div class="timeline-item">
            <div class="timeline-dot" style="background-color: {config['bg_color']}; border: 2px solid {config['color']};">
                <span style="color: {config['color']};">{config['icon']}</span>
            </div>
            <div class="timeline-content">
                <div class="timeline-time">{formatted_time}</div>
                <div class="timeline-status" style="color: {config['color']};">
                    {format_status(old_status) if old_status != "Ismeretlen" else "-"} → {format_status(new_status)}
                </div>
                <div class="timeline-meta">
                    {user_name}
                </div>
            </div>
        </div>
        """
    
    # Close the timeline div
    timeline_html += "</div>"
    
    # Display the timeline
    st.markdown(timeline_html, unsafe_allow_html=True)

def inject_status_styles():
    """
    Inject CSS for status elements.
    
    Returns:
        None: Injects CSS via st.markdown
    """
    css = """
    <style>
    .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        border-radius: 14px !important;
        font-weight: 500 !important;
        white-space: nowrap !important;
    }
    
    .status-pill {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
        white-space: nowrap !important;
    }
    
    /* Status colors for table cells */
    .dataframe td:nth-child(2) {
        position: relative;
        padding-left: 30px !important;
    }
    
    .dataframe td:nth-child(2)::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }
    
    /* Status color indicators */
    .status-created::before { background-color: #64B5F6 !important; }
    .status-confirmed::before { background-color: #FFA726 !important; }
    .status-accepted::before { background-color: #66BB6A !important; }
    .status-rejected::before { background-color: #EF5350 !important; }
    .status-finalized::before { background-color: #8D6E63 !important; }
    </style>
    """
    
    st.markdown(css, unsafe_allow_html=True)

def display_enhanced_status_legend():
    """
    Display a visually enhanced status legend with color indicators and descriptions.
    Replacement for ui_components.display_status_legend().
    
    Returns:
        None: Displays the legend directly via st.markdown
    """
    st.markdown("### Státuszok magyarázata")
    
    # Group statuses by category
    categories = {}
    for status, config in STATUS_CONFIG.items():
        category = config["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append((status, config))
    
    # Create legend HTML
    legend_html = """
    <style>
    .status-legend-container {
        margin-bottom: 20px;
    }
    .status-category-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        overflow: hidden;
    }
    .status-category-header {
        padding: 8px 16px;
        font-weight: 600;
        background-color: #f5f5f5;
        border-bottom: 1px solid #eee;
    }
    .status-items-container {
        padding: 12px 16px;
    }
    .status-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f5f5f5;
    }
    .status-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
    .status-item-content {
        margin-left: 10px;
        flex-grow: 1;
    }
    .status-name {
        font-weight: 500;
        margin-bottom: 3px;
    }
    .status-description {
        font-size: 0.85rem;
        color: #666;
    }
    </style>
    
    <div class="status-legend-container">
    """
    
    # Add each category and its statuses
    for category, statuses in categories.items():
        legend_html += f"""
        <div class="status-category-card">
            <div class="status-category-header">{category}</div>
            <div class="status-items-container">
        """
        
        for status, config in statuses:
            legend_html += f"""
            <div class="status-item">
                {render_status_pill(status, size="medium")}
                <div class="status-item-content">
                    <div class="status-name" style="color: {config['color']};">{format_status(status)}</div>
                    <div class="status-description">{config['description']}</div>
                </div>
            </div>
            """
        
        legend_html += """
            </div>
        </div>
        """
    
    legend_html += "</div>"
    
    # Display the legend
    st.markdown(legend_html, unsafe_allow_html=True)

def format_colored_status_cell(status):
    """
    Format a status cell with color coding for tables.
    
    Args:
        status (str): The status to format
        
    Returns:
        str: HTML for the status cell
    """
    return f'<div style="display:flex;align-items:center;">{render_status_pill(status)}</div>'

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Status Visualization Test", layout="wide")
    
    st.title("Status Visualization Components")
    
    # Inject CSS
    inject_status_styles()
    
    # Test all status badges
    st.header("Status Badges")
    st.subheader("Sizes Comparison")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### Small")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_badge(status, size="small"), unsafe_allow_html=True)
    
    with col2:
        st.markdown("#### Medium (Default)")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_badge(status), unsafe_allow_html=True)
    
    with col3:
        st.markdown("#### Large")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_badge(status, size="large"), unsafe_allow_html=True)
    
    # Test status pills
    st.header("Status Pills")
    st.subheader("Different Variations")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### Icon and Text")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_pill(status), unsafe_allow_html=True)
    
    with col2:
        st.markdown("#### Icon Only")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_pill(status, with_text=False), unsafe_allow_html=True)
    
    with col3:
        st.markdown("#### Text Only")
        for status in STATUS_CONFIG.keys():
            st.markdown(render_status_pill(status, with_icon=False), unsafe_allow_html=True)
    
    # Test status legend
    st.header("Status Legend")
    render_status_chips_legend()
    
    # Test enhanced status legend
    st.header("Enhanced Status Legend")
    display_enhanced_status_legend()
    
    # Test progress bar
    st.header("Status Progress Bar")
    
    status_options = list(STATUS_CONFIG.keys())
    selected_status = st.selectbox("Select a status", status_options)
    
    render_status_progress_bar(selected_status)
    
    # Test status history timeline
    st.header("Status History Timeline")
    
    # Create sample logs
    sample_logs = [
        {
            "timestamp": "2025-05-10T10:15:30Z",
            "old_status": None,
            "new_status": "CREATED",
            "user_name": "Termelő Tamás"
        },
        {
            "timestamp": "2025-05-12T14:25:45Z",
            "old_status": "CREATED",
            "new_status": "CONFIRMED_BY_COMPANY",
            "user_name": "Operátor Olga"
        },
        {
            "timestamp": "2025-05-15T09:10:15Z",
            "old_status": "CONFIRMED_BY_COMPANY",
            "new_status": "ACCEPTED_BY_USER",
            "user_name": "Termelő Tamás"
        },
        {
            "timestamp": "2025-05-20T11:30:00Z",
            "old_status": "ACCEPTED_BY_USER",
            "new_status": "FINALIZED",
            "user_name": "Rendszer"
        }
    ]
    
    display_status_transition_history(sample_logs)
    
    # Show rejection example
    st.subheader("Rejection Example")
    
    rejected_logs = [
        {
            "timestamp": "2025-05-10T10:15:30Z",
            "old_status": None,
            "new_status": "CREATED",
            "user_name": "Termelő Tamás"
        },
        {
            "timestamp": "2025-05-12T14:25:45Z",
            "old_status": "CREATED",
            "new_status": "CONFIRMED_BY_COMPANY",
            "user_name": "Operátor Olga"
        },
        {
            "timestamp": "2025-05-15T09:10:15Z",
            "old_status": "CONFIRMED_BY_COMPANY",
            "new_status": "REJECTED_BY_USER",
            "user_name": "Termelő Tamás"
        }
    ]
    
    # Show rejection workflow
    render_status_progress_bar("REJECTED_BY_USER")
    
    # Show rejection timeline
    display_status_transition_history(rejected_logs)