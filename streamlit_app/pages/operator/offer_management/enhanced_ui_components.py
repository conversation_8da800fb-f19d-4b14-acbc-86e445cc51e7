"""
<PERSON><PERSON>lett felhasználói felület komponensek az ajánlatkezeléshez.
Ezek a komponensek fejlettebb interakciós lehetőségeket biztosítanak.
"""
import streamlit as st
import uuid
import logging
from datetime import datetime, timedelta
import app_config as config

# Try multiple import paths with fallbacks for formatting utilities
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_date, format_quantity, format_price, format_datetime
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_date, format_quantity, format_price, format_datetime
    except ImportError:
        # Fallback formatting functions if import fails
        logging.warning("Could not import formatting functions in enhanced_ui_components.py, using fallbacks")
        format_status = lambda x: x
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

logger = logging.getLogger(__name__)

def display_mobile_offer_card(offer, index, on_click_func=None):
    """
    Mobilbarát kártya megjelenítése az ajánlathoz.
    
    Ez a függvény mobileszközökre optimalizált, kompakt kártyaként jeleníti meg
    az ajánlat legfontosabb információit.
    
    Args:
        offer (dict): Az ajánlat adatai
        index (int): Az ajánlat sorszáma/indexe a listában
        on_click_func (callable, optional): Függvény, ami meghívódik a kártyára kattintáskor
    
    Returns:
        None
    """
    # Adatok kinyerése
    offer_id = offer.get("id")
    status = offer.get("status", "")
    status_text = format_status(status)
    
    # Termék és termelő adatok
    product_name = offer.get("product_type", {}).get("name", "Ismeretlen termék")
    producer_name = offer.get("user", {}).get("company_name", "") or offer.get("user", {}).get("contact_name", "Ismeretlen termelő")
    
    # Mennyiség és dátum
    quantity = format_quantity(offer.get("quantity_in_kg", 0))
    delivery_date = format_date(offer.get("delivery_date", ""))
    
    # Státusz színkód
    status_colors = {
        "CREATED": "#FFD700",         # Arany
        "CONFIRMED_BY_COMPANY": "#87CEFA",  # Világoskék
        "ACCEPTED_BY_USER": "#98FB98",  # Világoszöld
        "REJECTED_BY_USER": "#FFA07A",  # Világospiros
        "FINALIZED": "#20B2AA",      # Türkiz
        "pending": "#FFC107",      # Sárga
        "approved": "#28A745",     # Zöld
        "rejected": "#DC3545",     # Piros
        "in_progress": "#17A2B8",  # Kék
        "completed": "#6C757D",    # Szürke
        "canceled": "#343A40"      # Sötétszürke
    }
    status_color = status_colors.get(status, "#6c757d")
    
    # Kártya HTML kód generálása
    card_html = f"""
    <div style="border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px; padding: 15px; 
                background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); position: relative;
                transition: transform 0.2s, box-shadow 0.2s;">
        <div style="position: absolute; top: 0; right: 0; height: 100%; width: 8px; 
                    background-color: {status_color}; border-top-right-radius: 8px; 
                    border-bottom-right-radius: 8px;"></div>
        <div style="font-weight: bold; font-size: 16px; margin-bottom: 8px;">
            {product_name} <span style="color: #6c757d; font-size: 12px;">#{offer_id}</span>
        </div>
        <div style="color: #555; margin-bottom: 8px;">Termelő: {producer_name}</div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Mennyiség: {quantity}</span>
            <span>Beszállítás: {delivery_date}</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
            <div style="background-color: {status_color}; color: white; padding: 4px 10px; 
                     border-radius: 15px; font-size: 12px; box-shadow: 0 2px 3px rgba(0,0,0,0.1);">
                {status_text}
            </div>
            <button 
                onclick="selectOffer_{index}()"
                style="background-color: #3584e4; color: white; border: none; 
                       padding: 6px 12px; border-radius: 4px; cursor: pointer;
                       font-weight: bold; transition: background-color 0.2s;">
                Megtekintés
            </button>
        </div>
    </div>
    
    <script>
    function selectOffer_{index}() {{
        // Az ajánlat kiválasztása a session state-ben
        const input = document.getElementById('selected_offer_{index}');
        if (input) {{
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }}
    }}
    
    // Kártyára kattintás kezelése
    document.addEventListener('DOMContentLoaded', function() {{
        const card = document.querySelector('.offer-card-{index}');
        if (card) {{
            card.addEventListener('click', function(e) {{
                // Ne kattintsunk, ha a gombra kattintottak
                if (e.target.tagName !== 'BUTTON') {{
                    selectOffer_{index}();
                }}
            }});
        }}
    }});
    </script>
    """
    
    # HTML megjelenítése
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Rejtett input a kiválasztás követéséhez
    selected = st.text_input(f"Selected Offer {index}", value="false", key=f"selected_offer_{index}", label_visibility="collapsed")
    
    # Ellenőrizzük, hogy kiválasztották-e az ajánlatot
    if selected.lower() == "true" and on_click_func:
        on_click_func()

def render_quantity_input(label, key, value=None, min_value=0.1, max_value=10000.0, step=0.1, help=None):
    """
    Mennyiség beviteli mező renderelése beépített validációval.
    
    Ez a függvény egy mennyiség beviteli mezőt jelenít meg valós idejű validációval
    és vizuális visszajelzéssel a felhasználó számára.
    
    Args:
        label (str): A mező címkéje.
        key (str): Egyedi kulcs a komponenshez.
        value (float, optional): Alapértelmezett érték. Defaults to None.
        min_value (float, optional): Minimum megengedett érték. Defaults to 0.1.
        max_value (float, optional): Maximum megengedett érték. Defaults to 10000.0.
        step (float, optional): Lépésköz. Defaults to 0.1.
        help (str, optional): Segítő szöveg. Defaults to None.
        
    Returns:
        float/None: A megadott mennyiség vagy None, ha érvénytelen.
    """
    # Ha nincs megadva value, üres stringet használunk kezdőértéknek
    if value is None:
        value = ""
    else:
        # Biztosítsuk, hogy a kezdőérték string legyen
        value = str(value).replace('.', ',')
    
    # Előző érték nyilvántartása a session state-ben
    if f"{key}_prev" not in st.session_state:
        st.session_state[f"{key}_prev"] = value
    
    # Hiba állapot nyilvántartása
    if f"{key}_error" not in st.session_state:
        st.session_state[f"{key}_error"] = ""
    
    # Előre meghatározott CSS a szebb megjelenéshez
    input_css = """
    <style>
    .quantity-input {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 10px;
        width: 100%;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    .quantity-input:focus {
        border-color: #3584e4;
        outline: none;
        box-shadow: 0 0 0 2px rgba(53, 132, 228, 0.3);
    }
    .quantity-error {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 4px;
    }
    </style>
    """
    
    # A beviteli mező megjelenítése
    input_container = st.container()
    with input_container:
        st.markdown(input_css, unsafe_allow_html=True)
        col1, col2 = st.columns([3, 1])
        
        # Beviteli mező saját column-ban
        with col1:
            quantity_str = st.text_input(
                label=label,
                value=value,
                key=key,
                help=help or f"Adjon meg egy számot {min_value} és {max_value} között."
            )
        
        # Validáció
        quantity = None
        if quantity_str:
            try:
                # Vessző helyettesítése ponttal
                quantity_str = quantity_str.replace(',', '.')
                quantity = float(quantity_str)
                
                # Érték tartomány ellenőrzése
                if quantity < min_value:
                    st.session_state[f"{key}_error"] = f"A minimum érték {min_value}."
                elif quantity > max_value:
                    st.session_state[f"{key}_error"] = f"A maximum érték {max_value}."
                else:
                    st.session_state[f"{key}_error"] = ""
                    st.session_state[f"{key}_prev"] = quantity
            except ValueError:
                st.session_state[f"{key}_error"] = "A mennyiségnek számnak kell lennie."
                quantity = None
        else:
            st.session_state[f"{key}_error"] = "Ez a mező kötelező."
            quantity = None
        
        # Hibaüzenet megjelenítése
        if st.session_state[f"{key}_error"]:
            st.markdown(f'<div class="quantity-error">{st.session_state[f"{key}_error"]}</div>', unsafe_allow_html=True)
        
    return quantity

def render_section_card(title, content, color="#3584e4", icon=None, is_mobile=False, key=None, expanded=True):
    """
    Kártyaként megjelenített tartalom szekció.
    
    Args:
        title (str): A kártya címe
        content (callable): Függvény, amely a kártya tartalmát rendereli
        color (str, optional): A kártya fejlécének színe. Defaults to "#3584e4".
        icon (str, optional): Ikon a kártya fejlécéhez. Defaults to None.
        is_mobile (bool, optional): Mobilnézet-e. Defaults to False.
        key (str, optional): Egyedi kulcs a kártyához. Defaults to None.
        expanded (bool, optional): Alapértelmezetten kinyitott-e. Ha None, akkor nem összecsukható. Defaults to True.
    """
    # Egyedi kulcs generálása
    if key is None:
        key = f"card_{str(uuid.uuid4())[:8]}"
    
    # Állapot inicializálása a session state-ben
    if expanded is not None and f"card_expanded_{key}" not in st.session_state:
        st.session_state[f"card_expanded_{key}"] = expanded
    
    # Mobilbarát méretezés
    card_width = "100%" if is_mobile else "95%"
    
    # CSS stílus a kártyához
    card_css = f"""
    <style>
    .card-{key} {{
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        width: {card_width};
        overflow: hidden;
    }}
    
    .card-header-{key} {{
        background-color: {color};
        color: white;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        cursor: {f"pointer" if expanded is not None else "default"};
    }}
    
    .card-icon-{key} {{
        margin-right: 10px;
        font-size: 1.2em;
    }}
    
    .card-title-{key} {{
        font-weight: bold;
        font-size: 1.1em;
        flex-grow: 1;
    }}
    
    .card-toggle-{key} {{
        font-size: 1.2em;
    }}
    
    .card-content-{key} {{
        padding: 15px;
        background-color: white;
        transition: max-height 0.3s ease-out;
        overflow: hidden;
    }}
    
    @keyframes fadeIn {{
        from {{ opacity: 0; }}
        to {{ opacity: 1; }}
    }}
    </style>
    """
    
    # JavaScript a kártya összecsukásához/kinyitásához
    toggle_js = ""
    if expanded is not None:
        toggle_js = f"""
        <script>
        // Kártya összecsukás/kinyitás JS
        document.addEventListener('DOMContentLoaded', function() {{
            const cardHeader = document.querySelector('.card-header-{key}');
            const cardContent = document.querySelector('.card-content-{key}');
            const cardToggle = document.querySelector('.card-toggle-{key}');
            
            if (cardHeader && cardContent && cardToggle) {{
                cardHeader.addEventListener('click', function() {{
                    // Váltjuk a láthatóságot
                    if (cardContent.style.display === 'none') {{
                        cardContent.style.display = 'block';
                        cardContent.style.animation = 'fadeIn 0.3s ease-out';
                        cardToggle.textContent = '▼';
                        
                        // Állapot frissítése rejtett mező használatával
                        const stateInput = document.getElementById('card_state_{key}');
                        if (stateInput) {{
                            stateInput.value = 'expanded';
                            stateInput.dispatchEvent(new Event('change'));
                        }}
                    }} else {{
                        cardContent.style.display = 'none';
                        cardToggle.textContent = '▶';
                        
                        // Állapot frissítése rejtett mező használatával
                        const stateInput = document.getElementById('card_state_{key}');
                        if (stateInput) {{
                            stateInput.value = 'collapsed';
                            stateInput.dispatchEvent(new Event('change'));
                        }}
                    }}
                }});
            }}
        }});
        </script>
        """
    
    # A kártya HTML
    is_expanded = st.session_state.get(f"card_expanded_{key}", expanded) if expanded is not None else True
    toggle_button = f'<div class="card-toggle-{key}">{"▼" if is_expanded else "▶"}</div>' if expanded is not None else ''
    
    header_html = f"""
    <div class="card-header-{key}">
        <div class="card-icon-{key}">{icon or ""}</div>
        <div class="card-title-{key}">{title}</div>
        {toggle_button}
    </div>
    """
    
    # A teljes kártya HTML (fejléc + tartalom container)
    card_html = f"""
    {card_css}
    <div class="card-{key}">
        {header_html}
        <div class="card-content-{key}" style="display: {'block' if is_expanded else 'none'};">
            <!-- A tartalom itt lesz dinamikusan beillesztve Streamlit által -->
        </div>
    </div>
    {toggle_js}
    """
    
    # A kártya renderelése
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Ha a kártya ki van nyitva, akkor rendereljük a tartalmát
    if expanded is None or is_expanded:
        # Létrehozunk egy kontextust a tartalom rendereléshez
        content_placeholder = st.container()
        
        # A tartalmat a létrehozott konténer kontextusában rendereljük
        with content_placeholder:
            content()  # Meghívjuk a tartalmat renderelő függvényt
    
    # Kártya állapotának követése
    if expanded is not None:
        card_state = st.text_input(
            f"Card State {key}", 
            value="expanded" if is_expanded else "collapsed", 
            key=f"card_state_{key}", 
            label_visibility="collapsed"
        )
        
        # Ha változott az állapot, frissítjük a session state-et
        if card_state == "expanded" and not is_expanded:
            st.session_state[f"card_expanded_{key}"] = True
            st.rerun()
        elif card_state == "collapsed" and is_expanded:
            st.session_state[f"card_expanded_{key}"] = False
            st.rerun()

def render_confirmation_dialog(title, message, confirm_text="Igen", cancel_text="Nem", icon="⚠️", key=None):
    """
    Megerősítő párbeszédablak renderelése kritikus műveletekhez.
    
    Ez a függvény egy megerősítő párbeszédablakot jelenít meg, amely
    a felhasználó explicit megerősítését kéri a kritikus műveletek
    (pl. törlés, státuszváltások) előtt.
    
    Args:
        title (str): A párbeszédablak címe.
        message (str): A megerősítő üzenet.
        confirm_text (str, optional): A megerősítő gomb szövege. Defaults to "Igen".
        cancel_text (str, optional): A mégsem gomb szövege. Defaults to "Nem".
        icon (str, optional): Az ikon. Defaults to "⚠️".
        key (str, optional): Egyedi kulcs a komponenshez. Defaults to None.
        
    Returns:
        bool: True ha a felhasználó megerősíti, False egyébként.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"confirm_dialog_{str(uuid.uuid4())[:8]}"
    
    # Ellenőrizzük, hogy a dialógus állapotát tároltuk-e már
    if f"{key}_open" not in st.session_state:
        st.session_state[f"{key}_open"] = False
        st.session_state[f"{key}_confirmed"] = False
    
    # Ha a dialógus nem nyitott, visszatérünk
    if not st.session_state[f"{key}_open"]:
        return False
    
    # A dialógus megjelenítése
    dialog_container = st.container()
    with dialog_container:
        # Stílus a dialógushoz
        st.markdown(f"""
        <style>
        .dialog-overlay-{key} {{
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        
        .dialog-container-{key} {{
            background-color: var(--background-color, white);
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            animation: dialog-fade-in-{key} 0.3s ease;
        }}
        
        @keyframes dialog-fade-in-{key} {{
            from {{ opacity: 0; transform: translateY(-20px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        .dialog-title-{key} {{
            background-color: #f0f2f6;
            padding: 15px;
            font-weight: bold;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }}
        
        .dialog-title-icon-{key} {{
            font-size: 1.4em;
            margin-right: 10px;
        }}
        
        .dialog-content-{key} {{
            padding: 20px 15px;
            font-size: 1.05em;
            line-height: 1.4;
        }}
        
        .dialog-buttons-{key} {{
            display: flex;
            justify-content: flex-end;
            padding: 10px 15px 20px;
            gap: 15px;
        }}
        
        .dialog-button-{key} {{
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }}
        
        .dialog-button-{key}:hover {{
            opacity: 0.9;
            transform: translateY(-2px);
        }}
        
        .dialog-confirm-{key} {{
            background-color: #ff4b4b;
            color: white;
        }}
        
        .dialog-cancel-{key} {{
            background-color: #f0f2f6;
            color: #333;
        }}
        </style>
        
        <div class="dialog-overlay-{key}">
            <div class="dialog-container-{key}">
                <div class="dialog-title-{key}">
                    <span class="dialog-title-icon-{key}">{icon}</span>
                    <span>{title}</span>
                </div>
                <div class="dialog-content-{key}">
                    {message}
                </div>
                <div class="dialog-buttons-{key}">
                    <button class="dialog-button-{key} dialog-cancel-{key}" id="dialog_cancel_btn_{key}">{cancel_text}</button>
                    <button class="dialog-button-{key} dialog-confirm-{key}" id="dialog_confirm_btn_{key}">{confirm_text}</button>
                </div>
            </div>
        </div>
        
        <script>
            // Gomb eseménykezelők hozzáadása
            document.addEventListener('DOMContentLoaded', function() {{
                const confirmBtn = document.getElementById('dialog_confirm_btn_{key}');
                const cancelBtn = document.getElementById('dialog_cancel_btn_{key}');
                const resultInput = document.getElementById('{key}_result');
                
                if (confirmBtn && resultInput) {{
                    confirmBtn.addEventListener('click', function() {{
                        resultInput.value = 'true';
                        resultInput.dispatchEvent(new Event('change'));
                    }});
                }}
                
                if (cancelBtn && resultInput) {{
                    cancelBtn.addEventListener('click', function() {{
                        resultInput.value = 'false';
                        resultInput.dispatchEvent(new Event('change'));
                    }});
                }}
                
                // ESC gomb kezelése a dialógushoz
                document.addEventListener('keydown', function(e) {{
                    if (e.key === 'Escape' && resultInput) {{
                        resultInput.value = 'false';
                        resultInput.dispatchEvent(new Event('change'));
                    }}
                }});
            }});
        </script>
        """, unsafe_allow_html=True)
        
        # Gomb állapot és eredmény követése
        result = st.text_input(f"Dialog result", value="waiting", key=f"{key}_result", label_visibility="collapsed")
        
        if result == "true":
            st.session_state[f"{key}_confirmed"] = True
            st.session_state[f"{key}_open"] = False
            return True
        elif result == "false":
            st.session_state[f"{key}_confirmed"] = False
            st.session_state[f"{key}_open"] = False
            return False
    
    return False

def open_confirmation_dialog(title, message, confirm_text="Igen", cancel_text="Nem", icon="⚠️", key=None):
    """
    Megnyit egy megerősítő dialógust egy adott kulccsal.
    
    Ez a segédfüggvény beállítja a session state-et, hogy egy megerősítő 
    dialógus megjelenjen a következő újbóli futtatáskor.
    
    Args:
        title (str): A dialógus címe
        message (str): A megerősítendő üzenet
        confirm_text (str, optional): A megerősítő gomb szövege. Defaults to "Igen".
        cancel_text (str, optional): A mégsem gomb szövege. Defaults to "Nem".
        icon (str, optional): A dialógus ikonja. Defaults to "⚠️".
        key (str, optional): Egyedi kulcs a dialógushoz. Defaults to None.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"confirm_dialog_{str(uuid.uuid4())[:8]}"
    
    # Beállítjuk a dialógus állapotát
    dialog_key = f"{key}_open"
    st.session_state[dialog_key] = True
    
    # Tároljuk a dialógus paramétereit a session state-ben
    st.session_state[f"{key}_title"] = title
    st.session_state[f"{key}_message"] = message
    st.session_state[f"{key}_confirm_text"] = confirm_text
    st.session_state[f"{key}_cancel_text"] = cancel_text
    st.session_state[f"{key}_icon"] = icon
    
    # Újratelenítjük az oldalt, hogy a dialógus megjelenjen
    st.rerun()

def show_inline_error(error_message):
    """
    Inline hibaüzenet megjelenítése közvetlenül a hiba után.
    
    Args:
        error_message (str): A megjelenítendő hibaüzenet.
    """
    if error_message:
        st.markdown(f"""
        <div style="color: #721c24; background-color: #f8d7da; 
                    border: 1px solid #f5c6cb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #dc3545;">
            <strong>⚠️ Hiba:</strong> {error_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_warning(warning_message):
    """
    Inline figyelmeztető üzenet megjelenítése közvetlenül a mező után.
    
    Args:
        warning_message (str): A megjelenítendő figyelmeztetés.
    """
    if warning_message:
        st.markdown(f"""
        <div style="color: #856404; background-color: #fff3cd; 
                    border: 1px solid #ffeeba; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #ffc107;">
            <strong>⚠️ Figyelmeztetés:</strong> {warning_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_success(success_message):
    """
    Inline sikeres művelet üzenet megjelenítése.
    
    Args:
        success_message (str): A megjelenítendő sikeres művelet üzenete.
    """
    if success_message:
        st.markdown(f"""
        <div style="color: #155724; background-color: #d4edda; 
                    border: 1px solid #c3e6cb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #28a745;">
            <strong>✅ Sikeres:</strong> {success_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_info(info_message):
    """
    Inline információs üzenet megjelenítése.
    
    Args:
        info_message (str): A megjelenítendő információs üzenet.
    """
    if info_message:
        st.markdown(f"""
        <div style="color: #0c5460; background-color: #d1ecf1; 
                    border: 1px solid #bee5eb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #17a2b8;">
            <strong>ℹ️ Információ:</strong> {info_message}
        </div>
        """, unsafe_allow_html=True)

def render_active_filters_indicator():
    """
    Az aktív szűrők megjelenítése címkékkel a keresőmezők alatt.
    
    Ez vizuális visszajelzést ad a felhasználónak, hogy milyen szűrési feltételek vannak érvényben.
    """
    active_filters = []
    
    try:
        # Biztonságos formázás
        def safe_format_status(status):
            try:
                return format_status(status)
            except Exception as e:
                logger.error(f"Error formatting status in filter indicator: {e}")
                return str(status)
        
        # Termelő szűrő ellenőrzése
        if "producer_filter_om" in st.session_state and st.session_state.producer_filter_om is not None:
            active_filters.append(f"Termelő: {st.session_state.producer_filter_om}")
        
        # Státusz szűrő ellenőrzése
        if "status_filter_om" in st.session_state and st.session_state.status_filter_om is not None:
            active_filters.append(f"Státusz: {safe_format_status(st.session_state.status_filter_om)}")
        
        # Dátum szűrők ellenőrzése
        if "from_date_filter_om" in st.session_state and "to_date_filter_om" in st.session_state:
            from_date = st.session_state.from_date_filter_om
            to_date = st.session_state.to_date_filter_om
            
            # Csak akkor jelenítjük meg, ha nem az alapértelmezett 30 napos időszak
            default_from = (datetime.now() - timedelta(days=30)).date()
            default_to = datetime.now().date()
            
            if from_date != default_from or to_date != default_to:
                # Biztonságos dátum formázás
                try:
                    from_date_str = from_date.strftime('%Y-%m-%d')
                    to_date_str = to_date.strftime('%Y-%m-%d')
                    active_filters.append(f"Időszak: {from_date_str} - {to_date_str}")
                except Exception as e:
                    logger.error(f"Error formatting dates in filter indicator: {e}")
                    active_filters.append("Időszak: Egyedi szűrés")
    except Exception as e:
        logger.error(f"Error rendering active filters: {e}")
        # If there's an error, just add a generic filter indicator
        active_filters = ["Aktív szűrők vannak beállítva"]
    
    # Ha vannak aktív szűrők, megjelenítsük őket
    if active_filters:
        html_content = """
        <style>
        .active-filters {
            margin: 10px 0;
            padding: 5px 0;
        }
        .filter-tag {
            display: inline-block;
            background-color: #f0f2f6;
            border-radius: 15px;
            padding: 5px 10px;
            margin: 2px 5px;
            font-size: 0.8em;
        }
        </style>
        <div class="active-filters">
            <strong>Aktív szűrők:</strong> 
        """
        
        for filter_text in active_filters:
            html_content += f'<span class="filter-tag">{filter_text}</span>'
        
        html_content += '</div>'
        
        st.markdown(html_content, unsafe_allow_html=True)