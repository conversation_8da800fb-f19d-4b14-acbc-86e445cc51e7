"""
Display component implementations based on the component_base architecture.

This module provides reusable display components that follow the ComponentBase architecture.
These components handle various display formats including cards, tables, statistics,
status indicators, and other visual elements.
"""
import streamlit as st
import logging
import uuid
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List, Callable, Union, Tuple, TypeVar, cast

from .component_base import ComponentBase, CompositeComponent

# Try to import formatting utilities with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        # Fallback formatting functions if import fails
        logging.warning("Could not import formatting functions, using fallbacks")
        format_status = lambda x: x
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Setup logging
logger = logging.getLogger(__name__)


class Card(ComponentBase):
    """
    A card component for displaying content in a bordered container with header.
    
    This component creates a visually distinct card with optional header, 
    expandable/collapsible behavior, and customizable styling.
    """
    
    def __init__(
        self,
        title: str,
        content: Callable[[], None] = None,
        color: str = "#3584e4",
        icon: Optional[str] = None,
        is_expanded: bool = True,
        collapsible: bool = True,
        show_border: bool = True,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a card component.
        
        Args:
            title: The card title to display in the header
            content: Callable function that renders the card content
            color: Header background color (hex, rgb, or named color)
            icon: Optional icon to display in the header (emoji or text)
            is_expanded: Whether the card should be initially expanded
            collapsible: Whether the card can be collapsed/expanded
            show_border: Whether to show a border around the card
            component_id: Optional unique ID for the component
        """
        props = {
            'title': title,
            'color': color,
            'icon': icon,
            'collapsible': collapsible,
            'show_border': show_border
        }
        super().__init__(props, component_id)
        
        # Store the content rendering function
        self._content_func = content
        
        # Initialize expanded state
        self.set_state({'expanded': is_expanded})
    
    def render(self) -> None:
        """Render the card with header and content."""
        # Get component properties
        title = self.props['title']
        color = self.props['color']
        icon = self.props.get('icon', '')
        collapsible = self.props.get('collapsible', True)
        show_border = self.props.get('show_border', True)
        
        # Get current state
        is_expanded = self.get_state_value('expanded', True)
        
        # Generate a unique key for this card
        card_key = f"card_{self.component_id}"
        
        # Define toggle icon based on expanded state
        toggle_icon = "▼" if is_expanded else "▶"
        
        # Define CSS styles for the card
        border_style = f"border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin-bottom: 20px;" if show_border else ""
        card_style = f"""
            <style>
                .{card_key}-header {{
                    background-color: {color};
                    color: white;
                    padding: 10px 15px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    cursor: {("pointer" if collapsible else "default")};
                }}
                .{card_key}-title {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: bold;
                }}
                .{card_key}-content {{
                    padding: {("15px" if show_border else "10px 0")};
                    display: {("block" if is_expanded else "none")};
                }}
                .{card_key}-container {{
                    {border_style}
                }}
            </style>
        """
        
        # Render card header
        header_html = f"""
            <div class="{card_key}-container">
                <div class="{card_key}-header" id="{card_key}-header">
                    <div class="{card_key}-title">
                        {icon} {title}
                    </div>
                    {f'<div class="{card_key}-toggle">{toggle_icon}</div>' if collapsible else ''}
                </div>
            </div>
        """
        
        # Inject CSS and header HTML
        st.markdown(card_style, unsafe_allow_html=True)
        st.markdown(header_html, unsafe_allow_html=True)
        
        # Toggle expanded state when header is clicked
        if collapsible:
            is_clicked = st.checkbox(
                "Toggle Card",
                value=False,
                key=f"{card_key}_toggle",
                label_visibility="collapsed"
            )
            
            if is_clicked:
                new_expanded_state = not is_expanded
                self.set_state({'expanded': new_expanded_state})
                st.rerun()
        
        # Render card content in a container if expanded
        if is_expanded and self._content_func:
            with st.container():
                # Add custom class to the container for styling
                st.markdown(f'<div class="{card_key}-content">', unsafe_allow_html=True)
                
                # Render the actual content
                self._content_func()
                
                # Close the container div
                st.markdown('</div>', unsafe_allow_html=True)


class StatusBadge(ComponentBase):
    """
    A status badge component for displaying status values with appropriate styling.
    
    This component shows a status value (like "COMPLETED", "PENDING") with 
    color-coded styling and optional icon.
    """
    
    # Default color map for common statuses
    DEFAULT_STATUS_COLORS = {
        "CREATED": "#FFA07A",  # Light Salmon
        "CONFIRMED_BY_COMPANY": "#FFD700",  # Gold
        "ACCEPTED_BY_USER": "#98FB98",  # Pale Green
        "REJECTED_BY_USER": "#FF6347",  # Tomato
        "FINALIZED": "#20B2AA",  # Light Sea Green
        "COMPLETED": "#4CAF50",  # Green
        "PENDING": "#FFC107",  # Amber
        "ACTIVE": "#2196F3",  # Blue
        "INACTIVE": "#9E9E9E",  # Grey
        "ERROR": "#F44336",  # Red
        "WARNING": "#FF9800",  # Orange
        "INFO": "#2196F3",  # Blue
        "SUCCESS": "#4CAF50",  # Green
    }
    
    # Default icon map for common statuses
    DEFAULT_STATUS_ICONS = {
        "CREATED": "🆕",
        "CONFIRMED_BY_COMPANY": "👍",
        "ACCEPTED_BY_USER": "✅",
        "REJECTED_BY_USER": "❌",
        "FINALIZED": "🏁",
        "COMPLETED": "✓",
        "PENDING": "⏳",
        "ACTIVE": "▶️",
        "INACTIVE": "⏹️",
        "ERROR": "❌",
        "WARNING": "⚠️",
        "INFO": "ℹ️",
        "SUCCESS": "✅",
    }
    
    def __init__(
        self,
        status: str,
        color: Optional[str] = None,
        icon: Optional[str] = None,
        size: str = "medium",
        format_func: Optional[Callable[[str], str]] = None,
        custom_colors: Optional[Dict[str, str]] = None,
        custom_icons: Optional[Dict[str, str]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a status badge component.
        
        Args:
            status: The status value to display
            color: Optional specific color to use (overrides status-based color)
            icon: Optional specific icon to use (overrides status-based icon)
            size: Badge size ('small', 'medium', 'large')
            format_func: Optional function to format the display of the status
            custom_colors: Optional custom color map for status values
            custom_icons: Optional custom icon map for status values
            component_id: Optional unique ID for the component
        """
        props = {
            'status': status,
            'color': color,
            'icon': icon,
            'size': size,
            'format_func': format_func,
            'custom_colors': custom_colors or {},
            'custom_icons': custom_icons or {},
        }
        super().__init__(props, component_id)
    
    def render(self) -> None:
        """Render the status badge."""
        # Get component properties
        status = self.props['status']
        format_func = self.props.get('format_func', format_status)
        
        # Determine the display text
        if format_func and callable(format_func):
            try:
                display_text = format_func(status)
            except Exception as e:
                logger.error(f"Error formatting status: {e}")
                display_text = str(status)
        else:
            display_text = str(status)
        
        # Determine badge size
        size = self.props.get('size', 'medium')
        if size == 'small':
            font_size = '0.8em'
            padding = '2px 6px'
        elif size == 'large':
            font_size = '1.1em'
            padding = '6px 12px'
        else:  # medium
            font_size = '0.9em'
            padding = '4px 8px'
        
        # Determine badge color
        custom_colors = self.props.get('custom_colors', {})
        color_map = {**self.DEFAULT_STATUS_COLORS, **custom_colors}
        color = self.props.get('color') or color_map.get(status, "#9E9E9E")  # Default to grey
        
        # Determine icon to display
        custom_icons = self.props.get('custom_icons', {})
        icon_map = {**self.DEFAULT_STATUS_ICONS, **custom_icons}
        icon = self.props.get('icon') or icon_map.get(status, "")
        
        # Generate a unique key for this badge's CSS
        badge_key = f"badge_{self.component_id}"
        
        # Create the badge using HTML/CSS
        badge_html = f"""
        <style>
            .{badge_key} {{
                background-color: {color};
                color: white;
                padding: {padding};
                border-radius: 12px;
                font-size: {font_size};
                font-weight: bold;
                display: inline-flex;
                align-items: center;
                gap: 4px;
            }}
        </style>
        <span class="{badge_key}">
            {f'<span>{icon}</span>' if icon else ''}
            <span>{display_text}</span>
        </span>
        """
        
        # Render the badge
        st.markdown(badge_html, unsafe_allow_html=True)


class DataTable(ComponentBase):
    """
    A data table component for displaying tabular data with sorting and filtering.
    
    This component creates a table from a list of dictionaries or similar data structure,
    with support for sorting columns, filtering rows, and custom rendering of cell values.
    """
    
    def __init__(
        self,
        data: List[Dict[str, Any]],
        columns: List[Dict[str, Any]],
        key_field: str = "id",
        sortable: bool = True,
        filterable: bool = True,
        pagination: bool = True,
        page_size: int = 10,
        max_height: Optional[str] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a data table component.
        
        Args:
            data: List of dictionaries, each representing a table row
            columns: List of column configurations, each with at least 'field' and 'label'
                     Optional keys: 'sortable', 'filterable', 'format_func', 'width', 'align'
            key_field: The field that uniquely identifies each row
            sortable: Whether the table allows sorting (global setting)
            filterable: Whether the table allows filtering (global setting)
            pagination: Whether to enable pagination
            page_size: Number of rows per page when pagination is enabled
            max_height: Optional maximum height for the table (e.g. '400px')
            component_id: Optional unique ID for the component
        """
        props = {
            'columns': columns,
            'key_field': key_field,
            'sortable': sortable,
            'filterable': filterable,
            'pagination': pagination,
            'page_size': page_size,
            'max_height': max_height,
        }
        super().__init__(props, component_id)
        
        # Initialize state
        self.set_state({
            'data': data,
            'filtered_data': data,
            'sort_field': None,
            'sort_direction': 'asc',
            'filters': {},
            'current_page': 1,
        })
    
    def _apply_filters(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply active filters to the data."""
        filters = self.get_state_value('filters', {})
        if not filters:
            return data
        
        filtered_data = []
        for row in data:
            include_row = True
            
            for field, filter_value in filters.items():
                if not filter_value:  # Skip empty filters
                    continue
                
                field_value = self._get_nested_value(row, field)
                if field_value is None:
                    include_row = False
                    break
                
                # Case-insensitive string search
                if isinstance(field_value, str) and isinstance(filter_value, str):
                    if filter_value.lower() not in field_value.lower():
                        include_row = False
                        break
                # Equality check for non-strings
                elif field_value != filter_value:
                    include_row = False
                    break
            
            if include_row:
                filtered_data.append(row)
        
        return filtered_data
    
    def _sort_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Sort the data based on current sort field and direction."""
        sort_field = self.get_state_value('sort_field')
        sort_direction = self.get_state_value('sort_direction', 'asc')
        
        if not sort_field:
            return data
        
        # Create a copy of the data to avoid modifying the original
        sorted_data = list(data)
        
        # Define a key function that handles None values and nested fields
        def sort_key(row):
            value = self._get_nested_value(row, sort_field)
            # Handle None values in sorting (consider them less than everything)
            if value is None:
                return (0, None) if sort_direction == 'asc' else (1, None)
            return (1, value) if sort_direction == 'asc' else (0, value)
        
        # Sort the data
        sorted_data.sort(key=sort_key)
        
        return sorted_data
    
    def _get_nested_value(self, obj: Dict[str, Any], path: str) -> Any:
        """Get a value from a nested dictionary using dot notation (e.g., 'user.name')."""
        parts = path.split('.')
        current = obj
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
        
        return current
    
    def _get_paginated_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Get a page of data based on current page and page size."""
        if not self.props.get('pagination', True):
            return data
        
        current_page = self.get_state_value('current_page', 1)
        page_size = self.props.get('page_size', 10)
        
        start_index = (current_page - 1) * page_size
        end_index = start_index + page_size
        
        return data[start_index:end_index]
    
    def render(self) -> None:
        """Render the data table."""
        # Get component properties
        columns = self.props['columns']
        sortable = self.props.get('sortable', True)
        filterable = self.props.get('filterable', True)
        pagination = self.props.get('pagination', True)
        max_height = self.props.get('max_height')
        
        # Get current state
        data = self.get_state_value('data', [])
        sort_field = self.get_state_value('sort_field')
        sort_direction = self.get_state_value('sort_direction', 'asc')
        filters = self.get_state_value('filters', {})
        current_page = self.get_state_value('current_page', 1)
        
        # Apply filters to data
        filtered_data = self._apply_filters(data)
        
        # Apply sorting
        sorted_data = self._sort_data(filtered_data)
        
        # Store filtered data in state for pagination
        if self.get_state_value('filtered_data') != filtered_data:
            self.set_state({'filtered_data': filtered_data})
        
        # Get the current page of data
        display_data = self._get_paginated_data(sorted_data)
        
        # Calculate total pages for pagination
        total_items = len(filtered_data)
        page_size = self.props.get('page_size', 10)
        total_pages = max(1, (total_items + page_size - 1) // page_size)
        
        # Render filter controls if enabled
        if filterable:
            st.markdown("### Filters")
            cols = st.columns(min(4, len(columns)))
            new_filters = {}
            
            for i, column in enumerate(columns):
                if column.get('filterable', True):
                    with cols[i % len(cols)]:
                        filter_key = f"{self.component_id}_filter_{column['field']}"
                        current_filter = filters.get(column['field'], "")
                        
                        # Create a text input for the filter
                        filter_value = st.text_input(
                            f"Filter by {column['label']}",
                            value=current_filter,
                            key=filter_key
                        )
                        
                        new_filters[column['field']] = filter_value
            
            # Update filters if changed
            if new_filters != filters:
                self.set_state({'filters': new_filters, 'current_page': 1})
                st.rerun()
            
            st.markdown("---")
        
        # Create table header HTML
        header_html = "<tr>"
        for column in columns:
            # Determine column width style
            width_style = f"width: {column['width']};" if 'width' in column else ""
            
            # Determine text alignment style
            align = column.get('align', 'left')
            align_style = f"text-align: {align};"
            
            # Combine styles
            style = f"style='{width_style} {align_style}'"
            
            # Determine sort indicators
            sort_indicator = ""
            if sortable and column.get('sortable', True):
                if sort_field == column['field']:
                    sort_indicator = "▲" if sort_direction == 'asc' else "▼"
            
            header_html += f"<th {style}>{column['label']} {sort_indicator}</th>"
        header_html += "</tr>"
        
        # Create table rows HTML
        rows_html = ""
        for i, row in enumerate(display_data):
            rows_html += "<tr>"
            for column in columns:
                # Get the cell value
                field = column['field']
                value = self._get_nested_value(row, field)
                
                # Format the value if a format function is provided
                format_func = column.get('format_func')
                if format_func and callable(format_func):
                    try:
                        display_value = format_func(value)
                    except Exception as e:
                        logger.error(f"Error formatting value: {e}")
                        display_value = str(value) if value is not None else ""
                else:
                    display_value = str(value) if value is not None else ""
                
                # Determine text alignment style
                align = column.get('align', 'left')
                align_style = f"text-align: {align};"
                
                rows_html += f"<td style='{align_style}'>{display_value}</td>"
            rows_html += "</tr>"
        
        # Generate a unique key for this table's CSS
        table_key = f"table_{self.component_id}"
        
        # Create table CSS
        table_css = f"""
        <style>
            .{table_key}-container {{
                overflow-x: auto;
                {f"max-height: {max_height}; overflow-y: auto;" if max_height else ""}
            }}
            .{table_key} {{
                width: 100%;
                border-collapse: collapse;
                border: 1px solid #ddd;
            }}
            .{table_key} th, .{table_key} td {{
                padding: 8px;
                border: 1px solid #ddd;
            }}
            .{table_key} th {{
                background-color: #f2f2f2;
                position: sticky;
                top: 0;
                z-index: 10;
                cursor: {("pointer" if sortable else "default")};
            }}
            .{table_key} tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .{table_key} tr:hover {{
                background-color: #f0f0f0;
            }}
        </style>
        """
        
        # Create table HTML
        table_html = f"""
        <div class="{table_key}-container">
            <table class="{table_key}">
                <thead>
                    {header_html}
                </thead>
                <tbody>
                    {rows_html}
                </tbody>
            </table>
        </div>
        """
        
        # Render the table
        st.markdown(table_css, unsafe_allow_html=True)
        st.markdown(table_html, unsafe_allow_html=True)
        
        # Add click handlers for sorting if enabled
        if sortable:
            col_index = st.selectbox(
                "Sort by column:",
                options=range(len(columns)),
                format_func=lambda i: columns[i]['label'],
                index=0,
                key=f"{table_key}_sort_column"
            )
            
            sort_dir = st.radio(
                "Sort direction:",
                options=["asc", "desc"],
                format_func=lambda x: "Ascending" if x == "asc" else "Descending",
                horizontal=True,
                key=f"{table_key}_sort_direction"
            )
            
            if col_index is not None and columns[col_index].get('sortable', True):
                new_sort_field = columns[col_index]['field']
                
                # Update sort state if changed
                if new_sort_field != sort_field or sort_dir != sort_direction:
                    self.set_state({
                        'sort_field': new_sort_field,
                        'sort_direction': sort_dir
                    })
                    st.rerun()
        
        # Render pagination controls if enabled
        if pagination and total_pages > 1:
            st.markdown(f"**Page {current_page} of {total_pages}** ({total_items} total items)")
            
            # Create navigation buttons
            cols = st.columns([1, 1, 3, 1, 1])
            
            with cols[0]:
                if st.button("⏮ First", disabled=current_page == 1):
                    self.set_state({'current_page': 1})
                    st.rerun()
            
            with cols[1]:
                if st.button("◀ Prev", disabled=current_page == 1):
                    self.set_state({'current_page': current_page - 1})
                    st.rerun()
            
            with cols[3]:
                if st.button("Next ▶", disabled=current_page == total_pages):
                    self.set_state({'current_page': current_page + 1})
                    st.rerun()
            
            with cols[4]:
                if st.button("Last ⏭", disabled=current_page == total_pages):
                    self.set_state({'current_page': total_pages})
                    st.rerun()
            
            # Page number input
            with cols[2]:
                page_input = st.number_input(
                    "Go to page",
                    min_value=1,
                    max_value=total_pages,
                    value=current_page,
                    step=1,
                    key=f"{table_key}_page_input"
                )
                
                if page_input != current_page:
                    self.set_state({'current_page': page_input})
                    st.rerun()


class MetricCard(ComponentBase):
    """
    A metric card component for displaying key metrics with labels and optional icons.
    
    This component shows a single metric value with descriptive label, trend indicator,
    and formatting options.
    """
    
    def __init__(
        self,
        label: str,
        value: Union[int, float, str],
        prefix: str = "",
        suffix: str = "",
        delta: Optional[Union[int, float, str]] = None,
        delta_color: str = "normal",
        format_func: Optional[Callable[[Any], str]] = None,
        icon: Optional[str] = None,
        color: Optional[str] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a metric card component.
        
        Args:
            label: The label for the metric
            value: The metric value to display
            prefix: Text or symbol to display before the value
            suffix: Text or symbol to display after the value
            delta: Optional delta value to show change (absolute or percentage)
            delta_color: Color scheme for delta ('normal', 'inverse', 'off')
            format_func: Optional function to format the display of the value
            icon: Optional icon to display alongside the metric
            color: Optional color for the metric value
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'value': value,
            'prefix': prefix,
            'suffix': suffix,
            'delta': delta,
            'delta_color': delta_color,
            'format_func': format_func,
            'icon': icon,
            'color': color,
        }
        super().__init__(props, component_id)
    
    def render(self) -> None:
        """Render the metric card."""
        # Get component properties
        label = self.props['label']
        value = self.props['value']
        prefix = self.props.get('prefix', '')
        suffix = self.props.get('suffix', '')
        delta = self.props.get('delta')
        delta_color = self.props.get('delta_color', 'normal')
        format_func = self.props.get('format_func')
        icon = self.props.get('icon', '')
        color = self.props.get('color')
        
        # Format the value if a format function is provided
        if format_func and callable(format_func):
            try:
                formatted_value = format_func(value)
            except Exception as e:
                logger.error(f"Error formatting metric value: {e}")
                formatted_value = str(value)
        else:
            formatted_value = str(value)
        
        # Combine prefix, value, and suffix
        display_value = f"{prefix}{formatted_value}{suffix}"
        
        # Generate a unique key for this metric's CSS
        metric_key = f"metric_{self.component_id}"
        
        # Determine delta color and symbol
        delta_symbol = ""
        delta_color_value = ""
        
        if delta is not None:
            # Determine if delta is positive, negative, or zero
            try:
                delta_num = float(delta) if isinstance(delta, (int, float, str)) else 0
                if delta_num > 0:
                    delta_symbol = "↑"
                    delta_color_value = "#4CAF50" if delta_color == "normal" else "#F44336"  # Green or Red
                elif delta_num < 0:
                    delta_symbol = "↓"
                    delta_color_value = "#F44336" if delta_color == "normal" else "#4CAF50"  # Red or Green
                else:
                    delta_symbol = "−"
                    delta_color_value = "#9E9E9E"  # Grey
                
                # Format delta as percentage if it seems to be a percentage
                if isinstance(delta, str) and "%" in delta:
                    delta_display = delta
                else:
                    if abs(delta_num) < 1 and abs(delta_num) > 0:
                        # Small values show more decimal places
                        delta_display = f"{delta_num:+.2f}"
                    else:
                        delta_display = f"{delta_num:+.0f}"
            except (ValueError, TypeError):
                # If delta can't be converted to a number, just display it as is
                delta_display = str(delta)
                delta_symbol = "•"
                delta_color_value = "#9E9E9E"  # Grey
        
        # Create CSS for the metric card
        metric_css = f"""
        <style>
            .{metric_key}-container {{
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}
            .{metric_key}-label {{
                font-size: 0.9em;
                color: #666;
                margin-bottom: 5px;
                display: flex;
                align-items: center;
                gap: 5px;
            }}
            .{metric_key}-value {{
                font-size: 1.8em;
                font-weight: bold;
                color: {color or '#333'};
            }}
            .{metric_key}-delta {{
                font-size: 0.9em;
                color: {delta_color_value};
                margin-top: 5px;
                display: flex;
                align-items: center;
                gap: 3px;
            }}
            .{metric_key}-icon {{
                font-size: 1.2em;
            }}
        </style>
        """
        
        # Create HTML for the metric card
        metric_html = f"""
        <div class="{metric_key}-container">
            <div class="{metric_key}-label">
                {f'<span class="{metric_key}-icon">{icon}</span>' if icon else ''}
                <span>{label}</span>
            </div>
            <div class="{metric_key}-value">{display_value}</div>
            {f'<div class="{metric_key}-delta">{delta_symbol} {delta_display}</div>' if delta is not None else ''}
        </div>
        """
        
        # Render the metric card
        st.markdown(metric_css, unsafe_allow_html=True)
        st.markdown(metric_html, unsafe_allow_html=True)


class Tooltip(ComponentBase):
    """
    A tooltip component for displaying additional information on hover.
    
    This component shows extra information when the user hovers over an element.
    It's useful for providing context, explanations, or extra details without cluttering the UI.
    """
    
    def __init__(
        self,
        content: str,
        text: str,
        position: str = "top",
        max_width: str = "200px",
        icon: Optional[str] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a tooltip component.
        
        Args:
            content: The tooltip content (shown on hover)
            text: The visible text or element that triggers the tooltip
            position: Tooltip position ('top', 'bottom', 'left', 'right')
            max_width: Maximum width of the tooltip
            icon: Optional icon to show before the text
            component_id: Optional unique ID for the component
        """
        props = {
            'content': content,
            'text': text,
            'position': position,
            'max_width': max_width,
            'icon': icon,
        }
        super().__init__(props, component_id)
    
    def render(self) -> None:
        """Render the tooltip."""
        # Get component properties
        content = self.props['content']
        text = self.props['text']
        position = self.props.get('position', 'top')
        max_width = self.props.get('max_width', '200px')
        icon = self.props.get('icon')
        
        # Generate a unique key for this tooltip's CSS
        tooltip_key = f"tooltip_{self.component_id}"
        
        # Create CSS for the tooltip
        tooltip_css = f"""
        <style>
            .{tooltip_key} {{
                position: relative;
                display: inline-block;
                cursor: help;
            }}
            
            .{tooltip_key} .tooltip-text {{
                visibility: hidden;
                background-color: #333;
                color: #fff;
                text-align: center;
                border-radius: 6px;
                padding: 5px 10px;
                position: absolute;
                z-index: 1;
                opacity: 0;
                transition: opacity 0.3s;
                max-width: {max_width};
                width: max-content;
            }}
            
            .{tooltip_key}:hover .tooltip-text {{
                visibility: visible;
                opacity: 0.9;
            }}
            
            /* Positions */
            .{tooltip_key} .tooltip-text.top {{
                bottom: 125%;
                left: 50%;
                transform: translateX(-50%);
            }}
            
            .{tooltip_key} .tooltip-text.bottom {{
                top: 125%;
                left: 50%;
                transform: translateX(-50%);
            }}
            
            .{tooltip_key} .tooltip-text.left {{
                right: 110%;
                top: 50%;
                transform: translateY(-50%);
            }}
            
            .{tooltip_key} .tooltip-text.right {{
                left: 110%;
                top: 50%;
                transform: translateY(-50%);
            }}
            
            /* Arrow */
            .{tooltip_key} .tooltip-text::after {{
                content: "";
                position: absolute;
                border-width: 5px;
                border-style: solid;
            }}
            
            .{tooltip_key} .tooltip-text.top::after {{
                top: 100%;
                left: 50%;
                margin-left: -5px;
                border-color: #333 transparent transparent transparent;
            }}
            
            .{tooltip_key} .tooltip-text.bottom::after {{
                bottom: 100%;
                left: 50%;
                margin-left: -5px;
                border-color: transparent transparent #333 transparent;
            }}
            
            .{tooltip_key} .tooltip-text.left::after {{
                top: 50%;
                left: 100%;
                margin-top: -5px;
                border-color: transparent transparent transparent #333;
            }}
            
            .{tooltip_key} .tooltip-text.right::after {{
                top: 50%;
                right: 100%;
                margin-top: -5px;
                border-color: transparent #333 transparent transparent;
            }}
        </style>
        """
        
        # Create HTML for the tooltip
        tooltip_html = f"""
        <div class="{tooltip_key}">
            {f'<span>{icon}</span>' if icon else ''} {text}
            <span class="tooltip-text {position}">{content}</span>
        </div>
        """
        
        # Render the tooltip
        st.markdown(tooltip_css, unsafe_allow_html=True)
        st.markdown(tooltip_html, unsafe_allow_html=True)


class ProgressBar(ComponentBase):
    """
    A customizable progress bar component.
    
    This component shows a visual representation of progress towards a goal,
    with options for color coding, labeling, and formatting.
    """
    
    def __init__(
        self,
        value: float,
        min_value: float = 0.0,
        max_value: float = 100.0,
        label: Optional[str] = None,
        show_percentage: bool = True,
        color: str = "#3584e4",
        height: str = "20px",
        format_func: Optional[Callable[[float], str]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a progress bar component.
        
        Args:
            value: Current progress value
            min_value: Minimum value (0%)
            max_value: Maximum value (100%)
            label: Optional label to display above the progress bar
            show_percentage: Whether to show the percentage value
            color: Color of the progress bar
            height: Height of the progress bar
            format_func: Optional function to format the display of the value
            component_id: Optional unique ID for the component
        """
        props = {
            'value': value,
            'min_value': min_value,
            'max_value': max_value,
            'label': label,
            'show_percentage': show_percentage,
            'color': color,
            'height': height,
            'format_func': format_func,
        }
        super().__init__(props, component_id)
    
    def render(self) -> None:
        """Render the progress bar."""
        # Get component properties
        value = self.props['value']
        min_value = self.props.get('min_value', 0.0)
        max_value = self.props.get('max_value', 100.0)
        label = self.props.get('label')
        show_percentage = self.props.get('show_percentage', True)
        color = self.props.get('color', '#3584e4')
        height = self.props.get('height', '20px')
        format_func = self.props.get('format_func')
        
        # Calculate percentage
        try:
            percentage = min(100, max(0, (value - min_value) / (max_value - min_value) * 100))
        except (ZeroDivisionError, TypeError):
            percentage = 0
        
        # Format the displayed value
        if format_func and callable(format_func):
            try:
                display_value = format_func(value)
            except Exception as e:
                logger.error(f"Error formatting progress value: {e}")
                display_value = f"{value:.1f}"
        else:
            display_value = f"{value:.1f}"
        
        # Generate a unique key for this progress bar's CSS
        progress_key = f"progress_{self.component_id}"
        
        # Create CSS for the progress bar
        progress_css = f"""
        <style>
            .{progress_key}-container {{
                margin: 5px 0 15px 0;
            }}
            .{progress_key}-label {{
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
                font-size: 0.9em;
                color: #555;
            }}
            .{progress_key}-bar-container {{
                width: 100%;
                background-color: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                height: {height};
            }}
            .{progress_key}-bar {{
                height: 100%;
                width: {percentage}%;
                background-color: {color};
                transition: width 0.5s ease;
            }}
        </style>
        """
        
        # Create HTML for the progress bar
        progress_html = f"""
        <div class="{progress_key}-container">
        """
        
        # Add label and value display if provided
        if label or show_percentage:
            progress_html += f"""
            <div class="{progress_key}-label">
                {f'<span>{label}</span>' if label else '<span></span>'}
                {f'<span>{display_value} ({percentage:.1f}%)</span>' if show_percentage else ''}
            </div>
            """
        
        # Add the progress bar
        progress_html += f"""
            <div class="{progress_key}-bar-container">
                <div class="{progress_key}-bar"></div>
            </div>
        </div>
        """
        
        # Render the progress bar
        st.markdown(progress_css, unsafe_allow_html=True)
        st.markdown(progress_html, unsafe_allow_html=True)


class Alert(ComponentBase):
    """
    An alert component for displaying messages with different severity levels.
    
    This component shows messages with appropriate styling based on their type
    (info, success, warning, error).
    """
    
    def __init__(
        self,
        message: str,
        type: str = "info",
        dismissible: bool = False,
        icon: Optional[str] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize an alert component.
        
        Args:
            message: The alert message to display
            type: Alert type ('info', 'success', 'warning', 'error')
            dismissible: Whether the alert can be dismissed (closed)
            icon: Optional custom icon to display
            component_id: Optional unique ID for the component
        """
        props = {
            'message': message,
            'type': type,
            'dismissible': dismissible,
            'icon': icon,
        }
        super().__init__(props, component_id)
        
        # Initialize state
        self.set_state({'visible': True})
    
    def render(self) -> None:
        """Render the alert component."""
        # Check if alert should be visible
        if not self.get_state_value('visible', True):
            return
        
        # Get component properties
        message = self.props['message']
        type_ = self.props.get('type', 'info')
        dismissible = self.props.get('dismissible', False)
        custom_icon = self.props.get('icon')
        
        # Define alert styles for different types
        alert_styles = {
            'info': {
                'bg_color': '#d1ecf1',
                'border_color': '#bee5eb',
                'text_color': '#0c5460',
                'icon': custom_icon or 'ℹ️',
            },
            'success': {
                'bg_color': '#d4edda',
                'border_color': '#c3e6cb',
                'text_color': '#155724',
                'icon': custom_icon or '✅',
            },
            'warning': {
                'bg_color': '#fff3cd',
                'border_color': '#ffeeba',
                'text_color': '#856404',
                'icon': custom_icon or '⚠️',
            },
            'error': {
                'bg_color': '#f8d7da',
                'border_color': '#f5c6cb',
                'text_color': '#721c24',
                'icon': custom_icon or '❌',
            },
        }
        
        # Get style based on alert type
        style = alert_styles.get(type_.lower(), alert_styles['info'])
        
        # Generate a unique key for this alert's CSS
        alert_key = f"alert_{self.component_id}"
        
        # Create CSS for the alert
        alert_css = f"""
        <style>
            .{alert_key} {{
                display: flex;
                align-items: flex-start;
                padding: 12px 15px;
                margin-bottom: 15px;
                border: 1px solid {style['border_color']};
                border-radius: 4px;
                background-color: {style['bg_color']};
                color: {style['text_color']};
                position: relative;
            }}
            .{alert_key}-icon {{
                margin-right: 10px;
                font-size: 1.1em;
            }}
            .{alert_key}-content {{
                flex-grow: 1;
            }}
            .{alert_key}-close {{
                cursor: pointer;
                margin-left: 10px;
                font-weight: bold;
                font-size: 1.2em;
                line-height: 1;
            }}
        </style>
        """
        
        # Create HTML for the alert
        alert_html = f"""
        <div class="{alert_key}" id="{alert_key}">
            <div class="{alert_key}-icon">{style['icon']}</div>
            <div class="{alert_key}-content">{message}</div>
            {f'<div class="{alert_key}-close" onclick="this.parentElement.style.display=\'none\'">×</div>' if dismissible else ''}
        </div>
        """
        
        # Render the alert
        st.markdown(alert_css, unsafe_allow_html=True)
        st.markdown(alert_html, unsafe_allow_html=True)
        
        # Add dismiss button if alert is dismissible
        if dismissible:
            # A hidden checkbox to handle the dismiss action
            is_dismissed = st.checkbox(
                "Dismiss Alert",
                value=False,
                key=f"{alert_key}_dismiss",
                label_visibility="collapsed"
            )
            
            if is_dismissed:
                self.set_state({'visible': False})
                st.rerun()


class TabContainer(ComponentBase):
    """
    A tabbed container component for organizing content into tabs.
    
    This component groups related content into tabbed sections, allowing
    users to switch between them while maintaining a clean interface.
    """
    
    def __init__(
        self,
        tabs: Dict[str, Callable[[], None]],
        default_tab: Optional[str] = None,
        position: str = "top",
        component_id: Optional[str] = None,
    ):
        """
        Initialize a tab container component.
        
        Args:
            tabs: Dictionary mapping tab labels to content rendering functions
            default_tab: The tab label to show by default
            position: Tab position ('top', 'left', 'right')
            component_id: Optional unique ID for the component
        """
        props = {
            'tabs': tabs,
            'position': position,
        }
        super().__init__(props, component_id)
        
        # Set the default tab (first tab if not specified)
        default_tab = default_tab or next(iter(tabs.keys())) if tabs else None
        
        # Initialize state
        self.set_state({'active_tab': default_tab})
    
    def render(self) -> None:
        """Render the tab container."""
        # Get component properties
        tabs = self.props['tabs']
        position = self.props.get('position', 'top')
        
        # Get current state
        active_tab = self.get_state_value('active_tab')
        
        # Exit if no tabs are defined
        if not tabs:
            st.warning("No tabs defined for tab container.")
            return
        
        # Ensure active tab is valid
        if active_tab not in tabs:
            active_tab = next(iter(tabs.keys()))
            self.set_state({'active_tab': active_tab})
        
        # Generate a unique key for this tab container's CSS
        tab_key = f"tabs_{self.component_id}"
        
        # Define CSS styles based on tab position
        if position == 'left':
            tab_layout = """
                display: flex;
                flex-direction: row;
            """
            tabs_style = """
                display: flex;
                flex-direction: column;
                min-width: 150px;
                border-right: 1px solid #ddd;
            """
            tab_style = """
                padding: 10px 15px;
                border-bottom: 1px solid #ddd;
                cursor: pointer;
                transition: background-color 0.3s;
            """
            active_tab_style = """
                background-color: #f0f2f6;
                border-right: 3px solid #3584e4;
                font-weight: bold;
            """
            content_style = """
                flex-grow: 1;
                padding: 15px;
            """
        elif position == 'right':
            tab_layout = """
                display: flex;
                flex-direction: row-reverse;
            """
            tabs_style = """
                display: flex;
                flex-direction: column;
                min-width: 150px;
                border-left: 1px solid #ddd;
            """
            tab_style = """
                padding: 10px 15px;
                border-bottom: 1px solid #ddd;
                cursor: pointer;
                transition: background-color 0.3s;
            """
            active_tab_style = """
                background-color: #f0f2f6;
                border-left: 3px solid #3584e4;
                font-weight: bold;
            """
            content_style = """
                flex-grow: 1;
                padding: 15px;
            """
        else:  # top (default)
            tab_layout = """
                display: flex;
                flex-direction: column;
            """
            tabs_style = """
                display: flex;
                flex-direction: row;
                border-bottom: 1px solid #ddd;
            """
            tab_style = """
                padding: 10px 15px;
                cursor: pointer;
                transition: background-color 0.3s;
                margin-right: 5px;
            """
            active_tab_style = """
                background-color: #f0f2f6;
                border-bottom: 3px solid #3584e4;
                font-weight: bold;
            """
            content_style = """
                padding: 15px 0;
            """
        
        # Create CSS for the tab container
        tab_css = f"""
        <style>
            .{tab_key}-container {{
                {tab_layout}
                margin-bottom: 20px;
            }}
            .{tab_key}-tabs {{
                {tabs_style}
            }}
            .{tab_key}-tab {{
                {tab_style}
            }}
            .{tab_key}-tab:hover {{
                background-color: #f8f9fa;
            }}
            .{tab_key}-tab.active {{
                {active_tab_style}
            }}
            .{tab_key}-content {{
                {content_style}
            }}
        </style>
        """
        
        # Create HTML for the tab container
        tab_html = f"""
        <div class="{tab_key}-container">
            <div class="{tab_key}-tabs">
        """
        
        # Add tab buttons
        for tab_label in tabs.keys():
            active_class = "active" if tab_label == active_tab else ""
            tab_html += f'<div class="{tab_key}-tab {active_class}" id="{tab_key}_{tab_label}">{tab_label}</div>'
        
        tab_html += """
            </div>
            <div class="{tab_key}-content">
                <!-- Tab content will be rendered here -->
            </div>
        </div>
        """
        
        # Render the tab container structure
        st.markdown(tab_css, unsafe_allow_html=True)
        st.markdown(tab_html, unsafe_allow_html=True)
        
        # Add tab selection through radio buttons
        selected_tab = st.radio(
            "Select Tab",
            options=list(tabs.keys()),
            index=list(tabs.keys()).index(active_tab),
            key=f"{tab_key}_selector",
            label_visibility="collapsed"
        )
        
        # Update active tab if changed
        if selected_tab != active_tab:
            self.set_state({'active_tab': selected_tab})
            st.rerun()
        
        # Render the active tab content
        if active_tab in tabs and callable(tabs[active_tab]):
            tabs[active_tab]()


class Accordion(ComponentBase):
    """
    An accordion component for collapsible content sections.
    
    This component groups content into collapsible sections, allowing
    users to show or hide details as needed.
    """
    
    def __init__(
        self,
        sections: Dict[str, Callable[[], None]],
        default_open: Optional[List[str]] = None,
        multi_expand: bool = True,
        component_id: Optional[str] = None,
    ):
        """
        Initialize an accordion component.
        
        Args:
            sections: Dictionary mapping section titles to content rendering functions
            default_open: List of section titles that should be open by default
            multi_expand: Whether multiple sections can be open simultaneously
            component_id: Optional unique ID for the component
        """
        props = {
            'sections': sections,
            'multi_expand': multi_expand,
        }
        super().__init__(props, component_id)
        
        # Initialize state with default open sections
        open_sections = {}
        for title in sections.keys():
            open_sections[title] = title in (default_open or [])
        
        self.set_state({'open_sections': open_sections})
    
    def render(self) -> None:
        """Render the accordion component."""
        # Get component properties
        sections = self.props['sections']
        multi_expand = self.props.get('multi_expand', True)
        
        # Get current state
        open_sections = self.get_state_value('open_sections', {})
        
        # Generate a unique key for this accordion's CSS
        accordion_key = f"accordion_{self.component_id}"
        
        # Create CSS for the accordion
        accordion_css = f"""
        <style>
            .{accordion_key}-container {{
                border: 1px solid #ddd;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 20px;
            }}
            .{accordion_key}-section {{
                margin-bottom: 1px;
            }}
            .{accordion_key}-header {{
                padding: 10px 15px;
                background-color: #f8f9fa;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: background-color 0.3s;
            }}
            .{accordion_key}-header:hover {{
                background-color: #f0f0f0;
            }}
            .{accordion_key}-icon {{
                font-size: 1.2em;
            }}
            .{accordion_key}-content {{
                padding: 15px;
                border-top: 1px solid #ddd;
            }}
        </style>
        """
        
        # Render the accordion CSS
        st.markdown(accordion_css, unsafe_allow_html=True)
        
        # Create a container for the accordion
        st.markdown(f'<div class="{accordion_key}-container">', unsafe_allow_html=True)
        
        # Render each section
        for title, render_func in sections.items():
            # Check if section should be open
            is_open = open_sections.get(title, False)
            icon = "▼" if is_open else "▶"
            
            # Create section header
            section_key = f"{accordion_key}_{title.replace(' ', '_')}"
            
            # Create a checkbox to track section state
            if st.checkbox(
                title,
                value=is_open,
                key=section_key,
                label_visibility="collapsed"
            ):
                # Section is open, update state if changed
                if not is_open:
                    # If not multi-expand, close all other sections
                    if not multi_expand:
                        for sec_title in open_sections:
                            open_sections[sec_title] = sec_title == title
                    else:
                        open_sections[title] = True
                    
                    self.set_state({'open_sections': open_sections})
                    st.rerun()
                
                # Render section header and content
                st.markdown(f"""
                    <div class="{accordion_key}-section">
                        <div class="{accordion_key}-header">
                            <div>{title}</div>
                            <div class="{accordion_key}-icon">{icon}</div>
                        </div>
                    </div>
                """, unsafe_allow_html=True)
                
                # Render the content
                with st.container():
                    render_func()
            else:
                # Section is closed, update state if changed
                if is_open:
                    open_sections[title] = False
                    self.set_state({'open_sections': open_sections})
                    st.rerun()
                
                # Just render the header
                st.markdown(f"""
                    <div class="{accordion_key}-section">
                        <div class="{accordion_key}-header">
                            <div>{title}</div>
                            <div class="{accordion_key}-icon">{icon}</div>
                        </div>
                    </div>
                """, unsafe_allow_html=True)
        
        # Close the accordion container
        st.markdown('</div>', unsafe_allow_html=True)


class Timeline(ComponentBase):
    """
    A timeline component for displaying chronological events.
    
    This component shows a vertical or horizontal timeline of events,
    with customizable styling and content.
    """
    
    def __init__(
        self,
        events: List[Dict[str, Any]],
        orientation: str = "vertical",
        compact: bool = False,
        date_format: Optional[Callable[[Union[str, datetime, date]], str]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a timeline component.
        
        Args:
            events: List of event dictionaries, each with at least 'date' and 'title' keys
                   Optional keys: 'description', 'icon', 'color'
            orientation: Timeline orientation ('vertical' or 'horizontal')
            compact: Whether to use a compact layout
            date_format: Optional function to format the display of dates
            component_id: Optional unique ID for the component
        """
        props = {
            'events': events,
            'orientation': orientation,
            'compact': compact,
            'date_format': date_format or format_date,
        }
        super().__init__(props, component_id)
    
    def render(self) -> None:
        """Render the timeline component."""
        # Get component properties
        events = self.props['events']
        orientation = self.props.get('orientation', 'vertical')
        compact = self.props.get('compact', False)
        date_format = self.props.get('date_format', format_date)
        
        # Sort events by date
        try:
            sorted_events = sorted(
                events,
                key=lambda e: e['date'] if isinstance(e['date'], (datetime, date)) else datetime.now()
            )
        except (KeyError, TypeError):
            # If sorting fails, use original order
            sorted_events = events
        
        # Generate a unique key for this timeline's CSS
        timeline_key = f"timeline_{self.component_id}"
        
        # Define CSS styles based on orientation
        if orientation == 'horizontal':
            container_style = """
                display: flex;
                flex-direction: row;
                overflow-x: auto;
                padding: 20px 0;
            """
            event_style = """
                margin-right: 30px;
                flex: 0 0 250px;
                position: relative;
            """
            line_style = """
                position: absolute;
                top: 20px;
                right: -15px;
                width: 30px;
                height: 2px;
                background-color: #ddd;
            """
            dot_style = """
                width: 14px;
                height: 14px;
                border-radius: 50%;
                position: absolute;
                top: 14px;
                right: -22px;
                z-index: 2;
            """
            if compact:
                container_style = """
                    display: flex;
                    flex-direction: row;
                    overflow-x: auto;
                    padding: 10px 0;
                """
                event_style = """
                    margin-right: 20px;
                    flex: 0 0 150px;
                    position: relative;
                """
        else:  # vertical (default)
            container_style = """
                position: relative;
                padding-left: 30px;
            """
            event_style = """
                margin-bottom: 30px;
                position: relative;
            """
            line_style = """
                position: absolute;
                top: 0;
                bottom: 0;
                left: -30px;
                width: 2px;
                background-color: #ddd;
            """
            dot_style = """
                width: 14px;
                height: 14px;
                border-radius: 50%;
                position: absolute;
                top: 0;
                left: -36px;
                z-index: 2;
            """
            if compact:
                container_style = """
                    position: relative;
                    padding-left: 20px;
                """
                event_style = """
                    margin-bottom: 15px;
                    position: relative;
                """
                line_style = """
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -20px;
                    width: 2px;
                    background-color: #ddd;
                """
                dot_style = """
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    position: absolute;
                    top: 3px;
                    left: -24px;
                    z-index: 2;
                """
        
        # Create CSS for the timeline
        timeline_css = f"""
        <style>
            .{timeline_key}-container {{
                {container_style}
            }}
            .{timeline_key}-event {{
                {event_style}
            }}
            .{timeline_key}-date {{
                font-weight: bold;
                color: #666;
                margin-bottom: 5px;
                font-size: {("0.8em" if compact else "0.9em")};
            }}
            .{timeline_key}-title {{
                font-weight: bold;
                margin-bottom: {("3px" if compact else "5px")};
                font-size: {("1em" if compact else "1.1em")};
            }}
            .{timeline_key}-description {{
                color: #666;
                font-size: {("0.8em" if compact else "0.9em")};
            }}
            .{timeline_key}-line {{
                {line_style}
            }}
            .{timeline_key}-dot {{
                {dot_style}
            }}
        </style>
        """
        
        # Create HTML for the timeline container
        timeline_html = f'<div class="{timeline_key}-container">'
        
        # Vertical timeline needs a continuous line
        if orientation == 'vertical':
            timeline_html += f'<div class="{timeline_key}-line"></div>'
        
        # Render each event
        for event in sorted_events:
            # Get event details
            title = event.get('title', '')
            description = event.get('description', '')
            
            # Format the date
            event_date = event.get('date')
            if event_date is not None:
                try:
                    date_str = date_format(event_date)
                except Exception as e:
                    logger.error(f"Error formatting date: {e}")
                    date_str = str(event_date)
            else:
                date_str = ""
            
            # Get event styling
            color = event.get('color', '#3584e4')
            icon = event.get('icon', '')
            
            # Create HTML for each event
            timeline_html += f"""
                <div class="{timeline_key}-event">
                    <div class="{timeline_key}-dot" style="background-color: {color};">
                        {icon}
                    </div>
                    {f'<div class="{timeline_key}-line"></div>' if orientation == 'horizontal' else ''}
                    <div class="{timeline_key}-date">{date_str}</div>
                    <div class="{timeline_key}-title">{title}</div>
                    {f'<div class="{timeline_key}-description">{description}</div>' if description else ''}
                </div>
            """
        
        # Close the timeline container
        timeline_html += '</div>'
        
        # Render the timeline
        st.markdown(timeline_css, unsafe_allow_html=True)
        st.markdown(timeline_html, unsafe_allow_html=True)


# Additional helper components and functions can be added here