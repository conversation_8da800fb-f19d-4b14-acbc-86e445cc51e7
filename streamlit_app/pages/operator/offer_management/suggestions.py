"""
Context-sensitive suggestion system for the offer management page.
Provides intelligent filter suggestions based on offer data and user behavior.
"""
import streamlit as st
import logging
import uuid
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Set, Union

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
    from streamlit_app.pages.operator.offer_management.column_filters import (
        get_nested_value, 
        COLUMN_FILTERS, 
        OPERATORS,
        extract_unique_values
    )
    from streamlit_app.pages.operator.offer_management.complex_filters import (
        FilterCondition,
        FilterGroup,
        LOGICAL_OPERATORS
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
        from pages.operator.offer_management.column_filters import (
            get_nested_value, 
            COLUMN_FILTERS, 
            OPERATORS,
            extract_unique_values
        )
        from pages.operator.offer_management.complex_filters import (
            FilterCondition,
            FilterGroup,
            LOGICAL_OPERATORS
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            from column_filters import (
                get_nested_value, 
                COLUMN_FILTERS, 
                OPERATORS,
                extract_unique_values
            )
            from complex_filters import (
                FilterCondition,
                FilterGroup,
                LOGICAL_OPERATORS
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in suggestions.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
            
            # Minimal COLUMN_FILTERS and OPERATORS definitions
            COLUMN_FILTERS = {
                "id": {
                    "display_name": "Azonosító",
                    "field": "id",
                    "filter_type": "numeric",
                    "operators": ["equals", "not_equals", "greater_than", "less_than"]
                },
                "status": {
                    "display_name": "Státusz",
                    "field": "status",
                    "filter_type": "categorical",
                    "operators": ["equals", "not_equals"]
                }
            }
            
            OPERATORS = {
                "equals": {
                    "display_name": "=",
                    "full_name": "Egyenlő",
                    "function": lambda field, value: field == value
                },
                "not_equals": {
                    "display_name": "≠",
                    "full_name": "Nem egyenlő",
                    "function": lambda field, value: field != value
                }
            }
            
            def extract_unique_values(offers, column_path, limit=100):
                """Extract unique values for a column from offers."""
                values = []
                for offer in offers:
                    value = get_nested_value(offer, column_path)
                    if value is not None and value not in values:
                        values.append(value)
                return values
            
            # Minimal implementation of FilterCondition and FilterGroup
            class FilterCondition:
                def __init__(self, column, operator, value):
                    self.column = column
                    self.operator = operator
                    self.value = value
                    
                def to_display_string(self):
                    return f"{self.column} {self.operator} {self.value}"
            
            class FilterGroup:
                def __init__(self, operator="AND", conditions=None):
                    self.operator = operator
                    self.conditions = conditions or []
                    
                def add_condition(self, condition):
                    self.conditions.append(condition)
                    
                def to_display_string(self):
                    return f"{self.operator}: {len(self.conditions)} conditions"
            
            LOGICAL_OPERATORS = {
                "AND": {"display_name": "ÉS", "symbol": "∧", "description": "Minden feltételnek teljesülnie kell"},
                "OR": {"display_name": "VAGY", "symbol": "∨", "description": "Legalább egy feltételnek teljesülnie kell"},
                "NOT": {"display_name": "NEM", "symbol": "¬", "description": "A feltétel ellenkezőjének kell teljesülnie"}
            }

# Logger setup
logger = logging.getLogger(__name__)

# Define suggestion types and priorities
SUGGESTION_TYPES = {
    "ANOMALY": {
        "priority": 10,
        "display_name": "Kiugró érték",
        "icon": "⚠️",
        "color": "#FF5722"
    },
    "FREQUENT_PATTERN": {
        "priority": 7,
        "display_name": "Gyakori minta",
        "icon": "🔄",
        "color": "#2196F3"
    },
    "RECENT_ACTIVITY": {
        "priority": 9,
        "display_name": "Friss aktivitás",
        "icon": "🕒",
        "color": "#4CAF50"
    },
    "VALUE_DISTRIBUTION": {
        "priority": 5,
        "display_name": "Érték eloszlás",
        "icon": "📊",
        "color": "#9C27B0"
    },
    "SIMILAR_TO_CURRENT": {
        "priority": 8,
        "display_name": "Jelenlegi szűrőkhöz hasonló",
        "icon": "🔍",
        "color": "#FF9800"
    },
    "EMPTY_FIELD": {
        "priority": 4,
        "display_name": "Hiányzó adatok",
        "icon": "❓",
        "color": "#607D8B"
    }
}

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

class Suggestion:
    """
    Represents a suggestion for a filter condition or group.
    """
    def __init__(
        self, 
        title: str,
        description: str,
        filter_condition: Union[FilterCondition, FilterGroup],
        suggestion_type: str,
        relevance_score: float = 1.0,
        explanation: str = None
    ):
        """
        Initialize a suggestion.
        
        Args:
            title: Short title for the suggestion
            description: Longer description explaining the suggestion
            filter_condition: Suggested filter condition or group
            suggestion_type: Type of suggestion (from SUGGESTION_TYPES)
            relevance_score: Score from 0 to 1 indicating relevance
            explanation: Optional explanation of why this suggestion is relevant
        """
        self.title = title
        self.description = description
        self.filter_condition = filter_condition
        self.suggestion_type = suggestion_type
        self.relevance_score = max(0.0, min(1.0, relevance_score))  # Clamp to 0-1
        self.explanation = explanation
    
    @property
    def priority(self) -> int:
        """
        Get the priority based on type and relevance.
        
        Returns:
            Priority score
        """
        base_priority = SUGGESTION_TYPES.get(self.suggestion_type, {}).get("priority", 0)
        # Scale by relevance and return as integer (higher is more important)
        return int(base_priority * self.relevance_score * 10)
    
    @property
    def display_info(self) -> Dict[str, str]:
        """
        Get display information for the suggestion.
        
        Returns:
            Dictionary with display properties
        """
        return SUGGESTION_TYPES.get(self.suggestion_type, {
            "display_name": "Unknown",
            "icon": "❔",
            "color": "#888888"
        })

def find_anomalies(offers: List[Dict[str, Any]]) -> List[Suggestion]:
    """
    Find anomalies in the offers data.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        List of anomaly-based suggestions
    """
    suggestions = []
    
    if not offers:
        return suggestions
    
    # Convert to dataframe for easier analysis
    df_data = []
    for offer in offers:
        row = {
            "id": offer.get("id"),
            "status": offer.get("status"),
            "delivery_date": get_nested_value(offer, "delivery_date"),
            "product_name": get_nested_value(offer, "product_type.name"),
            "quantity_in_kg": get_nested_value(offer, "quantity_in_kg"),
            "price": get_nested_value(offer, "price"),
            "created_at": get_nested_value(offer, "created_at"),
            "producer_name": get_nested_value(offer, "user.contact_name")
        }
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    # Find numeric outliers (quantities, prices)
    try:
        if "quantity_in_kg" in df.columns and df["quantity_in_kg"].notna().any():
            q_mean = df["quantity_in_kg"].mean()
            q_std = df["quantity_in_kg"].std()
            
            # Check for very large quantities (> 3 standard deviations)
            high_threshold = q_mean + 3 * q_std
            high_quantity_values = df[df["quantity_in_kg"] > high_threshold]["quantity_in_kg"].tolist()
            
            if high_quantity_values:
                min_high_value = min(high_quantity_values)
                
                filter_condition = FilterCondition(
                    column="quantity_in_kg",
                    operator="greater_than",
                    value=min_high_value
                )
                
                suggestions.append(Suggestion(
                    title="Kiugróan nagy mennyiségek",
                    description=f"A legtöbb ajánlat mennyiségétől jelentősen eltérő, legalább {min_high_value:.1f} kg mennyiségű ajánlatok",
                    filter_condition=filter_condition,
                    suggestion_type="ANOMALY",
                    relevance_score=0.9,
                    explanation=f"Az átlagos mennyiség {q_mean:.1f} kg, a szórás {q_std:.1f} kg. A {min_high_value:.1f} kg feletti mennyiségek jelentősen eltérnek az átlagtól."
                ))
    except Exception as e:
        logger.warning(f"Error analyzing quantity anomalies: {e}")
    
    # Find price outliers
    try:
        if "price" in df.columns and df["price"].notna().any():
            p_mean = df["price"].mean()
            p_std = df["price"].std()
            
            # Check for very high prices (> 3 standard deviations)
            high_price_threshold = p_mean + 3 * p_std
            high_price_values = df[df["price"] > high_price_threshold]["price"].tolist()
            
            if high_price_values:
                min_high_price = min(high_price_values)
                
                filter_condition = FilterCondition(
                    column="price",
                    operator="greater_than",
                    value=min_high_price
                )
                
                suggestions.append(Suggestion(
                    title="Kiugróan magas árak",
                    description=f"A legtöbb ajánlat árától jelentősen eltérő, legalább {format_price(min_high_price)} értékű ajánlatok",
                    filter_condition=filter_condition,
                    suggestion_type="ANOMALY",
                    relevance_score=0.9,
                    explanation=f"Az átlagos ár {format_price(p_mean)}, a szórás {format_price(p_std)}. A {format_price(min_high_price)} feletti árak jelentősen eltérnek az átlagtól."
                ))
                
                # Also suggest very low prices
                low_price_threshold = max(0, p_mean - 2 * p_std)  # Avoid negative thresholds
                if low_price_threshold > 0:
                    low_price_values = df[(df["price"] < low_price_threshold) & (df["price"] > 0)]["price"].tolist()
                    
                    if low_price_values:
                        max_low_price = max(low_price_values)
                        
                        filter_condition = FilterCondition(
                            column="price",
                            operator="less_than",
                            value=max_low_price
                        )
                        
                        suggestions.append(Suggestion(
                            title="Kiugróan alacsony árak",
                            description=f"A legtöbb ajánlat árától jelentősen eltérő, legfeljebb {format_price(max_low_price)} értékű ajánlatok",
                            filter_condition=filter_condition,
                            suggestion_type="ANOMALY",
                            relevance_score=0.8,
                            explanation=f"Az átlagos ár {format_price(p_mean)}, a szórás {format_price(p_std)}. A {format_price(max_low_price)} alatti árak jelentősen eltérnek az átlagtól."
                        ))
        
        # Create a suggestion for both high price AND high quantity (potential fraud or error)
        if "price" in df.columns and "quantity_in_kg" in df.columns and df["price"].notna().any() and df["quantity_in_kg"].notna().any():
            p_high = p_mean + 2 * p_std
            q_high = q_mean + 2 * q_std
            
            high_price_and_quantity = df[(df["price"] > p_high) & (df["quantity_in_kg"] > q_high)]
            if not high_price_and_quantity.empty:
                # Create a group with AND condition
                group = FilterGroup(operator="AND")
                group.add_condition(FilterCondition(
                    column="price",
                    operator="greater_than",
                    value=p_high
                ))
                group.add_condition(FilterCondition(
                    column="quantity_in_kg",
                    operator="greater_than",
                    value=q_high
                ))
                
                suggestions.append(Suggestion(
                    title="Magas ár ÉS nagy mennyiség",
                    description=f"Magas árú és nagy mennyiségű ajánlatok ({high_price_and_quantity.shape[0]} db)",
                    filter_condition=group,
                    suggestion_type="ANOMALY",
                    relevance_score=1.0,
                    explanation="Az ajánlatok, amelyek ára és mennyisége is kiugróan magas, különös figyelmet érdemelnek, mert jelentős pénzügyi értéket képviselnek vagy hibásak lehetnek."
                ))
    except Exception as e:
        logger.warning(f"Error analyzing price anomalies: {e}")
    
    # Detect delivery date anomalies
    try:
        if "delivery_date" in df.columns and df["delivery_date"].notna().any():
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_any_dtype(df["delivery_date"]):
                df["delivery_date"] = pd.to_datetime(df["delivery_date"], errors="coerce")
            
            today = pd.Timestamp(datetime.now().date())
            
            # Past delivery dates (potential problems)
            past_deliveries = df[df["delivery_date"] < today]
            if not past_deliveries.empty:
                filter_condition = FilterCondition(
                    column="delivery_date",
                    operator="before",
                    value=today.to_pydatetime().date()
                )
                
                suggestions.append(Suggestion(
                    title="Elmúlt beszállítási dátumok",
                    description=f"Már elmúlt beszállítási dátummal rendelkező ajánlatok ({past_deliveries.shape[0]} db)",
                    filter_condition=filter_condition,
                    suggestion_type="ANOMALY",
                    relevance_score=0.95,
                    explanation="Az elmúlt beszállítási dátummal rendelkező ajánlatok problémásak lehetnek és sürgős figyelmet igényelnek."
                ))
            
            # Very far future delivery dates (potential data entry errors)
            far_future = today + pd.Timedelta(days=365)  # 1 year from now
            far_future_deliveries = df[df["delivery_date"] > far_future]
            if not far_future_deliveries.empty:
                filter_condition = FilterCondition(
                    column="delivery_date",
                    operator="after",
                    value=far_future.to_pydatetime().date()
                )
                
                suggestions.append(Suggestion(
                    title="Nagyon távoli beszállítási dátumok",
                    description=f"Egy évnél későbbi beszállítási dátummal rendelkező ajánlatok ({far_future_deliveries.shape[0]} db)",
                    filter_condition=filter_condition,
                    suggestion_type="ANOMALY",
                    relevance_score=0.85,
                    explanation="A túl távoli jövőbe tervezett beszállítások adatbeviteli hibából is származhatnak."
                ))
    except Exception as e:
        logger.warning(f"Error analyzing delivery date anomalies: {e}")
    
    return suggestions

def find_frequent_patterns(offers: List[Dict[str, Any]]) -> List[Suggestion]:
    """
    Find frequent patterns in the offers data.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        List of pattern-based suggestions
    """
    suggestions = []
    
    if not offers:
        return suggestions
    
    # Count occurrences of different values
    product_counts = {}
    status_counts = {}
    producer_counts = {}
    
    for offer in offers:
        # Product counts
        product_name = get_nested_value(offer, "product_type.name")
        if product_name:
            product_counts[product_name] = product_counts.get(product_name, 0) + 1
        
        # Status counts
        status = offer.get("status")
        if status:
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Producer counts
        producer_name = get_nested_value(offer, "user.contact_name")
        if producer_name:
            producer_counts[producer_name] = producer_counts.get(producer_name, 0) + 1
    
    # Find most common product types
    if product_counts:
        top_products = sorted(product_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        for product, count in top_products:
            percentage = count / len(offers) * 100
            
            if percentage >= 20:  # Only suggest if it's at least 20% of offers
                filter_condition = FilterCondition(
                    column="product_type.name",
                    operator="equals",
                    value=product
                )
                
                suggestions.append(Suggestion(
                    title=f"{product} termékek",
                    description=f"{product} termékek ({count} db, az összes {percentage:.1f}%-a)",
                    filter_condition=filter_condition,
                    suggestion_type="FREQUENT_PATTERN",
                    relevance_score=min(percentage / 100, 0.9),  # Higher percentage -> higher relevance
                    explanation=f"A {product} a leggyakoribb termék az ajánlatok között, érdemes lehet részletesebben áttekinteni."
                ))
    
    # Find most common statuses
    if status_counts:
        top_statuses = sorted(status_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        for status, count in top_statuses:
            percentage = count / len(offers) * 100
            
            if percentage >= 20:  # Only suggest if it's at least 20% of offers
                filter_condition = FilterCondition(
                    column="status",
                    operator="equals",
                    value=status
                )
                
                suggestions.append(Suggestion(
                    title=f"{format_status(status)} státuszú ajánlatok",
                    description=f"{format_status(status)} státuszban lévő ajánlatok ({count} db, az összes {percentage:.1f}%-a)",
                    filter_condition=filter_condition,
                    suggestion_type="FREQUENT_PATTERN",
                    relevance_score=min(percentage / 100, 0.8),  # High but slightly lower than products
                    explanation=f"A {format_status(status)} a leggyakoribb státusz az ajánlatok között."
                ))
    
    # Find most active producers
    if producer_counts:
        top_producers = sorted(producer_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        for producer, count in top_producers:
            percentage = count / len(offers) * 100
            
            if percentage >= 15:  # Only suggest if it's at least 15% of offers
                filter_condition = FilterCondition(
                    column="user.contact_name",
                    operator="equals",
                    value=producer
                )
                
                suggestions.append(Suggestion(
                    title=f"{producer} termelő ajánlatai",
                    description=f"{producer} által leadott ajánlatok ({count} db, az összes {percentage:.1f}%-a)",
                    filter_condition=filter_condition,
                    suggestion_type="FREQUENT_PATTERN",
                    relevance_score=min(percentage / 100, 0.85),
                    explanation=f"{producer} a legaktívabb termelő az ajánlatok között."
                ))
    
    # Find frequent combinations (product and status)
    product_status_counts = {}
    for offer in offers:
        product = get_nested_value(offer, "product_type.name")
        status = offer.get("status")
        if product and status:
            key = (product, status)
            product_status_counts[key] = product_status_counts.get(key, 0) + 1
    
    if product_status_counts:
        top_combinations = sorted(product_status_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        for (product, status), count in top_combinations:
            percentage = count / len(offers) * 100
            
            if percentage >= 10:  # Lower threshold for combinations
                # Create a group with AND condition
                group = FilterGroup(operator="AND")
                group.add_condition(FilterCondition(
                    column="product_type.name",
                    operator="equals",
                    value=product
                ))
                group.add_condition(FilterCondition(
                    column="status",
                    operator="equals",
                    value=status
                ))
                
                suggestions.append(Suggestion(
                    title=f"{product} + {format_status(status)}",
                    description=f"{format_status(status)} státuszú {product} termékek ({count} db, az összes {percentage:.1f}%-a)",
                    filter_condition=group,
                    suggestion_type="FREQUENT_PATTERN",
                    relevance_score=min(percentage / 100 + 0.1, 0.9),  # Boost combinations slightly
                    explanation=f"A {product} termékek {format_status(status)} státuszban gyakran fordulnak elő."
                ))
    
    return suggestions

def find_recent_activity(offers: List[Dict[str, Any]]) -> List[Suggestion]:
    """
    Find recent activities in the offers data.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        List of activity-based suggestions
    """
    suggestions = []
    
    if not offers:
        return suggestions
    
    # Create a dataframe for easier analysis
    df_data = []
    for offer in offers:
        created_at = get_nested_value(offer, "created_at")
        row = {
            "id": offer.get("id"),
            "status": offer.get("status"),
            "created_at": created_at,
            "product_name": get_nested_value(offer, "product_type.name"),
            "producer_name": get_nested_value(offer, "user.contact_name")
        }
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    # Ensure created_at is datetime
    if "created_at" in df.columns and df["created_at"].notna().any():
        if not pd.api.types.is_datetime64_any_dtype(df["created_at"]):
            df["created_at"] = pd.to_datetime(df["created_at"], errors="coerce")
        
        # Find recent offers (last 24 hours)
        now = pd.Timestamp(datetime.now())
        yesterday = now - pd.Timedelta(days=1)
        recent_offers = df[df["created_at"] > yesterday]
        
        if not recent_offers.empty:
            filter_condition = FilterCondition(
                column="created_at",
                operator="after",
                value=yesterday.to_pydatetime()
            )
            
            suggestions.append(Suggestion(
                title="Utolsó 24 óra ajánlatai",
                description=f"Az elmúlt 24 órában létrehozott ajánlatok ({recent_offers.shape[0]} db)",
                filter_condition=filter_condition,
                suggestion_type="RECENT_ACTIVITY",
                relevance_score=0.95,
                explanation="Az új ajánlatok gyakran azonnali figyelmet igényelnek."
            ))
        
        # Find offers from last week
        last_week = now - pd.Timedelta(days=7)
        last_week_offers = df[(df["created_at"] > last_week) & (df["created_at"] <= yesterday)]
        
        if not last_week_offers.empty and last_week_offers.shape[0] > 5:  # Only if there are enough
            # Create a between condition
            filter_condition = FilterCondition(
                column="created_at",
                operator="between",
                value=(last_week.to_pydatetime(), yesterday.to_pydatetime())
            )
            
            suggestions.append(Suggestion(
                title="Elmúlt hét ajánlatai",
                description=f"Az elmúlt hétben létrehozott ajánlatok ({last_week_offers.shape[0]} db)",
                filter_condition=filter_condition,
                suggestion_type="RECENT_ACTIVITY",
                relevance_score=0.8,
                explanation="Az elmúlt hét ajánlatai még mindig frissnek számítanak és figyelmet igényelhetnek."
            ))
        
        # Analyze recent activity by status
        if not recent_offers.empty and "status" in recent_offers.columns:
            status_counts = recent_offers["status"].value_counts()
            
            # Find the most common recent status
            if not status_counts.empty:
                top_status = status_counts.index[0]
                top_count = status_counts.iloc[0]
                
                if top_count >= 3:  # Need at least a few to suggest
                    # Create a group with AND condition
                    group = FilterGroup(operator="AND")
                    group.add_condition(FilterCondition(
                        column="created_at",
                        operator="after",
                        value=yesterday.to_pydatetime()
                    ))
                    group.add_condition(FilterCondition(
                        column="status",
                        operator="equals",
                        value=top_status
                    ))
                    
                    suggestions.append(Suggestion(
                        title=f"Új {format_status(top_status)} státuszú ajánlatok",
                        description=f"Az elmúlt 24 órában létrehozott {format_status(top_status)} státuszú ajánlatok ({top_count} db)",
                        filter_condition=group,
                        suggestion_type="RECENT_ACTIVITY",
                        relevance_score=0.9,
                        explanation=f"A {format_status(top_status)} a leggyakoribb státusz az új ajánlatok között."
                    ))
    
    # Find statuses that need attention (CREATED, CONFIRMED_BY_COMPANY)
    attention_statuses = {"CREATED", "CONFIRMED_BY_COMPANY"}
    for status in attention_statuses:
        status_offers = [o for o in offers if o.get("status") == status]
        if status_offers:
            filter_condition = FilterCondition(
                column="status",
                operator="equals",
                value=status
            )
            
            title = f"{format_status(status)} státuszú ajánlatok"
            if status == "CREATED":
                description = f"Még nem feldolgozott ajánlatok ({len(status_offers)} db)"
                explanation = "A még nem feldolgozott ajánlatok figyelmet igényelnek."
                relevance = 0.9
            elif status == "CONFIRMED_BY_COMPANY":
                description = f"Feldolgozott, de termelő által még nem elfogadott ajánlatok ({len(status_offers)} db)"
                explanation = "Ezek az ajánlatok már feldolgozottak, de a termelő válaszára várnak."
                relevance = 0.85
            else:
                description = f"{format_status(status)} státuszú ajánlatok ({len(status_offers)} db)"
                explanation = "Ezek az ajánlatok figyelmet igényelhetnek."
                relevance = 0.7
            
            suggestions.append(Suggestion(
                title=title,
                description=description,
                filter_condition=filter_condition,
                suggestion_type="RECENT_ACTIVITY",
                relevance_score=relevance,
                explanation=explanation
            ))
    
    return suggestions

def find_value_distribution(offers: List[Dict[str, Any]]) -> List[Suggestion]:
    """
    Analyze value distributions in the offers data.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        List of distribution-based suggestions
    """
    suggestions = []
    
    if not offers or len(offers) < 5:
        return suggestions
    
    # Convert to dataframe for easier analysis
    df_data = []
    for offer in offers:
        row = {
            "id": offer.get("id"),
            "status": offer.get("status"),
            "delivery_date": get_nested_value(offer, "delivery_date"),
            "product_name": get_nested_value(offer, "product_type.name"),
            "quantity_in_kg": get_nested_value(offer, "quantity_in_kg"),
            "price": get_nested_value(offer, "price")
        }
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    # Analyze price distributions
    if "price" in df.columns and df["price"].notna().any():
        # Get quartiles
        q1 = df["price"].quantile(0.25)
        q3 = df["price"].quantile(0.75)
        
        # Low price range (bottom 25%)
        if q1 > 0:
            filter_condition = FilterCondition(
                column="price",
                operator="less_than",
                value=q1
            )
            
            suggestions.append(Suggestion(
                title="Alacsony árak",
                description=f"Az alsó 25%-ba tartozó árak ({format_price(q1)} alatt)",
                filter_condition=filter_condition,
                suggestion_type="VALUE_DISTRIBUTION",
                relevance_score=0.7,
                explanation=f"Az ajánlatok alsó 25%-a {format_price(q1)} alatti áron van."
            ))
        
        # High price range (top 25%)
        filter_condition = FilterCondition(
            column="price",
            operator="greater_than",
            value=q3
        )
        
        suggestions.append(Suggestion(
            title="Magas árak",
            description=f"A felső 25%-ba tartozó árak ({format_price(q3)} felett)",
            filter_condition=filter_condition,
            suggestion_type="VALUE_DISTRIBUTION",
            relevance_score=0.75,
            explanation=f"Az ajánlatok felső 25%-a {format_price(q3)} feletti áron van."
        ))
    
    # Analyze quantity distributions
    if "quantity_in_kg" in df.columns and df["quantity_in_kg"].notna().any():
        # Get quartiles
        q1 = df["quantity_in_kg"].quantile(0.25)
        q3 = df["quantity_in_kg"].quantile(0.75)
        
        # High quantity range (top 25%)
        filter_condition = FilterCondition(
            column="quantity_in_kg",
            operator="greater_than",
            value=q3
        )
        
        suggestions.append(Suggestion(
            title="Nagy mennyiségek",
            description=f"A felső 25%-ba tartozó mennyiségek ({q3:.1f} kg felett)",
            filter_condition=filter_condition,
            suggestion_type="VALUE_DISTRIBUTION",
            relevance_score=0.7,
            explanation=f"Az ajánlatok felső 25%-a {q3:.1f} kg feletti mennyiséget tartalmaz."
        ))
        
        # Create a group suggestion for high price AND high quantity
        group = FilterGroup(operator="AND")
        group.add_condition(FilterCondition(
            column="price",
            operator="greater_than",
            value=q3
        ))
        group.add_condition(FilterCondition(
            column="quantity_in_kg",
            operator="greater_than",
            value=q3
        ))
        
        high_price_and_quantity = df[(df["price"] > q3) & (df["quantity_in_kg"] > q3)]
        count = high_price_and_quantity.shape[0]
        percentage = count / len(offers) * 100
        
        if count > 0:
            suggestions.append(Suggestion(
                title="Magas ár ÉS nagy mennyiség",
                description=f"Az átlagosnál drágább ÉS nagyobb mennyiségű ajánlatok ({count} db, {percentage:.1f}%)",
                filter_condition=group,
                suggestion_type="VALUE_DISTRIBUTION",
                relevance_score=0.8,
                explanation="Ezek az ajánlatok a mennyiség és az ár szempontjából is a felső tartományba esnek."
            ))
    
    return suggestions

def find_similar_to_current(
    offers: List[Dict[str, Any]], 
    current_filters: Dict[str, Any]
) -> List[Suggestion]:
    """
    Find similar filters to the currently applied ones.
    
    Args:
        offers: List of offer dictionaries
        current_filters: Currently applied filters
        
    Returns:
        List of similarity-based suggestions
    """
    suggestions = []
    
    if not offers or not current_filters:
        return suggestions
    
    # Extract filter values
    selected_producer_id = current_filters.get("producer_filter_om")
    selected_status = current_filters.get("status_filter_om")
    selected_from_date = current_filters.get("from_date_filter_om")
    selected_to_date = current_filters.get("to_date_filter_om")
    
    # If we have a selected producer, suggest different statuses for this producer
    if selected_producer_id:
        # Get all offers for this producer
        producer_offers = [o for o in offers if get_nested_value(o, "user.id") == selected_producer_id]
        
        if producer_offers:
            producer_name = get_nested_value(producer_offers[0], "user.contact_name") or f"Termelő #{selected_producer_id}"
            
            # Count status occurrences
            status_counts = {}
            for offer in producer_offers:
                status = offer.get("status")
                if status:
                    status_counts[status] = status_counts.get(status, 0) + 1
            
            # Suggest statuses this producer has (excluding already selected status)
            for status, count in status_counts.items():
                if status != selected_status and count > 0:
                    # Create a group with AND condition
                    group = FilterGroup(operator="AND")
                    group.add_condition(FilterCondition(
                        column="user.id",
                        operator="equals",
                        value=selected_producer_id
                    ))
                    group.add_condition(FilterCondition(
                        column="status",
                        operator="equals",
                        value=status
                    ))
                    
                    suggestions.append(Suggestion(
                        title=f"{producer_name} + {format_status(status)}",
                        description=f"{producer_name} {format_status(status)} státuszú ajánlatai ({count} db)",
                        filter_condition=group,
                        suggestion_type="SIMILAR_TO_CURRENT",
                        relevance_score=min(0.8 + (count / len(producer_offers) * 0.2), 0.95),
                        explanation=f"Jelenleg {producer_name} termelő adatait nézi, ezek a {format_status(status)} státuszú ajánlatai."
                    ))
    
    # If we have a selected status, suggest different producers with this status
    if selected_status:
        # Get all offers with this status
        status_offers = [o for o in offers if o.get("status") == selected_status]
        
        if status_offers:
            # Count producer occurrences
            producer_counts = {}
            for offer in status_offers:
                producer_id = get_nested_value(offer, "user.id")
                producer_name = get_nested_value(offer, "user.contact_name")
                if producer_id and producer_name:
                    if producer_id not in producer_counts:
                        producer_counts[producer_id] = {"name": producer_name, "count": 0}
                    producer_counts[producer_id]["count"] += 1
            
            # Suggest producers with this status (excluding already selected producer)
            sorted_producers = sorted(producer_counts.items(), key=lambda x: x[1]["count"], reverse=True)
            for producer_id, data in sorted_producers[:5]:  # Limit to top 5
                if producer_id != selected_producer_id and data["count"] > 0:
                    # Create a group with AND condition
                    group = FilterGroup(operator="AND")
                    group.add_condition(FilterCondition(
                        column="status",
                        operator="equals",
                        value=selected_status
                    ))
                    group.add_condition(FilterCondition(
                        column="user.id",
                        operator="equals",
                        value=producer_id
                    ))
                    
                    suggestions.append(Suggestion(
                        title=f"{format_status(selected_status)} + {data['name']}",
                        description=f"{data['name']} {format_status(selected_status)} státuszú ajánlatai ({data['count']} db)",
                        filter_condition=group,
                        suggestion_type="SIMILAR_TO_CURRENT",
                        relevance_score=min(0.75 + (data["count"] / len(status_offers) * 0.2), 0.9),
                        explanation=f"Jelenleg {format_status(selected_status)} státuszú ajánlatokat néz, ezek {data['name']} termelő ilyen ajánlatai."
                    ))
    
    # Suggest changing the date range if it's not the default
    default_from = datetime.now().date() - timedelta(days=30)
    default_to = datetime.now().date()
    
    if selected_from_date and selected_from_date != default_from:
        # Suggest wider date range (1 month before the selected from date)
        wider_from = selected_from_date - timedelta(days=30)
        
        filter_condition = FilterCondition(
            column="created_at",
            operator="between",
            value=(wider_from, selected_to_date or default_to)
        )
        
        suggestions.append(Suggestion(
            title="Kibővített időtartomány",
            description=f"Korábbi időszaktól ({wider_from.strftime('%Y-%m-%d')}) kezdődő ajánlatok",
            filter_condition=filter_condition,
            suggestion_type="SIMILAR_TO_CURRENT",
            relevance_score=0.65,
            explanation="Ez a szűrő a jelenleginél szélesebb időtartományt fed le, így több ajánlatot talál."
        ))
    
    return suggestions

def find_empty_fields(offers: List[Dict[str, Any]]) -> List[Suggestion]:
    """
    Find offers with empty fields.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        List of empty field suggestions
    """
    suggestions = []
    
    if not offers:
        return suggestions
    
    # Count offers with empty important fields
    empty_description_count = 0
    empty_delivery_date_count = 0
    
    for offer in offers:
        # Check for empty description
        description = get_nested_value(offer, "description")
        if description is None or description == "":
            empty_description_count += 1
        
        # Check for empty delivery date
        delivery_date = get_nested_value(offer, "delivery_date")
        if delivery_date is None:
            empty_delivery_date_count += 1
    
    # Create suggestions for empty fields
    if empty_description_count > 0:
        percentage = empty_description_count / len(offers) * 100
        
        # Suggestion for empty descriptions
        filter_condition = FilterCondition(
            column="description",
            operator="equals",
            value=""
        )
        
        suggestions.append(Suggestion(
            title="Hiányzó megjegyzések",
            description=f"Megjegyzés nélküli ajánlatok ({empty_description_count} db, {percentage:.1f}%)",
            filter_condition=filter_condition,
            suggestion_type="EMPTY_FIELD",
            relevance_score=min(0.5 + percentage / 100, 0.85),
            explanation="A megjegyzés nélküli ajánlatok hiányos információkat tartalmazhatnak."
        ))
    
    if empty_delivery_date_count > 0:
        percentage = empty_delivery_date_count / len(offers) * 100
        
        # Suggestion for empty delivery dates
        filter_condition = FilterCondition(
            column="delivery_date",
            operator="equals",
            value=None
        )
        
        suggestions.append(Suggestion(
            title="Hiányzó beszállítási dátumok",
            description=f"Beszállítási dátum nélküli ajánlatok ({empty_delivery_date_count} db, {percentage:.1f}%)",
            filter_condition=filter_condition,
            suggestion_type="EMPTY_FIELD",
            relevance_score=min(0.6 + percentage / 100, 0.9),  # Higher priority than descriptions
            explanation="A beszállítási dátum nélküli ajánlatok figyelmet igényelnek."
        ))
    
    return suggestions

def generate_suggestions(
    offers: List[Dict[str, Any]], 
    current_filters: Dict[str, Any] = None,
    max_suggestions: int = 10
) -> List[Suggestion]:
    """
    Generate intelligent suggestions based on offers data.
    
    Args:
        offers: List of offer dictionaries
        current_filters: Currently applied filters
        max_suggestions: Maximum number of suggestions to return
        
    Returns:
        List of suggestions
    """
    all_suggestions = []
    
    # Run all suggestion generators
    all_suggestions.extend(find_anomalies(offers))
    all_suggestions.extend(find_frequent_patterns(offers))
    all_suggestions.extend(find_recent_activity(offers))
    all_suggestions.extend(find_value_distribution(offers))
    
    if current_filters:
        all_suggestions.extend(find_similar_to_current(offers, current_filters))
    
    all_suggestions.extend(find_empty_fields(offers))
    
    # Sort by priority (higher first)
    all_suggestions.sort(key=lambda x: x.priority, reverse=True)
    
    # Return limited number of suggestions
    return all_suggestions[:max_suggestions]

def render_suggestion_card(suggestion: Suggestion, index: int, on_apply: callable = None):
    """
    Render a single suggestion card.
    
    Args:
        suggestion: Suggestion to render
        index: Index for unique keys
        on_apply: Callback when suggestion is applied
    """
    display_info = suggestion.display_info
    icon = display_info.get("icon", "❔")
    color = display_info.get("color", "#888888")
    
    # Create card with CSS styling
    card_html = f"""
    <div class="suggestion-card" style="border: 1px solid #e0e0e0; border-left: 4px solid {color}; 
                                      border-radius: 4px; padding: 12px; margin-bottom: 10px;
                                      background-color: #ffffff;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="display: flex; align-items: center;">
                <span style="font-size: 1.2em; margin-right: 8px;">{icon}</span>
                <span style="font-weight: 500; font-size: 1.05em;">{suggestion.title}</span>
            </div>
            <span style="color: {color}; font-size: 0.8em; padding: 2px 6px; background-color: {color}20; 
                         border-radius: 4px;">
                {display_info.get("display_name", "Javaslat")}
            </span>
        </div>
        <div style="margin-bottom: 8px;">{suggestion.description}</div>
        <div id="explanation-{index}" style="display: none; margin: 8px 0px; font-size: 0.9em; 
                                           color: #666; background-color: #f5f5f5; 
                                           padding: 8px; border-radius: 4px;">
            {suggestion.explanation or "Nincs további magyarázat."}
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <button id="toggle-explanation-{index}" 
                    onclick="toggleExplanation({index})" 
                    style="background: none; border: none; color: #1976D2; cursor: pointer; 
                           font-size: 0.9em; padding: 0;">
                Magyarázat ▾
            </button>
            <button id="apply-suggestion-{index}" 
                    onclick="applySuggestion({index})"
                    style="background-color: #f5f5f5; border: 1px solid #e0e0e0; color: #333;
                           border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 0.9em;">
                Alkalmaz
            </button>
        </div>
    </div>
    """
    
    return card_html

def render_suggestions_ui(
    offers: List[Dict[str, Any]], 
    current_filters: Dict[str, Any] = None,
    on_apply: callable = None
):
    """
    Render the suggestions UI.
    
    Args:
        offers: List of offer dictionaries
        current_filters: Currently applied filters
        on_apply: Callback when a suggestion is applied
    """
    # Generate suggestions
    suggestions = generate_suggestions(offers, current_filters)
    
    if not suggestions:
        st.info("Nincs elérhető javaslat a jelenlegi ajánlatokhoz.")
        return
    
    # Create JavaScript for handling button clicks
    js = """
    <script>
    function toggleExplanation(index) {
        const explanation = document.getElementById('explanation-' + index);
        const toggleButton = document.getElementById('toggle-explanation-' + index);
        
        if (explanation.style.display === 'none') {
            explanation.style.display = 'block';
            toggleButton.innerHTML = 'Magyarázat ▴';
        } else {
            explanation.style.display = 'none';
            toggleButton.innerHTML = 'Magyarázat ▾';
        }
    }
    
    function applySuggestion(index) {
        const input = document.getElementById('apply-suggestion-' + index);
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    </script>
    """
    
    # Render container with cards
    st.markdown("""
    <style>
    .suggestions-container {
        margin: 15px 0;
    }
    .suggestion-card:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Render all suggestions
    html = js + """
    <div class="suggestions-container">
    """
    
    for i, suggestion in enumerate(suggestions):
        html += render_suggestion_card(suggestion, i)
    
    html += """
    </div>
    """
    
    st.markdown(html, unsafe_allow_html=True)
    
    # Add hidden inputs for button clicks
    for i, suggestion in enumerate(suggestions):
        key = f"apply-suggestion-{i}"
        if st.text_input(f"Apply suggestion {i}", value="false", key=key, label_visibility="collapsed") == "true":
            # Handle applying the suggestion
            if on_apply:
                on_apply(suggestion)
            
            # Force refresh
            st.rerun()

def apply_suggestion_to_filters(suggestion: Suggestion):
    """
    Apply a suggestion to the current filters in session state.
    
    Args:
        suggestion: Suggestion to apply
    """
    # Check if we need to store suggestion in state
    if "applied_suggestion" not in st.session_state:
        st.session_state["applied_suggestion"] = suggestion
    
    # Check if we need to initialize complex filter root
    if "complex_filter_root" not in st.session_state:
        st.session_state["complex_filter_root"] = FilterGroup(operator="AND")
    
    # Add the filter condition to the complex filter root
    st.session_state["complex_filter_root"].add_condition(suggestion.filter_condition)
    
    # Show success message
    show_inline_success(f"Javaslat alkalmazva: {suggestion.title}")

def apply_suggestions(offers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Apply context-sensitive suggestions to offers and render UI.
    
    Args:
        offers: List of offer dictionaries
        
    Returns:
        Filtered offers (same as input, this function doesn't filter directly)
    """
    # Get current filters from session state
    current_filters = {
        "producer_filter_om": st.session_state.get("producer_filter_om"),
        "status_filter_om": st.session_state.get("status_filter_om"),
        "from_date_filter_om": st.session_state.get("from_date_filter_om"),
        "to_date_filter_om": st.session_state.get("to_date_filter_om")
    }
    
    # Create styled UI container
    st.markdown("""
    <style>
    .suggestions-title-container {
        margin: 15px 0;
        padding: 15px;
        background-color: #ECEFF1;
        border-radius: 8px;
        border: 1px solid #CFD8DC;
    }
    .suggestions-title {
        font-weight: 500;
        margin-bottom: 10px;
        color: #263238;
        display: flex;
        align-items: center;
    }
    .suggestions-badge {
        background-color: #1976D2;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8em;
        margin-left: 8px;
    }
    </style>
    <div class="suggestions-title-container">
        <div class="suggestions-title">
            Intelligens szűrési javaslatok
            <span class="suggestions-badge">AI</span>
        </div>
    """, unsafe_allow_html=True)
    
    # Render suggestions UI
    with st.container():
        render_suggestions_ui(
            offers=offers,
            current_filters=current_filters,
            on_apply=apply_suggestion_to_filters
        )
    
    # Close the container div
    st.markdown("</div>", unsafe_allow_html=True)
    
    # We don't directly filter here, we just return the same offers
    # (filtering happens via the complex filters component)
    return offers

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Suggestion System Test", layout="wide")
    
    st.title("Suggestion System Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-05-20T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"id": 1, "contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-05-15T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"id": 2, "contact_name": "Mezőgazda Márton", "email": "<EMAIL>"},
            "description": "Kiváló minőségű, zamatos körte."
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-05-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"id": 1, "contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Nagy mennyiségű étkezési alma, hosszú tárolhatósággal."
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-04-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"id": 3, "contact_name": "Gyümölcsös Gábor", "email": "<EMAIL>"},
            "description": "Befőzésre alkalmas szilva, magas cukortartalommal."
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-05-21T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"id": 4, "contact_name": "Almás Anna", "email": "<EMAIL>"},
            "description": ""  # Empty description
        },
        {
            "id": 6,
            "status": "CREATED",
            "delivery_date": None,  # Missing delivery date
            "created_at": "2025-05-21T09:10:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 600,
            "price": 380,
            "user": {"id": 1, "contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Friss tavaszi alma."
        },
        {
            "id": 7,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-04-10",  # Past delivery date
            "created_at": "2025-03-20T08:30:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 150,
            "price": 580,
            "user": {"id": 3, "contact_name": "Gyümölcsös Gábor", "email": "<EMAIL>"},
            "description": "Korai szilva első termés."
        },
        {
            "id": 8,
            "status": "CREATED",
            "delivery_date": "2026-08-15",  # Far future
            "created_at": "2025-05-19T13:45:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 1200,  # Large quantity
            "price": 290,
            "user": {"id": 1, "contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Nagy mennyiségű áru jövő évi leszállítása."
        },
        {
            "id": 9,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-30",
            "created_at": "2025-05-05T11:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 350,
            "price": 1200,  # Very high price
            "user": {"id": 2, "contact_name": "Mezőgazda Márton", "email": "<EMAIL>"},
            "description": "Prémium minőségű bio körte, kiemelkedő áron."
        },
        {
            "id": 10,
            "status": "REJECTED_BY_USER",
            "delivery_date": "2025-05-15",
            "created_at": "2025-04-30T10:10:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 200,
            "price": 100,  # Very low price
            "user": {"id": 4, "contact_name": "Almás Anna", "email": "<EMAIL>"},
            "description": "Alacsony árkategóriás termék."
        }
    ]
    
    # Apply suggestions
    apply_suggestions(sample_offers)
    
    # Display tab to see the raw sample data
    with st.expander("Minta ajánlatok", expanded=False):
        st.json(sample_offers, expanded=False)