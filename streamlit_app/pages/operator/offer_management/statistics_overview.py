"""
Statistical overview panel for the offer management page.
Provides visualizations and metrics for offers data.
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import logging
import uuid
import json

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.api_client import get_statistics, get_offers, get_product_types
    from streamlit_app.pages.operator.offer_management.custom_css_framework import inject_custom_css
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_info,
        show_inline_success,
        show_inline_warning
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.api_client import get_statistics, get_offers, get_product_types
        from pages.operator.offer_management.custom_css_framework import inject_custom_css
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_info,
            show_inline_success,
            show_inline_warning
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        try:
            # Try direct local import
            from api_client import get_statistics, get_offers, get_product_types
            from custom_css_framework import inject_custom_css
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_info,
                show_inline_success,
                show_inline_warning
            )
            # Fallback formatting functions if import fails
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in statistics_overview.py, using minimal implementations")
            
            def get_statistics(params=None):
                """Fallback statistics API call"""
                return False, "API client not available"
                
            def get_offers(params=None):
                """Fallback offers API call"""
                return False, "API client not available"
                
            def get_product_types():
                """Fallback product types API call"""
                return False, "API client not available"
                
            def inject_custom_css():
                """Fallback CSS injection"""
                st.markdown("""
                <style>
                .stat-card {
                    padding: 1rem;
                    border-radius: 0.5rem;
                    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                    margin-bottom: 1rem;
                }
                .stat-number {
                    font-size: 1.75rem;
                    font-weight: bold;
                }
                .stat-label {
                    font-size: 0.875rem;
                    color: #6c757d;
                }
                </style>
                """, unsafe_allow_html=True)
                
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Logger setup
logger = logging.getLogger(__name__)

def generate_unique_key(base_name, suffix=None):
    """
    Egyedi kulcs generálása Streamlit widgetekhez.
    
    Args:
        base_name (str): Az alap kulcsnév
        suffix (str, optional): Opcionális utótag
        
    Returns:
        str: Egyedi kulcs a widget számára
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def fetch_statistics_data(params=None):
    """
    Fetch statistics data from the API.
    
    Args:
        params (dict, optional): Parameters for filtering the statistics
        
    Returns:
        tuple: (success, data/error_message)
    """
    logger.info(f"Fetching statistics data with params: {params}")
    
    # If no params provided, use default time range (last 30 days)
    if not params:
        today = datetime.now().date()
        thirty_days_ago = today - timedelta(days=30)
        params = {
            "date_from": thirty_days_ago.strftime("%Y-%m-%d"),
            "date_to": today.strftime("%Y-%m-%d")
        }
    
    # Call the API
    success, result = get_statistics(params)
    
    if not success:
        logger.error(f"Error fetching statistics: {result}")
        return False, result
    
    return True, result

def fetch_offers_for_stats(params=None):
    """
    Fetch offers for generating statistics when the API doesn't provide them directly.
    
    Args:
        params (dict, optional): Parameters for filtering the offers
        
    Returns:
        tuple: (success, offers/error_message)
    """
    logger.info(f"Fetching offers for statistics with params: {params}")
    
    # If no params provided, use default time range (last 30 days)
    if not params:
        today = datetime.now().date()
        thirty_days_ago = today - timedelta(days=30)
        params = {
            "from_date": thirty_days_ago.strftime("%Y-%m-%d"),
            "to_date": today.strftime("%Y-%m-%d")
        }
    
    # Call the API
    success, result = get_offers(params)
    
    if not success:
        logger.error(f"Error fetching offers for statistics: {result}")
        return False, result
    
    return True, result

def process_offers_data(offers):
    """
    Process raw offers data into a format suitable for statistics visualization.
    
    Args:
        offers (list): List of offer objects
        
    Returns:
        dict: Processed statistics data
    """
    # Convert to DataFrame for easier processing
    df = pd.DataFrame(offers)
    
    # Initialize statistics dict
    stats = {
        "total_offers": len(df),
        "total_quantity": 0,
        "total_value": 0,
        "status_counts": {},
        "product_counts": {},
        "daily_counts": {},
        "monthly_counts": {}
    }
    
    # Skip processing if no offers
    if len(df) == 0:
        return stats
    
    # Calculate total quantity and value
    if "quantity_in_kg" in df.columns:
        stats["total_quantity"] = df["quantity_in_kg"].sum()
    
    if "price" in df.columns and "quantity_in_kg" in df.columns:
        df["total_value"] = df["price"] * df["quantity_in_kg"]
        stats["total_value"] = df["total_value"].sum()
    
    # Status distribution
    if "status" in df.columns:
        stats["status_counts"] = df["status"].value_counts().to_dict()
    
    # Product type distribution
    if "product_name" in df.columns:
        stats["product_counts"] = df["product_name"].value_counts().to_dict()
    
    # Time distribution
    if "created_at" in df.columns:
        df["date"] = pd.to_datetime(df["created_at"]).dt.date
        df["month"] = pd.to_datetime(df["created_at"]).dt.strftime("%Y-%m")
        
        # Daily counts
        stats["daily_counts"] = df.groupby("date").size().to_dict()
        
        # Monthly counts
        stats["monthly_counts"] = df.groupby("month").size().to_dict()
    
    return stats

def render_statistic_card(title, value, subtext=None, icon=None, color="#1E88E5"):
    """
    Render a statistic card with a title, value, and optional subtext and icon.
    
    Args:
        title (str): Card title
        value (str/int/float): The main statistic value
        subtext (str, optional): Additional context or explanation
        icon (str, optional): Icon name from the Material Design icon set
        color (str, optional): Card accent color
    """
    # Format the value based on type
    if isinstance(value, int):
        formatted_value = f"{value:,}"
    elif isinstance(value, float):
        formatted_value = f"{value:,.2f}"
    else:
        formatted_value = value
    
    # Define the icon HTML if provided
    icon_html = f"""<i class="material-icons" style="font-size: 1.5rem; color: {color}; margin-right: 0.5rem;">{icon}</i>""" if icon else ""
    
    # Define the subtext HTML if provided
    subtext_html = f"""<div class="stat-label">{subtext}</div>""" if subtext else ""
    
    # Render the card
    st.markdown(
        f"""
        <div class="stat-card" style="border-left: 4px solid {color}; background-color: white;">
            <div style="display: flex; align-items: center;">
                {icon_html}
                <div>
                    <div class="stat-label">{title}</div>
                    <div class="stat-number" style="color: {color};">{formatted_value}</div>
                    {subtext_html}
                </div>
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )

def plot_status_distribution(stats_data):
    """
    Create a pie chart showing the distribution of offers by status.
    
    Args:
        stats_data (dict): Statistics data containing status counts
        
    Returns:
        plotly.graph_objects.Figure: The plotly figure object
    """
    # Status mapping for display
    status_mapping = {
        "CREATED": "Létrehozva",
        "CONFIRMED_BY_COMPANY": "Megerősítve",
        "ACCEPTED_BY_USER": "Elfogadva",
        "REJECTED_BY_USER": "Elutasítva",
        "FINALIZED": "Teljesítve",
        "MODIFIED": "Módosítva"
    }
    
    # Status colors
    status_colors = {
        "CREATED": "#64B5F6",            # Light Blue
        "CONFIRMED_BY_COMPANY": "#FFA726", # Orange
        "ACCEPTED_BY_USER": "#66BB6A",    # Green
        "REJECTED_BY_USER": "#EF5350",    # Red
        "FINALIZED": "#8D6E63",          # Brown
        "MODIFIED": "#9575CD"            # Purple
    }
    
    # Extract and prepare data
    status_counts = stats_data.get("status_counts", {})
    
    if not status_counts:
        # Create an empty figure if no data
        fig = go.Figure()
        fig.update_layout(
            title="Nincsenek adatok a státusz eloszláshoz",
            height=350
        )
        return fig
    
    # Convert status keys to display names
    labels = [status_mapping.get(status, status) for status in status_counts.keys()]
    values = list(status_counts.values())
    colors = [status_colors.get(status, "#BDBDBD") for status in status_counts.keys()]
    
    # Create pie chart
    fig = go.Figure(data=[go.Pie(
        labels=labels,
        values=values,
        marker=dict(colors=colors),
        textinfo='percent+label',
        hole=0.4,
    )])
    
    # Update layout
    fig.update_layout(
        title="Ajánlatok státusz szerint",
        height=350,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=-0.2,
            xanchor="center",
            x=0.5
        )
    )
    
    return fig

def plot_product_distribution(stats_data):
    """
    Create a horizontal bar chart showing the distribution of offers by product type.
    
    Args:
        stats_data (dict): Statistics data containing product counts
        
    Returns:
        plotly.graph_objects.Figure: The plotly figure object
    """
    # Extract and prepare data
    product_counts = stats_data.get("product_counts", {})
    
    if not product_counts:
        # Create an empty figure if no data
        fig = go.Figure()
        fig.update_layout(
            title="Nincsenek adatok a termék eloszláshoz",
            height=350
        )
        return fig
    
    # Sort products by count (descending)
    sorted_products = sorted(product_counts.items(), key=lambda x: x[1], reverse=True)
    products = [item[0] for item in sorted_products]
    counts = [item[1] for item in sorted_products]
    
    # Create horizontal bar chart
    fig = go.Figure(data=[go.Bar(
        x=counts,
        y=products,
        orientation='h',
        marker=dict(color="#4CAF50"),
    )])
    
    # Update layout
    fig.update_layout(
        title="Ajánlatok termék típus szerint",
        height=max(350, 50 * len(products)),  # Dynamic height based on number of products
        xaxis_title="Ajánlatok száma",
        margin=dict(l=20, r=20, t=40, b=20),
    )
    
    return fig

def plot_time_series(stats_data):
    """
    Create a time series line chart showing offers over time.
    
    Args:
        stats_data (dict): Statistics data containing daily or monthly counts
        
    Returns:
        plotly.graph_objects.Figure: The plotly figure object
    """
    # Extract and prepare data
    daily_counts = stats_data.get("daily_counts", {})
    
    if not daily_counts:
        # Create an empty figure if no data
        fig = go.Figure()
        fig.update_layout(
            title="Nincsenek adatok az időbeli eloszláshoz",
            height=350
        )
        return fig
    
    # Convert to pandas Series for easier processing
    if isinstance(daily_counts, dict):
        # Convert string dates (if any) to datetime objects
        if daily_counts and isinstance(list(daily_counts.keys())[0], str):
            daily_counts = {datetime.strptime(date, "%Y-%m-%d").date() if isinstance(date, str) else date: count 
                         for date, count in daily_counts.items()}
        
        # Convert to Series and sort by date
        ts = pd.Series(daily_counts)
        ts.index = pd.to_datetime(ts.index)
        ts = ts.sort_index()
    else:
        # If it's already a Series, just ensure it's sorted
        ts = daily_counts.sort_index()
    
    # Create line chart
    fig = go.Figure(data=[go.Scatter(
        x=ts.index,
        y=ts.values,
        mode='lines+markers',
        line=dict(color="#1976D2", width=2),
        marker=dict(color="#1976D2", size=6),
    )])
    
    # Update layout
    fig.update_layout(
        title="Ajánlatok időbeli eloszlása",
        height=350,
        xaxis_title="Dátum",
        yaxis_title="Ajánlatok száma",
        margin=dict(l=20, r=20, t=40, b=20),
    )
    
    return fig

def filter_stats_date_range():
    """
    Render date range filter controls for statistics.
    
    Returns:
        tuple: (from_date, to_date) - Selected date range
    """
    # Default date range (last 30 days)
    default_from_date = datetime.now().date() - timedelta(days=30)
    default_to_date = datetime.now().date()
    
    # Check if we have values in session state
    from_date = st.session_state.get("stats_from_date", default_from_date)
    to_date = st.session_state.get("stats_to_date", default_to_date)
    
    # Create columns for the date picker
    col1, col2 = st.columns(2)
    
    # Render date pickers
    with col1:
        from_date = st.date_input(
            "Kezdő dátum",
            value=from_date,
            key=generate_unique_key("stats_from_date")
        )
    
    with col2:
        to_date = st.date_input(
            "Záró dátum",
            value=to_date,
            key=generate_unique_key("stats_to_date")
        )
    
    # Store in session state
    st.session_state["stats_from_date"] = from_date
    st.session_state["stats_to_date"] = to_date
    
    # Validate date range
    if from_date > to_date:
        show_inline_warning("A kezdő dátum későbbi, mint a záró dátum. Az adatok szűrése nem fog megfelelően működni.")
    
    return from_date, to_date

def render_statistics_overview(offers=None, show_filters=True):
    """
    Render the statistics overview panel for the offer management page.
    
    Args:
        offers (list, optional): Preloaded offers data. If not provided, data will be loaded from the API.
        show_filters (bool, optional): Whether to show filter controls. Defaults to True.
    """
    # Inject custom CSS
    inject_custom_css()
    
    # Add title and description
    st.markdown("## Ajánlatok Statisztikai Áttekintése")
    st.markdown("Az ajánlatok statisztikai elemzése és vizualizációja a kiválasztott időszakra.")
    
    # Date range filter
    if show_filters:
        st.markdown("### Időszak kiválasztása")
        from_date, to_date = filter_stats_date_range()
        
        # Generate API params from the selected date range
        params = {
            "date_from": from_date.strftime("%Y-%m-%d"),
            "date_to": to_date.strftime("%Y-%m-%d")
        }
        
        # Refresh button
        if st.button("Statisztikák frissítése", key=generate_unique_key("refresh_stats")):
            st.session_state.pop("statistics_data", None)
            st.experimental_rerun()
    else:
        # Use default time range
        today = datetime.now().date()
        thirty_days_ago = today - timedelta(days=30)
        params = {
            "date_from": thirty_days_ago.strftime("%Y-%m-%d"),
            "date_to": today.strftime("%Y-%m-%d")
        }
    
    # Show loading indicator
    with st.spinner("Statisztikák betöltése..."):
        # Try to get statistics directly from the API first
        success, stats_data = fetch_statistics_data(params)
        
        if not success:
            # If the direct API call failed, try to generate statistics from offers data
            if offers is None:
                success, offers = fetch_offers_for_stats(params)
                
                if not success:
                    show_inline_error(f"Hiba a statisztikák betöltése során: {offers}")
                    return
            
            # Process offers data to generate statistics
            stats_data = process_offers_data(offers)
    
    # Display summary statistics in cards
    st.markdown("### Összesített Mutatók")
    
    # Create 3-column layout for summary stats
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Total offers
        total_offers = stats_data.get("total_offers", 0) if isinstance(stats_data, dict) else 0
        render_statistic_card(
            "Összes ajánlat", 
            total_offers,
            "az időszakban",
            icon="insert_chart",
            color="#1976D2"  # Blue
        )
    
    with col2:
        # Total quantity
        total_quantity = stats_data.get("total_quantity", 0) if isinstance(stats_data, dict) else 0
        render_statistic_card(
            "Összes mennyiség", 
            f"{total_quantity:,.2f} kg",
            "az időszakban",
            icon="assessment",
            color="#43A047"  # Green
        )
    
    with col3:
        # Total value
        total_value = stats_data.get("total_value", 0) if isinstance(stats_data, dict) else 0
        render_statistic_card(
            "Összes érték", 
            f"{total_value:,.0f} Ft",
            "az időszakban",
            icon="monetization_on",
            color="#E53935"  # Red
        )
    
    # Status distribution
    status_counts = stats_data.get("status_counts", {}) if isinstance(stats_data, dict) else {}
    
    # Additional metrics row
    col1, col2 = st.columns(2)
    
    with col1:
        # Active offers (non-finalized, non-rejected)
        active_count = sum(status_counts.get(status, 0) for status in ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER"])
        render_statistic_card(
            "Aktív ajánlatok", 
            active_count,
            "nem lezárt ajánlatok",
            icon="hourglass_empty",
            color="#FFA000"  # Amber
        )
    
    with col2:
        # Completion rate
        finalized_count = status_counts.get("FINALIZED", 0)
        completion_rate = (finalized_count / total_offers * 100) if total_offers > 0 else 0
        render_statistic_card(
            "Teljesítési arány", 
            f"{completion_rate:.1f}%",
            f"{finalized_count} teljesített a {total_offers} ajánlatból",
            icon="done_all",
            color="#6D4C41"  # Brown
        )
    
    # Visualizations
    st.markdown("### Vizualizációk")
    
    # Distribution visualizations
    col1, col2 = st.columns(2)
    
    with col1:
        # Status distribution chart
        fig = plot_status_distribution(stats_data)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Product distribution chart
        fig = plot_product_distribution(stats_data)
        st.plotly_chart(fig, use_container_width=True)
    
    # Time series chart
    fig = plot_time_series(stats_data)
    st.plotly_chart(fig, use_container_width=True)
    
    # Export options
    st.markdown("### Exportálási Lehetőségek")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Statisztikák exportálása CSV", key=generate_unique_key("export_csv")):
            # Convert stats data to CSV
            if isinstance(stats_data, dict):
                # Convert the nested dictionaries to a flattened format
                flat_stats = {
                    "Összes ajánlat": stats_data.get("total_offers", 0),
                    "Összes mennyiség (kg)": stats_data.get("total_quantity", 0),
                    "Összes érték (Ft)": stats_data.get("total_value", 0),
                }
                
                # Add status counts
                for status, count in stats_data.get("status_counts", {}).items():
                    flat_stats[f"Státusz - {status}"] = count
                
                # Add product counts
                for product, count in stats_data.get("product_counts", {}).items():
                    flat_stats[f"Termék - {product}"] = count
                
                # Create DataFrame and convert to CSV
                df = pd.DataFrame([flat_stats])
                csv = df.to_csv(index=False)
                
                st.download_button(
                    label="Letöltés CSV",
                    data=csv,
                    file_name=f"ajanlat_statisztikak_{datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv",
                )
    
    with col2:
        if st.button("Statisztikák exportálása JSON", key=generate_unique_key("export_json")):
            # Convert stats data to JSON
            if isinstance(stats_data, dict):
                # Convert date objects to strings
                json_compatible_data = json.dumps(stats_data, default=str, ensure_ascii=False, indent=2)
                
                st.download_button(
                    label="Letöltés JSON",
                    data=json_compatible_data,
                    file_name=f"ajanlat_statisztikak_{datetime.now().strftime('%Y%m%d')}.json",
                    mime="application/json",
                )

# Test function
if __name__ == "__main__":
    # Test the component with dummy data
    st.set_page_config(layout="wide")
    
    # Sample data
    dummy_stats = {
        "total_offers": 125,
        "total_quantity": 1250.75,
        "total_value": 3752250,
        "status_counts": {
            "CREATED": 35,
            "CONFIRMED_BY_COMPANY": 42,
            "ACCEPTED_BY_USER": 28,
            "REJECTED_BY_USER": 15,
            "FINALIZED": 5
        },
        "product_counts": {
            "Alma": 35,
            "Körte": 27,
            "Szilva": 18,
            "Szőlő": 15,
            "Dinnye": 10,
            "Sárgabarack": 8,
            "Cseresznye": 7,
            "Meggy": 5
        },
        "daily_counts": {
            "2025-04-01": 3,
            "2025-04-02": 5,
            "2025-04-03": 4,
            "2025-04-04": 7,
            "2025-04-05": 6,
            "2025-04-06": 3,
            "2025-04-07": 8,
            "2025-04-08": 10,
            "2025-04-09": 12,
            "2025-04-10": 9,
            "2025-04-11": 7,
            "2025-04-12": 5,
            "2025-04-13": 6,
            "2025-04-14": 8,
            "2025-04-15": 9,
            "2025-04-16": 7,
            "2025-04-17": 5,
            "2025-04-18": 6,
            "2025-04-19": 4,
            "2025-04-20": 2,
            "2025-04-21": 3,
            "2025-04-22": 5,
            "2025-04-23": 8,
            "2025-04-24": 9,
            "2025-04-25": 11,
            "2025-04-26": 10,
            "2025-04-27": 8,
            "2025-04-28": 7,
            "2025-04-29": 5,
            "2025-04-30": 4
        }
    }
    
    # Render the statistics overview
    render_statistics_overview(dummy_stats)