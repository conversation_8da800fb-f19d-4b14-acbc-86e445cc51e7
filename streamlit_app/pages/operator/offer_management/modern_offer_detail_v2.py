"""
Modern Offer Detail View - Vizu<PERSON><PERSON><PERSON> gaz<PERSON>, interakt<PERSON>v aj<PERSON><PERSON>
Inspired by CodePen designs with modern UI/UX principles
"""
import streamlit as st
import streamlit.components.v1 as components
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
import json
import logging
import html

# Import our HTML renderer
try:
    from .html_renderer import render_html, render_css
except ImportError:
    # Fallback implementation
    def render_html(content, height=None):
        if hasattr(st, 'html'):
            st.html(content)
        else:
            components.html(content, height=height or 600)

logger = logging.getLogger(__name__)

class ModernOfferDetailView:
    """Modern, vizuálisan gazdag ajánlat részletező nézet"""
    
    def __init__(self, offer_data):
        self.offer = offer_data
        self.inject_modern_styles()
    
    def inject_modern_styles(self):
        """Modern CSS design system injection"""
        css_content = """
        <style>
            :root {
                /* Modern color palette */
                --primary: #6366F1;
                --primary-light: #818CF8;
                --primary-dark: #4F46E5;
                --secondary: #EC4899;
                --success: #10B981;
                --warning: #F59E0B;
                --danger: #EF4444;
                --info: #3B82F6;
                
                /* Neutral colors */
                --gray-50: #F9FAFB;
                --gray-100: #F3F4F6;
                --gray-200: #E5E7EB;
                --gray-300: #D1D5DB;
                --gray-400: #9CA3AF;
                --gray-500: #6B7280;
                --gray-600: #4B5563;
                --gray-700: #374151;
                --gray-800: #1F2937;
                --gray-900: #111827;
                
                /* Shadows */
                --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                
                /* Animations */
                --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
                --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
                --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            /* Modern Card Container */
            .modern-offer-container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 2rem;
                animation: fadeIn var(--transition-slow);
            }
            
            /* Hero Section */
            .offer-hero {
                background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
                color: white;
                padding: 3rem;
                border-radius: 24px;
                margin-bottom: 2rem;
                box-shadow: var(--shadow-xl);
                position: relative;
                overflow: hidden;
            }
            
            .offer-hero::before {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                animation: pulse 4s ease-in-out infinite;
            }
            
            .hero-content {
                position: relative;
                z-index: 1;
            }
            
            /* Status Badge */
            .status-badge-modern {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1.5rem;
                border-radius: 9999px;
                font-size: 0.875rem;
                font-weight: 600;
                transition: all var(--transition-base);
                background: rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            
            .status-badge-modern:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            
            /* Modern Card */
            .modern-card {
                background: white;
                border-radius: 16px;
                padding: 1.5rem;
                box-shadow: var(--shadow-md);
                transition: all var(--transition-base);
                border: 1px solid var(--gray-200);
                position: relative;
                overflow: hidden;
                margin-bottom: 1.5rem;
            }
            
            .modern-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
                border-color: var(--primary-light);
            }
            
            .modern-card::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: var(--primary);
                transform: scaleY(0);
                transition: transform var(--transition-base);
                transform-origin: bottom;
            }
            
            .modern-card:hover::after {
                transform: scaleY(1);
                transform-origin: top;
            }
            
            .card-header {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 1rem;
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--gray-800);
            }
            
            .card-icon {
                font-size: 1.5rem;
            }
            
            /* Metric Card */
            .metric-card {
                background: var(--gray-50);
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 1rem;
                transition: all var(--transition-fast);
            }
            
            .metric-card:hover {
                background: var(--gray-100);
                transform: scale(1.02);
            }
            
            .metric-label {
                font-size: 0.875rem;
                color: var(--gray-600);
                margin-bottom: 0.25rem;
            }
            
            .metric-value {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--gray-900);
            }
            
            .metric-delta {
                font-size: 0.75rem;
                margin-top: 0.25rem;
            }
            
            .metric-delta.positive {
                color: var(--success);
            }
            
            .metric-delta.negative {
                color: var(--danger);
            }
            
            /* Timeline */
            .timeline-container {
                position: relative;
                padding-left: 2rem;
            }
            
            .timeline-event {
                display: flex;
                align-items: start;
                margin-bottom: 1.5rem;
                animation: slideIn var(--transition-base);
            }
            
            .timeline-dot {
                position: absolute;
                left: 0;
                width: 1rem;
                height: 1rem;
                background: var(--primary);
                border-radius: 50%;
                box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            }
            
            .timeline-dot.completed {
                background: var(--success);
            }
            
            .timeline-dot.pending {
                background: var(--gray-300);
            }
            
            .timeline-content {
                margin-left: 1.5rem;
            }
            
            .timeline-line {
                position: absolute;
                left: 0.5rem;
                width: 1px;
                background: var(--gray-200);
            }
            
            /* Data Viz Card */
            .viz-card {
                background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
                border-radius: 16px;
                padding: 2rem;
                box-shadow: var(--shadow-lg);
                position: relative;
                margin-bottom: 1.5rem;
            }
            
            /* Interactive Elements */
            .interactive-element {
                cursor: pointer;
                transition: all var(--transition-fast);
                position: relative;
            }
            
            .interactive-element:hover {
                transform: scale(1.05);
            }
            
            .interactive-element:active {
                transform: scale(0.95);
            }
            
            /* Floating Action Buttons */
            .fab-container {
                position: fixed;
                bottom: 2rem;
                right: 2rem;
                display: flex;
                flex-direction: column;
                gap: 1rem;
                z-index: 1000;
            }
            
            .fab {
                width: 56px;
                height: 56px;
                border-radius: 50%;
                background: var(--primary);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: var(--shadow-lg);
                transition: all var(--transition-base);
                cursor: pointer;
                font-size: 1.25rem;
            }
            
            .fab:hover {
                transform: scale(1.1);
                box-shadow: var(--shadow-xl);
            }
            
            /* Resizable Panel */
            .resizable-panel {
                position: relative;
                min-width: 300px;
                resize: horizontal;
                overflow: auto;
                background: white;
                border-radius: 16px;
                padding: 1.5rem;
                box-shadow: var(--shadow-md);
                margin-bottom: 1.5rem;
            }
            
            .resize-handle {
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 10px;
                cursor: ew-resize;
                background: var(--gray-200);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.75rem;
                color: var(--gray-500);
                opacity: 0;
                transition: opacity var(--transition-fast);
            }
            
            .resizable-panel:hover .resize-handle {
                opacity: 1;
            }
            
            /* Animations */
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.3; }
                50% { transform: scale(1.1); opacity: 0.1; }
            }
            
            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            /* Responsive Design */
            @media (max-width: 768px) {
                .modern-offer-container { padding: 1rem; }
                .offer-hero { padding: 2rem; border-radius: 16px; }
                .fab-container { bottom: 1rem; right: 1rem; }
                .fab { width: 48px; height: 48px; font-size: 1rem; }
                .modern-card { padding: 1rem; }
            }
        </style>
        """
        st.markdown(css_content, unsafe_allow_html=True)
    
    def render(self):
        """Main render method"""
        # Container wrapper
        st.markdown('<div class="modern-offer-container">', unsafe_allow_html=True)
        
        # Hero section
        self.render_hero_section()
        
        # Main content grid
        col1, col2, col3 = st.columns([1.2, 1, 1])
        
        with col1:
            self.render_offer_summary_card()
            self.render_timeline_card()
        
        with col2:
            self.render_product_details_card()
            self.render_pricing_card()
        
        with col3:
            self.render_producer_card()
            self.render_actions_card()
        
        # Advanced visualizations
        self.render_data_visualizations()
        
        # Floating action buttons
        self.render_fab_menu()
        
        # Close container
        st.markdown('</div>', unsafe_allow_html=True)
    
    def render_hero_section(self):
        """Modern hero section with gradient background"""
        status = self.offer.get('status', 'UNKNOWN')
        offer_id = self.offer.get('id', 'N/A')
        quantity = self._to_float(self.offer.get('quantity_in_kg', 0))
        price = self._to_float(self.offer.get('price', 0))
        total = quantity * price if quantity and price else 0
        delivery_date = self.offer.get('delivery_date', '')
        
        hero_html = f"""
        <div class="offer-hero">
            <div class="hero-content">
                <h1 style="font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700;">
                    Ajánlat #{offer_id}
                </h1>
                <div style="display: flex; align-items: center; gap: 2rem; flex-wrap: wrap;">
                    <div class="status-badge-modern">
                        <span>{self._get_status_icon(status)}</span>
                        <span>{self._get_status_display(status)}</span>
                    </div>
                    <div style="display: flex; gap: 3rem;">
                        <div>
                            <div style="opacity: 0.8; font-size: 0.875rem;">Mennyiség</div>
                            <div style="font-size: 1.5rem; font-weight: 600;">
                                {self._format_quantity(quantity)} kg
                            </div>
                        </div>
                        <div>
                            <div style="opacity: 0.8; font-size: 0.875rem;">Összérték</div>
                            <div style="font-size: 1.5rem; font-weight: 600;">
                                {self._format_price(total)}
                            </div>
                        </div>
                        <div>
                            <div style="opacity: 0.8; font-size: 0.875rem;">Beszállítás</div>
                            <div style="font-size: 1.5rem; font-weight: 600;">
                                {self._format_date(delivery_date) if delivery_date else 'N/A'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """
        render_html(hero_html, height=200)
    
    def render_offer_summary_card(self):
        """Offer summary with modern card design"""
        # Generate card content
        card_html = """
        <div class="modern-card">
            <div class="card-header">
                <span class="card-icon">📋</span>
                <span>Ajánlat összefoglaló</span>
            </div>
        """
        
        # Key metrics - ensure numeric values
        price = self._to_float(self.offer.get('price', 0))
        confirmed_price = self._to_float(self.offer.get('confirmed_price', 0))
        price_delta = ((confirmed_price - price) / price * 100) if price > 0 else 0
        
        quantity = self._to_float(self.offer.get('quantity_in_kg', 0))
        confirmed_quantity = self._to_float(self.offer.get('confirmed_quantity', 0))
        fulfillment = (confirmed_quantity / quantity * 100) if quantity > 0 else 0
        
        metrics = [
            {
                "label": "Egységár",
                "value": f"{self._format_price(price)}/kg",
                "delta": f"{price_delta:+.1f}%" if confirmed_price else None,
                "delta_class": "positive" if price_delta > 0 else "negative" if price_delta < 0 else ""
            },
            {
                "label": "Teljesítés",
                "value": f"{fulfillment:.0f}%",
                "delta": None,
                "delta_class": ""
            }
        ]
        
        for metric in metrics:
            card_html += f"""
            <div class="metric-card">
                <div class="metric-label">{metric['label']}</div>
                <div class="metric-value">{metric['value']}</div>
                {f'<div class="metric-delta {metric["delta_class"]}">{metric["delta"]}</div>' if metric['delta'] else ''}
            </div>
            """
        
        # Add note if exists
        note = self.offer.get('note')
        if note:
            escaped_note = html.escape(str(note))
            card_html += f"""
            <div style="margin-top: 1rem; padding: 0.75rem; background: var(--gray-50); border-radius: 8px;">
                <div style="font-size: 0.875rem; color: var(--gray-600); margin-bottom: 0.25rem;">Megjegyzés</div>
                <div style="color: var(--gray-800);">{escaped_note}</div>
            </div>
            """
        
        card_html += "</div>"
        render_html(card_html, height=300)
    
    def render_product_details_card(self):
        """Interactive product details card"""
        product = self.offer.get('product_type', {})
        product_name = html.escape(str(product.get('name', 'N/A')))
        category_name = html.escape(str(product.get('category', {}).get('name', 'N/A')))
        
        card_html = f"""
        <div class="modern-card">
            <div class="card-header">
                <span class="card-icon">🌾</span>
                <span>Termék részletek</span>
            </div>
            
            <div style="
                width: 100%;
                height: 120px;
                background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 3rem;
                margin-bottom: 1rem;
            ">
                🌾
            </div>
            
            <div style="font-weight: 600; font-size: 1.125rem; margin-bottom: 0.25rem;">
                {product_name}
            </div>
            <div style="color: var(--gray-600); font-size: 0.875rem;">
                Kategória: {category_name}
            </div>
        """
        
        # Quality parameters as tags
        quality_params = self.offer.get('quality_parameters', {})
        if quality_params and isinstance(quality_params, dict):
            card_html += '<div style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-top: 1rem;">'
            for param, value in quality_params.items():
                escaped_param = html.escape(str(param))
                escaped_value = html.escape(str(value))
                card_html += f"""
                <span style="
                    background: var(--gray-100);
                    color: var(--gray-700);
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    border: 1px solid var(--gray-200);
                ">
                    {escaped_param}: {escaped_value}
                </span>
                """
            card_html += '</div>'
        
        card_html += "</div>"
        render_html(card_html, height=350)
    
    def render_pricing_card(self):
        """Advanced pricing visualization"""
        st.markdown("#### 💰 Árazás és visszaigazolás")
        
        # Create pricing comparison chart
        fig = go.Figure()
        
        # Data preparation - ensure numeric values
        categories = ['Eredeti', 'Visszaigazolt']
        quantities = [
            self._to_float(self.offer.get('quantity_in_kg', 0)),
            self._to_float(self.offer.get('confirmed_quantity', self.offer.get('quantity_in_kg', 0)))
        ]
        prices = [
            self._to_float(self.offer.get('price', 0)),
            self._to_float(self.offer.get('confirmed_price', self.offer.get('price', 0)))
        ]
        
        # Bar chart for quantities
        fig.add_trace(go.Bar(
            name='Mennyiség (kg)',
            x=categories,
            y=quantities,
            marker_color='#6366F1',
            text=[f'{q:,.0f} kg' for q in quantities],
            textposition='auto',
            yaxis='y',
        ))
        
        # Line chart for prices
        fig.add_trace(go.Scatter(
            name='Egységár (Ft/kg)',
            x=categories,
            y=prices,
            mode='lines+markers+text',
            marker=dict(size=12, color='#EC4899'),
            line=dict(width=3, color='#EC4899'),
            text=[f'{p:,.0f} Ft' for p in prices],
            textposition='top center',
            yaxis='y2'
        ))
        
        # Layout configuration
        fig.update_layout(
            title='',
            xaxis=dict(title=''),
            yaxis=dict(title='Mennyiség (kg)', side='left', showgrid=False),
            yaxis2=dict(title='Egységár (Ft/kg)', overlaying='y', side='right', showgrid=False),
            height=250,
            margin=dict(l=0, r=0, t=20, b=0),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="top",
                y=1.1,
                xanchor="center",
                x=0.5
            ),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    def render_timeline_card(self):
        """Interactive timeline visualization"""
        events = self._get_timeline_events()
        
        timeline_html = """
        <div class="modern-card">
            <div class="card-header">
                <span class="card-icon">🕒</span>
                <span>Idősor</span>
            </div>
            <div class="timeline-container">
        """
        
        for i, event in enumerate(events):
            is_completed = event.get('completed', True)
            dot_class = 'completed' if is_completed else 'pending'
            
            # Calculate line height for connecting line
            line_height = "3rem" if i < len(events) - 1 else "0"
            line_top = f"{2.5 + i * 4.5}rem"
            
            timeline_html += f"""
            <div class="timeline-event">
                <div class="timeline-dot {dot_class}"></div>
                <div class="timeline-content">
                    <div style="font-weight: 600; color: var(--gray-800);">
                        {html.escape(event['label'])}
                    </div>
                    <div style="font-size: 0.875rem; color: var(--gray-500);">
                        {html.escape(event['date'])}
                    </div>
                    {f'<div style="font-size: 0.75rem; color: var(--gray-400); margin-top: 0.25rem;">{html.escape(event.get("description", ""))}</div>' if event.get("description") else ''}
                </div>
            </div>
            """
            
            # Add connecting line if not last event
            if i < len(events) - 1:
                timeline_html += f"""
                <div class="timeline-line" style="top: {line_top}; height: {line_height};"></div>
                """
        
        timeline_html += """
            </div>
        </div>
        """
        
        render_html(timeline_html, height=400)
    
    def render_producer_card(self):
        """Producer information card"""
        producer = self.offer.get('user', {})
        
        producer_name = html.escape(str(producer.get('contact_name', 'N/A')))
        company_name = html.escape(str(producer.get('company_name', 'N/A')))
        email = html.escape(str(producer.get('email', '')))
        phone = html.escape(str(producer.get('phone', '')))
        
        card_html = f"""
        <div class="modern-card">
            <div class="card-header">
                <span class="card-icon">👤</span>
                <span>Termelő adatok</span>
            </div>
            
            <div style="text-align: center; margin-bottom: 1rem;">
                <div style="
                    width: 80px;
                    height: 80px;
                    background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 2rem;
                    margin: 0 auto 1rem;
                ">
                    👤
                </div>
                <div style="font-weight: 600; font-size: 1.125rem;">
                    {producer_name}
                </div>
                <div style="color: var(--gray-600); font-size: 0.875rem;">
                    {company_name}
                </div>
            </div>
            
            <div style="space-y: 0.75rem;">
        """
        
        if email:
            card_html += f"""
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;">
                <span style="color: var(--gray-500);">📧</span>
                <a href="mailto:{email}" style="color: var(--primary); text-decoration: none;">
                    {email}
                </a>
            </div>
            """
        
        if phone:
            card_html += f"""
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: var(--gray-500);">📱</span>
                <a href="tel:{phone}" style="color: var(--primary); text-decoration: none;">
                    {phone}
                </a>
            </div>
            """
        
        card_html += """
            </div>
        </div>
        """
        
        render_html(card_html, height=300)
    
    def render_actions_card(self):
        """Quick actions card"""
        st.markdown("#### ⚡ Gyors műveletek")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📧 Email küldése", use_container_width=True):
                st.info("Email funkció fejlesztés alatt")
            
            if st.button("📄 PDF generálás", use_container_width=True):
                st.info("PDF export fejlesztés alatt")
        
        with col2:
            if st.button("✏️ Szerkesztés", use_container_width=True):
                st.info("Szerkesztés fejlesztés alatt")
            
            if st.button("🔄 Státusz váltás", use_container_width=True):
                st.info("Státusz váltás fejlesztés alatt")
    
    def render_data_visualizations(self):
        """Advanced data visualizations section"""
        st.markdown("## 📊 Részletes elemzések")
        
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_price_history_chart()
        
        with col2:
            self.render_delivery_performance_gauge()
    
    def render_price_history_chart(self):
        """Price history line chart"""
        st.markdown("#### 📈 Ár történet")
        
        # Generate sample data (in real app, this would come from API)
        import numpy as np
        dates = [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30, 0, -5)]
        base_price = self._to_float(self.offer.get('price', 850))
        prices = base_price + np.random.randint(-50, 50, len(dates))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=prices,
            mode='lines+markers',
            name='Ár',
            line=dict(color='#6366F1', width=3),
            marker=dict(size=8, color='#6366F1'),
            fill='tozeroy',
            fillcolor='rgba(99, 102, 241, 0.1)'
        ))
        
        # Add current price line
        fig.add_hline(
            y=base_price,
            line_dash="dash",
            line_color="#EC4899",
            annotation_text=f"Jelenlegi: {base_price} Ft",
            annotation_position="right"
        )
        
        fig.update_layout(
            height=300,
            margin=dict(l=0, r=0, t=0, b=0),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            showlegend=False,
            hovermode='x unified',
            xaxis=dict(showgrid=False),
            yaxis=dict(showgrid=True, gridcolor='rgba(0,0,0,0.05)', title='Ár (Ft/kg)')
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    def render_delivery_performance_gauge(self):
        """Delivery performance gauge chart"""
        st.markdown("#### 🚚 Szállítási teljesítmény")
        
        # Calculate performance (sample calculation)
        delivery_date = self.offer.get('delivery_date')
        if delivery_date:
            if isinstance(delivery_date, str):
                delivery_date = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
            days_until = (delivery_date - datetime.now()).days
            performance = max(0, min(100, 100 - days_until * 2))
        else:
            performance = 50
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = performance,
            delta = {'reference': 80, 'relative': False},
            title = {'text': "Teljesítmény %"},
            domain = {'x': [0, 1], 'y': [0, 1]},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "#6366F1"},
                'steps': [
                    {'range': [0, 50], 'color': "rgba(239, 68, 68, 0.1)"},
                    {'range': [50, 80], 'color': "rgba(245, 158, 11, 0.1)"},
                    {'range': [80, 100], 'color': "rgba(16, 185, 129, 0.1)"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(
            height=300,
            margin=dict(l=0, r=0, t=40, b=0),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    def render_fab_menu(self):
        """Floating action button menu"""
        fab_html = """
        <div class="fab-container">
            <div class="fab" onclick="window.print()" title="Nyomtatás">
                🖨️
            </div>
            <div class="fab" style="background: var(--success);" title="Excel export">
                📊
            </div>
            <div class="fab" style="background: var(--warning);" title="PDF export">
                📄
            </div>
            <div class="fab" style="background: var(--info);" title="Megosztás">
                🔗
            </div>
        </div>
        """
        render_html(fab_html, height=0)
    
    # Helper methods
    def _to_float(self, value):
        """Convert value to float safely"""
        try:
            if value is None:
                return 0.0
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                # Remove any non-numeric characters except . and -
                cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
                return float(cleaned) if cleaned else 0.0
            return 0.0
        except (ValueError, TypeError):
            return 0.0
    
    def _get_status_icon(self, status):
        """Get icon for status"""
        icons = {
            'CREATED': '🔵',
            'CONFIRMED_BY_COMPANY': '🟡',
            'ACCEPTED_BY_USER': '🟢',
            'REJECTED_BY_USER': '🔴',
            'FINALIZED': '✅'
        }
        return icons.get(status, '⚪')
    
    def _get_status_display(self, status):
        """Get display text for status"""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Visszaigazolva',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)
    
    def _format_quantity(self, value):
        """Format quantity value"""
        try:
            numeric_value = self._to_float(value)
            return f"{numeric_value:,.2f}" if numeric_value else "0"
        except:
            return str(value) if value else "0"
    
    def _format_price(self, value):
        """Format price value"""
        try:
            numeric_value = self._to_float(value)
            return f"{numeric_value:,.0f} Ft" if numeric_value else "0 Ft"
        except:
            return f"{value} Ft" if value else "0 Ft"
    
    def _format_date(self, value):
        """Format date value"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d.")
            return value.strftime("%Y. %m. %d.")
        except:
            return str(value) if value else "-"
    
    def _get_timeline_events(self):
        """Get timeline events from offer data"""
        events = []
        
        # Created event
        if self.offer.get('created_at'):
            events.append({
                'label': 'Létrehozva',
                'date': self._format_date(self.offer.get('created_at')),
                'completed': True
            })
        
        # Status-based events
        status = self.offer.get('status')
        
        if status in ['CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']:
            events.append({
                'label': 'Visszaigazolva',
                'date': self._format_date(self.offer.get('confirmed_at')) or 'Teljesítve',
                'completed': True
            })
        
        if status in ['ACCEPTED_BY_USER', 'FINALIZED']:
            events.append({
                'label': 'Elfogadva',
                'date': self._format_date(self.offer.get('accepted_at')) or 'Teljesítve',
                'completed': True
            })
        elif status == 'REJECTED_BY_USER':
            events.append({
                'label': 'Elutasítva',
                'date': self._format_date(self.offer.get('rejected_at')) or 'Teljesítve',
                'description': self.offer.get('rejection_reason'),
                'completed': True
            })
        
        if status == 'FINALIZED':
            events.append({
                'label': 'Véglegesítve',
                'date': self._format_date(self.offer.get('finalized_at')) or 'Teljesítve',
                'completed': True
            })
        
        # Delivery event
        if self.offer.get('delivery_date'):
            delivery_date = self.offer.get('delivery_date')
            if isinstance(delivery_date, str):
                try:
                    delivery_date = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
                except:
                    delivery_date = None
            
            is_future = delivery_date and delivery_date > datetime.now() if delivery_date else False
            
            events.append({
                'label': 'Beszállítás',
                'date': self._format_date(delivery_date),
                'description': 'Tervezett' if is_future else None,
                'completed': not is_future
            })
        
        return events