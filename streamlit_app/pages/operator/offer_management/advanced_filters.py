"""
Fejlett szűrési komponensek az ajánlatkezelő modulhoz.

Ez a modul fejlett szűrési komponenseket tartalmaz, többe<PERSON> között
csúszkás dátumtartomán<PERSON>, multiple-select s<PERSON><PERSON><PERSON><PERSON>,
term<PERSON>k típus szerinti szűrést és előre definiált beállításokat.
"""
import streamlit as st
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# Az egyedi CSS keretrendszer importálása
try:
    from streamlit_app.pages.operator.offer_management.custom_css_framework import (
        inject_filter_styles,
        COLORS
    )
except ImportError:
    try:
        from pages.operator.offer_management.custom_css_framework import (
            inject_filter_styles,
            COLORS
        )
    except ImportError:
        try:
            from offer_management.custom_css_framework import (
                inject_filter_styles,
                COLORS
            )
        except ImportError:
            try:
                from custom_css_framework import (
                    inject_filter_styles,
                    COLORS
                )
            except ImportError:
                logging.error("Nem sikerült importálni a CSS keretrendszert")
                # Fallback minimális implementációk
                def inject_filter_styles():
                    pass
                COLORS = {"primary": "#3584e4"}

# Logging beállítása
logger = logging.getLogger(__name__)

def inject_advanced_filter_styles():
    """
    Fejlett szűrők egyedi stílusainak injektálása.
    """
    # Először az alap szűrő stílusokat injektáljuk
    inject_filter_styles()
    
    # Fejlett szűrő stílusok
    advanced_filter_css = """
    <style>
    /* Csúszkás dátumszűrő stílusok */
    .date-slider-container {
        padding: 10px 5px;
        margin: 15px 0;
    }
    
    .date-slider-labels {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .date-slider-start-label, .date-slider-end-label {
        font-size: var(--font-size-sm);
        color: var(--color-darkgray);
    }
    
    .date-slider-track {
        position: relative;
        height: 6px;
        background-color: var(--color-midgray);
        border-radius: var(--radius-full);
        margin: 15px 0;
    }
    
    .date-slider-range {
        position: absolute;
        height: 6px;
        background-color: var(--color-primary);
        border-radius: var(--radius-full);
    }
    
    .date-slider-handle {
        position: absolute;
        top: -7px;
        width: 20px;
        height: 20px;
        background-color: white;
        border: 2px solid var(--color-primary);
        border-radius: 50%;
        cursor: pointer;
        box-shadow: var(--shadow-small);
        transition: box-shadow var(--animation-fast), transform var(--animation-fast);
    }
    
    .date-slider-handle:hover {
        box-shadow: var(--shadow-medium);
        transform: scale(1.1);
    }
    
    .date-slider-tooltip {
        position: absolute;
        top: -35px;
        transform: translateX(-50%);
        background-color: var(--color-primary);
        color: white;
        padding: 2px 6px;
        border-radius: var(--radius-md);
        font-size: var(--font-size-xs);
        white-space: nowrap;
        pointer-events: none;
        opacity: 0;
        transition: opacity var(--animation-fast);
    }
    
    .date-slider-handle:hover .date-slider-tooltip {
        opacity: 1;
    }
    
    .date-slider-inputs {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        gap: 10px;
    }
    
    .date-slider-input {
        flex: 1;
    }
    
    /* Multiple-select checkbox stílusok */
    .multi-select-container {
        margin: 10px 0;
    }
    
    .multi-select-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .multi-select-title {
        font-weight: 500;
        color: var(--color-darkgray);
    }
    
    .multi-select-actions {
        display: flex;
        gap: 5px;
    }
    
    .multi-select-action {
        font-size: var(--font-size-xs);
        color: var(--color-primary);
        cursor: pointer;
        transition: color var(--animation-fast);
    }
    
    .multi-select-action:hover {
        color: var(--color-darkgray);
        text-decoration: underline;
    }
    
    .multi-select-items {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-md);
        padding: 5px;
    }
    
    .multi-select-item {
        padding: 5px;
        border-radius: var(--radius-sm);
        margin-bottom: 2px;
        transition: background-color var(--animation-fast);
        display: flex;
        align-items: center;
    }
    
    .multi-select-item:hover {
        background-color: var(--color-lightgray);
    }
    
    .multi-select-checkbox {
        margin-right: 8px;
    }
    
    .multi-select-label {
        flex: 1;
    }
    
    .multi-select-search {
        margin-bottom: 10px;
        padding: 5px 10px;
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-md);
        width: 100%;
    }
    
    /* Gyorsszűrő chipek */
    .quick-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin: 10px 0;
    }
    
    .quick-filter-chip {
        display: inline-flex;
        align-items: center;
        padding: 5px 12px;
        background-color: var(--color-lightgray);
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: all var(--animation-fast);
        font-size: var(--font-size-sm);
    }
    
    .quick-filter-chip:hover {
        background-color: rgba(53, 132, 228, 0.1);
        border-color: var(--color-primary);
    }
    
    .quick-filter-chip.active {
        background-color: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
    }
    
    .quick-filter-icon {
        margin-right: 5px;
    }
    
    /* Termék típus autocomplete */
    .autocomplete-container {
        position: relative;
        margin: 10px 0;
    }
    
    .autocomplete-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
    }
    
    .autocomplete-results {
        position: absolute;
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
        background-color: white;
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-medium);
        z-index: 100;
    }
    
    .autocomplete-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color var(--animation-fast);
    }
    
    .autocomplete-item:hover {
        background-color: rgba(53, 132, 228, 0.1);
    }
    
    .autocomplete-highlight {
        font-weight: bold;
        color: var(--color-primary);
    }
    
    /* Range slider stílusok */
    .range-slider-container {
        margin: 15px 0;
    }
    
    .range-slider-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .range-slider-title {
        font-weight: 500;
        color: var(--color-darkgray);
    }
    
    .range-slider-values {
        font-size: var(--font-size-sm);
        color: var(--color-primary);
    }
    </style>
    """
    
    # JavaScript a dátum csúszka működéséhez
    date_slider_js = """
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dátum csúszkák inicializálása
        function initDateSliders() {
            const dateSliders = document.querySelectorAll('.date-slider-container');
            
            dateSliders.forEach(slider => {
                const sliderId = slider.getAttribute('data-slider-id');
                const startHandle = slider.querySelector('.date-slider-start-handle');
                const endHandle = slider.querySelector('.date-slider-end-handle');
                const rangeEl = slider.querySelector('.date-slider-range');
                const startTooltip = startHandle.querySelector('.date-slider-tooltip');
                const endTooltip = endHandle.querySelector('.date-slider-tooltip');
                const track = slider.querySelector('.date-slider-track');
                
                // Kezdő adatok lekérése
                const startDate = new Date(slider.getAttribute('data-start-date'));
                const endDate = new Date(slider.getAttribute('data-end-date'));
                const minDate = new Date(slider.getAttribute('data-min-date'));
                const maxDate = new Date(slider.getAttribute('data-max-date'));
                
                // Teljes időtartam milliszekundumban
                const totalRange = maxDate.getTime() - minDate.getTime();
                
                // Kezdeti pozíciók beállítása
                let startPos = ((startDate.getTime() - minDate.getTime()) / totalRange) * 100;
                let endPos = ((endDate.getTime() - minDate.getTime()) / totalRange) * 100;
                
                startPos = Math.max(0, Math.min(100, startPos));
                endPos = Math.max(0, Math.min(100, endPos));
                
                startHandle.style.left = startPos + '%';
                endHandle.style.left = endPos + '%';
                rangeEl.style.left = startPos + '%';
                rangeEl.style.width = (endPos - startPos) + '%';
                
                // Tooltip frissítése
                function updateTooltips() {
                    startTooltip.textContent = formatDate(getDateFromPosition(startPos));
                    endTooltip.textContent = formatDate(getDateFromPosition(endPos));
                }
                
                // Dátum pozícióból
                function getDateFromPosition(position) {
                    const posTime = (position / 100) * totalRange + minDate.getTime();
                    return new Date(posTime);
                }
                
                // Tooltip dátum formázás
                function formatDate(date) {
                    return date.toISOString().split('T')[0];
                }
                
                // Fogantyúk húzásának kezelése
                function handleDrag(handle, isStart) {
                    let isDragging = false;
                    let startX, startLeft;
                    
                    handle.addEventListener('mousedown', startDrag);
                    handle.addEventListener('touchstart', e => {
                        const touch = e.touches[0];
                        startDrag({ clientX: touch.clientX });
                    });
                    
                    function startDrag(e) {
                        e.preventDefault();
                        isDragging = true;
                        startX = e.clientX;
                        startLeft = parseFloat(handle.style.left);
                        
                        document.addEventListener('mousemove', drag);
                        document.addEventListener('touchmove', touchDrag);
                        document.addEventListener('mouseup', stopDrag);
                        document.addEventListener('touchend', stopDrag);
                    }
                    
                    function touchDrag(e) {
                        const touch = e.touches[0];
                        drag({ clientX: touch.clientX });
                    }
                    
                    function drag(e) {
                        if (!isDragging) return;
                        
                        const trackRect = track.getBoundingClientRect();
                        const deltaX = e.clientX - startX;
                        let newPos = startLeft + (deltaX / trackRect.width) * 100;
                        
                        // Korlátok
                        newPos = Math.max(0, Math.min(100, newPos));
                        
                        // Fogantyúk nem keresztezhetik egymást
                        if (isStart) {
                            newPos = Math.min(newPos, endPos - 1);
                            startPos = newPos;
                        } else {
                            newPos = Math.max(newPos, startPos + 1);
                            endPos = newPos;
                        }
                        
                        // Pozíció frissítése
                        handle.style.left = newPos + '%';
                        
                        // Range frissítése
                        rangeEl.style.left = startPos + '%';
                        rangeEl.style.width = (endPos - startPos) + '%';
                        
                        // Tooltip frissítése
                        updateTooltips();
                    }
                    
                    function stopDrag() {
                        if (!isDragging) return;
                        isDragging = false;
                        
                        document.removeEventListener('mousemove', drag);
                        document.removeEventListener('touchmove', touchDrag);
                        document.removeEventListener('mouseup', stopDrag);
                        document.removeEventListener('touchend', stopDrag);
                        
                        // Streamlit visszajelzés a változásról
                        const hiddenStart = document.getElementById(`date_slider_start_${sliderId}`);
                        const hiddenEnd = document.getElementById(`date_slider_end_${sliderId}`);
                        if (hiddenStart && hiddenEnd) {
                            hiddenStart.value = formatDate(getDateFromPosition(startPos));
                            hiddenEnd.value = formatDate(getDateFromPosition(endPos));
                            
                            hiddenStart.dispatchEvent(new Event('change'));
                            hiddenEnd.dispatchEvent(new Event('change'));
                        }
                    }
                }
                
                // Fogantyúk inicializálása
                handleDrag(startHandle, true);
                handleDrag(endHandle, false);
                
                // Kezdeti tooltip beállítások
                updateTooltips();
            });
        }
        
        // Inicializálás
        initDateSliders();
        
        // MutationObserver a dinamikusan hozzáadott csúszkákhoz
        const observer = new MutationObserver((mutations) => {
            let reinitNeeded = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && (
                            node.classList?.contains('date-slider-container') || 
                            node.querySelector?.('.date-slider-container')
                        )) {
                            reinitNeeded = true;
                        }
                    });
                }
            });
            
            if (reinitNeeded) {
                initDateSliders();
            }
        });
        
        // A teljes dokumentum figyelése
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
    </script>
    """
    
    try:
        st.markdown(advanced_filter_css + date_slider_js, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a fejlett szűrő stílusok injektálásakor: {str(e)}")


def render_date_range_slider(
    min_date: datetime,
    max_date: datetime,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    key: Optional[str] = None
) -> Tuple[datetime, datetime]:
    """
    Csúszkás dátumtartomány szűrő megjelenítése.
    
    Vizuális dátumcsúszka két fogantyúval, amely lehetővé teszi egy időtartomány
    interaktív kiválasztását a min és max dátumok között.
    
    Args:
        min_date: A legkorábbi választható dátum
        max_date: A legkésőbbi választható dátum
        start_date: A kezdeti kezdő dátum (alapértelmezetten min_date)
        end_date: A kezdeti végső dátum (alapértelmezetten max_date)
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Tuple[datetime, datetime]: A kiválasztott (kezdő, végső) dátumpár
    """
    # Egyedi azonosító generálása ha nincs megadva
    slider_id = key or f"date_slider_{str(uuid.uuid4())[:8]}"
    
    # Alapértelmezett dátumok beállítása ha nincsenek megadva
    if start_date is None:
        start_date = min_date
    if end_date is None:
        end_date = max_date
    
    # Ellenőrizzük, hogy a dátumok datetime objektumok
    for date_obj in [min_date, max_date, start_date, end_date]:
        if not isinstance(date_obj, datetime):
            raise TypeError("Minden dátumnak datetime objektumnak kell lennie")
    
    # Ellenőrizzük, hogy a dátumok sorrendje megfelelő
    if min_date > max_date:
        min_date, max_date = max_date, min_date
    
    start_date = max(min_date, min(start_date, max_date))
    end_date = max(start_date, min(end_date, max_date))
    
    # Session state kulcsok
    start_key = f"{slider_id}_start"
    end_key = f"{slider_id}_end"
    
    # Inicializáljuk a session state-et ha még nem létezik
    if start_key not in st.session_state:
        st.session_state[start_key] = start_date.strftime("%Y-%m-%d")
    if end_key not in st.session_state:
        st.session_state[end_key] = end_date.strftime("%Y-%m-%d")
    
    # HTML a csúszkához
    st.markdown(f"""
    <div class="date-slider-container" data-slider-id="{slider_id}"
         data-min-date="{min_date.strftime("%Y-%m-%d")}"
         data-max-date="{max_date.strftime("%Y-%m-%d")}" 
         data-start-date="{start_date.strftime("%Y-%m-%d")}"
         data-end-date="{end_date.strftime("%Y-%m-%d")}">
        
        <div class="date-slider-labels">
            <div class="date-slider-start-label">{min_date.strftime("%Y-%m-%d")}</div>
            <div class="date-slider-end-label">{max_date.strftime("%Y-%m-%d")}</div>
        </div>
        
        <div class="date-slider-track">
            <div class="date-slider-range"></div>
            <div class="date-slider-start-handle date-slider-handle" style="left: 0%;">
                <div class="date-slider-tooltip"></div>
            </div>
            <div class="date-slider-end-handle date-slider-handle" style="left: 100%;">
                <div class="date-slider-tooltip"></div>
            </div>
        </div>
        
        <div class="date-slider-inputs">
            <div class="date-slider-input">
                <small>Kezdő dátum:</small>
                <div><strong>{st.session_state[start_key]}</strong></div>
            </div>
            <div class="date-slider-input">
                <small>Végső dátum:</small>
                <div><strong>{st.session_state[end_key]}</strong></div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Rejtett input mezők a kiválasztott dátumok tárolására
    start_date_str = st.text_input(
        "Start Date", 
        value=st.session_state[start_key],
        key=f"date_slider_start_{slider_id}",
        label_visibility="collapsed"
    )
    
    end_date_str = st.text_input(
        "End Date", 
        value=st.session_state[end_key],
        key=f"date_slider_end_{slider_id}",
        label_visibility="collapsed"
    )
    
    # Ha változott valamelyik dátum, frissítsük a session state-et
    if start_date_str != st.session_state[start_key]:
        st.session_state[start_key] = start_date_str
    
    if end_date_str != st.session_state[end_key]:
        st.session_state[end_key] = end_date_str
    
    # Konvertáljuk a kiválasztott dátumokat datetime objektumokká
    try:
        selected_start = datetime.strptime(st.session_state[start_key], "%Y-%m-%d")
    except (ValueError, TypeError):
        selected_start = start_date
    
    try:
        selected_end = datetime.strptime(st.session_state[end_key], "%Y-%m-%d")
    except (ValueError, TypeError):
        selected_end = end_date
    
    return selected_start, selected_end


def render_multiple_select(
    options: List[Dict[str, Any]],
    value_key: str,
    label_key: Optional[str] = None,
    default_selected: Optional[List[Any]] = None,
    title: str = "Válasszon elemeket",
    placeholder: str = "Keresés...",
    key: Optional[str] = None,
    show_search: bool = True,
    show_select_all: bool = True,
    height: Optional[int] = None
) -> List[Any]:
    """
    Fejlett multiple-select komponens keresőmezővel és egyéb funkciókkal.
    
    Args:
        options: Dict objektumok listája a választható opciókhoz
        value_key: Dict kulcs az érték mezőhöz az options listában
        label_key: Dict kulcs a felirat mezőhöz (ha None, a value_key lesz használva)
        default_selected: Alapértelmezetten kiválasztott értékek listája
        title: A komponens címe
        placeholder: Placeholder szöveg a keresőmezőhöz
        key: Egyedi azonosító a komponenshez
        show_search: Mutasson-e keresőmezőt
        show_select_all: Mutassa-e az összes kijelölése/törlése gombokat
        height: A lista magassága pixelben (None = alapértelmezett)
        
    Returns:
        List[Any]: A kiválasztott értékek listája
    """
    # Egyedi azonosító
    component_id = key or f"multi_select_{str(uuid.uuid4())[:8]}"
    
    # Session state kulcs
    selected_key = f"{component_id}_selected"
    
    # Ha nincs megadva label_key, használjuk a value_key-t
    if label_key is None:
        label_key = value_key
    
    # Alapértelmezett kiválasztások inicializálása
    if selected_key not in st.session_state:
        st.session_state[selected_key] = default_selected or []
    
    # A komponens HTML struktúrája
    st.markdown(f"""
    <div class="multi-select-container">
        <div class="multi-select-header">
            <div class="multi-select-title">{title}</div>
            
            {f'''
            <div class="multi-select-actions">
                <div class="multi-select-action" id="select_all_{component_id}">Mind kijelölése</div>
                <div class="multi-select-action" id="clear_all_{component_id}">Törlés</div>
            </div>
            ''' if show_select_all else ''}
        </div>
        
        {f'<input type="text" class="multi-select-search" placeholder="{placeholder}" id="search_{component_id}">' if show_search else ''}
        
        <div class="multi-select-items" {f'style="max-height: {height}px"' if height else ''}>
    """, unsafe_allow_html=True)
    
    # Egyéni magasságú lista esetén scrollbar stílus
    if height:
        st.markdown(f"""
        <style>
        .multi-select-items {{
            max-height: {height}px;
            overflow-y: auto;
        }}
        </style>
        """, unsafe_allow_html=True)
    
    # A checkbox elemek generálása és megjelenítése
    for i, option in enumerate(options):
        value = option[value_key]
        label = option[label_key]
        option_id = f"{component_id}_opt_{i}"
        is_checked = value in st.session_state[selected_key]
        
        st.markdown(f"""
        <div class="multi-select-item" data-value="{value}" data-label="{label}">
            <input type="checkbox" id="{option_id}" class="multi-select-checkbox" 
                   {' checked' if is_checked else ''}>
            <label for="{option_id}" class="multi-select-label">{label}</label>
        </div>
        """, unsafe_allow_html=True)
    
    # Lezárjuk a multi-select konténert
    st.markdown("</div></div>", unsafe_allow_html=True)
    
    # JavaScript a kereséshez és kiválasztáshoz
    js = f"""
    <script>
    document.addEventListener('DOMContentLoaded', function() {{
        const container = document.querySelector('.multi-select-container');
        const items = container.querySelectorAll('.multi-select-item');
        const checkboxes = container.querySelectorAll('.multi-select-checkbox');
        
        // Keresőmező eseménykezelő
        const searchInput = document.getElementById('search_{component_id}');
        if (searchInput) {{
            searchInput.addEventListener('input', function() {{
                const searchText = this.value.toLowerCase();
                
                items.forEach(item => {{
                    const label = item.getAttribute('data-label').toLowerCase();
                    if (label.includes(searchText)) {{
                        item.style.display = '';
                    }} else {{
                        item.style.display = 'none';
                    }}
                }});
            }});
        }}
        
        // Mind kijelölése gomb
        const selectAllBtn = document.getElementById('select_all_{component_id}');
        if (selectAllBtn) {{
            selectAllBtn.addEventListener('click', function() {{
                checkboxes.forEach(checkbox => {{
                    checkbox.checked = true;
                }});
                
                // Értesítjük Streamlit-et a változásról
                document.getElementById('multi_select_update_{component_id}').value = 'select_all';
                document.getElementById('multi_select_update_{component_id}').dispatchEvent(new Event('change'));
            }});
        }}
        
        // Törlés gomb
        const clearAllBtn = document.getElementById('clear_all_{component_id}');
        if (clearAllBtn) {{
            clearAllBtn.addEventListener('click', function() {{
                checkboxes.forEach(checkbox => {{
                    checkbox.checked = false;
                }});
                
                // Értesítjük Streamlit-et a változásról
                document.getElementById('multi_select_update_{component_id}').value = 'clear_all';
                document.getElementById('multi_select_update_{component_id}').dispatchEvent(new Event('change'));
            }});
        }}
        
        // Checkbox eseménykezelők
        checkboxes.forEach((checkbox, index) => {{
            checkbox.addEventListener('change', function() {{
                const itemValue = items[index].getAttribute('data-value');
                const isChecked = this.checked;
                
                // Értesítjük Streamlit-et a változásról
                document.getElementById('multi_select_update_{component_id}').value = 
                    JSON.stringify({{ value: itemValue, checked: isChecked }});
                document.getElementById('multi_select_update_{component_id}').dispatchEvent(new Event('change'));
            }});
        }});
    }});
    </script>
    """
    
    st.markdown(js, unsafe_allow_html=True)
    
    # Rejtett beviteli mező a JavaScript -> Streamlit kommunikációhoz
    update_value = st.text_input(
        "MultiSelect Update", 
        value="", 
        key=f"multi_select_update_{component_id}",
        label_visibility="collapsed"
    )
    
    # Frissítsük a kiválasztott értékeket a rejtett mező értéke alapján
    if update_value:
        if update_value == "select_all":
            # Minden érték kiválasztása
            st.session_state[selected_key] = [option[value_key] for option in options]
            st.rerun()
        elif update_value == "clear_all":
            # Minden kiválasztás törlése
            st.session_state[selected_key] = []
            st.rerun()
        else:
            try:
                # Egy checkbox változtatása
                update_data = json.loads(update_value)
                item_value = update_data.get("value")
                is_checked = update_data.get("checked", False)
                
                if is_checked and item_value not in st.session_state[selected_key]:
                    st.session_state[selected_key].append(item_value)
                elif not is_checked and item_value in st.session_state[selected_key]:
                    st.session_state[selected_key].remove(item_value)
                
                st.rerun()
            except Exception as e:
                logger.error(f"Hiba a multi-select frissítése során: {str(e)}")
    
    # Visszaadjuk a jelenleg kiválasztott értékeket
    return st.session_state[selected_key]


def render_quick_filter_chips(
    filters: List[Dict[str, Any]],
    active_index: Optional[int] = None,
    on_select: Optional[Callable[[Dict[str, Any], int], None]] = None,
    key: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Előre definiált gyorsszűrő chipek megjelenítése.
    
    Args:
        filters: Szűrő objektumok listája {label, icon, filters} formátumban
        active_index: Az aktív szűrő indexe (None, ha nincs aktív)
        on_select: Hívandó függvény szűrő kiválasztásakor (params: szűrő, index)
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Optional[Dict[str, Any]]: A kiválasztott szűrő vagy None, ha nincs aktív
    """
    # Egyedi azonosító
    component_id = key or f"quick_filters_{str(uuid.uuid4())[:8]}"
    
    # Session state kulcs
    active_key = f"{component_id}_active"
    
    # Inicializálás, ha szükséges
    if active_key not in st.session_state:
        st.session_state[active_key] = active_index
    
    # HTML a chip-ekhez
    st.markdown('<div class="quick-filters">', unsafe_allow_html=True)
    
    # Chip-ek generálása
    for i, filter_item in enumerate(filters):
        label = filter_item.get("label", f"Szűrő {i+1}")
        icon = filter_item.get("icon", "")
        is_active = st.session_state[active_key] == i
        
        st.markdown(f"""
        <div class="quick-filter-chip {'active' if is_active else ''}" 
             data-index="{i}" id="quick_filter_{component_id}_{i}">
            {f'<span class="quick-filter-icon">{icon}</span>' if icon else ''}
            {label}
        </div>
        """, unsafe_allow_html=True)
    
    # Lezárjuk a chip konténert
    st.markdown('</div>', unsafe_allow_html=True)
    
    # JavaScript a chip kiválasztáshoz
    js = f"""
    <script>
    document.addEventListener('DOMContentLoaded', function() {{
        const chips = document.querySelectorAll('[id^="quick_filter_{component_id}_"]');
        
        chips.forEach(chip => {{
            chip.addEventListener('click', function() {{
                const index = this.getAttribute('data-index');
                
                // Értesítjük Streamlit-et a változásról
                document.getElementById('quick_filter_selected_{component_id}').value = index;
                document.getElementById('quick_filter_selected_{component_id}').dispatchEvent(new Event('change'));
            }});
        }});
    }});
    </script>
    """
    
    st.markdown(js, unsafe_allow_html=True)
    
    # Rejtett beviteli mező a JavaScript -> Streamlit kommunikációhoz
    selected_index_str = st.text_input(
        "Quick Filter Selected", 
        value="", 
        key=f"quick_filter_selected_{component_id}",
        label_visibility="collapsed"
    )
    
    # Frissítsük a kiválasztott chip-et a rejtett mező értéke alapján
    if selected_index_str:
        try:
            selected_index = int(selected_index_str)
            
            # Kapcsoló logika: ha már aktív, akkor kikapcsoljuk
            if st.session_state[active_key] == selected_index:
                st.session_state[active_key] = None
            else:
                st.session_state[active_key] = selected_index
            
            # Hívjuk meg a callback-et, ha van
            if on_select and 0 <= selected_index < len(filters):
                on_select(filters[selected_index], selected_index)
            
            st.rerun()
        except (ValueError, IndexError) as e:
            logger.error(f"Hiba a gyorsszűrő kiválasztásakor: {str(e)}")
    
    # Visszaadjuk a kiválasztott szűrőt vagy None-t
    active_idx = st.session_state[active_key]
    if active_idx is not None and 0 <= active_idx < len(filters):
        return filters[active_idx]
    return None


def render_product_autocomplete(
    products: List[Dict[str, Any]],
    value_key: str = "id",
    label_key: str = "name",
    placeholder: str = "Termék keresése...",
    default_value: Optional[Any] = None,
    key: Optional[str] = None
) -> Optional[Any]:
    """
    Termék típus kereső autocomplete komponens.
    
    Args:
        products: Termék objektumok listája
        value_key: Dict kulcs az érték mezőhöz a products listában
        label_key: Dict kulcs a megjelenítendő névhez
        placeholder: Placeholder szöveg
        default_value: Alapértelmezett kiválasztott érték
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Optional[Any]: A kiválasztott termék értéke vagy None, ha nincs kiválasztva
    """
    # Egyedi azonosító
    component_id = key or f"autocomplete_{str(uuid.uuid4())[:8]}"
    
    # Session state kulcsok
    value_key_state = f"{component_id}_value"
    search_key_state = f"{component_id}_search"
    
    # Inicializálás, ha szükséges
    if value_key_state not in st.session_state:
        st.session_state[value_key_state] = default_value
    
    if search_key_state not in st.session_state:
        selected_product = next((p for p in products if p[value_key] == default_value), None)
        st.session_state[search_key_state] = selected_product[label_key] if selected_product else ""
    
    # HTML az autocomplete-hez
    st.markdown(f"""
    <div class="autocomplete-container" id="autocomplete_{component_id}">
        <input type="text" class="autocomplete-input" placeholder="{placeholder}"
               value="{st.session_state[search_key_state]}" id="autocomplete_input_{component_id}">
        <div class="autocomplete-results" id="autocomplete_results_{component_id}" style="display: none;">
            <!-- Az eredmények ide kerülnek -->
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # JavaScript az autocomplete működéséhez
    js = f"""
    <script>
    document.addEventListener('DOMContentLoaded', function() {{
        const input = document.getElementById('autocomplete_input_{component_id}');
        const resultsContainer = document.getElementById('autocomplete_results_{component_id}');
        const products = {json.dumps([{value_key: p[value_key], label_key: p[label_key]} for p in products])};
        let selectedIndex = -1;
        
        // Keresési eredmények szűrése és megjelenítése
        function showResults(query) {{
            query = query.toLowerCase();
            
            // Ha üres a keresőmező, elrejtjük az eredményeket
            if (!query) {{
                resultsContainer.style.display = 'none';
                return;
            }}
            
            // Eredmények szűrése
            const filteredProducts = products.filter(product => 
                product.{label_key}.toLowerCase().includes(query)
            ).slice(0, 10);  // Maximum 10 találat
            
            // Eredmények megjelenítése
            if (filteredProducts.length > 0) {{
                let resultsHtml = '';
                
                filteredProducts.forEach((product, index) => {{
                    const label = product.{label_key};
                    const value = product.{value_key};
                    
                    // A keresett szöveg kiemelése
                    const highlightedLabel = label.replace(
                        new RegExp(query, 'gi'),
                        match => `<span class="autocomplete-highlight">${{match}}</span>`
                    );
                    
                    resultsHtml += `
                        <div class="autocomplete-item" data-value="${{value}}" data-label="${{label}}"
                             data-index="${{index}}">
                            ${{highlightedLabel}}
                        </div>
                    `;
                }});
                
                resultsContainer.innerHTML = resultsHtml;
                resultsContainer.style.display = 'block';
                
                // Eseménykezelők hozzáadása az elemekhez
                const items = resultsContainer.querySelectorAll('.autocomplete-item');
                items.forEach(item => {{
                    item.addEventListener('click', function() {{
                        const value = this.getAttribute('data-value');
                        const label = this.getAttribute('data-label');
                        
                        // Beállítjuk a kiválasztott értéket
                        input.value = label;
                        resultsContainer.style.display = 'none';
                        
                        // Értesítjük Streamlit-et a változásról
                        document.getElementById('autocomplete_selected_{component_id}').value = 
                            JSON.stringify({{ value: value, label: label }});
                        document.getElementById('autocomplete_selected_{component_id}').dispatchEvent(new Event('change'));
                    }});
                    
                    item.addEventListener('mouseover', function() {{
                        selectedIndex = parseInt(this.getAttribute('data-index'));
                        highlightSelectedItem();
                    }});
                }});
            }} else {{
                resultsContainer.innerHTML = '<div class="autocomplete-item">Nincs találat</div>';
                resultsContainer.style.display = 'block';
                selectedIndex = -1;
            }}
        }}
        
        // Kijelölt elem kiemelése
        function highlightSelectedItem() {{
            const items = resultsContainer.querySelectorAll('.autocomplete-item');
            
            items.forEach((item, index) => {{
                if (index === selectedIndex) {{
                    item.style.backgroundColor = 'rgba(53, 132, 228, 0.1)';
                }} else {{
                    item.style.backgroundColor = '';
                }}
            }});
        }}
        
        // Input eseménykezelők
        input.addEventListener('input', function() {{
            const query = this.value;
            showResults(query);
            
            // Frissítjük a keresőszöveget Streamlit-ben
            document.getElementById('autocomplete_search_{component_id}').value = query;
            document.getElementById('autocomplete_search_{component_id}').dispatchEvent(new Event('change'));
        }});
        
        input.addEventListener('focus', function() {{
            // Ha már van keresőszöveg, újra megjelenítjük az eredményeket
            if (this.value) {{
                showResults(this.value);
            }}
        }});
        
        // Billentyűkezelés (le/fel nyilak, enter, escape)
        input.addEventListener('keydown', function(e) {{
            const items = resultsContainer.querySelectorAll('.autocomplete-item');
            
            if (e.key === 'ArrowDown') {{
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                highlightSelectedItem();
            }} else if (e.key === 'ArrowUp') {{
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                highlightSelectedItem();
            }} else if (e.key === 'Enter' && selectedIndex >= 0) {{
                e.preventDefault();
                items[selectedIndex].click();
            }} else if (e.key === 'Escape') {{
                resultsContainer.style.display = 'none';
                selectedIndex = -1;
            }}
        }});
        
        // Kattintás kezelése az autocomplete-en kívül
        document.addEventListener('click', function(e) {{
            if (!input.contains(e.target) && !resultsContainer.contains(e.target)) {{
                resultsContainer.style.display = 'none';
            }}
        }});
    }});
    </script>
    """
    
    st.markdown(js, unsafe_allow_html=True)
    
    # Rejtett beviteli mezők a JavaScript -> Streamlit kommunikációhoz
    selected_json = st.text_input(
        "Autocomplete Selected", 
        value="", 
        key=f"autocomplete_selected_{component_id}",
        label_visibility="collapsed"
    )
    
    search_text = st.text_input(
        "Autocomplete Search", 
        value=st.session_state[search_key_state], 
        key=f"autocomplete_search_{component_id}",
        label_visibility="collapsed"
    )
    
    # Keresőszöveg frissítése session state-ben
    if search_text != st.session_state[search_key_state]:
        st.session_state[search_key_state] = search_text
    
    # Kiválasztott érték frissítése a JSON-ból
    if selected_json:
        try:
            selected_data = json.loads(selected_json)
            selected_value = selected_data.get("value")
            selected_label = selected_data.get("label")
            
            # Frissítjük a session state-t
            st.session_state[value_key_state] = selected_value
            st.session_state[search_key_state] = selected_label
            
            st.rerun()
        except Exception as e:
            logger.error(f"Hiba az autocomplete kiválasztásakor: {str(e)}")
    
    # Visszaadjuk a kiválasztott értéket
    return st.session_state[value_key_state]


def render_range_slider(
    min_value: Union[int, float],
    max_value: Union[int, float],
    default_values: Optional[Tuple[Union[int, float], Union[int, float]]] = None,
    label: str = "Tartomány:",
    format_func: Optional[Callable[[Union[int, float]], str]] = None,
    key: Optional[str] = None,
    step: Optional[Union[int, float]] = None
) -> Tuple[Union[int, float], Union[int, float]]:
    """
    Tartomány csúszka két fogantyúval.
    
    Args:
        min_value: Minimum érték
        max_value: Maximum érték
        default_values: Alapértelmezett (min, max) értékek
        label: Címke szöveg
        format_func: Függvény az értékek formázásához
        key: Egyedi azonosító a komponenshez
        step: Lépésköz (None = folytonos)
        
    Returns:
        Tuple[Union[int, float], Union[int, float]]: A kiválasztott (min, max) értékek
    """
    # Alapértelmezett értékek
    if default_values is None:
        default_values = (min_value, max_value)
    
    # Session state kulcsok
    component_id = key or f"range_slider_{str(uuid.uuid4())[:8]}"
    range_key = f"{component_id}_values"
    
    # Inicializálás, ha szükséges
    if range_key not in st.session_state:
        st.session_state[range_key] = default_values
    
    # Érték formázó függvény
    if format_func is None:
        if isinstance(min_value, int) and isinstance(max_value, int):
            format_func = lambda x: f"{int(x):,}".replace(",", " ")
        else:
            format_func = lambda x: f"{x:.1f}".replace(".", ",")
    
    # Az aktuális értékek
    current_values = st.session_state[range_key]
    
    # A csúszka létrehozása
    st.markdown(f"""
    <div class="range-slider-container">
        <div class="range-slider-header">
            <div class="range-slider-title">{label}</div>
            <div class="range-slider-values">
                {format_func(current_values[0])} - {format_func(current_values[1])}
            </div>
        </div>
    """, unsafe_allow_html=True)
    
    # Streamlit slider használata
    values = st.slider(
        label,
        min_value=min_value,
        max_value=max_value,
        value=current_values,
        step=step,
        key=component_id,
        label_visibility="collapsed"
    )
    
    # Frissítsük a session state-t
    if values != st.session_state[range_key]:
        st.session_state[range_key] = values
    
    # Lezárjuk a range slider konténert
    st.markdown("</div>", unsafe_allow_html=True)
    
    return values


def render_full_status_filter(
    default_selected: Optional[List[str]] = None,
    key: Optional[str] = None
) -> List[str]:
    """
    Teljes státusz szűrő multiple-select checkbox-okkal.
    
    Args:
        default_selected: Alapértelmezetten kiválasztott státuszok
        key: Egyedi azonosító a komponenshez
        
    Returns:
        List[str]: A kiválasztott státuszok listája
    """
    # Státusz opciók és címkék
    status_options = [
        {"id": "CREATED", "name": "Létrehozva", "group": "Kezdeti státuszok"},
        {"id": "CONFIRMED_BY_COMPANY", "name": "Megerősítve cég által", "group": "Kezdeti státuszok"},
        {"id": "ACCEPTED_BY_USER", "name": "Elfogadva felhasználó által", "group": "Végső státuszok"},
        {"id": "REJECTED_BY_USER", "name": "Elutasítva felhasználó által", "group": "Végső státuszok"},
        {"id": "FINALIZED", "name": "Véglegesítve", "group": "Végső státuszok"}
    ]
    
    # Multiple select használata a státusz szűréshez
    selected_statuses = render_multiple_select(
        options=status_options,
        value_key="id",
        label_key="name",
        default_selected=default_selected,
        title="Státusz szűrés",
        placeholder="Státusz keresése...",
        key=key or "status_filter",
        show_search=True,
        show_select_all=True
    )
    
    return selected_statuses


def render_quick_date_filters(
    on_select: Optional[Callable[[Dict[str, Any], int], None]] = None,
    key: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Gyors dátumszűrő opciók megjelenítése.
    
    Args:
        on_select: Hívandó függvény szűrő kiválasztásakor (params: szűrő, index)
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Optional[Dict[str, Any]]: A kiválasztott szűrő vagy None, ha nincs aktív
    """
    today = datetime.now().date()
    
    # Előre definiált dátumszűrők
    date_filters = [
        {
            "label": "Ma",
            "icon": "📅",
            "filters": {
                "from_date": today,
                "to_date": today
            }
        },
        {
            "label": "Tegnap",
            "icon": "📅",
            "filters": {
                "from_date": today - timedelta(days=1),
                "to_date": today - timedelta(days=1)
            }
        },
        {
            "label": "Előző 7 nap",
            "icon": "📅",
            "filters": {
                "from_date": today - timedelta(days=7),
                "to_date": today
            }
        },
        {
            "label": "Előző 30 nap",
            "icon": "📅",
            "filters": {
                "from_date": today - timedelta(days=30),
                "to_date": today
            }
        },
        {
            "label": "Jövő hét",
            "icon": "📅",
            "filters": {
                "from_date": today,
                "to_date": today + timedelta(days=7)
            }
        }
    ]
    
    # A quick filter chips megjelenítése
    selected_filter = render_quick_filter_chips(
        filters=date_filters,
        active_index=None,
        on_select=on_select,
        key=key or "quick_date_filters"
    )
    
    return selected_filter


def render_quick_status_filters(
    on_select: Optional[Callable[[Dict[str, Any], int], None]] = None,
    key: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Gyors státusz szűrő opciók megjelenítése.
    
    Args:
        on_select: Hívandó függvény szűrő kiválasztásakor (params: szűrő, index)
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Optional[Dict[str, Any]]: A kiválasztott szűrő vagy None, ha nincs aktív
    """
    # Előre definiált státusz szűrők
    status_filters = [
        {
            "label": "Új ajánlatok",
            "icon": "🆕",
            "filters": {
                "statuses": ["CREATED"]
            }
        },
        {
            "label": "Megerősített",
            "icon": "✅",
            "filters": {
                "statuses": ["CONFIRMED_BY_COMPANY"]
            }
        },
        {
            "label": "Elfogadott",
            "icon": "👍",
            "filters": {
                "statuses": ["ACCEPTED_BY_USER"]
            }
        },
        {
            "label": "Elutasított",
            "icon": "👎",
            "filters": {
                "statuses": ["REJECTED_BY_USER"]
            }
        },
        {
            "label": "Véglegesített",
            "icon": "🏁",
            "filters": {
                "statuses": ["FINALIZED"]
            }
        },
        {
            "label": "Folyamatban",
            "icon": "⏳",
            "filters": {
                "statuses": ["CREATED", "CONFIRMED_BY_COMPANY"]
            }
        },
        {
            "label": "Lezártak",
            "icon": "🔒",
            "filters": {
                "statuses": ["ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            }
        }
    ]
    
    # A quick filter chips megjelenítése
    selected_filter = render_quick_filter_chips(
        filters=status_filters,
        active_index=None,
        on_select=on_select,
        key=key or "quick_status_filters"
    )
    
    return selected_filter


def render_complete_advanced_filter_form(
    products: Optional[List[Dict[str, Any]]] = None,
    on_filter: Optional[Callable[[Dict[str, Any]], None]] = None,
    on_reset: Optional[Callable[[], None]] = None,
    default_values: Optional[Dict[str, Any]] = None,
    key: Optional[str] = None
) -> Dict[str, Any]:
    """
    Teljes fejlett szűrési űrlap renderelése az összes összetevővel.
    
    Args:
        products: Termékek listája az autocomplete-hez (ha None, nem jelenik meg)
        on_filter: Hívandó függvény a keresés gombra kattintáskor (param: szűrő adatok)
        on_reset: Hívandó függvény a reset gombra kattintáskor
        default_values: Alapértelmezett értékek
        key: Egyedi azonosító a komponenshez
        
    Returns:
        Dict[str, Any]: Az összes aktuális szűrőbeállítás
    """
    # Stílusok injektálása
    inject_advanced_filter_styles()
    
    # Egyedi azonosító
    component_id = key or f"advanced_filter_{str(uuid.uuid4())[:8]}"
    
    # Alapértelmezett értékek inicializálása
    if default_values is None:
        default_values = {}
    
    # Session state kulcsok az űrlap értékekhez
    form_key = f"{component_id}_form"
    
    # Ma és alapértelmezett dátumok
    today = datetime.now().date()
    start_date = default_values.get("from_date", today - timedelta(days=30))
    end_date = default_values.get("to_date", today)
    
    # Szűrő adatok inicializálása
    filter_data = {
        "statuses": default_values.get("statuses", []),
        "from_date": start_date,
        "to_date": end_date,
        "product_id": default_values.get("product_id", None),
        "min_quantity": default_values.get("min_quantity", None),
        "max_quantity": default_values.get("max_quantity", None),
        "producer_id": default_values.get("producer_id", None)
    }
    
    # Gyorsszűrő panel
    st.markdown("### Gyorsszűrők")
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Dátum szerinti gyorsszűrés:**")
        selected_date_filter = render_quick_date_filters(
            key=f"{component_id}_date_quick"
        )
        
        # Dátum gyorsszűrő alkalmazása, ha van kiválasztva
        if selected_date_filter:
            filter_data["from_date"] = selected_date_filter["filters"].get("from_date", filter_data["from_date"])
            filter_data["to_date"] = selected_date_filter["filters"].get("to_date", filter_data["to_date"])
    
    with col2:
        st.markdown("**Státusz szerinti gyorsszűrés:**")
        selected_status_filter = render_quick_status_filters(
            key=f"{component_id}_status_quick"
        )
        
        # Státusz gyorsszűrő alkalmazása, ha van kiválasztva
        if selected_status_filter:
            filter_data["statuses"] = selected_status_filter["filters"].get("statuses", filter_data["statuses"])
    
    # Részletes szűrők header
    st.markdown("---")
    st.markdown("### Részletes szűrők")
    
    # Státusz szűrő
    st.markdown("#### Státusz szűrők")
    selected_statuses = render_full_status_filter(
        default_selected=filter_data["statuses"],
        key=f"{component_id}_status"
    )
    filter_data["statuses"] = selected_statuses
    
    # Dátum szűrő
    st.markdown("#### Dátum szűrő")
    min_date = today - timedelta(days=365)  # 1 évvel ezelőtt
    max_date = today + timedelta(days=365)   # 1 évvel később
    
    start_date, end_date = render_date_range_slider(
        min_date=datetime.combine(min_date, datetime.min.time()),
        max_date=datetime.combine(max_date, datetime.min.time()),
        start_date=datetime.combine(filter_data["from_date"], datetime.min.time()),
        end_date=datetime.combine(filter_data["to_date"], datetime.min.time()),
        key=f"{component_id}_date_range"
    )
    
    filter_data["from_date"] = start_date.date()
    filter_data["to_date"] = end_date.date()
    
    # Termék és mennyiség szűrők
    st.markdown("#### Termék és mennyiség szűrők")
    col1, col2 = st.columns(2)
    
    with col1:
        # Termék autocomplete, ha van termék lista
        if products:
            product_id = render_product_autocomplete(
                products=products,
                value_key="id",
                label_key="name",
                placeholder="Termék keresése...",
                default_value=filter_data["product_id"],
                key=f"{component_id}_product"
            )
            filter_data["product_id"] = product_id
        else:
            # Ha nincs termék lista, egyszerű beviteli mezőt használunk
            product_id = st.text_input(
                "Termék azonosító:",
                value=filter_data["product_id"] or "",
                key=f"{component_id}_product_simple"
            )
            filter_data["product_id"] = product_id if product_id else None
    
    with col2:
        # Termelő azonosító
        producer_id = st.text_input(
            "Termelő azonosító:",
            value=filter_data["producer_id"] or "",
            key=f"{component_id}_producer"
        )
        filter_data["producer_id"] = producer_id if producer_id else None
    
    # Mennyiség szűrő
    quantity_range = render_range_slider(
        min_value=0,
        max_value=10000,
        default_values=(
            filter_data["min_quantity"] or 0,
            filter_data["max_quantity"] or 10000
        ),
        label="Mennyiség (kg):",
        format_func=lambda x: f"{int(x):,} kg".replace(",", " "),
        key=f"{component_id}_quantity",
        step=10
    )
    
    filter_data["min_quantity"] = quantity_range[0] if quantity_range[0] > 0 else None
    filter_data["max_quantity"] = quantity_range[1] if quantity_range[1] < 10000 else None
    
    # Gombok
    st.markdown("---")
    col1, col2 = st.columns(2)
    
    with col1:
        search_clicked = st.button(
            "🔍 Keresés", 
            key=f"{component_id}_search",
            use_container_width=True,
            type="primary"
        )
        if search_clicked and on_filter:
            on_filter(filter_data)
    
    with col2:
        reset_clicked = st.button(
            "🔄 Alaphelyzet", 
            key=f"{component_id}_reset",
            use_container_width=True
        )
        if reset_clicked and on_reset:
            on_reset()
    
    return filter_data


# Példa használat, ha ezt a modult közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(page_title="Fejlett Szűrési Komponensek", layout="wide")
    
    st.title("Fejlett Szűrési Komponensek Demó")
    
    # Stílusok betöltése
    inject_advanced_filter_styles()
    
    # Példa termékek az autocomplete-hez
    sample_products = [
        {"id": 1, "name": "Alma", "category": "Gyümölcs"},
        {"id": 2, "name": "Körte", "category": "Gyümölcs"},
        {"id": 3, "name": "Banán", "category": "Gyümölcs"},
        {"id": 4, "name": "Búza", "category": "Gabona"},
        {"id": 5, "name": "Kukorica", "category": "Gabona"},
        {"id": 6, "name": "Árpa", "category": "Gabona"},
        {"id": 7, "name": "Dinnye", "category": "Gyümölcs"},
        {"id": 8, "name": "Szőlő", "category": "Gyümölcs"},
        {"id": 9, "name": "Burgonya", "category": "Zöldség"},
        {"id": 10, "name": "Paradicsom", "category": "Zöldség"},
    ]
    
    # Demo komponensek tabs-ban
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "1. Dátum csúszka", 
        "2. Multiple select", 
        "3. Gyorsszűrők", 
        "4. Autocomplete", 
        "5. Teljes szűrőpanel"
    ])
    
    with tab1:
        st.header("Csúszkás dátumtartomány szűrő")
        
        st.markdown("""
        Ez a komponens egy interaktív csúszkát biztosít, amivel vizuálisan lehet kiválasztani egy dátumtartományt.
        
        * Két fogantyú: kezdő és végső dátum kiválasztásához
        * Vizuális indikátor a kiválasztott tartományra
        * Tooltipek a pontos dátumok megjelenítésére
        * Dinamikus CSS animációk
        """)
        
        # Dátumok a demonstrációhoz
        today = datetime.now().date()
        min_date = today - timedelta(days=365)  # 1 évvel ezelőtt
        max_date = today + timedelta(days=365)   # 1 évvel később
        start_date = today - timedelta(days=30)
        end_date = today
        
        # Komponens renderelése
        selected_start, selected_end = render_date_range_slider(
            min_date=datetime.combine(min_date, datetime.min.time()),
            max_date=datetime.combine(max_date, datetime.min.time()),
            start_date=datetime.combine(start_date, datetime.min.time()),
            end_date=datetime.combine(end_date, datetime.min.time()),
            key="demo_date_slider"
        )
        
        # Eredmény megjelenítése
        st.success(f"Kiválasztott tartomány: {selected_start.date()} - {selected_end.date()}")
    
    with tab2:
        st.header("Multiple-select státusz szűrő")
        
        st.markdown("""
        Ez a komponens egy fejlett, kereshető multiple-select komponens, amellyel több opciót lehet kiválasztani.
        
        * Keresőmező a gyors szűréshez
        * "Mind kijelölése" és "Törlés" gombok
        * Checkbox-ok a kiválasztáshoz
        * Vizuális visszajelzés a kiválasztott elemekről
        """)
        
        # Komponens renderelése
        selected_statuses = render_full_status_filter(key="demo_status_filter")
        
        # Eredmény megjelenítése
        if selected_statuses:
            st.success(f"Kiválasztott státuszok: {', '.join(selected_statuses)}")
        else:
            st.info("Nincs kiválasztott státusz")
    
    with tab3:
        st.header("Gyorsszűrő chipek")
        
        st.markdown("""
        Előre definiált gyorsszűrő chipek, amelyekkel könnyen lehet tipikus szűréseket alkalmazni.
        
        * Vizuális visszajelzés az aktív szűrőről
        * Ikonok a jobb áttekinthetőségért
        * Kapcsoló működés: újra kattintás kikapcsolja a szűrőt
        """)
        
        # Dátum gyorsszűrők
        st.subheader("Dátum gyorsszűrők")
        selected_date_filter = render_quick_date_filters(key="demo_date_quick")
        
        if selected_date_filter:
            st.success(f"""
            Kiválasztott dátum szűrő: 
            {selected_date_filter['label']} 
            ({selected_date_filter['filters']['from_date']} - {selected_date_filter['filters']['to_date']})
            """)
        else:
            st.info("Nincs kiválasztott dátum szűrő")
        
        # Státusz gyorsszűrők
        st.subheader("Státusz gyorsszűrők")
        selected_status_filter = render_quick_status_filters(key="demo_status_quick")
        
        if selected_status_filter:
            st.success(f"""
            Kiválasztott státusz szűrő: 
            {selected_status_filter['label']} 
            ({', '.join(selected_status_filter['filters']['statuses'])})
            """)
        else:
            st.info("Nincs kiválasztott státusz szűrő")
    
    with tab4:
        st.header("Termék típus autocomplete")
        
        st.markdown("""
        Ez a komponens egy autocomplete keresőmezőt biztosít a termék típusok kiválasztásához.
        
        * Valós idejű szűrés gépelés közben
        * A keresett szöveg kiemelése a találatokban
        * Billentyűzet kezelés (le/fel nyilak, enter, escape)
        * Egér és touch használat támogatása
        """)
        
        # Komponens renderelése
        selected_product = render_product_autocomplete(
            products=sample_products,
            value_key="id",
            label_key="name",
            placeholder="Termék keresése...",
            key="demo_product"
        )
        
        # Eredmény megjelenítése
        if selected_product:
            selected_name = next((p["name"] for p in sample_products if p["id"] == selected_product), None)
            st.success(f"Kiválasztott termék: {selected_name} (ID: {selected_product})")
        else:
            st.info("Nincs kiválasztott termék")
    
    with tab5:
        st.header("Teljes fejlett szűrőpanel")
        
        st.markdown("""
        Ez a panel az összes fejlett szűrési komponenst együtt tartalmazza egy teljes szűrési élményhez.
        
        Fő összetevők:
        * Gyorsszűrő chipek a gyakori szűrésekhez
        * Státusz multiple-select szűrő
        * Dátumtartomány csúszka
        * Termék autocomplete
        * Mennyiség tartomány szűrő
        """)
        
        # A teljes szűrőpanel renderelése
        def handle_filter(data):
            st.success(f"Keresés a következő szűrőkkel: {data}")
        
        def handle_reset():
            st.info("Szűrők alaphelyzetbe állítva")
            st.rerun()
        
        filter_data = render_complete_advanced_filter_form(
            products=sample_products,
            on_filter=handle_filter,
            on_reset=handle_reset,
            key="demo_complete"
        )
        
        # Aktuális szűrési beállítások megjelenítése
        with st.expander("Aktuális szűrési beállítások"):
            st.json(filter_data)
    
    # Fejlesztői információk
    with st.expander("Fejlesztői információk"):
        st.markdown("""
        **Modul:** advanced_filters.py
        
        **Fő függvények:**
        - `render_date_range_slider`: Csúszkás dátumtartomány szűrő
        - `render_multiple_select`: Fejlett, kereshető multiple-select
        - `render_quick_filter_chips`: Előre definiált gyorsszűrők
        - `render_product_autocomplete`: Termék típus autocomplete
        - `render_range_slider`: Egyéb tartomány szűrők
        - `render_full_status_filter`: Teljes státusz szűrő (multiple-select alapján)
        - `render_complete_advanced_filter_form`: Az összes komponens egy teljes űrlapon
        
        **Technológiai megoldások:**
        - Egyedi CSS és JavaScript használata a komponensekhez
        - Session state használata az állapot követéséhez
        - Streamlit rejtett mezők a JS-Streamlit kommunikációhoz
        - Hibakezelés és fallback logika minden komponensben
        """)