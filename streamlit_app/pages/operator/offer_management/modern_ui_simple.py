"""
Egyszerűsített modern UI komponensek valós adatokkal
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta, date
import logging
import sys
import os
import json
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def safe_json_display(data, title="JSON Data"):
    """Biztonságos JSON megjelenítés Streamlit-ben"""
    
    def json_serializer(obj):
        """Custom JSON serializer date/datetime objektumokhoz"""
        if isinstance(obj, (datetime, date)):
            return obj.strftime('%Y-%m-%d')
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    try:
        # Streamlit kompatibilis formátum készítése
        display_data = {}
        for key, value in data.items():
            if hasattr(value, 'strftime'):  # Date/datetime objektum
                display_data[key] = value.strftime('%Y-%m-%d')
            elif isinstance(value, list):
                display_data[key] = value
            elif value is None:
                display_data[key] = None
            else:
                display_data[key] = value
        
        # Próbáljuk meg natív st.json()-nal
        st.json(display_data)
    except Exception:
        try:
            # Ha nem sikerül, akkor JSON string-ként
            json_str = json.dumps(data, default=json_serializer, indent=2, ensure_ascii=False)
            st.code(json_str, language='json')
        except Exception as e:
            # Végső fallback
            st.text(f"{title}: {data}")
            st.error(f"JSON megjelenítés hiba: {e}")

# Flexible config imports
try:
    # Try to import directly first
    import app_config as config
    logger.info("Successfully imported config module directly")
except ImportError:
    try:
        # Try Docker path
        from streamlit_app import app_config as config
        logger.info("Successfully imported config module from streamlit_app path")
    except ImportError:
        try:
            # Try relative path
            from ... import app_config as config
            logger.info("Successfully imported config module from relative path")
        except ImportError:
            logger.error("Failed to import config module. API Base URL won't be displayed.")
            # Create minimal placeholder config to prevent complete failure
            class DummyConfig:
                def __init__(self):
                    self.API_BASE_URL = "N/A (Config not found)"
                    self.DEBUG_MODE = False
                    self.APP_NAME = "POM APP"
                    self.COMPANY_NAME = "Zöldség Világ Kft."
                def __getattr__(self, name):
                    return f"N/A (Config '{name}' not found)"
            config = DummyConfig()

def render_debug_info():
    """Debug információk megjelenítése"""
    if st.session_state.get("debug_mode", False):
        st.sidebar.markdown("## ⚙️ Debug Információ")
        st.sidebar.write(f"API Base URL: {getattr(config, 'API_BASE_URL', 'N/A')}")
        
        # Termelők teszt
        st.sidebar.markdown("### Termelők teszt:")
        from .api_client import get_producers
        success, producers = get_producers()
        st.sidebar.write(f"Siker: {success}")
        st.sidebar.write(f"Eredmény: {producers[:2] if success and len(producers) > 2 else producers}")
        
        # Ajánlatok teszt
        st.sidebar.markdown("### Ajánlatok teszt:")
        now = datetime.now()
        st.sidebar.write(f"🔍 get_offers() hívás: {now.strftime('%H:%M:%S.%f')[:-3]}")
        
        from .api_client import get_offers
        st.sidebar.write(f"Bemenő paraméterek: {None}")
        success, offers = get_offers()
        
        if success:
            st.sidebar.write(f"API válasz mérete: {len(offers)}")
            st.sidebar.write(f"Siker: {success}")
            st.sidebar.write(f"Ajánlatok száma: {len(offers)}")
        else:
            st.sidebar.write(f"Hiba: {offers}")
            
        # Session state monitor
        st.sidebar.markdown("### 🔧 Session State Monitor")
        st.sidebar.write("Összes session state kulcs:")
        for key in st.session_state:
            st.sidebar.write(f"{key}: {type(st.session_state[key])}")

def inject_modern_styles():
    """Modern CSS stílusok a ui.md alapján"""
    st.markdown("""
    <style>
    /* Modern szűrőpanel */
    .modern-filter-panel {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
        margin: 1rem 0;
        overflow: hidden;
        animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .filter-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    /* Aktív szűrő címkék */
    .active-filter-badge {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        color: #1976d2;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        margin: 0.25rem 0.25rem 0.25rem 0;
        display: inline-block;
    }
    
    /* Statisztika kártya (Sötét téma) */
    .stats-card {
        background: black; /* Marad fekete, vagy egy nagyon sötét szürke, pl. #121212 */
        border-radius: 12px;
        /* Az árnyék fekete alapon kevésbé látszik, lehet sötétebb vagy egy finom világosabb "izzás" */
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3); /* Kicsit erősebb sötét árnyék */
        /* A keret legyen sötétszürke, vagy egy nagyon halvány világos a fekete alapon */
        border: 1px solid #333333; /* Sötétszürke keret */
        margin: 1rem 0;
        overflow: hidden;
    }

    .stats-header {
        /* Ez a gradient valószínűleg jól mutat sötét témán is, a fehér szöveggel */
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
    }

    .stats-content {
        padding: 1.5rem;
        /* A tartalom háttere legyen sötét, de elkülönülhet a kártya feketéjétől */
        background: #1a1a1a; /* Nagyon sötét szürke */
        color: #e0e0e0; /* Világos szürke szöveg a jó olvashatóságért */
    }

    .stats-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem;
        /* A sorok háttere is sötét, elkülönülhet a .stats-content-től */
        background: #2c2c2c; /* Egy kicsit világosabb sötétszürke */
        border-radius: 8px;
        /* A soroknak nem feltétlenül kell saját árnyék sötét témában, vagy csak nagyon finom */
        box-shadow: none; /* Eltávolítjuk az előző árnyékot */
        /* Esetleg egy finom keret a soroknak, ha szükséges az elválasztás */
        /* border: 1px solid #3f3f3f; */
    }

    /* Általános szövegszín a .stats-row-ban, ha nincs specifikusabb */
    .stats-row {
        /* ... (fentiek) ... */
        color: #cccccc; /* Világos szürke szöveg */
    }


    .stats-value {
        font-size: 1.5rem;
        font-weight: 600;
        /* A kék értéknek világosabbnak/élénkebbnek kell lennie sötét alapon */
        color: #ffffff; /* Élénkebb, világos kék (GitHub link színe) */
        /* Alternatívák: color: #87ceeb; (égkék) vagy color: #ffffff; (fehér) */
    }
    
    /* Mobil kártyák */
    .mobile-offer-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
        padding: 1.25rem;
        margin: 1rem 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
    }
    
    .mobile-offer-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    @keyframes slideInFromTop {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    </style>
    """, unsafe_allow_html=True)

def render_page_header():
    """Modern oldal fejléc"""
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    ">
        <h1 style="margin: 0; font-size: 2.5rem; font-weight: 600;">🪙 Ajánlatok Kezelése</h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">Valós adatok modern felületen</p>
    </div>
    """, unsafe_allow_html=True)

def get_producers_from_api():
    """Termelők lekérése API-ból - OPTIMALIZÁLT verzió session state cache-sel"""
    
    # Ellenőrizzük a cache-t session state-ben
    cache_key = 'producers_cache'
    cache_timestamp_key = 'producers_cache_timestamp'
    cache_duration = 300  # 5 perc cache
    
    current_time = datetime.now()
    
    # Cache ellenőrzés
    if (cache_key in st.session_state and 
        cache_timestamp_key in st.session_state and
        (current_time - st.session_state[cache_timestamp_key]).total_seconds() < cache_duration):
        
        logger.debug("Using cached producers data")
        return st.session_state[cache_key]
    
    try:
        # Import a javított api_client-ből
        from .api_client import get_producers
        
        logger.info("Fetching fresh producers data from API")
        success, producers_data = get_producers()
        if success and producers_data:
            producers_list = [] # Változás: (név, id) tuple-öket tárolunk
            if isinstance(producers_data, list) and producers_data:
                for p_dict in producers_data:
                    if isinstance(p_dict, dict):
                        producer_id = p_dict.get('id') # Feltételezzük, hogy 'id' a kulcs
                        if not producer_id:
                            # Próbálkozás más lehetséges ID kulcsokkal, pl. 'user_id'
                            producer_id = p_dict.get('user_id') 

                        if producer_id: # Csak akkor adjuk hozzá, ha van ID
                            # Név kinyerése: contact_name, company_name, majd name
                            display_name = p_dict.get('contact_name') or \
                                           p_dict.get('company_name') or \
                                           p_dict.get('name') or \
                                           f"Producer ID: {producer_id}" # Fallback, ha nincs név
                            producers_list.append((str(display_name), producer_id))
            
            if producers_list:
                # Rendezés a megjelenítési név alapján, egyedi párosok biztosítása
                sorted_producers = sorted(list(set(producers_list)), key=lambda x: x[0])
                
                # Cache-be mentés
                st.session_state[cache_key] = sorted_producers
                st.session_state[cache_timestamp_key] = current_time
                logger.info(f"Cached {len(sorted_producers)} producers for {cache_duration} seconds")
                
                return sorted_producers
        
        # Fallback: üres lista - ezt is cache-eljük, hogy ne próbálkozzunk folyamatosan
        fallback_result = []
        st.session_state[cache_key] = fallback_result
        st.session_state[cache_timestamp_key] = current_time
        logger.warning("No producers found, cached empty result")
        return fallback_result
        
    except Exception as e:
        logger.error(f"Hiba a termelők betöltése során: {e}")
        # Hibás eredményt is cache-eljük rövid időre, hogy ne spammeljük az API-t
        fallback_result = []
        st.session_state[cache_key] = fallback_result
        st.session_state[cache_timestamp_key] = current_time
        st.error(f"Hiba a termelők betöltése során: {e}")
        return fallback_result

def get_products_from_api():
    """Termékek lekérése API-ból - JAVÍTOTT verzió product_types API-val"""
    
    # Ellenőrizzük a cache-t session state-ben
    cache_key = 'products_cache'
    cache_timestamp_key = 'products_cache_timestamp'
    cache_duration = 180  # 3 perc cache (rövidebb, mert gyakrabban változhatnak)
    
    current_time = datetime.now()
    
    # Cache ellenőrzés - CSAK a nevek listáját adjuk vissza a UI-nak
    if (cache_key in st.session_state and 
        cache_timestamp_key in st.session_state and
        (current_time - st.session_state[cache_timestamp_key]).total_seconds() < cache_duration):
        
        logger.debug("Using cached products data")
        cached_data = st.session_state[cache_key]
        
        # Ha objektumok vannak cache-elve, neveket adjunk vissza
        if cached_data and isinstance(cached_data[0], dict):
            return [product['name'] for product in cached_data if 'name' in product]
        else:
            return cached_data
    
    try:
        # JAVÍTÁS: Product Types API használata az ajánlatok helyett
        from api.products import get_product_types
        logger.info("Fetching fresh product types data from dedicated API")
        
        success, product_types = get_product_types()
        if success and product_types:
            # FONTOS: A teljes objektumokat cache-eljük, de csak neveket adunk vissza
            st.session_state[cache_key] = product_types  # Teljes objektumok cache-elése
            st.session_state[cache_timestamp_key] = current_time
            logger.info(f"Cached {len(product_types)} full product type objects for {cache_duration} seconds")
            
            # UI-nak csak a neveket adjuk vissza
            product_names = []
            for product_type in product_types:
                if isinstance(product_type, dict) and 'name' in product_type:
                    product_names.append(product_type['name'])
            
            return sorted([p for p in product_names if p])
        
        else:
            # Fallback: Próbáljuk az ajánlatokból (régi módszer)
            logger.warning("Product types API failed, falling back to offers API")
            from api.offers import get_offers
            success, offers = get_offers()
            if success:
                products = list(set(offer.get('product_name', '') for offer in offers if offer.get('product_name')))
                sorted_products = sorted([p for p in products if p])
                
                # Cache-be mentés (csak nevek, mivel nincs ID info)
                st.session_state[cache_key] = sorted_products
                st.session_state[cache_timestamp_key] = current_time
                logger.info(f"Cached {len(sorted_products)} product names from offers fallback for {cache_duration} seconds")
                
                return sorted_products
        
        # Ha semmi sem működött: üres lista
        fallback_result = []
        st.session_state[cache_key] = fallback_result
        st.session_state[cache_timestamp_key] = current_time
        logger.warning("No products found from any source, cached empty result")
        return fallback_result
        
    except Exception as e:
        logger.error(f"Hiba a termékek betöltése során: {e}")
        # Hibás eredményt is cache-eljük rövid időre
        fallback_result = []
        st.session_state[cache_key] = fallback_result
        st.session_state[cache_timestamp_key] = current_time
        return fallback_result
    
def get_product_type_id(product_name: str) -> Optional[int]:
    """Get product_type_id from product name using cache or API"""
    if not product_name or product_name == "Minden termék":
        return None
    
    try:
        # Check session state cache first
        if 'products_cache' in st.session_state:
            products = st.session_state['products_cache']
            
            # Debug log
            logger.debug(f"Products cache type: {type(products)}")
            if products:
                logger.debug(f"First product type: {type(products[0])}")
            
            # Handle different data structures
            if isinstance(products, list) and products:
                # Check if we have full objects (new format)
                if isinstance(products[0], dict):
                    for product in products:
                        if isinstance(product, dict):
                            if product.get('name', '').strip().lower() == product_name.strip().lower():
                                return product.get('id')
                elif isinstance(products[0], str):
                    # Ha csak string lista van, nincs ID - próbáljuk API-ból
                    logger.warning(f"Products cache contains strings, querying API for ID")
        
        # If not in cache or cache has no IDs, try API
        try:
            from api.products import get_product_types
            success, products = get_product_types()
            
            if success and isinstance(products, list):
                # Update cache with full objects
                st.session_state['products_cache'] = products
                st.session_state['products_cache_timestamp'] = datetime.now()
                
                # Find the product ID
                for product in products:
                    if isinstance(product, dict):
                        if product.get('name', '').strip().lower() == product_name.strip().lower():
                            logger.info(f"Found product_type_id {product.get('id')} for '{product_name}'")
                            return product.get('id')
                
                logger.warning(f"Product '{product_name}' not found in API response")
                return None
                
        except ImportError:
            logger.error(f"Could not import get_product_types")
            return None
            
    except Exception as e:
        logger.error(f"Error getting product_type_id for '{product_name}': {e}")
        return None
    
    logger.warning(f"Could not find product_type_id for: '{product_name}'")
    return None

def render_modern_filter_panel():
    """Modern szűrőpanel valós adatokkal"""
    
    # Header HTML
    st.markdown("""
    <div class="modern-filter-panel">
        <div class="filter-header">
            <span>🔍 Ajánlatok szűrése</span>
            <span>⚙️</span>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Szűrő form
    with st.container():
        col1, col2 = st.columns(2)
        
        with col1:
            # Termelő szűrő - valós adatokkal
            producers_with_ids = get_producers_from_api() # Lista (display_name, producer_id) tuple-ökből
            # Az opciók tartalmazzák a "Minden termelő" stringet és a (név, id) tuple-öket
            producer_options_for_selectbox = [("Minden termelő", None)] + producers_with_ids
            
            # 🚨 CRITICAL DEBUG: Log before widget render
            current_session_value = st.session_state.get("modern_producer_filter", "NOT_SET")
            logger.critical(f"🔍 Before selectbox - session state modern_producer_filter: {current_session_value}")
            
            selected_producer_tuple = st.selectbox(
                "Termelő:",
                options=producer_options_for_selectbox,
                format_func=lambda x: x[0],  # Csak a nevet (tuple első elemét) jelenítjük meg
                key="modern_producer_filter"
            )
            
            # 🚨 CRITICAL DEBUG: Log after widget render
            logger.critical(f"🔍 After selectbox - selected_producer_tuple: {selected_producer_tuple}")
            logger.critical(f"🔍 After selectbox - session state modern_producer_filter: {st.session_state.get('modern_producer_filter', 'NOT_SET')}")
            
            # selected_producer_tuple most (display_name, producer_id) vagy ("Minden termelő", None)
            selected_producer_display_name = selected_producer_tuple[0]
            selected_producer_id = selected_producer_tuple[1]

            # 🔧 JAVÍTÁS: Dátum szűrők - checkbox-szal vezérelt
            enable_date_filter = st.checkbox(
                "📅 Dátum szűrés engedélyezése", 
                value=False,
                key="enable_date_filter"
            )
            
            if enable_date_filter:
                from_date = st.date_input(
                    "Kezdő dátum:",
                    value=datetime.now().date() - timedelta(days=30),
                    key="modern_from_date"
                )
                # Csak akkor adjuk vissza, ha engedélyezve van
                actual_from_date = from_date
            else:
                actual_from_date = None
            
            # Termék szűrő - valós adatokkal
            products = get_products_from_api()
            product_options = ["Minden termék"] + products
            selected_product = st.selectbox(
                "Termék:",
                product_options,
                key="modern_product_filter"
            )
        
        with col2:
            # Státusz szűrő
            status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            status_labels = ["Létrehozva", "Megerősítve", "Elfogadva", "Elutasítva", "Véglegesítve"]
            
            # 🔧 JAVÍTÁS: Státusz szűrő - checkbox-szal vezérelt
            enable_status_filter = st.checkbox(
                "🏷️ Státusz szűrés engedélyezése",
                value=False,
                key="enable_status_filter"
            )
            
            if enable_status_filter:
                selected_statuses = st.multiselect(
                    "Státusz:",
                    options=status_options,
                    format_func=lambda x: dict(zip(status_options, status_labels)).get(x, x),
                    key="modern_status_filter"
                )
                actual_statuses = selected_statuses if selected_statuses else None
            else:
                actual_statuses = None
            
            # Végső dátum - csak ha a dátum szűrés engedélyezve van
            if enable_date_filter:
                to_date = st.date_input(
                    "Végső dátum:",
                    value=datetime.now().date(),
                    key="modern_to_date"
                )
                actual_to_date = to_date
            else:
                actual_to_date = None
            
            # Keresés
            search_term = st.text_input(
                "Keresés:",
                placeholder="Termelő vagy termék neve...",
                key="modern_search"
            )
        
        # Gombok
        col3, col4, col5 = st.columns([2, 1, 1])
        with col4:
            if st.button("🔄 Alaphelyzet", key="filter_reset_btn"):
                # 🚨 CRITICAL DEBUG: Log reset action
                logger.critical("🔄 RESET BUTTON CLICKED - Clearing filter session state")
                
                # Log current producer filter before reset
                current_producer = st.session_state.get("modern_producer_filter", "NOT_SET")
                logger.critical(f"🔄 Before reset - producer filter was: {current_producer}")
                
                # Offers cache törlés 
                offers_cache_keys = ['offers_cache', 'cache_timestamp', 'last_filters', 'offers_data']
                for cache_key in offers_cache_keys:
                    if cache_key in st.session_state:
                        del st.session_state[cache_key]
                
                # API cache-ek NEM törlődnek, csak a szűrők és offers cache
                # Ez optimalizáció: producers/products cache megmarad
                        
                # Szűrő kulcsok törlése
                filter_keys = [
                    "modern_producer_filter", "modern_status_filter", 
                    "modern_from_date", "modern_to_date", 
                    "modern_product_filter", "modern_search",
                    "enable_date_filter", "enable_status_filter"  # Új kulcsok
                ]
                for key in filter_keys:
                    if key in st.session_state:
                        logger.critical(f"🔄 Deleting session state key: {key} = {st.session_state[key]}")
                        del st.session_state[key]
                
                logger.critical("🔄 Filter reset completed - triggering rerun")
                st.rerun()
        
        with col5:
            if st.button("🗑️ Cache", key="cache_clear_btn"):
                # Minden típusú cache törlése
                all_cache_keys = [
                    'offers_cache', 'cache_timestamp', 'last_filters', 'offers_data',  # Offers cache
                    'producers_cache', 'producers_cache_timestamp',  # Producers cache 
                    'products_cache', 'products_cache_timestamp',    # Products cache
                ]
                cleared_count = 0
                for cache_key in all_cache_keys:
                    if cache_key in st.session_state:
                        del st.session_state[cache_key]
                        cleared_count += 1
                
                if cleared_count > 0:
                    st.success(f"✅ {cleared_count} cache kulcs törölve!")
                    logger.info(f"Manual cache clear: {cleared_count} keys deleted")
                else:
                    st.info("ℹ️ Nincs cache törlésre")
                st.rerun()
    
    # 🔧 JAVÍTÁS: Producer filter konzisztens formátum
    producer_filter_tuple = None
    if selected_producer_id is not None and selected_producer_display_name != "Minden termelő":
        producer_filter_tuple = (selected_producer_display_name, selected_producer_id)
        
    # Product filter konzisztens formátum
    product_filter_tuple = None
    product_type_id = None

    if selected_product and selected_product != "Minden termék":
        # Ha van product_type_id információ
        product_type_id = get_product_type_id(selected_product)
        if product_type_id:
            product_filter_tuple = (selected_product, product_type_id)
        else:
            # Ha nincs ID, csak a név legyen
            logger.warning(f"No product_type_id found for '{selected_product}'")

    filters_to_return = {
        'producer_filter': producer_filter_tuple,
        'producer_id': selected_producer_id if selected_producer_id is not None else None,
        'producer_display_name': selected_producer_display_name if selected_producer_display_name != "Minden termelő" else None,
        'status': actual_statuses,  # Már kezelt érték
        'from_date': actual_from_date,  # Már kezelt érték
        'to_date': actual_to_date,  # Már kezelt érték
        'product_filter': product_filter_tuple,  # Új mező
        'product_name': selected_product if selected_product != "Minden termék" else None,
        'product_type_id': product_type_id,  # Új mező
        'search_term': search_term.strip() if search_term and search_term.strip() else None
    }
    
    # 🔧 JAVÍTOTT: Debug panel biztonságos JSON kezeléssel (csak debug módban)
    if st.session_state.get("debug_mode", False):
        with st.expander("🔧 Szűrő Debug Információ", expanded=False):
            st.write("**UI Szűrők**")
            safe_json_display(filters_to_return, "UI Szűrők")
            
            # API konverzió teszt és diagnosztika
            try:
                from .api_parameter_converter import APIParameterConverter
                from .filter_diagnostic_tool import FilterDiagnosticTool, render_filter_diagnostic_panel
                
                converter = APIParameterConverter()
                api_params = converter.convert_filters(filters_to_return)
                
                st.write("**API Paraméterek**")
                safe_json_display(api_params, "API Paraméterek")
                
                # 🚨 CRITICAL: Filter Diagnostic Integration
                if 'filter_diagnostic_tool' not in st.session_state:
                    st.session_state.filter_diagnostic_tool = FilterDiagnosticTool()
                
                # Mock results for diagnosis (in real app, this would be actual results)
                mock_results = st.session_state.get('offers_cache', [])
                if mock_results:
                    st.write("**🚨 KRITIKUS SZŰRŐ DIAGNOSZTIKA**")
                    render_filter_diagnostic_panel(
                        st.session_state.filter_diagnostic_tool,
                        filters_to_return,
                        api_params,
                        mock_results
                    )
                
                # Validation test
                is_valid, errors = converter.validate_api_params(api_params)
                st.write(f"**Validáció**: {'✅ Sikeres' if is_valid else '❌ Hibás'}")
                if errors:
                    for error in errors:
                        st.error(error)
                
                # Conversion cache stats
                cache_stats = converter.get_cache_stats()
                if cache_stats['cache_entries'] > 0:
                    st.write("**Konverzió Cache**")
                    st.write(f"Bejegyzések: {cache_stats['cache_entries']}")
                    st.write(f"Átlagos kor: {cache_stats['average_age_seconds']:.1f}s")
                    st.write(f"Max kor: {cache_stats['max_age_seconds']:.1f}s")
                    st.write(f"Validáció elérhető: {'✅' if cache_stats.get('validation_available') else '❌'}")
                    if cache_stats['cache_keys']:
                        st.write(f"Kulcsok: {', '.join(cache_stats['cache_keys'][:3])}{'...' if len(cache_stats['cache_keys']) > 3 else ''}")
                
                # 🚨 CRITICAL: Producer validation
                if st.button("🔍 Termelő validáció", key="validate_producers"):
                    validation_report = converter.validate_all_cached_producers()
                    st.write("**Termelő Cache Validáció**")
                    
                    if validation_report['cache_available']:
                        st.write(f"Összes termelő: {validation_report['total_producers']}")
                        st.write(f"Érvényes termelők: {validation_report['valid_producers']}")
                        
                        if validation_report['invalid_producers']:
                            st.error(f"❌ {len(validation_report['invalid_producers'])} érvénytelen termelő:")
                            for invalid in validation_report['invalid_producers'][:3]:
                                st.write(f"- {invalid['name']}: {invalid['issue']}")
                        else:
                            st.success("✅ Minden termelő érvényes")
                    else:
                        st.warning("⚠️ Termelő cache nem elérhető")
                
                # Test conversion with sample data
                if st.button("🧪 Konverzió teszt", key="test_conversion"):
                    converter.test_filter_conversion()
                
            except ImportError as e:
                st.warning(f"API konverter nem elérhető: {e}")
            except Exception as e:
                st.error(f"Hiba a debug információk megjelenítésekor: {e}")
            
            # Performance info
            offer_state = st.session_state.get('offer_state_manager', {})
            metrics = offer_state.get('performance_metrics', {})
            if metrics:
                st.write("**Teljesítmény Metrikák**")
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("API hívások", metrics.get('api_calls_count', 0))
                    st.metric("Cache találatok", metrics.get('cache_hits', 0))
                with col2:
                    st.metric("Cache kihagyások", metrics.get('cache_misses', 0))
                    avg_time = metrics.get('average_load_time', 0)
                    st.metric("Átlagos idő (s)", f"{avg_time:.2f}")
            
            # Cache státusz debug
            st.write("**API Cache Státusz**")
            cache_info = {
                'producers_cache': 'producers_cache' in st.session_state,
                'products_cache': 'products_cache' in st.session_state,
                'offers_cache': 'offers_cache' in st.session_state,
            }
            
            for cache_name, exists in cache_info.items():
                if exists:
                    timestamp_key = f"{cache_name}_timestamp" if cache_name != 'offers_cache' else 'cache_timestamp'
                    if timestamp_key in st.session_state:
                        age = (datetime.now() - st.session_state[timestamp_key]).total_seconds()
                        st.write(f"✅ {cache_name}: {age:.1f}s régi")
                    else:
                        st.write(f"✅ {cache_name}: aktív")
                else:
                    st.write(f"❌ {cache_name}: nincs")
            
            # Session state filter debug
            st.write("**Session State Szűrő Kulcsok**")
            filter_keys = [k for k in st.session_state.keys() if any(prefix in k for prefix in ['modern_', 'enable_'])]
            for key in sorted(filter_keys):
                value = st.session_state[key]
                value_type = type(value).__name__
                if hasattr(value, 'strftime'):
                    display_value = value.strftime('%Y-%m-%d')
                else:
                    display_value = value
                st.write(f"- `{key}`: {display_value} ({value_type})")
    
    # 🚨 CRITICAL FINAL DEBUG: Log what we're returning
    logger.critical(f"🎯 RETURNING FILTERS: {filters_to_return}")
    logger.critical(f"🎯 Session state modern_producer_filter at return: {st.session_state.get('modern_producer_filter', 'NOT_SET')}")
    
    return filters_to_return

def render_active_filters(filters):
    """Aktív szűrők megjelenítése - CHECKBOX verzió"""
    active_filters = []
    
    # Csak akkor jelenítünk meg szűrőt, ha ténylegesen be van állítva
    if filters.get('producer_display_name'):
        active_filters.append(f"👤 Termelő: {filters['producer_display_name']}")
    
    if filters.get('status') and len(filters['status']) > 0:
        status_labels = {
            "CREATED": "Létrehozva",
            "CONFIRMED_BY_COMPANY": "Megerősítve", 
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Véglegesítve"
        }
        status_text = ', '.join(status_labels.get(s, s) for s in filters['status'])
        active_filters.append(f"🏷️ Státusz: {status_text}")
    
    # Dátum szűrő - rugalmas megjelenítés
    if filters.get('from_date') is not None and filters.get('to_date') is not None:
        active_filters.append(f"📅 Időszak: {filters['from_date']} - {filters['to_date']}")
    elif filters.get('from_date') is not None:
        active_filters.append(f"📅 Kezdő dátum: {filters['from_date']}")
    elif filters.get('to_date') is not None:
        active_filters.append(f"📅 Végső dátum: {filters['to_date']}")
    
    if filters.get('product_name'):
        active_filters.append(f"🌾 Termék: {filters['product_name']}")
    
    if filters.get('search_term'):
        active_filters.append(f"🔍 Keresés: {filters['search_term']}")
    
    # Aktív szűrők megjelenítése modern stílusban
    if active_filters:
        st.markdown("""
        <style>
        .active-filters-container {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 16px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .active-filter-item {
            display: inline-block;
            background: white;
            color: #1976d2;
            padding: 4px 12px;
            margin: 4px 8px 4px 0;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            border: 1px solid #2196f3;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        </style>
        """, unsafe_allow_html=True)
        
        filter_html = f"""
        <div class="active-filters-container">
            <strong>✨ Aktív szűrők:</strong><br>
            {''.join(f'<span class="active-filter-item">{filter_text}</span>' for filter_text in active_filters)}
        </div>
        """
        st.markdown(filter_html, unsafe_allow_html=True)
    else:
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            border-left: 4px solid #8bc34a;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 16px 0;
            color: #558b2f;
            font-weight: 500;
        ">
            🔓 Nincsenek aktív szűrők - minden ajánlat megjelenik
        </div>
        """, unsafe_allow_html=True)

def render_statistics_card(offers):
    """Statisztikai kártya valós adatokkal - DEBUG verzió"""
    if not offers:
        return
    
    total_offers = len(offers)
    
    # Debug: Adatok elemzése
    quantity_values = []
    price_values = []
    valid_quantity_count = 0
    valid_price_count = 0
    
    for offer in offers:
        # Quantity elemzés
        qty_raw = offer.get('quantity_value', offer.get('quantity_in_kg'))
        if qty_raw is not None and str(qty_raw).strip():
            try:
                qty_val = float(qty_raw)
                quantity_values.append(qty_val)
                if qty_val > 0:
                    valid_quantity_count += 1
            except (ValueError, TypeError):
                pass
        
        # Price elemzés
        price_raw = offer.get('price') or offer.get('confirmed_price')
        if price_raw is not None and str(price_raw).strip():
            try:
                price_val = float(price_raw)
                price_values.append(price_val)
                if price_val > 0:
                    valid_price_count += 1
            except (ValueError, TypeError):
                pass
    
    # Számítások
    total_quantity = sum(quantity_values)
    total_value = sum(float(offer.get('quantity_value', offer.get('quantity_in_kg')) or 0) * float(offer.get('price') or offer.get('confirmed_price') or 0) for offer in offers)
    avg_price = sum(price_values) / len(price_values) if price_values else 0
    
    # Státusz elemzés
    status_counts = {}
    for offer in offers:
        status = offer.get('status', 'UNKNOWN')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    st.markdown(f"""
    <div class="stats-card">
        <div class="stats-header">📊 Statisztika</div>
        <div class="stats-content">
            <div class="stats-row">
                <span>Ajánlatok száma:</span>
                <span class="stats-value">{total_offers}</span>
            </div>
            <div class="stats-row">
                <span>Összes mennyiség:</span>
                <span class="stats-value">{total_quantity:,.0f} kg</span>
            </div>
            <div class="stats-row">
                <span>Átlagár:</span>
                <span class="stats-value">{avg_price:,.0f} Ft/kg</span>
            </div>
            <div class="stats-row">
                <span>Összérték:</span>
                <span class="stats-value">{total_value:,.0f} Ft</span>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Debug információk expanderben
    if st.session_state.get("debug_mode", False):
        with st.expander("🔧 Statisztika Debug Információ", expanded=False):
            st.write("**Adatminőség Elemzés**")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**Mennyiség adatok:**")
                st.write(f"- Összes ajánlat: {total_offers}")
                st.write(f"- Érvényes mennyiség: {len(quantity_values)}")
                st.write(f"- Pozitív mennyiség: {valid_quantity_count}")
                st.write(f"- Hiányzó/nulla: {total_offers - len(quantity_values)}")
                
                if quantity_values:
                    st.write(f"- Min mennyiség: {min(quantity_values):,.0f} kg")
                    st.write(f"- Max mennyiség: {max(quantity_values):,.0f} kg")
            
            with col2:
                st.write(f"**Ár adatok:**")
                st.write(f"- Érvényes ár: {len(price_values)}")
                st.write(f"- Pozitív ár: {valid_price_count}")
                st.write(f"- Hiányzó/nulla: {total_offers - len(price_values)}")
                
                if price_values:
                    st.write(f"- Min ár: {min(price_values):,.0f} Ft/kg")
                    st.write(f"- Max ár: {max(price_values):,.0f} Ft/kg")
            
            st.write("**Státusz Megoszlás:**")
            for status, count in status_counts.items():
                percentage = (count / total_offers) * 100
                status_labels = {
                    "CREATED": "Létrehozva",
                    "CONFIRMED_BY_COMPANY": "Megerősítve",
                    "ACCEPTED_BY_USER": "Elfogadva", 
                    "REJECTED_BY_USER": "Elutasítva",
                    "FINALIZED": "Véglegesítve"
                }
                label = status_labels.get(status, status)
                st.write(f"- {label}: {count} ({percentage:.1f}%)")
            
            # Adatstruktúra debug
            if st.checkbox("Részletes adatstruktúra", key="detailed_data_debug"):
                st.write("**Első 3 ajánlat adatstruktúrája:**")
                for i, offer in enumerate(offers[:3]):
                    st.write(f"**Ajánlat #{i+1}:**")
                    relevant_fields = {
                        'id': offer.get('id'),
                        'quantity_in_kg': offer.get('quantity_value', offer.get('quantity_in_kg', 0)),
                        'confirmed_quantity': offer.get('confirmed_quantity'),
                        'price': offer.get('price'),
                        'confirmed_price': offer.get('confirmed_price'),
                        'status': offer.get('status'),
                        'product_name': offer.get('product_name'),
                        'created_at': offer.get('created_at')
                    }
                    safe_json_display(relevant_fields, f"Ajánlat #{i+1}")

def render_offers_display(offers, device_type):
    """Ajánlatok megjelenítése eszköz típus szerint - DEBUG verzió"""
    if not offers:
        st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
        return
    
    # Adatok előzetes elemzése
    unique_producers = set()
    unique_products = set()
    date_range = []
    
    for offer in offers:
        # Termelő elemzés
        producer_name = (offer.get('user', {}).get('contact_name') or 
                        offer.get('user_name') or 
                        offer.get('user', {}).get('company_name'))
        if producer_name:
            unique_producers.add(producer_name)
        
        # Termék elemzés
        product_name = offer.get('product_name')
        if product_name:
            unique_products.add(product_name)
        
        # Dátum elemzés
        created_at = offer.get('created_at')
        if created_at:
            try:
                if isinstance(created_at, str):
                    date_obj = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    date_range.append(date_obj)
            except:
                pass
    
    # Dátum tartomány
    date_info = ""
    if date_range:
        min_date = min(date_range).strftime('%Y.%m.%d')
        max_date = max(date_range).strftime('%Y.%m.%d')
        if min_date == max_date:
            date_info = f" | {min_date}"
        else:
            date_info = f" | {min_date} - {max_date}"
    
    # Eredmények száma
    st.markdown(f"""
    <div style="
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
        margin: 1rem 0;
        overflow: hidden;
    ">
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        ">
            <span>📋 Ajánlatok listája</span>
            <span>Találatok: {len(offers)} ajánlat{date_info}</span>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Debug információk expanderben
    if st.session_state.get("debug_mode", False):
        with st.expander("🔧 Ajánlatok Debug Információ", expanded=False):
            st.write("**Lista Elemzés**")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"**Eszköz típus:** {device_type}")
                st.write(f"**Összes ajánlat:** {len(offers)}")
                st.write(f"**Egyedi termelők:** {len(unique_producers)}")
                
            with col2:
                st.write(f"**Egyedi termékek:** {len(unique_products)}")
                if date_range:
                    st.write(f"**Legkorábbi:** {min(date_range).strftime('%Y.%m.%d')}")
                    st.write(f"**Legújabb:** {max(date_range).strftime('%Y.%m.%d')}")
                
            with col3:
                st.write(f"**Dátum tartomány:** {len(date_range)} érvényes")
                avg_per_producer = len(offers) / len(unique_producers) if unique_producers else 0
                st.write(f"**Átlag/termelő:** {avg_per_producer:.1f}")
            
            # Termelők listája
            if st.checkbox("Termelők listája", key="producers_list_debug"):
                st.write("**Termelők az eredményekben:**")
                for i, producer in enumerate(sorted(unique_producers)[:10], 1):
                    count = sum(1 for offer in offers 
                              if (offer.get('user', {}).get('contact_name') or 
                                  offer.get('user_name') or 
                                  offer.get('user', {}).get('company_name')) == producer)
                    st.write(f"{i}. {producer} ({count} ajánlat)")
                if len(unique_producers) > 10:
                    st.write(f"... és még {len(unique_producers) - 10} termelő")
            
            # Termékek listája
            if st.checkbox("Termékek listája", key="products_list_debug"):
                st.write("**Termékek az eredményekben:**")
                for i, product in enumerate(sorted(unique_products)[:10], 1):
                    count = sum(1 for offer in offers if offer.get('product_name') == product)
                    st.write(f"{i}. {product} ({count} ajánlat)")
                if len(unique_products) > 10:
                    st.write(f"... és még {len(unique_products) - 10} termék")
            
            # Mintaadatok
            if st.checkbox("Minta ajánlatok", key="sample_offers_debug"):
                st.write("**Első 2 ajánlat alapadatai:**")
                for i, offer in enumerate(offers[:2], 1):
                    basic_info = {
                        'id': offer.get('id'),
                        'status': offer.get('status'),
                        'producer': (offer.get('user', {}).get('contact_name') or 
                                   offer.get('user_name') or 
                                   offer.get('user', {}).get('company_name')),
                        'product': offer.get('product_name'),
                        'quantity': offer.get('quantity_value', offer.get('quantity_in_kg', 0)),
                        'price': offer.get('price'),
                        'confirmed_price': offer.get('confirmed_price'),
                        'created_at': offer.get('created_at')
                    }
                    st.write(f"**#{i}:**")
                    safe_json_display(basic_info, f"Ajánlat #{i}")
    
    if device_type == 'mobile':
        render_mobile_offers(offers)
    else:
        render_desktop_table(offers)

def render_mobile_offers(offers):
    """Mobil kártyák valós adatokkal"""
    for offer in offers:
        status_emoji = {
            'CREATED': '📝',
            'CONFIRMED_BY_COMPANY': '🏢',
            'ACCEPTED_BY_USER': '👍', 
            'REJECTED_BY_USER': '👎',
            'FINALIZED': '✅'
        }.get(offer.get('status', ''), '⚪')
        
        status_text = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva', 
            'FINALIZED': 'Véglegesítve'
        }.get(offer.get('status', ''), 'Ismeretlen')
        
        # Dátum formázás
        created_at = offer.get('created_at', '')
        if created_at:
            try:
                if isinstance(created_at, str):
                    formatted_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime('%Y.%m.%d')
                else:
                    formatted_date = created_at.strftime('%Y.%m.%d')
            except:
                formatted_date = str(created_at)[:10]
        else:
            formatted_date = 'N/A'

        # Termelő nevének kinyerése - contact_name prioritással
        producer_display_name = offer.get('user', {}).get('contact_name') or \
                                offer.get('user_name') or \
                                offer.get('user', {}).get('company_name') or \
                                'Ismeretlen termelő'

        # Ár kezelése (mint a desktop verzióban)
        confirmed_price_str = offer.get('confirmed_price')
        price_display = "Visszaigazolásra vár"
        if confirmed_price_str:
            try:
                confirmed_price_val = float(confirmed_price_str)
                if confirmed_price_val > 0:
                    price_display = f"💰 {confirmed_price_val:,.0f} Ft/kg"
            except ValueError:
                pass # Marad a "Visszaigazolásra vár"
        
        # Mennyiség kezelése (mint a desktop verzióban)
        confirmed_quantity_str = offer.get('confirmed_quantity')
        quantity_to_display_str = confirmed_quantity_str or offer.get('quantity_value', offer.get('quantity_in_kg', 0))
        quantity_display = "-"
        if quantity_to_display_str:
            try:
                quantity_val = float(quantity_to_display_str)
                quantity_display = f"📦 {quantity_val:,.0f} kg"
            except ValueError:
                pass # Marad a "-"
        
        st.markdown(f"""
        <div class="mobile-offer-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <div style="font-weight: 600; font-size: 1.1rem; color: #1a73e8;">
                    #{offer.get('id', 'N/A')}
                </div>
                <div style="font-size: 0.875rem; color: #666;">
                    📅 {formatted_date}
                </div>
            </div>
            
            <div style="margin-bottom: 0.75rem;">
                <div style="font-weight: 500; color: #333; margin-bottom: 0.25rem;">
                    👤 {producer_display_name}
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <span style="margin-right: 0.5rem; font-size: 1.1rem;">🌾</span>
                    <span style="font-weight: 500;">{offer.get('product_name', 'Ismeretlen termék')}</span>
                </div>
            </div>
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <div style="background: #f8f9fa; padding: 0.5rem; border-radius: 8px; flex: 1; margin-right: 0.5rem;">
                    <div style="font-size: 0.75rem; color: #666; margin-bottom: 0.25rem;">Mennyiség</div>
                    <div style="font-weight: 600; color: #1a73e8;">{quantity_display}</div>
                </div>
                <div style="background: #f8f9fa; padding: 0.5rem; border-radius: 8px; flex: 1; margin-left: 0.5rem;">
                    <div style="font-size: 0.75rem; color: #666; margin-bottom: 0.25rem;">Ár</div>
                    <div style="font-weight: 600; color: #1a73e8;">{price_display}</div>
                </div>
            </div>
            
            <div style="
                display: flex; 
                align-items: center; 
                background: #f0f8ff; 
                padding: 0.75rem; 
                border-radius: 8px;
                border-left: 4px solid #1a73e8;
                margin-bottom: 1rem;
            ">
                <span style="margin-right: 0.5rem; font-size: 1.1rem;">{status_emoji}</span>
                <span style="font-weight: 500;">{status_text}</span>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Add Details button for mobile cards
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button(
                "👁️ Részletek megtekintése", 
                key=f"details_mobile_{offer.get('id')}", 
                use_container_width=True,
                type="primary"
            ):
                st.session_state.selected_offer_id = offer.get('id')
                st.rerun()

def render_desktop_table(offers):
    """Asztali táblázat valós adatokkal"""
    # DataFrame előkészítése
    table_data = []
    for offer in offers:
        status_emoji = {
            'CREATED': '📝',
            'CONFIRMED_BY_COMPANY': '🏢',
            'ACCEPTED_BY_USER': '👍',
            'REJECTED_BY_USER': '👎',
            'FINALIZED': '✅'
        }.get(offer.get('status', ''), '⚪')
        
        status_text = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }.get(offer.get('status', ''), 'Ismeretlen')
        
        # Dátum formázás
        created_at = offer.get('created_at', '')
        if created_at:
            try:
                if isinstance(created_at, str):
                    formatted_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime('%Y.%m.%d')
                else:
                    formatted_date = created_at.strftime('%Y.%m.%d')
            except:
                formatted_date = str(created_at)[:10]
        else:
            formatted_date = 'N/A'

        # Termelő nevének kinyerése - contact_name prioritással
        producer_display_name = offer.get('user', {}).get('contact_name') or \
                                offer.get('user_name') or \
                                offer.get('user', {}).get('company_name') or \
                                'Ismeretlen'

        # Ár kezelése
        confirmed_price_str = offer.get('confirmed_price')
        price_display = "Visszaigazolásra vár"
        if confirmed_price_str:
            try:
                confirmed_price_val = float(confirmed_price_str)
                if confirmed_price_val > 0:
                    price_display = f"💰 {confirmed_price_val:,.0f} Ft/kg"
            except ValueError:
                pass # Marad a "Visszaigazolásra vár"
        
        # Mennyiség kezelése
        confirmed_quantity_str = offer.get('confirmed_quantity')
        quantity_to_display_str = confirmed_quantity_str or offer.get('quantity_value', offer.get('quantity_in_kg', 0))
        quantity_display = "-"
        if quantity_to_display_str:
            try:
                quantity_val = float(quantity_to_display_str)
                quantity_display = f"📦 {quantity_val:,.0f} kg"
            except ValueError:
                pass # Marad a "-"

        table_data.append({
            'ID': f"#{offer.get('id', 'N/A')}",
            'Dátum': formatted_date,
            'Termelő': producer_display_name,
            'Termék': f"🌾 {offer.get('product_name', 'Ismeretlen')}",
            'Mennyiség': quantity_display,
            'Ár': price_display,
            'Státusz': f"{status_emoji} {status_text}",
            'Műveletek': offer.get('id')  # Store ID for button generation
        })
    
    # Enhanced table with interactive buttons
    if table_data:
        # Create DataFrame for display (without the Műveletek column)
        display_data = [{k: v for k, v in row.items() if k != 'Műveletek'} for row in table_data]
        df = pd.DataFrame(display_data)
        
        # Display the main table
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Add interactive buttons section
        st.markdown("### 🔧 Műveletek")
        st.markdown("Válassz egy ajánlatot a műveletek végrehajtásához:")
        
        # Create columns for offer selection and actions
        num_cols = min(len(offers), 4)  # Max 4 columns
        cols = st.columns(num_cols)
        
        for i, offer in enumerate(offers[:num_cols]):  # Show first few offers
            with cols[i % num_cols]:
                offer_id = offer.get('id')
                st.markdown(f"**#{offer_id}**")
                st.caption(f"{offer.get('product_name', 'N/A')[:20]}...")
                
                if st.button(
                    "👁️ Részletek", 
                    key=f"details_desktop_{offer_id}",
                    use_container_width=True
                ):
                    st.session_state.selected_offer_id = offer_id
                    st.rerun()
        
        # If there are more offers, show a selection dropdown
        if len(offers) > num_cols:
            st.markdown("**További ajánlatok:**")
            remaining_offers = offers[num_cols:]
            
            selected_offer_for_details = st.selectbox(
                "Válassz ajánlatot a részletekhez:",
                options=[None] + [offer.get('id') for offer in remaining_offers],
                format_func=lambda x: "-- Válassz --" if x is None else f"#{x} - {next((o.get('product_name', 'N/A') for o in remaining_offers if o.get('id') == x), 'N/A')[:30]}...",
                key="offer_details_selector"
            )
            
            if selected_offer_for_details:
                col1, col2, col3 = st.columns([1, 1, 2])
                with col1:
                    if st.button("👁️ Részletek megtekintése", key="details_from_selector"):
                        st.session_state.selected_offer_id = selected_offer_for_details
                        st.rerun()
    else:
        st.info("Nincsenek ajánlatok a megjelenítéshez")

# Főoldal futtatása - csak akkor, ha közvetlenül ezt a modult futtatjuk
def main():
    """Főoldal futtatása"""
    # Debug információk megjelenítése (ha a DEBUG_MODE be van kapcsolva)
    render_debug_info()
    
    # CSS stílusok betöltése
    inject_modern_styles()
    
    # Oldal fejléc megjelenítése
    render_page_header()
    
    # Szűrők és paraméterek inicializálása
    if 'offer_filters' not in st.session_state:
        st.session_state.offer_filters = {}
    
    # Modern szűrőpanel megjelenítése
    render_modern_filter_panel()
    
    # API-tól ajánlatok lekérése a szűrőkkel
    from .api_client import get_offers
    success, offers = get_offers(st.session_state.offer_filters if 'offer_filters' in st.session_state else None)
    
    if not success:
        st.error(f"Hiba történt az ajánlatok lekérése közben: {offers}")
        return
    
    # Aktív szűrők megjelenítése
    render_active_filters(st.session_state.offer_filters if 'offer_filters' in st.session_state else {})
    
    # Statisztikai adatok megjelenítése
    render_statistics_card(offers)
    
    # Képernyőméret-alapú megjelenítés
    device_type = "desktop"  # Itt lehetne valós eszközfelismerés
    render_offers_display(offers, device_type)

# Főoldal futtatása - csak akkor, ha közvetlenül ezt a modult futtatjuk
if __name__ == "__main__":
    main()
