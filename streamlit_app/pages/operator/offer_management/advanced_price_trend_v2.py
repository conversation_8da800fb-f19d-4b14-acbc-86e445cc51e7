"""
📊 Ártrend grafikon újratervezése - Teljes időszak alapú megközelítés
Advanced Price Trend Analysis V2 - COMPLETE IMPLEMENTATION
"""
import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import pandas as pd
import time
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)

# ============================================================================
# TIME PERIOD GENERATOR
# ============================================================================

def generate_time_periods(start_date: datetime, end_date: datetime, 
                         granularity: str) -> List[Dict[str, Any]]:
    """
    Időszakok generálása a megadott felbontás szerint
    """
    periods = []
    current = start_date
    
    if granularity == 'daily':
        while current <= end_date:
            periods.append({
                'start': current,
                'end': current,
                'label': current.strftime('%m/%d'),
                'full_label': current.strftime('%Y-%m-%d')
            })
            current += timedelta(days=1)
            
    elif granularity == 'weekly':
        # Start from Monday of the first week
        start_monday = current - timedelta(days=current.weekday())
        current = start_monday
        
        while current <= end_date:
            week_end = current + timedelta(days=6)
            if week_end > end_date:
                week_end = end_date
            
            periods.append({
                'start': current,
                'end': week_end,
                'label': f"{current.strftime('%m/%d')}-{week_end.strftime('%m/%d')}",
                'full_label': f"{current.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}"
            })
            current += timedelta(days=7)
            
    elif granularity == 'monthly':
        while current <= end_date:
            # Hónap első napja
            month_start = datetime(current.year, current.month, 1)
            
            # Hónap utolsó napja
            if current.month == 12:
                month_end = datetime(current.year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = datetime(current.year, current.month + 1, 1) - timedelta(days=1)
            
            if month_end > end_date:
                month_end = end_date
                
            periods.append({
                'start': month_start,
                'end': month_end,
                'label': current.strftime('%Y/%m'),
                'full_label': current.strftime('%Y. %B')
            })
            
            # Következő hónap
            if current.month == 12:
                current = datetime(current.year + 1, 1, 1)
            else:
                current = datetime(current.year, current.month + 1, 1)
                
    elif granularity == 'quarterly':
        while current <= end_date:
            # Negyedév kezdete
            quarter = (current.month - 1) // 3 + 1
            quarter_start = datetime(current.year, (quarter - 1) * 3 + 1, 1)
            
            # Negyedév vége
            if quarter == 4:
                quarter_end = datetime(current.year + 1, 1, 1) - timedelta(days=1)
            else:
                quarter_end = datetime(current.year, quarter * 3 + 1, 1) - timedelta(days=1)
            
            if quarter_end > end_date:
                quarter_end = end_date
                
            periods.append({
                'start': quarter_start,
                'end': quarter_end,
                'label': f"{current.year}/Q{quarter}",
                'full_label': f"{current.year}. {quarter}. negyedév"
            })
            
            # Következő negyedév
            if quarter == 4:
                current = datetime(current.year + 1, 1, 1)
            else:
                current = datetime(current.year, quarter * 3 + 1, 1)
                
    elif granularity == 'yearly':
        while current <= end_date:
            year_start = datetime(current.year, 1, 1)
            year_end = datetime(current.year, 12, 31)
            
            if year_end > end_date:
                year_end = end_date
                
            periods.append({
                'start': year_start,
                'end': year_end,
                'label': str(current.year),
                'full_label': f"{current.year}. év"
            })
            
            current = datetime(current.year + 1, 1, 1)
    
    return periods

# ============================================================================
# INTELLIGENT GRANULARITY RECOMMENDER
# ============================================================================

def get_available_granularities(start_date: datetime, end_date: datetime) -> List[str]:
    """
    Az időszak hossza alapján ajánl megfelelő felbontásokat
    """
    delta = end_date - start_date
    days = delta.days
    
    if days <= 7:
        return ['daily']
    elif days <= 31:
        return ['daily', 'weekly']
    elif days <= 93:  # ~3 hónap
        return ['daily', 'weekly', 'monthly']
    elif days <= 365:
        return ['weekly', 'monthly']
    elif days <= 730:  # 2 év
        return ['monthly', 'quarterly']
    else:
        return ['monthly', 'quarterly', 'yearly']

def get_preset_dates(preset: str) -> Tuple[datetime, datetime]:
    """
    Előre definiált időszakok dátumainak visszaadása
    """
    now = datetime.now()
    
    if preset == "Elmúlt 7 nap":
        return now - timedelta(days=7), now
    elif preset == "Elmúlt 30 nap":
        return now - timedelta(days=30), now
    elif preset == "Elmúlt 3 hónap":
        return now - timedelta(days=90), now
    elif preset == "Elmúlt 6 hónap":
        return now - timedelta(days=180), now
    elif preset == "Elmúlt 1 év":
        return now - timedelta(days=365), now
    elif preset == "Idei év":
        return datetime(now.year, 1, 1), now
    elif preset == "Tavalyi év":
        last_year = now.year - 1
        return datetime(last_year, 1, 1), datetime(last_year, 12, 31)
    else:  # Egyéni időszak
        return now - timedelta(days=90), now

# ============================================================================
# CUSTOM DATE FIELD FILTERING
# ============================================================================

def get_offers_with_custom_date_filter(params: dict, date_field: str = "delivery_date") -> dict:
    """
    Ajánlatok lekérése egyéni dátum mező szerinti szűréssel
    
    Since the API only supports delivery_date filtering, this function
    manually filters results by other date fields (created_at, confirmed_at).
    """
    try:
        import requests
        from datetime import datetime
        
        # If using delivery_date, check if we should use fallback
        if date_field == "delivery_date":
            # Try standard API first, but if it returns 0 results, 
            # it might be because many offers have null delivery_date
            standard_result = call_statistics_api_with_retry(params)
            
            # If we get very few results, try manual filtering with fallback
            if (standard_result and 
                standard_result.get('total_offers', 0) < 5 and 
                not standard_result.get('error')):
                
                logger.info("Low offer count with delivery_date API, trying manual filtering with fallback")
                # Continue with manual filtering to use fallback logic
            else:
                return standard_result
        
        # For other date fields, we need manual filtering
        logger.info(f"Manual filtering required for date_field: {date_field}")
        
        # First step: get all offers for the product/quality without date filtering
        base_params = {
            'product_type_id': params.get('product_type_id'),
            'category_id': params.get('category_id'),
            'quality_grade_id': params.get('quality_grade_id'),
            'status': params.get('status'),
            'limit': 1000  # Large limit to get complete dataset
        }
        
        # Remove None values
        base_params = {k: v for k, v in base_params.items() if v is not None}
        
        # API call to get all offers
        headers = {}
        try:
            from ...api.offers import get_auth_headers
            headers.update(get_auth_headers())
        except ImportError:
            try:
                from ..api.offers import get_auth_headers
                headers.update(get_auth_headers())
            except ImportError:
                try:
                    from streamlit_app.api.offers import get_auth_headers
                    headers.update(get_auth_headers())
                except ImportError:
                    import streamlit as st
                    if hasattr(st, 'session_state') and 'access_token' in st.session_state:
                        headers['Authorization'] = f"Bearer {st.session_state.access_token}"
        
        response = requests.get(
            "http://backend:8000/api/offers",
            params=base_params,
            headers=headers,
            timeout=10
        )
        
        if response.status_code != 200:
            logger.error(f"API error: {response.status_code}")
            return {'error': 'api_error', 'status_code': response.status_code}
            
        offers = response.json()
        
        # Second step: manual filtering by the specified date field
        date_from = datetime.strptime(params['date_from'], '%Y-%m-%d')
        date_to = datetime.strptime(params['date_to'], '%Y-%m-%d')
        
        filtered_offers = []
        total_quantity = 0
        weighted_price_sum = 0
        
        for offer in offers:
            # Get the appropriate date field with fallback logic
            date_str = offer.get(date_field)
            
            # JEGYZET: delivery_date kötelező mező az API sémában, mindig ki van töltve
            # A fallback logic csak más date_field típusokhoz szükséges (created_at, updated_at)
            
            if date_str:
                try:
                    # Parse date string (take only first 10 characters - YYYY-MM-DD)
                    offer_date = datetime.strptime(date_str[:10], '%Y-%m-%d')
                    
                    # Check if within the time period
                    if date_from <= offer_date <= date_to:
                        price = float(offer.get('confirmed_price', 0))
                        quantity = float(offer.get('confirmed_quantity', 1))
                        
                        if price > 0:  # Only count offers with valid prices
                            filtered_offers.append(offer)
                            total_quantity += quantity
                            weighted_price_sum += price * quantity
                            
                except (ValueError, TypeError):
                    logger.warning(f"Invalid date format in offer {offer.get('id')}: {date_str}")
        
        # Calculate average price
        if filtered_offers and total_quantity > 0:
            average_price = weighted_price_sum / total_quantity
            return {
                'average_price': str(average_price),  # API returns as string
                'total_offers': len(offers),  # Total found
                'confirmed_offers_count': len(filtered_offers),  # Filtered count
                'date_field_used': date_field,
                'manual_filtering': True
            }
        else:
            return {
                'average_price': None,
                'total_offers': len(offers),
                'confirmed_offers_count': 0,
                'date_field_used': date_field,
                'manual_filtering': True
            }
            
    except Exception as e:
        logger.error(f"Custom date filter error: {str(e)}")
        return {'error': str(e)}

# ============================================================================
# OPTIMIZED API CALLS WITH RETRY AND CACHE
# ============================================================================

def generate_cache_key(params: dict) -> str:
    """
    Cache kulcs generálása API paraméterek alapján
    """
    import hashlib
    import json
    
    # Normalize params for consistent hashing
    normalized = json.dumps(params, sort_keys=True)
    return hashlib.md5(normalized.encode()).hexdigest()

@st.cache_data(ttl=300)
def call_statistics_api_with_retry(params: dict, max_retries: int = 3) -> dict:
    """
    Statistics API hívás retry logikával és cache-eléssel
    """
    cache_key = generate_cache_key(params)
    
    # Check session state cache first
    if 'api_cache' in st.session_state:
        if cache_key in st.session_state.api_cache:
            cached_data, timestamp = st.session_state.api_cache[cache_key]
            if datetime.now() - timestamp < timedelta(minutes=5):
                logger.info(f"Cache hit for {cache_key}")
                return cached_data
    
    # API call with retry
    for attempt in range(max_retries):
        try:
            response = make_api_call(params, timeout=3 + attempt)  # Increasing timeout
            
            if response.status_code == 200:
                data = response.json()
                
                # Cache the result in session state
                if 'api_cache' not in st.session_state:
                    st.session_state.api_cache = {}
                
                st.session_state.api_cache[cache_key] = (data, datetime.now())
                logger.info(f"API call successful for {cache_key}")
                return data
                
        except requests.exceptions.Timeout:
            logger.warning(f"API timeout on attempt {attempt + 1} for {cache_key}")
            if attempt < max_retries - 1:
                time.sleep(0.5 * (attempt + 1))  # Exponential backoff
                continue
            else:
                logger.error(f"Max timeouts reached for {cache_key}")
                return {'error': 'timeout', 'average_price': None, 'total_offers': 0}
        except Exception as e:
            logger.error(f"API error on attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(0.5 * (attempt + 1))
                continue
                
    logger.error(f"Max retries exceeded for {cache_key}")
    return {'error': 'max_retries_exceeded', 'average_price': None, 'total_offers': 0}

def make_api_call(params: dict, timeout: int = 5) -> requests.Response:
    """
    Tényleges API hívás
    """
    # Use the same URL as working API calls
    API_BASE_URL = "http://backend:8000"
    
    # Auth headers
    headers = {}
    try:
        from ...api.offers import get_auth_headers
        headers.update(get_auth_headers())
    except ImportError:
        try:
            from ..api.offers import get_auth_headers
            headers.update(get_auth_headers())
        except ImportError:
            try:
                from streamlit_app.api.offers import get_auth_headers
                headers.update(get_auth_headers())
            except ImportError:
                # Fallback
                if hasattr(st, 'session_state') and 'access_token' in st.session_state:
                    headers['Authorization'] = f"Bearer {st.session_state.access_token}"
    
    logger.info(f"Making API call with params: {params}")
    
    response = requests.get(
        f"{API_BASE_URL}/api/offers/statistics",
        params=params,
        headers=headers,
        timeout=timeout
    )
    
    logger.info(f"API response status: {response.status_code}")
    return response

# ============================================================================
# HIERARCHICAL DATA FETCHING
# ============================================================================

def get_price_trend_data_with_smart_fallback(offer: Dict[str, Any], 
                                           start_date: datetime,
                                           end_date: datetime,
                                           granularity: str = "daily",
                                           date_type: str = "delivery_date") -> Dict[str, Any]:
    """
    Ártrend adatok lekérése intelligens fallback logikával
    """
    # Első próbálkozás az eredeti paraméterekkel
    trend_data = get_price_trend_data_v2(offer, start_date, end_date, granularity, date_type)
    
    # Sikeresség ellenőrzése
    success_rate = calculate_success_rate(trend_data)
    
    if success_rate < 10:  # Ha 10% alatt van a siker
        st.warning("⚠️ Kevés találat a kiválasztott időszakban")
        
        # Intelligens fallback javaslat
        smart_suggestion = analyze_offer_dates_for_trend(offer)
        
        if smart_suggestion and smart_suggestion.get('delivery_year'):
            delivery_year = smart_suggestion['delivery_year']
            current_year = datetime.now().year
            
            # Ha nem a delivery date évét használjuk, próbáljuk azt
            if start_date.year != delivery_year or end_date.year != delivery_year:
                st.info(f"🔄 Automatikus újrapróbálkozás {delivery_year} évvel (beszállítási dátum alapján)...")
                
                # Optimális időszak
                optimal_start = datetime(delivery_year, 1, 1)
                optimal_end = datetime(delivery_year, 12, 31)
                
                # Újrapróbálkozás
                trend_data = get_price_trend_data_v2(offer, optimal_start, optimal_end, granularity, date_type)
                new_success_rate = calculate_success_rate(trend_data)
                
                if new_success_rate > success_rate:
                    st.success(f"✅ Jobb eredmény {delivery_year} évvel! ({new_success_rate:.0f}% vs {success_rate:.0f}%)")
                    # Frissítsük a settings-et is a feedback számára
                    trend_data['auto_optimized'] = True
                    trend_data['original_period'] = f"{start_date.year}-{end_date.year}"
                    trend_data['optimized_period'] = f"{delivery_year}"
                else:
                    st.warning(f"⚠️ {delivery_year} évvel sem javult jelentősen ({new_success_rate:.0f}%)")
        
        # Ha még mindig alacsony, próbáljuk created_at-tel
        if success_rate < 5 and date_type == "delivery_date":
            st.info("🔄 Próbálkozás létrehozási dátum alapján...")
            
            created_trend_data = get_price_trend_data_v2(offer, start_date, end_date, granularity, "created_at")
            created_success_rate = calculate_success_rate(created_trend_data)
            
            if created_success_rate > success_rate:
                st.success(f"✅ Jobb eredmény létrehozási dátummal! ({created_success_rate:.0f}% vs {success_rate:.0f}%)")
                trend_data = created_trend_data
                trend_data['auto_fallback_to_created'] = True
    
    return trend_data

def calculate_success_rate(trend_data: Dict[str, Any]) -> float:
    """
    Ártrend adatok sikerességi arányának kiszámítása
    """
    if not trend_data or not trend_data.get('hierarchies'):
        return 0.0
    
    total_periods = 0
    successful_periods = 0
    
    for hierarchy in trend_data['hierarchies']:
        data_points = hierarchy.get('data_points', 0)
        successful_periods += data_points
        
        # Becsüljük a teljes időszakok számát a dates alapján
        if 'dates' in hierarchy:
            total_periods = max(total_periods, len(hierarchy['dates']))
    
    if total_periods == 0:
        return 0.0
    
    return (successful_periods / total_periods) * 100

def get_price_trend_data_v2(offer: Dict[str, Any], 
                           start_date: datetime,
                           end_date: datetime,
                           granularity: str = "daily",
                           date_type: str = "delivery_date") -> Dict[str, Any]:
    """
    Ártrend adatok lekérése a megadott időszakra és felbontással
    """
    logger.info(f"Getting price trend data v2: {start_date} to {end_date}, {granularity}")
    
    # Hierarchia szintek meghatározása
    product_type = offer.get('product_type', {}) or {}
    quality_grade = offer.get('quality_grade', {}) or {}
    
    hierarchy_params = []
    
    # Termék + Minőség (ha van minőség ID)
    if product_type.get('id') and quality_grade.get('id'):
        hierarchy_params.append({
            'level': 'product_quality',
            'params': {
                'product_type_id': product_type['id'],
                'quality_grade_id': quality_grade['id']
            },
            'label': f"{product_type.get('name', 'Termék')} - {quality_grade.get('name', 'Minőség')}",
            'priority': 1
        })
    
    # Termék (ha van termék ID)
    if product_type.get('id'):
        hierarchy_params.append({
            'level': 'product',
            'params': {
                'product_type_id': product_type['id']
            },
            'label': f"{product_type.get('name', 'Termék')} (összes minőség)",
            'priority': 2
        })
    
    # Kategória (ha van kategória ID)
    if product_type.get('category_id'):
        hierarchy_params.append({
            'level': 'category',
            'params': {
                'category_id': product_type['category_id']
            },
            'label': "Kategória átlag",
            'priority': 3
        })
    
    # Időszakok generálása
    periods = generate_time_periods(start_date, end_date, granularity)
    
    logger.info(f"Generated {len(periods)} periods for {granularity} granularity")
    
    # Adatgyűjtés minden hierarchia szinthez
    all_results = []
    
    for hierarchy in hierarchy_params:
        period_data = []
        successful_periods = 0
        
        logger.info(f"Fetching data for hierarchy: {hierarchy['level']}")
        
        for period in periods:
            # API paraméterek összeállítása
            api_params = {
                **hierarchy['params'],
                'date_from': period['start'].strftime('%Y-%m-%d'),
                'date_to': period['end'].strftime('%Y-%m-%d'),
                'status': 'CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED'
                # REMOVED date_field parameter - API does not support it!
            }
            
            # Warning if trying to use non-delivery_date filtering
            if date_type != 'delivery_date':
                logger.warning(f"API doesn't support filtering by {date_type}, using delivery_date instead")
            
            # API hívás - use custom filtering for non-delivery_date fields
            if date_type != 'delivery_date':
                result = get_offers_with_custom_date_filter(api_params, date_type)
            else:
                result = call_statistics_api_with_retry(api_params)
            
            # Adatok feldolgozása
            price = None
            offer_count = 0
            rejection_reason = None
            
            if result and not result.get('error'):
                total_offers = result.get('total_offers', 0)
                average_price = result.get('average_price')
                confirmed_offers = result.get('confirmed_offers_count', 0)
                status_counts = result.get('status_counts', {})
                
                # Részletes diagnosztika
                if total_offers == 0:
                    rejection_reason = f"Nincs ajánlat a megadott kritériumokkal"
                elif not average_price:
                    rejection_reason = f"Van {total_offers} ajánlat, de nincs average_price ({confirmed_offers} confirmed)"
                    if status_counts:
                        rejection_reason += f" | Státuszok: {status_counts}"
                else:
                    try:
                        price = float(average_price)
                        offer_count = int(total_offers)
                        if price > 0 and offer_count > 0:
                            successful_periods += 1
                        elif price == 0:
                            rejection_reason = f"Average price = 0 ({total_offers} ajánlat, {confirmed_offers} confirmed)"
                        else:
                            rejection_reason = f"Invalid price: {price}"
                    except (ValueError, TypeError) as e:
                        rejection_reason = f"Price conversion error: {average_price} -> {str(e)}"
            else:
                error_info = result.get('error') if result else 'No result'
                rejection_reason = f"API error: {error_info}"
            
            period_data.append({
                'period': period,
                'price': price,
                'offer_count': offer_count,
                'api_result': result,
                'rejection_reason': rejection_reason
            })
        
        all_results.append({
            'hierarchy': hierarchy,
            'data': period_data,
            'successful_periods': successful_periods
        })
        
        logger.info(f"Hierarchy {hierarchy['level']}: {successful_periods}/{len(periods)} periods with data")
    
    return process_hierarchical_trend_data(all_results, granularity)

def process_hierarchical_trend_data(all_results: List[Dict], granularity: str) -> Dict[str, Any]:
    """
    Hierarchikus ártrend adatok feldolgozása
    """
    processed_data = {
        'hierarchies': [],
        'periods_info': [],
        'granularity': granularity,
        'data_quality': {}
    }
    
    # Process each hierarchy level
    for result in all_results:
        hierarchy = result['hierarchy']
        data = result['data']
        
        dates = []
        prices = []
        offer_counts = []
        labels = []
        hover_texts = []
        
        for period_data in data:
            period = period_data['period']
            price = period_data['price']
            count = period_data['offer_count']
            
            if price is not None and price > 0:
                dates.append(period['start'])
                prices.append(price)
                offer_counts.append(count)
                labels.append(period['label'])
                hover_texts.append(f"{period['full_label']}<br>{count} ajánlat")
        
        processed_hierarchy = {
            'level': hierarchy['level'],
            'label': hierarchy['label'],
            'priority': hierarchy['priority'],
            'dates': dates,
            'prices': prices,
            'offer_counts': offer_counts,
            'labels': labels,
            'hover_texts': hover_texts,
            'data_points': len(dates)
        }
        
        processed_data['hierarchies'].append(processed_hierarchy)
        processed_data['data_quality'][hierarchy['level']] = {
            'total_periods': len(data),
            'periods_with_data': len(dates),
            'coverage_percent': (len(dates) / len(data)) * 100 if data else 0
        }
    
    # Sort hierarchies by priority
    processed_data['hierarchies'].sort(key=lambda x: x['priority'])
    
    return processed_data

# ============================================================================
# INTELLIGENT TIME PERIOD ANALYSIS
# ============================================================================

def analyze_offer_dates_for_trend(offer: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ajánlat dátumainak intelligens elemzése az optimális ártrend időszak meghatározásához
    """
    try:
        delivery_date = offer.get('delivery_date')
        created_at = offer.get('created_at')
        
        if not delivery_date:
            return None
            
        # Dátumok konvertálása
        delivery_dt = datetime.strptime(delivery_date, '%Y-%m-%d')
        created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00')) if created_at else None
        
        delivery_year = delivery_dt.year
        created_year = created_dt.year if created_dt else delivery_year
        current_year = datetime.now().year
        
        # Év eltérés elemzése
        year_difference = abs(delivery_year - created_year)
        years_from_now = abs(current_year - delivery_year)
        
        # Ajánlás generálása
        recommendation = {
            'delivery_date': delivery_date,
            'delivery_year': delivery_year,
            'created_year': created_year,
            'current_year': current_year,
            'year_difference': year_difference,
            'years_from_now': years_from_now,
            'has_date_mismatch': year_difference > 0,
            'is_historical': years_from_now > 0,
            'analysis': {}
        }
        
        # Optimális időszak meghatározása
        if year_difference == 0:
            # Ugyanabban az évben - egyszerű eset
            if delivery_year == current_year:
                recommendation['recommended_preset'] = "Idei év"
                recommendation['analysis']['status'] = 'optimal'
                recommendation['analysis']['message'] = f"✅ Optimális: Beszállítás és létrehozás {delivery_year}-ben"
            elif delivery_year == current_year - 1:
                recommendation['recommended_preset'] = "Tavalyi év"
                recommendation['analysis']['status'] = 'good'
                recommendation['analysis']['message'] = f"✅ Jó: Beszállítás és létrehozás {delivery_year}-ben"
            else:
                recommendation['recommended_preset'] = f"{delivery_year}. év"
                recommendation['analysis']['status'] = 'historical'
                recommendation['analysis']['message'] = f"📅 Történeti: {delivery_year}. év ajánlott"
        else:
            # Eltérő évek - választani kell
            recommendation['analysis']['status'] = 'conflict'
            recommendation['analysis']['message'] = f"⚠️ Dátum eltérés: Beszállítás {delivery_year}, Létrehozás {created_year}"
            
            # Delivery date előnyben részesítése (üzleti logika)
            if delivery_year == current_year:
                recommendation['recommended_preset'] = "Idei év"
                recommendation['analysis']['recommendation'] = "Beszállítási dátum alapján (idei év)"
            elif delivery_year == current_year - 1:
                recommendation['recommended_preset'] = "Tavalyi év"
                recommendation['analysis']['recommendation'] = "Beszállítási dátum alapján (tavalyi év)"
            else:
                recommendation['recommended_preset'] = f"{delivery_year}. év"
                recommendation['analysis']['recommendation'] = f"Beszállítási dátum alapján ({delivery_year}. év)"
        
        return recommendation
        
    except Exception as e:
        logger.error(f"Error analyzing offer dates: {str(e)}")
        return None

def render_smart_period_suggestion(suggestion: Dict[str, Any]):
    """
    Intelligens időszak javaslat megjelenítése
    """
    analysis = suggestion.get('analysis', {})
    status = analysis.get('status', 'unknown')
    
    # Státusz alapú színek és ikonok
    if status == 'optimal':
        st.success(f"🎯 **Intelligens javaslat**: {analysis.get('message', '')}")
    elif status == 'good':
        st.info(f"💡 **Ajánlás**: {analysis.get('message', '')}")
    elif status == 'historical':
        st.warning(f"📅 **Történeti elemzés**: {analysis.get('message', '')}")
    elif status == 'conflict':
        st.warning(f"⚠️ **Dátum konfliktus észlelve**")
        
        # Részletes konfliktus információ - FIXED: No nested expander, use checkbox
        st.markdown("**🔍 Részletes dátum elemzés:**")
        show_date_details = st.checkbox("Mutasd a részletes dátum elemzést", key="show_date_analysis")
        
        if show_date_details:
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**📅 Ajánlat dátumok:**")
                st.write(f"🚚 Beszállítás: {suggestion['delivery_date']} ({suggestion['delivery_year']})")
                st.write(f"📝 Létrehozás: {suggestion['created_year']} év")
                st.write(f"⏰ Év eltérés: {suggestion['year_difference']} év")
            
            with col2:
                st.markdown("**💡 Ajánlás:**")
                st.write(f"**Ártrend elemzéshez használd**: {analysis.get('recommendation', 'Beszállítási dátum alapján')}")
                st.write(f"**Javasolt időszak**: {suggestion.get('recommended_preset', 'Egyéni')}")
                st.info("A beszállítási dátum általában pontosabb az ártrend elemzéshez")

def get_preset_dates_smart(preset: str, suggestion: Dict[str, Any] = None) -> Tuple[datetime, datetime]:
    """
    Előre definiált időszakok dátumainak visszaadása - intelligens javaslatokkal
    """
    now = datetime.now()
    
    # Intelligens javaslat kezelése
    if suggestion and preset == suggestion.get('recommended_preset'):
        delivery_year = suggestion.get('delivery_year')
        if delivery_year and f"{delivery_year}. év" in preset:
            return datetime(delivery_year, 1, 1), datetime(delivery_year, 12, 31)
    
    # Standard preset logika
    if preset == "Elmúlt 7 nap":
        return now - timedelta(days=7), now
    elif preset == "Elmúlt 30 nap":
        return now - timedelta(days=30), now
    elif preset == "Elmúlt 3 hónap":
        return now - timedelta(days=90), now
    elif preset == "Elmúlt 6 hónap":
        return now - timedelta(days=180), now
    elif preset == "Elmúlt 1 év":
        return now - timedelta(days=365), now
    elif preset == "Idei év":
        return datetime(now.year, 1, 1), now
    elif preset == "Tavalyi év":
        last_year = now.year - 1
        return datetime(last_year, 1, 1), datetime(last_year, 12, 31)
    else:  # Egyéni időszak vagy custom
        return now - timedelta(days=90), now

# ============================================================================
# ADVANCED UI CONTROLS
# ============================================================================

def render_advanced_price_trend_controls(offer: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Fejlett kontrollok az ártrend elemzéshez - INTELLIGENS IDŐSZAK JAVASLATTAL
    """
    st.markdown("### 📊 Ártrend elemzés beállításai")
    
    # Intelligens időszak elemzés
    smart_period_suggestion = None
    if offer:
        smart_period_suggestion = analyze_offer_dates_for_trend(offer)
        if smart_period_suggestion:
            render_smart_period_suggestion(smart_period_suggestion)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Előre definiált időszakok + intelligens javaslat
        preset_options = [
            "Elmúlt 7 nap",
            "Elmúlt 30 nap", 
            "Elmúlt 3 hónap",
            "Elmúlt 6 hónap",
            "Elmúlt 1 év",
            "Idei év",
            "Tavalyi év",
            "Egyéni időszak"
        ]
        
        # Ha van intelligens javaslat, add hozzá
        if smart_period_suggestion and smart_period_suggestion.get('recommended_preset'):
            recommended = smart_period_suggestion['recommended_preset']
            if recommended not in preset_options:
                preset_options.insert(0, recommended)
        
        preset = st.selectbox(
            "⏱️ Gyors választás",
            preset_options,
            index=0 if smart_period_suggestion else 2  # Smart javaslat vagy Elmúlt 3 hónap
        )
        
        # Egyéni időszak választó
        if preset == "Egyéni időszak":
            date_range = st.date_input(
                "📅 Időszak",
                value=(
                    datetime.now() - timedelta(days=90),
                    datetime.now()
                ),
                max_value=datetime.now(),
                help="Válassz kezdő és záró dátumot"
            )
            if isinstance(date_range, tuple) and len(date_range) == 2:
                start_date, end_date = date_range
            else:
                start_date, end_date = get_preset_dates("Elmúlt 3 hónap")
        else:
            start_date, end_date = get_preset_dates_smart(preset, smart_period_suggestion)
    
    with col2:
        # Felbontás választó - dinamikusan az időszak alapján
        available_granularities = get_available_granularities(start_date, end_date)
        
        granularity_labels = {
            'daily': '📅 Napi',
            'weekly': '📆 Heti', 
            'monthly': '🗓️ Havi',
            'quarterly': '📊 Negyedéves',
            'yearly': '📈 Éves'
        }
        
        granularity = st.selectbox(
            "📈 Felbontás",
            options=available_granularities,
            format_func=lambda x: granularity_labels.get(x, x),
            help="Az időszak hossza alapján automatikusan szűrt opciók"
        )
    
    with col3:
        # Dátum típus választó
        date_type = st.selectbox(
            "📅 Dátum alapja",
            ["delivery_date", "confirmed_at", "created_at"],
            index=0,  # Default: delivery_date
            format_func=lambda x: {
                "delivery_date": "🚚 Beszállítási dátum",
                "confirmed_at": "✅ Visszaigazolási dátum", 
                "created_at": "📝 Létrehozási dátum"
            }.get(x, x),
            help="Melyik dátum szerint csoportosítsa az árakat"
        )
        
        # Megjelenítési opciók
        st.markdown("**👁️ Megjelenítés**")
        
        show_hierarchy = st.multiselect(
            "Hierarchia szintek",
            ["Termék+Minőség", "Termék", "Kategória"],
            default=["Termék+Minőség"],
            help="Válaszd ki a megjelenítendő árszinteket"
        )
        
        show_volume = st.checkbox("📊 Volumen megjelenítése", value=True)
        show_markers = st.checkbox("📍 Saját ajánlatok jelölése", value=True)
    
    # Haladó beállítások - NO EXPANDER (avoid nesting)
    st.markdown("---")
    st.markdown("#### 🔧 Haladó beállítások")
    col1, col2 = st.columns(2)
    
    with col1:
        aggregation = st.radio(
            "Aggregáció típusa",
            ["Súlyozott átlag", "Egyszerű átlag", "Medián", "Min/Max"],
            help="Súlyozott átlag: mennyiséggel súlyozott árátlag"
        )
        
        smoothing = st.selectbox(
            "Simítás",
            ["Nincs", "Mozgóátlag (3)", "Mozgóátlag (5)", "Trend vonal"],
            help="Adatsor simítása a trend jobb láthatóságáért"
        )
    
    with col2:
        min_offers = st.number_input(
            "Minimum ajánlatszám",
            min_value=0,
            value=1,
            help="Csak azok az időszakok jelennek meg, ahol legalább ennyi ajánlat van"
        )
        
        show_confidence = st.checkbox(
            "Konfidencia sáv",
            value=False,
            help="Megbízhatósági sáv megjelenítése az adatok minősége alapján"
        )
    
    # Exportálási opciók - NO EXPANDER (avoid nesting)
    st.markdown("---")
    st.markdown("#### 💾 Export és összehasonlítás")
    col1, col2 = st.columns(2)
    
    with col1:
        export_format = st.selectbox(
            "Export formátum",
            ["Nincs", "CSV", "Excel", "PNG", "PDF"]
        )
    
    with col2:
        compare_period = st.selectbox(
            "Összehasonlítás",
            ["Nincs", "Előző időszak", "Tavalyi ugyanez", "Egyéni"]
        )
    
    return {
        'start_date': start_date,
        'end_date': end_date,
        'granularity': granularity,
        'date_type': date_type,
        'show_hierarchy': show_hierarchy,
        'show_volume': show_volume,
        'show_markers': show_markers,
        'aggregation': aggregation,
        'smoothing': smoothing,
        'min_offers': min_offers,
        'show_confidence': show_confidence,
        'export_format': export_format,
        'compare_period': compare_period,
        'preset': preset
    }

# ============================================================================
# HIERARCHICAL TREND VISUALIZATION
# ============================================================================

def render_hierarchical_price_trend(trend_data: Dict[str, Any], settings: Dict[str, Any]) -> go.Figure:
    """
    Hierarchikus ártrend grafikon renderelése
    """
    # Determine number of subplots
    num_rows = 2 if settings['show_volume'] else 1
    row_heights = [0.7, 0.3] if settings['show_volume'] else [1]
    
    fig = make_subplots(
        rows=num_rows,
        cols=1,
        row_heights=row_heights,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=(
            "Ártrend",
            "Ajánlatok száma" if settings['show_volume'] else None
        )
    )
    
    # Színek és stílusok hierarchia szerint
    styles = {
        'product_quality': {'color': '#0099e0', 'width': 3, 'dash': 'solid'},
        'product': {'color': '#ff8c1a', 'width': 2, 'dash': 'dash'},
        'category': {'color': '#10dc60', 'width': 1, 'dash': 'dot'}
    }
    
    # Mapping for hierarchy display names
    hierarchy_mapping = {
        'product_quality': 'Termék+Minőség',
        'product': 'Termék',
        'category': 'Kategória'
    }
    
    # Track if we have any data
    has_data = False
    
    # Ártrend vonalak
    for hierarchy_data in trend_data['hierarchies']:
        hierarchy_level = hierarchy_data['level']
        hierarchy_display = hierarchy_mapping.get(hierarchy_level, hierarchy_level)
        
        if hierarchy_display in settings['show_hierarchy'] and hierarchy_data['data_points'] > 0:
            style = styles.get(hierarchy_level, {'color': '#888888', 'width': 1, 'dash': 'solid'})
            
            # Apply smoothing if requested
            dates = hierarchy_data['dates']
            prices = hierarchy_data['prices']
            
            if settings['smoothing'] != "Nincs" and len(prices) > 2:
                prices = apply_smoothing(prices, settings['smoothing'])
            
            # Filter by minimum offers if specified
            if settings['min_offers'] > 1:
                filtered_data = [(d, p, c, h) for d, p, c, h in zip(
                    dates, prices, hierarchy_data['offer_counts'], hierarchy_data['hover_texts']
                ) if c >= settings['min_offers']]
                
                if filtered_data:
                    dates, prices, offer_counts, hover_texts = zip(*filtered_data)
                else:
                    continue
            else:
                hover_texts = hierarchy_data['hover_texts']
            
            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=prices,
                    name=hierarchy_data['label'],
                    mode='lines+markers',
                    line=dict(
                        color=style['color'],
                        width=style['width'],
                        dash=style['dash']
                    ),
                    marker=dict(
                        size=6,
                        color=style['color']
                    ),
                    hovertemplate=f'<b>{hierarchy_data["label"]}</b><br>' +
                                 '%{text}<br>' +
                                 'Ár: %{y:,.0f} Ft/kg<extra></extra>',
                    text=hover_texts,
                    connectgaps=False
                ),
                row=1, col=1
            )
            
            has_data = True
    
    # Volumen oszlopdiagram
    if settings['show_volume'] and has_data:
        # Use the first hierarchy with data for volume display
        for hierarchy_data in trend_data['hierarchies']:
            if hierarchy_data['data_points'] > 0:
                fig.add_trace(
                    go.Bar(
                        x=hierarchy_data['dates'],
                        y=hierarchy_data['offer_counts'],
                        name='Ajánlatok száma',
                        marker_color='rgba(128, 128, 128, 0.5)',
                        hovertemplate='Ajánlatok: %{y} db<br>%{x}<extra></extra>',
                        showlegend=False
                    ),
                    row=2, col=1
                )
                break
    
    # Layout beállítások
    title = f"📈 Ártrend elemzés: {settings['start_date'].strftime('%Y.%m.%d')} - {settings['end_date'].strftime('%Y.%m.%d')}"
    if settings['granularity']:
        granularity_labels = {
            'daily': 'napi',
            'weekly': 'heti',
            'monthly': 'havi',
            'quarterly': 'negyedéves',
            'yearly': 'éves'
        }
        title += f" ({granularity_labels.get(settings['granularity'], settings['granularity'])} bontás)"
    
    # Dátum típus jelzése
    if settings.get('date_type'):
        date_type_labels = {
            "delivery_date": "beszállítási dátum",
            "confirmed_at": "visszaigazolási dátum", 
            "created_at": "létrehozási dátum"
        }
        date_label = date_type_labels.get(settings['date_type'], settings['date_type'])
        title += f", {date_label}"
    
    fig.update_layout(
        title=dict(
            text=title,
            font=dict(color='#ffffff', size=16)
        ),
        hovermode='x unified',
        plot_bgcolor='#1a1a1a',
        paper_bgcolor='#1a1a1a',
        font=dict(color='#ffffff'),
        showlegend=True,
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01,
            bgcolor='rgba(0,0,0,0.5)'
        ),
        height=500 if settings['show_volume'] else 400
    )
    
    # X-axis formatting
    fig.update_xaxes(
        gridcolor='#2a2a2a',
        title='Időszak' if not settings['show_volume'] else '',
        tickangle=-45 if settings['granularity'] == 'daily' else 0
    )
    
    # Y-axis formatting
    fig.update_yaxes(
        gridcolor='#2a2a2a',
        title='Egységár (Ft/kg)',
        tickformat=',.0f',
        row=1, col=1
    )
    
    if settings['show_volume']:
        fig.update_yaxes(
            gridcolor='#2a2a2a',
            title='Ajánlatok száma (db)',
            row=2, col=1
        )
    
    return fig

def apply_smoothing(prices: List[float], smoothing_type: str) -> List[float]:
    """
    Simítás alkalmazása az árakat tartalmazó listára
    """
    if smoothing_type == "Nincs" or len(prices) < 3:
        return prices
    
    if smoothing_type == "Mozgóátlag (3)":
        return pd.Series(prices).rolling(window=3, center=True).mean().fillna(method='bfill').fillna(method='ffill').tolist()
    elif smoothing_type == "Mozgóátlag (5)":
        return pd.Series(prices).rolling(window=5, center=True).mean().fillna(method='bfill').fillna(method='ffill').tolist()
    elif smoothing_type == "Trend vonal":
        # Simple linear trend
        import numpy as np
        x = np.arange(len(prices))
        z = np.polyfit(x, prices, 1)
        p = np.poly1d(z)
        return p(x).tolist()
    
    return prices

# ============================================================================
# DATA QUALITY AND EXPORT FUNCTIONS
# ============================================================================

def render_data_quality_info(trend_data: Dict[str, Any]):
    """
    Adatminőség információk megjelenítése
    """
    st.markdown("#### 📊 Adatminőség")
    
    quality_data = trend_data.get('data_quality', {})
    
    if not quality_data:
        st.warning("Nincs adatminőség információ")
        return
    
    cols = st.columns(len(quality_data))
    
    hierarchy_labels = {
        'product_quality': 'Termék+Minőség',
        'product': 'Termék',
        'category': 'Kategória'
    }
    
    for i, (level, quality) in enumerate(quality_data.items()):
        with cols[i]:
            label = hierarchy_labels.get(level, level)
            coverage = quality['coverage_percent']
            
            # Color based on coverage
            if coverage >= 80:
                color = "🟢"
            elif coverage >= 50:
                color = "🟡"
            else:
                color = "🔴"
            
            st.metric(
                f"{color} {label}",
                f"{quality['periods_with_data']}/{quality['total_periods']}",
                f"{coverage:.1f}% lefedettség"
            )

def export_trend_data(trend_data: Dict[str, Any], settings: Dict[str, Any], format_type: str):
    """
    Ártrend adatok exportálása
    """
    if format_type == "Nincs":
        return
    
    # Prepare data for export
    export_data = []
    
    for hierarchy in trend_data['hierarchies']:
        for i, date in enumerate(hierarchy['dates']):
            export_data.append({
                'Dátum': date.strftime('%Y-%m-%d'),
                'Hierarchia': hierarchy['label'],
                'Ár (Ft/kg)': hierarchy['prices'][i],
                'Ajánlatok száma': hierarchy['offer_counts'][i],
                'Időszak címke': hierarchy['labels'][i]
            })
    
    if not export_data:
        st.warning("Nincs exportálható adat")
        return
    
    df = pd.DataFrame(export_data)
    
    if format_type == "CSV":
        csv = df.to_csv(index=False, encoding='utf-8')
        st.download_button(
            label="📄 CSV letöltése",
            data=csv,
            file_name=f"artrend_{settings['start_date'].strftime('%Y%m%d')}_{settings['end_date'].strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
    
    elif format_type == "Excel":
        import io
        buffer = io.BytesIO()
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Ártrend')
        
        st.download_button(
            label="📊 Excel letöltése",
            data=buffer.getvalue(),
            file_name=f"artrend_{settings['start_date'].strftime('%Y%m%d')}_{settings['end_date'].strftime('%Y%m%d')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

# ============================================================================
# MAIN INTEGRATION FUNCTION
# ============================================================================

def render_advanced_price_trend_analysis(offer: Dict[str, Any]):
    """
    Teljes fejlett ártrend elemzés renderelése
    """
    st.markdown("#### 📈 Fejlett Ártrend Elemzés")
    
    try:
        # Kontrollok renderelése - offer átadással az intelligens javaslatokhoz
        settings = render_advanced_price_trend_controls(offer)
        
        # Debug info ELŐTTE (beállításokkal)
        render_debug_info(offer, settings)
        
        if st.button("🚀 Elemzés indítása", type="primary"):
            with st.spinner('📊 Ártrend adatok lekérése és feldolgozása...'):
                # Adatok lekérése intelligens fallback-kel
                trend_data = get_price_trend_data_with_smart_fallback(
                    offer=offer,
                    start_date=settings['start_date'],
                    end_date=settings['end_date'],
                    granularity=settings['granularity'],
                    date_type=settings['date_type']
                )
                
                # Debug info UTÁNA (az adatokkal)
                render_debug_info(offer, settings, trend_data)
                
                # Adatminőség ellenőrzése
                has_data = any(h['data_points'] > 0 for h in trend_data['hierarchies'])
                
                if not has_data:
                    st.warning("📈 Nincs adat a megadott időszakra és feltételekre")
                    st.info("""
                    💡 **Javaslatok:**
                    - Próbálj hosszabb időszakot
                    - Csökkentsd a minimum ajánlatszámot
                    - Válassz más felbontást
                    - Ellenőrizd a hierarchia szinteket
                    """)
                    return
                
                # Grafikon renderelése
                fig = render_hierarchical_price_trend(trend_data, settings)
                st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': True})
                
                # Adatminőség információk
                render_data_quality_info(trend_data)
                
                # Export opciók
                if settings['export_format'] != "Nincs":
                    export_trend_data(trend_data, settings, settings['export_format'])
                
                # Összefoglaló statisztikák
                render_trend_summary_stats(trend_data, settings)
                
    except Exception as e:
        logger.error(f"Error in advanced price trend analysis: {str(e)}")
        st.error(f"Hiba az ártrend elemzés során: {str(e)}")
        
        # Debug információ fejlesztőknek
        if st.checkbox("🔍 Debug információ", value=False):
            st.code(f"Error details: {str(e)}")
            import traceback
            st.code(traceback.format_exc())

def render_trend_summary_stats(trend_data: Dict[str, Any], settings: Dict[str, Any]):
    """
    Trend összefoglaló statisztikák
    """
    st.markdown("#### 📊 Összefoglaló statisztikák")
    
    # Find the best hierarchy level with data
    best_hierarchy = None
    for hierarchy in trend_data['hierarchies']:
        if hierarchy['data_points'] > 0:
            best_hierarchy = hierarchy
            break
    
    if not best_hierarchy:
        return
    
    prices = best_hierarchy['prices']
    if len(prices) < 2:
        st.info("Nincs elegendő adat a statisztikákhoz")
        return
    
    # Calculate statistics
    first_price = prices[0]
    last_price = prices[-1]
    min_price = min(prices)
    max_price = max(prices)
    avg_price = sum(prices) / len(prices)
    
    change_absolute = last_price - first_price
    change_percent = (change_absolute / first_price) * 100 if first_price > 0 else 0
    
    # Display in columns
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Kezdő ár",
            f"{first_price:,.0f} Ft/kg",
            help="Az időszak első áradata"
        )
    
    with col2:
        st.metric(
            "Záró ár", 
            f"{last_price:,.0f} Ft/kg",
            delta=f"{change_absolute:+,.0f} Ft/kg",
            help="Az időszak utolsó áradata"
        )
    
    with col3:
        st.metric(
            "Változás",
            f"{change_percent:+.1f}%",
            help="Százalékos változás a kezdő és záró ár között"
        )
    
    with col4:
        st.metric(
            "Min - Max",
            f"{min_price:,.0f} - {max_price:,.0f}",
            help="Legalacsonyabb és legmagasabb ár az időszakban"
        )
    
    # Trend analysis
    if len(prices) >= 3:
        st.markdown("#### 📈 Trend elemzés")
        
        # Simple trend calculation
        import numpy as np
        x = np.arange(len(prices))
        slope, intercept = np.polyfit(x, prices, 1)
        
        if abs(slope) < 1:
            trend_desc = "Stabil"
            trend_emoji = "📊"
        elif slope > 0:
            trend_desc = "Emelkedő"
            trend_emoji = "📈"
        else:
            trend_desc = "Csökkenő"
            trend_emoji = "📉"
        
        st.info(f"{trend_emoji} **Trend**: {trend_desc} ({slope:+.2f} Ft/kg per időszak)")


def render_debug_info(offer: Dict[str, Any], settings: Dict[str, Any], trend_data: Dict[str, Any] = None):
    """Debug információk megjelenítése fejlett nézetben"""
    
    if not st.session_state.get("debug_mode", False):
        return
        
    st.info("🔍 Debug információk - Fejlett ártrend elemzés")
    
    # Időszak információk
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("**📅 Időszak adatok:**")
        st.write(f"- Kezdő dátum: {settings['start_date'].strftime('%Y-%m-%d')}")
        st.write(f"- Záró dátum: {settings['end_date'].strftime('%Y-%m-%d')}")
        st.write(f"- Napok száma: {(settings['end_date'] - settings['start_date']).days}")
        st.write(f"- Felbontás: {settings['granularity']}")
        st.write(f"- Dátum mező: {settings['date_type']}")
        
    with col2:
        st.markdown("**🏗️ Hierarchia szintek:**")
        product_type = offer.get('product_type', {}) or {}
        quality_grade = offer.get('quality_grade', {}) or {}
        
        hierarchies = []
        if product_type.get('id') and quality_grade.get('id'):
            hierarchies.append(f"✅ Termék+Minőség (ID: {product_type['id']}/{quality_grade['id']})")
        if product_type.get('id'):
            hierarchies.append(f"✅ Termék (ID: {product_type['id']})")
        if product_type.get('category_id'):
            hierarchies.append(f"✅ Kategória (ID: {product_type['category_id']})")
            
        for h in hierarchies:
            st.write(h)
            
    with col3:
        st.markdown("**⚙️ Beállítások:**")
        st.write(f"- Min. ajánlatszám: {settings['min_offers']}")
        st.write(f"- Aggregáció: {settings['aggregation']}")
        st.write(f"- Simítás: {settings['smoothing']}")
        active_hierarchies = len(settings['show_hierarchy'])
        st.write(f"- Aktív hierarchiák: {active_hierarchies}")
    
    # Időszakok generálása és API hívások száma
    if trend_data:
        st.markdown("**📊 Feldolgozási statisztikák:**")
        
        total_periods = 0
        total_api_calls = 0
        successful_calls = 0
        
        for hierarchy in trend_data.get('hierarchies', []):
            level_periods = len(hierarchy.get('dates', []))
            total_periods = max(total_periods, level_periods)
            
        # Számítsuk ki az API hívások számát
        num_hierarchies = len(trend_data.get('hierarchies', []))
        periods = generate_time_periods(settings['start_date'], settings['end_date'], settings['granularity'])
        total_api_calls = num_hierarchies * len(periods)
        
        # Sikeres hívások száma
        for hierarchy in trend_data.get('hierarchies', []):
            successful_calls += hierarchy.get('data_points', 0)
            
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Időszakok száma", len(periods))
        with col2:
            st.metric("API hívások", total_api_calls)
        with col3:
            success_rate = (successful_calls / total_api_calls * 100) if total_api_calls > 0 else 0
            st.metric("Sikeres válasz", f"{success_rate:.0f}%")
        with col4:
            total_data_points = sum(h.get('data_points', 0) for h in trend_data.get('hierarchies', []))
            st.metric("Adatpontok", total_data_points)
        
        # RÉSZLETES HIBAANALÍZIS - miért lettek kizárva az ajánlatok
        if success_rate < 50:  # Ha kevesebb mint 50% sikeres
            st.markdown("---")
            st.markdown("**🔍 Részletes hibaanalízis - Miért lettek kizárva az ajánlatok?**")
            
            # Gyűjtsük össze az összes rejection reason-t
            rejection_summary = {}
            total_rejections = 0
            
            # Iteráljunk végig minden hierarchián és időszakon
            for hierarchy_data in trend_data.get('hierarchies', []):
                hierarchy_level = hierarchy_data.get('level', 'unknown')
                
                # Keressük meg az eredeti adatokat a process során
                # Ez egy workaround, mert a processed data már nem tartalmazza a rejection reason-eket
                # Ideális esetben ezt a raw data-ból kellene kinyerni
                
                st.write(f"**📋 {hierarchy_data.get('label', hierarchy_level)} hierarchia:**")
                
                periods_with_data = hierarchy_data.get('data_points', 0)
                total_periods = len(periods)
                failed_periods = total_periods - periods_with_data
                
                if failed_periods > 0:
                    st.write(f"- ❌ **{failed_periods}/{total_periods} időszak sikertelen**")
                    st.write(f"- ✅ {periods_with_data} időszak sikeres")
                    
                    # Mutassuk meg a lehetséges okokat
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write("**Lehetséges okok:**")
                        st.write("• Nincs ajánlat az időszakban")
                        st.write("• Ajánlatok nem megfelelő státuszban")
                        st.write("• Nincs confirmed_price érték")
                    
                    with col2:
                        st.write("**Ellenőrizd:**")
                        st.write(f"• product_type_id: {product_type.get('id')}")
                        if quality_grade.get('id'):
                            st.write(f"• quality_grade_id: {quality_grade.get('id')}")
                        st.write(f"• Státusz szűrés: CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED")
                        st.write(f"• Dátum típus: {settings.get('date_type', 'delivery_date')}")
                else:
                    st.write(f"- ✅ **Minden időszak sikeres** ({periods_with_data}/{total_periods})")
            
            # Javaslatok a javításhoz
            st.markdown("**💡 Javasolt megoldások:**")
            
            if total_data_points == 0:
                st.error("**Kritikus: Egyáltalán nincs adat!**")
                st.write("1. **Ellenőrizd az ajánlatok státuszát**: Vannak-e CONFIRMED_BY_COMPANY/ACCEPTED_BY_USER/FINALIZED státuszú ajánlatok?")
                st.write("2. **Ellenőrizd a termék ID-kat**: Létezik-e valóban ilyen termék/minőségi osztály kombináció?")
                st.write("3. **Próbálj kategória szintű keresést**: Kapcsold ki a konkrét terméket, használj csak kategóriát")
                st.write("4. **Ellenőrizd az időszakot**: Lehet túl szűk az időintervallum?")
            else:
                st.warning("**Részleges adat - optimalizálható**")
                st.write("1. **Bővítsd az időszakot**: Próbálj hosszabb időintervallumot")
                st.write("2. **Csökkentsd a minimum ajánlatszámot**: Állítsd 0-ra a min. ajánlatszám filtert")
                st.write("3. **Próbáld a kategória szintű elemzést**: Kevésbé specifikus, de több adat")
        
        # API paraméter példa
        st.markdown("**🔧 API hívás példa:**")
        if st.checkbox("Mutasd az API hívás példát", key="show_api_example"):
            if periods:
                sample_period = periods[0]
                sample_params = {
                    'product_type_id': product_type.get('id'),
                    'quality_grade_id': quality_grade.get('id'),
                    'date_from': sample_period['start'].strftime('%Y-%m-%d'),
                    'date_to': sample_period['end'].strftime('%Y-%m-%d'),
                    'status': 'CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED'
                }
                # FIXED: Show actual API behavior and limitations
                if settings['date_type'] == 'delivery_date':
                    api_request_info = {
                        'api_params': sample_params,
                        'filtering_method': 'API native (delivery_date)',
                        'explanation': f"Az API natívan támogatja a delivery_date szűrést"
                    }
                    filter_info = f"""
                    ✅ **API natív szűrés**: A backend API közvetlenül szűr delivery_date alapján.
                    
                    API végpont: `/api/offers/statistics`
                    Támogatott paraméterek: date_from, date_to (delivery_date-re vonatkozik)
                    """
                else:
                    api_request_info = {
                        'api_params': sample_params,
                        'filtering_method': f'Manual filtering ({settings["date_type"]})',
                        'explanation': f"Manuális szűrés {settings['date_type']} alapján, mert az API csak delivery_date-t támogat"
                    }
                    filter_info = f"""
                    ⚠️ **Manuális szűrés**: Az API NEM támogatja a {settings['date_type']} szerinti szűrést!
                    
                    Megoldás:
                    1. API hívás minden ajánlatra (product_type_id, quality_grade_id, status alapján)
                    2. Frontend szűrés {settings['date_type']} dátum mező alapján
                    3. Átlagár számítása a szűrt eredményekből
                    
                    ⚡ Teljesítmény: Lassabb, mert több adat letöltése szükséges
                    """
                
                st.json(api_request_info)
                st.info(filter_info)
                
                # LIVE API TESZT - egy konkrét időszakra
                st.markdown("**🧪 Live API teszt egy időszakra:**")
                if st.button("🔍 Tesztelj egy konkrét időszakot", key="test_single_period"):
                    if periods:
                        test_period = periods[0]  # Első időszak
                        test_params = {
                            'product_type_id': product_type.get('id'),
                            'quality_grade_id': quality_grade.get('id'),
                            'date_from': test_period['start'].strftime('%Y-%m-%d'),
                            'date_to': test_period['end'].strftime('%Y-%m-%d'),
                            'status': 'CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED'
                        }
                        
                        st.write(f"**Teszt időszak**: {test_period['full_label']}")
                        st.json(test_params)
                        
                        # Tényleges API hívás
                        with st.spinner('API hívás folyamatban...'):
                            if settings['date_type'] != 'delivery_date':
                                test_result = get_offers_with_custom_date_filter(test_params, settings['date_type'])
                            else:
                                test_result = call_statistics_api_with_retry(test_params)
                        
                        st.write("**API válasz:**")
                        st.json(test_result)
                        
                        # Elemzés
                        if test_result:
                            total_offers = test_result.get('total_offers', 0)
                            average_price = test_result.get('average_price')
                            confirmed_offers = test_result.get('confirmed_offers_count', 0)
                            status_counts = test_result.get('status_counts', {})
                            
                            if total_offers == 0:
                                st.error("❌ **Nincs ajánlat**: A megadott kritériumokkal nem található ajánlat ebben az időszakban")
                            elif not average_price:
                                st.warning(f"⚠️ **Nincs ár**: {total_offers} ajánlat van, de nincs average_price")
                                if status_counts:
                                    st.write(f"Státusz bontás: {status_counts}")
                                if confirmed_offers != total_offers:
                                    st.write(f"Ebből {confirmed_offers} confirmed státuszban van")
                            else:
                                st.success(f"✅ **Sikeres**: {total_offers} ajánlat, átlagár: {average_price} Ft/kg")
                        else:
                            st.error("❌ **API hiba**: Nincs válasz az API-tól")
        
        # Adatminőség részletes elemzése
        if 'data_quality' in trend_data:
            st.markdown("**📈 Adatminőség hierarchiánként:**")
            
            quality_df_data = []
            for level, quality in trend_data['data_quality'].items():
                quality_df_data.append({
                    'Hierarchia': level,
                    'Összes időszak': quality['total_periods'],
                    'Adattal rendelkező': quality['periods_with_data'],
                    'Lefedettség %': f"{quality['coverage_percent']:.1f}%",
                    'Hiányzó időszakok': quality['total_periods'] - quality['periods_with_data']
                })
                
            import pandas as pd
            df = pd.DataFrame(quality_df_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
    
    # API Response struktúra debug (részletesebb a fejlett nézetben)
    if not trend_data:  # Csak a beállítások megjelenítésekor
        st.markdown("---")
        st.markdown("### 🔍 Offer objektum teljes debug")
        if st.checkbox("Mutasd az offer objektum debug adatokat", key="show_offer_debug"):
            st.write("**Teljes offer objektum:**")
            
            # Alapadatok
            col1, col2 = st.columns(2)
            with col1:
                st.write("**🔑 Azonosítók és státusz:**")
                ids_status = {
                    'offer_id': offer.get('id'),
                    'user_id': offer.get('user_id'),
                    'product_type_id': offer.get('product_type_id'),
                    'quality_grade_id': offer.get('quality_grade_id'),
                    'status': offer.get('status'),
                    'status_display': offer.get('status_display')
                }
                st.json(ids_status)
                
                st.write("**💰 Üzleti adatok:**")
                business_data = {
                    'confirmed_price': offer.get('confirmed_price'),
                    'confirmed_quantity': offer.get('confirmed_quantity'),
                    'delivery_date': offer.get('delivery_date'),
                    'created_at': offer.get('created_at'),
                    'updated_at': offer.get('updated_at')
                }
                st.json(business_data)
            
            with col2:
                st.write("**👤 User objektum:**")
                user_obj = offer.get('user', {})
                if user_obj:
                    user_debug = {
                        'id': user_obj.get('id'),
                        'role': user_obj.get('role'),
                        'contact_name': user_obj.get('contact_name'),
                        'company_name': user_obj.get('company_name'),
                        'email': user_obj.get('email'),
                        'is_active': user_obj.get('is_active')
                    }
                    st.json(user_debug)
                else:
                    st.warning("User objektum hiányzik")
                
                st.write("**🌾 Product Type objektum:**")
                product_type_obj = offer.get('product_type', {})
                if product_type_obj:
                    product_debug = {
                        'id': product_type_obj.get('id'),
                        'name': product_type_obj.get('name'),
                        'category_id': product_type_obj.get('category_id'),
                        'has_quality_grades': product_type_obj.get('has_quality_grades'),
                        'description': product_type_obj.get('description')
                    }
                    st.json(product_debug)
                else:
                    st.warning("Product Type objektum hiányzik")
                
                st.write("**🏅 Quality Grade objektum:**")
                quality_grade_obj = offer.get('quality_grade')
                if quality_grade_obj:
                    st.json(quality_grade_obj)
                else:
                    st.info("Quality Grade: NULL (normális lehet)")
            
            # Kulcsok listája
            st.write("**🔍 Összes kulcs az offer objektumban:**")
            all_keys = list(offer.keys()) if offer else []
            st.write(f"Összesen {len(all_keys)} kulcs:")
            st.code(', '.join(all_keys))