"""
Nutrition Facts stílusú DetailContainer komponens - v2 with st.html() support
A CodePen nutrition facts design alapján adaptált ajánlat megjelenítéshez
"""
import streamlit as st
import uuid
import logging
import html
from datetime import datetime

# Import our universal HTML renderer
try:
    from .html_renderer import render_html, render_css
except ImportError:
    from html_renderer import render_html, render_css

logger = logging.getLogger(__name__)

class NutritionFactsContainer:
    """
    Nutrition Facts stílusú konténer komponens
    
    A nutrition facts label design-ját adaptálja ajánlat adatok megjelenítéséhez.
    Tiszta, táblázatos, professzionális megjelenés.
    Automatikusan használja st.html()-t ha elérhető.
    """
    
    def __init__(self, title="Ajánlat Részletek", subtitle="", icon="📋", key=None):
        """
        Inicializálja a nutrition facts stílus<PERSON> kont<PERSON>.
        
        Args:
            title (str): Főcím (pl. "Ajánlat Részletek")
            subtitle (str): Alcím (pl. "Ajánlat #9")
            icon (str): Emoji ikon
            key (str, optional): Egyedi azonosító
        """
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.key = key or f"nutrition_facts_{str(uuid.uuid4())[:8]}"
        
        # HTML escape a biztonság kedvéért
        self.safe_title = html.escape(str(title))
        self.safe_subtitle = html.escape(str(subtitle))
        self.safe_icon = html.escape(str(icon))
    
    def render(self, offer_data, debug_mode=False):
        """
        Nutrition facts stílusú ajánlat megjelenítés
        
        Args:
            offer_data (dict): Az ajánlat adatai
            debug_mode (bool): Debug mód engedélyezése
        """
        try:
            if debug_mode:
                st.checkbox(f"🔍 Debug {self.title}", key=f"debug_{self.key}", value=True)
                with st.expander("Debug Info", expanded=False):
                    st.write("**Offer Data Keys:**", list(offer_data.keys()) if offer_data else [])
                    st.write("**Container Key:**", self.key)
                    
                    # Check st.html availability
                    from html_renderer import check_st_html_availability
                    st.write("**st.html() status:**", check_st_html_availability())
            
            # Generate complete HTML with inline CSS
            html_content = self._generate_complete_nutrition_html(offer_data)
            
            # Use our universal renderer
            used_st_html = render_html(html_content, height=700)
            
            if debug_mode:
                if used_st_html:
                    st.success("✅ Rendered with st.html()")
                else:
                    st.info("ℹ️ Rendered with components.html() fallback")
                    
        except Exception as e:
            logger.error(f"Hiba a NutritionFactsContainer rendereléskor: {str(e)}")
            st.error(f"❌ Hiba a konténer megjelenítésekor: {str(e)}")
            if debug_mode:
                st.exception(e)
    
    def _generate_complete_nutrition_html(self, offer_data):
        """Teljes HTML generálás inline CSS-sel"""
        
        # Adatok előkészítése
        offer_id = offer_data.get('id', 'N/A')
        status = offer_data.get('status', 'UNKNOWN')
        status_display = self._get_status_display(status)
        
        producer = offer_data.get('user', {})
        product = offer_data.get('product_type', {})
        
        # HTML escape a biztonság kedvéért
        producer_name = html.escape(str(producer.get('contact_name', 'N/A')))
        company_name = html.escape(str(producer.get('company_name', 'N/A')))
        product_name = html.escape(str(product.get('name', 'N/A')))
        
        # Mennyiség és árak
        quantity = offer_data.get('quantity_in_kg', 0)
        price = offer_data.get('price', 0)
        total_value = quantity * price if quantity and price else 0
        
        # Visszaigazolt értékek
        confirmed_quantity = offer_data.get('confirmed_quantity')
        confirmed_price = offer_data.get('confirmed_price')
        
        # Dátumok
        created_at = self._format_date(offer_data.get('created_at', ''))
        delivery_date = self._format_date(offer_data.get('delivery_date', ''))
        
        # Teljes HTML + CSS
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
        <style>
            /* Nutrition Facts Label Style */
            .nutrition-facts {{
                border: 3px solid #000;
                margin: 20px auto;
                width: 100%;
                max-width: 420px;
                padding: 1rem;
                font-family: 'Helvetica Neue', Arial, sans-serif;
                background: white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            
            .nutrition-facts__header {{
                border-bottom: 10px solid #000;
                padding-bottom: 0.5rem;
                margin-bottom: 0.5rem;
            }}
            
            .nutrition-facts__title {{
                font-weight: 900;
                font-size: 2.2rem;
                margin: 0;
                line-height: 1;
                letter-spacing: -1px;
            }}
            
            .nutrition-facts__subtitle {{
                font-size: 1.1rem;
                margin: 0.25rem 0;
                font-weight: 400;
            }}
            
            .nutrition-facts__table {{
                width: 100%;
                border-collapse: collapse;
            }}
            
            .nutrition-facts__table td,
            .nutrition-facts__table th {{
                padding: 0.4rem 0;
                border-top: 1px solid #000;
                font-weight: normal;
                text-align: left;
                font-size: 0.95rem;
            }}
            
            .nutrition-facts__table td:last-child {{
                text-align: right;
                font-weight: bold;
            }}
            
            .nutrition-facts__table .thick-divider td {{
                border-top-width: 5px;
                padding-top: 0.5rem;
            }}
            
            .nutrition-facts__table .section-header td {{
                font-weight: 900;
                font-size: 1rem;
                border: none;
                padding-top: 0.75rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            
            /* Státusz badge-ek */
            .status-badge {{
                display: inline-block;
                padding: 0.3rem 0.8rem;
                border-radius: 15px;
                font-size: 0.85rem;
                font-weight: bold;
                margin: 0.5rem 0;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            
            .status-created {{
                background: #E3F2FD;
                color: #1565C0;
                border: 1px solid #90CAF9;
            }}
            
            .status-confirmed_by_company {{
                background: #FFF8E1;
                color: #F57C00;
                border: 1px solid #FFD54F;
            }}
            
            .status-accepted_by_user {{
                background: #E8F5E9;
                color: #2E7D32;
                border: 1px solid #81C784;
            }}
            
            .status-rejected_by_user {{
                background: #FFEBEE;
                color: #C62828;
                border: 1px solid #EF5350;
            }}
            
            .status-finalized {{
                background: #F3E5F5;
                color: #6A1B9A;
                border: 1px solid #BA68C8;
            }}
            
            /* Megjegyzés */
            .note-section {{
                margin-top: 1rem;
                padding: 0.75rem;
                background: #F5F5F5;
                border-radius: 4px;
                border-left: 4px solid #2196F3;
                font-size: 0.9rem;
            }}
            
            .note-section strong {{
                color: #1976D2;
            }}
            
            /* Links */
            a {{
                color: #1976D2;
                text-decoration: none;
            }}
            
            a:hover {{
                text-decoration: underline;
            }}
            
            /* Responsive */
            @media (max-width: 480px) {{
                .nutrition-facts {{
                    margin: 10px;
                    padding: 0.75rem;
                }}
                
                .nutrition-facts__title {{
                    font-size: 1.8rem;
                }}
                
                .nutrition-facts__table td,
                .nutrition-facts__table th {{
                    font-size: 0.85rem;
                }}
            }}
        </style>
        </head>
        <body>
        <div class="nutrition-facts">
            <header class="nutrition-facts__header">
                <h1 class="nutrition-facts__title">{self.safe_icon} {self.safe_title}</h1>
                <p class="nutrition-facts__subtitle"><strong>Ajánlat #{offer_id}</strong></p>
                <p class="nutrition-facts__subtitle">{company_name}</p>
                <span class="status-badge status-{status.lower()}">{status_display}</span>
            </header>
            
            <table class="nutrition-facts__table">
                <tbody>
                    <!-- Alapadatok -->
                    <tr>
                        <td><strong>Termék</strong></td>
                        <td>{product_name}</td>
                    </tr>
                    <tr>
                        <td><strong>Termelő</strong></td>
                        <td>{producer_name}</td>
                    </tr>
                    
                    <!-- Mennyiség és Árazás -->
                    <tr class="thick-divider">
                        <td colspan="2" class="section-header">Mennyiség és Árazás</td>
                    </tr>
                    <tr>
                        <td>Mennyiség</td>
                        <td>{self._format_quantity(quantity)} kg</td>
                    </tr>
                    <tr>
                        <td>Egységár</td>
                        <td>{self._format_price(price)}/kg</td>
                    </tr>
                    <tr>
                        <td><strong>Összérték</strong></td>
                        <td><strong>{self._format_price(total_value)}</strong></td>
                    </tr>
                    
                    {self._generate_confirmed_section(confirmed_quantity, confirmed_price)}
                    
                    <!-- Dátumok -->
                    <tr class="thick-divider">
                        <td colspan="2" class="section-header">Fontos Dátumok</td>
                    </tr>
                    <tr>
                        <td>Létrehozás</td>
                        <td>{created_at}</td>
                    </tr>
                    <tr>
                        <td>Beszállítás</td>
                        <td>{delivery_date}</td>
                    </tr>
                    
                    {self._generate_contact_section(producer)}
                </tbody>
            </table>
            
            {self._generate_note_section(offer_data.get('note'))}
        </div>
        </body>
        </html>
        """
    
    def _generate_confirmed_section(self, confirmed_quantity, confirmed_price):
        """Visszaigazolt adatok szekció"""
        if not confirmed_quantity and not confirmed_price:
            return ""
        
        html_parts = ["""
        <tr class="thick-divider">
            <td colspan="2" class="section-header">Visszaigazolt Adatok</td>
        </tr>
        """]
        
        if confirmed_quantity:
            html_parts.append(f"""
            <tr>
                <td>Visszaig. mennyiség</td>
                <td>{self._format_quantity(confirmed_quantity)} kg</td>
            </tr>
            """)
        
        if confirmed_price:
            html_parts.append(f"""
            <tr>
                <td>Visszaig. ár</td>
                <td>{self._format_price(confirmed_price)}/kg</td>
            </tr>
            """)
        
        if confirmed_quantity and confirmed_price:
            confirmed_total = float(confirmed_quantity) * float(confirmed_price)
            html_parts.append(f"""
            <tr>
                <td><strong>Visszaig. összérték</strong></td>
                <td><strong>{self._format_price(confirmed_total)}</strong></td>
            </tr>
            """)
        
        return ''.join(html_parts)
    
    def _generate_contact_section(self, producer):
        """Kapcsolattartó szekció"""
        email = producer.get('email')
        phone = producer.get('phone') or producer.get('phone_number')
        
        if not email and not phone:
            return ""
        
        html_parts = ["""
        <tr class="thick-divider">
            <td colspan="2" class="section-header">Kapcsolattartó</td>
        </tr>
        """]
        
        if email:
            escaped_email = html.escape(str(email))
            html_parts.append(f"""
            <tr>
                <td>Email</td>
                <td><a href="mailto:{escaped_email}">{escaped_email}</a></td>
            </tr>
            """)
        
        if phone:
            escaped_phone = html.escape(str(phone))
            html_parts.append(f"""
            <tr>
                <td>Telefon</td>
                <td><a href="tel:{escaped_phone}">{escaped_phone}</a></td>
            </tr>
            """)
        
        return ''.join(html_parts)
    
    def _generate_note_section(self, note):
        """Megjegyzés szekció"""
        if not note:
            return ""
        
        escaped_note = html.escape(str(note))
        return f"""
        <div class="note-section">
            <strong>📝 Megjegyzés:</strong><br>
            {escaped_note}
        </div>
        """
    
    # Segédfüggvények
    def _get_status_display(self, status):
        """Státusz megjelenítési szöveg"""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Visszaigazolva',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)
    
    def _format_quantity(self, value):
        """Mennyiség formázása"""
        try:
            return f"{float(value):,.2f}" if value else "0"
        except:
            return str(value) if value else "0"
    
    def _format_price(self, value):
        """Ár formázása"""
        try:
            return f"{float(value):,.0f} Ft" if value else "0 Ft"
        except:
            return f"{value} Ft" if value else "0 Ft"
    
    def _format_date(self, value):
        """Dátum formázása"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d.")
            return value.strftime("%Y. %m. %d.")
        except:
            return str(value) if value else "-"


# Helper functions for easy usage
def create_offer_facts_panel(offer_data, title="Ajánlat Részletek", icon="📋", debug_mode=False):
    """
    Egyszerű wrapper függvény nutrition facts panel létrehozásához
    """
    container = NutritionFactsContainer(title=title, icon=icon)
    container.render(offer_data, debug_mode=debug_mode)


def create_producer_facts_panel(producer_data, title="Termelő Adatok", icon="👤", debug_mode=False):
    """
    Egyszerű wrapper függvény termelő adatok megjelenítéséhez
    """
    container = NutritionFactsContainer(title=title, icon=icon)
    container.render(producer_data, debug_mode=debug_mode)