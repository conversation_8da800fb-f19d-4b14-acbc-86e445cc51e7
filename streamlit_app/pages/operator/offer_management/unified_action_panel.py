#!/usr/bin/env python3
"""
Unified Action Panel Component
<PERSON><PERSON><PERSON><PERSON><PERSON>, egységes megjelenítés a műveleti gombokhoz
"""
import streamlit as st
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class UnifiedActionPanel:
    """
    Egységes műveleti panel az összes fontos művelet egy helyen történő megjelenítéséhez.
    Illeszkedik a meglévő design rendszerhez és rugalmas konfigurációt biztosít.
    """
    
    def __init__(self, offer_id: int, offer_data: Dict[str, Any]):
        """
        Inicializálja az egységes műveleti panelt.
        
        Args:
            offer_id: Az ajánlat azonosítója
            offer_data: Az ajánlat adatai
        """
        self.offer_id = offer_id
        self.offer_data = offer_data
        self.unique_key = f"unified_action_{offer_id}_{str(uuid.uuid4())[:8]}"
        
        # Eszköz típus meghatározása
        self.is_mobile = st.session_state.get('is_mobile', False)
        
        # Inject modern styles
        self._inject_styles()
    
    def _inject_styles(self):
        """Modern CSS stílusok injektálása"""
        st.markdown("""
        <style>
        .unified-action-panel {
            background: transparent;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid var(--border-color, #e1e5e9);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            animation: slideInFromBottom 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .action-panel-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .action-grid {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .action-button-container {
            background: transparent;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid var(--border-color, #e8eaed);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .action-button-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .action-status-info {
            background: transparent;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 500;
            color: var(--text-color, #2e7d32);
        }
        
        .action-quick-info {
            background: transparent;
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            text-align: center;
            color: var(--text-color, inherit);
        }
        
        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def render(self, 
               actions: List[Dict[str, Any]],
               title: str = "Műveletek",
               show_status_info: bool = True,
               callbacks: Optional[Dict[str, Callable]] = None) -> Dict[str, Any]:
        """
        Rendereli az egységes műveleti panelt.
        
        Args:
            actions: Lista a megjelenítendő műveletekről
            title: A panel címe
            show_status_info: Mutassa-e a státusz információt
            callbacks: Callback függvények a műveletekhez
            
        Returns:
            Dict az elvégzett műveletekkel
        """
        callbacks = callbacks or {}
        results = {}
        
        # Container kezdete
        st.markdown('<div class="unified-action-panel">', unsafe_allow_html=True)
        
        # Státusz információ
        if show_status_info:
            self._render_status_info()
        
        # Gyors információ
        self._render_quick_info()
        
        # Műveletek grid
        if self.is_mobile:
            # Mobil: 1 oszlop
            for action in actions:
                result = self._render_action_button(action, callbacks)
                if result:
                    results.update(result)
        else:
            # Desktop: 2-3 oszlop
            cols_per_row = min(3, len(actions))
            for i in range(0, len(actions), cols_per_row):
                cols = st.columns(cols_per_row)
                for j, action in enumerate(actions[i:i+cols_per_row]):
                    with cols[j]:
                        result = self._render_action_button(action, callbacks)
                        if result:
                            results.update(result)
        
        # Container vége
        st.markdown('</div>', unsafe_allow_html=True)
        
        return results
    
    def _render_status_info(self):
        """Rendereli a státusz információt"""
        status = self.offer_data.get('status', 'UNKNOWN')
        status_text = self._get_status_text(status)
        
        st.markdown(f'''
        <div class="action-status-info">
            <strong>Jelenlegi státusz:</strong> {status_text}
        </div>
        ''', unsafe_allow_html=True)
    
    def _render_quick_info(self):
        """Rendereli a gyors információt"""
        # Üres függvény - nincs szükség a gyors infóra
        pass
    
    def _render_action_button(self, action: Dict[str, Any], callbacks: Dict[str, Callable]) -> Optional[Dict[str, Any]]:
        """
        Renderel egy egyszerű műveleti gombot.
        
        Args:
            action: A művelet konfigurációja
            callbacks: Callback függvények
            
        Returns:
            Dict az elvégzett művelettel, ha van
        """
        action_type = action.get('type', '')
        label = action.get('label', '')
        icon = action.get('icon', '🔧')
        enabled = action.get('enabled', True)
        style = action.get('style', 'primary')
        
        # Egyedi kulcs generálás
        button_key = f"{self.unique_key}_{action_type}"
        
        # Gomb stílus meghatározása
        if style == 'primary':
            button_type = 'primary'
        elif style == 'secondary':
            button_type = 'secondary'
        else:
            button_type = None
        
        # Egyszerű gomb renderelése
        if st.button(
            f"{icon} {label}",
            key=button_key,
            disabled=not enabled,
            type=button_type,
            use_container_width=True
        ):
            # Callback végrehajtása
            if action_type in callbacks:
                try:
                    result = callbacks[action_type](self.offer_id, action)
                    logger.info(f"Action {action_type} executed successfully for offer {self.offer_id}")
                    return {action_type: result}
                except Exception as e:
                    logger.error(f"Action {action_type} failed: {str(e)}")
                    st.error(f"Hiba a művelet végrehajtása során: {str(e)}")
            else:
                logger.warning(f"No callback found for action type: {action_type}")
                st.warning(f"Nincs kezelő a {action_type} művelethez")
        
        return None
    
    def _get_status_text(self, status: str) -> str:
        """Státusz szöveg fordítás"""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Cég által megerősítve',
            'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
            'REJECTED_BY_USER': 'Felhasználó által elutasítva',
            'FINALIZED': 'Véglegesítve',
            'CANCELLED': 'Törölve'
        }
        return status_map.get(status, status)

def get_default_actions(offer_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Alapértelmezett műveletek listája az ajánlat státusza alapján.
    
    Args:
        offer_data: Az ajánlat adatai
        
    Returns:
        Lista az elérhető műveletekről
    """
    status = offer_data.get('status', 'CREATED')
    actions = []
    
    # Vissza gomb - mindig elérhető
    actions.append({
        'type': 'back',
        'label': 'Vissza a listához',
        'icon': '←',
        'description': 'Visszatérés az ajánlatok listájához',
        'enabled': True,
        'style': 'secondary'
    })
    
    # Státusz változtatás
    if status in ['CREATED', 'CONFIRMED_BY_COMPANY']:
        actions.append({
            'type': 'status_change',
            'label': 'Státusz módosítás',
            'icon': '🔄',
            'description': 'Az ajánlat státuszának módosítása',
            'enabled': True,
            'style': 'primary'
        })
    
    # Szerkesztés
    if status in ['CREATED']:
        actions.append({
            'type': 'edit',
            'label': 'Szerkesztés',
            'icon': '✏️',
            'description': 'Az ajánlat adatainak szerkesztése',
            'enabled': True,
            'style': 'secondary'
        })
    
    # Export
    actions.append({
        'type': 'export',
        'label': 'Export',
        'icon': '📥',
        'description': 'Az ajánlat exportálása',
        'enabled': True,
        'style': 'secondary'
    })
    
    # Törlés - csak létrehozott ajánlatoknál
    if status == 'CREATED':
        actions.append({
            'type': 'delete',
            'label': 'Törlés',
            'icon': '🗑️',
            'description': 'Az ajánlat törlése',
            'enabled': True,
            'style': 'secondary'
        })
    
    # Részletek
    actions.append({
        'type': 'details',
        'label': 'Részletek',
        'icon': '📋',
        'description': 'Az ajánlat részletes adatai',
        'enabled': True,
        'style': 'secondary'
    })
    
    return actions

def get_default_callbacks() -> Dict[str, Callable]:
    """
    Alapértelmezett callback függvények.
    
    Returns:
        Dict a callback függvényekkel
    """
    def back_callback(offer_id: int, action: Dict[str, Any]):
        """Vissza gomb callback"""
        st.session_state["go_back_to_offer_list"] = True
        st.rerun()
    
    def status_change_callback(offer_id: int, action: Dict[str, Any]):
        """Státusz változtatás callback"""
        st.session_state[f"show_status_dialog_{offer_id}"] = True
        st.rerun()
    
    def edit_callback(offer_id: int, action: Dict[str, Any]):
        """Szerkesztés callback"""
        st.session_state["edit_mode"] = True
        st.rerun()
    
    def export_callback(offer_id: int, action: Dict[str, Any]):
        """Export callback"""
        st.session_state["show_export_dialog"] = True
        st.rerun()
    
    def delete_callback(offer_id: int, action: Dict[str, Any]):
        """Törlés callback"""
        st.session_state["show_delete_dialog"] = True
        st.rerun()
    
    def details_callback(offer_id: int, action: Dict[str, Any]):
        """Részletek callback"""
        st.session_state["show_details_dialog"] = True
        st.rerun()
    
    return {
        'back': back_callback,
        'status_change': status_change_callback,
        'edit': edit_callback,
        'export': export_callback,
        'delete': delete_callback,
        'details': details_callback
    }