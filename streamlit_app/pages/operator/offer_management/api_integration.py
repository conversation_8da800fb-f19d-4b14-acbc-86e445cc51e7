"""
API Integration Helper for Enhanced Offer Management Components
Connects the new UI components with existing backend API endpoints
"""
import streamlit as st
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date
import requests
import json

logger = logging.getLogger(__name__)

class EnhancedAPIClient:
    """
    Enhanced API client that integrates with existing backend
    and provides data formatting for the modern UI components.
    """
    
    def __init__(self):
        self.base_url = self._get_api_base_url()
        self.session = requests.Session()
        self._setup_session()
        
    def _get_api_base_url(self) -> str:
        """Get API base URL from configuration."""
        try:
            # Try to import from existing config
            try:
                import app_config as config
                return getattr(config, 'API_BASE_URL', 'http://api:8000')
            except ImportError:
                try:
                    from streamlit_app import app_config as config
                    return getattr(config, 'API_BASE_URL', 'http://api:8000')
                except ImportError:
                    return 'http://api:8000'
        except Exception as e:
            logger.warning(f"Could not load API config: {e}, using default")
            return 'http://api:8000'
            
    def _setup_session(self):
        """Setup requests session with authentication and headers."""
        try:
            # Add authentication if available
            if hasattr(st.session_state, 'access_token') and st.session_state.access_token:
                self.session.headers.update({
                    'Authorization': f'Bearer {st.session_state.access_token}',
                    'Content-Type': 'application/json'
                })
            else:
                self.session.headers.update({
                    'Content-Type': 'application/json'
                })
        except Exception as e:
            logger.warning(f"Error setting up API session: {e}")
            
    def get_offers_with_filters(self, filters: Dict[str, Any]) -> Tuple[bool, List[Dict] | str]:
        """
        Get offers with modern filter format.
        Converts modern filter format to API parameters.
        """
        try:
            # Convert modern filters to API parameters
            api_params = self._convert_filters_to_api_params(filters)
            
            # Make API call
            response = self.session.get(f"{self.base_url}/api/offers", params=api_params)
            
            if response.status_code == 200:
                data = response.json()
                
                # Handle different response formats
                if isinstance(data, dict):
                    if 'success' in data and data['success']:
                        offers = data.get('data', [])
                    elif 'data' in data:
                        offers = data['data']
                    else:
                        offers = data
                else:
                    offers = data
                    
                # Enrich offers with additional data
                enriched_offers = self._enrich_offers_data(offers)
                
                logger.info(f"Successfully loaded {len(enriched_offers)} offers")
                return True, enriched_offers
                
            else:
                error_msg = f"API error: {response.status_code}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Error loading offers: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
            
    def get_statistics_data(self, offers: List[Dict]) -> Dict[str, Any]:
        """
        Calculate statistics data for the enhanced stats card.
        """
        try:
            if not offers:
                return {
                    'total_quantity': 0,
                    'average_price': 0,
                    'total_value': 0,
                    'status_distribution': {},
                    'product_distribution': {}
                }
                
            # Calculate basic metrics
            total_quantity = sum(self._safe_get_number(offer, 'quantity_in_kg') for offer in offers)
            prices = [self._safe_get_number(offer, 'price') for offer in offers if self._safe_get_number(offer, 'price') > 0]
            average_price = sum(prices) / len(prices) if prices else 0
            total_value = sum(self._safe_get_number(offer, 'quantity_in_kg') * self._safe_get_number(offer, 'price') for offer in offers)
            
            # Status distribution
            status_dist = {}
            for offer in offers:
                status = offer.get('status', 'UNKNOWN')
                status_display = self._get_status_display_name(status)
                status_dist[status_display] = status_dist.get(status_display, 0) + 1
                
            # Product distribution
            product_dist = {}
            for offer in offers:
                product_name = self._extract_product_name(offer)
                product_dist[product_name] = product_dist.get(product_name, 0) + 1
                
            return {
                'total_quantity': total_quantity,
                'average_price': average_price,
                'total_value': total_value,
                'status_distribution': status_dist,
                'product_distribution': product_dist
            }
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            return {
                'total_quantity': 0,
                'average_price': 0,
                'total_value': 0,
                'status_distribution': {},
                'product_distribution': {}
            }
            
    def get_producers_list(self) -> Tuple[bool, List[str]]:
        """Get list of producer names for filter dropdown."""
        try:
            response = self.session.get(f"{self.base_url}/api/users")
            
            if response.status_code == 200:
                data = response.json()
                
                # Handle different response formats
                if isinstance(data, dict):
                    users = data.get('data', []) if 'data' in data else data.get('users', [])
                else:
                    users = data
                    
                # Extract producer names
                producers = []
                for user in users:
                    if isinstance(user, dict):
                        name = user.get('name') or user.get('username') or user.get('full_name')
                        if name:
                            producers.append(name)
                            
                logger.info(f"Loaded {len(producers)} producers")
                return True, sorted(producers)
                
            else:
                logger.warning(f"Failed to load producers: {response.status_code}")
                return False, []
                
        except Exception as e:
            logger.error(f"Error loading producers: {e}")
            return False, []
            
    def get_products_list(self) -> Tuple[bool, List[str]]:
        """Get list of product names for filter dropdown."""
        try:
            # Try different endpoints for products
            endpoints = ['/api/products/types', '/api/products', '/api/product-types']
            
            for endpoint in endpoints:
                try:
                    response = self.session.get(f"{self.base_url}{endpoint}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # Handle different response formats
                        if isinstance(data, dict):
                            products = data.get('data', []) if 'data' in data else data.get('products', [])
                        else:
                            products = data
                            
                        # Extract product names
                        product_names = []
                        for product in products:
                            if isinstance(product, dict):
                                name = product.get('name') or product.get('type_name') or product.get('product_name')
                                if name:
                                    product_names.append(name)
                            elif isinstance(product, str):
                                product_names.append(product)
                                
                        if product_names:
                            logger.info(f"Loaded {len(product_names)} products from {endpoint}")
                            return True, sorted(product_names)
                            
                except Exception as e:
                    logger.debug(f"Failed to load from {endpoint}: {e}")
                    continue
                    
            # If all endpoints failed, return fallback data
            logger.warning("Failed to load products from API, using fallback")
            return False, []
            
        except Exception as e:
            logger.error(f"Error loading products: {e}")
            return False, []
            
    def save_filter_preset(self, name: str, filters: Dict[str, Any], is_default: bool = False) -> Tuple[bool, str]:
        """Save filter preset to backend."""
        try:
            payload = {
                'name': name,
                'filter_data': filters,
                'is_default': is_default
            }
            
            response = self.session.post(f"{self.base_url}/api/user/filters", json=payload)
            
            if response.status_code in [200, 201]:
                logger.info(f"Successfully saved filter preset: {name}")
                return True, "Szűrő sikeresen mentve"
            else:
                error_msg = f"Failed to save filter: {response.status_code}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Error saving filter: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
            
    def get_saved_filters(self) -> Tuple[bool, List[Dict]]:
        """Get saved filter presets from backend."""
        try:
            response = self.session.get(f"{self.base_url}/api/user/filters")
            
            if response.status_code == 200:
                data = response.json()
                
                # Handle different response formats
                if isinstance(data, dict):
                    filters = data.get('data', []) if 'data' in data else data.get('filters', [])
                else:
                    filters = data
                    
                logger.info(f"Loaded {len(filters)} saved filters")
                return True, filters
                
            else:
                logger.warning(f"Failed to load saved filters: {response.status_code}")
                return False, []
                
        except Exception as e:
            logger.error(f"Error loading saved filters: {e}")
            return False, []
            
    def _convert_filters_to_api_params(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Convert modern filter format to API parameters."""
        api_params = {}
        
        # Producer filter
        if filters.get('producer'):
            # Find producer ID by name if needed
            api_params['producer_name'] = filters['producer']
            
        # Status filters
        if filters.get('statuses'):
            statuses = filters['statuses']
            if isinstance(statuses, list):
                api_params['status'] = ','.join(statuses)
            else:
                api_params['status'] = statuses
                
        # Date range filters
        if filters.get('from_date'):
            if isinstance(filters['from_date'], date):
                api_params['date_from'] = filters['from_date'].isoformat()
            else:
                api_params['date_from'] = str(filters['from_date'])
                
        if filters.get('to_date'):
            if isinstance(filters['to_date'], date):
                api_params['date_to'] = filters['to_date'].isoformat()
            else:
                api_params['date_to'] = str(filters['to_date'])
                
        # Product type filter
        if filters.get('product_type'):
            api_params['product_type'] = filters['product_type']
            
        # Search filter
        if filters.get('search'):
            api_params['search'] = filters['search']
            
        return api_params
        
    def _enrich_offers_data(self, offers: List[Dict]) -> List[Dict]:
        """Enrich offers data for modern UI components."""
        enriched = []
        
        for offer in offers:
            enriched_offer = offer.copy()
            
            # Ensure user data structure
            if 'user' not in enriched_offer and 'producer_name' in enriched_offer:
                enriched_offer['user'] = {'name': enriched_offer['producer_name']}
            elif 'user' in enriched_offer and isinstance(enriched_offer['user'], str):
                enriched_offer['user'] = {'name': enriched_offer['user']}
                
            # Ensure product_type data structure
            if 'product_type' not in enriched_offer and 'product_name' in enriched_offer:
                enriched_offer['product_type'] = {'name': enriched_offer['product_name']}
            elif 'product_type' in enriched_offer and isinstance(enriched_offer['product_type'], str):
                enriched_offer['product_type'] = {'name': enriched_offer['product_type']}
                
            # Ensure numeric fields
            enriched_offer['quantity_in_kg'] = self._safe_get_number(enriched_offer, 'quantity_in_kg')
            enriched_offer['price'] = self._safe_get_number(enriched_offer, 'price')
            
            # Ensure date format
            if 'created_at' in enriched_offer:
                enriched_offer['created_at'] = self._format_date_for_display(enriched_offer['created_at'])
            
            enriched.append(enriched_offer)
            
        return enriched
        
    def _safe_get_number(self, data: Dict, key: str, default: float = 0.0) -> float:
        """Safely extract numeric value from data."""
        try:
            value = data.get(key, default)
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # Try to parse as number
                return float(value.replace(',', '.'))
            else:
                return default
        except (ValueError, TypeError):
            return default
            
    def _extract_product_name(self, offer: Dict) -> str:
        """Extract product name from offer data."""
        # Try different possible field names
        if 'product_type' in offer:
            if isinstance(offer['product_type'], dict):
                return offer['product_type'].get('name', 'Ismeretlen termék')
            else:
                return str(offer['product_type'])
        elif 'product_name' in offer:
            return offer['product_name']
        elif 'product' in offer:
            return offer['product']
        else:
            return 'Ismeretlen termék'
            
    def _get_status_display_name(self, status: str) -> str:
        """Get human-readable status name."""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)
        
    def _format_date_for_display(self, date_str: str) -> str:
        """Format date string for display."""
        try:
            if isinstance(date_str, str):
                # Try to parse different date formats
                for fmt in ['%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%SZ', '%Y-%m-%d']:
                    try:
                        dt = datetime.strptime(date_str, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
            return date_str
        except Exception:
            return date_str

# Global API client instance
api_client = EnhancedAPIClient()

# Convenience functions for components
def get_offers_for_filters(filters: Dict[str, Any]) -> Tuple[bool, List[Dict]]:
    """Get offers with modern filter support."""
    return api_client.get_offers_with_filters(filters)

def get_statistics_for_offers(offers: List[Dict]) -> Dict[str, Any]:
    """Get statistics data for offers."""
    return api_client.get_statistics_data(offers)

def get_filter_options() -> Tuple[List[str], List[str]]:
    """Get available options for filters."""
    success_producers, producers = api_client.get_producers_list()
    success_products, products = api_client.get_products_list()
    
    # Fallback data if API calls fail
    if not success_producers:
        producers = ["Kovács János", "Szabó Péter", "Nagy István", "Tóth Mária", "Horváth Anna"]
    if not success_products:
        products = ["Alma", "Szőlő", "Burgonya", "Paradicsom", "Hagyma", "Répa"]
        
    return producers, products

def save_user_filter(name: str, filters: Dict[str, Any], is_default: bool = False) -> Tuple[bool, str]:
    """Save filter preset for user."""
    return api_client.save_filter_preset(name, filters, is_default)

def get_user_saved_filters() -> List[Dict]:
    """Get user's saved filter presets."""
    success, filters = api_client.get_saved_filters()
    return filters if success else []