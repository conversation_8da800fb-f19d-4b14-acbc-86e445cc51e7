"""
Fokozatos betöltés megvalósítása (infinite scroll vagy 'Tö<PERSON> betöltése' gomb).

Ez a modul az ajánlatok fokozatos betöltéséhez szükséges funkciókat biztosítja
a nagy adatmennyiségek hatékony kezelésére mind asztali, mind mobileszközökön.
"""
import streamlit as st
import logging
import time
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

# Logging beállítása
logger = logging.getLogger(__name__)

# Import enhanced UI components - with fallbacks to prevent circular imports
try:
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
    except ImportError:
        try:
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
        except ImportError:
            # Basic fallback implementations
            logger.warning("Could not import enhanced UI components, using fallbacks")
            show_inline_error = lambda msg: st.error(msg)
            show_inline_warning = lambda msg: st.warning(msg)
            show_inline_success = lambda msg: st.success(msg)
            show_inline_info = lambda msg: st.info(msg)

def inject_infinite_scroll_styles():
    """
    CSS és JavaScript stílusok betöltése a végtelen görgetéshez és a 'Több betöltése' gombhoz.
    """
    # CSS stílusok a végtelen görgetéshez
    css = """
    <style>
    /* Végtelen görgetés konténere */
    .infinite-scroll-container {
        position: relative;
        padding-bottom: 50px;
    }
    
    /* Betöltés indikátor az infinite scroll-hoz */
    .infinite-scroll-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        background-color: rgba(255, 255, 255, 0.8);
        margin: 10px 0;
        border-radius: 5px;
    }
    
    /* Spinner animáció */
    .infinite-scroll-spinner {
        width: 24px;
        height: 24px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3584e4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Stílusok a 'Több betöltése' gombhoz */
    .load-more-button {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f0f2f6;
        color: #333;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 15px;
        margin: 15px auto;
        cursor: pointer;
        transition: all 0.2s ease;
        max-width: 200px;
        text-align: center;
    }
    
    .load-more-button:hover {
        background-color: #e6e9ee;
        border-color: #ccc;
    }
    
    .load-more-button:active {
        transform: translateY(1px);
    }
    
    .load-more-icon {
        margin-right: 8px;
    }
    
    /* Mobilon nagyobb gombok */
    @media (max-width: 768px) {
        .load-more-button {
            padding: 12px 20px;
            width: 90%;
            max-width: 300px;
            font-size: 16px;
        }
    }
    
    /* Observer sentinel elem */
    .infinite-scroll-sentinel {
        height: 20px;
        margin-top: 20px;
        width: 100%;
    }
    
    /* Nincs több betöltendő elem üzenet */
    .infinite-scroll-end {
        text-align: center;
        padding: 15px;
        color: #666;
        font-style: italic;
        margin: 15px 0;
        border-top: 1px dashed #ddd;
    }
    </style>
    """
    
    # JavaScript a végtelen görgetéshez
    js = """
    <script>
    // Várjuk meg a DOM betöltődését
    document.addEventListener('DOMContentLoaded', function() {
        // Infinite scroll inicializálása
        initInfiniteScroll();
        
        // Visszagörgetés nyomon követése a végtelen görgetéshez
        window.addEventListener('scroll', function() {
            const scrollContainers = document.querySelectorAll('.infinite-scroll-container');
            
            scrollContainers.forEach(container => {
                const containerId = container.id;
                // Elmentjük a scroll pozíciót
                saveScrollPosition(containerId);
            });
        });
        
        // MutationObserver a dinamikusan hozzáadott elemekhez
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && 
                            node.classList && 
                            node.classList.contains('infinite-scroll-container')) {
                            initInfiniteScrollForContainer(node);
                        }
                    });
                }
            });
        });
        
        // Az egész body figyelése a dinamikusan hozzáadott elemekhez
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
    
    // Infinite scroll inicializálása
    function initInfiniteScroll() {
        const scrollContainers = document.querySelectorAll('.infinite-scroll-container');
        
        scrollContainers.forEach(container => {
            initInfiniteScrollForContainer(container);
        });
    }
    
    // Konténer-specifikus inicializálás
    function initInfiniteScrollForContainer(container) {
        // Megnézzük, milyen módot használ
        const mode = container.getAttribute('data-mode') || 'button';
        const containerId = container.id;
        
        if (mode === 'scroll') {
            // Intersection Observer beállítása a végtelen görgetéshez
            setupIntersectionObserver(container);
        } else {
            // 'Több betöltése' gomb eseménykezelő
            setupLoadMoreButton(container);
        }
        
        // Görgetési pozíció visszaállítása
        restoreScrollPosition(containerId);
    }
    
    // Intersection Observer beállítása a végtelen görgetéshez
    function setupIntersectionObserver(container) {
        // Sentinel elem keresése
        const sentinel = container.querySelector('.infinite-scroll-sentinel');
        if (!sentinel) return;
        
        const containerId = container.id;
        const loadingItem = container.querySelector('.infinite-scroll-loading');
        
        // Rejtett input a Streamlit állapothoz
        const triggerInput = document.getElementById(`${containerId}_trigger`);
        
        // Az observer létrehozása
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Ha a sentinel elem láthatóvá válik, betöltünk több elemet
                    if (loadingItem) {
                        loadingItem.style.display = 'flex';
                    }
                    
                    // Értesítjük a Streamlit-et, hogy betölthet több elemet
                    if (triggerInput) {
                        triggerInput.value = 'load_more';
                        triggerInput.dispatchEvent(new Event('change'));
                    }
                    
                    // Átmenetileg megszüntetjük a megfigyelést, amíg be nem töltődnek az új elemek
                    observer.unobserve(sentinel);
                    
                    // Kis késleltetéssel újra elkezdjük figyelni, hogy ne történjen több betöltés azonnal
                    setTimeout(() => {
                        observer.observe(sentinel);
                    }, 1000);
                }
            });
        }, {
            root: null, // a viewport
            rootMargin: '0px 0px 200px 0px', // már 200px-el a sentinel előtt elkezdi a betöltést
            threshold: 0.1 // 10% láthatóság elegendő a betöltéshez
        });
        
        // A sentinel elem megfigyelésének indítása
        observer.observe(sentinel);
    }
    
    // 'Több betöltése' gomb eseménykezelő
    function setupLoadMoreButton(container) {
        const loadMoreButton = container.querySelector('.load-more-button');
        if (!loadMoreButton) return;
        
        const containerId = container.id;
        const loadingItem = container.querySelector('.infinite-scroll-loading');
        
        // Rejtett input a Streamlit állapothoz
        const triggerInput = document.getElementById(`${containerId}_trigger`);
        
        // Gomb klikk esemény
        loadMoreButton.addEventListener('click', function() {
            // Betöltés indikátor megjelenítése
            if (loadingItem) {
                loadingItem.style.display = 'flex';
            }
            
            // A gomb elrejtése, amíg zajlik a betöltés
            loadMoreButton.style.display = 'none';
            
            // Értesítjük a Streamlit-et, hogy betölthet több elemet
            if (triggerInput) {
                triggerInput.value = 'load_more';
                triggerInput.dispatchEvent(new Event('change'));
            }
        });
    }
    
    // A görgetési pozíció elmentése
    function saveScrollPosition(containerId) {
        const scrollY = window.scrollY || window.pageYOffset;
        localStorage.setItem(`scroll_position_${containerId}`, scrollY.toString());
    }
    
    // A görgetési pozíció visszaállítása
    function restoreScrollPosition(containerId) {
        try {
            const savedPosition = localStorage.getItem(`scroll_position_${containerId}`);
            if (savedPosition) {
                // Rövid késleltetés után görgetünk, hogy az elemek betöltődjenek
                setTimeout(() => {
                    window.scrollTo(0, parseInt(savedPosition));
                }, 100);
            }
        } catch (e) {
            console.error('Error restoring scroll position', e);
        }
    }
    </script>
    """
    
    try:
        st.markdown(css + js, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a végtelen görgetés stílusok injektálásakor: {str(e)}")


def render_infinite_scroll_container(
    mode: str = "button",
    has_more: bool = True,
    key: Optional[str] = None,
    loading_text: str = "Adatok betöltése..."
) -> Dict[str, Any]:
    """
    Végtelen görgetés konténer renderelése.
    
    Ez a függvény létrehozza a szükséges HTML struktúrát a végtelen görgetéshez
    vagy a 'Több betöltése' gombhoz, és visszaadja a szükséges információkat
    a betöltési állapot kezeléséhez.
    
    Args:
        mode: A betöltés módja: "scroll" (végtelen görgetés) vagy "button" (gomb)
        has_more: Van-e még több betöltendő elem
        key: Egyedi azonosító a konténerhez
        loading_text: Szöveg a betöltés során
        
    Returns:
        Dict[str, Any]: Betöltési állapot információk
            - "should_load_more": True, ha több elemet kell betölteni
            - "container_key": A konténer azonosítója
    """
    # Stílusok betöltése
    inject_infinite_scroll_styles()
    
    # Egyedi azonosító generálása
    component_id = key or f"infinite_scroll_{str(uuid.uuid4())[:8]}"
    
    # HTML a végtelen görgetés konténerhez
    st.markdown(f"""
    <div id="{component_id}" class="infinite-scroll-container" data-mode="{mode}">
    """, unsafe_allow_html=True)
    
    # Eldöntjük, hogy milyen módot használunk
    if mode == "scroll":
        # Végtelen görgetés esetén egy rejtett sentinel elemet adunk hozzá
        if has_more:
            st.markdown(f"""
            <div class="infinite-scroll-sentinel" id="{component_id}_sentinel"></div>
            <div class="infinite-scroll-loading" style="display: none;">
                <div class="infinite-scroll-spinner"></div>
                <div>{loading_text}</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Ha nincs több betöltendő elem
            st.markdown(f"""
            <div class="infinite-scroll-end">Nincs több betöltendő elem</div>
            """, unsafe_allow_html=True)
    else:
        # Több betöltése gomb esetén
        if has_more:
            st.markdown(f"""
            <div class="load-more-button" id="{component_id}_load_more">
                <span class="load-more-icon">⏬</span>
                <span>Több betöltése</span>
            </div>
            <div class="infinite-scroll-loading" style="display: none;">
                <div class="infinite-scroll-spinner"></div>
                <div>{loading_text}</div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Ha nincs több betöltendő elem
            st.markdown(f"""
            <div class="infinite-scroll-end">Nincs több betöltendő elem</div>
            """, unsafe_allow_html=True)
    
    # Rejtett input a Streamlit esemény kezeléshez
    trigger_value = st.text_input(
        "Infinite Scroll Trigger",
        value="",
        key=f"{component_id}_trigger",
        label_visibility="collapsed"
    )
    
    # A konténer lezárása
    st.markdown("</div>", unsafe_allow_html=True)
    
    # Visszaadjuk a betöltési állapotot
    return {
        "should_load_more": trigger_value == "load_more",
        "container_key": component_id
    }


def render_paginated_data(
    data: List[Any],
    item_renderer: Callable[[Any, int], None],
    page_size: int = 10,
    mode: str = "button",
    session_key: Optional[str] = None,
    loading_text: str = "Adatok betöltése...",
    no_data_message: str = "Nincs megjeleníthető adat"
) -> None:
    """
    Adatok fokozatos betöltésének renderelése lapozással.
    
    Ez a függvény kezeli az adatok fokozatos betöltését a felhasználói
    interakciók alapján (görgetés vagy gomb kattintás).
    
    Args:
        data: Az összes adat listája
        item_renderer: Függvény, amely rendereli az egyes elemeket
        page_size: Egy oldalon megjelenő elemek száma
        mode: A betöltés módja: "scroll" (végtelen görgetés) vagy "button" (gomb)
        session_key: Egyedi azonosító a session state-hez
        loading_text: Szöveg a betöltés során
        no_data_message: Üzenet, ha nincs adat
    """
    # Nincs adat kezelése
    if not data:
        st.info(no_data_message)
        return
    
    # Egyedi azonosító a session state-hez
    key_prefix = session_key or "progressive_loading"
    current_page_key = f"{key_prefix}_current_page"
    
    # Inicializáljuk a session state-et
    if current_page_key not in st.session_state:
        st.session_state[current_page_key] = 1
    
    # Aktuális oldal és teljes oldalszám
    current_page = st.session_state[current_page_key]
    total_items = len(data)
    total_pages = (total_items + page_size - 1) // page_size
    
    # Végtelen görgetés konténer renderelése
    scroll_state = render_infinite_scroll_container(
        mode=mode,
        has_more=current_page < total_pages,
        key=f"{key_prefix}_container",
        loading_text=loading_text
    )
    
    # Jelenlegi oldalon megjelenítendő elemek lekérése
    start_idx = 0
    end_idx = min(current_page * page_size, total_items)
    visible_data = data[:end_idx]
    
    # Az elemek renderelése
    for idx, item in enumerate(visible_data):
        item_renderer(item, idx)
    
    # Ha több betöltés szükséges
    if scroll_state["should_load_more"] and current_page < total_pages:
        # Az oldal számát növeljük
        st.session_state[current_page_key] += 1
        
        # Újrarendereljük az oldalt
        st.rerun()


def offer_renderer_wrapper(
    renderer_func: Callable[[Any, int], None],
    item_key_func: Optional[Callable[[Any], str]] = None
) -> Callable[[Any, int], None]:
    """
    Ajánlat renderelő függvény wrapper a cache invalidáció elkerülésére.
    
    A függvény biztosítja, hogy minden ajánlat egyedi kulccsal renderelődjön
    a cache-elés problémáinak elkerülése érdekében.
    
    Args:
        renderer_func: Az eredeti renderelő függvény (params: offer, index)
        item_key_func: Függvény, ami egyedi kulcsot generál az elemhez (param: offer)
        
    Returns:
        Callable[[Any, int], None]: A módosított renderelő függvény
    """
    def wrapped_renderer(offer, index):
        # Egyedi kulcs generálása az ajánlathoz
        unique_key = item_key_func(offer) if item_key_func else f"offer_{offer.get('id')}_{index}"
        
        # Az ajánlat konténere
        with st.container():
            # Az eredeti renderelő függvény meghívása az egyedi kulccsal
            renderer_func(offer, index)
    
    return wrapped_renderer


# Példa használatra
if __name__ == "__main__":
    st.set_page_config(page_title="Fokozatos betöltés demonstráció", layout="wide")
    
    st.title("Fokozatos betöltés demonstráció")
    
    # Példa adatok generálása
    import pandas as pd
    import numpy as np
    
    def generate_sample_offers(num_items=50):
        """Példa ajánlatok generálása teszteléshez"""
        statuses = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
        producers = [
            {"id": 1, "name": "Kovács János"}, 
            {"id": 2, "name": "Nagy Bt."}, 
            {"id": 3, "name": "Példa Kft."}
        ]
        products = [
            {"id": 1, "name": "Alma"}, 
            {"id": 2, "name": "Körte"}, 
            {"id": 3, "name": "Banán"}, 
            {"id": 4, "name": "Szőlő"}
        ]
        
        offers = []
        for i in range(1, num_items + 1):
            offer = {
                "id": i,
                "status": np.random.choice(statuses),
                "product_type": np.random.choice(products),
                "user": np.random.choice(producers),
                "quantity_in_kg": np.random.randint(10, 1000),
                "price": np.random.randint(100, 500),
                "delivery_date": pd.Timestamp("2025-01-01") + pd.Timedelta(days=np.random.randint(0, 365)),
                "created_at": pd.Timestamp("2025-01-01") - pd.Timedelta(days=np.random.randint(0, 30))
            }
            offers.append(offer)
        
        return offers
    
    # Példa adatok
    sample_offers = generate_sample_offers(50)
    
    # Példa ajánlat renderelő függvény
    def render_offer_card(offer, index):
        """Példa ajánlat kártya renderelése"""
        status_colors = {
            "CREATED": "#FFA07A",
            "CONFIRMED_BY_COMPANY": "#FFD700",
            "ACCEPTED_BY_USER": "#98FB98",
            "REJECTED_BY_USER": "#FF6347",
            "FINALIZED": "#20B2AA"
        }
        
        status_color = status_colors.get(offer["status"], "#777777")
        
        # Kártya stílus
        st.markdown(f"""
        <style>
        .offer-card-{index} {{
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: white;
        }}
        .offer-status-{index} {{
            background-color: {status_color};
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            display: inline-block;
        }}
        </style>
        """, unsafe_allow_html=True)
        
        # Kártya tartalom
        st.markdown(f"""
        <div class="offer-card-{index}">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <div style="font-weight: bold;">#{offer["id"]} - {offer["product_type"]["name"]}</div>
                <div class="offer-status-{index}">{offer["status"]}</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <div>Termelő: {offer["user"]["name"]}</div>
                <div>Mennyiség: {offer["quantity_in_kg"]} kg</div>
            </div>
            <div style="display: flex; justify-content: space-between;">
                <div>Ár: {offer["price"]} Ft/kg</div>
                <div>Dátum: {offer["delivery_date"].strftime('%Y-%m-%d')}</div>
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # Demo a különböző betöltési módokról
    tab1, tab2 = st.tabs(["1. 'Több betöltése' gomb", "2. Végtelen görgetés"])
    
    with tab1:
        st.header("'Több betöltése' gomb")
        st.write("""
        Ez a mód egy gomb segítségével teszi lehetővé az adatok fokozatos betöltését.
        Minden kattintásra újabb adag adat töltődik be.
        """)
        
        # Adatok rendszerezése a gombbal
        render_paginated_data(
            data=sample_offers,
            item_renderer=render_offer_card,
            page_size=5,
            mode="button",
            session_key="button_demo",
            loading_text="Ajánlatok betöltése..."
        )
    
    with tab2:
        st.header("Végtelen görgetés")
        st.write("""
        Ez a mód automatikusan betölti az újabb adagokat, amikor a felhasználó
        az oldal aljára görget, így biztosítva a folyamatos, megszakítás nélküli
        böngészést.
        """)
        
        # Adatok rendszerezése végtelen görgetéssel
        render_paginated_data(
            data=sample_offers,
            item_renderer=render_offer_card,
            page_size=5,
            mode="scroll",
            session_key="scroll_demo",
            loading_text="Ajánlatok betöltése..."
        )
    
    # Fejlesztői információk
    with st.expander("Fejlesztői információk"):
        st.markdown("""
        **Modul:** progressive_loading.py
        
        **Fő függvények:**
        - `render_infinite_scroll_container`: Végtelen görgetés konténer létrehozása
        - `render_paginated_data`: Adatok fokozatos betöltésének kezelése
        - `offer_renderer_wrapper`: Segédfüggvény a renderer cache problémák elkerülésére
        
        **Technológiai megoldások:**
        - Intersection Observer API a végtelen görgetéshez
        - Session state használata az oldal követésére
        - Egyedi CSS animációk a betöltési indikátorokhoz
        - Scroll pozíció mentése és visszaállítása
        
        **Támogatott módok:**
        - **Button**: 'Több betöltése' gomb esetén a felhasználónak explicit kattintania kell a további adatok betöltéséhez
        - **Scroll**: Végtelen görgetésnél automatikusan betöltődnek az újabb elemek, amikor a felhasználó az oldal aljára görget
        
        **Adaptív viselkedés:**
        - Mobilon nagyobb gombok és érintés-barát interfész
        - A legoptimálisabb megoldás automatikus kiválasztása a képernyőmérethez
        - Asztali böngészőkben finom scrollbar és keyboard navigáció
        """)