"""
Minim<PERSON><PERSON> Téma - Tiszta Streamlit Megoldás
Egyszerű, natív komponensekkel meg<PERSON>ósított dark theme
"""
import streamlit as st
from datetime import datetime
import plotly.graph_objects as go

def apply_minimal_dark_theme():
    """Minimális sötét téma CSS"""
    st.markdown("""
    <style>
        /* Alap háttér */
        .stApp {
            background-color: #0a0a0a;
        }
        
        /* Streamlit elemek testreszabása */
        .element-container {
            background-color: transparent !important;
        }
        
        /* Métrikák stílusa */
        [data-testid="metric-container"] {
            background-color: #1a1a1a;
            border: 1px solid #2a2a2a;
            padding: 1rem;
            border-radius: 8px;
            color: white;
            margin-bottom: 0.5rem;
        }
        
        /* Expanderek */
        .streamlit-expanderHeader {
            background-color: #1a1a1a !important;
            border: 1px solid #2a2a2a !important;
            border-radius: 8px !important;
            color: white !important;
        }
        
        .streamlit-expanderContent {
            background-color: #1a1a1a !important;
            border: 1px solid #2a2a2a !important;
            border-top: none !important;
            color: white !important;
        }
        
        /* Gombok */
        .stButton > button {
            background-color: #1a1a1a;
            border: 1px solid #2a2a2a;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .stButton > button:hover {
            background-color: #2a2a2a;
            border-color: #3a3a3a;
        }
        
        /* Oszlopok közötti térköz */
        [data-testid="column"] {
            padding: 0 0.5rem;
        }
        
        /* Színes felső csíkok */
        .green-top {
            border-top: 3px solid #10dc60 !important;
        }
        
        .orange-top {
            border-top: 3px solid #ff8c1a !important;
        }
        
        .blue-top {
            border-top: 3px solid #0099e0 !important;
        }
        
        /* Progress bar */
        .stProgress > div > div {
            background-color: #10dc60;
        }
        
        /* Címek */
        h1, h2, h3, h4, h5, h6 {
            color: white !important;
        }
        
        /* Szövegek */
        p, span, div {
            color: #e0e0e0;
        }
        
        /* Info box */
        .info-box {
            background-color: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        /* Status badge */
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            background: rgba(16, 220, 96, 0.2);
            color: #10dc60;
            border: 1px solid rgba(16, 220, 96, 0.3);
        }
        
        /* Container styling */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        /* Card hover effects */
        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
        }
        
        /* Timeline items */
        .timeline-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 6px;
            transition: background 0.2s ease;
        }
        
        .timeline-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        /* Status indicators */
        .status-indicator {
            padding: 1rem;
            text-align: center;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .status-indicator.active {
            background: rgba(16, 220, 96, 0.1);
            border: 1px solid #10dc60;
        }
        
        .status-indicator.inactive {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
        }
        
        .status-indicator:hover {
            transform: translateY(-2px);
        }
    </style>
    """, unsafe_allow_html=True)

def render_dark_theme_offer(offer):
    """Sötét témájú ajánlat részletek renderelése"""
    
    # CSS alkalmazása
    apply_minimal_dark_theme()
    
    # Main container
    st.markdown('<div class="main-container">', unsafe_allow_html=True)
    
    # Fejléc
    status_text = _get_status_display(offer.get('status', 'CREATED'))
    status_class = 'status-badge'
    
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #1a1a1a, #2a2a2a); 
                border: 1px solid #2a2a2a; 
                border-radius: 12px; 
                padding: 2rem; 
                margin-bottom: 2rem;
                border-top: 4px solid #10dc60;">
        <h1 style="margin: 0; color: white;">Ajánlat #{offer.get('id', 'N/A')}</h1>
        <p style="color: #808080; margin: 0.5rem 0;">
            Utoljára módosítva: {_format_datetime(offer.get('updated_at') or offer.get('created_at'))}
        </p>
        <div class="{status_class}">● {status_text}</div>
    </div>
    """, unsafe_allow_html=True)
    
    # Műveleti gombok
    st.markdown("#### ⚡ Gyors műveletek")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        if st.button("✏️ Szerkesztés", use_container_width=True):
            st.info("Szerkesztés funkció fejlesztés alatt")
    with col2:
        if st.button("📋 Másolás", use_container_width=True):
            st.success("Link másolva a vágólapra!")
    with col3:
        if st.button("📊 Export", use_container_width=True):
            st.info("Export funkció fejlesztés alatt")
    with col4:
        if st.button("🗑️ Törlés", use_container_width=True):
            st.warning("Törlés funkció fejlesztés alatt")
    
    st.markdown("---")
    
    # Fő tartalom - 2 oszlopos elrendezés
    col1, col2 = st.columns(2)
    
    with col1:
        # Ajánlat adatai
        with st.container():
            st.markdown('<div class="info-box green-top">', unsafe_allow_html=True)
            st.markdown("### 📋 Ajánlat adatai")
            
            col_a, col_b = st.columns(2)
            with col_a:
                st.markdown("**Azonosító:**")
                st.markdown("**Beszállítás:**")
                st.markdown("**Termék:**")
                st.markdown("**Kategória:**")
                st.markdown("**Mennyiség:**")
            with col_b:
                st.markdown(f"{offer.get('id', 'N/A')}")
                st.markdown(f"{_format_date(offer.get('delivery_date'))}")
                st.markdown(f"{offer.get('product_type', {}).get('name', 'N/A')}")
                st.markdown(f"{offer.get('product_type', {}).get('category', {}).get('name', 'N/A')}")
                st.markdown(f"**{_format_quantity(offer.get('quantity_in_kg'))} kg**")
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Szállítási információk
        with st.container():
            st.markdown('<div class="info-box">', unsafe_allow_html=True)
            st.markdown("### 🚚 Szállítási információk")
            
            delivery_date = offer.get('delivery_date')
            delivery_status = _get_delivery_status(delivery_date)
            
            col_a, col_b = st.columns(2)
            with col_a:
                st.markdown("**Szállítási dátum:**")
                st.markdown("**Szállítási cím:**")
                st.markdown("**Szállítási mód:**")
                st.markdown("**Státusz:**")
            with col_b:
                st.markdown(f"{_format_date(delivery_date)}")
                st.markdown("Központi raktár")
                st.markdown("Saját szállítás")
                st.markdown(f"{delivery_status}")
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Timeline
        with st.expander("📅 Állapotváltozási napló", expanded=True):
            events = [
                ("Létrehozva", offer.get('created_at'), "Rendszer"),
                ("Cég által visszaigazolva", offer.get('confirmed_at'), "Kiss Péter"),
                ("Elfogadva", offer.get('accepted_at'), offer.get('user', {}).get('contact_name', 'N/A'))
            ]
            
            for title, date, user in events:
                if date:
                    st.markdown(f"""
                    <div class="timeline-item">
                        <div style="width: 10px; height: 10px; background: #10dc60; 
                                    border-radius: 50%; margin-top: 0.5rem;"></div>
                        <div>
                            <div style="font-weight: 500; color: white;">{title}</div>
                            <div style="color: #808080; font-size: 0.875rem;">
                                {_format_datetime(date)} • {user}
                            </div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
    
    with col2:
        # Visszaigazolás
        with st.container():
            st.markdown('<div class="info-box orange-top">', unsafe_allow_html=True)
            st.markdown("### 📊 Visszaigazolás")
            
            quantity = _to_float(offer.get('quantity_in_kg', 0))
            confirmed_quantity = _to_float(offer.get('confirmed_quantity', quantity))
            progress = (confirmed_quantity / quantity) if quantity > 0 else 0
            
            col_a, col_b = st.columns(2)
            with col_a:
                st.metric("Eredeti mennyiség", f"{_format_quantity(quantity)} kg")
                st.metric("Eredeti ár", f"{_format_price(offer.get('price', 0))}/kg")
            with col_b:
                st.metric("Visszaigazolt", f"{_format_quantity(confirmed_quantity)} kg", 
                         delta=f"{(progress*100):.0f}%")
                st.metric("Visszaigazolt ár", f"{_format_price(offer.get('confirmed_price', offer.get('price', 0)))}/kg")
            
            st.progress(progress)
            st.markdown(f"<p style='text-align: center; color: #808080;'>{progress*100:.0f}% teljesítve</p>", 
                       unsafe_allow_html=True)
            
            # Összérték számítás
            confirmed_price = _to_float(offer.get('confirmed_price', offer.get('price', 0)))
            total_value = confirmed_quantity * confirmed_price
            st.markdown(f"**💰 Összérték:** {_format_price(total_value)}")
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Termelő adatai
        with st.container():
            st.markdown('<div class="info-box">', unsafe_allow_html=True)
            st.markdown("### 👤 Termelő adatai")
            
            user = offer.get('user', {})
            
            col_a, col_b = st.columns(2)
            with col_a:
                st.markdown("**Név:**")
                st.markdown("**Cégnév:**")
                st.markdown("**Telefon:**")
                st.markdown("**Email:**")
            with col_b:
                st.markdown(f"{user.get('contact_name', 'N/A')}")
                st.markdown(f"{user.get('company_name', 'N/A')}")
                st.markdown(f"{user.get('phone', 'N/A')}")
                email = user.get('email', 'N/A')
                if email != 'N/A':
                    st.markdown(f"[{email}](mailto:{email})")
                else:
                    st.markdown(email)
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Ár grafikon
        with st.expander("📈 Ár összehasonlítás", expanded=True):
            fig = go.Figure()
            
            categories = ['Eredeti', 'Visszaigazolt']
            values = [
                _to_float(offer.get('price', 0)),
                _to_float(offer.get('confirmed_price', offer.get('price', 0)))
            ]
            
            fig.add_trace(go.Bar(
                x=categories,
                y=values,
                marker_color=['#ff8c1a', '#10dc60'],
                text=[f'{v:,.0f} Ft' for v in values],
                textposition='outside',
                textfont=dict(size=14, color='white')
            ))
            
            fig.update_layout(
                plot_bgcolor='#1a1a1a',
                paper_bgcolor='#1a1a1a',
                font=dict(color='#ffffff'),
                showlegend=False,
                height=250,
                margin=dict(l=20, r=20, t=20, b=20),
                yaxis=dict(
                    gridcolor='#2a2a2a',
                    title='Egységár (Ft/kg)',
                    titlefont=dict(size=12, color='white')
                ),
                xaxis=dict(
                    gridcolor='#2a2a2a'
                )
            )
            
            st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    # Termék részletek (ha vannak)
    if offer.get('note') or offer.get('quality_parameters'):
        st.markdown("---")
        st.markdown("### 📝 További információk")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if offer.get('note'):
                with st.container():
                    st.markdown('<div class="info-box blue-top">', unsafe_allow_html=True)
                    st.markdown("#### Megjegyzés")
                    st.markdown(f"{offer.get('note')}")
                    st.markdown('</div>', unsafe_allow_html=True)
        
        with col2:
            if offer.get('quality_parameters'):
                with st.container():
                    st.markdown('<div class="info-box">', unsafe_allow_html=True)
                    st.markdown("#### Minőségi paraméterek")
                    params = offer.get('quality_parameters', {})
                    if isinstance(params, dict):
                        for param, value in params.items():
                            st.markdown(f"**{param}:** {value}")
                    st.markdown('</div>', unsafe_allow_html=True)
    
    # Állapot indikátorok
    st.markdown("---")
    st.markdown("### 📊 Státusz előzmények")
    
    # Státusz alapú progresszió
    current_status = offer.get('status', 'CREATED')
    status_progression = ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'FINALIZED']
    current_index = status_progression.index(current_status) if current_status in status_progression else 0
    
    cols = st.columns(4)
    state_info = [
        ("Létrehozva", "CREATED"),
        ("Visszaigazolva", "CONFIRMED_BY_COMPANY"),
        ("Elfogadva", "ACCEPTED_BY_USER"),
        ("Véglegesítve", "FINALIZED")
    ]
    
    for col, (label, status), i in zip(cols, state_info, range(4)):
        with col:
            is_active = i <= current_index
            status_class = "active" if is_active else "inactive"
            value = "✓" if is_active else "-"
            color = "#10dc60" if is_active else "#808080"
            
            st.markdown(f"""
            <div class="status-indicator {status_class}">
                <div style="color: #808080; font-size: 0.75rem; 
                            text-transform: uppercase; margin-bottom: 0.5rem;">
                    {label}
                </div>
                <div style="color: {color}; font-size: 1.5rem; font-weight: 600;">
                    {value}
                </div>
            </div>
            """, unsafe_allow_html=True)
    
    # Close container
    st.markdown('</div>', unsafe_allow_html=True)

# Segéd függvények
def _to_float(value):
    """Érték biztonságos float konvertálása"""
    try:
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # Eltávolítjuk a nem numerikus karaktereket
            cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
            return float(cleaned) if cleaned else 0.0
        return 0.0
    except (ValueError, TypeError):
        return 0.0

def _get_status_display(status):
    """Státusz megjelenítő szöveg"""
    status_map = {
        'CREATED': 'Létrehozva',
        'CONFIRMED_BY_COMPANY': 'Cég által visszaigazolva',
        'ACCEPTED_BY_USER': 'Elfogadva',
        'REJECTED_BY_USER': 'Elutasítva',
        'FINALIZED': 'Véglegesítve'
    }
    return status_map.get(status, status)

def _get_delivery_status(delivery_date):
    """Szállítási státusz meghatározása"""
    if not delivery_date:
        return "Nincs megadva"
    
    try:
        if isinstance(delivery_date, str):
            delivery_dt = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
        else:
            delivery_dt = delivery_date
        
        days_until = (delivery_dt - datetime.now()).days
        
        if days_until < 0:
            return f"Lejárt ({abs(days_until)} napja)"
        elif days_until == 0:
            return "Ma esedékes"
        elif days_until == 1:
            return "Holnap esedékes"
        else:
            return f"{days_until} nap múlva esedékes"
    except:
        return "Nincs megadva"

def _format_quantity(value):
    """Mennyiség formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f}"

def _format_price(value):
    """Ár formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f} Ft"

def _format_date(value):
    """Dátum formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d.")
        return "-"
    except:
        return "-"

def _format_datetime(value):
    """Dátum és idő formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d. %H:%M")
        return "-"
    except:
        return "-"