"""
Vizu<PERSON><PERSON> visszajelzési komponensek az ajánlat kezeléshez.
Tartalmazza a státuszváltozások és műveletek visszajelzéseit.
"""
import streamlit as st
import logging
import uuid

# Try different import paths for formatting
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status
    except ImportError:
        # Fallback formatting function if import fails
        logging.warning("Could not import format_status function, using fallback")
        format_status = lambda x: x if x else "Ismeretlen"

logger = logging.getLogger(__name__)

def show_operation_feedback(operation_name, success, result_message=None, error_message=None, duration=3):
    """
    Vizuálisan attraktív visszajelzés megjelenítése műveletek elvégzéséről.
    
    Ez a függvény egy vizu<PERSON><PERSON><PERSON> feltűn<PERSON>, animált visszajelzést ad a felhasználónak
    a művelet sikerességéről vagy esetleges hibájáról.
    
    Args:
        operation_name (str): A művelet neve, ami megjelenik a visszajelzésben
        success (bool): Sikeres volt-e a művelet
        result_message (str, optional): Egyéni sikeres üzenet. Defaults to None.
        error_message (str, optional): Egyéni hibaüzenet. Defaults to None.
        duration (int, optional): Másodpercek száma, ameddig látható. Defaults to 3.
    """
    if "feedback_shown" not in st.session_state:
        st.session_state.feedback_shown = False
    
    # Ha már megjelenítettük a visszajelzést, nem mutatjuk újra
    if st.session_state.feedback_shown:
        return
    
    # Alapértelmezett üzenetek
    if not result_message:
        result_message = f"{operation_name} sikeresen végrehajtva!"
    if not error_message:
        error_message = f"Hiba történt a(z) {operation_name} során."
    
    # Stílus definíció
    feedback_style = """
    <style>
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-20px); }
        10% { opacity: 1; transform: translateY(0); }
        90% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-20px); }
    }
    .feedback-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        animation: fadeInOut 3s ease forwards;
    }
    .success-feedback {
        background-color: #28a745;
        color: white;
    }
    .error-feedback {
        background-color: #dc3545;
        color: white;
    }
    .feedback-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .feedback-icon {
        font-size: 20px;
        margin-right: 10px;
    }
    </style>
    """
    
    # HTML tartalom létrehozása
    if success:
        container_class = "success-feedback"
        icon = "✅"
        title = "Sikeres művelet"
        message = result_message
    else:
        container_class = "error-feedback"
        icon = "❌"
        title = "Hiba történt"
        message = error_message
    
    html_content = f"""
    {feedback_style}
    <div class="feedback-container {container_class}">
        <div class="feedback-title">
            <span class="feedback-icon">{icon}</span>
            {title}
        </div>
        <div class="feedback-message">{message}</div>
    </div>
    """
    
    # Megjelenítjük a visszajelzést
    st.markdown(html_content, unsafe_allow_html=True)
    
    # Beállítjuk, hogy megjelent a visszajelzés
    st.session_state.feedback_shown = True
    
    # Timer beállítása az újratöltéshez a megadott idő után
    # Ezt JavaScript-tel oldjuk meg
    js_timer = f"""
    <script>
        setTimeout(function() {{
            // Töröljük a visszajelzés-mutatót a sessionből
            window.sessionStorage.removeItem('feedback_shown');
            // Frissítés csak, ha még mindig ezen az oldalon vagyunk
            if (window.location.href.includes('offer_management')) {{
                window.location.reload();
            }}
        }}, {duration * 1000});
    </script>
    """
    
    st.markdown(js_timer, unsafe_allow_html=True)

def display_status_change_feedback(old_status, new_status, success):
    """
    Vizuálisan informatív visszajelzés megjelenítése státuszváltozáskor.
    
    A függvény megjeleníti, hogy hogyan változott az ajánlat státusza,
    és szöveges magyarázatot ad a változás jelentéséről.
    
    Args:
        old_status (str): Az ajánlat korábbi státusza
        new_status (str): Az ajánlat új státusza
        success (bool): Sikeres volt-e a művelet
    """
    if not success:
        show_operation_feedback(
            "státuszváltoztatás", 
            False, 
            error_message=f"Nem sikerült a státuszt megváltoztatni erre: {format_status(new_status)}"
        )
        return
    
    # Státuszmagyarázatok szótára
    status_explanations = {
        "CREATED": "Az ajánlat létrehozva, de még nem került megerősítésre.",
        "CONFIRMED_BY_COMPANY": "Az ajánlat megerősítésre került a vállalat által. Most már a termelő elfogadhatja vagy elutasíthatja.",
        "ACCEPTED_BY_USER": "A termelő elfogadta az ajánlatot. Az ajánlat most már véglegesíthető.",
        "REJECTED_BY_USER": "A termelő elutasította az ajánlatot. Az ajánlatot újra lehet nyitni.",
        "FINALIZED": "Az ajánlat véglegesítésre került. További módosítás nem lehetséges.",
        "pending": "Az ajánlat feldolgozásra vár.",
        "approved": "Az ajánlat elfogadásra került, a termelő értesítést kap.",
        "rejected": "Az ajánlat elutasításra került, a termelő értesítést kap.",
        "in_progress": "Az ajánlat feldolgozás alatt áll.",
        "completed": "Az ajánlat teljesítve lett, a folyamat lezárult.",
        "canceled": "Az ajánlat törlésre került, a folyamat megszakadt."
    }
    
    # Alapértelmezett magyarázat, ha nem találunk specifikusat
    explanation = status_explanations.get(
        new_status, 
        f"Az ajánlat státusza megváltozott: {format_status(old_status)} → {format_status(new_status)}"
    )
    
    # Státuszváltás megjelenítése
    success_message = f"""
    Státusz sikeresen megváltoztatva: 
    <span style="color:#6c757d">{format_status(old_status)}</span> → 
    <span style="font-weight:bold;color:#28a745">{format_status(new_status)}</span>
    <br/><br/>
    <span style="font-size:0.9em">{explanation}</span>
    """
    
    show_operation_feedback(
        "státuszváltoztatás", 
        True, 
        result_message=success_message
    )

def create_styled_button(label, icon=None, on_click=None, key=None, type="primary", disabled=False, size="md", help=None, tooltip=None):
    """
    Konzisztens, stílusos gombok létrehozása az alkalmazásban.
    
    Ez a függvény egy egységes megjelenésű és viselkedésű gombot hoz létre,
    amely követi a stílusútmutatónkat és opcionálisan tooltipet is tartalmaz.
    
    Args:
        label (str): A gomb címkéje/szövege
        icon (str, optional): Ikon a gomb előtt. Defaults to None.
        on_click (callable, optional): Eseménykezelő a gombkattintáshoz. Defaults to None.
        key (str, optional): Egyedi kulcs a gombhoz. Defaults to None.
        type (str, optional): Gomb típusa: primary, secondary, success, danger, warning, info. Defaults to "primary".
        disabled (bool, optional): Le van-e tiltva a gomb. Defaults to False.
        size (str, optional): Gomb mérete: sm, md, lg. Defaults to "md".
        help (str, optional): Segítő szöveg a gombhoz. Defaults to None.
        tooltip (str, optional): Tooltip szöveg a gombhoz. Defaults to None.
        
    Returns:
        bool: True ha a gombra kattintottak, False ha nem
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"btn_{label.lower().replace(' ', '_')}_{str(uuid.uuid4())[:8]}"
    
    # Ikon hozzáadása a címkéhez, ha van
    display_label = f"{icon} {label}" if icon else label
    
    # Biztosítjuk, hogy csak 'primary' vagy 'secondary' típust használjunk
    # Az egyéb típusokat konvertáljuk ezekre
    button_type = "primary"
    if type in ["secondary", "warning", "danger", "dark"]:
        button_type = "secondary"
    
    # Gomb létrehozása a megfelelő mérettel
    if size == "sm":
        container = st.container()
        with container:
            st.markdown(f"""<div style="transform: scale(0.8); transform-origin: left top;">""", unsafe_allow_html=True)
            clicked = st.button(
                display_label,
                key=key,
                type=button_type,
                disabled=disabled,
                help=help,
                on_click=on_click if on_click else None
            )
            st.markdown("</div>", unsafe_allow_html=True)
    elif size == "lg":
        # Nagy méretű gomb CSS stílussal
        st.markdown(f"""
        <style>
        .lg-button-{key} {{
            font-size: 1.2em;
            font-weight: bold;
        }}
        </style>
        """, unsafe_allow_html=True)
        clicked = st.button(
            display_label,
            key=key,
            type=button_type,
            disabled=disabled,
            help=help,
            on_click=on_click if on_click else None
        )
        # Utólag adjuk hozzá a CSS osztályt JavaScript segítségével
        st.markdown(f"""
        <script>
        document.addEventListener('DOMContentLoaded', function() {{
            var btn = document.querySelector('button[data-testid="{key}"]');
            if (btn) {{
                btn.classList.add('lg-button-{key}');
            }}
        }});
        </script>
        """, unsafe_allow_html=True)
    else:  # md
        clicked = st.button(
            display_label,
            key=key,
            type=button_type,
            disabled=disabled,
            help=help,
            on_click=on_click if on_click else None
        )
    
    # Tooltip hozzáadása, ha van
    if tooltip and clicked is False:  # Csak akkor jelenítjük meg, ha nem kattintottak rá
        tooltip_id = f"{key}_tooltip"
        
        # A tooltip HTML és stílus létrehozása
        tooltip_style = f"""
        <style>
        .btn-tooltip-{tooltip_id} {{
            position: relative;
            display: inline-block;
        }}
        
        .btn-tooltip-{tooltip_id} .tooltiptext {{
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8em;
        }}
        
        .btn-tooltip-{tooltip_id}:hover .tooltiptext {{
            visibility: visible;
            opacity: 1;
        }}
        </style>
        <script>
        // Tooltip elhelyezése a gomb fölé
        document.addEventListener('DOMContentLoaded', function() {{
            var btn = document.querySelector('button[aria-describedby="{key}"]');
            if (btn) {{
                var tooltipDiv = document.createElement('div');
                tooltipDiv.className = 'btn-tooltip-{tooltip_id}';
                var tooltipText = document.createElement('span');
                tooltipText.className = 'tooltiptext';
                tooltipText.innerText = '{tooltip}';
                tooltipDiv.appendChild(tooltipText);
                btn.parentNode.insertBefore(tooltipDiv, btn);
                tooltipDiv.appendChild(btn);
            }}
        }});
        </script>
        """
        
        st.markdown(tooltip_style, unsafe_allow_html=True)
    
    return clicked

def create_action_button_group(buttons, columns=None):
    """
    Gombok csoportjának létrehozása konzisztens elrendezéssel.
    
    Args:
        buttons (list): Gombok definíciója, minden elem egy dict a következő kulcsokkal:
            - label: gomb címkéje
            - icon: ikon a gombra (opcionális)
            - on_click: kattintási eseménykezelő (opcionális)
            - key: egyedi kulcs (opcionális)
            - type: gomb típusa (opcionális, alapértelmezett: "primary")
            - disabled: le van-e tiltva (opcionális, alapértelmezett: False)
            - size: méret (opcionális, alapértelmezett: "md")
            - help: segítő szöveg (opcionális)
            - tooltip: tooltip szöveg (opcionális)
        columns (int, optional): Oszlopok száma. Defaults to None (egyenlő a gombok számával).
    
    Returns:
        list: A gombkattintások listája (True/False minden gombra)
    """
    # Ha nincs megadva oszlopszám, használjuk a gombok számát
    if columns is None:
        columns = len(buttons)
    
    # Létrehozzuk az oszlopokat
    cols = st.columns(columns)
    
    # Egyenlően osztjuk el a gombokat az oszlopokban
    clicks = []
    for i, button in enumerate(buttons):
        with cols[i % columns]:
            click = create_styled_button(
                label=button["label"],
                icon=button.get("icon"),
                on_click=button.get("on_click"),
                key=button.get("key"),
                type=button.get("type", "primary"),
                disabled=button.get("disabled", False),
                size=button.get("size", "md"),
                help=button.get("help"),
                tooltip=button.get("tooltip")
            )
            clicks.append(click)
    
    return clicks