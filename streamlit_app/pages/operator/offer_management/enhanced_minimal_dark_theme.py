"""
Enhanced Minimal Dark Theme - Best Practices Implementation
Fejlesztett minimal dark theme a dokumentáció best practices alapján
"""
import streamlit as st
from datetime import datetime
import plotly.graph_objects as go
import hashlib

def inject_enhanced_styles():
    """Fejlesztett CSS stílusok best practices alapján"""
    st.markdown("""
    <style>
        /* Alap háttér és reset */
        .stApp {
            background-color: #0a0a0a;
        }
        
        /* Sticky Action Bar */
        .sticky-action-bar {
            position: sticky;
            top: 0;
            z-index: 999;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 1rem;
            margin: -1rem -1rem 2rem -1rem;
            border-bottom: 1px solid #2a2a2a;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .action-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .action-btn {
            padding: 0.5rem 1rem;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 6px;
            color: white;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            background: #2a2a2a;
            border-color: #3a3a3a;
            transform: translateY(-1px);
        }
        
        .action-btn.primary {
            background: #0099e0;
            border-color: #0099e0;
        }
        
        .action-btn.primary:hover {
            background: #0077b5;
        }
        
        /* Enhanced Info Panels */
        .info-panel {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            color: #ffffff;
        }
        
        .info-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
        }
        
        .panel-header {
            padding: 1.5rem;
            border-bottom: 1px solid #2a2a2a;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .panel-header:hover {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .panel-content {
            padding: 1.5rem;
            transition: all 0.3s ease;
            color: #ffffff;
        }
        
        /* Grid layouts */
        .panel-content .grid-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            color: #ffffff;
        }
        
        .panel-content .grid-layout div {
            color: #ffffff;
        }
        
        .panel-content .grid-layout strong {
            color: #a0a0a0;
        }
        
        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }
        
        .status-indicator:hover {
            transform: scale(1.05);
        }
        
        /* Enhanced Timeline */
        .timeline-container {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline-line {
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #10dc60, #0099e0);
            opacity: 0.3;
        }
        
        .timeline-event {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            animation: slideInRight 0.3s ease-out;
        }
        
        .timeline-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-top: 0.25rem;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }
        
        .timeline-dot:hover {
            transform: scale(1.2);
        }
        
        .timeline-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.02);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #2a2a2a;
            transition: all 0.2s ease;
        }
        
        .timeline-content:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: #3a3a3a;
        }
        
        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            animation: slideIn 0.3s ease-out;
        }
        
        /* Expandable Sections */
        .expandable-section {
            margin-bottom: 1rem;
        }
        
        .expand-button {
            width: 100%;
            text-align: left;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ffffff;
            transition: all 0.2s ease;
        }
        
        .expand-button:hover {
            background: #2a2a2a;
            border-color: #3a3a3a;
        }
        
        /* Animations */
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        @keyframes slideInRight {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .sticky-action-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .action-group {
                justify-content: center;
            }
            
            .info-panel {
                margin-bottom: 1rem;
            }
            
            .panel-header, .panel-content {
                padding: 1rem;
            }
        }
        
        /* Keyboard shortcut indicators */
        [data-shortcut]::after {
            content: attr(data-shortcut);
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
        }
        
        [data-shortcut]:hover::after {
            opacity: 1;
        }
        
        /* Progress indicators */
        .progress-enhanced {
            background: #2a2a2a;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
            position: relative;
        }
        
        .progress-fill-enhanced {
            height: 100%;
            background: linear-gradient(90deg, #10dc60, #00dc82);
            transition: width 0.5s ease;
            position: relative;
        }
        
        .progress-fill-enhanced::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
    """, unsafe_allow_html=True)

def render_status_indicator(status):
    """Egységes státusz megjelenítés színkódolással"""
    status_config = {
        "CREATED": {"color": "#FFA07A", "icon": "🆕", "text": "Létrehozva"},
        "CONFIRMED_BY_COMPANY": {"color": "#FFD700", "icon": "✅", "text": "Visszaigazolva"},
        "ACCEPTED_BY_USER": {"color": "#98FB98", "icon": "✔️", "text": "Elfogadva"},
        "REJECTED_BY_USER": {"color": "#FFB6C1", "icon": "❌", "text": "Elutasítva"},
        "FINALIZED": {"color": "#87CEEB", "icon": "🔒", "text": "Véglegesítve"}
    }
    
    config = status_config.get(status, {"color": "#6c757d", "icon": "❓", "text": status})
    
    return f"""
    <div class="status-indicator" style="background: {config['color']}20; border: 1px solid {config['color']}40;">
        <span style="font-size: 1.2rem;">{config['icon']}</span>
        <span style="color: {config['color']};">{config['text']}</span>
    </div>
    """

def render_sticky_action_bar(offer_id, current_status):
    """Sticky műveleti sáv a dokumentáció alapján"""
    valid_statuses = get_valid_next_statuses(current_status)
    status_options = ""
    for status_key, status_label in valid_statuses.items():
        status_options += f'<option value="{status_key}">{status_label}</option>'
    
    return f"""
    <div class="sticky-action-bar">
        <div class="action-group">
            <button onclick="window.history.back()" class="action-btn" data-shortcut="Alt+←">
                ← Vissza
            </button>
            <span style="color: #808080; font-size: 0.875rem;">Ajánlat #{offer_id}</span>
        </div>
        
        <div class="action-group" style="margin-left: auto;">
            <select class="action-btn" onchange="handleStatusChange(this.value)" data-shortcut="S">
                <option value="">Státusz váltás...</option>
                {status_options}
            </select>
            
            <button class="action-btn primary" data-shortcut="E">✏️ Szerkesztés</button>
            <button class="action-btn" data-shortcut="Ctrl+P">📊 Export</button>
            <button class="action-btn" onclick="toggleMenu()" data-shortcut="M">⋮</button>
        </div>
    </div>
    """

def render_info_panel(title, icon, content, color_accent="#10dc60", expanded=True, panel_id=None):
    """Egységes panel design a dokumentáció alapján"""
    if panel_id is None:
        panel_id = f"panel_{hashlib.md5(title.encode()).hexdigest()[:8]}"
    
    display_style = "display: block;" if expanded else "display: none;"
    arrow = "▼" if expanded else "▶"
    
    return f"""
    <div class="info-panel">
        <div style="border-top: 3px solid {color_accent};"></div>
        
        <div class="panel-header" onclick="togglePanel('{panel_id}')">
            <h3 style="margin: 0; display: flex; align-items: center; gap: 0.75rem;">
                <span style="font-size: 1.5rem;">{icon}</span>
                <span style="color: #ffffff; font-weight: 600;">{title}</span>
                <span id="{panel_id}-arrow" style="margin-left: auto; transition: transform 0.3s;">
                    {arrow}
                </span>
            </h3>
        </div>
        
        <div id="{panel_id}" class="panel-content" style="{display_style}">
            {content}
        </div>
    </div>
    
    <script>
    function togglePanel(panelId) {{
        const panel = document.getElementById(panelId);
        const arrow = document.getElementById(panelId + '-arrow');
        
        if (panel && arrow) {{
            if (panel.style.display === 'none') {{
                panel.style.display = 'block';
                arrow.textContent = '▼';
            }} else {{
                panel.style.display = 'none';
                arrow.textContent = '▶';
            }}
        }}
    }}
    </script>
    """

def render_enhanced_timeline(events):
    """Vizuális timeline a dokumentáció alapján"""
    timeline_html = """
    <div class="timeline-container">
        <div class="timeline-line"></div>
    """
    
    for i, event in enumerate(events):
        is_completed = event.get('completed', False)
        dot_color = "#10dc60" if is_completed else "#666666"
        dot_shadow = f"box-shadow: 0 0 0 4px {dot_color}20;"
        
        timeline_html += f"""
        <div class="timeline-event" style="animation-delay: {i * 0.1}s;">
            <div class="timeline-dot" style="background: {dot_color}; {dot_shadow}">
            </div>
            
            <div class="timeline-content">
                <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.25rem;">
                    {event['title']}
                </div>
                <div style="font-size: 0.875rem; color: #808080;">
                    {event['date']} • {event['user']}
                </div>
                {f'<div style="font-size: 0.875rem; color: #a0a0a0; margin-top: 0.5rem;">{event.get("note", "")}</div>' if event.get("note") else ''}
            </div>
        </div>
        """
    
    timeline_html += "</div>"
    return timeline_html

def show_notification(message, type="info", duration=3000):
    """Toast notification a dokumentáció alapján"""
    colors = {
        "success": {"bg": "#28a745", "icon": "✅"},
        "error": {"bg": "#dc3545", "icon": "❌"},
        "warning": {"bg": "#ffc107", "icon": "⚠️"},
        "info": {"bg": "#17a2b8", "icon": "ℹ️"}
    }
    
    config = colors.get(type, colors["info"])
    notification_id = f"notification_{hashlib.md5(message.encode()).hexdigest()[:8]}"
    
    notification_html = f"""
    <div id="{notification_id}" class="notification" 
         style="background: {config['bg']}; color: white;">
        <span style="font-size: 1.5rem;">{config['icon']}</span>
        <span>{message}</span>
    </div>
    
    <script>
    setTimeout(() => {{
        const el = document.getElementById('{notification_id}');
        if (el) {{
            el.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => el.remove(), 300);
        }}
    }}, {duration});
    </script>
    """
    
    st.markdown(notification_html, unsafe_allow_html=True)

def inject_keyboard_shortcuts():
    """Billentyűparancsok a dokumentáció alapján"""
    shortcuts_js = """
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const shortcuts = {
            'j': () => navigateNext(),
            'k': () => navigatePrev(),
            'e': () => toggleEditMode(),
            's': (e) => { if (e.altKey) handleStatusDialog(); },
            'Escape': () => cancelAction(),
            'Enter': () => confirmAction(),
            'r': (e) => { if (e.altKey) location.reload(); },
            'p': (e) => { if (e.ctrlKey) exportData(); },
            'm': () => toggleMenu()
        };
        
        document.addEventListener('keydown', function(e) {
            // Skip if typing in input
            if (['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName)) {
                return;
            }
            
            const key = e.key.toLowerCase();
            const handler = shortcuts[key] || shortcuts[e.key];
            if (handler) {
                e.preventDefault();
                handler(e);
            }
        });
        
        // Show shortcut hints
        document.querySelectorAll('[data-shortcut]').forEach(el => {
            const shortcut = el.dataset.shortcut;
            if (el.title) {
                el.title += ` (${shortcut})`;
            } else {
                el.title = `Shortcut: ${shortcut}`;
            }
        });
    });
    
    function navigateNext() {
        console.log('Navigate next (j)');
        // Implementation for navigation
    }
    
    function navigatePrev() {
        console.log('Navigate prev (k)');
        // Implementation for navigation
    }
    
    function toggleEditMode() {
        console.log('Toggle edit mode (e)');
        // Implementation for edit mode
    }
    
    function handleStatusDialog() {
        console.log('Status dialog (Alt+S)');
        // Implementation for status change
    }
    
    function cancelAction() {
        console.log('Cancel (Escape)');
        // Implementation for cancel
    }
    
    function confirmAction() {
        console.log('Confirm (Enter)');
        // Implementation for confirm
    }
    
    function exportData() {
        console.log('Export (Ctrl+P)');
        // Implementation for export
    }
    
    function toggleMenu() {
        console.log('Toggle menu (m)');
        // Implementation for menu toggle
    }
    
    function handleStatusChange(status) {
        if (status) {
            console.log('Status change to:', status);
            // Implementation for status change
        }
    }
    </script>
    """
    st.markdown(shortcuts_js, unsafe_allow_html=True)

def render_enhanced_progress_bar(current, total, label="Progress"):
    """Enhanced progress bar shimmer effekttel"""
    if total == 0:
        percentage = 0
    else:
        percentage = (current / total) * 100
    
    return f"""
    <div style="margin: 1rem 0;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="color: #a0a0a0; font-size: 0.875rem;">{label}</span>
            <span style="color: #ffffff; font-size: 0.875rem;">{percentage:.1f}%</span>
        </div>
        <div class="progress-enhanced">
            <div class="progress-fill-enhanced" style="width: {percentage}%;"></div>
        </div>
    </div>
    """

def get_valid_next_statuses(current_status):
    """Érvényes következő státuszok"""
    status_transitions = {
        "CREATED": {
            "CONFIRMED_BY_COMPANY": "Visszaigazolás"
        },
        "CONFIRMED_BY_COMPANY": {
            "ACCEPTED_BY_USER": "Elfogadás",
            "REJECTED_BY_USER": "Elutasítás"
        },
        "ACCEPTED_BY_USER": {
            "FINALIZED": "Véglegesítés"
        },
        "REJECTED_BY_USER": {},
        "FINALIZED": {}
    }
    
    return status_transitions.get(current_status, {})

def render_enhanced_dark_theme_offer(offer):
    """Enhanced minimal dark theme renderelés"""
    # CSS és JavaScript injektálás
    inject_enhanced_styles()
    inject_keyboard_shortcuts()
    
    # Sticky action bar
    action_bar_html = render_sticky_action_bar(offer.get('id', 'N/A'), offer.get('status', 'CREATED'))
    st.markdown(action_bar_html, unsafe_allow_html=True)
    
    # Header with status indicator
    status_html = render_status_indicator(offer.get('status', 'CREATED'))
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #1a1a1a, #2a2a2a); 
                border: 1px solid #2a2a2a; 
                border-radius: 12px; 
                padding: 2rem; 
                margin-bottom: 2rem;
                border-top: 4px solid #10dc60;
                animation: fadeIn 0.5s ease-out;">
        <h1 style="margin: 0; color: white;">Ajánlat #{offer.get('id', 'N/A')}</h1>
        <p style="color: #808080; margin: 0.5rem 0;">
            Utoljára módosítva: {_format_datetime(offer.get('updated_at') or offer.get('created_at'))}
        </p>
        {status_html}
    </div>
    """, unsafe_allow_html=True)
    
    # Main content in panels
    col1, col2 = st.columns(2)
    
    with col1:
        # Offer info panel
        offer_content = render_offer_info_content(offer)
        offer_panel = render_info_panel("Ajánlat adatai", "📋", offer_content, "#10dc60")
        st.markdown(offer_panel, unsafe_allow_html=True)
        
        # Delivery info panel
        delivery_content = render_delivery_info_content(offer)
        delivery_panel = render_info_panel("Szállítási információk", "🚚", delivery_content, "#0099e0")
        st.markdown(delivery_panel, unsafe_allow_html=True)
        
        # Timeline panel
        timeline_events = get_timeline_events(offer)
        timeline_content = render_enhanced_timeline(timeline_events)
        timeline_panel = render_info_panel("Állapotváltozási napló", "📅", timeline_content, "#ff8c1a", False)
        st.markdown(timeline_panel, unsafe_allow_html=True)
    
    with col2:
        # Confirmation panel
        confirmation_content = render_confirmation_content(offer)
        confirmation_panel = render_info_panel("Visszaigazolás", "📊", confirmation_content, "#ff8c1a")
        st.markdown(confirmation_panel, unsafe_allow_html=True)
        
        # Producer panel
        producer_content = render_producer_content(offer)
        producer_panel = render_info_panel("Termelő adatai", "👤", producer_content, "#10dc60")
        st.markdown(producer_panel, unsafe_allow_html=True)
        
        # Chart panel
        chart_content = render_chart_content(offer)
        chart_panel = render_info_panel("Ár összehasonlítás", "📈", chart_content, "#0099e0", False)
        st.markdown(chart_panel, unsafe_allow_html=True)
    
    # Success notification example
    if st.session_state.get('show_notification', False):
        show_notification("Ajánlat sikeresen betöltve!", "success")
        st.session_state.show_notification = False

# Content rendering functions
def render_offer_info_content(offer):
    """Ajánlat információs tartalom"""
    # Safely extract and format values
    offer_id = str(offer.get('id', 'N/A'))
    delivery_date = _format_date(offer.get('delivery_date'))
    product_name = str(offer.get('product_type', {}).get('name', 'N/A'))
    category_name = str(offer.get('product_type', {}).get('category', {}).get('name', 'N/A'))
    quantity = _format_quantity(offer.get('quantity_in_kg'))
    
    return f"""
    <div class="grid-layout">
        <div><strong>Azonosító:</strong></div>
        <div>{offer_id}</div>
        
        <div><strong>Beszállítás:</strong></div>
        <div>{delivery_date}</div>
        
        <div><strong>Termék:</strong></div>
        <div>{product_name}</div>
        
        <div><strong>Kategória:</strong></div>
        <div>{category_name}</div>
        
        <div><strong>Mennyiség:</strong></div>
        <div style="color: #0099e0; font-weight: 600;">{quantity} kg</div>
    </div>
    """

def render_delivery_info_content(offer):
    """Szállítási információs tartalom"""
    delivery_date = offer.get('delivery_date')
    delivery_status = _get_delivery_status(delivery_date)
    formatted_date = _format_date(delivery_date)
    
    return f"""
    <div class="grid-layout">
        <div><strong>Szállítási dátum:</strong></div>
        <div>{formatted_date}</div>
        
        <div><strong>Státusz:</strong></div>
        <div style="color: #10dc60; font-weight: 500;">{delivery_status}</div>
        
        <div><strong>Szállítási cím:</strong></div>
        <div>Központi raktár</div>
        
        <div><strong>Szállítási mód:</strong></div>
        <div>Saját szállítás</div>
    </div>
    """

def render_confirmation_content(offer):
    """Visszaigazolási tartalom"""
    quantity = _to_float(offer.get('quantity_in_kg', 0))
    confirmed_quantity = _to_float(offer.get('confirmed_quantity', quantity))
    confirmed_price = _to_float(offer.get('confirmed_price', offer.get('price', 0)))
    total_value = confirmed_quantity * confirmed_price
    
    progress_bar = render_enhanced_progress_bar(confirmed_quantity, quantity, "Teljesítés")
    quantity_formatted = _format_quantity(quantity)
    confirmed_quantity_formatted = _format_quantity(confirmed_quantity)
    price_formatted = _format_price(confirmed_price)
    total_formatted = _format_price(total_value)
    
    return f"""
    <div class="grid-layout">
        <div><strong>Eredeti mennyiség:</strong></div>
        <div>{quantity_formatted} kg</div>
        
        <div><strong>Visszaigazolt:</strong></div>
        <div style="color: #10dc60; font-weight: 600;">{confirmed_quantity_formatted} kg</div>
        
        <div style="grid-column: 1 / -1;">
            {progress_bar}
        </div>
        
        <div><strong>Visszaigazolt ár:</strong></div>
        <div>{price_formatted}/kg</div>
        
        <div><strong>Összérték:</strong></div>
        <div style="color: #0099e0; font-weight: 600; font-size: 1.1rem;">{total_formatted}</div>
    </div>
    """

def render_producer_content(offer):
    """Termelő információs tartalom"""
    user = offer.get('user', {})
    contact_name = str(user.get('contact_name', 'N/A'))
    company_name = str(user.get('company_name', 'N/A'))
    phone = str(user.get('phone', 'N/A'))
    email = user.get('email', '')
    
    email_html = f'<a href="mailto:{email}" style="color: #0099e0; text-decoration: none;">{email}</a>' if email else 'N/A'
    
    return f"""
    <div class="grid-layout">
        <div><strong>Név:</strong></div>
        <div>{contact_name}</div>
        
        <div><strong>Cégnév:</strong></div>
        <div>{company_name}</div>
        
        <div><strong>Telefon:</strong></div>
        <div style="color: #10dc60;">{phone}</div>
        
        <div><strong>Email:</strong></div>
        <div>{email_html}</div>
    </div>
    """

def render_chart_content(offer):
    """Grafikon tartalom"""
    # This would contain the Plotly chart
    return """
    <div style="text-align: center; padding: 2rem; color: #808080;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
        <div style="color: #ffffff; font-size: 1rem;">Grafikon itt jelenik meg</div>
        <div style="font-size: 0.875rem; margin-top: 0.5rem; color: #a0a0a0;">
            Plotly chart komponens
        </div>
    </div>
    """

def get_timeline_events(offer):
    """Timeline események lekérése"""
    events = []
    
    if offer.get('created_at'):
        events.append({
            'title': 'Létrehozva',
            'date': _format_datetime(offer.get('created_at')),
            'user': 'Rendszer',
            'completed': True
        })
    
    if offer.get('confirmed_at'):
        events.append({
            'title': 'Cég által visszaigazolva',
            'date': _format_datetime(offer.get('confirmed_at')),
            'user': 'Kiss Péter',
            'completed': True
        })
    
    if offer.get('accepted_at'):
        events.append({
            'title': 'Termelő által elfogadva',
            'date': _format_datetime(offer.get('accepted_at')),
            'user': offer.get('user', {}).get('contact_name', 'N/A'),
            'completed': True
        })
    
    return events

# Import helper functions from minimal_dark_theme
def _to_float(value):
    """Érték biztonságos float konvertálása"""
    try:
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
            return float(cleaned) if cleaned else 0.0
        return 0.0
    except (ValueError, TypeError):
        return 0.0

def _format_quantity(value):
    """Mennyiség formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f}"

def _format_price(value):
    """Ár formázása"""
    numeric_value = _to_float(value)
    return f"{numeric_value:,.0f} Ft"

def _format_date(value):
    """Dátum formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d.")
        return "-"
    except:
        return "-"

def _format_datetime(value):
    """Dátum és idő formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y. %m. %d. %H:%M")
        return "-"
    except:
        return "-"

def _get_delivery_status(delivery_date):
    """Szállítási státusz meghatározása"""
    if not delivery_date:
        return "Nincs megadva"
    
    try:
        if isinstance(delivery_date, str):
            delivery_dt = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
        else:
            delivery_dt = delivery_date
        
        days_until = (delivery_dt - datetime.now()).days
        
        if days_until < 0:
            return f"Lejárt ({abs(days_until)} napja)"
        elif days_until == 0:
            return "Ma esedékes"
        elif days_until == 1:
            return "Holnap esedékes"
        else:
            return f"{days_until} nap múlva esedékes"
    except:
        return "Nincs megadva"

def test_html_rendering():
    """Test function to verify HTML rendering works correctly"""
    import streamlit as st
    
    # Test the CSS injection
    inject_enhanced_styles()
    
    # Test simple HTML rendering
    test_html = """
    <div class="info-panel">
        <div class="panel-content">
            <div class="grid-layout">
                <div><strong>Test Label:</strong></div>
                <div>Test Value</div>
            </div>
        </div>
    </div>
    """
    
    st.markdown("### HTML Rendering Test")
    st.markdown(test_html, unsafe_allow_html=True)
    st.markdown("### End Test")
    
    return True