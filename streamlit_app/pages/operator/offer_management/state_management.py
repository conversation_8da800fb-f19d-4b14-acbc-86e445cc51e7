"""
State management functions for the offer management page.
Contains functions for initializing and managing session state and caching.
"""
import streamlit as st
import time
import logging
import uuid

logger = logging.getLogger(__name__)

def init_page_state():
    """
    Oldal állapotának inicializálása.
    
    Ez a függvény inicializálja az alapvető session state változókat.
    """
    # Oldal inicializálása
    if "page_initialized" not in st.session_state:
        logger.info("Initializing page state for the first time")
        
        # Alapértelmezett értékek
        st.session_state.page_initialized = True
        st.session_state.is_mobile = False
        st.session_state.current_page = 1
        st.session_state.offers_per_page = 20
        
        # Cache inicializálása
        st.session_state.offer_cache = {}
        st.session_state.producers_cache = {}
        st.session_state.products_cache = {}
        st.session_state.api_cache = {}
        
        # Utolsó frissítési idők
        st.session_state.last_refresh = {}

def inject_keyboard_shortcuts():
    """
    JavaScript alapú billentyűzetkombinációk befecskendezése a streamlit alkalmazásba.
    
    Ez a függvény a következő billentyűparancsokat teszi lehetővé:
    - Alt+N: Új ajánlat létrehozása
    - Alt+R: Oldal frissítése
    - Alt+S: Keresés indítása
    - Alt+B: Vissza navigálás
    - Alt+E: Szerkesztés mód ki/be
    - Alt+A: Ajánlat elfogadása
    - Alt+D: Ajánlat törlése
    - Alt+← / →: Előző / Következő oldal
    - Escape: Vissza vagy modális ablak bezárása
    - 1-9: Gyors kiválasztás a táblázatban
    - J/K: Lefelé/Felfelé navigálás a táblázatban
    - F: Fókusz a keresőmezőre
    """
    # JavaScript a gyorsbillentyűkhöz - bővített változat
    shortcuts_js = """
    <script>
    // Az oldal betöltésekor aktiváljuk a billentyűzetes vezérlést
    document.addEventListener('DOMContentLoaded', function() {
        // Megváltozott e már a jelenlegi key handler
        let handlerActive = false;
        
        // Ellenőrizzük minden 100ms-ban, hogy található-e már a DOM-ban gomb/táblázat
        const checkInterval = setInterval(function() {
            if (\!handlerActive && 
                (document.querySelector('table') || 
                document.querySelector('button[kind="primary"]'))) {
                
                // A DOM már betöltődött, inicializáljuk a billentyűzetkezelőt
                initKeyboardHandler();
                handlerActive = true;
                clearInterval(checkInterval);
            }
        }, 100);
        
        function initKeyboardHandler() {
            document.addEventListener('keydown', function(e) {
                // Ne kezeljük a billentyűeseményeket, ha input mezőben vagyunk
                if (document.activeElement.tagName === 'INPUT' || 
                    document.activeElement.tagName === 'TEXTAREA' ||
                    document.activeElement.tagName === 'SELECT') {
                    return;
                }
                
                // Aktuális URL ellenőrzése, hogy részletes nézetben vagyunk-e
                const isDetailView = window.location.search.includes('offer_id=') || 
                                     document.title.includes('részletei');
                
                // Alt + billentyű kombinációk
                if (e.altKey) {
                    switch(e.key) {
                        case 'n':  // Alt+N: Új ajánlat
                            // Új ajánlat gomb megkeresése és kattintás
                            clickButton('Új ajánlat', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'r':  // Alt+R: Frissítés
                            // Frissítés gomb megkeresése és kattintás
                            if (\!clickButton('Frissítés', 'button', false) && 
                                \!clickButton('🔄', 'button', false)) {
                                // Ha nincs külön frissítés gomb, akkor az oldalt frissítjük
                                window.location.reload();
                            }
                            e.preventDefault();
                            break;
                            
                        case 's':  // Alt+S: Keresés
                            // Keresés gomb megkeresése és kattintás
                            clickButton('Keresés', 'button', false) || clickButton('🔍', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'b':  // Alt+B: Vissza
                            // Vissza gomb megkeresése és kattintás
                            clickButton('Vissza', 'button', true) || 
                            clickButton('⬅️', 'button', true) || 
                            clickButton('Mégsem', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'e':  // Alt+E: Szerkesztés
                            // Szerkesztés gomb megkeresése és kattintás
                            clickButton('Szerkesztés', 'button', true) || 
                            clickButton('✏️', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'a':  // Alt+A: Jóváhagyás/Elfogadás
                            // Jóváhagyás gomb megkeresése és kattintás
                            clickButton('Jóváhagyás', 'button', false) || 
                            clickButton('Elfogadás', 'button', false) || 
                            clickButton('✅', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'd':  // Alt+D: Törlés
                            // Törlés gomb megkeresése és kattintás
                            clickButton('Törlés', 'button', false) || 
                            clickButton('🗑️', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'ArrowLeft':  // Alt+BalNyíl: Előző oldal
                            // Előző oldal gomb megkeresése és kattintás
                            clickButton('Előző oldal', 'button', false) || 
                            clickButton('◀️', 'button', false);
                            e.preventDefault();
                            break;
                            
                        case 'ArrowRight':  // Alt+JobbNyíl: Következő oldal
                            // Következő oldal gomb megkeresése és kattintás
                            clickButton('Következő oldal', 'button', false) || 
                            clickButton('▶️', 'button', false);
                            e.preventDefault();
                            break;
                    }
                } else {
                    // Billentyűparancsok Alt nélkül
                    switch(e.key) {
                        case 'j': // Le (következő elem)
                            navigateList(1);
                            e.preventDefault();
                            break;
                            
                        case 'k': // Fel (előző elem)
                            navigateList(-1);
                            e.preventDefault();
                            break;
                        
                        case 'f': // Fókusz a keresőmezőre
                            focusOnElement('input[aria-label="Keresés"]') || 
                            focusOnElement('input[placeholder*="Keresés"]');
                            e.preventDefault();
                            break;
                            
                        case 'Escape': // Vissza vagy modális ablak bezárása
                            // Ha van modális ablak, azt zárjuk be
                            const modalClose = document.querySelector('.modal-close') || 
                                              document.querySelector('.close-button');
                            if (modalClose) {
                                modalClose.click();
                                e.preventDefault();
                                return;
                            }
                            
                            // Részletes nézetből vissza a listához
                            if (isDetailView) {
                                clickButton('Vissza a listához', 'button', true);
                                e.preventDefault();
                            }
                            break;
                            
                        default:
                            // Számok 1-9: Táblázat sor kiválasztása
                            if (\!isNaN(parseInt(e.key)) && parseInt(e.key) >= 1 && parseInt(e.key) <= 9) {
                                selectTableRow(parseInt(e.key) - 1);
                                e.preventDefault();
                            }
                            break;
                    }
                }
            });
        }
        
        // Segédfüggvények
        function navigateList(direction) {
            const rows = document.querySelectorAll('table tbody tr');
            if (rows.length === 0) return;
            
            // Megkeressük, hogy melyik sor van kijelölve
            let activeIndex = -1;
            rows.forEach((row, index) => {
                if (row.classList.contains('selected') || 
                    row.hasAttribute('data-selected') || 
                    row.classList.contains('active')) {
                    activeIndex = index;
                }
            });
            
            // Kiválasztjuk a következő/előző sort
            let newIndex = (activeIndex < 0) ? 0 : activeIndex + direction;
            if (newIndex < 0) newIndex = 0;
            if (newIndex >= rows.length) newIndex = rows.length - 1;
            
            // Csak ha tényleg változott az index, akkor kattintunk
            if (newIndex \!== activeIndex) {
                selectTableRow(newIndex);
            }
        }
        
        function selectTableRow(rowIndex) {
            // Táblázat sorainak megkeresése
            const rows = document.querySelectorAll('table tbody tr');
            if (rows && rows.length > rowIndex) {
                // Az adott sor kiválasztása
                const row = rows[rowIndex];
                
                // Ellenőrizzük, hogy a sor már ki van-e választva
                if (row.classList.contains('selected') || 
                    row.hasAttribute('data-selected') || 
                    row.classList.contains('active')) {
                    // Részletek megnyitása dupla kattintással
                    const viewBtn = Array.from(document.querySelectorAll('button')).find(btn => 
                        btn.textContent.includes('Részletek')
                    );
                    if (viewBtn) {
                        viewBtn.click();
                    }
                } else {
                    // Sor kiválasztása
                    // Először keresünk kattintható elemet a sorban
                    const clickable = row.querySelector('a, button, input[type="checkbox"]');
                    if (clickable) {
                        clickable.click();
                    } else {
                        // Ha nincs kifejezetten kattintható elem, akkor a sorra kattintunk
                        simulateClick(row);
                    }
                }
            }
        }
        
        function clickButton(textContent, tagName = 'button', partialMatch = false) {
            // Megkeressük a gombot szöveg alapján
            const elements = Array.from(document.querySelectorAll(tagName));
            const element = elements.find(el => {
                if (partialMatch) {
                    return el.textContent.trim().includes(textContent);
                } else {
                    return el.textContent.trim() === textContent;
                }
            });
            
            if (element) {
                simulateClick(element);
                return true;
            }
            return false;
        }
        
        function simulateClick(element) {
            if (element) {
                // Létrehozunk egy kattintási eseményt
                const event = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                // Elküldünk egy kattintási eseményt
                element.dispatchEvent(event);
            }
        }
        
        function focusOnElement(selector) {
            const element = document.querySelector(selector);
            if (element) {
                element.focus();
                return true;
            }
            return false;
        }
    });
    </script>
    """
    
    # Beillesztjük a JavaScript kódot
    st.markdown(shortcuts_js, unsafe_allow_html=True)
    
    # Segítő tippek megjelenítése (ha fejlesztői módban vagyunk)
    if st.session_state.get('dev_mode', False):
        with st.expander("⌨️ Billentyűparancsok", expanded=False):
            st.write("""
            - **J/K**: Le/Fel navigálás az ajánlatok között
            - **Alt+N/R/S**: Új/Frissítés/Keresés
            - **Alt+B**: Vissza
            - **Alt+E**: Szerkesztés mód
            - **Alt+A/D**: Elfogadás/Törlés
            - **Alt+←/→**: Előző/Következő oldal
            - **Escape**: Vissza a listához vagy ablak bezárása
            - **1-9**: Gyors kiválasztás a táblázatban
            - **F**: Fókusz a keresőmezőre
            """)

def lazy_load_related_data(offer_id, data_type):
    """
    Kapcsolódó adatok késleltetett betöltése az ajánlathoz.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        data_type (str): Az adattípus (logs, attachments, stb.)
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    # Biztosítjuk, hogy az offer_id integer legyen a konzisztens használathoz
    try:
        if isinstance(offer_id, str) and offer_id.isdigit():
            offer_id = int(offer_id)
    except (ValueError, TypeError):
        logger.error(f"Érvénytelen ajánlat azonosító a lazy_load_related_data függvényben: {offer_id}")
    
    # Inicializáljuk a lazy loaded adatokat, ha még nem létezik
    if "lazy_loaded_data" not in st.session_state:
        st.session_state.lazy_loaded_data = {}
    
    # Ajánlat specifikus cache inicializálása
    if offer_id not in st.session_state.lazy_loaded_data:
        st.session_state.lazy_loaded_data[offer_id] = {}
    
    # Ha már van cached adat, akkor azt adjuk vissza
    if data_type in st.session_state.lazy_loaded_data[offer_id]:
        cached_data = st.session_state.lazy_loaded_data[offer_id][data_type]
        # Ellenőrizzük, hogy a cached adat nem túl régi-e (15 perces időkorlát)
        if time.time() - cached_data.get("timestamp", 0) < 900:  # 15 perc = 900 másodperc
            return cached_data.get("success", False), cached_data.get("data")
    
    # Különböző típusú adatok lekérése
    try:
        if data_type == "logs":
            # Import a funkcióhoz több lehetséges útvonalon
            try:
                # Try absolute import first
                from streamlit_app.pages.operator.offer_management.api_client import get_offer_logs
            except ImportError:
                try:
                    # Try module import with full path
                    from pages.operator.offer_management.api_client import get_offer_logs
                except ImportError:
                    try:
                        # Try direct local import
                        from api_client import get_offer_logs
                    except ImportError:
                        # Fallback implementation if import fails
                        logger.warning("Could not import get_offer_logs, using fallback")
                        def get_offer_logs(offer_id):
                            return False, "API client not available"
                        
            success, result = get_offer_logs(offer_id)
        elif data_type == "attachments":
            # Import a funkcióhoz több lehetséges útvonalon
            try:
                # Try absolute import first
                from streamlit_app.pages.operator.offer_management.api_client import get_offer_attachments
            except ImportError:
                try:
                    # Try module import with full path
                    from pages.operator.offer_management.api_client import get_offer_attachments
                except ImportError:
                    try:
                        # Try direct local import
                        from api_client import get_offer_attachments
                    except ImportError:
                        # Fallback implementation if import fails
                        logger.warning("Could not import get_offer_attachments, using fallback")
                        def get_offer_attachments(offer_id):
                            return False, "API client not available"
                        
            success, result = get_offer_attachments(offer_id)
        elif data_type == "related_offers":
            # Kapcsolódó ajánlatok lekérése, pl. ugyanattól a termelőtől
            # Import a funkciókhoz több lehetséges útvonalon
            try:
                # Try absolute import first
                from streamlit_app.pages.operator.offer_management.api_client import get_offer, get_offers
            except ImportError:
                try:
                    # Try module import with full path
                    from pages.operator.offer_management.api_client import get_offer, get_offers
                except ImportError:
                    try:
                        # Try direct local import
                        from api_client import get_offer, get_offers
                    except ImportError:
                        # Fallback implementation if import fails
                        logger.warning("Could not import get_offer and get_offers, using fallbacks")
                        def get_offer(offer_id):
                            return False, "API client not available"
                        def get_offers(params=None):
                            return False, "API client not available"
                
            success, offer_details = get_offer(offer_id)
            if success and offer_details:
                user_id = offer_details.get("user", {}).get("id")
                if user_id:
                    success, result = get_offers(params={"user_id": user_id, "limit": 5})
                else:
                    success, result = False, "Nem található felhasználó azonosító az ajánlathoz"
            else:
                success, result = False, "Nem található ajánlat a megadott azonosítóval"
        else:
            success, result = False, f"Ismeretlen adattípus: {data_type}"
        
        # Cache-eljük az eredményt
        st.session_state.lazy_loaded_data[offer_id][data_type] = {
            "success": success,
            "data": result,
            "timestamp": time.time()
        }
        
        return success, result
    except Exception as e:
        logger.error(f"Hiba a lazy_load_related_data függvényben ehhez az adattípushoz: {data_type}: {str(e)}")
        return False, str(e)

def lazy_load_cache(cache_key, data_loader_func, cache_ttl=300):
    """
    Általános lusta betöltés cache használatával.
    
    Args:
        cache_key (str): Cache kulcs a betöltött adathoz
        data_loader_func (callable): Függvény, ami betölti az adatokat
        cache_ttl (int, optional): Cache időtartama másodpercben. Defaults to 300.
        
    Returns:
        tuple: (success, data) ahol success egy boolean érték, data a betöltött adat vagy hibaüzenet
    """
    # API cache inicializálása, ha még nem létezik
    if "api_cache" not in st.session_state:
        st.session_state.api_cache = {}
    
    # Utolsó frissítések inicializálása, ha még nem létezik
    if "last_refresh" not in st.session_state:
        st.session_state.last_refresh = {}
    
    current_time = time.time()
    
    # Ellenőrizzük, hogy az adatok a cache-ben vannak-e és frissek-e
    if (cache_key in st.session_state.api_cache and 
        cache_key in st.session_state.last_refresh and 
        (current_time - st.session_state.last_refresh[cache_key]) < cache_ttl):
        
        # Az adatok frissek a cache-ben, visszaadjuk őket
        return True, st.session_state.api_cache[cache_key]
    
    # Az adatok nincsenek a cache-ben vagy elavultak, újra kell tölteni
    try:
        success, result = data_loader_func()
        
        if success:
            # Friss adatok a cache-be
            st.session_state.api_cache[cache_key] = result
            st.session_state.last_refresh[cache_key] = current_time
            
            return True, result
        else:
            # Hiba történt, logoljuk
            logger.error(f"Error loading data for cache key {cache_key}: {result}")
            return False, result
            
    except Exception as e:
        # Kivétel esetén logoljuk és visszaadjuk a hibát
        logger.error(f"Exception loading data for cache key {cache_key}: {str(e)}")
        return False, f"Hiba az adatok betöltése során: {str(e)}"

def clear_cache(keys=None):
    """
    Cache törlése, opcionálisan csak bizonyos kulcsokra.
    
    Args:
        keys (list, optional): Cache kulcsok listája, amelyeket törölni kell. 
                              Ha None, akkor az összes cache-t töröljük. Defaults to None.
    """
    if keys is None:
        # Teljes cache törlése
        if "api_cache" in st.session_state:
            st.session_state.api_cache = {}
        if "last_refresh" in st.session_state:
            st.session_state.last_refresh = {}
        if "offer_cache" in st.session_state:
            st.session_state.offer_cache = {}
        if "producers_cache" in st.session_state:
            st.session_state.producers_cache = {}
        if "products_cache" in st.session_state:
            st.session_state.products_cache = {}
        
        logger.info("Cleared all caches")
    else:
        # Csak a megadott kulcsok törlése
        for key in keys:
            if "api_cache" in st.session_state and key in st.session_state.api_cache:
                del st.session_state.api_cache[key]
            if "last_refresh" in st.session_state and key in st.session_state.last_refresh:
                del st.session_state.last_refresh[key]
            if key.startswith("offer_") and "offer_cache" in st.session_state:
                offer_id = key.split("_")[1]
                if offer_id in st.session_state.offer_cache:
                    del st.session_state.offer_cache[offer_id]
        
        logger.info(f"Cleared cache for keys: {keys}")

def get_producer_details(producer_id):
    """
    Termelő részletes adatainak lekérése, cache használatával.
    
    Args:
        producer_id (int/str): A termelő azonosítója
        
    Returns:
        dict: A termelő adatai vagy None hiba esetén
    """
    # Cache kulcs
    cache_key = f"producer_{producer_id}"
    
    # Betöltő függvény - több lehetséges útvonalon
    try:
        # Try absolute import first
        from streamlit_app.pages.operator.offer_management.api_client import get_producer
    except ImportError:
        try:
            # Try module import with full path
            from pages.operator.offer_management.api_client import get_producer
        except ImportError:
            try:
                # Try direct local import
                from api_client import get_producer
            except ImportError:
                # Fallback implementation if import fails
                logger.warning("Could not import get_producer, using fallback")
                def get_producer(producer_id):
                    return False, "API client not available"
    
    data_loader = lambda: get_producer(producer_id)
    
    # Lusta betöltés
    success, result = lazy_load_cache(cache_key, data_loader)
    
    return result if success else None

def get_product_details(product_id):
    """
    Termék részletes adatainak lekérése, cache használatával.
    
    Args:
        product_id (int/str): A termék azonosítója
        
    Returns:
        dict: A termék adatai vagy None hiba esetén
    """
    # Cache kulcs
    cache_key = f"product_{product_id}"
    
    # Betöltő függvény - több lehetséges útvonalon
    try:
        # Try absolute import first
        from streamlit_app.pages.operator.offer_management.api_client import get_product_type
    except ImportError:
        try:
            # Try module import with full path
            from pages.operator.offer_management.api_client import get_product_type
        except ImportError:
            try:
                # Try direct local import
                from api_client import get_product_type
            except ImportError:
                # Fallback implementation if import fails
                logger.warning("Could not import get_product_type, using fallback")
                def get_product_type(product_id):
                    return False, "API client not available"
    
    data_loader = lambda: get_product_type(product_id)
    
    # Lusta betöltés
    success, result = lazy_load_cache(cache_key, data_loader)
    
    return result if success else None
