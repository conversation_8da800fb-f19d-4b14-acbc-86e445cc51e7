"""
Keyboard Navigation and Accessibility Module
Implements keyboard shortcuts, ARIA attributes, and accessibility improvements
"""
import streamlit as st
import logging
from typing import Dict, List, Callable, Optional
import json

logger = logging.getLogger(__name__)

class KeyboardNavigationManager:
    """
    Manages keyboard shortcuts and navigation for the offer management interface.
    """
    
    def __init__(self):
        self.shortcuts = {
            # Global shortcuts
            'Alt+N': 'new_offer',
            'Alt+R': 'refresh_data',
            'Alt+S': 'search_focus',
            'Alt+F': 'filter_focus',
            'Alt+E': 'export_data',
            'Escape': 'cancel_action',
            
            # Navigation shortcuts
            'Alt+ArrowLeft': 'previous_page',
            'Alt+ArrowRight': 'next_page',
            'J': 'next_item',
            'K': 'previous_item',
            'Enter': 'select_item',
            
            # Filter shortcuts
            'F1': 'toggle_filters',
            'F2': 'clear_filters',
            'F3': 'save_filters',
            'F4': 'load_filters',
            
            # View shortcuts
            'V': 'toggle_view_mode',
            '1': 'list_view',
            '2': 'card_view',
            '3': 'grid_view'
        }
        
        self.current_focus = None
        self.navigation_stack = []
        
    def inject_keyboard_shortcuts(self):
        """Inject keyboard shortcuts into the page."""
        shortcuts_js = self._generate_shortcuts_js()
        
        st.markdown(f"""
        <script>
        {shortcuts_js}
        </script>
        """, unsafe_allow_html=True)
        
        # Also display keyboard help
        self._render_keyboard_help()
        
    def _generate_shortcuts_js(self) -> str:
        """Generate JavaScript for keyboard shortcuts."""
        shortcuts_json = json.dumps(self.shortcuts)
        
        return f"""
        // Keyboard shortcuts for offer management
        document.addEventListener('keydown', function(event) {{
            const shortcuts = {shortcuts_json};
            const key = getKeyCombo(event);
            
            if (shortcuts[key]) {{
                event.preventDefault();
                handleKeyboardShortcut(shortcuts[key], event);
            }}
        }});
        
        function getKeyCombo(event) {{
            let combo = '';
            
            if (event.altKey) combo += 'Alt+';
            if (event.ctrlKey) combo += 'Ctrl+';
            if (event.shiftKey) combo += 'Shift+';
            
            if (event.key === 'ArrowLeft') combo += 'ArrowLeft';
            else if (event.key === 'ArrowRight') combo += 'ArrowRight';
            else if (event.key === 'ArrowUp') combo += 'ArrowUp';
            else if (event.key === 'ArrowDown') combo += 'ArrowDown';
            else if (event.key === 'Escape') combo += 'Escape';
            else if (event.key === 'Enter') combo += 'Enter';
            else if (event.key.length === 1) combo += event.key.toUpperCase();
            else combo += event.key;
            
            return combo;
        }}
        
        function handleKeyboardShortcut(action, event) {{
            console.log('Keyboard shortcut:', action);
            
            switch(action) {{
                case 'refresh_data':
                    // Trigger refresh by clicking refresh button if it exists
                    const refreshBtn = document.querySelector('[data-testid="refresh-button"]');
                    if (refreshBtn) refreshBtn.click();
                    break;
                    
                case 'search_focus':
                    // Focus search input
                    const searchInput = document.querySelector('input[placeholder*="Keresés"]');
                    if (searchInput) searchInput.focus();
                    break;
                    
                case 'filter_focus':
                    // Focus first filter element
                    const filterPanel = document.querySelector('.modern-filter-panel');
                    if (filterPanel) {{
                        const firstInput = filterPanel.querySelector('input, select, button');
                        if (firstInput) firstInput.focus();
                    }}
                    break;
                    
                case 'toggle_filters':
                    // Toggle filter panel visibility
                    const toggleBtn = document.querySelector('[data-testid="filter-toggle"]');
                    if (toggleBtn) toggleBtn.click();
                    break;
                    
                case 'cancel_action':
                    // Close modals or cancel current action
                    const closeBtn = document.querySelector('[data-testid="close-modal"]');
                    if (closeBtn) closeBtn.click();
                    break;
                    
                case 'next_item':
                case 'previous_item':
                    // Navigate through offer items
                    navigateOfferItems(action === 'next_item' ? 1 : -1);
                    break;
                    
                default:
                    // Store action for Streamlit to handle
                    window.parent.postMessage({{
                        type: 'keyboard_shortcut',
                        action: action
                    }}, '*');
            }}
        }}
        
        function navigateOfferItems(direction) {{
            const offerCards = document.querySelectorAll('.offer-card, .offer-list-row');
            if (offerCards.length === 0) return;
            
            let currentIndex = getCurrentOfferIndex();
            let newIndex = currentIndex + direction;
            
            if (newIndex < 0) newIndex = offerCards.length - 1;
            if (newIndex >= offerCards.length) newIndex = 0;
            
            // Remove previous highlight
            offerCards.forEach(card => card.classList.remove('keyboard-focused'));
            
            // Add highlight to new item
            offerCards[newIndex].classList.add('keyboard-focused');
            offerCards[newIndex].scrollIntoView({{ behavior: 'smooth', block: 'center' }});
            
            // Store current index
            sessionStorage.setItem('currentOfferIndex', newIndex.toString());
        }}
        
        function getCurrentOfferIndex() {{
            const stored = sessionStorage.getItem('currentOfferIndex');
            return stored ? parseInt(stored) : -1;
        }}
        
        // Add CSS for keyboard focus
        const style = document.createElement('style');
        style.textContent = `
            .keyboard-focused {{
                outline: 2px solid #1a73e8 !important;
                outline-offset: 2px !important;
                box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.2) !important;
            }}
            
            .keyboard-help {{
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 1rem;
                border-radius: 8px;
                font-size: 0.875rem;
                z-index: 1000;
                max-width: 300px;
                display: none;
            }}
            
            .keyboard-help.show {{
                display: block;
            }}
            
            .keyboard-help h4 {{
                margin: 0 0 0.5rem 0;
                color: #1a73e8;
            }}
            
            .keyboard-help .shortcut {{
                display: flex;
                justify-content: space-between;
                margin: 0.25rem 0;
            }}
            
            .keyboard-help .key {{
                background: rgba(255, 255, 255, 0.2);
                padding: 0.125rem 0.25rem;
                border-radius: 3px;
                font-family: monospace;
                font-size: 0.75rem;
            }}
        `;
        document.head.appendChild(style);
        
        // Toggle keyboard help with F1
        document.addEventListener('keydown', function(event) {{
            if (event.key === 'F1') {{
                event.preventDefault();
                toggleKeyboardHelp();
            }}
        }});
        
        function toggleKeyboardHelp() {{
            let helpPanel = document.querySelector('.keyboard-help');
            if (!helpPanel) {{
                helpPanel = createKeyboardHelpPanel();
                document.body.appendChild(helpPanel);
            }}
            
            helpPanel.classList.toggle('show');
        }}
        
        function createKeyboardHelpPanel() {{
            const panel = document.createElement('div');
            panel.className = 'keyboard-help';
            panel.innerHTML = `
                <h4>⌨️ Billentyűparancsok</h4>
                <div class="shortcut"><span>Alt+R</span><span class="key">Frissítés</span></div>
                <div class="shortcut"><span>Alt+S</span><span class="key">Keresés</span></div>
                <div class="shortcut"><span>Alt+F</span><span class="key">Szűrők</span></div>
                <div class="shortcut"><span>J/K</span><span class="key">Navigálás</span></div>
                <div class="shortcut"><span>Enter</span><span class="key">Kiválasztás</span></div>
                <div class="shortcut"><span>Esc</span><span class="key">Mégse</span></div>
                <div class="shortcut"><span>F1</span><span class="key">Súgó</span></div>
            `;
            return panel;
        }}
        """
        
    def _render_keyboard_help(self):
        """Render keyboard shortcuts help."""
        if st.session_state.get('show_keyboard_help', False):
            with st.expander("⌨️ Billentyűparancsok", expanded=False):
                st.markdown("""
                ### Globális parancsok
                - **Alt + R**: Adatok frissítése
                - **Alt + S**: Keresés mező aktívvá tétele
                - **Alt + F**: Szűrők panel aktívvá tétele
                - **Alt + E**: Adatok exportálása
                - **Escape**: Művelet megszakítása
                
                ### Navigálás
                - **Alt + ←/→**: Előző/következő oldal
                - **J/K**: Következő/előző elem
                - **Enter**: Elem kiválasztása
                - **1/2/3**: Nézet váltása (lista/kártya/rács)
                
                ### Szűrők
                - **F1**: Szűrők panel megjelenítése/elrejtése
                - **F2**: Szűrők törlése
                - **F3**: Szűrők mentése
                - **F4**: Mentett szűrők betöltése
                
                ### Egyéb
                - **V**: Nézet váltása
                - **?**: Súgó megjelenítése
                """)

class AccessibilityManager:
    """
    Manages accessibility features including ARIA attributes, screen reader support, and color contrast.
    """
    
    def __init__(self):
        self.aria_live_regions = {}
        self.focus_management = {}
        
    def inject_accessibility_features(self):
        """Inject accessibility features into the page."""
        # Add ARIA live regions
        self._add_aria_live_regions()
        
        # Add skip navigation
        self._add_skip_navigation()
        
        # Add focus management
        self._add_focus_management()
        
        # Add high contrast mode
        self._add_high_contrast_mode()
        
    def _add_aria_live_regions(self):
        """Add ARIA live regions for dynamic content."""
        st.markdown("""
        <div id="aria-live-polite" aria-live="polite" aria-atomic="true" 
             style="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;"></div>
        <div id="aria-live-assertive" aria-live="assertive" aria-atomic="true"
             style="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;"></div>
        """, unsafe_allow_html=True)
        
    def _add_skip_navigation(self):
        """Add skip navigation links."""
        st.markdown("""
        <style>
        .skip-nav {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        }
        .skip-nav:focus {
            top: 6px;
        }
        </style>
        <a href="#main-content" class="skip-nav">Ugrás a fő tartalomhoz</a>
        <a href="#filter-panel" class="skip-nav">Ugrás a szűrőkhöz</a>
        <a href="#offers-list" class="skip-nav">Ugrás az ajánlatokhoz</a>
        """, unsafe_allow_html=True)
        
    def _add_focus_management(self):
        """Add focus management for modals and dynamic content."""
        st.markdown("""
        <script>
        // Focus management for dynamic content
        function manageFocus() {
            // Store previous focus
            let previousFocus = null;
            
            // Focus trap for modals
            function trapFocus(element) {
                const focusableElements = element.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                
                const firstFocusable = focusableElements[0];
                const lastFocusable = focusableElements[focusableElements.length - 1];
                
                element.addEventListener('keydown', function(e) {
                    if (e.key === 'Tab') {
                        if (e.shiftKey) {
                            if (document.activeElement === firstFocusable) {
                                lastFocusable.focus();
                                e.preventDefault();
                            }
                        } else {
                            if (document.activeElement === lastFocusable) {
                                firstFocusable.focus();
                                e.preventDefault();
                            }
                        }
                    }
                });
                
                firstFocusable.focus();
            }
            
            // Auto-announce dynamic content changes
            function announceChange(message, priority = 'polite') {
                const liveRegion = document.getElementById(`aria-live-${priority}`);
                if (liveRegion) {
                    liveRegion.textContent = message;
                    setTimeout(() => {
                        liveRegion.textContent = '';
                    }, 1000);
                }
            }
            
            // Export functions for use by components
            window.accessibilityManager = {
                trapFocus,
                announceChange,
                storeFocus: () => { previousFocus = document.activeElement; },
                restoreFocus: () => { if (previousFocus) previousFocus.focus(); }
            };
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', manageFocus);
        } else {
            manageFocus();
        }
        </script>
        """, unsafe_allow_html=True)
        
    def _add_high_contrast_mode(self):
        """Add high contrast mode toggle."""
        if st.session_state.get('high_contrast_mode', False):
            st.markdown("""
            <style>
            /* High contrast mode styles */
            .high-contrast * {
                background: black !important;
                color: white !important;
                border-color: white !important;
            }
            
            .high-contrast .modern-filter-panel {
                background: black !important;
                border: 2px solid white !important;
            }
            
            .high-contrast .offer-card {
                background: black !important;
                border: 2px solid white !important;
                color: white !important;
            }
            
            .high-contrast button {
                background: white !important;
                color: black !important;
                border: 2px solid white !important;
            }
            
            .high-contrast input, .high-contrast select {
                background: black !important;
                color: white !important;
                border: 2px solid white !important;
            }
            </style>
            """, unsafe_allow_html=True)
            
    def announce_change(self, message: str, priority: str = 'polite'):
        """Announce changes to screen readers."""
        st.markdown(f"""
        <script>
        if (window.accessibilityManager) {{
            window.accessibilityManager.announceChange('{message}', '{priority}');
        }}
        </script>
        """, unsafe_allow_html=True)
        
    def add_aria_attributes(self, element_id: str, attributes: Dict[str, str]):
        """Add ARIA attributes to an element."""
        aria_attrs = ' '.join([f'{key}="{value}"' for key, value in attributes.items()])
        
        st.markdown(f"""
        <script>
        document.addEventListener('DOMContentLoaded', function() {{
            const element = document.getElementById('{element_id}');
            if (element) {{
                {'; '.join([f'element.setAttribute("{key}", "{value}")' for key, value in attributes.items()])}
            }}
        }});
        </script>
        """, unsafe_allow_html=True)

class AccessibleComponent:
    """
    Base class for creating accessible components with built-in ARIA support.
    """
    
    def __init__(self, component_id: str, role: str = None):
        self.component_id = component_id
        self.role = role
        self.aria_attributes = {}
        
    def set_aria_label(self, label: str):
        """Set ARIA label for the component."""
        self.aria_attributes['aria-label'] = label
        return self
        
    def set_aria_describedby(self, description_id: str):
        """Set ARIA described by for the component."""
        self.aria_attributes['aria-describedby'] = description_id
        return self
        
    def set_aria_expanded(self, expanded: bool):
        """Set ARIA expanded state."""
        self.aria_attributes['aria-expanded'] = str(expanded).lower()
        return self
        
    def set_aria_live(self, live_type: str):
        """Set ARIA live region type."""
        self.aria_attributes['aria-live'] = live_type
        return self
        
    def render_with_accessibility(self, content: str):
        """Render component with accessibility attributes."""
        aria_attrs = ' '.join([f'{key}="{value}"' for key, value in self.aria_attributes.items()])
        role_attr = f'role="{self.role}"' if self.role else ''
        
        accessible_content = f"""
        <div id="{self.component_id}" {role_attr} {aria_attrs}>
            {content}
        </div>
        """
        
        st.markdown(accessible_content, unsafe_allow_html=True)

# Global instances
keyboard_manager = KeyboardNavigationManager()
accessibility_manager = AccessibilityManager()

# Utility functions
def make_accessible_button(label: str, key: str, on_click: Callable = None, 
                          aria_label: str = None, aria_describedby: str = None):
    """Create an accessible button with proper ARIA attributes."""
    button_aria_label = aria_label or label
    
    button = st.button(
        label,
        key=key,
        help=button_aria_label,
        on_click=on_click
    )
    
    # Add ARIA attributes via JavaScript
    if aria_label or aria_describedby:
        attrs = {}
        if aria_label:
            attrs['aria-label'] = aria_label
        if aria_describedby:
            attrs['aria-describedby'] = aria_describedby
            
        accessibility_manager.add_aria_attributes(f"button-{key}", attrs)
    
    return button

def make_accessible_input(label: str, key: str, input_type: str = 'text',
                         aria_label: str = None, aria_describedby: str = None):
    """Create an accessible input with proper ARIA attributes."""
    input_aria_label = aria_label or label
    
    if input_type == 'text':
        input_value = st.text_input(label, key=key, help=input_aria_label)
    elif input_type == 'select':
        options = st.session_state.get(f"{key}_options", [])
        input_value = st.selectbox(label, options, key=key, help=input_aria_label)
    else:
        input_value = st.text_input(label, key=key, help=input_aria_label)
    
    # Add ARIA attributes
    if aria_label or aria_describedby:
        attrs = {}
        if aria_label:
            attrs['aria-label'] = aria_label
        if aria_describedby:
            attrs['aria-describedby'] = aria_describedby
            
        accessibility_manager.add_aria_attributes(f"input-{key}", attrs)
    
    return input_value

def announce_filter_change(filter_name: str, new_value: str):
    """Announce filter changes to screen readers."""
    message = f"{filter_name} szűrő megváltozott: {new_value}"
    accessibility_manager.announce_change(message)

def announce_data_loaded(count: int, data_type: str = "ajánlat"):
    """Announce when data is loaded."""
    message = f"{count} {data_type} betöltve"
    accessibility_manager.announce_change(message)

# Export accessibility utilities
__all__ = [
    'KeyboardNavigationManager',
    'keyboard_manager',
    'AccessibilityManager', 
    'accessibility_manager',
    'AccessibleComponent',
    'make_accessible_button',
    'make_accessible_input',
    'announce_filter_change',
    'announce_data_loaded'
]