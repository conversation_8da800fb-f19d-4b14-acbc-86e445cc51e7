#!/usr/bin/env python3
"""
API Data Diagnostic Tool - Adatbázis szintű hibák diagnosztizálása
🔬 Comprehensive API data integrity and filtering diagnostics

This tool investigates "0 results" problems by:
1. Testing producer existence in database
2. Analyzing API parameter variations
3. Checking date range impacts
4. Identifying similar producers
5. Validating data integrity
"""

import streamlit as st
import logging
import sys
from datetime import datetime, timedelta, date
from collections import defaultdict

# Python version-independent typing imports
try:
    from typing import Dict, List, Any, Optional, Union, Tuple
except ImportError:
    from typing import Any, List, Optional, Union, Tuple
    Dict = dict

if 'Dict' not in globals():
    Dict = dict

logger = logging.getLogger(__name__)


class APIDataDiagnostic:
    """API adatintegritás diagnosztikai eszköz"""
    
    def __init__(self, api_client):
        self.api_client = api_client
        
    def diagnose_producer_data(self, producer_id: int, date_from: str, date_to: str) -> Dict[str, Any]:
        """Komprehenzív producer adat diagnosztika"""
        
        diagnosis = {
            'producer_id': producer_id,
            'date_range': f"{date_from} - {date_to}",
            'tests': {},
            'recommendations': [],
            'data_availability': {},
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"🔬 Starting comprehensive diagnosis for Producer ID {producer_id}")
        
        # TEST 1: Producer létezésének ellenőrzése
        st.info("🧪 Testing producer existence...")
        diagnosis['tests']['producer_exists'] = self._test_producer_exists(producer_id)
        
        # TEST 2: Minimális szűrési tesztek
        st.info("🧪 Testing minimal filters...")
        diagnosis['tests']['minimal_filters'] = self._test_minimal_filters(producer_id)
        
        # TEST 3: Időintervallum tesztelése
        st.info("🧪 Testing date ranges...")
        diagnosis['tests']['date_range_tests'] = self._test_date_ranges(producer_id, date_from, date_to)
        
        # TEST 4: API paraméter variációk
        st.info("🧪 Testing parameter variations...")
        diagnosis['tests']['parameter_variations'] = self._test_parameter_variations(producer_id)
        
        # TEST 5: Hasonló producer-ek keresése
        st.info("🧪 Finding similar producers...")
        diagnosis['tests']['similar_producers'] = self._find_similar_producers(producer_id)
        
        # Generate recommendations
        diagnosis['recommendations'] = self._generate_recommendations(diagnosis['tests'])
        
        logger.info(f"🔬 Diagnosis completed for Producer ID {producer_id}")
        return diagnosis
    
    def _test_producer_exists(self, producer_id: int) -> Dict[str, Any]:
        """Producer létezés teszt különböző megközelítésekkel"""
        
        test_result = {
            'name': 'Producer Existence Check',
            'success': False,
            'data': {},
            'error': None,
            'existence_confirmed': False,
            'methods_tested': []
        }
        
        try:
            # Method 1: Get all offers without filters to see all producers
            try:
                success, all_offers = self.api_client.get_offers({})
                if success and isinstance(all_offers, list):
                    test_result['data']['all_offers_count'] = len(all_offers)
                    
                    # Extract all producer IDs from offers
                    found_producers = set()
                    for offer in all_offers:
                        # Check various fields where producer ID might be
                        producer_fields = ['producer_id', 'user_id', 'created_by_user_id', 'owner_id']
                        for field in producer_fields:
                            if field in offer and offer[field]:
                                found_producers.add(offer[field])
                        
                        # Check nested user object
                        if 'user' in offer and isinstance(offer['user'], dict) and 'id' in offer['user']:
                            found_producers.add(offer['user']['id'])
                    
                    test_result['data']['found_producers'] = sorted(list(found_producers))
                    test_result['data']['producer_exists_in_offers'] = producer_id in found_producers
                    test_result['methods_tested'].append('all_offers_scan')
                    
                    if producer_id in found_producers:
                        test_result['existence_confirmed'] = True
                else:
                    test_result['data']['all_offers_error'] = f"Failed to get all offers: {all_offers}"
            except Exception as e:
                test_result['data']['all_offers_error'] = str(e)
            
            # Method 2: Direct producer_id filter test
            try:
                success, producer_offers = self.api_client.get_offers({'producer_id': producer_id})
                test_result['data']['direct_producer_filter'] = {
                    'success': success,
                    'count': len(producer_offers) if isinstance(producer_offers, list) else 0,
                    'has_results': len(producer_offers) > 0 if isinstance(producer_offers, list) else False
                }
                test_result['methods_tested'].append('direct_producer_filter')
                
                if isinstance(producer_offers, list) and len(producer_offers) > 0:
                    test_result['existence_confirmed'] = True
            except Exception as e:
                test_result['data']['direct_producer_filter_error'] = str(e)
            
            # Method 3: User-based filters
            user_filters = ['user_id', 'created_by_user_id', 'filter_user_id', 'owner_id']
            for filter_field in user_filters:
                try:
                    success, user_offers = self.api_client.get_offers({filter_field: producer_id})
                    test_result['data'][f'{filter_field}_filter'] = {
                        'success': success,
                        'count': len(user_offers) if isinstance(user_offers, list) else 0,
                        'has_results': len(user_offers) > 0 if isinstance(user_offers, list) else False
                    }
                    test_result['methods_tested'].append(f'{filter_field}_filter')
                    
                    if isinstance(user_offers, list) and len(user_offers) > 0:
                        test_result['existence_confirmed'] = True
                except Exception as e:
                    test_result['data'][f'{filter_field}_filter_error'] = str(e)
            
            test_result['success'] = len(test_result['methods_tested']) > 0
            
        except Exception as e:
            test_result['error'] = str(e)
            logger.error(f"Producer existence test failed: {e}")
            
        return test_result
    
    def _test_minimal_filters(self, producer_id: int) -> Dict[str, Any]:
        """Minimális szűrők tesztelése egyenként"""
        
        test_cases = [
            {"name": "No filters", "params": {}},
            {"name": "Only user_id (RECOMMENDED)", "params": {"user_id": producer_id}},
            {"name": "Only producer_id (DEPRECATED)", "params": {"producer_id": producer_id}},
            {"name": "Only created_by_user_id", "params": {"created_by_user_id": producer_id}},
            {"name": "Producer + User ID", "params": {"producer_id": producer_id, "user_id": producer_id}},
        ]
        
        results = {}
        
        for test_case in test_cases:
            test_name = test_case["name"]
            params = test_case["params"]
            
            try:
                success, data = self.api_client.get_offers(params)
                
                result = {
                    'params': params,
                    'success': success,
                    'count': len(data) if isinstance(data, list) else 0,
                    'has_results': len(data) > 0 if isinstance(data, list) else False,
                    'working': success and len(data) > 0 if isinstance(data, list) else False
                }
                
                # Analyze first few results for producer ID presence
                if isinstance(data, list) and data:
                    sample_analysis = self._analyze_offers_for_producer_id(data[:3], producer_id)
                    result['sample_analysis'] = sample_analysis
                
                results[test_name] = result
                
            except Exception as e:
                results[test_name] = {
                    'params': params,
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def _test_date_ranges(self, producer_id: int, date_from: str, date_to: str) -> Dict[str, Any]:
        """Különböző időintervallumok tesztelése a producer ID-val"""
        
        try:
            base_date = datetime.strptime(date_from, '%Y-%m-%d')
        except:
            base_date = datetime.now() - timedelta(days=30)
        
        # Különböző időintervallumok generálása
        test_ranges = [
            {
                'name': 'Original range',
                'from': date_from,
                'to': date_to
            },
            {
                'name': 'Last 3 months',
                'from': (base_date - timedelta(days=90)).strftime('%Y-%m-%d'),
                'to': datetime.now().strftime('%Y-%m-%d')
            },
            {
                'name': 'Last 6 months',
                'from': (base_date - timedelta(days=180)).strftime('%Y-%m-%d'),
                'to': datetime.now().strftime('%Y-%m-%d')
            },
            {
                'name': 'Last year',
                'from': (base_date - timedelta(days=365)).strftime('%Y-%m-%d'),
                'to': datetime.now().strftime('%Y-%m-%d')
            },
            {
                'name': 'No date filter',
                'from': None,
                'to': None
            }
        ]
        
        results = {}
        
        for test_range in test_ranges:
            test_name = test_range['name']
            
            # Base parameters with user_id (RECOMMENDED by backend)
            params = {'user_id': producer_id}
            
            # Add date filters if specified
            if test_range['from']:
                params['date_from'] = test_range['from']
            if test_range['to']:
                params['date_to'] = test_range['to']
            
            try:
                success, data = self.api_client.get_offers(params)
                
                results[test_name] = {
                    'params': params,
                    'success': success,
                    'count': len(data) if isinstance(data, list) else 0,
                    'date_range': f"{test_range['from']} - {test_range['to']}",
                    'working': success and len(data) > 0 if isinstance(data, list) else False
                }
                
                # If we have results, analyze dates
                if isinstance(data, list) and data:
                    date_analysis = self._analyze_offer_dates(data)
                    results[test_name]['date_analysis'] = date_analysis
                
            except Exception as e:
                results[test_name] = {
                    'params': params,
                    'success': False,
                    'error': str(e),
                    'date_range': f"{test_range['from']} - {test_range['to']}"
                }
        
        return results
    
    def _test_parameter_variations(self, producer_id: int) -> Dict[str, Any]:
        """API paraméter variációk szisztematikus tesztelése"""
        
        # Különböző paraméter kombinációk - JAVÍTÁS: user_id prioritás
        param_variations = [
            {
                'name': 'Only user_id (RECOMMENDED)', 
                'params': {'user_id': producer_id}
            },
            {
                'name': 'Only producer_id (DEPRECATED)', 
                'params': {'producer_id': producer_id}
            },
            {
                'name': 'Only created_by_user_id', 
                'params': {'created_by_user_id': producer_id}
            },
            {
                'name': 'Producer + User', 
                'params': {'producer_id': producer_id, 'user_id': producer_id}
            },
            {
                'name': 'All duplicate params (current problem)', 
                'params': {
                    'user_id': producer_id,
                    'producer_id': producer_id,
                    'created_by_user_id': producer_id,
                    'filter_user_id': producer_id,
                    'owner_id': producer_id
                }
            },
            {
                'name': 'Minimal (no user params)', 
                'params': {'producer_id': producer_id}
            },
            {
                'name': 'User fields only', 
                'params': {
                    'user_id': producer_id,
                    'created_by_user_id': producer_id
                }
            }
        ]
        
        results = {}
        working_combinations = []
        
        for variation in param_variations:
            test_name = variation['name']
            params = variation['params']
            
            try:
                success, data = self.api_client.get_offers(params)
                
                result = {
                    'params': params,
                    'param_count': len(params),
                    'success': success,
                    'count': len(data) if isinstance(data, list) else 0,
                    'working': success and len(data) > 0 if isinstance(data, list) else False
                }
                
                # Detailed analysis if we have results
                if isinstance(data, list) and data:
                    working_combinations.append(test_name)
                    
                    # Analyze producer IDs in results
                    found_producer_ids = set()
                    contains_our_producer = False
                    
                    for offer in data[:5]:  # First 5 offers
                        producer_fields = ['producer_id', 'user_id', 'created_by_user_id', 'owner_id']
                        for field in producer_fields:
                            if field in offer and offer[field]:
                                found_producer_ids.add(offer[field])
                                if offer[field] == producer_id:
                                    contains_our_producer = True
                        
                        # Check nested user object
                        if 'user' in offer and isinstance(offer['user'], dict):
                            if 'id' in offer['user']:
                                found_producer_ids.add(offer['user']['id'])
                                if offer['user']['id'] == producer_id:
                                    contains_our_producer = True
                    
                    result['found_producer_ids'] = sorted(list(found_producer_ids))
                    result['contains_our_producer'] = contains_our_producer
                    result['sample_offer'] = data[0]  # First offer for inspection
                
                results[test_name] = result
                
            except Exception as e:
                results[test_name] = {
                    'params': params,
                    'success': False,
                    'error': str(e)
                }
        
        # Summary analysis
        results['_summary'] = {
            'total_variations': len(param_variations),
            'working_combinations': working_combinations,
            'working_count': len(working_combinations),
            'success_rate': len(working_combinations) / len(param_variations) * 100
        }
        
        return results
    
    def _find_similar_producers(self, producer_id: int) -> Dict[str, Any]:
        """Hasonló producer-ek keresése és elemzése"""
        
        try:
            # Get all offers to analyze producer distribution
            success, all_offers = self.api_client.get_offers({'limit': 200})  # Increased limit
            
            if not success or not isinstance(all_offers, list):
                return {'error': f'Could not fetch offers for comparison: {all_offers}'}
            
            # Producer statistics collection
            producer_stats = defaultdict(lambda: {
                'count': 0, 
                'fields': set(), 
                'sample_offers': [],
                'date_range': {'earliest': None, 'latest': None}
            })
            
            for offer in all_offers:
                # Extract producer IDs from various fields
                producer_ids_found = []
                
                # Direct fields
                producer_fields = ['producer_id', 'user_id', 'created_by_user_id', 'owner_id']
                for field in producer_fields:
                    if field in offer and offer[field]:
                        producer_ids_found.append((field, offer[field]))
                
                # Nested user object
                if 'user' in offer and isinstance(offer['user'], dict):
                    if 'id' in offer['user']:
                        producer_ids_found.append(('user.id', offer['user']['id']))
                
                # Update statistics for each found producer ID
                for field, pid in producer_ids_found:
                    producer_stats[pid]['count'] += 1
                    producer_stats[pid]['fields'].add(field)
                    
                    # Keep sample offers (max 3 per producer)
                    if len(producer_stats[pid]['sample_offers']) < 3:
                        producer_stats[pid]['sample_offers'].append(offer)
                    
                    # Track date range
                    offer_date = self._extract_offer_date(offer)
                    if offer_date:
                        if (producer_stats[pid]['date_range']['earliest'] is None or 
                            offer_date < producer_stats[pid]['date_range']['earliest']):
                            producer_stats[pid]['date_range']['earliest'] = offer_date
                        
                        if (producer_stats[pid]['date_range']['latest'] is None or 
                            offer_date > producer_stats[pid]['date_range']['latest']):
                            producer_stats[pid]['date_range']['latest'] = offer_date
            
            # Convert defaultdict to regular dict for JSON serialization
            producer_stats = dict(producer_stats)
            
            # Sort producers by offer count
            sorted_producers = sorted(
                producer_stats.items(), 
                key=lambda x: x[1]['count'], 
                reverse=True
            )
            
            return {
                'total_offers_analyzed': len(all_offers),
                'total_unique_producers': len(producer_stats),
                'our_producer_found': producer_id in producer_stats,
                'our_producer_stats': producer_stats.get(producer_id, 'Not found'),
                'top_producers': [
                    {
                        'producer_id': pid, 
                        'count': stats['count'], 
                        'fields': list(stats['fields']),
                        'date_range': {
                            'earliest': stats['date_range']['earliest'].isoformat() if stats['date_range']['earliest'] else None,
                            'latest': stats['date_range']['latest'].isoformat() if stats['date_range']['latest'] else None
                        }
                    } 
                    for pid, stats in sorted_producers[:10]
                ],
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Similar producers analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_offers_for_producer_id(self, offers: List[Dict], target_producer_id: int) -> Dict[str, Any]:
        """Analyze offers to see how producer ID appears in different fields"""
        
        analysis = {
            'total_offers_analyzed': len(offers),
            'producer_id_occurrences': {},
            'target_producer_found': False,
            'field_analysis': {}
        }
        
        producer_fields = ['producer_id', 'user_id', 'created_by_user_id', 'owner_id']
        
        for offer in offers:
            for field in producer_fields:
                if field in offer and offer[field]:
                    pid = offer[field]
                    if field not in analysis['producer_id_occurrences']:
                        analysis['producer_id_occurrences'][field] = []
                    analysis['producer_id_occurrences'][field].append(pid)
                    
                    if pid == target_producer_id:
                        analysis['target_producer_found'] = True
            
            # Check nested user object
            if 'user' in offer and isinstance(offer['user'], dict):
                if 'id' in offer['user']:
                    pid = offer['user']['id']
                    if 'user.id' not in analysis['producer_id_occurrences']:
                        analysis['producer_id_occurrences']['user.id'] = []
                    analysis['producer_id_occurrences']['user.id'].append(pid)
                    
                    if pid == target_producer_id:
                        analysis['target_producer_found'] = True
        
        return analysis
    
    def _analyze_offer_dates(self, offers: List[Dict]) -> Dict[str, Any]:
        """Analyze date distribution in offers"""
        
        dates = []
        for offer in offers:
            offer_date = self._extract_offer_date(offer)
            if offer_date:
                dates.append(offer_date)
        
        if not dates:
            return {'error': 'No valid dates found in offers'}
        
        dates.sort()
        
        return {
            'total_offers_with_dates': len(dates),
            'earliest_date': dates[0].isoformat(),
            'latest_date': dates[-1].isoformat(),
            'date_span_days': (dates[-1] - dates[0]).days if len(dates) > 1 else 0
        }
    
    def _extract_offer_date(self, offer: Dict) -> Optional[datetime]:
        """Extract date from offer object"""
        
        # Try different date fields
        date_fields = ['created_at', 'updated_at', 'date_created', 'offer_date', 'delivery_date']
        
        for field in date_fields:
            if field in offer and offer[field]:
                try:
                    if isinstance(offer[field], str):
                        # Try to parse ISO format
                        return datetime.fromisoformat(offer[field].replace('Z', '+00:00'))
                    elif hasattr(offer[field], 'date'):
                        return offer[field]
                except:
                    continue
        
        return None
    
    def _generate_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on test results"""
        
        recommendations = []
        
        # Check producer existence
        if 'producer_exists' in test_results:
            existence_test = test_results['producer_exists']
            if not existence_test.get('existence_confirmed', False):
                recommendations.append(
                    "🚨 CRITICAL: Producer ID not found in database. Check if producer exists or use a different ID."
                )
            else:
                recommendations.append(
                    "✅ Producer ID confirmed to exist in database."
                )
        
        # Check parameter variations
        if 'parameter_variations' in test_results:
            param_test = test_results['parameter_variations']
            summary = param_test.get('_summary', {})
            working_combinations = summary.get('working_combinations', [])
            
            if working_combinations:
                recommendations.append(
                    f"✅ Found {len(working_combinations)} working parameter combinations: {', '.join(working_combinations)}"
                )
                recommendations.append(
                    "💡 TIP: Use simpler parameter combinations instead of duplicating all user-related fields."
                )
            else:
                recommendations.append(
                    "🚨 CRITICAL: No parameter combinations returned results. Data may not exist for this producer."
                )
        
        # Check date ranges
        if 'date_range_tests' in test_results:
            date_tests = test_results['date_range_tests']
            working_ranges = [name for name, test in date_tests.items() 
                            if test.get('working', False)]
            
            if working_ranges:
                recommendations.append(
                    f"📅 Data found in these date ranges: {', '.join(working_ranges)}"
                )
                if 'Original range' not in working_ranges:
                    recommendations.append(
                        "⚠️ Original date range has no data. Consider expanding the date range."
                    )
            else:
                recommendations.append(
                    "📅 No data found in any tested date ranges. Producer may have no offers in the system."
                )
        
        # Check minimal filters
        if 'minimal_filters' in test_results:
            minimal_tests = test_results['minimal_filters']
            working_minimal = [name for name, test in minimal_tests.items() 
                             if test.get('working', False)]
            
            if working_minimal:
                recommendations.append(
                    f"🔍 These minimal filters work: {', '.join(working_minimal)}"
                )
            else:
                recommendations.append(
                    "🔍 No minimal filters return results. This suggests a data availability issue."
                )
        
        if not recommendations:
            recommendations.append("📊 Run diagnostics to get specific recommendations.")
        
        return recommendations


def render_api_data_diagnostic_ui(producer_id: int, date_from: str, date_to: str):
    """API Data Diagnostic UI megjelenítése"""
    
    st.subheader("🔬 **API ADAT DIAGNOSZTIKA**")
    st.write(f"**Cél**: Producer ID {producer_id} adatainak elemzése ({date_from} - {date_to})")
    st.info("ℹ️ **Backend API:** A szűréshez `user_id` paramétert használ (nem `producer_id`-t)")
    
    # Initialize diagnostic tool
    if 'api_diagnostic_tool' not in st.session_state:
        try:
            # Import API client
            from .api_client import get_offers
            
            # Create API client wrapper
            class APIClientWrapper:
                def get_offers(self, params):
                    return get_offers(params)
            
            st.session_state.api_diagnostic_tool = APIDataDiagnostic(APIClientWrapper())
            st.success("✅ API Diagnostic Tool initialized")
        except Exception as e:
            st.error(f"❌ Could not initialize API diagnostic tool: {e}")
            return
    
    diagnostic_tool = st.session_state.api_diagnostic_tool
    
    # Control buttons
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔬 **TELJES DIAGNOSZTIKA**", type="primary", use_container_width=True):
            st.session_state.run_full_api_diagnosis = True
    
    with col2:
        if st.button("📊 Paraméter Teszt", use_container_width=True):
            st.session_state.run_quick_param_test = True
    
    with col3:
        if st.button("🔍 Producer Keresés", use_container_width=True):
            st.session_state.run_similar_producers = True
    
    with col4:
        if st.button("📅 Dátum Teszt", use_container_width=True):
            st.session_state.run_date_test = True
    
    # Execute diagnostics based on button clicks
    if st.session_state.get('run_full_api_diagnosis', False):
        run_full_api_diagnosis(diagnostic_tool, producer_id, date_from, date_to)
        st.session_state.run_full_api_diagnosis = False
    
    if st.session_state.get('run_quick_param_test', False):
        run_quick_param_test(diagnostic_tool, producer_id)
        st.session_state.run_quick_param_test = False
    
    if st.session_state.get('run_similar_producers', False):
        run_similar_producers_analysis(diagnostic_tool, producer_id)
        st.session_state.run_similar_producers = False
    
    if st.session_state.get('run_date_test', False):
        run_date_range_test(diagnostic_tool, producer_id, date_from, date_to)
        st.session_state.run_date_test = False


def run_full_api_diagnosis(diagnostic_tool, producer_id, date_from, date_to):
    """Teljes API diagnosztika futtatása"""
    
    st.divider()
    st.subheader("🔬 **TELJES DIAGNOSZTIKA FUTTATÁSA**")
    
    with st.spinner("🔬 Comprehensive API diagnosis in progress..."):
        diagnosis = diagnostic_tool.diagnose_producer_data(producer_id, date_from, date_to)
    
    st.success("✅ Diagnosis completed!")
    
    # Display recommendations first
    if diagnosis.get('recommendations'):
        st.subheader("💡 **AJÁNLÁSOK**")
        for rec in diagnosis['recommendations']:
            if rec.startswith("🚨"):
                st.error(rec)
            elif rec.startswith("⚠️"):
                st.warning(rec)
            elif rec.startswith("✅"):
                st.success(rec)
            else:
                st.info(rec)
    
    # Display test results
    st.subheader("🧪 **RÉSZLETES TESZT EREDMÉNYEK**")
    
    for test_name, test_result in diagnosis['tests'].items():
        display_api_test_result(test_name, test_result)


def run_quick_param_test(diagnostic_tool, producer_id):
    """Gyors paraméter teszt"""
    
    st.divider()
    st.subheader("📊 **PARAMÉTER VARIÁCIÓK TESZTELÉSE**")
    
    with st.spinner("📊 Testing parameter variations..."):
        test_result = diagnostic_tool._test_parameter_variations(producer_id)
    
    # Summary metrics
    summary = test_result.get('_summary', {})
    working_combinations = summary.get('working_combinations', [])
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Összes Teszt", summary.get('total_variations', 0))
    with col2:
        st.metric("Működő Tesztek", len(working_combinations))
    with col3:
        st.metric("Sikeres Arány", f"{summary.get('success_rate', 0):.0f}%")
    
    if working_combinations:
        st.success(f"✅ Működő kombinációk: {', '.join(working_combinations)}")
    else:
        st.error("❌ Egyetlen paraméter kombináció sem ad eredményt!")
    
    # Detailed results
    for test_name, result in test_result.items():
        if test_name.startswith('_'):
            continue
            
        working = result.get('working', False)
        count = result.get('count', 0)
        success_icon = "✅" if working else "❌" if result.get('success', False) else "🚨"
        
        with st.expander(f"{success_icon} {test_name}: {count} results"):
            st.json(result)


def run_similar_producers_analysis(diagnostic_tool, producer_id):
    """Hasonló producer-ek elemzése"""
    
    st.divider()
    st.subheader("🔍 **PRODUCER-EK ELEMZÉSE**")
    
    with st.spinner("🔍 Analyzing producers in database..."):
        result = diagnostic_tool._find_similar_producers(producer_id)
    
    if 'error' in result:
        st.error(f"❌ Hiba: {result['error']}")
        return
    
    # Summary metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Összes Producer", result.get('total_unique_producers', 0))
    with col2:
        found = result.get('our_producer_found', False)
        st.metric("Producer ID Found", "✅ Yes" if found else "❌ No")
    with col3:
        our_stats = result.get('our_producer_stats', {})
        count = our_stats.get('count', 0) if isinstance(our_stats, dict) else 0
        st.metric(f"Producer {producer_id} Offers", count)
    
    # Producer status
    if result.get('our_producer_found', False):
        st.success(f"🎯 **Producer ID {producer_id} FOUND in database!**")
        our_stats = result['our_producer_stats']
        st.write(f"- **Offer count**: {our_stats['count']}")
        st.write(f"- **Found in fields**: {', '.join(our_stats['fields'])}")
        
        if st.button("📋 Show sample offer"):
            if our_stats['sample_offers']:
                st.json(our_stats['sample_offers'][0])
    else:
        st.error(f"❌ **Producer ID {producer_id} NOT FOUND in database!**")
        st.error("This explains why filtering returns 0 results.")
    
    # Top producers
    if 'top_producers' in result:
        st.write("**🏆 Top Producers by offer count:**")
        for producer in result['top_producers'][:5]:
            pid = producer['producer_id']
            count = producer['count']
            fields = ', '.join(producer['fields'])
            highlight = "🎯" if pid == producer_id else "📊"
            st.write(f"{highlight} **Producer ID {pid}**: {count} offers (fields: {fields})")


def run_date_range_test(diagnostic_tool, producer_id, date_from, date_to):
    """Dátum tartomány teszt"""
    
    st.divider()
    st.subheader("📅 **DÁTUM TARTOMÁNY TESZT**")
    
    with st.spinner("📅 Testing different date ranges..."):
        test_result = diagnostic_tool._test_date_ranges(producer_id, date_from, date_to)
    
    # Find working ranges
    working_ranges = [name for name, result in test_result.items() 
                     if result.get('working', False)]
    
    if working_ranges:
        st.success(f"✅ Data found in: {', '.join(working_ranges)}")
    else:
        st.error("❌ No data found in any date range tested!")
    
    # Detailed results
    for range_name, result in test_result.items():
        working = result.get('working', False)
        count = result.get('count', 0)
        date_range = result.get('date_range', 'N/A')
        success_icon = "✅" if working else "❌"
        
        with st.expander(f"{success_icon} {range_name}: {count} offers ({date_range})"):
            st.json(result)


def display_api_test_result(test_name: str, test_result: Dict[str, Any]):
    """API teszt eredmény megjelenítése"""
    
    with st.expander(f"🧪 {test_name.replace('_', ' ').title()}"):
        
        if isinstance(test_result, dict) and 'success' in test_result:
            # Single test result
            if test_result.get('success', False):
                st.success("✅ Test successful")
            else:
                st.error("❌ Test failed")
                if 'error' in test_result:
                    st.error(f"Error: {test_result['error']}")
            
            # Show existence confirmation if available
            if 'existence_confirmed' in test_result:
                if test_result['existence_confirmed']:
                    st.success("🎯 Producer existence CONFIRMED!")
                else:
                    st.error("❌ Producer existence NOT confirmed")
            
            if 'data' in test_result:
                st.write("**Test Data:**")
                st.json(test_result['data'])
        
        else:
            # Multiple test results
            for sub_test_name, sub_result in test_result.items():
                if sub_test_name.startswith('_'):
                    continue
                    
                st.write(f"**{sub_test_name}:**")
                
                if isinstance(sub_result, dict):
                    if sub_result.get('success', False):
                        count = sub_result.get('count', 0)
                        working = sub_result.get('working', False)
                        
                        if working:
                            st.success(f"✅ Success: {count} results")
                        else:
                            st.warning(f"⚠️ Success but no results: {count}")
                        
                        if 'params' in sub_result:
                            params = sub_result['params']
                            # Kiemelés ha user_id paramétert használ (ajánlott)
                            if 'user_id' in params:
                                st.success(f"✅ Parameters (RECOMMENDED): {params}")
                            elif 'producer_id' in params:
                                st.warning(f"⚠️ Parameters (DEPRECATED): {params}")
                            else:
                                st.write(f"Parameters: {params}")
                        
                        if 'found_producer_ids' in sub_result:
                            st.write(f"Found Producer IDs: {sub_result['found_producer_ids']}")
                        
                        if 'contains_our_producer' in sub_result:
                            if sub_result['contains_our_producer']:
                                st.success("🎯 Contains our Producer ID!")
                            else:
                                st.warning("❌ Does not contain our Producer ID")
                    
                    else:
                        st.error(f"❌ Failed")
                        if 'error' in sub_result:
                            st.error(f"Error: {sub_result['error']}")
                
                st.write("---")