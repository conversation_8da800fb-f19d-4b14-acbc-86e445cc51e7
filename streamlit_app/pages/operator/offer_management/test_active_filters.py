"""
Tesztmodul a fejlett aktív szűrő megjelenítésének demonstrálására.
Ez a fájl közvetlenül futtatható Streamlit-tel a komponens teszteléséhez.
"""
import streamlit as st
from datetime import datetime, timedelta
import logging

try:
    from streamlit_app.pages.operator.offer_management.active_filter_display import (
        render_active_filters_panel,
        render_active_filter_badges,
        render_filter_summary,
        inject_active_filter_styles
    )
    from streamlit_app.pages.operator.offer_management.modern_search_panel import (
        render_modern_search_panel_complete
    )
except ImportError:
    try:
        from pages.operator.offer_management.active_filter_display import (
            render_active_filters_panel,
            render_active_filter_badges,
            render_filter_summary,
            inject_active_filter_styles
        )
        from pages.operator.offer_management.modern_search_panel import (
            render_modern_search_panel_complete
        )
    except ImportError:
        try:
            from active_filter_display import (
                render_active_filters_panel,
                render_active_filter_badges,
                render_filter_summary,
                inject_active_filter_styles
            )
            from modern_search_panel import (
                render_modern_search_panel_complete
            )
        except ImportError:
            st.error("Nem sikerült importálni a szükséges modulokat!")
            st.stop()

# Konfiguráljuk a naplózást
logging.basicConfig(level=logging.INFO)

# Oldal konfigurálása
st.set_page_config(
    page_title="Fejlett Aktív Szűrő Megjelenítés",
    page_icon="🏷️",
    layout="wide"
)

# Demó fejléc
st.title("Fejlett Aktív Szűrő Megjelenítés - Demó")
st.markdown("""
Ez a demó oldal a fejlett aktív szűrő megjelenítést mutatja be, amely a TASK-1.3 feladat részeként készült.

A komponens a következő jellemzőket tartalmazza:
- ✅ Stílusos címke (badge) megjelenítés az aktív szűrőkhöz
- ✅ Kategóriánként eltérő vizuális jelölés a címkéken
- ✅ Egyérintéses törlési funkció a címkékhez
- ✅ "Összes törlése" opció
- ✅ Összesítő számláló a szűrőpanel fejlécében
- ✅ Animációk a felhasználói élmény javításához

Az alábbi demó két részből áll:
1. Önálló aktív szűrő megjelenítő komponens - csak láthatóvá teszi a már beállított szűrőket
2. Integrált megjelenítés a modern keresőpanellel - a teljes munkafolyamat bemutatása
""")

# Szükséges stílusok betöltése
inject_active_filter_styles()

# Demo szűrők létrehozása
if "demo_filters" not in st.session_state:
    st.session_state.demo_filters = {
        "status": "CONFIRMED_BY_COMPANY",
        "producer_id": 12345,
        "producer_name": "Demo Producer Ltd.",
        "from_date": datetime.now().date() - timedelta(days=7),
        "to_date": datetime.now().date(),
        "product_type_id": 42,
        "product_name": "Alma",
        "min_quantity": 100,
        "max_quantity": 500
    }

# Az integrált demo rész szűrői
if "integrated_filters" not in st.session_state:
    st.session_state.integrated_filters = {
        "status": None,
        "producer_id": None,
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_type_id": None,
        "min_quantity": None,
        "max_quantity": None
    }

# Szűrő törlés kezelése
def handle_remove_filter(key):
    if key in st.session_state.demo_filters:
        # A szűrő "törlése" a demóhoz (valójában csak None-ra állítjuk)
        st.session_state.demo_filters[key] = None
        st.success(f"{key} szűrő törölve!")

# Összes szűrő törlése
def handle_clear_all_filters():
    for key in st.session_state.demo_filters:
        st.session_state.demo_filters[key] = None
    st.success("Minden szűrő törölve!")

# Integrált demo szűrő törlés kezelése
def handle_integrated_remove_filter(key):
    if key in st.session_state.integrated_filters:
        st.session_state.integrated_filters[key] = None
        # Dátum szűrők esetén visszaállítjuk az alapértelmezett értékeket
        if key == "from_date":
            st.session_state.integrated_filters["from_date"] = datetime.now().date() - timedelta(days=30)
        elif key == "to_date":
            st.session_state.integrated_filters["to_date"] = datetime.now().date()

# Integrált demo összes szűrő törlése
def handle_integrated_clear_all_filters():
    # Alapértelmezésre visszaállítás
    st.session_state.integrated_filters = {
        "status": None,
        "producer_id": None,
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_type_id": None,
        "min_quantity": None,
        "max_quantity": None
    }

# Keresés kezelése az integrált demóban
def handle_integrated_search(params):
    st.session_state.integrated_filters.update(params)
    st.success("Keresési paraméterek frissítve!")

# Két fő demo rész 
tab1, tab2 = st.tabs(["1. Önálló Komponens", "2. Integrált Megjelenítés"])

with tab1:
    st.header("Önálló Aktív Szűrő Megjelenítés")
    
    # Szűrők konfigurálása a demóhoz
    with st.expander("Szűrők beállítása a demóhoz"):
        col1, col2 = st.columns(2)
        
        with col1:
            # Státusz
            status_options = [None, "CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            st.session_state.demo_filters["status"] = st.selectbox(
                "Státusz:",
                options=status_options,
                format_func=lambda x: "Nincs szűrés" if x is None else x,
                index=status_options.index(st.session_state.demo_filters["status"]) if st.session_state.demo_filters["status"] in status_options else 0
            )
            
            # Termelő adatok
            producer_id = st.number_input(
                "Termelő ID:",
                min_value=0,
                value=st.session_state.demo_filters["producer_id"] or 0
            )
            st.session_state.demo_filters["producer_id"] = producer_id if producer_id > 0 else None
            
            producer_name = st.text_input(
                "Termelő neve:",
                value=st.session_state.demo_filters["producer_name"] or ""
            )
            st.session_state.demo_filters["producer_name"] = producer_name if producer_name else None
            
            # Termék adatok
            product_type_id = st.number_input(
                "Terméktípus ID:",
                min_value=0,
                value=st.session_state.demo_filters["product_type_id"] or 0
            )
            st.session_state.demo_filters["product_type_id"] = product_type_id if product_type_id > 0 else None
            
            product_name = st.text_input(
                "Termék neve:",
                value=st.session_state.demo_filters["product_name"] or ""
            )
            st.session_state.demo_filters["product_name"] = product_name if product_name else None
        
        with col2:
            # Dátumok
            from_date = st.date_input(
                "Kezdő dátum:",
                value=st.session_state.demo_filters["from_date"]
            )
            st.session_state.demo_filters["from_date"] = from_date
            
            to_date = st.date_input(
                "Végső dátum:",
                value=st.session_state.demo_filters["to_date"] or datetime.now().date()
            )
            st.session_state.demo_filters["to_date"] = to_date
            
            # Mennyiségek
            min_quantity = st.number_input(
                "Min. mennyiség (kg):",
                min_value=0,
                value=st.session_state.demo_filters["min_quantity"] or 0
            )
            st.session_state.demo_filters["min_quantity"] = min_quantity if min_quantity > 0 else None
            
            max_quantity = st.number_input(
                "Max. mennyiség (kg):",
                min_value=0,
                value=st.session_state.demo_filters["max_quantity"] or 0
            )
            st.session_state.demo_filters["max_quantity"] = max_quantity if max_quantity > 0 else None
    
    # Különböző megjelenítési módok bemutatása
    st.subheader("1. Teljes Aktív Szűrők Panel")
    filter_count = render_active_filters_panel(
        filters=st.session_state.demo_filters,
        prefix="standalone",
        title="Aktív szűrők",
        icon="🔍",
        on_remove=handle_remove_filter,
        on_clear_all=handle_clear_all_filters,
        categorize=True
    )
    
    st.markdown(f"""
    *Ez a panel **{filter_count} aktív szűrőt** jelenít meg. Főbb tulajdonságok:*
    - Számlálós fejléc az aktív szűrők számával
    - "Összes törlése" gomb a teljes törléshez
    - Színkódolt címkék a különböző típusokhoz
    - X gomb minden címkén az egyenkénti törléshez
    - Animált megjelenés/eltűnés
    """)
    
    st.subheader("2. Csak Címkék (Panel Nélkül)")
    badge_count = render_active_filter_badges(
        filters=st.session_state.demo_filters,
        prefix="standalone_badges",
        on_remove=handle_remove_filter,
        categorize=True
    )
    
    st.markdown(f"""
    *Ez a verzió csak a címkéket jeleníti meg, panel nélkül.*
    Hasznos amikor:
    - Korlátozott a hely
    - A címkéket más komponensekbe kell integrálni
    - Szükségtelen a fejléc és a "Törlés mind" funkció
    """)
    
    st.subheader("3. Szöveges Összefoglaló")
    render_filter_summary(
        filters=st.session_state.demo_filters,
        prefix="standalone_summary",
        show_empty=True
    )
    
    st.markdown("""
    *Ez a legegyszerűbb megjelenítési forma.*
    Ideális amikor:
    - Nagyon korlátozott a hely
    - E-mailben vagy riportban kell megjeleníteni a szűrési feltételeket
    - Nincs szükség interaktivitásra
    """)

with tab2:
    st.header("Integrált Megjelenítés Modern Keresőpanellel")
    
    st.markdown("""
    Ez a demo bemutatja, hogyan működik együtt a Modern Keresőpanel (TASK-1.1) és az Aktív Szűrő Megjelenítés (TASK-1.3).
    1. Használja a keresőpanelt a szűrők beállításához
    2. A keresőgombra kattintva a szűrők megjelennek az Aktív Szűrők panelen
    3. A szűrőket egyenként vagy együtt törölheti
    """)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Modern keresőpanel
        st.subheader("Keresőpanel")
        search_params = render_modern_search_panel_complete(
            title="Ajánlatok keresése",
            icon="🔍",
            collapsible=True,
            expanded=True,
            on_search=handle_integrated_search,
            panel_id="integrated_search"
        )
    
    with col2:
        # Aktív szűrők panel az eredményekkel
        st.subheader("Kiválasztott szűrők")
        filter_count = render_active_filters_panel(
            filters=st.session_state.integrated_filters,
            prefix="integrated",
            title="Aktív szűrők",
            icon="🏷️",
            on_remove=handle_integrated_remove_filter,
            on_clear_all=handle_integrated_clear_all_filters,
            categorize=True
        )
        
        # Találatok száma - demo
        active_count = sum(1 for v in st.session_state.integrated_filters.values() if v is not None and v != "")
        if active_count > 0:
            st.metric("Találatok száma", f"{active_count * 12} ajánlat")
        else:
            st.info("Állítson be szűrőket a keresőpanelen")

# Fejlesztői információk
with st.expander("Fejlesztői információk"):
    st.markdown("""
    **Modul:** active_filter_display.py
    
    **Fő funkciók:**
    - `render_active_filters_panel`: Teljes panel megjelenítése
    - `render_active_filter_badges`: Csak címkék megjelenítése
    - `render_filter_summary`: Szöveges összefoglaló
    
    **Segédfunkciók:**
    - `format_filter_value`: Szűrő értékek formázása olvasható formátumra
    - `format_filter_key`: Szűrő kulcsok formázása olvasható formátumra
    - `get_category_for_key`: Szűrő kategóriák meghatározása
    
    **Használt technológiák:**
    - Custom CSS keretrendszer
    - HTML/CSS/JavaScript az interaktivitáshoz
    - Streamlit session state a szűrők állapotának követéséhez
    - Animációk az UX javításához
    
    **Kapcsolódó fájlok:**
    - active_filter_display.py: a fő komponens
    - modern_search_panel.py: a kereső komponens, amivel integrálva működik
    - custom_css_framework.py: az alap CSS keretrendszer
    """)