"""
Ajánlat adatok validációs funkcióit tartalmazza.
Robusztus ellenőrzések és felhasználóbarát hibaüzenetek biztosítása.
"""
import logging
import re
import streamlit as st
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

def validate_offer_data(offer_data, is_new=True):
    """
    Ajánlat adatok kliens oldali validálása beküldés előtt.
    
    Ez a függvény ellenőrzi az ajánlat adatainak érvényességét mielőtt
    elküldenénk azokat az API-nak. Visszaadja a hibákat és figyelmeztetéseket.
    
    Args:
        offer_data (dict): Az ellenőrizendő ajánlat adatok.
        is_new (bool, optional): Új ajánlat létrehozása vagy meglévő módosítása. Defaults to True.
    
    Returns:
        tuple: (<PERSON><PERSON><PERSON><PERSON><PERSON>, hib<PERSON>, figyelmeztetések) ahol:
            - érvényes (bool): True ha az adatok érvényesek, False egyébként
            - hibák (dict): Mező név és hibaüzenetek szótára
            - figyelmeztetések (dict): Mező név és figyelmeztető üzenetek szótára
    """
    errors = {}
    warnings = {}
    
    # Kötelező mezők ellenőrzése
    required_fields = {
        "product_type_id": "Termék típus",
        "quantity_in_kg": "Mennyiség",
        "delivery_date": "Beszállítási dátum",
    }
    
    if is_new:
        required_fields["user_id"] = "Termelő"
    
    # Kötelező mezők meglétének ellenőrzése
    for field, field_name in required_fields.items():
        if field not in offer_data or not offer_data[field]:
            errors[field] = f"A(z) {field_name} mező kitöltése kötelező."
    
    # Mennyiség validálása
    if "quantity_in_kg" in offer_data and offer_data.get("quantity_in_kg"):
        try:
            # Biztonságos konverzió
            if isinstance(offer_data["quantity_in_kg"], str):
                # Vessző helyettesítése ponttal
                qty_str = offer_data["quantity_in_kg"].replace(',', '.')
                qty = float(qty_str)
            else:
                qty = float(offer_data["quantity_in_kg"])
                
            if qty <= 0:
                errors["quantity_in_kg"] = "A mennyiségnek pozitív számnak kell lennie."
            elif qty < 1:
                warnings["quantity_in_kg"] = "1 kg-nál kevesebb mennyiség nem tipikus. Biztos ebben?"
            elif qty > 10000:
                warnings["quantity_in_kg"] = "Szokatlanul nagy mennyiség. Biztos helyesen adta meg?"
        except ValueError:
            errors["quantity_in_kg"] = "A mennyiségnek számnak kell lennie."
    
    # Ár validálása (ha meg lett adva)
    if "price" in offer_data and offer_data.get("price"):
        try:
            # Biztonságos konverzió
            if isinstance(offer_data["price"], str):
                # Vessző helyettesítése ponttal
                price_str = offer_data["price"].replace(',', '.')
                price = float(price_str)
            else:
                price = float(offer_data["price"])
                
            if price < 0:
                errors["price"] = "Az ár nem lehet negatív."
            elif price == 0:
                warnings["price"] = "Ingyenes ajánlatot ad meg. Biztos ebben?"
            elif price > 100000:
                warnings["price"] = "Szokatlanul magas ár. Biztos helyesen adta meg?"
        except ValueError:
            errors["price"] = "Az árnak számnak kell lennie."
    
    # Dátum validálása
    if "delivery_date" in offer_data and offer_data.get("delivery_date"):
        try:
            if isinstance(offer_data["delivery_date"], str):
                try:
                    delivery_date = datetime.strptime(offer_data["delivery_date"], "%Y-%m-%d").date()
                except ValueError:
                    # Próbáljuk meg más formátumban is
                    try:
                        delivery_date = datetime.strptime(offer_data["delivery_date"], "%Y.%m.%d").date()
                    except ValueError:
                        try:
                            delivery_date = datetime.strptime(offer_data["delivery_date"], "%d.%m.%Y").date()
                        except ValueError:
                            try:
                                delivery_date = datetime.strptime(offer_data["delivery_date"], "%d/%m/%Y").date()
                            except ValueError:
                                raise ValueError("Ismeretlen dátumformátum.")
            else:
                delivery_date = offer_data["delivery_date"]
                
            today = datetime.now().date()
            
            if delivery_date < today:
                errors["delivery_date"] = "A beszállítási dátum nem lehet múltbeli."
            elif delivery_date == today:
                warnings["delivery_date"] = "A beszállítás a mai napra van ütemezve. Biztos benne?"
            elif delivery_date > today + timedelta(days=365):
                warnings["delivery_date"] = "A beszállítási dátum több mint egy év múlva van."
        except ValueError as e:
            errors["delivery_date"] = f"Érvénytelen dátum: {str(e)} Használja az ÉÉÉÉ-HH-NN formátumot."
    
    # Megjegyzés hosszának ellenőrzése
    if "note" in offer_data and offer_data.get("note"):
        if len(str(offer_data["note"])) > 500:
            errors["note"] = "A megjegyzés túl hosszú (maximum 500 karakter)."
    
    # Termelő ellenőrzése új ajánlatnál
    if is_new and "user_id" in offer_data:
        if not offer_data["user_id"]:
            errors["user_id"] = "A termelő kiválasztása kötelező új ajánlatnál."
    
    # Visszaigazolt mennyiség és ár ellenőrzése (ha vannak)
    if "confirmed_quantity" in offer_data and offer_data.get("confirmed_quantity"):
        try:
            # Biztonságos konverzió
            if isinstance(offer_data["confirmed_quantity"], str):
                # Vessző helyettesítése ponttal
                conf_qty_str = offer_data["confirmed_quantity"].replace(',', '.')
                conf_qty = float(conf_qty_str)
            else:
                conf_qty = float(offer_data["confirmed_quantity"])
                
            if conf_qty <= 0:
                errors["confirmed_quantity"] = "A visszaigazolt mennyiségnek pozitív számnak kell lennie."
            
            # Ha a mennyiség is meg van adva, ellenőrizzük az arányokat
            if "quantity_in_kg" in offer_data and offer_data.get("quantity_in_kg"):
                try:
                    original_qty = float(str(offer_data["quantity_in_kg"]).replace(',', '.'))
                    if conf_qty > original_qty * 1.1:  # 10% tolerancia
                        warnings["confirmed_quantity"] = "A visszaigazolt mennyiség jelentősen magasabb, mint az eredeti mennyiség."
                except ValueError:
                    pass  # Az eredeti mennyiség már hibás, így ezt nem ellenőrizzük
        except ValueError:
            errors["confirmed_quantity"] = "A visszaigazolt mennyiségnek számnak kell lennie."
    
    if "confirmed_price" in offer_data and offer_data.get("confirmed_price"):
        try:
            # Biztonságos konverzió
            if isinstance(offer_data["confirmed_price"], str):
                # Vessző helyettesítése ponttal
                conf_price_str = offer_data["confirmed_price"].replace(',', '.')
                conf_price = float(conf_price_str)
            else:
                conf_price = float(offer_data["confirmed_price"])
                
            if conf_price < 0:
                errors["confirmed_price"] = "A visszaigazolt ár nem lehet negatív."
            
            # Ha az eredeti ár is meg van adva, ellenőrizzük az arányokat
            if "price" in offer_data and offer_data.get("price"):
                try:
                    original_price = float(str(offer_data["price"]).replace(',', '.'))
                    
                    if conf_price < original_price * 0.8:  # 20% csökkenés
                        warnings["confirmed_price"] = "A visszaigazolt ár jelentősen alacsonyabb, mint az eredeti ár."
                    elif conf_price > original_price * 1.2:  # 20% emelkedés
                        warnings["confirmed_price"] = "A visszaigazolt ár jelentősen magasabb, mint az eredeti ár."
                except ValueError:
                    pass  # Az eredeti ár már hibás, így ezt nem ellenőrizzük
        except ValueError:
            errors["confirmed_price"] = "A visszaigazolt árnak számnak kell lennie."
    
    # Érvényesség meghatározása
    is_valid = len(errors) == 0
    
    return is_valid, errors, warnings

def validate_email(email):
    """
    Email cím validálás.
    
    Args:
        email (str): Az ellenőrizendő email cím
    
    Returns:
        bool: True, ha érvényes, False egyébként
    """
    if not email:
        return False
        
    # RFC 5322 kompatibilis email regex
    pattern = r"""(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])"""
    return bool(re.match(pattern, email, re.IGNORECASE))

def validate_phone(phone):
    """
    Telefonszám validálás.
    
    Args:
        phone (str): Az ellenőrizendő telefonszám
    
    Returns:
        bool: True, ha érvényes, False egyébként
    """
    if not phone:
        return False
        
    # Szóközök, kötőjelek, zárójelek eltávolítása
    stripped_phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Érvényes formátumok:
    # +36701234567, 06701234567, 36701234567, 701234567
    patterns = [
        r'^\+?\d{1,3}\d{9,}$',  # Nemzetközi formátum: +36701234567
        r'^0\d{9,}$',            # Magyar formátum: 06701234567
        r'^\d{9,}$'              # Csak számjegyek: 701234567 vagy 36701234567
    ]
    
    return any(re.match(pattern, stripped_phone) for pattern in patterns)

def validate_safe_filename(filename):
    """
    Biztonságos fájlnév ellenőrzése.
    
    Args:
        filename (str): Az ellenőrizendő fájlnév
    
    Returns:
        bool: True, ha biztonságos, False egyébként
    """
    if not filename:
        return False
        
    # Fájlnév maximum 255 karakter lehet
    if len(filename) > 255:
        return False
    
    # Csak megengedett karaktereket tartalmazhat (betűk, számok, pont, kötőjel, aláhúzás)
    pattern = r'^[a-zA-Z0-9\.\-_]+$'
    return bool(re.match(pattern, filename))

def sanitize_html(text):
    """
    HTML karakterek eltávolítása a szövegből a biztonság érdekében.
    
    Args:
        text (str): A tisztítandó szöveg
    
    Returns:
        str: A tisztított szöveg
    """
    if not text:
        return ""
    
    # Az összes HTML tag és entitás eltávolítása
    sanitized = re.sub(r'<[^>]*?>', '', text)
    sanitized = re.sub(r'&[^;]+;', '', sanitized)
    
    return sanitized

def show_validation_errors(errors, warnings):
    """
    Hibaüzenetek és figyelmeztetések megjelenítése Streamlit-ben.
    
    Args:
        errors (dict): Hibaüzenetek szótára (mező: hibaüzenet)
        warnings (dict): Figyelmeztetések szótára (mező: figyelmeztetés)
    """
    # Hibák megjelenítése
    if errors:
        error_container = st.container()
        with error_container:
            st.error("Az űrlap kitöltése során hibák merültek fel:")
            for field, message in errors.items():
                st.markdown(f"- **{field}**: {message}")
    
    # Figyelmeztetések megjelenítése
    if warnings:
        warning_container = st.container()
        with warning_container:
            st.warning("Figyelmeztetések:")
            for field, message in warnings.items():
                st.markdown(f"- **{field}**: {message}")