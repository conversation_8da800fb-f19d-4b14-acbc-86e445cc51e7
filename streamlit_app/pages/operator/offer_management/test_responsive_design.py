"""
Tesztmodul a továbbfejlesztett reszponzív design teszteléséhez.

Ez a modul bemutatja a TASK-1.4 feladat keretében fejlesztett
reszponzív és mobil/touch-optimalizált funkciókat.
"""
import streamlit as st
from datetime import datetime, timedelta
import logging
import json
import uuid

try:
    from streamlit_app.pages.operator.offer_management.enhanced_responsive_ui import (
        inject_responsive_enhancements,
        detect_device_info,
        create_responsive_container,
        create_adaptive_grid,
        create_swipeable_item,
        render_adaptive_filter_panel
    )
    from streamlit_app.pages.operator.offer_management.active_filter_display import (
        render_active_filters_panel
    )
    from streamlit_app.pages.operator.offer_management.modern_search_panel import (
        render_search_form
    )
except ImportError:
    try:
        from pages.operator.offer_management.enhanced_responsive_ui import (
            inject_responsive_enhancements,
            detect_device_info,
            create_responsive_container,
            create_adaptive_grid,
            create_swipeable_item,
            render_adaptive_filter_panel
        )
        from pages.operator.offer_management.active_filter_display import (
            render_active_filters_panel
        )
        from pages.operator.offer_management.modern_search_panel import (
            render_search_form
        )
    except ImportError:
        try:
            from enhanced_responsive_ui import (
                inject_responsive_enhancements,
                detect_device_info,
                create_responsive_container,
                create_adaptive_grid,
                create_swipeable_item,
                render_adaptive_filter_panel
            )
            from active_filter_display import (
                render_active_filters_panel
            )
            from modern_search_panel import (
                render_search_form
            )
        except ImportError:
            st.error("Nem sikerült importálni a szükséges modulokat!")
            st.stop()

# Logging beállítása
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Oldal konfigurálása
st.set_page_config(
    page_title="Reszponzív Design Teszt",
    page_icon="📱",
    layout="wide"
)

# Reszponzív fejlesztések betöltése
inject_responsive_enhancements()

# Eszköz információk detektálása
device_info = detect_device_info()
device_type = device_info['deviceType']
screen_width = device_info['width']
screen_height = device_info['height']
is_touch = device_info['isTouch']
orientation = device_info['orientation']

# Főcím és leírás
st.title("Reszponzív Design Továbbfejlesztés Demo")

# Fejléc és info 
device_type_emoji = "📱" if device_type == "mobile" else "📟" if device_type == "tablet" else "🖥️"
st.markdown(f"""
### {device_type_emoji} Jelenlegi eszköz: {device_type.capitalize()}
Ez a demo a reszponzív design továbbfejlesztéseket mutatja be (TASK-1.4). Az oldal automatikusan alkalmazkodik a képernyő méretéhez 
és az eszköz típusához, valamint gesztusvezérlést kínál az érintőképernyős eszközökön.

**Az eszköz adatai:**
* Képernyőméret: {screen_width}×{screen_height} px 
* Érintőképernyő: {'Igen ✓' if is_touch else 'Nem ✗'}
* Tájolás: {orientation}
""")

# Tesztadatok inicializálása
if "test_offers" not in st.session_state:
    st.session_state.test_offers = [
        {
            "id": f"offer{i}",
            "product_name": f"Termék {i}",
            "producer_name": f"Termelő {i}",
            "status": ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"][i % 5],
            "quantity": 100 * (i + 1),
            "created_at": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"),
            "delivery_date": (datetime.now() + timedelta(days=i*7)).strftime("%Y-%m-%d")
        }
        for i in range(5)
    ]

# Szűrőbeállítások
if "demo_filters" not in st.session_state:
    st.session_state.demo_filters = {
        "status": None,
        "producer_id": None,
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_type_id": None,
        "min_quantity": None,
        "max_quantity": None
    }

# Eseménykezelők
def handle_filter_remove(key):
    if key in st.session_state.demo_filters:
        st.session_state.demo_filters[key] = None
        
        # Dátum szűrőket alapértelmezettre állítjuk, ne None-ra
        if key == "from_date":
            st.session_state.demo_filters["from_date"] = datetime.now().date() - timedelta(days=30)
        elif key == "to_date":
            st.session_state.demo_filters["to_date"] = datetime.now().date()

def handle_filter_clear():
    st.session_state.demo_filters = {
        "status": None,
        "producer_id": None,
        "from_date": datetime.now().date() - timedelta(days=30),
        "to_date": datetime.now().date(),
        "product_type_id": None,
        "min_quantity": None,
        "max_quantity": None
    }

def handle_offer_delete(offer_id):
    st.session_state.test_offers = [offer for offer in st.session_state.test_offers if offer["id"] != offer_id]
    st.success(f"Ajánlat törölve: {offer_id}")

def handle_offer_edit(offer_id):
    st.info(f"Ajánlat szerkesztése: {offer_id}")
    # Valós alkalmazásban itt nyitnánk meg a szerkesztő felületet

def handle_search(params):
    st.session_state.demo_filters.update(params)
    st.success("Keresési paraméterek frissítve!")

# Demók vezérlése tab-okkal
tab1, tab2, tab3, tab4 = st.tabs([
    "1. Adaptív Szűrőpanel", 
    "2. Swipe Gesztusok",
    "3. Többeszközös Megjelenítés",
    "4. Teljes Demó Alkalmazás"
])

with tab1:
    st.header("Adaptív Szűrőpanel")
    
    st.markdown("""
    Ez a komponens automatikusan alkalmazkodik a különböző eszközökhöz:
    - **Mobil**: Lebegő szűrőgomb és teljes képernyős drawer
    - **Tablet**: Oldalsáv elrendezésben megjelenő szűrőpanel
    - **Desktop**: Hagyományos összecsukható kártyapanel
    
    Próbálja ki különböző képernyőméreteken vagy eszközökön!
    """)
    
    # Szűrő komponensek demó funkció
    def render_demo_filters():
        st.selectbox("Státusz:", [None, "CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"],
                    format_func=lambda x: "Válasszon státuszt..." if x is None else x)
        
        st.text_input("Termelő azonosító:")
        
        col1, col2 = st.columns(2)
        with col1:
            st.date_input("Kezdő dátum:", value=datetime.now().date() - timedelta(days=30))
        with col2:
            st.date_input("Végső dátum:", value=datetime.now().date())
        
        st.button("🔍 Keresés", use_container_width=True)
    
    # Reszponzív szűrőpanel
    render_adaptive_filter_panel(
        filter_components_func=render_demo_filters,
        title="Ajánlatok szűrése",
        icon="🔍",
        collapsible=True,
        expanded=True,
        key="demo_filter"
    )
    
    st.markdown("""
    #### Implementációs megjegyzések:
    
    ```python
    render_adaptive_filter_panel(
        filter_components_func=render_demo_filters,
        title="Ajánlatok szűrése",
        icon="🔍",
        collapsible=True,
        expanded=True,
        key="demo_filter"
    )
    ```
    
    Ez a komponens:
    - Automatikusan detektálja az eszköztípust
    - Különböző UI-t használ az egyes eszköztípusoknak
    - Mobilon gesztusvezérlést is támogat
    """)

with tab2:
    st.header("Swipe Gesztusok")
    
    st.markdown("""
    Ez a demó a mobil eszközökre optimalizált swipe gesztus támogatást mutatja be.
    
    **Mobil és érintőképernyős készülékeken:**
    - Húzza (swipe) az elemeket balra a műveletek megjelenítéséhez
    - Húzza jobbra a műveletek elrejtéséhez
    - Érintse meg a törlés/szerkesztés gombokat a megfelelő művelethez
    
    **Asztali gépeken:**
    - Az egérkurzort az elem fölé mozgatva jelennek meg a műveletek
    """)
    
    # Ajánlatok megjelenítése swipeable elemekként
    for offer in st.session_state.test_offers:
        create_swipeable_item(
            content_func=lambda o=offer: st.markdown(f"""
                ### {o['product_name']}
                **Termelő:** {o['producer_name']}  
                **Státusz:** {o['status']}  
                **Mennyiség:** {o['quantity']} kg  
                **Szállítási dátum:** {o['delivery_date']}
            """),
            item_id=offer["id"],
            on_delete=handle_offer_delete,
            on_edit=handle_offer_edit
        )
    
    st.markdown("""
    #### Implementációs megjegyzések:
    
    ```python
    create_swipeable_item(
        content_func=lambda: st.markdown("..."),
        item_id="example_id",
        on_delete=handle_delete,
        on_edit=handle_edit
    )
    ```
    
    Ez a komponens:
    - JavaScript-et használ a touch események felismerésére
    - Különböző UI élményt nyújt mobil és asztali eszközökön
    - A swipe gesztusok csak mobilon/touch eszközökön aktiválódnak
    """)

with tab3:
    st.header("Többeszközös Megjelenítés")
    
    st.markdown("""
    Ez a demó azt mutatja be, hogyan lehet teljesen különböző tartalmakat megjeleníteni
    a különböző eszközökön, optimalizálva a felhasználói élményt minden képernyőméretre.
    """)
    
    # Eszköz-specifikus tartalom
    def mobile_content():
        st.markdown("### Mobilra optimalizált nézet")
        st.markdown("Ez a nézet egy egyszerűsített, egyoszlopos elrendezés kártyákkal.")
        
        for offer in st.session_state.test_offers[:3]:
            st.markdown(f"""
            <div style="border: 1px solid #ddd; border-radius: 10px; padding: 12px; margin-bottom: 10px;">
                <div style="font-weight: bold;">{offer['product_name']}</div>
                <div>Termelő: {offer['producer_name']}</div>
                <div>Státusz: <span style="color: blue;">{offer['status']}</span></div>
                <div>Mennyiség: {offer['quantity']} kg</div>
            </div>
            """, unsafe_allow_html=True)
        
        st.button("További ajánlatok betöltése...", use_container_width=True)

    def tablet_content():
        st.markdown("### Tabletre optimalizált nézet")
        st.markdown("Ez a nézet két oszlopot használ és közepes részletességű adatokat jelenít meg.")
        
        col1, col2 = st.columns(2)
        
        for i, offer in enumerate(st.session_state.test_offers[:4]):
            with col1 if i % 2 == 0 else col2:
                st.markdown(f"""
                <div style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                    <div style="font-weight: bold; font-size: 18px;">{offer['product_name']}</div>
                    <div>Termelő: {offer['producer_name']}</div>
                    <div>Státusz: <span style="color: blue;">{offer['status']}</span></div>
                    <div>Mennyiség: {offer['quantity']} kg</div>
                    <div>Létrehozva: {offer['created_at']}</div>
                    <div>Szállítás: {offer['delivery_date']}</div>
                </div>
                """, unsafe_allow_html=True)

    def desktop_content():
        st.markdown("### Asztali gépre optimalizált nézet")
        st.markdown("Ez a nézet táblázatos formátumot használ és a legtöbb adatot jeleníti meg.")
        
        # Táblázat fejléc
        col1, col2, col3, col4, col5, col6 = st.columns([1, 2, 2, 1, 1, 1])
        with col1: st.markdown("**ID**")
        with col2: st.markdown("**Termék**")
        with col3: st.markdown("**Termelő**")
        with col4: st.markdown("**Státusz**")
        with col5: st.markdown("**Mennyiség**")
        with col6: st.markdown("**Szállítás**")
        
        # Táblázat sorok
        for offer in st.session_state.test_offers:
            col1, col2, col3, col4, col5, col6 = st.columns([1, 2, 2, 1, 1, 1])
            with col1: st.markdown(offer['id'])
            with col2: st.markdown(offer['product_name'])
            with col3: st.markdown(offer['producer_name'])
            with col4: st.markdown(offer['status'])
            with col5: st.markdown(f"{offer['quantity']} kg")
            with col6: st.markdown(offer['delivery_date'])
    
    # Adaptív konténer a tartalom megjelenítéséhez
    create_responsive_container(
        content_func=lambda: st.info("Alapértelmezett tartalom, ez sose jelenik meg"),
        mobile_content_func=mobile_content,
        tablet_content_func=tablet_content,
        desktop_content_func=desktop_content
    )
    
    st.markdown("""
    #### Implementációs megjegyzések:
    
    ```python
    create_responsive_container(
        content_func=default_content,
        mobile_content_func=mobile_content,
        tablet_content_func=tablet_content,
        desktop_content_func=desktop_content
    )
    ```
    
    Ez a komponens:
    - Külön függvényeket hív meg a különböző eszköztípusokhoz
    - Teljesen különböző UI-t jelenít meg eszközönként
    - Automatikusan átáll a megfelelő nézetre a képernyőméret alapján
    """)

with tab4:
    st.header("Teljes Demó Alkalmazás")
    
    st.markdown("""
    Ez a demo egy komplett alkalmazás működését mutatja be, ahol az összes reszponzív
    komponens együttműködik. Az alkalmazás a következő elemeket tartalmazza:
    
    1. Adaptív szűrőpanel 
    2. Swipe-olható ajánlat elemek
    3. Aktív szűrők megjelenítése
    4. Eszköztípus-specifikus elrendezés
    """)
    
    # Adaptív elrendezés
    if device_type == "mobile":
        # Mobilon egymás alatti elrendezés
        # Első a modern szűrőpanel
        render_adaptive_filter_panel(
            filter_components_func=lambda: render_search_form(handle_search=handle_search, prefix="complete_demo"),
            title="Ajánlatok szűrése",
            icon="🔍",
            collapsible=True,
            expanded=False,  # Mobilon alapértelmezetten összecsukva
            key="complete_demo_filter"
        )
        
        # Aktív szűrők panel
        render_active_filters_panel(
            filters=st.session_state.demo_filters,
            prefix="demo_complete",
            title="Aktív szűrők",
            icon="🏷️",
            on_remove=handle_filter_remove,
            on_clear_all=handle_filter_clear,
            categorize=True
        )
        
        # Ajánlatok megjelenítése swipe funkciókkal
        st.subheader("Ajánlatok")
        
        # Mobilon csak néhány ajánlatot mutatunk
        for offer in st.session_state.test_offers[:3]:
            create_swipeable_item(
                content_func=lambda o=offer: st.markdown(f"""
                    ### {o['product_name']}
                    **Termelő:** {o['producer_name']}  
                    **Státusz:** {o['status']}  
                    **Mennyiség:** {o['quantity']} kg  
                    **Létrehozva:** {o['created_at']}
                """),
                item_id=offer["id"],
                on_delete=handle_offer_delete,
                on_edit=handle_offer_edit
            )
        
        # Mobilon több betöltése gomb
        if len(st.session_state.test_offers) > 3:
            st.button("További ajánlatok betöltése...", use_container_width=True)
    
    elif device_type == "tablet":
        # Tableten két részre osztott elrendezés: bal oldali szűrőpanel, jobb oldali tartalom
        col1, col2 = st.columns([1, 2])
        
        with col1:
            # Szűrőpanel
            st.subheader("Szűrések")
            render_search_form(handle_search=handle_search, prefix="complete_demo")
        
        with col2:
            # Aktív szűrők + ajánlatok listája
            render_active_filters_panel(
                filters=st.session_state.demo_filters,
                prefix="demo_complete",
                title="Aktív szűrők",
                icon="🏷️",
                on_remove=handle_filter_remove,
                on_clear_all=handle_filter_clear,
                categorize=True
            )
            
            # Ajánlatok listája két oszlopban
            st.subheader("Ajánlatok")
            
            # Kétoszlopos elrendezés a tableten
            inner_col1, inner_col2 = st.columns(2)
            
            for i, offer in enumerate(st.session_state.test_offers):
                with inner_col1 if i % 2 == 0 else inner_col2:
                    with st.container():
                        st.markdown(f"""
                        <div style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                            <div style="font-weight: bold; font-size: 18px;">{offer['product_name']}</div>
                            <div>Termelő: {offer['producer_name']}</div>
                            <div>Státusz: <span style="color: blue;">{offer['status']}</span></div>
                            <div>Mennyiség: {offer['quantity']} kg</div>
                            <div>Szállítás: {offer['delivery_date']}</div>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # Műveleti gombok egy sorban
                        btn_col1, btn_col2 = st.columns(2)
                        with btn_col1:
                            if st.button("✏️ Szerkesztés", key=f"edit_{offer['id']}", use_container_width=True):
                                handle_offer_edit(offer["id"])
                        with btn_col2:
                            if st.button("🗑️ Törlés", key=f"delete_{offer['id']}", use_container_width=True):
                                handle_offer_delete(offer["id"])
    
    else:  # desktop
        # Desktop nézet három részre osztva: szűrőpanel, aktív szűrők, táblázat
        # Asztali nézeten összecsukható szűrőpanel
        render_adaptive_filter_panel(
            filter_components_func=lambda: render_search_form(handle_search=handle_search, prefix="complete_demo"),
            title="Ajánlatok szűrése",
            icon="🔍",
            collapsible=True,
            expanded=True,
            key="complete_demo_filter"
        )
        
        # Aktív szűrők
        render_active_filters_panel(
            filters=st.session_state.demo_filters,
            prefix="demo_complete",
            title="Aktív szűrők",
            icon="🏷️",
            on_remove=handle_filter_remove,
            on_clear_all=handle_filter_clear,
            categorize=True
        )
        
        # Táblázatos megjelenítés
        st.subheader("Ajánlatok listája")
        
        # Táblázat fejléc
        col1, col2, col3, col4, col5, col6, col7 = st.columns([0.5, 1.5, 1.5, 1, 1, 1, 1])
        with col1: st.markdown("**ID**")
        with col2: st.markdown("**Termék**")
        with col3: st.markdown("**Termelő**")
        with col4: st.markdown("**Státusz**")
        with col5: st.markdown("**Mennyiség**")
        with col6: st.markdown("**Dátumok**")
        with col7: st.markdown("**Műveletek**")
        
        # Táblázat sorok
        for offer in st.session_state.test_offers:
            col1, col2, col3, col4, col5, col6, col7 = st.columns([0.5, 1.5, 1.5, 1, 1, 1, 1])
            with col1: st.markdown(offer['id'])
            with col2: st.markdown(offer['product_name'])
            with col3: st.markdown(offer['producer_name'])
            with col4: st.markdown(offer['status'])
            with col5: st.markdown(f"{offer['quantity']} kg")
            with col6: 
                st.markdown(f"Létrehozva: {offer['created_at']}")
                st.markdown(f"Szállítás: {offer['delivery_date']}")
            with col7:
                btn_col1, btn_col2 = st.columns(2)
                with btn_col1:
                    if st.button("✏️", key=f"edit_{offer['id']}"):
                        handle_offer_edit(offer["id"])
                with btn_col2:
                    if st.button("🗑️", key=f"delete_{offer['id']}"):
                        handle_offer_delete(offer["id"])

# Fejlesztői információk az oldal alján
with st.expander("Fejlesztői információk"):
    st.markdown("""
    **Modul:** enhanced_responsive_ui.py
    
    **Fő funkciók:**
    - `inject_responsive_enhancements`: Az összes reszponzív fejlesztés betöltése
    - `detect_device_info`: Eszköz adatok detektálása
    - `create_responsive_container`: Eszköztípus-specifikus tartalom
    - `create_adaptive_grid`: Adaptív grid elrendezés
    - `create_swipeable_item`: Swipe gesztusokat támogató elem mobilra
    - `render_adaptive_filter_panel`: Adaptív szűrőpanel
    
    **Technológiák:**
    - CSS Media Queries a képernyőméret-alapú adaptációhoz
    - JavaScript a gesztusvezérlés és eszközdetektálás megvalósításához
    - CSS Grid a reszponzív elrendezéshez
    - Swipe gesztus felismerés érintőképernyős eszközökhöz
    
    **Egyéb:**
    - Képernyőméret szerinti HTML osztályok
    - Eszköztípus felismerés (mobil, tablet, desktop)
    - Érintőképernyő detektálás
    - Képernyő tájolás felismerés
    
    Ez a komponens könyvtár teljes körű reszponzív felületet biztosít a Streamlit-alkalmazáshoz,
    és segít optimalizálni a felhasználói élményt minden eszköztípuson.
    """)