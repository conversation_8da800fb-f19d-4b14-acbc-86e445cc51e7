"""
Modern Dark Theme Offer Detail View
Sötét témájú, modern megjelenésű ajánlat részletező
"""
import streamlit as st
import streamlit.components.v1 as components
from datetime import datetime
import plotly.graph_objects as go
import json
import html
import logging

# Import our HTML renderer
try:
    from .html_renderer import render_html, render_css
except ImportError:
    # Fallback implementation
    def render_html(content, height=None):
        if hasattr(st, 'html'):
            st.html(content)
        else:
            components.html(content, height=height or 600, scrolling=True)
    
    def render_css(css_content):
        st.markdown(css_content, unsafe_allow_html=True)

logger = logging.getLogger(__name__)

class DarkThemeOfferDetail:
    """Sötét témájú modern ajánlat részletező"""
    
    def __init__(self, offer_data):
        self.offer = offer_data
        self.inject_dark_theme_styles()
    
    def inject_dark_theme_styles(self):
        """Sötét téma CSS injektálása"""
        dark_theme_css = """
        <style>
            /* CSS változók a sötét témához - Cleaner modern design */
            :root {
                --bg-primary: #0a0a0a;
                --bg-secondary: #111111;
                --bg-card: #1a1a1a;
                --bg-hover: #252525;
                
                --text-primary: #ffffff;
                --text-secondary: #b0b0b0;
                --text-muted: #707070;
                
                --border-color: #2a2a2a;
                --border-hover: #3a3a3a;
                
                /* Status colors */
                --status-green: #10dc60;
                --status-yellow: #ffce00;
                --status-red: #ff5045;
                --status-blue: #0099e0;
                --status-orange: #ff8c1a;
                --status-purple: #7c3aed;
                
                /* Action colors */
                --primary: #0099e0;
                --primary-hover: #0077b5;
                --success: #10dc60;
                --warning: #ffce00;
                --danger: #ff5045;
                
                /* Shadows */
                --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
                --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
                --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.20);
                --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.25);
            }
            
            /* Light theme variables */
            .light-theme {
                --bg-primary: #ffffff;
                --bg-secondary: #f8f9fa;
                --bg-card: #ffffff;
                --bg-hover: #e9ecef;
                
                --text-primary: #212529;
                --text-secondary: #495057;
                --text-muted: #6c757d;
                
                --border-color: #dee2e6;
                --border-hover: #ced4da;
                
                /* Light theme shadows */
                --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.075);
                --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
                --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.125);
                --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.15);
            }
            
            /* Smooth theme transitions */
            body, .dark-card, .offer-header, .action-bar, .timeline-event {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            }
            
            /* Modern card design - cleaner and simpler */
            .dark-card {
                background: var(--bg-card);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                box-shadow: var(--shadow-sm);
                position: relative;
                overflow: hidden;
            }
            
            /* Colored top strip for cards */
            .dark-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--primary);
            }
            
            .dark-card.status-green::before {
                background: var(--status-green);
            }
            
            .dark-card.status-orange::before {
                background: var(--status-orange);
            }
            
            .dark-card.status-yellow::before {
                background: var(--status-yellow);
            }
            
            .dark-card.status-blue::before {
                background: var(--status-blue);
            }
            
            .dark-card.status-purple::before {
                background: var(--status-purple);
            }
            
            /* Enhanced header section - larger and gradient */
            .offer-header {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                border-radius: 16px;
                padding: 3rem;
                margin-bottom: 2rem;
                position: relative;
                overflow: hidden;
            }
            
            .offer-header::after {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                animation: pulse 4s ease-in-out infinite;
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.5; }
                50% { transform: scale(1.1); opacity: 0.3; }
            }
            
            .offer-id {
                font-size: 2.5rem;
                font-weight: 700;
                color: white;
                margin-bottom: 0.5rem;
            }
            
            .offer-date {
                color: rgba(255, 255, 255, 0.8);
                font-size: 0.875rem;
                margin-bottom: 1.5rem;
            }
            
            /* Status badges - cleaner design */
            .status-badge {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-weight: 600;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                color: white;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            .status-elfogadva {
                background: linear-gradient(135deg, rgba(16, 220, 96, 0.2) 0%, rgba(16, 220, 96, 0.1) 100%);
                color: var(--status-green);
                border: 2px solid var(--status-green);
            }
            
            .status-visszaigazolva {
                background: linear-gradient(135deg, rgba(255, 140, 26, 0.2) 0%, rgba(255, 140, 26, 0.1) 100%);
                color: var(--status-orange);
                border: 2px solid var(--status-orange);
            }
            
            .status-letrehozva {
                background: linear-gradient(135deg, rgba(255, 206, 0, 0.2) 0%, rgba(255, 206, 0, 0.1) 100%);
                color: var(--status-yellow);
                border: 2px solid var(--status-yellow);
            }
            
            .status-elutasitva {
                background: linear-gradient(135deg, rgba(255, 80, 69, 0.2) 0%, rgba(255, 80, 69, 0.1) 100%);
                color: var(--status-red);
                border: 2px solid var(--status-red);
            }
            
            .status-veglegesitve {
                background: linear-gradient(135deg, rgba(0, 153, 224, 0.2) 0%, rgba(0, 153, 224, 0.1) 100%);
                color: var(--status-blue);
                border: 2px solid var(--status-blue);
            }
            
            /* Card headers with icons */
            .card-header {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 1.25rem;
                padding-bottom: 0.75rem;
                border-bottom: 1px solid var(--border-color);
            }
            
            .card-icon {
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
            }
            
            .card-title {
                color: var(--text-primary);
                font-size: 1rem;
                font-weight: 600;
                flex: 1;
            }
            
            /* Info rows - cleaner spacing */
            .info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.875rem 0;
            }
            
            .info-row + .info-row {
                border-top: 1px solid rgba(255, 255, 255, 0.05);
            }
            
            .info-label {
                color: var(--text-muted);
                font-size: 0.875rem;
                font-weight: 400;
            }
            
            .info-value {
                color: var(--text-primary);
                font-weight: 500;
                text-align: right;
                font-size: 0.875rem;
            }
            
            /* Kiemelt értékek */
            .highlight-value {
                color: var(--primary);
                font-weight: 600;
            }
            
            /* Progress bar */
            .progress-container {
                width: 100%;
                height: 8px;
                background: var(--bg-secondary);
                border-radius: 4px;
                overflow: hidden;
                margin: 1rem 0;
            }
            
            .progress-bar {
                height: 100%;
                background: var(--status-green);
                transition: width 0.3s ease;
            }
            
            /* Grid layout - larger gaps like in example */
            .offer-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
                margin-bottom: 2rem;
            }
            
            @media (max-width: 1024px) {
                .offer-grid {
                    grid-template-columns: 1fr;
                }
            }
            
            /* State indicators */
            .state-indicators {
                display: flex;
                gap: 1rem;
                margin-top: 2rem;
                flex-wrap: wrap;
            }
            
            .state-indicator {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1.25rem;
                background: var(--bg-card);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                font-size: 0.875rem;
            }
            
            .state-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: var(--text-muted);
            }
            
            .state-indicator.active .state-dot {
                background: var(--status-green);
                box-shadow: 0 0 0 3px rgba(16, 220, 96, 0.2);
            }
            
            /* Enhanced action bar with glassmorphism */
            .action-bar {
                position: sticky;
                top: 20px;
                z-index: 100;
                background: rgba(26, 29, 35, 0.8);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                padding: 1.25rem;
                margin: -1rem -1rem 1.5rem -1rem;
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            }
            
            .action-button {
                position: relative;
                padding: 0.75rem 1.5rem;
                border-radius: 12px;
                border: 1px solid var(--border-color);
                background: var(--bg-card);
                color: var(--text-primary);
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                font-family: inherit;
                font-size: 0.875rem;
                overflow: hidden;
            }
            
            /* Ripple effect */
            .action-button::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 0;
                height: 0;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                transform: translate(-50%, -50%);
                transition: width 0.6s, height 0.6s;
            }
            
            .action-button:active::after {
                width: 200px;
                height: 200px;
            }
            
            .action-button:hover {
                background: var(--bg-hover);
                border-color: var(--primary);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 153, 224, 0.3);
            }
            
            .action-button.primary {
                background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
                border-color: var(--primary);
                color: white;
            }
            
            .action-button.primary:hover {
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary) 100%);
                box-shadow: 0 6px 20px rgba(0, 153, 224, 0.5);
            }
            
            /* Enhanced interactive timeline */
            .timeline-container {
                position: relative;
                padding-left: 2rem;
            }
            
            .timeline-container::before {
                content: '';
                position: absolute;
                left: 0.75rem;
                top: 0;
                bottom: 0;
                width: 2px;
                background: linear-gradient(180deg, var(--primary) 0%, var(--status-blue) 100%);
                opacity: 0.3;
            }
            
            .timeline-event {
                display: flex;
                gap: 1rem;
                margin-bottom: 1.25rem;
                padding: 1rem;
                background: var(--bg-secondary);
                border-radius: 12px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                border: 1px solid transparent;
                opacity: 0;
                animation: slideInRight 0.5s ease-out forwards;
            }
            
            .timeline-event:nth-child(1) { animation-delay: 0.1s; }
            .timeline-event:nth-child(2) { animation-delay: 0.2s; }
            .timeline-event:nth-child(3) { animation-delay: 0.3s; }
            .timeline-event:nth-child(4) { animation-delay: 0.4s; }
            
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            .timeline-event:hover {
                background: var(--bg-hover);
                border-color: var(--border-hover);
                transform: translateX(5px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            }
            
            .timeline-dot {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                margin-top: 0.25rem;
                flex-shrink: 0;
                position: relative;
                box-shadow: 0 0 0 4px rgba(0, 153, 224, 0.2);
                animation: ripple 2s ease-in-out infinite;
            }
            
            @keyframes ripple {
                0% { box-shadow: 0 0 0 4px rgba(0, 153, 224, 0.2); }
                50% { box-shadow: 0 0 0 8px rgba(0, 153, 224, 0.1); }
                100% { box-shadow: 0 0 0 4px rgba(0, 153, 224, 0.2); }
            }
            
            .timeline-content {
                flex: 1;
            }
            
            .timeline-title {
                color: var(--text-primary);
                font-weight: 600;
                margin-bottom: 0.25rem;
                font-size: 0.925rem;
            }
            
            .timeline-meta {
                color: var(--text-muted);
                font-size: 0.8rem;
                opacity: 0.8;
            }
            
            /* Producer card */
            .producer-avatar {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, var(--status-orange) 0%, #ff6b1a 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 2rem;
                font-weight: 600;
                margin: 0 auto;
                box-shadow: 0 4px 12px rgba(255, 140, 26, 0.3);
            }
            
            /* Enhanced delivery info */
            .delivery-card {
                background: linear-gradient(135deg, rgba(255, 140, 26, 0.15) 0%, rgba(255, 140, 26, 0.05) 100%);
                border: 2px solid rgba(255, 140, 26, 0.3);
                position: relative;
                overflow: hidden;
            }
            
            .delivery-card::before {
                content: '🚚';
                position: absolute;
                font-size: 4rem;
                opacity: 0.1;
                right: -1rem;
                bottom: -1rem;
                transform: rotate(-15deg);
            }
            
            .delivery-icon {
                font-size: 2.5rem;
                margin-bottom: 0.5rem;
                animation: bounce 2s ease-in-out infinite;
            }
            
            @keyframes bounce {
                0%, 100% { transform: translateY(0); }
                50% { transform: translateY(-10px); }
            }
            
            /* Enhanced progress bars */
            .progress-container {
                width: 100%;
                height: 10px;
                background: var(--bg-secondary);
                border-radius: 5px;
                overflow: hidden;
                margin: 1.5rem 0;
                position: relative;
            }
            
            .progress-bar {
                height: 100%;
                background: linear-gradient(90deg, var(--status-green) 0%, var(--success) 100%);
                transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }
            
            .progress-bar::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.3) 50%,
                    rgba(255, 255, 255, 0) 100%
                );
                animation: shimmerProgress 2s linear infinite;
            }
            
            @keyframes shimmerProgress {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
            
            /* Pricing visualization */
            .price-chart {
                background: var(--bg-secondary);
                border-radius: 12px;
                padding: 1rem;
                margin-top: 1rem;
                border: 1px solid var(--border-color);
            }
            
            /* Responsive */
            @media (max-width: 768px) {
                .offer-grid {
                    grid-template-columns: 1fr;
                }
                
                .offer-header {
                    padding: 1.5rem;
                }
                
                .offer-id {
                    font-size: 1.5rem;
                }
                
                .action-bar {
                    padding: 0.75rem;
                    margin: -0.75rem -0.75rem 1rem -0.75rem;
                }
                
                .action-button {
                    font-size: 0.75rem;
                    padding: 0.4rem 0.8rem;
                }
            }
            
            /* Theme toggle button styles */
            .theme-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                width: 60px;
                height: 30px;
                background: var(--bg-card);
                border: 2px solid var(--border-color);
                border-radius: 15px;
                cursor: pointer;
                overflow: hidden;
                transition: all 0.3s ease;
                box-shadow: var(--shadow-md);
            }
            
            .theme-toggle:hover {
                transform: scale(1.05);
                box-shadow: var(--shadow-lg);
            }
            
            .theme-toggle-inner {
                position: relative;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 5px;
            }
            
            .theme-icon {
                font-size: 18px;
                transition: all 0.3s ease;
            }
            
            .sun-icon {
                color: #fdb813;
            }
            
            .moon-icon {
                color: #f5f3ce;
            }
            
            .theme-toggle-slider {
                position: absolute;
                width: 24px;
                height: 24px;
                background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
                border-radius: 50%;
                top: 3px;
                left: 3px;
                transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            
            .light-theme .theme-toggle-slider {
                transform: translateX(30px);
            }
            
            .light-theme .sun-icon {
                opacity: 1;
                transform: scale(1.2);
            }
            
            .light-theme .moon-icon {
                opacity: 0.5;
                transform: scale(0.8);
            }
            
            body:not(.light-theme) .sun-icon {
                opacity: 0.5;
                transform: scale(0.8);
            }
            
            body:not(.light-theme) .moon-icon {
                opacity: 1;
                transform: scale(1.2);
            }
        </style>
        """
        
        st.markdown(dark_theme_css, unsafe_allow_html=True)
    
    def render_theme_toggle(self):
        """Render theme toggle button"""
        theme_toggle_html = """
        <div class="theme-toggle" onclick="toggleTheme()">
            <div class="theme-toggle-inner">
                <span class="theme-icon sun-icon">☀️</span>
                <span class="theme-icon moon-icon">🌙</span>
                <div class="theme-toggle-slider"></div>
            </div>
        </div>
        
        <script>
            // Check for saved theme preference or default to dark mode
            const currentTheme = localStorage.getItem('theme') || 'dark';
            if (currentTheme === 'light') {
                document.body.classList.add('light-theme');
            }
            
            function toggleTheme() {
                const body = document.body;
                const isLight = body.classList.contains('light-theme');
                
                if (isLight) {
                    body.classList.remove('light-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    body.classList.add('light-theme');
                    localStorage.setItem('theme', 'light');
                }
                
                // Trigger a subtle animation
                const toggle = document.querySelector('.theme-toggle');
                toggle.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    toggle.style.transform = '';
                }, 150);
            }
            
            // Apply saved theme on page load
            document.addEventListener('DOMContentLoaded', function() {
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme === 'light') {
                    document.body.classList.add('light-theme');
                }
            });
        </script>
        """
        
        render_html(theme_toggle_html, height=50)
    
    def render(self):
        """Fő renderelő metódus"""
        # Main container
        st.markdown('<div style="max-width: 1200px; margin: 0 auto; padding: 2rem;">', unsafe_allow_html=True)
        
        # Enhanced header
        self.render_enhanced_header()
        
        # Main grid layout with cards
        grid_html = '<div class="offer-grid">'
        
        # First row
        grid_html += self.render_offer_info_card()
        grid_html += self.render_confirmation_card()
        
        # Second row
        grid_html += self.render_chart_card()
        grid_html += self.render_producer_card()
        
        grid_html += '</div>'
        
        render_html(grid_html, height=1200)
        
        # State indicators at the bottom
        self.render_state_indicators()
        
        # Close container
        st.markdown('</div>', unsafe_allow_html=True)
    
    def render_action_bar(self):
        """Műveleti sáv"""
        action_bar_html = """
        <div class="action-bar">
            <button class="action-button primary" onclick="alert('Szerkesztés funkció fejlesztés alatt')">
                ✏️ Szerkesztés
            </button>
            <button class="action-button" onclick="navigator.clipboard.writeText(window.location.href).then(() => alert('Link másolva!'))">
                📋 Másolás
            </button>
            <button class="action-button" onclick="alert('Export funkció fejlesztés alatt')">
                📊 Export
            </button>
            <button class="action-button" onclick="if(confirm('Biztosan törölni szeretné?')) alert('Törlés funkció fejlesztés alatt')">
                🗑️ Törlés
            </button>
        </div>
        """
        render_html(action_bar_html, height=80)
    
    def render_enhanced_header(self):
        """Enhanced header with gradient background"""
        status_map = {
            'CREATED': 'letrehozva',
            'CONFIRMED_BY_COMPANY': 'visszaigazolva',
            'ACCEPTED_BY_USER': 'elfogadva',
            'REJECTED_BY_USER': 'elutasitva',
            'FINALIZED': 'veglegesitve'
        }
        
        status = self.offer.get('status', 'CREATED')
        status_class = status_map.get(status, 'letrehozva')
        status_text = self._get_status_display(status)
        
        header_html = f"""
        <div class="offer-header">
            <div class="offer-id">Ajánlat #{self.offer.get('id', 'N/A')}</div>
            <div class="offer-date">Létrehozva: {self._format_date(self.offer.get('created_at'))}</div>
            <div class="status-badge">
                <span>●</span>
                <span>{status_text}</span>
            </div>
        </div>
        """
        
        render_html(header_html, height=200)
    
    def render_offer_info_card(self):
        """Offer information card"""
        product = self.offer.get('product_type', {})
        product_name = html.escape(str(product.get('name', 'N/A')))
        category_name = html.escape(str(product.get('category', {}).get('name', 'N/A')))
        
        return f"""
        <div class="dark-card status-blue">
            <div class="card-header">
                <div class="card-icon">📋</div>
                <div class="card-title">Ajánlat adatai</div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Azonosító</span>
                <span class="info-value">#{self.offer.get('id', 'N/A')}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Beszállítás</span>
                <span class="info-value">{self._format_date(self.offer.get('delivery_date'))}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Termék</span>
                <span class="info-value">{product_name}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Kategória</span>
                <span class="info-value">{category_name}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Minőség</span>
                <span class="info-value">{self.offer.get('quality_class', 'I. Osztály')}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Mennyiség</span>
                <span class="info-value highlight-value">{self._format_quantity(self.offer.get('quantity_in_kg'))} kg</span>
            </div>
        </div>
        """
    
    def render_confirmation_card(self):
        """Confirmation data card with progress"""
        quantity = self._to_float(self.offer.get('quantity_in_kg', 0))
        price = self._to_float(self.offer.get('price', 0))
        confirmed_quantity = self._to_float(self.offer.get('confirmed_quantity', quantity))
        confirmed_price = self._to_float(self.offer.get('confirmed_price', price))
        confirmed_total = confirmed_quantity * confirmed_price if confirmed_quantity and confirmed_price else 0
        
        # Progress calculation
        progress = min(100, (confirmed_quantity / quantity * 100) if quantity > 0 else 0)
        
        return f"""
        <div class="dark-card status-green">
            <div class="card-header">
                <div class="card-icon">✅</div>
                <div class="card-title">Visszaigazolás</div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Eredeti mennyiség</span>
                <span class="info-value">{self._format_quantity(quantity)} kg</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Visszaigazolt mennyiség</span>
                <span class="info-value highlight-value">{self._format_quantity(confirmed_quantity)} kg</span>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar" style="width: {progress:.1f}%"></div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Visszaigazolt ár</span>
                <span class="info-value">{self._format_price(confirmed_price)}/kg</span>
            </div>
            
            <div class="info-row" style="margin-top: 1rem; padding-top: 1rem; border-top: 2px solid var(--border-color);">
                <span class="info-label" style="font-weight: 600;">Összérték</span>
                <span class="info-value highlight-value" style="font-size: 1.25rem;">
                    {self._format_price(confirmed_total)}
                </span>
            </div>
        </div>
        """
    
    def render_chart_card(self):
        """Price comparison chart card"""
        return f"""
        <div class="dark-card status-purple">
            <div class="card-header">
                <div class="card-icon">📊</div>
                <div class="card-title">Ár összehasonlítás</div>
            </div>
            
            <div style="padding: 1rem 0;">
                <canvas id="priceChart" width="400" height="300"></canvas>
            </div>
            
            <script>
                // Chart will be rendered here
                setTimeout(() => {{
                    const ctx = document.getElementById('priceChart');
                    if (ctx) {{
                        // Placeholder for chart
                        ctx.style.background = 'rgba(255,255,255,0.02)';
                        ctx.style.borderRadius = '8px';
                    }}
                }}, 100);
            </script>
        </div>
        """
    
    def render_producer_card(self):
        """Producer information card"""
        producer = self.offer.get('user', {})
        producer_name = html.escape(str(producer.get('contact_name', 'N/A')))
        company_name = html.escape(str(producer.get('company_name', 'N/A')))
        email = html.escape(str(producer.get('email', '')))
        phone = html.escape(str(producer.get('phone', '')))
        
        return f"""
        <div class="dark-card status-orange">
            <div class="card-header">
                <div class="card-icon">👤</div>
                <div class="card-title">Termelő adatai</div>
            </div>
            
            <div style="text-align: center; padding: 2rem 0;">
                <div class="producer-avatar">
                    {producer_name[0].upper() if producer_name and producer_name != 'N/A' else '?'}
                </div>
                
                <div style="margin-top: 1rem;">
                    <div style="color: var(--text-primary); font-weight: 600; font-size: 1.125rem;">
                        {producer_name}
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem; margin-top: 0.25rem;">
                        {company_name}
                    </div>
                </div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Email</span>
                <span class="info-value">
                    <a href="mailto:{email}" style="color: var(--primary); text-decoration: none;">
                        {email if email else 'N/A'}
                    </a>
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Telefon</span>
                <span class="info-value">
                    <a href="tel:{phone}" style="color: var(--primary); text-decoration: none;">
                        {phone if phone else 'N/A'}
                    </a>
                </span>
            </div>
        </div>
        """
    
    def render_state_indicators(self):
        """Render state indicators at the bottom"""
        status = self.offer.get('status', 'CREATED')
        
        indicators_html = """
        <div class="state-indicators">
        """
        
        states = [
            ('Létrehozva', status in ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']),
            ('Visszaigazolva', status in ['CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']),
            ('Elfogadva', status in ['ACCEPTED_BY_USER', 'FINALIZED']),
            ('Véglegesítve', status == 'FINALIZED')
        ]
        
        for state_name, is_active in states:
            active_class = 'active' if is_active else ''
            indicators_html += f"""
            <div class="state-indicator {active_class}">
                <div class="state-dot"></div>
                <span>{state_name}</span>
            </div>
            """
        
        indicators_html += """
        </div>
        """
        
        render_html(indicators_html, height=80)
    
    def render_basic_info(self):
        """Alapadatok kártya"""
        product = self.offer.get('product_type', {})
        product_name = html.escape(str(product.get('name', 'N/A')))
        category_name = html.escape(str(product.get('category', {}).get('name', 'N/A')))
        
        offer_html = f"""
        <div class="dark-card">
            <h3 class="section-title">📋 Ajánlat adatai</h3>
            
            <div class="info-row">
                <span class="info-label">Azonosító:</span>
                <span class="info-value">{self.offer.get('id', 'N/A')}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Beszállítás:</span>
                <span class="info-value">{self._format_date(self.offer.get('delivery_date'))}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Termék:</span>
                <span class="info-value">{product_name}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Kategória:</span>
                <span class="info-value">{category_name}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Minőség:</span>
                <span class="info-value">{self.offer.get('quality_class', 'I. Osztály')}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Mennyiség:</span>
                <span class="info-value highlight-value">{self._format_quantity(self.offer.get('quantity_in_kg'))} kg</span>
            </div>
        </div>
        """
        
        render_html(offer_html, height=350)
    
    def render_pricing_info(self):
        """Árazási információk with enhanced visualization"""
        quantity = self._to_float(self.offer.get('quantity_in_kg', 0))
        price = self._to_float(self.offer.get('price', 0))
        total = quantity * price if quantity and price else 0
        
        confirmed_quantity = self._to_float(self.offer.get('confirmed_quantity', quantity))
        confirmed_price = self._to_float(self.offer.get('confirmed_price', price))
        confirmed_total = confirmed_quantity * confirmed_price if confirmed_quantity and confirmed_price else total
        
        # Progress számítás
        progress = min(100, (confirmed_quantity / quantity * 100) if quantity > 0 else 0)
        
        pricing_html = f"""
        <div class="dark-card">
            <h3 class="section-title">💰 Visszaigazolás</h3>
            
            <div class="info-row">
                <span class="info-label">Eredeti mennyiség:</span>
                <span class="info-value">{self._format_quantity(quantity)} kg</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Visszaigazolt mennyiség:</span>
                <span class="info-value highlight-value">{self._format_quantity(confirmed_quantity)} kg</span>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar" style="width: {progress:.1f}%"></div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Visszaigazolt ár:</span>
                <span class="info-value">{self._format_price(confirmed_price)}/kg</span>
            </div>
            
            <div class="info-row" style="border-top: 2px solid var(--border-color); margin-top: 1rem; padding-top: 1rem;">
                <span class="info-label" style="font-weight: 600;">Összérték:</span>
                <span class="info-value highlight-value" style="font-size: 1.25rem;">
                    {self._format_price(confirmed_total)}
                </span>
            </div>
        </div>
        """
        
        render_html(pricing_html, height=300)
        
        # Add pricing comparison chart
        st.markdown("#### 💹 Ár összehasonlítás")
        
        fig = go.Figure()
        
        # Add bars for original and confirmed values
        fig.add_trace(go.Bar(
            name='Eredeti',
            x=['Mennyiség (kg)', 'Egységár (Ft/kg)', 'Összérték (Ft)'],
            y=[quantity, price, total],
            marker_color='rgba(0, 153, 224, 0.8)',
            text=[f'{quantity:,.0f}', f'{price:,.0f}', f'{total:,.0f}'],
            textposition='outside'
        ))
        
        fig.add_trace(go.Bar(
            name='Visszaigazolt',
            x=['Mennyiség (kg)', 'Egységár (Ft/kg)', 'Összérték (Ft)'],
            y=[confirmed_quantity, confirmed_price, confirmed_total],
            marker_color='rgba(16, 220, 96, 0.8)',
            text=[f'{confirmed_quantity:,.0f}', f'{confirmed_price:,.0f}', f'{confirmed_total:,.0f}'],
            textposition='outside'
        ))
        
        fig.update_layout(
            height=250,
            margin=dict(l=0, r=0, t=0, b=0),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='#c3c9d0'),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            xaxis=dict(
                showgrid=False,
                tickfont=dict(size=10)
            ),
            yaxis=dict(
                showgrid=True,
                gridcolor='rgba(255,255,255,0.1)',
                tickformat=',',
                tickfont=dict(size=10)
            ),
            bargap=0.3
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    def render_delivery_info(self):
        """Szállítási információk"""
        delivery_date = self.offer.get('delivery_date')
        delivery_status = self._get_delivery_status(delivery_date)
        
        delivery_html = f"""
        <div class="dark-card delivery-card">
            <h3 class="section-title">🚚 Szállítási információk</h3>
            
            <div style="text-align: center; padding: 1rem 0;">
                <div class="delivery-icon">📦</div>
                <div style="color: var(--text-primary); font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem;">
                    {self._format_date(delivery_date)}
                </div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">
                    {delivery_status}
                </div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Szállítási cím:</span>
                <span class="info-value">{self.offer.get('delivery_address', 'Központi raktár')}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Szállítási mód:</span>
                <span class="info-value">{self.offer.get('delivery_method', 'Saját szállítás')}</span>
            </div>
        </div>
        """
        
        render_html(delivery_html, height=280)
    
    def render_timeline(self):
        """Állapotváltozási napló"""
        timeline_html = """
        <div class="dark-card">
            <h3 class="section-title">📅 Állapotváltozási napló</h3>
            
            <div class="timeline-container">
        """
        
        # Timeline események
        events = self._get_timeline_events()
        
        for event in events:
            if event.get('date'):
                timeline_html += f"""
                <div class="timeline-event">
                    <div class="timeline-dot" style="background: {event.get('color', 'var(--status-yellow)')};"></div>
                    <div class="timeline-content">
                        <div class="timeline-title">{html.escape(event.get('status', ''))}</div>
                        <div class="timeline-meta">
                            {self._format_datetime(event['date'])} • {html.escape(event.get('user', 'Rendszer'))}
                        </div>
                    </div>
                </div>
                """
        
        timeline_html += """
            </div>
        </div>
        """
        
        render_html(timeline_html, height=350)
    
    def render_producer_info(self):
        """Termelő információk"""
        producer = self.offer.get('user', {})
        producer_name = html.escape(str(producer.get('contact_name', 'N/A')))
        company_name = html.escape(str(producer.get('company_name', 'N/A')))
        email = html.escape(str(producer.get('email', '')))
        phone = html.escape(str(producer.get('phone', '')))
        
        producer_html = f"""
        <div class="dark-card">
            <h3 class="section-title">👤 Termelő adatai</h3>
            
            <div class="producer-avatar">
                {producer_name[0].upper() if producer_name and producer_name != 'N/A' else '?'}
            </div>
            
            <div style="text-align: center; margin-bottom: 1.5rem;">
                <div style="color: var(--text-primary); font-weight: 600; font-size: 1.125rem;">
                    {producer_name}
                </div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">
                    {company_name}
                </div>
            </div>
            
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">
                    <a href="mailto:{email}" style="color: var(--primary); text-decoration: none;">
                        {email if email else 'N/A'}
                    </a>
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Telefon:</span>
                <span class="info-value">
                    <a href="tel:{phone}" style="color: var(--primary); text-decoration: none;">
                        {phone if phone else 'N/A'}
                    </a>
                </span>
            </div>
        </div>
        """
        
        render_html(producer_html, height=350)
    
    def render_status_history(self):
        """Enhanced status progression visualization"""
        st.markdown("#### 📊 Státusz előzmények")
        
        # Create an enhanced status progression chart
        fig = go.Figure()
        
        statuses = ['Létrehozva', 'Visszaigazolva', 'Elfogadva', 'Véglegesítve']
        current_status_index = self._get_current_status_index()
        
        # Create gradient colors for completed statuses
        colors = []
        for i in range(len(statuses)):
            if i <= current_status_index:
                # Different vibrant colors for each completed status
                if i == 0:
                    colors.append('rgba(255, 206, 0, 0.8)')  # Yellow
                elif i == 1:
                    colors.append('rgba(255, 140, 26, 0.8)')  # Orange
                elif i == 2:
                    colors.append('rgba(16, 220, 96, 0.8)')  # Green
                else:
                    colors.append('rgba(0, 153, 224, 0.8)')  # Blue
            else:
                colors.append('rgba(42, 49, 66, 0.5)')  # Gray for pending
        
        # Add progress bar trace
        fig.add_trace(go.Bar(
            x=statuses,
            y=[1, 1, 1, 1],
            marker=dict(
                color=colors,
                line=dict(
                    color='rgba(255, 255, 255, 0.3)',
                    width=2
                )
            ),
            text=['✓' if i <= current_status_index else '○' for i in range(len(statuses))],
            textposition='inside',
            textfont=dict(size=24, color='white'),
            hovertemplate='<b>%{x}</b><br>' +
                         'Státusz: %{text}<br>' +
                         '<extra></extra>',
            width=0.7
        ))
        
        # Add connection lines
        for i in range(len(statuses) - 1):
            line_color = 'rgba(255, 255, 255, 0.3)' if i < current_status_index else 'rgba(255, 255, 255, 0.1)'
            fig.add_shape(
                type="line",
                x0=i + 0.35, y0=0.5,
                x1=i + 0.65, y1=0.5,
                line=dict(
                    color=line_color,
                    width=2,
                    dash="dot"
                )
            )
        
        fig.update_layout(
            height=220,
            margin=dict(l=0, r=0, t=20, b=0),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            showlegend=False,
            xaxis=dict(
                showgrid=False,
                showticklabels=True,
                tickfont=dict(color='#c3c9d0', size=11),
                fixedrange=True
            ),
            yaxis=dict(
                showgrid=False,
                showticklabels=False,
                range=[0, 1.3],
                fixedrange=True
            ),
            bargap=0.2,
            hoverlabel=dict(
                bgcolor="rgba(26, 29, 35, 0.9)",
                font_size=12,
                font_family="sans-serif"
            )
        )
        
        st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    # Helper methods
    def _to_float(self, value):
        """Convert value to float safely"""
        try:
            if value is None:
                return 0.0
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
                return float(cleaned) if cleaned else 0.0
            return 0.0
        except (ValueError, TypeError):
            return 0.0
    
    def _get_status_display(self, status):
        """Get display text for status"""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Cég által visszaigazolva',
            'ACCEPTED_BY_USER': 'Termelő által elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)
    
    def _format_quantity(self, value):
        """Format quantity value"""
        try:
            numeric_value = self._to_float(value)
            return f"{numeric_value:,.0f}" if numeric_value else "0"
        except:
            return "0"
    
    def _format_price(self, value):
        """Format price value"""
        try:
            numeric_value = self._to_float(value)
            return f"{numeric_value:,.0f} Ft" if numeric_value else "0 Ft"
        except:
            return "0 Ft"
    
    def _format_date(self, value):
        """Format date value"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d.")
            return value.strftime("%Y. %m. %d.")
        except:
            return str(value) if value else "-"
    
    def _format_datetime(self, value):
        """Format datetime value"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d. %H:%M")
            return value.strftime("%Y. %m. %d. %H:%M")
        except:
            return str(value) if value else "-"
    
    def _get_delivery_status(self, delivery_date):
        """Get delivery status text"""
        if not delivery_date:
            return "Nincs megadva"
        
        try:
            if isinstance(delivery_date, str):
                delivery_dt = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
            else:
                delivery_dt = delivery_date
            
            days_until = (delivery_dt - datetime.now()).days
            
            if days_until < 0:
                return f"Lejárt ({abs(days_until)} napja)"
            elif days_until == 0:
                return "Ma esedékes"
            elif days_until == 1:
                return "Holnap esedékes"
            else:
                return f"{days_until} nap múlva esedékes"
        except:
            return "Nincs megadva"
    
    def _get_timeline_events(self):
        """Get timeline events"""
        events = []
        status = self.offer.get('status')
        
        # Created event
        if self.offer.get('created_at'):
            events.append({
                'date': self.offer.get('created_at'),
                'status': 'Létrehozva',
                'user': self.offer.get('creator', {}).get('name', 'Rendszer'),
                'color': 'var(--status-yellow)'
            })
        
        # Confirmed event
        if status in ['CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']:
            events.append({
                'date': self.offer.get('confirmed_at', self.offer.get('updated_at')),
                'status': 'Cég által visszaigazolva',
                'user': 'Rendszer',
                'color': 'var(--status-orange)'
            })
        
        # Accepted/Rejected event
        if status == 'ACCEPTED_BY_USER':
            events.append({
                'date': self.offer.get('accepted_at', self.offer.get('updated_at')),
                'status': 'Termelő által elfogadva',
                'user': self.offer.get('user', {}).get('contact_name', 'N/A'),
                'color': 'var(--status-green)'
            })
        elif status == 'REJECTED_BY_USER':
            events.append({
                'date': self.offer.get('rejected_at', self.offer.get('updated_at')),
                'status': 'Termelő által elutasítva',
                'user': self.offer.get('user', {}).get('contact_name', 'N/A'),
                'color': 'var(--status-red)'
            })
        
        # Finalized event
        if status == 'FINALIZED':
            events.append({
                'date': self.offer.get('finalized_at', self.offer.get('updated_at')),
                'status': 'Véglegesítve',
                'user': 'Rendszer',
                'color': 'var(--status-blue)'
            })
        
        return events
    
    def _get_current_status_index(self):
        """Get current status index for progress visualization"""
        status = self.offer.get('status')
        status_map = {
            'CREATED': 0,
            'CONFIRMED_BY_COMPANY': 1,
            'ACCEPTED_BY_USER': 2,
            'FINALIZED': 3
        }
        return status_map.get(status, 0)