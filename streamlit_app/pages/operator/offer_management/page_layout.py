"""
Responsive Page Layout Manager for Offer Management
Implements the page layout specifications from UI_IMPLEMENTATION_PLAN.md
"""
import streamlit as st
from typing import Dict, Any, List, Optional, Callable
import logging
from datetime import datetime, date, timedelta

# Import our modern components
try:
    from .modern_components import (
        ModernFilterPanel, 
        EnhancedOfferStatsCard, 
        EnhancedOfferCard,
        LoadingSpinner
    )
    from .responsive_ui import inject_screen_detection
    from .api_client import get_offers, get_producers, get_products
    from .data_processing import prepare_filter_params
except ImportError:
    # Fallback imports for development
    try:
        from modern_components import (
            ModernFilterPanel, 
            EnhancedOfferStatsCard, 
            EnhancedOfferCard,
            LoadingSpinner
        )
        from responsive_ui import inject_screen_detection
        from api_client import get_offers, get_producers, get_products
        from data_processing import prepare_filter_params
    except ImportError:
        # Create dummy fallbacks
        def inject_screen_detection(): pass
        def get_offers(*args, **kwargs): return True, []
        def get_producers(*args, **kwargs): return True, []
        def get_products(*args, **kwargs): return True, []
        def prepare_filter_params(*args, **kwargs): return {}
        
        class ModernFilterPanel:
            def __init__(self, **kwargs): pass
            def render(self, **kwargs): st.info("ModernFilterPanel not available")
        
        class EnhancedOfferStatsCard:
            def __init__(self, **kwargs): pass
            def render(self, **kwargs): st.info("EnhancedOfferStatsCard not available")
            
        class EnhancedOfferCard:
            def __init__(self, **kwargs): pass
            def render(self, **kwargs): st.info("EnhancedOfferCard not available")
            
        class LoadingSpinner:
            def __init__(self, **kwargs): pass
            def render(self, **kwargs): st.info("Loading...")

logger = logging.getLogger(__name__)

class ResponsivePageLayout:
    """
    Responsive page layout manager implementing the design specifications.
    Handles desktop and mobile layouts as defined in UI_IMPLEMENTATION_PLAN.md
    """
    
    def __init__(self):
        self.component_id = "responsive_page_layout"
        self.initialize_state()
        
    def initialize_state(self):
        """Initialize session state for the page layout."""
        if 'page_layout_initialized' not in st.session_state:
            st.session_state.page_layout_initialized = True
            st.session_state.current_filters = {}
            st.session_state.offers_data = []
            st.session_state.stats_data = {}
            st.session_state.loading_offers = False
            st.session_state.loading_stats = False
            st.session_state.available_producers = []
            st.session_state.available_products = []
            st.session_state.view_mode = 'auto'  # auto, desktop, mobile
            
    def render(self):
        """Render the complete page layout."""
        # Inject screen detection
        inject_screen_detection()
        
        # Inject performance optimizations and accessibility
        try:
            from .performance_optimization import (
                performance_monitor, 
                render_performance_debug_panel
            )
            from .keyboard_accessibility import (
                keyboard_manager,
                accessibility_manager
            )
            
            # Initialize performance monitoring
            performance_monitor.measure_component_render('ResponsivePageLayout')
            
            # Inject keyboard shortcuts and accessibility
            keyboard_manager.inject_keyboard_shortcuts()
            accessibility_manager.inject_accessibility_features()
            
            # Show performance debug panel if enabled
            if st.session_state.get('debug_mode', False):
                render_performance_debug_panel()
                
        except ImportError as e:
            logger.warning(f"Performance/accessibility features not available: {e}")
        
        # Get screen information
        is_mobile = st.session_state.get('is_mobile', False)
        is_tablet = st.session_state.get('is_tablet', False)
        screen_width = st.session_state.get('screen_width', 1200)
        
        # Determine view mode
        if st.session_state.view_mode == 'auto':
            if is_mobile:
                current_view_mode = 'mobile'
            elif is_tablet:
                current_view_mode = 'tablet'
            else:
                current_view_mode = 'desktop'
        else:
            current_view_mode = st.session_state.view_mode
            
        logger.info(f"Rendering layout: {current_view_mode}, screen_width: {screen_width}px")
        
        # Render based on view mode
        if current_view_mode == 'mobile':
            self._render_mobile_layout()
        elif current_view_mode == 'tablet':
            self._render_tablet_layout()
        else:
            self._render_desktop_layout()
            
    def _render_desktop_layout(self):
        """Render desktop layout as per design specification."""
        st.markdown("""
        <style>
        .desktop-layout {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
        }
        .desktop-header {
            margin-bottom: 2rem;
        }
        .desktop-main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        .desktop-offer-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1.5rem 0 1rem 0;
        }
        .desktop-view-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        .view-control-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .view-control-btn.active {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }
        .view-control-btn:hover {
            background: #f8f9fa;
        }
        .view-control-btn.active:hover {
            background: #1557b0;
        }
        </style>
        """, unsafe_allow_html=True)
        
        st.markdown('<div class="desktop-layout">', unsafe_allow_html=True)
        
        # Header
        st.markdown('<div class="desktop-header">', unsafe_allow_html=True)
        col1, col2 = st.columns([3, 1])
        with col1:
            st.title("🔍 Ajánlatok kezelése")
        with col2:
            if st.button("🔄 Frissítés", help="Adatok frissítése"):
                st.rerun()
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Main content
        st.markdown('<div class="desktop-main-content">', unsafe_allow_html=True)
        
        # Filter Panel
        self._render_filter_panel()
        
        # Statistics Card  
        self._render_statistics_card()
        
        # Offer List Header with Controls
        self._render_offer_list_header()
        
        # Offer List
        self._render_offer_list('list')
        
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_tablet_layout(self):
        """Render tablet layout (hybrid approach)."""
        st.markdown("""
        <style>
        .tablet-layout {
            max-width: 1024px;
            margin: 0 auto;
            padding: 1rem;
        }
        .tablet-header {
            margin-bottom: 1.5rem;
        }
        .tablet-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        </style>
        """, unsafe_allow_html=True)
        
        st.markdown('<div class="tablet-layout">', unsafe_allow_html=True)
        
        # Compact header
        st.markdown('<div class="tablet-header">', unsafe_allow_html=True)
        st.title("🔍 Ajánlatok kezelése")
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Content
        st.markdown('<div class="tablet-content">', unsafe_allow_html=True)
        
        # Collapsible filter panel
        self._render_filter_panel(initially_collapsed=True)
        
        # Compact statistics
        self._render_statistics_card(compact=True)
        
        # Offer list in card mode
        self._render_offer_list('card')
        
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_mobile_layout(self):
        """Render mobile layout as per design specification."""
        st.markdown("""
        <style>
        .mobile-layout {
            padding: 0.5rem;
        }
        .mobile-header {
            text-align: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            margin: 0 -0.5rem 1rem -0.5rem;
        }
        .mobile-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        .mobile-filters-collapsed {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            border: 1px solid #e1e5e9;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .mobile-active-count {
            background: #ff5722;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        </style>
        """, unsafe_allow_html=True)
        
        st.markdown('<div class="mobile-layout">', unsafe_allow_html=True)
        
        # Mobile header
        st.markdown('''
        <div class="mobile-header">
            <h2 style="margin: 0; font-size: 1.25rem;">Ajánlatok kezelése</h2>
        </div>
        ''', unsafe_allow_html=True)
        
        # Content
        st.markdown('<div class="mobile-content">', unsafe_allow_html=True)
        
        # Collapsible filters indicator
        active_filter_count = len([v for v in st.session_state.current_filters.values() 
                                 if v is not None and v != "" and v != []])
        
        with st.expander(f"🔍 Szűrők ({active_filter_count} aktív)" if active_filter_count > 0 else "🔍 Szűrők", 
                        expanded=False):
            self._render_filter_panel(mobile_optimized=True)
        
        # Compact statistics
        self._render_statistics_card(compact=True)
        
        # Mobile offer list
        st.markdown("### 📋 Ajánlatok")
        self._render_offer_list('card')
        
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_filter_panel(self, initially_collapsed=False, mobile_optimized=False):
        """Render the filter panel component."""
        try:
            # Load available options if not already loaded
            if not st.session_state.available_producers:
                self._load_filter_options()
            
            filter_panel = ModernFilterPanel(component_id="main_filter_panel")
            filter_panel.render(
                title="🔍 Ajánlatok szűrése",
                filters=st.session_state.current_filters,
                available_producers=st.session_state.available_producers,
                available_products=st.session_state.available_products,
                on_filter_change=self._handle_filter_change
            )
            
        except Exception as e:
            logger.error(f"Error rendering filter panel: {e}")
            st.error("Szűrőpanel betöltési hiba")
            
    def _render_statistics_card(self, compact=False):
        """Render the statistics card component."""
        try:
            # Load statistics if needed
            if not st.session_state.stats_data or st.session_state.get('stats_needs_update', True):
                self._load_statistics()
            
            stats_card = EnhancedOfferStatsCard(component_id="main_stats_card")
            stats_card.render(
                title="📊 Statisztika",
                stats_data=st.session_state.stats_data,
                loading=st.session_state.loading_stats
            )
            
        except Exception as e:
            logger.error(f"Error rendering statistics card: {e}")
            st.error("Statisztika betöltési hiba")
            
    def _render_offer_list_header(self):
        """Render offer list header with view controls."""
        st.markdown('<div class="desktop-offer-list-header">', unsafe_allow_html=True)
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            offer_count = len(st.session_state.offers_data)
            st.markdown(f"### 📋 Ajánlatok listája ({offer_count} találat)")
            
        with col2:
            # View mode controls
            st.markdown('<div class="desktop-view-controls">', unsafe_allow_html=True)
            
            view_modes = {
                '📊': 'Statisztika',
                '📅': 'Naptár nézet', 
                '📱': 'Kártya nézet',
                '🗂️': 'Lista nézet'
            }
            
            cols = st.columns(len(view_modes))
            for i, (icon, tooltip) in enumerate(view_modes.items()):
                with cols[i]:
                    if st.button(icon, help=tooltip, key=f"view_mode_{i}"):
                        st.info(f"{tooltip} (fejlesztés alatt)")
                        
            st.markdown('</div>', unsafe_allow_html=True)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_offer_list(self, view_mode='list'):
        """Render the offer list with specified view mode."""
        try:
            # Load offers if needed
            if not st.session_state.offers_data or st.session_state.get('offers_need_update', True):
                self._load_offers()
                
            if st.session_state.loading_offers:
                spinner = LoadingSpinner(component_id="offer_loading")
                spinner.render(message="Ajánlatok betöltése...", size="medium")
                return
                
            offers = st.session_state.offers_data
            
            if not offers:
                st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
                return
                
            # Render offers based on view mode
            for i, offer in enumerate(offers):
                offer_card = EnhancedOfferCard(component_id=f"offer_card_{i}")
                offer_card.render(
                    offer=offer,
                    view_mode=view_mode,
                    on_view=lambda oid=offer.get('id'): self._handle_offer_view(oid),
                    on_edit=lambda oid=offer.get('id'): self._handle_offer_edit(oid),
                    on_status_change=lambda oid=offer.get('id'): self._handle_offer_status_change(oid)
                )
                
        except Exception as e:
            logger.error(f"Error rendering offer list: {e}")
            st.error("Ajánlatok betöltési hiba")
            
    def _load_filter_options(self):
        """Load available options for filters."""
        try:
            # Load producers
            success, producers = get_producers()
            if success:
                st.session_state.available_producers = [p.get('name', '') for p in producers if p.get('name')]
            else:
                st.session_state.available_producers = ["Kovács János", "Szabó Péter", "Nagy István"]
                logger.warning("Failed to load producers, using fallback data")
                
            # Load products
            success, products = get_products()
            if success:
                st.session_state.available_products = [p.get('name', '') for p in products if p.get('name')]
            else:
                st.session_state.available_products = ["Alma", "Szőlő", "Burgonya", "Paradicsom"]
                logger.warning("Failed to load products, using fallback data")
                
        except Exception as e:
            logger.error(f"Error loading filter options: {e}")
            # Use fallback data
            st.session_state.available_producers = ["Kovács János", "Szabó Péter", "Nagy István"]
            st.session_state.available_products = ["Alma", "Szőlő", "Burgonya", "Paradicsom"]
            
    def _load_offers(self):
        """Load offers based on current filters."""
        try:
            st.session_state.loading_offers = True
            st.session_state.offers_need_update = False
            
            # Prepare API parameters
            query_params = prepare_filter_params(
                st.session_state.current_filters.get('producer'),
                st.session_state.current_filters.get('statuses'),
                st.session_state.current_filters.get('from_date'),
                st.session_state.current_filters.get('to_date')
            )
            
            # Add search term if present
            if st.session_state.current_filters.get('search'):
                query_params['search'] = st.session_state.current_filters['search']
                
            # Call API
            success, offers = get_offers(query_params)
            
            if success:
                st.session_state.offers_data = offers
                logger.info(f"Loaded {len(offers)} offers")
                
                # Announce data loaded for accessibility
                try:
                    from .keyboard_accessibility import announce_data_loaded
                    announce_data_loaded(len(offers), "ajánlat")
                except ImportError:
                    pass
                    
            else:
                st.error(f"Hiba az ajánlatok betöltésekor: {offers}")
                st.session_state.offers_data = []
                
        except Exception as e:
            logger.error(f"Error loading offers: {e}")
            st.error("Ajánlatok betöltési hiba")
            st.session_state.offers_data = []
        finally:
            st.session_state.loading_offers = False
            
    def _load_statistics(self):
        """Load statistics based on current offers."""
        try:
            st.session_state.loading_stats = True
            st.session_state.stats_needs_update = False
            
            offers = st.session_state.offers_data
            
            if not offers:
                st.session_state.stats_data = {}
                return
                
            # Calculate statistics
            total_quantity = sum(offer.get('quantity_in_kg', 0) for offer in offers)
            total_value = sum(offer.get('quantity_in_kg', 0) * offer.get('price', 0) for offer in offers)
            average_price = sum(offer.get('price', 0) for offer in offers) / len(offers) if offers else 0
            
            # Status distribution
            status_dist = {}
            for offer in offers:
                status = offer.get('status', 'UNKNOWN')
                status_display = self._get_status_display(status)
                status_dist[status_display] = status_dist.get(status_display, 0) + 1
                
            # Product distribution  
            product_dist = {}
            for offer in offers:
                product = offer.get('product_type', {}).get('name', 'Ismeretlen')
                product_dist[product] = product_dist.get(product, 0) + 1
                
            st.session_state.stats_data = {
                'total_quantity': total_quantity,
                'average_price': average_price,
                'total_value': total_value,
                'status_distribution': status_dist,
                'product_distribution': product_dist
            }
            
        except Exception as e:
            logger.error(f"Error loading statistics: {e}")
            st.session_state.stats_data = {}
        finally:
            st.session_state.loading_stats = False
            
    def _handle_filter_change(self, new_filters: Dict):
        """Handle filter changes."""
        logger.info(f"Filter changed: {new_filters}")
        
        # Announce filter change for accessibility
        try:
            from .keyboard_accessibility import announce_filter_change
            changed_filters = []
            for key, value in new_filters.items():
                if st.session_state.current_filters.get(key) != value:
                    changed_filters.append(f"{key}: {value}")
            
            if changed_filters:
                announce_filter_change("Szűrők", ", ".join(changed_filters))
        except ImportError:
            pass
        
        st.session_state.current_filters = new_filters
        st.session_state.offers_need_update = True
        st.session_state.stats_needs_update = True
        st.rerun()
        
    def _handle_offer_view(self, offer_id):
        """Handle offer view action."""
        logger.info(f"View offer: {offer_id}")
        st.session_state.selected_offer_id = offer_id
        st.rerun()
        
    def _handle_offer_edit(self, offer_id):
        """Handle offer edit action."""
        logger.info(f"Edit offer: {offer_id}")
        st.info(f"Ajánlat #{offer_id} szerkesztése (fejlesztés alatt)")
        
    def _handle_offer_status_change(self, offer_id):
        """Handle offer status change action."""
        logger.info(f"Change status for offer: {offer_id}")
        st.info(f"Ajánlat #{offer_id} státusz módosítása (fejlesztés alatt)")
        
    def _get_status_display(self, status: str) -> str:
        """Get human-readable status display."""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)

# Factory function for easy usage
def create_responsive_page_layout():
    """Create and render responsive page layout."""
    layout = ResponsivePageLayout()
    layout.render()