"""
<PERSON><PERSON><PERSON>lat részletek megjelenítése.
Az új komponenseket használó ajánlat részletező oldal.
"""
import streamlit as st
import streamlit.components.v1 as components
import logging
from datetime import datetime, timedelta

# Rugalmas importálás - először megpróbáljuk a Docker-kompatibilis útvonalról importálni
try:
    # Docker környezetben /app a gyökér, nem /streamlit_app
    from pages.operator.offer_management.detail_components import (
        DetailContainer, 
        StatusIndicator, 
        EntityCard, 
        Timeline, 
        ActivityLog
    )
    from pages.operator.offer_management.action_components import (
        ActionBar,
        StatusTransitionModal,
        ConfirmationModal
    )
except ImportError:
    # Ha nem működik, próbáljuk meg közvetlenül importálni
    try:
        from detail_components import (
            DetailContainer, 
            StatusIndicator, 
            EntityCard, 
            Timeline, 
            ActivityLog
        )
        from action_components import (
            ActionBar,
            StatusTransitionModal,
            ConfirmationModal
        )
    except ImportError:
        # Ha még mindig nem mű<PERSON>ödik, használunk relatív importot
        logging.warning("Using fallback relative imports for detail_components and action_components")
        from .detail_components import (
            DetailContainer, 
            StatusIndicator, 
            EntityCard, 
            Timeline, 
            ActivityLog
        )
        from .action_components import (
            ActionBar,
            StatusTransitionModal,
            ConfirmationModal
        )

# Rugalmas importálás a API klienshez is - Docker-kompatibilis sorrendben
try:
    # Docker környezetben ez a helyes út
    from pages.operator.offer_management.api_client import (
        get_offer_details,
        get_offer_logs,
        update_offer_status,
        get_offer_attachments,
        get_related_offers
    )
    logging.info("Successfully imported API client from Docker path (pages)")
except ImportError:
    try:
        # Közvetlenül az aktuális könyvtárból
        from api_client import (
            get_offer_details,
            get_offer_logs,
            update_offer_status,
            get_offer_attachments,
            get_related_offers
        )
        logging.info("Successfully imported API client directly")
    except ImportError:
        try:
            # Streamlit app relatív útvonal (fejlesztői környezetben)
            from streamlit_app.pages.operator.offer_management.api_client import (
                get_offer_details,
                get_offer_logs,
                update_offer_status,
                get_offer_attachments,
                get_related_offers
            )
            logging.info("Successfully imported API client from streamlit_app path")
        except ImportError:
            try:
                # Relatív import, ha csomagként van használva
                from .api_client import (
                    get_offer_details,
                    get_offer_logs,
                    update_offer_status,
                    get_offer_attachments,
                    get_related_offers
                )
                logging.info("Successfully imported API client using relative import")
            except ImportError:
                # Végső fallback: sys.path manipuláció
                logging.warning("Using sys.path manipulation for api_client import")
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.append(current_dir)
                from api_client import (
                    get_offer_details,
                    get_offer_logs,
                    update_offer_status,
                    get_offer_attachments,
                    get_related_offers
                )

# Formázó függvények importálása - Docker-kompatibilis sorrendben
try:
    # Docker környezetben ez a helyes út
    from pages.utils.formatting import (
        format_status, 
        format_datetime, 
        format_date, 
        format_price, 
        format_quantity
    )
except ImportError:
    try:
        # Közvetlenül
        from utils.formatting import (
            format_status, 
            format_datetime, 
            format_date, 
            format_price, 
            format_quantity
        )
    except ImportError:
        try:
            # Streamlit app relatív útvonal
            from streamlit_app.utils.formatting import (
                format_status, 
                format_datetime, 
                format_date, 
                format_price, 
                format_quantity
            )
        except ImportError:
            # Ha nem találjuk a formázó függvényeket, alapértelmezett implementáció
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

logger = logging.getLogger(__name__)

def show_offer_detail(offer_id):
    """Debug verzió - HTML rendering problémák diagnosztizálása"""
    logger.info(f"Ajánlat részletek megjelenítése: #{offer_id}")
    
    # DEBUG MODE
    if st.checkbox("🔍 Debug HTML Rendering"):
        st.write("**Offer ID:**", offer_id)
        
        # Test HTML rendering
        st.markdown("**Test HTML rendering:**")
        st.write("Test 1: HTML enabled (should be red):")
        st.markdown("<div style='color: red;'>This should be red text</div>", unsafe_allow_html=True)
        st.write("Test 2: HTML disabled (should show tags):")
        st.markdown("<div style='color: red;'>This should be red text</div>", unsafe_allow_html=False)
        
        st.markdown("---")
    
    # Debug mode check
    debug_mode = st.sidebar.checkbox("🔍 Debug Mode", value=False, key="offer_detail_debug")
    if debug_mode:
        st.sidebar.markdown("---")
        st.sidebar.write("**Debug Info:**")
        st.sidebar.write(f"Offer ID: {offer_id}")
        st.sidebar.write(f"Session keys: {list(st.session_state.keys())}")
    
    # Azonnali vissza gombot adunk a legfelső sorba, hogy garantáltan működjön
    # Ez egy extra navigációs lehetőség a fejlécben lévő vissza gomb mellett
    st.markdown("### Ajánlat részletek")
    if st.button("← Vissza az ajánlatok listájához", key="global_back_button"):
        logger.info("Global back button clicked")
        # Beállítjuk a vissza navigációs jelzőt
        st.session_state["go_back_to_offer_list"] = True
        
        # Töröljük a kiválasztott ajánlat azonosítóját
        if "selected_offer_id" in st.session_state:
            logger.info(f"Clearing selected offer ID: {offer_id} from global back button")
            del st.session_state["selected_offer_id"]
            
        # Újratöltjük az oldalt
        st.rerun()
    
    # CSS stílusok injektálása
    try:
        # Importáljuk a html_rendering modult, ha lehetséges
        try:
            from .html_rendering import inject_styles
        except ImportError:
            try:
                from html_rendering import inject_styles
            except ImportError:
                # Fallback megoldás, ha nem sikerül importálni
                def inject_styles():
                    st.markdown("""
                    <style>
                    .sticky-action-bar {
                        position: sticky;
                        top: 0;
                        z-index: 999;
                        background-color: white;
                        padding: 10px 0;
                        border-bottom: 1px solid #e6e6e6;
                        margin-bottom: 20px;
                    }
                    </style>
                    """, unsafe_allow_html=True)
                    
        # Stílusok injektálása
        inject_styles()
    except Exception as e:
        logger.warning(f"Nem sikerült injektálni a stílusokat: {str(e)}")
    
    # Mobilnézet ellenőrzése
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    
    # Ajánlat adatok betöltése
    offer = _load_offer_details(offer_id)
    
    # Debug info for API response
    if debug_mode and offer:
        with st.sidebar.expander("🔍 API Response Debug"):
            st.json(offer)
            st.write("**Available keys:**", list(offer.keys()))
            if 'user' in offer:
                st.write("**User keys:**", list(offer['user'].keys()))
            if 'product_type' in offer:
                st.write("**Product_type keys:**", list(offer['product_type'].keys()))
    
    if not offer:
        st.error(f"Az ajánlat (#{offer_id}) nem található vagy hiba történt a betöltés során.")
        if st.button("Vissza"):
            if "selected_offer_id" in st.session_state:
                del st.session_state.selected_offer_id
            st.rerun()
        return
    
    # Inicializáljuk a session state-et a betöltési állapotokhoz
    for key in ['loading_logs', 'loading_attachments', 'loading_related']:
        if key not in st.session_state:
            st.session_state[key] = False
    
    # Kezelés, ha státuszváltást kértek
    _handle_status_change(offer_id, offer['status'])
    
    # Műveleti sáv megjelenítése - pass the offer object
    _render_action_bar(offer_id, offer['status'], offer)
    
    # Ajánlat fejléc információk megjelenítése
    _render_offer_header(offer)
    
    # Check if confirmation dialog should be shown
    if st.session_state.get(f"show_confirmation_dialog_{offer_id}", False):
        # Import stable confirmation dialog
        try:
            from .stable_confirmation_dialog import render_inline_confirmation_form
        except ImportError:
            try:
                from stable_confirmation_dialog import render_inline_confirmation_form
            except ImportError:
                # Fallback to original confirmation dialog
                try:
                    from .confirmation_dialog import render_confirmation_dialog as render_inline_confirmation_form
                except ImportError:
                    try:
                        from confirmation_dialog import render_confirmation_dialog as render_inline_confirmation_form
                    except ImportError:
                        st.error("❌ Nem sikerült betölteni a visszaigazolási dialógust")
                        del st.session_state[f"show_confirmation_dialog_{offer_id}"]
                        render_inline_confirmation_form = None
        
        if render_inline_confirmation_form:
            # Create a dedicated container for the confirmation dialog
            with st.container():
                st.markdown("---")
                # Render the stable inline confirmation form
                confirmed, quantity, price = render_inline_confirmation_form(offer, offer_id)
                
                if confirmed:
                    # Handle the confirmation
                    logger.info(f"Offer {offer_id} confirmed with quantity={quantity}, price={price}")
                    
                    # Prepare confirmation data
                    confirmation_data = {
                        "confirmed_quantity": quantity,
                        "confirmed_price": price
                    }
                    
                    # Get note if any
                    note = st.session_state.get(f"confirmation_note_{offer_id}", "")
                    if note:
                        confirmation_data["note"] = note
                        # Clean up note from session state
                        keys_to_clean = [k for k in st.session_state.keys() if k.startswith(f"confirmation_note_{offer_id}") or k.startswith(f"inline_") and str(offer_id) in k]
                        for key in keys_to_clean:
                            del st.session_state[key]
                    
                    # Update offer status
                    success, result = update_offer_status(offer_id, "CONFIRMED_BY_COMPANY", confirmation_data)
                    
                    if success:
                        st.success(f"✅ Ajánlat sikeresen visszaigazolva! Mennyiség: {quantity:,.0f} kg, Ár: {price:,.0f} Ft/kg")
                        # Clear dialog state
                        del st.session_state[f"show_confirmation_dialog_{offer_id}"]
                        # Rerun to refresh
                        st.rerun()
                    else:
                        st.error(f"❌ Hiba a visszaigazolás során: {result}")
                elif confirmed is False:
                    # User cancelled
                    logger.info("Confirmation cancelled by user")
                    st.info("ℹ️ Visszaigazolás megszakítva")
                    # Clear dialog state and any inline states
                    keys_to_clean = [k for k in st.session_state.keys() if (k.startswith(f"show_confirmation_dialog_{offer_id}") or k.startswith(f"inline_") and str(offer_id) in k)]
                    for key in keys_to_clean:
                        del st.session_state[key]
                    st.rerun()
                
                st.markdown("---")
            # Don't show other render modes when confirmation is active
            return
    
    # Rendering opciók a sidebar-ban
    st.sidebar.markdown("### 🎨 Rendering Options")
    
    render_mode = st.sidebar.radio(
        "Megjelenítési mód:",
        ["Simplified Enhanced Dark", "Working Dark Theme Final", "Enhanced Dark Theme", "Minimal Dark Theme", "Dark Theme UI", "Modern UI", "Nutrition Facts Style", "Native Streamlit", "DetailContainer", "Unified Actions"],
        index=0,  # Simplified Enhanced Dark alapértelmezett
        key="render_mode_selection"
    )
    
    if render_mode == "Simplified Enhanced Dark":
        # Simplified Enhanced Dark Theme megjelenítés - garantáltan működő verzió
        try:
            from .simplified_enhanced_dark_theme import render_simplified_enhanced_dark_theme_offer
        except ImportError:
            try:
                from simplified_enhanced_dark_theme import render_simplified_enhanced_dark_theme_offer
            except ImportError:
                st.error("❌ Simplified Enhanced Dark Theme komponens nem elérhető")
                render_simplified_enhanced_dark_theme_offer = None
        
        if render_simplified_enhanced_dark_theme_offer:
            st.info("🎨 Simplified Enhanced Dark - Garantáltan működő enhanced theme working patterns alapján")
            render_simplified_enhanced_dark_theme_offer(offer)
        else:
            st.error("❌ Nem sikerült betölteni a Simplified Enhanced Dark Theme komponenst")
            render_mode = "Working Dark Theme Final"  # Fallback

    elif render_mode == "Working Dark Theme Final":
        # Working Dark Theme Final megjelenítés - végleges működő verzió
        try:
            from .working_dark_theme_final import WorkingDarkThemeOffer
        except ImportError:
            try:
                from working_dark_theme_final import WorkingDarkThemeOffer
            except ImportError:
                st.error("❌ Working Dark Theme Final komponens nem elérhető")
                WorkingDarkThemeOffer = None
        
        if WorkingDarkThemeOffer:
            st.info("🌙 Working Dark Theme Final - Végleges működő sötét téma st.html() használatával")
            dark_view = WorkingDarkThemeOffer(offer)
            dark_view.render()
        else:
            st.error("❌ Nem sikerült betölteni a Working Dark Theme Final komponenst")
            render_mode = "Enhanced Dark Theme"  # Fallback

    elif render_mode == "Enhanced Dark Theme":
        # Enhanced Dark Theme megjelenítés
        try:
            from .enhanced_minimal_dark_theme import render_enhanced_dark_theme_offer
        except ImportError:
            try:
                from enhanced_minimal_dark_theme import render_enhanced_dark_theme_offer
            except ImportError:
                st.error("❌ Enhanced Dark Theme komponens nem elérhető")
                render_enhanced_dark_theme_offer = None
        
        if render_enhanced_dark_theme_offer:
            st.info("⚡ Enhanced Dark Theme enabled - Best practices implementáció")
            # Enable notification for demo
            st.session_state.show_notification = True
            render_enhanced_dark_theme_offer(offer)
        else:
            st.error("❌ Nem sikerült betölteni az Enhanced Dark Theme komponenst")
            render_mode = "Minimal Dark Theme"  # Fallback
    
    elif render_mode == "Minimal Dark Theme":
        # Minimal Dark Theme megjelenítés
        try:
            from .minimal_dark_theme import render_dark_theme_offer
        except ImportError:
            try:
                from minimal_dark_theme import render_dark_theme_offer
            except ImportError:
                st.error("❌ Minimal Dark Theme komponens nem elérhető")
                render_dark_theme_offer = None
        
        if render_dark_theme_offer:
            st.info("🌙 Minimal Dark Theme enabled - Egyszerű, tiszta sötét téma")
            render_dark_theme_offer(offer)
        else:
            st.error("❌ Nem sikerült betölteni a Minimal Dark Theme komponenst")
            render_mode = "Dark Theme UI"  # Fallback
    
    elif render_mode == "Dark Theme UI":
        # Dark Theme UI megjelenítés
        try:
            from .modern_dark_theme_detail import DarkThemeOfferDetail
        except ImportError:
            try:
                from modern_dark_theme_detail import DarkThemeOfferDetail
            except ImportError:
                st.error("❌ Dark Theme UI komponens nem elérhető")
                DarkThemeOfferDetail = None
        
        if DarkThemeOfferDetail:
            st.info("🌙 Dark Theme UI enabled - Modern sötét témájú design")
            dark_view = DarkThemeOfferDetail(offer)
            dark_view.render()
        else:
            st.error("❌ Nem sikerült betölteni a Dark Theme UI komponenst")
            render_mode = "Modern UI"  # Fallback
    
    elif render_mode == "Modern UI":
        # Modern UI megjelenítés
        try:
            from .modern_offer_detail_v2 import ModernOfferDetailView
        except ImportError:
            try:
                from modern_offer_detail_v2 import ModernOfferDetailView
            except ImportError:
                st.error("❌ Modern UI komponens nem elérhető")
                ModernOfferDetailView = None
        
        if ModernOfferDetailView:
            st.info("🎨 Modern UI enabled - Vizuálisan gazdag, interaktív design")
            modern_view = ModernOfferDetailView(offer)
            modern_view.render()
            
            # Resizable panels demo
            if st.sidebar.checkbox("Show Resizable Panels Demo", value=False, key="resizable_panels_demo"):
                st.markdown("### 🔧 Resizable Panels Demo")
                try:
                    from .resizable_panels import render_resizable_panel_system
                except ImportError:
                    from resizable_panels import render_resizable_panel_system
                
                render_resizable_panel_system()
        else:
            st.error("❌ Nem sikerült betölteni a Modern UI komponenst")
            render_mode = "Nutrition Facts Style"  # Fallback
    
    elif render_mode == "Nutrition Facts Style":
        # Nutrition Facts stílusú megjelenítés
        if create_offer_facts_panel is not None:
            st.info("🍏 Nutrition Facts Style enabled - Professional label design")
            
            # HTML teszt hozzáadása
            if st.sidebar.checkbox("Test HTML Rendering", value=False, key="test_html_rendering"):
                st.markdown("### 🧪 HTML Rendering Test")
                st.markdown("Plain text: This is normal text")
                st.markdown("<p style='color: red;'>HTML test: This should be red</p>", unsafe_allow_html=True)
                st.markdown("**Result:** Ha a 'HTML test' szöveg piros, akkor működik a HTML rendering!")
                
                # Check st.html availability
                if check_st_html_availability:
                    html_status = check_st_html_availability()
                    st.write("**st.html() availability:**")
                    st.json(html_status)
                st.markdown("---")
            
            if debug_mode:
                st.info("📋 Rendering nutrition facts panels...")
            
            try:
                # Státusz formázás hozzáadása
                if 'status_display' not in offer:
                    status_display_map = {
                        'CREATED': 'Létrehozva',
                        'CONFIRMED_BY_COMPANY': 'Cég által visszaigazolva',
                        'ACCEPTED_BY_USER': 'Elfogadva',
                        'REJECTED_BY_USER': 'Elutasítva',
                        'FINALIZED': 'Véglegesítve'
                    }
                    offer['status_display'] = status_display_map.get(offer.get('status', ''), offer.get('status', ''))
                
                # Nutrition Facts panel az ajánlathoz debug móddal
                create_offer_facts_panel(offer, title="Ajánlat Részletek", icon="📋", debug_mode=debug_mode)
                
                if debug_mode:
                    st.success("✅ Nutrition Facts panels rendered successfully")
            except Exception as e:
                st.error(f"Hiba a Nutrition Facts panelok megjelenítésekor: {str(e)}")
                if debug_mode:
                    st.exception(e)
        else:
            st.warning("⚠️ Nutrition Facts komponens nem elérhető. Fallback natív komponensekre.")
            render_mode = "Native Streamlit"
    
    if render_mode == "Native Streamlit":
        # Egyszerűsített renderelés natív Streamlit komponensekkel
        st.info("🔧 Native Streamlit enabled - Using basic Streamlit components")
        
        if debug_mode:
            st.info("📋 Rendering simplified panels...")
        
        try:
            _render_simple_basic_info(offer, debug_mode)
            _render_simple_producer_info(offer, debug_mode)
            _render_simple_product_info(offer, debug_mode)
            
            if debug_mode:
                st.success("✅ All simplified panels rendered successfully")
        except Exception as e:
            st.error(f"Hiba az egyszerűsített panelok megjelenítésekor: {str(e)}")
            if debug_mode:
                st.exception(e)
    
    elif render_mode == "DetailContainer":
        # Normál DetailContainer renderelés
        st.info("📋 DetailContainer enabled - Using HTML-based containers")
        
        if debug_mode:
            st.info("📋 Using DetailContainer components...")
        
        # Panel elrendezés a képernyőméret függvényében
        if is_mobile:
            # Mobilon egymás alatt jelennek meg a panelek
            _render_basic_info_panel(offer)
            _render_timeline_panel(offer)
            _render_product_panel(offer)
            _render_related_entities_panel(offer)
            _render_activities_panel(offer_id)
        elif is_tablet:
            # Tableten 2 oszlopos elrendezés
            col1, col2 = st.columns(2)
            
            with col1:
                _render_basic_info_panel(offer)
                _render_product_panel(offer)
                _render_activities_panel(offer_id)
            
            with col2:
                _render_timeline_panel(offer)
                _render_related_entities_panel(offer)
        else:
            # Asztali nézeten 3 oszlopos elrendezés
            col1, col2, col3 = st.columns([1, 1, 1])
            
            with col1:
                _render_basic_info_panel(offer)
                _render_product_panel(offer)
            
            with col2:
                _render_timeline_panel(offer)
                _render_related_entities_panel(offer)
            
            with col3:
                _render_activities_panel(offer_id)

    elif render_mode == "Unified Actions":
        # Unified Actions megjelenítés - egyszerű, akcióra fókuszáló interfész
        st.info("🎯 Unified Actions - Egyszerű, akcióra fókuszáló interfész")
        
        try:
            from .unified_action_panel import UnifiedActionPanel, get_default_actions, get_default_callbacks
            
            # Ajánlat alapadatok megjelenítése
            st.subheader(f"Ajánlat #{offer.get('id', 'N/A')}")
            
            # Alapvető információk
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Termelő", offer.get('producer_name', 'N/A'))
                st.metric("Státusz", offer.get('status', 'N/A'))
            
            with col2:
                st.metric("Termék", offer.get('product_name', 'N/A'))
                st.metric("Mennyiség", f"{offer.get('quantity', 0)} kg")
            
            with col3:
                st.metric("Ár", f"{offer.get('price', 0)} Ft/kg")
                st.metric("Létrehozva", offer.get('created_at', 'N/A')[:10] if offer.get('created_at') else 'N/A')
            
            # Unified Action Panel renderelése
            panel = UnifiedActionPanel(
                offer_id=offer.get('id', 0),
                offer_data=offer
            )
            
            # Alapértelmezett műveletek és callback-ek
            actions = get_default_actions(offer)
            callbacks = get_default_callbacks()
            
            # Testreszabott callback-ek hozzáadása
            def enhanced_status_change(offer_id: int, action: dict):
                """Testreszabott státusz változtatás callback"""
                st.session_state[f"show_status_dialog_{offer_id}"] = True
                st.session_state["status_change_source"] = "unified_panel"
                st.rerun()
            
            callbacks['status_change'] = enhanced_status_change
            
            # Panel renderelése
            results = panel.render(
                actions=actions,
                title="Elérhető műveletek",
                show_status_info=True,
                callbacks=callbacks
            )
            
            # Eredmények kezelése
            if results:
                logger.info(f"Unified Actions panel action executed: {results}")
                
        except ImportError as e:
            st.error(f"❌ Unified Action Panel komponens nem elérhető: {str(e)}")
            logger.error(f"Failed to import unified action panel: {str(e)}")
            
            # Fallback: egyszerű gombos interfész
            st.subheader("Egyszerű műveletek")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("← Vissza", key="fallback_back"):
                    st.session_state["go_back_to_offer_list"] = True
                    st.rerun()
            
            with col2:
                if st.button("🔄 Státusz", key="fallback_status"):
                    st.session_state[f"show_status_dialog_{offer.get('id', 0)}"] = True
                    st.rerun()
            
            with col3:
                if st.button("📥 Export", key="fallback_export"):
                    st.info("Export funkció")
                    
        except Exception as e:
            st.error(f"❌ Hiba a Unified Actions megjelenítésekor: {str(e)}")
            logger.error(f"Error rendering unified actions: {str(e)}")
            if debug_mode:
                st.exception(e)


def _load_offer_details(offer_id):
    """
    Ajánlat részletek betöltése az API-n keresztül.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        dict: Az ajánlat adatai vagy None hiba esetén
    """
    try:
        success, result = get_offer_details(offer_id)
        if success:
            return result
        else:
            logger.error(f"Hiba az ajánlat betöltése során: {result}")
            st.error(f"Nem sikerült betölteni az ajánlatot: {result}")
            return None
    except Exception as e:
        logger.exception(f"Kivétel az ajánlat betöltése során: {str(e)}")
        st.error(f"Hiba történt az ajánlat betöltése során: {str(e)}")
        return None


def _handle_status_change(offer_id, current_status):
    """
    Státuszváltás kezelése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        current_status (str): Az ajánlat jelenlegi státusza
    """
    # Ellenőrizzük, hogy van-e függőben lévő státuszváltás
    show_dialog_key = f"show_status_dialog_{offer_id}"
    new_status_key = f"new_status_{offer_id}"
    
    if st.session_state.get(show_dialog_key, False):
        new_status = st.session_state.get(new_status_key)
        
        # Ha van új státusz, megjelenítjük a megerősítő ablakot
        if new_status:
            st.info("Kérjük, erősítsd meg a státuszváltást:")
            
            # Ajánlat adatainak lekérése, ha CONFIRMED_BY_COMPANY státuszra váltunk
            offer_data = None
            if new_status == "CONFIRMED_BY_COMPANY":
                try:
                    success, data = get_offer_details(offer_id)
                    if success:
                        offer_data = data
                    else:
                        logger.warning(f"Nem sikerült lekérni az ajánlat részleteit a visszaigazoláshoz: {data}")
                except Exception as e:
                    logger.exception(f"Hiba az ajánlat lekérése során a visszaigazoláshoz: {str(e)}")
            
            # Megerősítő ablak
            status_modal = StatusTransitionModal(offer_id, current_status, new_status, offer_data)
            
            # CONFIRMED_BY_COMPANY státuszra váltásnál négy paramétert kapunk vissza
            if new_status == "CONFIRMED_BY_COMPANY":
                confirmed, note, confirmed_quantity, confirmed_price = status_modal.render(
                    on_confirm=lambda status, note, quantity, price: _confirm_status_change(
                        offer_id, status, note, quantity, price
                    ),
                    on_cancel=lambda: _cancel_status_change(offer_id)
                )
            else:
                # Egyéb státuszoknál csak a státuszt és megjegyzést adjuk át
                confirmed, note, _, _ = status_modal.render(
                    on_confirm=lambda status, note: _confirm_status_change(offer_id, status, note),
                    on_cancel=lambda: _cancel_status_change(offer_id)
                )
        else:
            # Ha nincs új státusz, töröljük a dialógus állapotot
            del st.session_state[show_dialog_key]
            if new_status_key in st.session_state:
                del st.session_state[new_status_key]


def _confirm_status_change(offer_id, new_status, note, confirmed_quantity=None, confirmed_price=None):
    """
    Státuszváltás megerősítése és végrehajtása.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        new_status (str): Az új státusz
        note (str): Megjegyzés a státuszváltáshoz
        confirmed_quantity (float, optional): Visszaigazolt mennyiség CONFIRMED_BY_COMPANY státusznál
        confirmed_price (float, optional): Visszaigazolt ár CONFIRMED_BY_COMPANY státusznál
    """
    try:
        # Biztosítsuk, hogy az API státuszokat használjuk
        # Ez a map segít, ha formázott státusz kerülne ide
        status_display_map = {
            "Létrehozva": "CREATED",
            "LÉTREHOZVA": "CREATED",
            "Megerősítve": "CONFIRMED_BY_COMPANY",
            "Cég által visszaigazolva": "CONFIRMED_BY_COMPANY",
            "MEGERŐSÍTVE": "CONFIRMED_BY_COMPANY",
            "VISSZAIGAZOLVA": "CONFIRMED_BY_COMPANY",
            "Elfogadva": "ACCEPTED_BY_USER",
            "ELFOGADVA": "ACCEPTED_BY_USER",
            "Elutasítva": "REJECTED_BY_USER",
            "ELUTASÍTVA": "REJECTED_BY_USER",
            "Teljesítve": "FINALIZED",
            "Véglegesítve": "FINALIZED",
            "TELJESÍTVE": "FINALIZED",
            "VÉGLEGESÍTVE": "FINALIZED"
        }
        
        # Ellenőrizzük, hogy a státusz API formátumú-e, ha nem, konvertáljuk
        api_status = new_status
        if new_status in status_display_map:
            api_status = status_display_map[new_status]
            logger.info(f"Státusz konvertálása: {new_status} → {api_status}")
        
        # A továbbiakban használjuk az API-kompatibilis státuszt
        new_status = api_status
        
        # Ellenőrizzük, hogy a mennyiség és az ár értékek helyesek-e
        if new_status == "CONFIRMED_BY_COMPANY":
            # Ellenőrizzük a fixed_ értékeket is, hogy támogassuk a korábbi változtatásokat
            if "fixed_quantity_value" in st.session_state and (confirmed_quantity is None or confirmed_quantity == 0):
                confirmed_quantity = float(st.session_state["fixed_quantity_value"])
                logger.info(f"Mennyiség fixed_quantity_value-ból: {confirmed_quantity}")
            
            if "fixed_price_value" in st.session_state and (confirmed_price is None or confirmed_price == 0):
                confirmed_price = float(st.session_state["fixed_price_value"])
                logger.info(f"Ár fixed_price_value-ból: {confirmed_price}")
            
            # Használjuk a session state értékeket, ha azok rendelkezésre állnak
            for key in st.session_state.keys():
                if key.startswith(f"status_change_{offer_id}") and "_quantity" in key:
                    if confirmed_quantity is None or confirmed_quantity == 0:
                        try:
                            # Próbáljuk használni az input értéket, ha van
                            input_key = f"{key}_input"
                            if input_key in st.session_state:
                                confirmed_quantity = float(st.session_state[input_key])
                            else:
                                confirmed_quantity = float(st.session_state[key])
                        except (ValueError, TypeError):
                            # Ha nem sikerül, használjuk a manuálisan megadott értéket
                            pass
                
                if key.startswith(f"status_change_{offer_id}") and "_price" in key:
                    if confirmed_price is None or confirmed_price == 0:
                        try:
                            # Próbáljuk használni az input értéket, ha van
                            input_key = f"{key}_input"
                            if input_key in st.session_state:
                                confirmed_price = float(st.session_state[input_key])
                            else:
                                confirmed_price = float(st.session_state[key])
                        except (ValueError, TypeError):
                            # Ha nem sikerül, használjuk a manuálisan megadott értéket
                            pass
            
            # Ellenőrizzük, hogy van-e érvényes mennyiség és ár
            if confirmed_quantity is None or confirmed_quantity <= 0:
                st.error("Érvénytelen mennyiség! A visszaigazolt mennyiségnek nagyobbnak kell lennie nullánál.")
                return
                
            if confirmed_price is None or confirmed_price <= 0:
                st.error("Érvénytelen ár! A visszaigazolt árnak nagyobbnak kell lennie nullánál.")
                return
        
        # Részletes naplózás
        logger.info(f"Státuszváltás végrehajtása: {offer_id} → {new_status}")
        logger.info(f"Mennyiség: {confirmed_quantity}, Ár: {confirmed_price}")
        
        # API hívás a státusz frissítéséhez - az új paraméterekkel
        if new_status == "CONFIRMED_BY_COMPANY" and confirmed_quantity is not None and confirmed_price is not None:
            # Létrehozzuk a visszaigazolási adatok dictionary-t
            confirmation_data = {
                "confirmed_quantity": confirmed_quantity,
                "confirmed_price": confirmed_price
            }
            
            logger.info(f"Visszaigazolási adatok: {confirmation_data}")
            
            # Ha visszaigazolás (CONFIRMED_BY_COMPANY), akkor továbbítjuk a mennyiséget és árat is
            success, result = update_offer_status(offer_id, new_status, confirmation_data)
        else:
            # Egyéb státuszoknál nem szükséges a mennyiség és ár
            success, result = update_offer_status(offer_id, new_status)
        
        if success:
            # Sikerüzenet státusz függvényében
            if new_status == "CONFIRMED_BY_COMPANY":
                st.success(f"Az ajánlat sikeresen visszaigazolva {format_price(confirmed_price)}/kg áron és " +
                          f"{format_quantity(confirmed_quantity)} kg mennyiséggel. " +
                          f"Új státusz: {format_status(new_status)}")
            else:
                st.success(f"A státusz sikeresen módosítva: {format_status(new_status)}")
                
            # Töröljük a dialógus állapotot és a kapcsolódó adatokat
            keys_to_delete = []
            
            # Gyűjtsük össze az összes törlendő kulcsot
            for key in st.session_state.keys():
                if (key.startswith(f"show_status_dialog_{offer_id}") or
                    key.startswith(f"new_status_{offer_id}") or
                    key.startswith(f"status_change_{offer_id}") or
                    key == "fixed_quantity_value" or
                    key == "fixed_price_value" or
                    key == "quantity_input_field" or 
                    key == "price_input_field"):
                    keys_to_delete.append(key)
            
            # Töröljük a kulcsokat
            for key in keys_to_delete:
                try:
                    del st.session_state[key]
                except Exception as e:
                    logger.warning(f"Nem sikerült törölni a session state kulcsot: {key}, hiba: {str(e)}")
            
            # Újratöltjük az oldalt
            st.rerun()
        else:
            # Részletesebb hibaüzenet
            error_msg = f"Hiba a státusz módosítása során: {result}"
            if new_status == "CONFIRMED_BY_COMPANY" and "visszaigazolási adatok" in str(result).lower():
                error_msg += "\nEllenőrizd, hogy megadtál-e megfelelő mennyiséget és árat a visszaigazoláshoz!"
            st.error(error_msg)
    except Exception as e:
        logger.exception(f"Kivétel a státusz módosítása során: {str(e)}")
        st.error(f"Hiba történt a státusz módosítása során: {str(e)}")


def _cancel_status_change(offer_id):
    """
    Státuszváltás megszakítása.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Töröljük a dialógus állapotot
    dialog_key = f"show_status_dialog_{offer_id}"
    status_key = f"new_status_{offer_id}"
    if dialog_key in st.session_state:
        del st.session_state[dialog_key]
    if status_key in st.session_state:
        del st.session_state[status_key]
    # Újratöltjük az oldalt
    st.rerun()


def _render_action_bar(offer_id, status, offer=None):
    """
    Műveleti sáv megjelenítése - FRISSÍTETT VERZIÓ.
    Modern quick action bar implementáció.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        status (str): Az ajánlat státusza
        offer (dict, optional): Az ajánlat adatai
    """
    # Ha nincs offer paraméter, akkor betöltjük
    if not offer:
        offer = _load_offer_details(offer_id)
        if not offer:
            # Fallback to original ActionBar if offer loading fails
            permissions = {
                "can_edit": status not in ["FINALIZED", "REJECTED_BY_USER"],
                "can_change_status": True,
                "can_delete": status != "FINALIZED"
            }
            action_bar = ActionBar(offer_id, status, permissions)
            action_bar.render()
            return
    
    # Try to import and use Quick Action Bar
    try:
        from .quick_action_bar import render_quick_action_bar_native
    except ImportError:
        try:
            from quick_action_bar import render_quick_action_bar_native
        except ImportError:
            # Fallback to original ActionBar if import fails
            permissions = {
                "can_edit": status not in ["FINALIZED", "REJECTED_BY_USER"],
                "can_change_status": True,
                "can_delete": status != "FINALIZED"
            }
            action_bar = ActionBar(offer_id, status, permissions)
            action_bar.render()
            return
    
    # Render the modern quick action bar
    # Use a unique key prefix based on the current render mode or context
    key_prefix = "offer_detail"
    action = render_quick_action_bar_native(offer, offer_id, key_prefix=key_prefix)
    
    # Handle quick action bar actions
    if action:
        if action == "back":
            logger.info("Quick action: Back button clicked")
            st.session_state["go_back_to_offer_list"] = True
            if "selected_offer_id" in st.session_state:
                del st.session_state["selected_offer_id"]
            st.rerun()
        elif action == "edit":
            logger.info("Quick action: Edit button clicked")
            st.session_state["edit_mode"] = True
            st.success("🔧 Szerkesztési mód aktiválva")
        elif action == "confirm":
            logger.info("Quick action: Confirm button clicked")
            # Set flag to show confirmation dialog
            st.session_state[f"show_confirmation_dialog_{offer_id}"] = True
            st.rerun()
        elif action == "accept":
            logger.info("Quick action: Accept button clicked")
            st.session_state[f"show_status_dialog_{offer_id}"] = True
            st.session_state[f"new_status_{offer_id}"] = "ACCEPTED_BY_USER"
            st.rerun()
        elif action == "reject":
            logger.info("Quick action: Reject button clicked")
            st.session_state[f"show_status_dialog_{offer_id}"] = True
            st.session_state[f"new_status_{offer_id}"] = "REJECTED_BY_USER"
            st.rerun()
        elif action == "finalize":
            logger.info("Quick action: Finalize button clicked")
            st.session_state[f"show_status_dialog_{offer_id}"] = True
            st.session_state[f"new_status_{offer_id}"] = "FINALIZED"
            st.rerun()
        elif action == "delete":
            logger.info("Quick action: Delete button clicked")
            if st.session_state.get("confirm_delete", False):
                # Perform delete
                st.error("Törlés funkció fejlesztés alatt")
            else:
                st.warning("⚠️ Biztosan törölni szeretnéd az ajánlatot? Kattints újra a megerősítéshez.")
                st.session_state["confirm_delete"] = True
        elif action == "export":
            logger.info("Quick action: Export button clicked")
            st.info("📊 Export funkció fejlesztés alatt")
        elif action == "print":
            logger.info("Quick action: Print button clicked")
            st.info("🖨️ Nyomtatási funkció fejlesztés alatt")
        elif action == "copy":
            logger.info("Quick action: Copy button clicked")
            # Copy offer link to clipboard
            offer_link = f"{st.get_option('server.baseUrlPath')}/offer/{offer_id}"
            st.success(f"📋 Link másolva: {offer_link}")
        elif action == "history":
            logger.info("Quick action: History button clicked")
            st.session_state["show_history"] = True
            st.info("📜 Előzmények panel megnyitva")
    
    # Opcionálisan: eredeti ActionBar mint fallback vagy kiegészítés
    # Ha szükséges további funkciók, akkor meghagyhatjuk
    # permissions = {
    #     "can_edit": status not in ["FINALIZED", "REJECTED_BY_USER"],
    #     "can_change_status": True,
    #     "can_delete": status != "FINALIZED"
    # }
    # action_bar = ActionBar(offer_id, status, permissions)
    # action_bar.render()


def _render_offer_header(offer):
    """
    Ajánlat fejléc információk megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Ajánlat azonosító és státusz
    st.markdown(f"## Ajánlat #{offer.get('id', '-')}")
    
    # Státusz indikátor
    StatusIndicator(
        status=offer.get('status', '-'),
        timestamp=offer.get('status_changed_at') or offer.get('updated_at') or offer.get('created_at'),
        description=offer.get('status_note')
    ).render()


def _render_basic_info_panel(offer):
    """
    Alapadatok panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    basic_info_container = DetailContainer(
        title="Alapadatok",
        icon="📄",
        expandable=True,
        expanded=True,
        color="#3584e4"
    )
    
    # Debug: Test if render works outside container
    if st.checkbox("🧪 Test Direct Rendering (bypass container)", key="test_direct_render"):
        st.write("**Direct rendering test (no container):**")
        _render_basic_info_content(offer)
        st.markdown("---")
        st.write("**Now testing with container:**")
    
    # Panel megjelenítése
    basic_info_container.render(lambda: _render_basic_info_content(offer))


def _render_basic_info_content(offer):
    """Alapadatok megjelenítése szép HTML-lel - DEBUG verzió"""
    # Debug checkbox a függvény elején
    debug_content = st.checkbox("🔍 Debug Basic Info Content", key="debug_basic_info")
    
    if debug_content:
        st.write("**Debug - Offer data structure:**")
        st.json({
            "offer_keys": list(offer.keys()) if offer else [],
            "user_data": offer.get('user', {}) if offer else {},
            "product_type_data": offer.get('product_type', {}) if offer else {}
        })
        st.markdown("---")
    
    # FONTOS: 'user' mező használata 'producer' helyett
    producer = offer.get('user', {})
    product = offer.get('product_type', {})
    
    if debug_content:
        st.write("**Producer data:**", producer)
        st.write("**Product data:**", product)
        st.markdown("---")
    
    # HTML táblázat
    html_content = f"""
    <div style="padding: 16px;">
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 12px 0; font-weight: bold; width: 30%; vertical-align: top;">Termelő:</td>
                <td style="padding: 12px 0;">{producer.get('contact_name', 'Nincs adat')}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Cég:</td>
                <td style="padding: 12px 0;">{producer.get('company_name', 'Nincs adat')}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Termék:</td>
                <td style="padding: 12px 0;">{product.get('name', 'Nincs adat')}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Mennyiség:</td>
                <td style="padding: 12px 0;">{format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)), offer.get('quantity_unit', 'kg'))}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Ár:</td>
                <td style="padding: 12px 0;">{format_price(offer.get('price', 0))}/{offer.get('quantity_unit', 'kg')}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Létrehozás:</td>
                <td style="padding: 12px 0;">{format_datetime(offer.get('created_at', '-'))}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: bold; vertical-align: top;">Beszállítás:</td>
                <td style="padding: 12px 0;">{format_date(offer.get('delivery_date', '-'))}</td>
            </tr>
        </table>
    </div>
    """
    
    if debug_content:
        st.write("**HTML Content to be rendered:**")
        st.code(html_content, language="html")
        st.markdown("---")
    
    # Test rendering different ways
    if debug_content:
        st.write("**Test 1: Direct st.write:**")
        st.write(f"Termelő: {producer.get('contact_name', 'Nincs adat')}")
        
        st.write("**Test 2: Simple HTML:**")
        st.markdown("<p style='color: blue;'>This should be blue</p>", unsafe_allow_html=True)
        
        st.write("**Test 3: Complex HTML table:**")
    
    # Use components.html for complex HTML tables
    components.html(html_content, height=300)
    
    # Megjegyzés külön
    if offer.get('note'):
        st.markdown(f"""
        <div style="
            background-color: #E3F2FD;
            border-left: 4px solid #2196F3;
            padding: 16px;
            margin-top: 16px;
            border-radius: 4px;
        ">
            <strong>📝 Megjegyzés:</strong><br/>
            {offer.get('note')}
        </div>
        """, unsafe_allow_html=True)


def _render_timeline_panel(offer):
    """
    Idősor panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    timeline_container = DetailContainer(
        title="Idősor",
        icon="🕒",
        expandable=True,
        expanded=True,
        color="#33a02c"
    )
    
    # Idősor események összeállítása
    events = _create_timeline_events(offer)
    
    # Panel megjelenítése
    timeline_container.render(lambda: Timeline(events).render())


def _create_timeline_events(offer):
    """
    Idősor események összeállítása az ajánlat adataiból.
    
    Args:
        offer (dict): Az ajánlat adatai
        
    Returns:
        list: Idősor események listája
    """
    events = []
    
    # Létrehozás dátuma
    if offer.get('created_at'):
        events.append({
            "date": offer.get('created_at'),
            "label": "Létrehozva",
            "description": f"Létrehozta: {offer.get('creator', {}).get('contact_name', 'Rendszer')}"
        })
    
    # Megerősítés dátuma
    if offer.get('confirmed_at'):
        events.append({
            "date": offer.get('confirmed_at'),
            "label": "Megerősítve",
            "description": None
        })
    
    # Elfogadás dátuma
    if offer.get('accepted_at'):
        events.append({
            "date": offer.get('accepted_at'),
            "label": "Elfogadva",
            "description": None
        })
    
    # Elutasítás dátuma
    if offer.get('rejected_at'):
        events.append({
            "date": offer.get('rejected_at'),
            "label": "Elutasítva",
            "description": offer.get('rejection_reason', None)
        })
    
    # Véglegesítés dátuma
    if offer.get('finalized_at'):
        events.append({
            "date": offer.get('finalized_at'),
            "label": "Véglegesítve",
            "description": None
        })
    
    # Beszállítás dátuma
    if offer.get('delivery_date'):
        # Ellenőrizzük, hogy ez a jövőben van-e
        is_future = (
            isinstance(offer.get('delivery_date'), datetime) and 
            offer.get('delivery_date') > datetime.now()
        )
        
        events.append({
            "date": offer.get('delivery_date'),
            "label": "Beszállítás",
            "description": "Tervezett" if is_future else None
        })
    
    return events


def _render_product_panel(offer):
    """
    Termék panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    product_container = DetailContainer(
        title="Termék adatok",
        icon="🥦",
        expandable=True,
        expanded=True,
        color="#e66100"
    )
    
    # Panel megjelenítése
    product_container.render(lambda: _render_product_content(offer))


def _render_product_content(offer):
    """
    Termék panel tartalmának megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Termék adatok
    product = offer.get('product_type', {})
    
    # Entitás kártya használata
    product_card = EntityCard(
        title="",  # Nincs külön cím a kártyának
        data=product,
        entity_type="product"
    )
    product_card.render()
    
    # Termék részletek táblázat
    st.markdown("### Termék adatok")
    
    # Termék mennyiség és ár
    cols = st.columns(3)
    with cols[0]:
        st.metric(
            label="Mennyiség", 
            value=format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)), offer.get('quantity_unit', 'kg'))
        )
    with cols[1]:
        if offer.get('price') is not None:
            st.metric(
                label="Egységár", 
                value=f"{format_price(offer.get('price', 0))}/{offer.get('quantity_unit', 'kg')}"
            )
    with cols[2]:
        # Calculate total price based on unit type
        quantity_value = offer.get('quantity_value', offer.get('quantity_in_kg', 0))
        quantity_unit = offer.get('quantity_unit', 'kg')
        
        if offer.get('price') is not None and quantity_value is not None:
            if quantity_unit == "db":
                # For pieces, use direct multiplication
                total_price = offer.get('price', 0) * quantity_value
            else:
                # For weight units, use the legacy calculation (converted to kg)
                quantity_in_kg = quantity_value if quantity_unit == "kg" else quantity_value * 1000 if quantity_unit == "tonna" else quantity_value
                total_price = offer.get('price', 0) * quantity_in_kg
            st.metric(
                label="Összérték", 
                value=f"{format_price(total_price)}"
            )
    
    # Termék minőségi paraméterek, ha vannak
    if offer.get('quality_parameters'):
        st.markdown("---")
        st.markdown("### Minőségi paraméterek")
        
        params = offer.get('quality_parameters', {})
        if isinstance(params, dict) and params:
            for name, value in params.items():
                st.markdown(f"**{name}:** {value}")
        else:
            st.info("Nincsenek minőségi paraméterek megadva.")


def _render_related_entities_panel(offer):
    """
    Kapcsolódó entitások panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    related_container = DetailContainer(
        title="Kapcsolódó entitások",
        icon="🔗",
        expandable=True,
        expanded=True,
        color="#5e35b1"
    )
    
    # Panel megjelenítése
    related_container.render(lambda: _render_related_entities_content(offer))


def _render_related_entities_content(offer):
    """
    Kapcsolódó entitások panel tartalmának megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Tab-ok létrehozása: Termelő, Termék, Kapcsolódó ajánlatok
    tabs = st.tabs(["Termelő", "Termék részletek", "Kapcsolódó ajánlatok"])
    
    # Termelő tab
    with tabs[0]:
        producer = offer.get('user', {})
        if producer:
            EntityCard(
                title="Termelő adatok",
                data=producer,
                entity_type="producer"
            ).render()
        else:
            st.info("Nincs termelő adat.")
    
    # Termék részletek tab
    with tabs[1]:
        product = offer.get('product_type', {})
        if product:
            st.markdown("#### Termék részletek")
            
            # További termék részletek
            st.markdown(f"**Kategória:** {product.get('category', {}).get('name', '-')}")
            
            if product.get('description'):
                st.markdown("**Leírás:**")
                st.markdown(product.get('description', ''))
            
            if product.get('growing_parameters'):
                st.markdown("**Termesztési paraméterek:**")
                params = product.get('growing_parameters', {})
                for name, value in params.items():
                    st.markdown(f"- **{name}:** {value}")
        else:
            st.info("Nincs termék részlet adat.")
    
    # Kapcsolódó ajánlatok tab
    with tabs[2]:
        # Betöltési állapot beállítása
        loading_key = 'loading_related'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Kapcsolódó ajánlatok betöltése...")
        else:
            # Kapcsolódó ajánlatok betöltése
            try:
                st.session_state[loading_key] = True
                success, related_offers = get_related_offers(offer.get('id'))
                
                if success and related_offers:
                    for related in related_offers:
                        EntityCard(
                            title=f"Ajánlat #{related.get('id')}",
                            data=related,
                            entity_type="related_offer"
                        ).render()
                        st.markdown("---")
                else:
                    st.info("Nincsenek kapcsolódó ajánlatok.")
            except Exception as e:
                logger.exception(f"Hiba a kapcsolódó ajánlatok betöltése során: {str(e)}")
                st.error(f"Hiba történt a kapcsolódó ajánlatok betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False


def _render_activities_panel(offer_id):
    """
    Tevékenységek panel megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Konténer inicializálása
    activities_container = DetailContainer(
        title="Tevékenységek",
        icon="🔔",
        expandable=True,
        expanded=True,
        color="#9c27b0"
    )
    
    # Panel megjelenítése
    activities_container.render(lambda: _render_activities_content(offer_id))


def _render_activities_content(offer_id):
    """
    Tevékenységek panel tartalmának megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Tab-ok létrehozása: Státusztörténet, Csatolmányok, Audit napló
    tabs = st.tabs(["Státusztörténet", "Csatolmányok", "Audit napló"])
    
    # Státusztörténet tab
    with tabs[0]:
        # Betöltési állapot beállítása
        loading_key = 'loading_logs'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Státusztörténet betöltése...")
        else:
            # Státuszváltozások betöltése
            try:
                st.session_state[loading_key] = True
                success, logs = get_offer_logs(offer_id, log_type="status")
                
                if success and logs:
                    ActivityLog(logs, log_type="status").render()
                else:
                    st.info("Nincs státusztörténet adat.")
            except Exception as e:
                logger.exception(f"Hiba a státusztörténet betöltése során: {str(e)}")
                st.error(f"Hiba történt a státusztörténet betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False
    
    # Csatolmányok tab
    with tabs[1]:
        # Betöltési állapot beállítása
        loading_key = 'loading_attachments'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Csatolmányok betöltése...")
        else:
            # Csatolmányok betöltése
            try:
                st.session_state[loading_key] = True
                success, attachments = get_offer_attachments(offer_id)
                
                if success and attachments:
                    ActivityLog(attachments, log_type="attachment").render()
                else:
                    st.info("Nincsenek csatolmányok.")
                    
                # Új csatolmány feltöltése gomb
                st.markdown("---")
                st.file_uploader("Új csatolmány feltöltése", key=f"attachment_upload_{offer_id}")
                
            except Exception as e:
                logger.exception(f"Hiba a csatolmányok betöltése során: {str(e)}")
                st.error(f"Hiba történt a csatolmányok betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False
    
    # Audit napló tab
    with tabs[2]:
        # Audit napló betöltése
        try:
            success, logs = get_offer_logs(offer_id, log_type="audit")
            
            if success and logs:
                ActivityLog(logs, log_type="audit").render()
            else:
                st.info("Nincs audit napló adat.")
        except Exception as e:
            logger.exception(f"Hiba az audit napló betöltése során: {str(e)}")
            st.error(f"Hiba történt az audit napló betöltése során: {str(e)}")


# Nutrition Facts Container importálása
try:
    from .nutrition_facts_container import (
        NutritionFactsContainer,
        create_offer_facts_panel,
        create_producer_facts_panel
    )
    # Also import the HTML renderer check
    from .html_renderer import check_st_html_availability
except ImportError:
    try:
        from nutrition_facts_container import (
            NutritionFactsContainer,
            create_offer_facts_panel,
            create_producer_facts_panel
        )
        from html_renderer import check_st_html_availability
    except ImportError:
        logging.warning("Could not import nutrition_facts_container, feature disabled")
        NutritionFactsContainer = None
        create_offer_facts_panel = None
        create_producer_facts_panel = None
        check_st_html_availability = None

# Egyszerűsített renderelő függvények HTML konténerek nélkül
def _render_simple_basic_info(offer, debug_mode=False):
    """Egyszerűsített alapadatok megjelenítés natív Streamlit komponensekkel"""
    if debug_mode:
        st.info("📋 Rendering basic info...")
    
    try:
        st.subheader("📋 Alapadatok")
        
        # Státusz megjelenítése
        status = offer.get('status', 'ISMERETLEN')
        StatusIndicator(status).render()
        
        # Alapvető információk
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Ajánlat információk:**")
            st.write(f"• **Ajánlat #:** {offer.get('id', 'Nincs adat')}")
            st.write(f"• **Mennyiség:** {_format_quantity(offer.get('quantity_in_kg', 0))} kg")
            st.write(f"• **Ár:** {_format_price(offer.get('price', 0))}/kg")
            st.write(f"• **Létrehozás:** {_format_datetime(offer.get('created_at', '-'))}")
            st.write(f"• **Beszállítás:** {_format_date(offer.get('delivery_date', '-'))}")
        
        with col2:
            st.markdown("**Termék információk:**")
            product = offer.get('product_type', {})
            st.write(f"• **Termék:** {product.get('name', 'Nincs adat')}")
            st.write(f"• **Kategória:** {product.get('category', {}).get('name', 'Nincs adat')}")
            
            # Visszaigazolt adatok, ha vannak
            if offer.get('confirmed_quantity'):
                st.write(f"• **Visszaig. mennyiség:** {_format_quantity(offer.get('confirmed_quantity', 0))} kg")
            if offer.get('confirmed_price'):
                st.write(f"• **Visszaig. ár:** {_format_price(offer.get('confirmed_price', 0))}/kg")
        
        # Megjegyzés külön
        if offer.get('note'):
            st.info(f"📝 **Megjegyzés:** {offer.get('note')}")
        
        if debug_mode:
            st.success("✅ Basic info rendered successfully")
            
    except Exception as e:
        st.error(f"Hiba az alapadatok megjelenítésekor: {str(e)}")
        if debug_mode:
            st.exception(e)


def _render_simple_producer_info(offer, debug_mode=False):
    """Egyszerűsített termelő adatok megjelenítés"""
    if debug_mode:
        st.info("📋 Rendering producer info...")
    
    try:
        st.subheader("👥 Termelő adatok")
        
        producer = offer.get('user', {})
        
        if not producer:
            st.info("Nincs elérhető termelő adat.")
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Alapadatok:**")
            st.write(f"• **Név:** {producer.get('contact_name', 'Nincs adat')}")
            st.write(f"• **Cégnév:** {producer.get('company_name', 'Nincs adat')}")
        
        with col2:
            st.markdown("**Kapcsolat:**")
            email = producer.get('email', 'Nincs adat')
            phone = producer.get('phone', 'Nincs adat')
            
            if email != 'Nincs adat':
                st.write(f"• **Email:** [{email}](mailto:{email})")
            else:
                st.write(f"• **Email:** {email}")
                
            if phone != 'Nincs adat':
                st.write(f"• **Telefon:** [{phone}](tel:{phone})")
            else:
                st.write(f"• **Telefon:** {phone}")
        
        # Cím külön
        if producer.get('address'):
            st.write(f"**📍 Cím:** {producer.get('address')}")
        
        if debug_mode:
            st.success("✅ Producer info rendered successfully")
            
    except Exception as e:
        st.error(f"Hiba a termelő adatok megjelenítésekor: {str(e)}")
        if debug_mode:
            st.exception(e)


def _render_simple_product_info(offer, debug_mode=False):
    """Egyszerűsített termék adatok megjelenítés"""
    if debug_mode:
        st.info("📋 Rendering product info...")
    
    try:
        st.subheader("🌾 Termék részletek")
        
        product = offer.get('product_type', {})
        
        if not product:
            st.info("Nincs elérhető termék adat.")
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Termék adatok:**")
            st.write(f"• **Termék neve:** {product.get('name', 'Nincs adat')}")
            st.write(f"• **Kategória:** {product.get('category', {}).get('name', 'Nincs adat')}")
            if product.get('unit'):
                st.write(f"• **Mértékegység:** {product.get('unit')}")
        
        with col2:
            st.markdown("**További információk:**")
            if product.get('description'):
                st.write(f"• **Leírás:** {product.get('description')}")
            else:
                st.write("• **Leírás:** Nincs megadva")
        
        if debug_mode:
            st.success("✅ Product info rendered successfully")
            
    except Exception as e:
        st.error(f"Hiba a termék adatok megjelenítésekor: {str(e)}")
        if debug_mode:
            st.exception(e)


# Formázó segédfüggvények
def _format_quantity(value):
    """Mennyiség formázása"""
    try:
        if value is None:
            return "0"
        return f"{float(value):,.2f}"
    except (ValueError, TypeError):
        return str(value) if value else "0"


def _format_price(value):
    """Ár formázása"""
    try:
        if value is None:
            return "0 Ft"
        return f"{float(value):,.0f} Ft"
    except (ValueError, TypeError):
        return f"{value} Ft" if value else "0 Ft"


def _format_datetime(value):
    """Dátum és idő formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            from datetime import datetime
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        return value.strftime("%Y-%m-%d %H:%M")
    except (ValueError, AttributeError):
        return str(value) if value else "-"


def _format_date(value):
    """Dátum formázása"""
    try:
        if not value:
            return "-"
        if isinstance(value, str):
            from datetime import datetime
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d")
        return value.strftime("%Y-%m-%d")
    except (ValueError, AttributeError):
        return str(value) if value else "-"


def test_nutrition_facts_in_offer_detail():
    """Teszt funkció a Nutrition Facts megjelenítés teszteléséhez"""
    st.markdown("## 🧪 Nutrition Facts Teszt az Ajánlat Részletekben")
    
    # Teszt adatok
    test_offer = {
        'id': 123,
        'status': 'ACCEPTED_BY_USER',
        'quantity_in_kg': 25.5,
        'price': 850,
        'confirmed_quantity': 25.0,
        'confirmed_price': 850,
        'created_at': '2024-04-27T08:30:00Z',
        'delivery_date': '2024-05-01',
        'note': 'Friss, bio minőségű termékek. Különös figyelmet fordítunk a minőségre.',
        'user': {
            'contact_name': 'Nagy János',
            'company_name': 'BioFarm Kft.',
            'email': '<EMAIL>',
            'phone': '+36301234567'
        },
        'product_type': {
            'name': 'Bio sárgarépa',
            'category': {'name': 'Gyökérzöldségek'}
        }
    }
    
    if create_offer_facts_panel:
        st.info("✅ Nutrition Facts komponens elérhető")
        create_offer_facts_panel(test_offer)
    else:
        st.error("❌ Nutrition Facts komponens nem elérhető") 