"""
Column customization functionality for the offer management page.
Allows users to select and order visible columns in data tables.
"""
import streamlit as st
import logging
import uuid
from typing import List, Dict, Any, Tuple, Optional, Set, Union
import json

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in column_customization.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Logger setup
logger = logging.getLogger(__name__)

# Define all available columns with metadata
DEFAULT_COLUMNS = {
    "id": {
        "display_name": "Azonosító",
        "field": "id",
        "formatter": lambda x: f"#{x}",
        "default_visible": True,
        "default_order": 0,
        "width": "small",
        "description": "Ajánlat egyedi azonosítója"
    },
    "status": {
        "display_name": "Státusz",
        "field": "status",
        "formatter": format_status,
        "default_visible": True,
        "default_order": 1,
        "width": "medium",
        "description": "Ajánlat jelenlegi státusza"
    },
    "product_name": {
        "display_name": "Termék",
        "field": "product_type.name",
        "formatter": lambda x: x or "Ismeretlen",
        "default_visible": True,
        "default_order": 2,
        "width": "medium",
        "description": "Termék neve"
    },
    "producer_name": {
        "display_name": "Termelő",
        "field": "user.contact_name",
        "formatter": lambda x: x or "Ismeretlen",
        "default_visible": True,
        "default_order": 3,
        "width": "medium",
        "description": "Termelő neve"
    },
    "quantity": {
        "display_name": "Mennyiség",
        "field": "quantity_in_kg",
        "formatter": format_quantity,
        "default_visible": True,
        "default_order": 4,
        "width": "small",
        "description": "Ajánlott mennyiség (kg)"
    },
    "price": {
        "display_name": "Ár",
        "field": "price",
        "formatter": format_price,
        "default_visible": True,
        "default_order": 5,
        "width": "small",
        "description": "Ajánlott ár (Ft/kg)"
    },
    "delivery_date": {
        "display_name": "Beszállítás",
        "field": "delivery_date",
        "formatter": format_date,
        "default_visible": True,
        "default_order": 6,
        "width": "medium",
        "description": "Tervezett beszállítási dátum"
    },
    "created_at": {
        "display_name": "Létrehozva",
        "field": "created_at",
        "formatter": format_datetime,
        "default_visible": False,
        "default_order": 7,
        "width": "medium",
        "description": "Ajánlat létrehozásának időpontja"
    },
    "created_by": {
        "display_name": "Létrehozta",
        "field": "created_by_user.contact_name",
        "formatter": lambda x: x or "-",
        "default_visible": False,
        "default_order": 8,
        "width": "medium", 
        "description": "Ajánlatot létrehozó felhasználó"
    },
    "confirmed_quantity": {
        "display_name": "Visszaigazolt mennyiség",
        "field": "confirmed_quantity",
        "formatter": format_quantity,
        "default_visible": False,
        "default_order": 9,
        "width": "medium",
        "description": "Ügyintéző által visszaigazolt mennyiség"
    },
    "confirmed_price": {
        "display_name": "Visszaigazolt ár",
        "field": "confirmed_price",
        "formatter": format_price,
        "default_visible": False,
        "default_order": 10,
        "width": "medium",
        "description": "Ügyintéző által visszaigazolt ár"
    },
    "quality_grade": {
        "display_name": "Minőség",
        "field": "quality_grade.name",
        "formatter": lambda x: x or "-",
        "default_visible": False,
        "default_order": 11,
        "width": "small",
        "description": "Termék minőségi besorolása"
    },
    "modified_at": {
        "display_name": "Módosítva",
        "field": "updated_at",
        "formatter": format_datetime,
        "default_visible": False,
        "default_order": 12,
        "width": "medium",
        "description": "Utolsó módosítás időpontja"
    },
    "total_value": {
        "display_name": "Teljes érték",
        "field": "total_value",
        "formatter": lambda x: format_price(x) if x is not None else format_price(lambda o: o.get("price", 0) * o.get("quantity_in_kg", 0)),
        "default_visible": False,
        "default_order": 13,
        "width": "medium",
        "description": "Ajánlat teljes értéke (ár × mennyiség)",
        "calculated": True
    },
    "description": {
        "display_name": "Megjegyzés",
        "field": "description",
        "formatter": lambda x: x or "-",
        "default_visible": False,
        "default_order": 14,
        "width": "large",
        "description": "Ajánlathoz fűzött megjegyzés"
    }
}

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def get_user_column_preferences() -> Dict[str, Dict[str, Any]]:
    """
    Get user's column preferences from session state.
    If not yet set, initialize with defaults.
    
    Returns:
        Dictionary of column preferences
    """
    # Unique key for storing preferences in session state
    pref_key = "offer_table_column_preferences"
    
    # Initialize with defaults if not yet set
    if pref_key not in st.session_state:
        # Start with a copy of the default columns
        user_prefs = {}
        
        # For each default column, set visibility based on default
        for col_id, col_info in DEFAULT_COLUMNS.items():
            user_prefs[col_id] = {
                "visible": col_info["default_visible"],
                "order": col_info["default_order"]
            }
        
        # Store in session state
        st.session_state[pref_key] = user_prefs
    
    return st.session_state[pref_key]

def save_column_preferences(preferences: Dict[str, Dict[str, Any]]) -> None:
    """
    Save column preferences to session state.
    
    Args:
        preferences: Dictionary of column preferences
    """
    pref_key = "offer_table_column_preferences"
    st.session_state[pref_key] = preferences

def render_column_chooser() -> None:
    """
    Render the column selection and ordering UI.
    """
    # Get current preferences
    user_prefs = get_user_column_preferences()
    
    # Create a copy for modifications
    updated_prefs = user_prefs.copy()
    
    st.markdown("### Oszlopok testreszabása")
    st.markdown("Válassza ki a megjelenítendő oszlopokat és állítsa be a sorrendjüket.")
    
    # Two-column layout
    col1, col2 = st.columns([3, 2])
    
    with col1:
        st.markdown("#### Látható oszlopok")
        
        # Create a sortable list of visible columns
        visible_columns = {col_id: prefs for col_id, prefs in user_prefs.items() if prefs["visible"]}
        # Sort by current order
        visible_columns = dict(sorted(visible_columns.items(), key=lambda x: x[1]["order"]))
        
        # Display visible columns with reordering options
        for i, (col_id, prefs) in enumerate(visible_columns.items()):
            col_info = DEFAULT_COLUMNS[col_id]
            
            # Create a container for each column
            container = st.container()
            move_col1, title_col, move_col2, remove_col = container.columns([1, 10, 1, 1])
            
            with move_col1:
                # Up button (disabled for first item)
                if i > 0:
                    if st.button("↑", key=f"up_{col_id}"):
                        # Swap with previous item
                        prev_col_id = list(visible_columns.keys())[i-1]
                        updated_prefs[col_id]["order"] = i - 1
                        updated_prefs[prev_col_id]["order"] = i
                        
                        # Save and refresh
                        save_column_preferences(updated_prefs)
                        st.rerun()
                else:
                    st.write("")  # Placeholder for alignment
            
            with title_col:
                st.markdown(f"**{col_info['display_name']}** - {col_info['description']}")
            
            with move_col2:
                # Down button (disabled for last item)
                if i < len(visible_columns) - 1:
                    if st.button("↓", key=f"down_{col_id}"):
                        # Swap with next item
                        next_col_id = list(visible_columns.keys())[i+1]
                        updated_prefs[col_id]["order"] = i + 1
                        updated_prefs[next_col_id]["order"] = i
                        
                        # Save and refresh
                        save_column_preferences(updated_prefs)
                        st.rerun()
                else:
                    st.write("")  # Placeholder for alignment
            
            with remove_col:
                # Remove button
                if st.button("×", key=f"remove_{col_id}"):
                    # Hide column
                    updated_prefs[col_id]["visible"] = False
                    
                    # Reorder remaining columns
                    reorder_columns(updated_prefs)
                    
                    # Save and refresh
                    save_column_preferences(updated_prefs)
                    st.rerun()
    
    with col2:
        st.markdown("#### Rejtett oszlopok")
        
        # List of hidden columns
        hidden_columns = {col_id: DEFAULT_COLUMNS[col_id] for col_id in user_prefs.keys() if not user_prefs[col_id]["visible"]}
        # Sort alphabetically by display name
        hidden_columns = dict(sorted(hidden_columns.items(), key=lambda x: x[1]["display_name"]))
        
        # Display hidden columns with add buttons
        for col_id, col_info in hidden_columns.items():
            container = st.container()
            title_col, add_col = container.columns([8, 1])
            
            with title_col:
                st.markdown(f"**{col_info['display_name']}**")
            
            with add_col:
                # Add button
                if st.button("+", key=f"add_{col_id}"):
                    # Show column
                    updated_prefs[col_id]["visible"] = True
                    
                    # Add it to the end
                    updated_prefs[col_id]["order"] = max([prefs["order"] for col_id, prefs in user_prefs.items() if prefs["visible"]], default=-1) + 1
                    
                    # Save and refresh
                    save_column_preferences(updated_prefs)
                    st.rerun()
    
    # Action buttons at the bottom
    st.markdown("---")
    col1, col2 = st.columns(2)
    
    with col1:
        # Reset to defaults
        if st.button("Alapértelmezett beállítások", type="secondary", key=generate_unique_key("reset_columns")):
            # Reset preferences to defaults
            reset_preferences = {}
            for col_id, col_info in DEFAULT_COLUMNS.items():
                reset_preferences[col_id] = {
                    "visible": col_info["default_visible"],
                    "order": col_info["default_order"]
                }
            
            # Save and refresh
            save_column_preferences(reset_preferences)
            show_inline_success("Oszlopok visszaállítva az alapértelmezett beállításokra.")
            st.rerun()
    
    with col2:
        # Show all columns
        if st.button("Összes oszlop mutatása", key=generate_unique_key("show_all_columns")):
            # Make all columns visible
            for col_id in user_prefs.keys():
                updated_prefs[col_id]["visible"] = True
            
            # Keep the default order
            for col_id, col_info in DEFAULT_COLUMNS.items():
                if col_id in updated_prefs:
                    updated_prefs[col_id]["order"] = col_info["default_order"]
            
            # Save and refresh
            save_column_preferences(updated_prefs)
            show_inline_success("Minden oszlop megjelenítve.")
            st.rerun()

def reorder_columns(preferences: Dict[str, Dict[str, Any]]) -> None:
    """
    Reorder columns to have consecutive order values.
    
    Args:
        preferences: Column preferences to reorder
    """
    # Get visible columns
    visible_columns = {col_id: prefs for col_id, prefs in preferences.items() if prefs["visible"]}
    
    # Sort by current order
    visible_columns = dict(sorted(visible_columns.items(), key=lambda x: x[1]["order"]))
    
    # Reassign consecutive order values
    for i, col_id in enumerate(visible_columns.keys()):
        preferences[col_id]["order"] = i

def get_visible_columns() -> Dict[str, Dict[str, Any]]:
    """
    Get the list of visible columns in their display order.
    
    Returns:
        Dictionary of visible columns with their properties
    """
    # Get user preferences
    user_prefs = get_user_column_preferences()
    
    # Filter visible columns
    visible_columns = {}
    for col_id, prefs in user_prefs.items():
        if prefs["visible"] and col_id in DEFAULT_COLUMNS:
            # Combine preferences with column info
            visible_columns[col_id] = DEFAULT_COLUMNS[col_id].copy()
            visible_columns[col_id]["order"] = prefs["order"]
    
    # Sort by order
    return dict(sorted(visible_columns.items(), key=lambda x: x[1]["order"]))

def prepare_display_data(offers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Prepare data for display based on visible columns.
    
    Args:
        offers: Raw offer data
    
    Returns:
        Formatted data for display
    """
    # Get visible columns
    visible_columns = get_visible_columns()
    
    # Format the data
    display_data = []
    for offer in offers:
        row = {}
        for col_id, col_info in visible_columns.items():
            field = col_info["field"]
            formatter = col_info["formatter"]
            
            # Handle nested fields (e.g., "user.contact_name")
            if "." in field:
                parts = field.split(".")
                value = offer
                for part in parts:
                    if isinstance(value, dict) and part in value:
                        value = value[part]
                    else:
                        value = None
                        break
            else:
                value = offer.get(field)
            
            # Format the value
            try:
                # Check if this is a calculated field
                if col_info.get("calculated", False) and value is None:
                    # This is a lambda that needs the whole offer
                    formatted = formatter(offer)
                else:
                    formatted = formatter(value)
                row[col_info["display_name"]] = formatted
            except Exception as e:
                # If formatting fails, use the raw value or a default
                logger.warning(f"Error formatting {field}: {e}")
                row[col_info["display_name"]] = value if value is not None else "-"
        
        display_data.append(row)
    
    return display_data

def render_column_customization_ui() -> None:
    """
    Render the column customization UI.
    """
    with st.expander("Oszlopok testreszabása", expanded=False):
        render_column_chooser()

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Column Customization Test", layout="wide")
    
    st.title("Column Customization Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "updated_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quality_grade": {"name": "I. osztály"},
            "quantity_in_kg": 500,
            "price": 350,
            "confirmed_quantity": None,
            "confirmed_price": None,
            "user": {"contact_name": "Termelő Tamás"},
            "created_by_user": {"contact_name": "Admin User"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "updated_at": "2025-04-07T09:15:00",
            "product_type": {"name": "Körte"},
            "quality_grade": {"name": "I. osztály"},
            "quantity_in_kg": 300,
            "price": 450,
            "confirmed_quantity": 250,
            "confirmed_price": 430,
            "user": {"contact_name": "Mezőgazda Márton"},
            "created_by_user": {"contact_name": "Admin User"},
            "description": "Kiváló minőségű, zamatos körte."
        }
    ]
    
    # Render column customization UI
    render_column_customization_ui()
    
    # Display sample data using customized columns
    st.markdown("### Sample Data with Customized Columns")
    
    # Get formatted data
    display_data = prepare_display_data(sample_offers)
    
    # Convert to DataFrame for display
    import pandas as pd
    df = pd.DataFrame(display_data)
    
    # Display in a table
    st.dataframe(df, use_container_width=True)