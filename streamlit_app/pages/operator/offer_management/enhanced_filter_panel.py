"""
Enhanced filter panel that utilizes existing advanced_filters.py
and integrates with the new filter management system
"""

import streamlit as st
import logging
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from .filter_manager import FilterManager
from .api_parameter_converter import APIParameterConverter

logger = logging.getLogger(__name__)

# Try to import existing advanced filters
try:
    from .advanced_filters import render_advanced_filter_panel
    HAS_ADVANCED_FILTERS = True
    logger.info("Successfully imported advanced_filters module")
except ImportError:
    logger.warning("Could not import advanced_filters, using fallback")
    HAS_ADVANCED_FILTERS = False

# Try to import modern UI
try:
    from .modern_ui_simple import inject_modern_styles, render_modern_filter_panel
    HAS_MODERN_UI = True
    logger.info("Successfully imported modern_ui_simple module")
except ImportError:
    logger.warning("Could not import modern_ui_simple, using fallback")
    HAS_MODERN_UI = False
    
    def inject_modern_styles():
        """Fallback styles"""
        pass
    
    def render_modern_filter_panel():
        """Fallback filter panel"""
        return {}


class EnhancedFilterPanel:
    """Enhanced filter panel with proper state management and chain validation"""
    
    def __init__(self, filter_manager: FilterManager):
        self.filter_manager = filter_manager
        self.api_converter = APIParameterConverter()
        self.validation_history = []
    
    def render(self) -> Dict[str, Any]:
        """Render the complete filter panel and return filter values"""
        # Inject modern styles
        inject_modern_styles()
        
        # Choose rendering method based on available modules
        if HAS_ADVANCED_FILTERS:
            filters = self._render_advanced_filter_panel()
        elif HAS_MODERN_UI:
            filters = self._render_modern_filter_panel()
        else:
            filters = self._render_fallback_filter_panel()
        
        # Validate and process filters through filter manager
        validated_filters = self.filter_manager.validate_filters(filters)
        
        # 🚨 CRITICAL: Comprehensive filter chain validation
        chain_validation = self.validate_filter_chain(filters, validated_filters)
        
        # Store validation history
        self.validation_history.append(chain_validation)
        
        # Display validation errors if any
        self._display_validation_feedback()
        
        # Display chain validation if there are issues
        if chain_validation['has_critical_issues']:
            self._display_chain_validation_errors(chain_validation)
        
        return validated_filters
    
    def _render_advanced_filter_panel(self) -> Dict[str, Any]:
        """Render using advanced filters module"""
        try:
            return render_advanced_filter_panel()
        except Exception as e:
            logger.error(f"Error rendering advanced filter panel: {e}")
            st.error("Hiba a fejlett szűrőpanel megjelenítésekor")
            return self._render_fallback_filter_panel()
    
    def _render_modern_filter_panel(self) -> Dict[str, Any]:
        """Render using modern UI module"""
        try:
            filters = render_modern_filter_panel()
            
            # 🚨 CRITICAL DEBUG: Log exactly what modern UI returns
            logger.critical(f"🔍 Modern UI returned filters: {filters}")
            
            # Check session state for modern_producer_filter specifically
            if 'modern_producer_filter' in st.session_state:
                modern_producer_value = st.session_state['modern_producer_filter']
                logger.critical(f"🔍 Session state modern_producer_filter: {modern_producer_value}")
                
                # Ensure producer_filter is properly set
                if modern_producer_value and modern_producer_value != ("Minden termelő", None):
                    filters['producer_filter'] = modern_producer_value
                    logger.critical(f"✅ Set producer_filter from session state: {modern_producer_value}")
            
            return filters
        except Exception as e:
            logger.error(f"Error rendering modern filter panel: {e}")
            st.error("Hiba a modern szűrőpanel megjelenítésekor")
            return self._render_fallback_filter_panel()
    
    def _render_fallback_filter_panel(self) -> Dict[str, Any]:
        """Fallback filter panel implementation"""
        st.subheader("🔍 Szűrők")
        
        filters = {}
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Producer filter
            producer_options = [("Minden termelő", None), ("Teszt Termelő", 1)]
            selected_producer = st.selectbox(
                "Termelő:",
                options=producer_options,
                format_func=lambda x: x[0],
                key="fallback_producer_filter"
            )
            filters['producer_filter'] = selected_producer
            
            # Date from
            from datetime import datetime, timedelta
            from_date = st.date_input(
                "Kezdő dátum:",
                value=datetime.now().date() - timedelta(days=30),
                key="fallback_from_date"
            )
            filters['from_date'] = from_date
            
            # Product filter
            product_filter = self._render_product_filter()
            filters.update(product_filter)
        
        with col2:
            # Status filter
            status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER"]
            selected_statuses = st.multiselect(
                "Státusz:",
                options=status_options,
                default=["CREATED"],
                key="fallback_status_filter"
            )
            filters['status'] = selected_statuses
            
            # Date to
            to_date = st.date_input(
                "Végső dátum:",
                value=datetime.now().date(),
                key="fallback_to_date"
            )
            filters['to_date'] = to_date
        
        # Search term
        search_term = st.text_input(
            "Keresés:",
            placeholder="Termelő vagy termék neve...",
            key="fallback_search"
        )
        filters['search_term'] = search_term
        
        # Reset button
        if st.button("🔄 Alaphelyzet", key="fallback_reset"):
            self._clear_fallback_filters()
            st.rerun()
        
        return filters
    
    def _get_cached_products(self) -> List[Dict[str, Any]]:
        """Terméktípusok lekérése cache-ből vagy API-ból"""
        if 'products_cache' in st.session_state:
            return st.session_state['products_cache']
        try:
            from streamlit_app.api.products_fixed import get_product_types
        except ImportError:
            from api.products_fixed import get_product_types
        success, products = get_product_types()
        if success and isinstance(products, list):
            st.session_state['products_cache'] = products
            return products
        return []

    def _render_product_filter(self) -> Dict[str, Any]:
        """Render product filter with ID storage"""
        products = self._get_cached_products()
        product_options = [("Minden termék", None)]
        for product in products:
            product_options.append((product['name'], product['id']))
        display_options = [opt[0] for opt in product_options]
        selected_index = st.selectbox(
            "Termék",
            range(len(display_options)),
            format_func=lambda x: display_options[x],
            key="fallback_product_filter"
        )
        selected_name, selected_id = product_options[selected_index]
        if selected_name != "Minden termék":
            return {'product_name': selected_name, 'product_type_id': selected_id}
        else:
            return {'product_name': None, 'product_type_id': None}

    def _clear_fallback_filters(self):
        """Clear fallback filter session state"""
        keys_to_clear = [
            'fallback_producer_filter',
            'fallback_status_filter',
            'fallback_from_date',
            'fallback_to_date',
            'fallback_search'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
    
    def _display_validation_feedback(self):
        """Display validation errors and warnings"""
        errors = self.filter_manager.get_validation_errors()
        
        if errors:
            with st.expander("⚠️ Szűrő figyelmeztetések", expanded=False):
                for error in errors:
                    st.warning(error)
    
    def render_filter_summary(self, filters: Dict[str, Any]):
        """Render a summary of active filters"""
        if not self.filter_manager.has_active_filters(filters):
            st.info("Nincsenek aktív szűrők")
            return
        
        st.subheader("Aktív szűrők")
        
        # Producer filter
        if filters.get('producer_display_name'):
            st.write(f"👤 **Termelő:** {filters['producer_display_name']}")
        
        # Status filter
        if filters.get('status'):
            status_labels = {
                'CREATED': 'Létrehozva',
                'CONFIRMED_BY_COMPANY': 'Megerősítve',
                'ACCEPTED_BY_USER': 'Elfogadva',
                'REJECTED_BY_USER': 'Elutasítva',
                'FINALIZED': 'Véglegesítve'
            }
            if isinstance(filters['status'], list):
                # Filter out None and ensure all are strings
                status_names = [status_labels.get(s, s) for s in filters['status'] if isinstance(s, str) and s is not None]
                st.write(f"📋 **Státusz:** {', '.join(status_names)}")
        
        # Date range
        if filters.get('from_date') and filters.get('to_date'):
            st.write(f"📅 **Időszak:** {filters['from_date']} - {filters['to_date']}")
        
        # Product filter
        if filters.get('product_name'):
            st.write(f"🌾 **Termék:** {filters['product_name']}")
        
        # Search term
        if filters.get('search_term'):
            st.write(f"🔍 **Keresés:** {filters['search_term']}")
    
    def render_filter_actions(self):
        """Render filter action buttons"""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Alaphelyzet", key="enhanced_reset_filters"):
                self._reset_all_filters()
                st.success("Szűrők visszaállítva!")
                st.rerun()
        
        with col2:
            if st.button("🗑️ Cache törlés", key="enhanced_clear_cache"):
                self.filter_manager.clear_cache()
                st.success("Cache törölve!")
                st.rerun()
        
        with col3:
            if st.button("📊 Debug", key="enhanced_debug_info"):
                self._show_debug_info()
    
    def _reset_all_filters(self):
        """Reset all filter values"""
        # Clear modern UI filters
        modern_keys = [k for k in st.session_state.keys() if k.startswith('modern_')]
        for key in modern_keys:
            del st.session_state[key]
        
        # Clear fallback filters
        fallback_keys = [k for k in st.session_state.keys() if k.startswith('fallback_')]
        for key in fallback_keys:
            del st.session_state[key]
        
        # Clear filter manager cache
        self.filter_manager.clear_cache()
        
        logger.info("All filters reset")
    
    def _show_debug_info(self):
        """Show debug information in expandable section"""
        with st.expander("🔧 Filter Debug Information", expanded=True):
            # Current filters
            current_filters = self.filter_manager.get_ui_filters()
            st.subheader("Jelenlegi szűrők")
            st.json(current_filters)
            
            # Validation errors
            errors = self.filter_manager.get_validation_errors()
            if errors:
                st.subheader("Validációs hibák")
                for error in errors:
                    st.error(error)
            
            # Session state filter keys
            filter_keys = [k for k in st.session_state.keys() 
                          if any(prefix in k for prefix in ['modern_', 'fallback_', 'filter_'])]
            st.subheader("Session state kulcsok")
            for key in sorted(filter_keys):
                st.write(f"- `{key}`: {type(st.session_state[key])}")
            
            # Available modules
            st.subheader("Elérhető modulok")
            st.write(f"- Advanced Filters: {'✅' if HAS_ADVANCED_FILTERS else '❌'}")
            st.write(f"- Modern UI: {'✅' if HAS_MODERN_UI else '❌'}")
    
    def get_filter_state_export(self) -> Dict[str, Any]:
        """Export current filter state for debugging"""
        return {
            'current_filters': self.filter_manager.get_ui_filters(),
            'validation_errors': self.filter_manager.get_validation_errors(),
            'has_active_filters': self.filter_manager.has_active_filters(self.filter_manager.get_ui_filters()),
            'available_modules': {
                'advanced_filters': HAS_ADVANCED_FILTERS,
                'modern_ui': HAS_MODERN_UI
            },
            'session_state_filter_keys': [k for k in st.session_state.keys() 
                                        if any(prefix in k for prefix in ['modern_', 'fallback_', 'filter_'])]
        }
    
    def validate_filter_chain(self, ui_filters: Dict[str, Any], validated_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Teljes szűrő lánc validálása minden lépésben"""
        from datetime import datetime
        
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'ui_filters': ui_filters.copy(),
            'validated_filters': validated_filters.copy(),
            'api_conversion': {},
            'validation_errors': [],
            'mapping_issues': [],
            'critical_issues': [],
            'has_critical_issues': False,
            'producer_validation': {}
        }
        
        try:
            # Step 1: UI to Validated filters check
            ui_to_validated_issues = self._validate_ui_to_validated_mapping(ui_filters, validated_filters)
            validation_report['mapping_issues'].extend(ui_to_validated_issues)
            
            # Step 2: Convert to API parameters
            try:
                api_params = self.api_converter.convert_filters(validated_filters)
                validation_report['api_conversion'] = api_params
                
                # Step 3: Validate API conversion
                api_conversion_issues = self._validate_api_conversion(validated_filters, api_params)
                validation_report['validation_errors'].extend(api_conversion_issues)
                
            except Exception as e:
                validation_report['critical_issues'].append(f"API conversion failed: {str(e)}")
                logger.error(f"API conversion failed in filter chain validation: {e}")
            
            # Step 4: Producer-specific validation
            if 'producer_id' in validated_filters or 'producer_display_name' in validated_filters:
                producer_validation = self._validate_producer_chain(validated_filters, validation_report.get('api_conversion', {}))
                validation_report['producer_validation'] = producer_validation
                
                if producer_validation.get('has_critical_issues'):
                    validation_report['critical_issues'].extend(producer_validation.get('issues', []))
            
            # Determine if there are critical issues
            validation_report['has_critical_issues'] = (
                len(validation_report['critical_issues']) > 0 or
                len(validation_report['validation_errors']) > 0 or
                validation_report['producer_validation'].get('has_critical_issues', False)
            )
            
            logger.info(f"Filter chain validation completed: {len(validation_report['validation_errors'])} errors, "
                       f"{len(validation_report['critical_issues'])} critical issues")
            
        except Exception as e:
            validation_report['critical_issues'].append(f"Filter chain validation failed: {str(e)}")
            validation_report['has_critical_issues'] = True
            logger.error(f"Filter chain validation exception: {e}")
        
        return validation_report
    
    def _validate_ui_to_validated_mapping(self, ui_filters: Dict[str, Any], validated_filters: Dict[str, Any]) -> List[str]:
        """UI szűrők és validált szűrők közötti leképezés ellenőrzése"""
        issues = []
        
        # Check for missing keys
        for ui_key in ui_filters:
            if ui_key not in validated_filters and ui_filters[ui_key] is not None:
                issues.append(f"UI filter '{ui_key}' missing in validated filters")
        
        # Check for value changes
        for key in validated_filters:
            if key in ui_filters:
                ui_value = ui_filters[key]
                validated_value = validated_filters[key]
                
                # Check for unexpected transformations
                if ui_value != validated_value and not self._is_valid_transformation(key, ui_value, validated_value):
                    issues.append(f"Unexpected transformation for '{key}': {ui_value} -> {validated_value}")
        
        return issues
    
    def _validate_api_conversion(self, validated_filters: Dict[str, Any], api_params: Dict[str, Any]) -> List[str]:
        """Validált szűrők és API paraméterek közötti konverzió ellenőrzése"""
        errors = []
        
        # Producer ID conversion check - JAVÍTÁS: Backend user_id-t vár
        if 'producer_id' in validated_filters:
            ui_producer_id = validated_filters['producer_id']
            api_user_id = api_params.get('user_id')  # Backend user_id paramétert vár!
            
            if ui_producer_id != api_user_id:
                errors.append(f"Producer ID conversion error: {ui_producer_id} -> user_id={api_user_id}")
        
        # Status conversion check
        if 'status' in validated_filters:
            ui_status = validated_filters['status']
            api_status = api_params.get('status')
            
            if isinstance(ui_status, list) and len(ui_status) == 1:
                expected_api_status = ui_status[0]
                if api_status != expected_api_status:
                    errors.append(f"Status conversion error: {ui_status} -> {api_status}")
        
        # Date conversion check
        for date_field, api_field in [('from_date', 'date_from'), ('to_date', 'date_to')]:
            if date_field in validated_filters:
                ui_date = validated_filters[date_field]
                api_date = api_params.get(api_field)
                
                if ui_date and hasattr(ui_date, 'strftime'):
                    expected_api_date = ui_date.strftime('%Y-%m-%d')
                    if api_date != expected_api_date:
                        errors.append(f"Date conversion error for {date_field}: {ui_date} -> {api_date}")
        
        return errors
    
    def _validate_producer_chain(self, validated_filters: Dict[str, Any], api_params: Dict[str, Any]) -> Dict[str, Any]:
        """Termelő specifikus lánc validáció"""
        validation = {
            'ui_producer_id': validated_filters.get('producer_id'),
            'ui_producer_name': validated_filters.get('producer_display_name'),
            'api_user_id': api_params.get('user_id'),  # Backend user_id paramétert vár!
            'has_critical_issues': False,
            'issues': []
        }
        
        # Check ID consistency
        if validation['ui_producer_id'] != validation['api_user_id']:
            validation['has_critical_issues'] = True
            validation['issues'].append(f"Producer ID mismatch: UI={validation['ui_producer_id']}, API user_id={validation['api_user_id']}")
        
        # Check producer existence in cache
        if validation['api_user_id'] and 'producers_cache' in st.session_state:
            producers = st.session_state['producers_cache']
            producer_found = False
            
            for display_name, producer_id in producers:
                if producer_id == validation['api_user_id']:
                    producer_found = True
                    if validation['ui_producer_name'] and display_name != validation['ui_producer_name']:
                        validation['issues'].append(f"Producer name mismatch: UI='{validation['ui_producer_name']}', Cache='{display_name}'")
                    break
            
            if not producer_found:
                validation['has_critical_issues'] = True
                validation['issues'].append(f"Producer ID {validation['api_user_id']} not found in cache")
        
        return validation
    
    def _is_valid_transformation(self, key: str, ui_value: Any, validated_value: Any) -> bool:
        """Ellenőrzi, hogy egy transzformáció érvényes-e"""
        # List to single value conversion (for status multiselect)
        if isinstance(ui_value, list) and len(ui_value) == 1 and ui_value[0] == validated_value:
            return True
        # Date object to string conversion
        # Avoid strftime on lists
        if hasattr(ui_value, 'strftime') and not isinstance(ui_value, list) and isinstance(validated_value, str):
            return ui_value.strftime('%Y-%m-%d') == validated_value
        # Empty list to None conversion
        if isinstance(ui_value, list) and len(ui_value) == 0 and validated_value is None:
            return True
        return False
    
    def _display_chain_validation_errors(self, chain_validation: Dict[str, Any]):
        """Lánc validációs hibák megjelenítése"""
        with st.expander("🚨 KRITIKUS SZŰRŐ HIBÁK", expanded=True):
            st.error("**Szűrő lánc validációs hibák találhatók!**")
            
            # Critical issues
            if chain_validation['critical_issues']:
                st.write("**Kritikus problémák:**")
                for issue in chain_validation['critical_issues']:
                    st.error(f"❌ {issue}")
            
            # Validation errors
            if chain_validation['validation_errors']:
                st.write("**Validációs hibák:**")
                for error in chain_validation['validation_errors']:
                    st.warning(f"⚠️ {error}")
            
            # Producer validation issues
            producer_val = chain_validation.get('producer_validation', {})
            if producer_val.get('has_critical_issues'):
                st.write("**Termelő validációs problémák:**")
                for issue in producer_val.get('issues', []):
                    st.error(f"❌ {issue}")
            
            # Show detailed info
            st.write("**Debug információk:**")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**UI Szűrők:**")
                ui_filters = chain_validation.get('ui_filters', {})
                for key, value in ui_filters.items():
                    if value is not None:
                        st.write(f"- {key}: {value}")
            
            with col2:
                st.write("**API Paraméterek:**")
                api_conversion = chain_validation.get('api_conversion', {})
                for key, value in api_conversion.items():
                    st.write(f"- {key}: {value}")
    
    def get_validation_history(self) -> List[Dict[str, Any]]:
        """Validációs előzmények lekérése"""
        return self.validation_history[-10:]  # Last 10 validations
    
    def get_current_filters(self) -> Dict[str, Any]:
        """🚨 CRITICAL FIX: Teljes session state scan"""
        
        current_filters = {}
        
        # Get ALL session state keys for investigation
        all_keys = list(st.session_state.keys())
        
        # Debug: Print all keys for investigation
        logger.critical(f"🔍 All session state keys: {all_keys}")
        
        # 🚨 CRITICAL: Find producer filter in ANY format
        producer_keys = [k for k in all_keys if 'producer' in k.lower()]
        logger.critical(f"🔍 Found producer-related keys: {producer_keys}")
        
        # First check for modern_producer_filter specifically (widget value)
        if 'modern_producer_filter' in st.session_state:
            widget_value = st.session_state['modern_producer_filter']
            logger.critical(f"🔍 Widget modern_producer_filter: {widget_value} (type: {type(widget_value)})")
            
            if widget_value not in [None, '', [], False, ('', ''), ("Minden termelő", None)]:
                current_filters['producer_filter'] = widget_value
                logger.critical(f"✅ Found producer filter from widget: {widget_value}")
        
        # If not found in widget, check other producer keys
        if 'producer_filter' not in current_filters:
            for pk in producer_keys:
                value = st.session_state[pk]
                logger.critical(f"🔍 Checking {pk}: {value} (type: {type(value)})")
                
                if value not in [None, '', [], False, ('', ''), ("Minden termelő", None)]:
                    current_filters['producer_filter'] = value
                    logger.critical(f"✅ Found producer filter: {pk} = {value}")
                    break
        
        # 🚨 FALLBACK: Reconstruct from separate producer_id/producer_display_name
        if 'producer_filter' not in current_filters:
            producer_id = None
            producer_name = None
            
            # Check for separate id and name fields
            for key in all_keys:
                if 'producer_id' in key.lower() and key not in producer_keys:
                    value = st.session_state[key]
                    if value not in [None, '', 0]:
                        producer_id = value
                        logger.critical(f"🔍 Found producer_id: {key} = {value}")
                        
                if 'producer' in key.lower() and 'name' in key.lower():
                    value = st.session_state[key]
                    if value not in [None, '', "Minden termelő"]:
                        producer_name = value
                        logger.critical(f"🔍 Found producer_name: {key} = {value}")
            
            # Reconstruct tuple if both found
            if producer_id is not None and producer_name is not None:
                current_filters['producer_filter'] = (producer_name, producer_id)
                logger.critical(f"✅ Reconstructed producer filter: ({producer_name}, {producer_id})")
        
        # Collect all modern_ filters from session state
        for key in st.session_state:
            if key.startswith('modern_'):
                value = st.session_state[key]
                
                # Skip empty values
                if value not in [None, '', [], False, ('', '')]:
                    # Convert key name (remove 'modern_' prefix)
                    filter_key = key.replace('modern_', '')
                    current_filters[filter_key] = value
                    logger.debug(f"✅ Modern filter: {filter_key} = {value}")
        
        # 🚨 CRITICAL: Additional fallback scan for any filter-like keys
        other_filter_patterns = ['filter', 'search', 'status', 'date', 'product']
        for pattern in other_filter_patterns:
            matching_keys = [k for k in all_keys if pattern in k.lower() and k not in producer_keys]
            for key in matching_keys:
                value = st.session_state[key]
                if value not in [None, '', [], False, ('', '')] and key not in current_filters:
                    # Map to standard filter names
                    if 'producer' in key.lower():
                        current_filters['producer_filter'] = value
                    elif 'search' in key.lower():
                        current_filters['search_term'] = value
                    elif 'status' in key.lower():
                        current_filters['status'] = value
                    elif 'date' in key.lower():
                        if 'from' in key.lower():
                            current_filters['from_date'] = value
                        elif 'to' in key.lower():
                            current_filters['to_date'] = value
                    elif 'product' in key.lower():
                        current_filters['product_filter'] = value
                    
                    logger.debug(f"✅ Fallback filter found: {key} = {value}")
        
        # Also collect other filter-related keys
        filter_keys = [
            'enable_date_filter',
            'enable_status_filter', 
            'enable_producer_filter',
            'enable_product_filter'
        ]
        
        for key in filter_keys:
            if key in st.session_state:
                current_filters[key] = st.session_state[key]
        
        logger.critical(f"🎯 FINAL COLLECTED FILTERS: {current_filters}")
        
        # 🚨 EMERGENCY CHECK: If no producer filter found but we have producer-related keys
        if 'producer_filter' not in current_filters and producer_keys:
            logger.error(f"🚨 EMERGENCY: Producer keys exist {producer_keys} but no producer_filter set!")
            for pk in producer_keys:
                value = st.session_state[pk]
                logger.error(f"🚨 Emergency check {pk}: {value}")
        
        return current_filters
    
    def get_ui_filters(self) -> Dict[str, Any]:
        """Alias for get_current_filters for backward compatibility"""
        return self.get_current_filters()
    
    def get_active_filter_summary(self) -> str:
        """Aktív szűrők összefoglalójának lekérése"""
        
        filters = self.get_current_filters()
        active_filters = []
        
        for key, value in filters.items():
            if key.startswith('enable_'):
                continue
                
            if isinstance(value, tuple) and len(value) == 2:
                name, id_val = value
                active_filters.append(f"{key}: {name}")
            elif isinstance(value, list) and value:
                active_filters.append(f"{key}: {', '.join(map(str, value))}")
            elif value:
                active_filters.append(f"{key}: {value}")
        
        return ", ".join(active_filters) if active_filters else "Nincs aktív szűrő"
