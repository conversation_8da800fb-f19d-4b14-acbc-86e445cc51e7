"""
Form component implementations based on the component_base architecture.

This module provides reusable form components that follow the ComponentBase architecture.
These components handle common form field types, including text inputs, number inputs,
select fields, date pickers, and more.
"""
import streamlit as st
import logging
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List, Callable, Union, Tuple, TypeVar, cast

from .component_base import ComponentBase, FormComponent, FormGroup

# Setup logging
logger = logging.getLogger(__name__)


class TextInput(FormComponent):
    """
    Text input field component.
    
    A text input field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        value: str = "",
        placeholder: str = "",
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[str], Tuple[bool, str]]] = None,
        type: str = "default",
        max_chars: Optional[int] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a text input component.
        
        Args:
            label: The field label
            value: Initial field value
            placeholder: Placeholder text when the field is empty
            help_text: Optional help text to display below the field
            required: Whether the field is required
            validator: Optional validation function
            type: Input type (default, password, etc.)
            max_chars: Maximum number of characters allowed
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'initial_value': value,
            'placeholder': placeholder,
            'help_text': help_text,
            'required': required,
            'type': type,
            'max_chars': max_chars
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> str:
        """
        Render the text input field.
        
        Returns:
            The current value of the text input
        """
        # Get component properties
        label = self.props['label']
        placeholder = self.props.get('placeholder', '')
        help_text = self.props.get('help_text')
        type_ = self.props.get('type', 'default')
        max_chars = self.props.get('max_chars')
        
        # Get current state
        current_value = self.get_state_value('value', '')
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use the appropriate Streamlit input widget based on type
        if type_ == "password":
            value = st.text_input(
                label=label,
                value=current_value,
                placeholder=placeholder,
                help=display_help,
                max_chars=max_chars,
                type="password",
                key=f"{self.component_id}_input"
            )
        elif type_ == "area":
            value = st.text_area(
                label=label,
                value=current_value,
                placeholder=placeholder,
                help=display_help,
                max_chars=max_chars,
                key=f"{self.component_id}_input"
            )
        else:
            value = st.text_input(
                label=label,
                value=current_value,
                placeholder=placeholder,
                help=display_help,
                max_chars=max_chars,
                key=f"{self.component_id}_input"
            )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class NumberInput(FormComponent):
    """
    Number input field component.
    
    A numeric input field with validation, error handling, and range constraints.
    """
    
    def __init__(
        self,
        label: str,
        value: Optional[Union[int, float]] = None,
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        step: Union[int, float] = 1,
        format: Optional[str] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[Union[int, float]], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a number input component.
        
        Args:
            label: The field label
            value: Initial field value
            min_value: Minimum allowed value
            max_value: Maximum allowed value
            step: Step size for increment/decrement
            format: Format string for displaying the number
            help_text: Optional help text to display below the field
            required: Whether the field is required
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'initial_value': value,
            'min_value': min_value,
            'max_value': max_value,
            'step': step,
            'format': format,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Optional[Union[int, float]]:
        """
        Render the number input field.
        
        Returns:
            The current value of the number input
        """
        # Get component properties
        label = self.props['label']
        min_value = self.props.get('min_value')
        max_value = self.props.get('max_value')
        step = self.props.get('step', 1)
        format_ = self.props.get('format')
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value')
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit number_input widget
        value = st.number_input(
            label=label,
            value=current_value if current_value is not None else (min_value or 0),
            min_value=min_value,
            max_value=max_value,
            step=step,
            format=format_,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class SelectInput(FormComponent):
    """
    Select (dropdown) input field component.
    
    A dropdown selection field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        options: List[Any],
        value: Any = None,
        format_func: Optional[Callable[[Any], str]] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[Any], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a select input component.
        
        Args:
            label: The field label
            options: List of options to choose from
            value: Initially selected value
            format_func: Function to format the display of options
            help_text: Optional help text to display below the field
            required: Whether the field is required
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'options': options,
            'initial_value': value,
            'format_func': format_func,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Any:
        """
        Render the select input field.
        
        Returns:
            The currently selected value
        """
        # Get component properties
        label = self.props['label']
        options = self.props['options']
        format_func = self.props.get('format_func')
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value')
        error = self.get_state_value('error')
        
        # Find the index of the current value in options, or default to 0
        if current_value is not None and current_value in options:
            index = options.index(current_value)
        else:
            index = 0
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit selectbox widget
        value = st.selectbox(
            label=label,
            options=options,
            index=index,
            format_func=format_func or str,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class MultiSelectInput(FormComponent):
    """
    Multi-select input field component.
    
    A multi-selection field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        options: List[Any],
        default: List[Any] = None,
        format_func: Optional[Callable[[Any], str]] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[List[Any]], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a multi-select input component.
        
        Args:
            label: The field label
            options: List of options to choose from
            default: Initially selected values
            format_func: Function to format the display of options
            help_text: Optional help text to display below the field
            required: Whether at least one selection is required
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'options': options,
            'initial_value': default or [],
            'format_func': format_func,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=default or [],
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> List[Any]:
        """
        Render the multi-select input field.
        
        Returns:
            The list of currently selected values
        """
        # Get component properties
        label = self.props['label']
        options = self.props['options']
        format_func = self.props.get('format_func')
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value', [])
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit multiselect widget
        value = st.multiselect(
            label=label,
            options=options,
            default=current_value,
            format_func=format_func or str,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class DateInput(FormComponent):
    """
    Date input field component.
    
    A date picker field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        value: Optional[Union[date, datetime]] = None,
        min_value: Optional[Union[date, datetime]] = None,
        max_value: Optional[Union[date, datetime]] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[date], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a date input component.
        
        Args:
            label: The field label
            value: Initial date value
            min_value: Minimum allowed date
            max_value: Maximum allowed date
            help_text: Optional help text to display below the field
            required: Whether the field is required
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        # If the input is a datetime, convert to date for consistency
        if value and isinstance(value, datetime):
            value = value.date()
        if min_value and isinstance(min_value, datetime):
            min_value = min_value.date()
        if max_value and isinstance(max_value, datetime):
            max_value = max_value.date()
        
        props = {
            'label': label,
            'initial_value': value,
            'min_value': min_value,
            'max_value': max_value,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Optional[date]:
        """
        Render the date input field.
        
        Returns:
            The currently selected date
        """
        # Get component properties
        label = self.props['label']
        min_value = self.props.get('min_value')
        max_value = self.props.get('max_value')
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value')
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit date_input widget
        value = st.date_input(
            label=label,
            value=current_value or datetime.now().date(),
            min_value=min_value,
            max_value=max_value,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class DateRangeInput(FormComponent):
    """
    Date range input component with start and end date selection.
    """
    
    def __init__(
        self,
        label: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        min_value: Optional[date] = None,
        max_value: Optional[date] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[Tuple[date, date]], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a date range input component.
        
        Args:
            label: The field label
            start_date: Initial start date
            end_date: Initial end date
            min_value: Minimum allowed date
            max_value: Maximum allowed date
            help_text: Optional help text to display below the field
            required: Whether the field is required
            validator: Optional validation function for validating the date range
            component_id: Optional unique ID for the component
        """
        # Default values
        today = datetime.now().date()
        if start_date is None:
            start_date = today - timedelta(days=30)
        if end_date is None:
            end_date = today
        
        props = {
            'label': label,
            'initial_value': (start_date, end_date),
            'min_value': min_value,
            'max_value': max_value,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=(start_date, end_date),
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Tuple[date, date]:
        """
        Render the date range input field.
        
        Returns:
            A tuple of (start_date, end_date)
        """
        # Get component properties
        label = self.props['label']
        min_value = self.props.get('min_value')
        max_value = self.props.get('max_value')
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value', (datetime.now().date() - timedelta(days=30), datetime.now().date()))
        start_date, end_date = current_value
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Create a subheader for the date range
        st.markdown(f"**{label}**")
        
        # Create columns for start and end dates
        col1, col2 = st.columns(2)
        
        # Render start date input
        with col1:
            new_start_date = st.date_input(
                "Kezdő dátum",
                value=start_date,
                min_value=min_value,
                max_value=max_value or end_date,
                help="A tartomány kezdő dátuma",
                key=f"{self.component_id}_start_date"
            )
        
        # Render end date input
        with col2:
            new_end_date = st.date_input(
                "Végső dátum",
                value=end_date,
                min_value=min_value or new_start_date,
                max_value=max_value,
                help="A tartomány végső dátuma",
                key=f"{self.component_id}_end_date"
            )
        
        # Validate date range
        if new_start_date > new_end_date:
            st.warning("A kezdő dátum nem lehet a végső dátum után.")
            new_start_date = new_end_date
        
        new_value = (new_start_date, new_end_date)
        
        # Update state if value changed
        if new_value != current_value:
            self.set_value(new_value)
        
        # Display help text
        if display_help:
            st.markdown(
                f"<p style='color: {display_help_color or 'grey'}; font-size: 0.85em;'>{display_help}</p>",
                unsafe_allow_html=True
            )
        
        return new_value


class CheckboxInput(FormComponent):
    """
    Checkbox input field component.
    
    A boolean checkbox field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        value: bool = False,
        help_text: Optional[str] = None,
        required: bool = False,
        validator: Optional[Callable[[bool], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a checkbox input component.
        
        Args:
            label: The field label
            value: Initial checkbox state
            help_text: Optional help text to display below the field
            required: Whether the field must be checked
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'initial_value': value,
            'help_text': help_text,
            'required': required
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> bool:
        """
        Render the checkbox input field.
        
        Returns:
            The current state of the checkbox (True/False)
        """
        # Get component properties
        label = self.props['label']
        help_text = self.props.get('help_text')
        
        # Get current state
        current_value = self.get_state_value('value', False)
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit checkbox widget
        value = st.checkbox(
            label=label,
            value=current_value,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class RadioInput(FormComponent):
    """
    Radio button input field component.
    
    A radio button selection field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        options: List[Any],
        value: Any = None,
        format_func: Optional[Callable[[Any], str]] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        horizontal: bool = False,
        validator: Optional[Callable[[Any], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a radio input component.
        
        Args:
            label: The field label
            options: List of options to choose from
            value: Initially selected value
            format_func: Function to format the display of options
            help_text: Optional help text to display below the field
            required: Whether the field is required
            horizontal: Whether to display radio buttons horizontally
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'options': options,
            'initial_value': value,
            'format_func': format_func,
            'help_text': help_text,
            'required': required,
            'horizontal': horizontal
        }
        
        # Set default value to first option if not specified
        if value is None and options:
            value = options[0]
        
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Any:
        """
        Render the radio input field.
        
        Returns:
            The currently selected value
        """
        # Get component properties
        label = self.props['label']
        options = self.props['options']
        format_func = self.props.get('format_func')
        help_text = self.props.get('help_text')
        horizontal = self.props.get('horizontal', False)
        
        # Get current state
        current_value = self.get_state_value('value')
        error = self.get_state_value('error')
        
        # Initialize index to 0 if current_value not in options
        if current_value is not None and current_value in options:
            index = options.index(current_value)
        else:
            index = 0
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit radio widget
        value = st.radio(
            label=label,
            options=options,
            index=index,
            format_func=format_func or str,
            help=display_help,
            horizontal=horizontal,
            key=f"{self.component_id}_input"
        )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class SliderInput(FormComponent):
    """
    Slider input field component.
    
    A range slider field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        min_value: Union[int, float],
        max_value: Union[int, float],
        value: Optional[Union[int, float, Tuple[int, int], Tuple[float, float]]] = None,
        step: Optional[Union[int, float]] = None,
        format: Optional[str] = None,
        help_text: Optional[str] = None,
        required: bool = False,
        is_range: bool = False,
        validator: Optional[Callable[[Union[float, Tuple[float, float]]], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a slider input component.
        
        Args:
            label: The field label
            min_value: Minimum slider value
            max_value: Maximum slider value
            value: Initial slider value (can be single value or tuple for range)
            step: Step size for slider movement
            format: Format string for displaying values
            help_text: Optional help text to display below the field
            required: Whether the field is required
            is_range: Whether this is a range slider
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        # Handle default values
        if value is None:
            if is_range:
                value = (min_value, max_value)
            else:
                value = min_value
        
        props = {
            'label': label,
            'min_value': min_value,
            'max_value': max_value,
            'initial_value': value,
            'step': step,
            'format': format,
            'help_text': help_text,
            'required': required,
            'is_range': is_range
        }
        super().__init__(
            label=label,
            value=value,
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> Union[float, Tuple[float, float]]:
        """
        Render the slider input field.
        
        Returns:
            The current slider value or range
        """
        # Get component properties
        label = self.props['label']
        min_value = self.props['min_value']
        max_value = self.props['max_value']
        step = self.props.get('step')
        format_ = self.props.get('format')
        help_text = self.props.get('help_text')
        is_range = self.props.get('is_range', False)
        
        # Get current state
        current_value = self.get_state_value('value')
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        display_help_color = "salmon" if error else None
        
        # Use Streamlit slider widget
        if is_range:
            value = st.slider(
                label=label,
                min_value=min_value,
                max_value=max_value,
                value=current_value if current_value else (min_value, max_value),
                step=step,
                format=format_,
                help=display_help,
                key=f"{self.component_id}_input"
            )
        else:
            value = st.slider(
                label=label,
                min_value=min_value,
                max_value=max_value,
                value=current_value if current_value is not None else min_value,
                step=step,
                format=format_,
                help=display_help,
                key=f"{self.component_id}_input"
            )
        
        # Update the component state if value changed
        if value != current_value:
            self.set_value(value)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return value


class FileInput(FormComponent):
    """
    File upload input component.
    
    A file upload field with validation and error handling.
    """
    
    def __init__(
        self,
        label: str,
        type: Optional[List[str]] = None,
        accept_multiple_files: bool = False,
        help_text: Optional[str] = None,
        required: bool = False,
        max_file_size_mb: Optional[int] = None,
        validator: Optional[Callable[[List[Any]], Tuple[bool, str]]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize a file input component.
        
        Args:
            label: The field label
            type: List of accepted file types (e.g. ["png", "jpg", "jpeg"])
            accept_multiple_files: Whether multiple files can be uploaded
            help_text: Optional help text to display below the field
            required: Whether file upload is required
            max_file_size_mb: Maximum allowed file size in MB
            validator: Optional validation function
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'type': type,
            'accept_multiple_files': accept_multiple_files,
            'help_text': help_text,
            'required': required,
            'max_file_size_mb': max_file_size_mb
        }
        super().__init__(
            label=label,
            value=[],
            required=required,
            validator=validator,
            component_id=component_id
        )
        
        # Update props
        self.props.update(props)
    
    def render(self) -> List[Any]:
        """
        Render the file input field.
        
        Returns:
            List of uploaded file objects
        """
        # Get component properties
        label = self.props['label']
        type_ = self.props.get('type')
        accept_multiple_files = self.props.get('accept_multiple_files', False)
        help_text = self.props.get('help_text')
        max_file_size_mb = self.props.get('max_file_size_mb')
        
        # Get current state
        current_value = self.get_state_value('value', [])
        error = self.get_state_value('error')
        
        # Prepare help text with error if present
        display_help = error or help_text
        if max_file_size_mb and not error:
            if display_help:
                display_help += f" Max file size: {max_file_size_mb}MB"
            else:
                display_help = f"Max file size: {max_file_size_mb}MB"
        
        display_help_color = "salmon" if error else None
        
        # Use Streamlit file_uploader widget
        uploaded_files = st.file_uploader(
            label=label,
            type=type_,
            accept_multiple_files=accept_multiple_files,
            help=display_help,
            key=f"{self.component_id}_input"
        )
        
        # Ensure we always have a list, even for single file uploads
        if not accept_multiple_files and uploaded_files is not None:
            files_list = [uploaded_files]
        elif accept_multiple_files and uploaded_files is not None:
            files_list = uploaded_files
        else:
            files_list = []
        
        # Validate file size if max_file_size_mb is specified
        if max_file_size_mb and files_list:
            for file in files_list:
                # Check if file has size attribute
                if hasattr(file, 'size'):
                    size_mb = file.size / (1024 * 1024)
                    if size_mb > max_file_size_mb:
                        error_msg = f"File '{file.name}' exceeds the maximum size of {max_file_size_mb}MB"
                        self.set_state({'error': error_msg, 'is_valid': False})
                        st.error(error_msg)
                        files_list = []
                        break
        
        # Update the component state if value changed
        if files_list != current_value:
            self.set_value(files_list)
        
        # Display error status with colored text if there's an error
        if error and display_help_color:
            st.markdown(f"<p style='color: {display_help_color};'>{error}</p>", unsafe_allow_html=True)
        
        return files_list


# Helper form validation functions that can be used with the validator parameter

def validate_email(email: str) -> Tuple[bool, str]:
    """
    Validate an email address.
    
    Args:
        email: The email address to validate
        
    Returns:
        (is_valid, error_message) tuple
    """
    import re
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    if not email:
        return False, "Email address is required"
    if not re.match(pattern, email):
        return False, "Please enter a valid email address"
    return True, ""


def validate_required(value: Any) -> Tuple[bool, str]:
    """
    Validate that a value is not empty.
    
    Args:
        value: The value to check
        
    Returns:
        (is_valid, error_message) tuple
    """
    if value is None or value == "" or (isinstance(value, (list, tuple, dict)) and len(value) == 0):
        return False, "This field is required"
    return True, ""


def validate_min_length(min_length: int) -> Callable[[str], Tuple[bool, str]]:
    """
    Create a validator that checks minimum string length.
    
    Args:
        min_length: The minimum required length
        
    Returns:
        Validator function that can be used with form components
    """
    def validator(value: str) -> Tuple[bool, str]:
        if not value:
            return True, ""  # Empty is OK unless combined with required validator
        if len(value) < min_length:
            return False, f"This field must be at least {min_length} characters long"
        return True, ""
    return validator


def validate_max_length(max_length: int) -> Callable[[str], Tuple[bool, str]]:
    """
    Create a validator that checks maximum string length.
    
    Args:
        max_length: The maximum allowed length
        
    Returns:
        Validator function that can be used with form components
    """
    def validator(value: str) -> Tuple[bool, str]:
        if not value:
            return True, ""  # Empty is OK unless combined with required validator
        if len(value) > max_length:
            return False, f"This field must be at most {max_length} characters long"
        return True, ""
    return validator


def validate_numeric(value: str) -> Tuple[bool, str]:
    """
    Validate that a string contains only numeric characters.
    
    Args:
        value: The string to validate
        
    Returns:
        (is_valid, error_message) tuple
    """
    if not value:
        return True, ""  # Empty is OK unless combined with required validator
    if not value.isdigit():
        return False, "This field must contain only numeric characters"
    return True, ""


def combine_validators(*validators: Callable[[Any], Tuple[bool, str]]) -> Callable[[Any], Tuple[bool, str]]:
    """
    Combine multiple validators into a single validator function.
    
    Args:
        *validators: The validator functions to combine
        
    Returns:
        A combined validator function that runs all validators in sequence
    """
    def combined_validator(value: Any) -> Tuple[bool, str]:
        for validator in validators:
            is_valid, error_message = validator(value)
            if not is_valid:
                return False, error_message
        return True, ""
    return combined_validator