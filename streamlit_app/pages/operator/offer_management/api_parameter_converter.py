"""
Robust API parameter conversion with validation
Fixes the current issues in prepare_api_filters()
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, date
import logging
import json
import streamlit as st

logger = logging.getLogger(__name__)


def safe_json_display(data, title="JSON Data"):
    """Biztonságos JSON megjelenítés Streamlit-ben"""
    import streamlit as st
    
    def json_serializer(obj):
        """Custom JSON serializer date/datetime objektumokhoz"""
        if isinstance(obj, (datetime, date)):
            return obj.strftime('%Y-%m-%d')
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    try:
        # Streamlit kompatibilis formátum készítése
        display_data = {}
        for key, value in data.items():
            if hasattr(value, 'strftime'):  # Date/datetime objektum
                display_data[key] = value.strftime('%Y-%m-%d')
            elif isinstance(value, list):
                display_data[key] = value
            elif value is None:
                display_data[key] = None
            else:
                display_data[key] = value
        
        # Próbáljuk meg natív st.json()-nal
        st.json(display_data)
    except Exception:
        try:
            # Ha nem sikerül, akkor JSON string-ként
            json_str = json.dumps(data, default=json_serializer, indent=2, ensure_ascii=False)
            st.code(json_str, language='json')
        except Exception as e:
            # Végső fallback
            st.text(f"{title}: {data}")
            st.error(f"JSON megjelenítés hiba: {e}")


class APIParameterConverter:
    """Converts UI filter values to API parameters with validation and caching"""
    
    VALID_STATUSES = [
        'CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 
        'REJECTED_BY_USER', 'FINALIZED'
    ]
    
    def __init__(self):
        """Initialize converter with conversion cache"""
        self._conversion_cache = {}
        self._cache_timestamp = {}
    
    def _generate_cache_key(self, ui_filters: Dict[str, Any]) -> str:
        """Generate cache key for filter combination"""
        import json
        import hashlib
        from datetime import date, datetime
        
        def serialize_for_key(obj):
            """Convert objects to JSON-serializable format for cache key"""
            if isinstance(obj, (date, datetime)):
                return obj.strftime('%Y-%m-%d')
            elif isinstance(obj, dict):
                return {k: serialize_for_key(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize_for_key(item) for item in obj]
            else:
                return obj
        
        # Create normalized version for consistent key generation
        normalized = serialize_for_key(ui_filters)
        
        # Generate hash-based key
        key_string = json.dumps(normalized, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()[:8]
    
    def convert_filters(self, ui_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Main conversion method with comprehensive validation and caching"""
        
        # Generate cache key for this filter combination
        cache_key = self._generate_cache_key(ui_filters)
        current_time = datetime.now()
        
        # Check cache (valid for 10 seconds to avoid redundant conversions in same render cycle)
        if (cache_key in self._conversion_cache and 
            cache_key in self._cache_timestamp and
            (current_time - self._cache_timestamp[cache_key]).total_seconds() < 10):
            
            logger.debug(f"Using cached conversion for key: {cache_key[:8]}...")
            return self._conversion_cache[cache_key].copy()
        
        api_params = {}
        
        logger.info(f"Converting UI filters to API parameters: {ui_filters}")
        
        try:
            # Convert each filter type
            self._convert_producer_filter(ui_filters, api_params)
            self._convert_status_filter(ui_filters, api_params)
            self._convert_date_filters(ui_filters, api_params)
            self._convert_product_filter(ui_filters, api_params)
            self._convert_search_filter(ui_filters, api_params)
            
            # Remove empty values
            cleaned_params = self._clean_parameters(api_params)
            
            # Cache the result
            self._conversion_cache[cache_key] = cleaned_params.copy()
            self._cache_timestamp[cache_key] = current_time
            
            # Cleanup old cache entries (keep only last 20)
            if len(self._conversion_cache) > 20:
                oldest_keys = sorted(self._cache_timestamp.items(), key=lambda x: x[1])[:5]
                for old_key, _ in oldest_keys:
                    del self._conversion_cache[old_key]
                    del self._cache_timestamp[old_key]
            
            logger.info(f"Converted filters: {ui_filters} -> {cleaned_params}")
            return cleaned_params
            
        except Exception as e:
            logger.error(f"Error converting filters: {e}")
            return {}
    
    def _convert_producer_filter(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """Convert producer filter to API parameter"""
        
        # Keressük meg a producer filtert
        producer_filter = ui_filters.get('producer_filter')
        
        logger.critical(f"🔍 API CONVERTER: Processing producer_filter: {producer_filter}")
        
        if producer_filter and producer_filter != 'Minden termelő':
            producer_id = None
            
            # Ha tuple (név, id)
            if isinstance(producer_filter, tuple) and len(producer_filter) == 2:
                name, producer_id = producer_filter
                logger.critical(f"🔍 API CONVERTER: Found tuple producer: {name} (ID: {producer_id})")
            # Ha csak ID
            elif isinstance(producer_filter, (int, str)):
                producer_id = producer_filter
                logger.critical(f"🔍 API CONVERTER: Found direct producer ID: {producer_id}")
                
            # 🎯 KRITIKUS JAVÍTÁS: Backend user_id paramétert vár, nem producer_id-t!
            if producer_id:
                try:
                    # Backend dokumentáció szerint user_id paramétert kell küldeni
                    api_params['user_id'] = int(producer_id)
                    logger.critical(f"✅ Producer filter set as user_id: user_id={producer_id}")
                except (ValueError, TypeError):
                    logger.error(f"❌ Invalid producer ID: {producer_id}")
        else:
            logger.info(f"ℹ️ No producer filter to convert")
    
    def _validate_producer_exists(self, producer_id: int, expected_name: str = None) -> bool:
        """Validate that producer ID exists and matches expected name"""
        try:
            # Try to get producers from cache or API
            if 'producers_cache' in st.session_state:
                producers = st.session_state['producers_cache']
                
                # Check if producer_id exists in cache
                for display_name, cached_id in producers:
                    if cached_id == producer_id:
                        if expected_name and display_name != expected_name:
                            logger.warning(f"Producer ID {producer_id} found but name mismatch: expected '{expected_name}', got '{display_name}'")
                        else:
                            logger.info(f"Producer ID {producer_id} validated successfully: {display_name}")
                        return True
                
                logger.warning(f"Producer ID {producer_id} not found in cached producers")
                return False
            else:
                logger.warning(f"No producers cache available for validation")
                return True  # Assume valid if no cache available
                
        except Exception as e:
            logger.error(f"Error validating producer {producer_id}: {e}")
            return True  # Assume valid on error to avoid blocking
    
    def _convert_status_filter(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]):
        """Convert status filter with multi-select support - JAVÍTOTT verzió"""
        status = ui_filters.get('status')
        
        # 🔧 JAVÍTÁS: Csak ha van érték és nem üres lista
        if status and isinstance(status, list) and len(status) > 0:
            # Filter valid statuses
            valid_statuses = [s for s in status if s in self.VALID_STATUSES]
            if valid_statuses:
                api_params['status'] = ','.join(valid_statuses)
                logger.debug(f"Converted status list: {status} -> {','.join(valid_statuses)}")
            else:
                logger.warning(f"No valid statuses found in: {status}")
        elif isinstance(status, str) and status.strip():
            if status.strip() in self.VALID_STATUSES:
                api_params['status'] = status.strip()
                logger.debug(f"Converted status string: {status}")
            else:
                logger.warning(f"Invalid status: {status}")
    
    def _convert_date_filters(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]):
        """Convert date range filters - JAVÍTOTT verzió"""
        from_date = ui_filters.get('from_date')
        to_date = ui_filters.get('to_date')
        
        # 🔧 JAVÍTÁS: Csak ha ténylegesen be van állítva (nem None)
        if from_date is not None:
            try:
                if isinstance(from_date, (datetime, date)):
                    api_params['date_from'] = from_date.strftime('%Y-%m-%d')
                    logger.debug(f"Converted from_date: {from_date}")
                elif isinstance(from_date, str):
                    # Try to parse string date
                    parsed_date = datetime.fromisoformat(from_date.replace('Z', '+00:00'))
                    api_params['date_from'] = parsed_date.strftime('%Y-%m-%d')
                    logger.debug(f"Parsed and converted from_date: {from_date}")
            except (ValueError, AttributeError) as e:
                logger.error(f"Error converting from_date {from_date}: {e}")
        
        if to_date is not None:
            try:
                if isinstance(to_date, (datetime, date)):
                    api_params['date_to'] = to_date.strftime('%Y-%m-%d')
                    logger.debug(f"Converted to_date: {to_date}")
                elif isinstance(to_date, str):
                    # Try to parse string date
                    parsed_date = datetime.fromisoformat(to_date.replace('Z', '+00:00'))
                    api_params['date_to'] = parsed_date.strftime('%Y-%m-%d')
                    logger.debug(f"Parsed and converted to_date: {to_date}")
            except (ValueError, AttributeError) as e:
                logger.error(f"Error converting to_date {to_date}: {e}")
        
        # Validate date range
        if 'date_from' in api_params and 'date_to' in api_params:
            try:
                from_dt = datetime.strptime(api_params['date_from'], '%Y-%m-%d')
                to_dt = datetime.strptime(api_params['date_to'], '%Y-%m-%d')
                if from_dt > to_dt:
                    logger.warning(f"Invalid date range: {api_params['date_from']} > {api_params['date_to']}")
                    # Swap dates
                    api_params['date_from'], api_params['date_to'] = api_params['date_to'], api_params['date_from']
                    logger.info(f"Swapped dates: from={api_params['date_from']}, to={api_params['date_to']}")
            except ValueError as e:
                logger.error(f"Error validating date range: {e}")
    
    def _convert_product_filter(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]):
        """Convert product filter to product_type_id"""
        # Prefer product_type_id if present
        product_type_id = ui_filters.get('product_type_id')
        if product_type_id:
            api_params['product_type_id'] = product_type_id
            logger.info(f"✅ Product type ID set: {product_type_id}")
        else:
            # Fallback to product_name lookup
            product_name = ui_filters.get('product_name')
            if product_name and product_name != "Minden termék":
                found_id = self._find_product_type_id(product_name)
                if found_id:
                    api_params['product_type_id'] = found_id
                    logger.info(f"✅ Product filter converted: '{product_name}' -> {found_id}")
                else:
                    logger.warning(f"⚠️ Could not find product_type_id for: '{product_name}'")

    def _find_product_type_id(self, product_name: str) -> Optional[int]:
        """Find product_type_id by product name"""
        try:
            # Check cache first
            if 'products_cache' in st.session_state:
                for product in st.session_state['products_cache']:
                    if product.get('name', '').strip().lower() == product_name.strip().lower():
                        return product.get('id')
            
            # If not in cache, try to get from API
            from .api_client import get_product_types
            success, product_types = get_product_types()
            
            if success and isinstance(product_types, list):
                for product_type in product_types:
                    if product_type.get('name', '').strip().lower() == product_name.strip().lower():
                        return product_type.get('id')
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding product_type_id: {e}")
            return None
    
    def _convert_search_filter(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]):
        """Convert search term filter"""
        search_term = ui_filters.get('search_term')
        
        if search_term and isinstance(search_term, str):
            cleaned_search = search_term.strip()
            if cleaned_search:
                api_params['search'] = cleaned_search
                logger.debug(f"Converted search_term: {search_term}")
    
    def _clean_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Clean parameters - ensure producer_id is kept"""
        cleaned = {}
        
        # Kritikus paraméterek, amiket mindig meg kell tartani
        # JAVÍTÁS: user_id a helyes producer szűrő paraméter
        critical_params = ['user_id', 'product_type_id', 'status', 'date_from', 'date_to']
        
        for key, value in params.items():
            # Ne szűrjük ki a metadata kulcsokat
            if key.startswith('_'):
                cleaned[key] = value
                continue
                
            # Tartsuk meg a kritikus paramétereket
            if key in critical_params and value is not None:
                if isinstance(value, str) and value.strip():
                    cleaned[key] = value.strip()
                elif isinstance(value, (int, float)):
                    cleaned[key] = value
                elif isinstance(value, bool):
                    cleaned[key] = value
                elif isinstance(value, list) and value:
                    cleaned[key] = value
                logger.debug(f"✅ Critical parameter kept: {key} = {value}")
            # Más paraméterek esetén is tartsuk meg, ha nem üresek
            elif value is not None:
                if isinstance(value, str) and value.strip():
                    cleaned[key] = value.strip()
                elif isinstance(value, list) and value:
                    cleaned[key] = value
                elif isinstance(value, (int, float)):
                    cleaned[key] = value
                elif isinstance(value, bool):
                    cleaned[key] = value
        
        logger.critical(f"🔧 Parameter cleaning: {params} -> {cleaned}")
        return cleaned
    
    def validate_api_params(self, params: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate API parameters and return (is_valid, errors)"""
        errors = []
        
        # Validate producer_id
        if 'producer_id' in params:
            if not isinstance(params['producer_id'], int):
                errors.append(f"producer_id must be an integer, got {type(params['producer_id'])}")
        
        # Validate status
        if 'status' in params:
            status_str = params['status']
            if isinstance(status_str, str):
                statuses = status_str.split(',')
                invalid_statuses = [s for s in statuses if s not in self.VALID_STATUSES]
                if invalid_statuses:
                    errors.append(f"Invalid statuses: {invalid_statuses}")
        
        # Validate dates
        for date_param in ['date_from', 'date_to']:
            if date_param in params:
                try:
                    datetime.strptime(params[date_param], '%Y-%m-%d')
                except ValueError:
                    errors.append(f"Invalid date format for {date_param}: {params[date_param]}")
        
        # Validate date range
        if 'date_from' in params and 'date_to' in params:
            try:
                from_dt = datetime.strptime(params['date_from'], '%Y-%m-%d')
                to_dt = datetime.strptime(params['date_to'], '%Y-%m-%d')
                if from_dt > to_dt:
                    errors.append(f"date_from ({params['date_from']}) cannot be after date_to ({params['date_to']})")
            except ValueError:
                # Date format errors already caught above
                pass
        
        is_valid = len(errors) == 0
        
        if errors:
            logger.warning(f"API parameter validation errors: {errors}")
        else:
            logger.debug("API parameters validation passed")
        
        return is_valid, errors
    
    def get_filter_summary(self, ui_filters: Dict[str, Any]) -> str:
        """Generate a human-readable summary of active filters"""
        summary_parts = []
        
        if ui_filters.get('producer_display_name'):
            summary_parts.append(f"Termelő: {ui_filters['producer_display_name']}")
        
        if ui_filters.get('status'):
            status_labels = {
                'CREATED': 'Létrehozva',
                'CONFIRMED_BY_COMPANY': 'Megerősítve',
                'ACCEPTED_BY_USER': 'Elfogadva',
                'REJECTED_BY_USER': 'Elutasítva',
                'FINALIZED': 'Véglegesítve'
            }
            if isinstance(ui_filters['status'], list):
                status_names = []
                for s in ui_filters['status']:
                    if s is not None:
                        label = status_labels.get(s, s)
                        if label is not None:
                            status_names.append(str(label))
                summary_parts.append(f"Státusz: {', '.join(status_names)}")
        
        if ui_filters.get('from_date') and ui_filters.get('to_date'):
            summary_parts.append(f"Időszak: {ui_filters['from_date']} - {ui_filters['to_date']}")
        
        if ui_filters.get('product_name'):
            summary_parts.append(f"Termék: {ui_filters['product_name']}")
        
        if ui_filters.get('search_term'):
            summary_parts.append(f"Keresés: {ui_filters['search_term']}")
        
        return "; ".join(summary_parts) if summary_parts else "Nincs aktív szűrő"
    
    def test_filter_conversion(self):
        """Teszt funkció a szűrő konverzió ellenőrzésére"""
        import streamlit as st
        
        # Test data
        test_filters = {
            'producer_id': 42,  # Int érték
            'producer_display_name': 'Szabó Gábor',
            'status': ['ACCEPTED_BY_USER'],
            'product_name': 'Kaliforniai paprika',
            'from_date': datetime(2025, 4, 22).date(),
            'to_date': datetime(2025, 5, 22).date()
        }
        
        api_params = self.convert_filters(test_filters)
        
        st.write("### 🧪 Filter Conversion Test")
        st.write("**Bemenő szűrők:**")
        safe_json_display(test_filters, "Bemenő szűrők")
        st.write("**API paraméterek:**")
        safe_json_display(api_params, "API paraméterek")
        
        # Ellenőrzés - JAVÍTÁS: Backend user_id paramétert vár
        checks = {
            'user_id': api_params.get('user_id') == 42,  # Backend user_id-t vár!
            'status': api_params.get('status') == 'ACCEPTED_BY_USER',
            'product_name': api_params.get('product_name') == 'Kaliforniai paprika',
            'date_from': api_params.get('date_from') is not None,
            'date_to': api_params.get('date_to') is not None
        }
        
        st.write("**Ellenőrzés eredménye:**")
        for key, passed in checks.items():
            icon = "✅" if passed else "❌"
            st.write(f"{icon} {key}: {passed}")
        
        return checks
    
    def clear_conversion_cache(self):
        """Clear conversion cache"""
        cleared_count = len(self._conversion_cache)
        self._conversion_cache.clear()
        self._cache_timestamp.clear()
        logger.info(f"Conversion cache cleared: {cleared_count} entries removed")
        return cleared_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get conversion cache statistics"""
        cache_count = len(self._conversion_cache)
        if cache_count > 0:
            current_time = datetime.now()
            ages = [(current_time - timestamp).total_seconds() 
                   for timestamp in self._cache_timestamp.values()]
            avg_age = sum(ages) / len(ages)
            max_age = max(ages)
        else:
            avg_age = max_age = 0
        
        return {
            'cache_entries': cache_count,
            'average_age_seconds': avg_age,
            'max_age_seconds': max_age,
            'cache_keys': list(self._conversion_cache.keys())[:5],  # Show first 5
            'validation_available': 'producers_cache' in st.session_state if hasattr(st, 'session_state') else False
        }
    
    def validate_all_cached_producers(self) -> Dict[str, Any]:
        """Validate all producers in cache for debugging"""
        validation_report = {
            'total_producers': 0,
            'valid_producers': 0,
            'invalid_producers': [],
            'cache_available': False
        }
        
        try:
            if 'producers_cache' in st.session_state:
                producers = st.session_state['producers_cache']
                validation_report['cache_available'] = True
                validation_report['total_producers'] = len(producers)
                
                for display_name, producer_id in producers:
                    if isinstance(producer_id, int) and producer_id > 0:
                        validation_report['valid_producers'] += 1
                    else:
                        validation_report['invalid_producers'].append({
                            'name': display_name,
                            'id': producer_id,
                            'issue': f"Invalid ID format: {type(producer_id)}"
                        })
            
        except Exception as e:
            validation_report['error'] = str(e)
        
        return validation_report
    
    def validate_producer_filter_response(self, expected_user_id: int, results: List[Dict]) -> Dict[str, Any]:
        """Validálja hogy a producer szűrő tényleg működött az API válaszban"""
        
        validation = {
            'timestamp': datetime.now().isoformat(),
            'expected_user_id': expected_user_id,
            'total_results': len(results),
            'matching_offers': 0,
            'found_user_ids': set(),
            'validation_passed': False,
            'field_analysis': {},
            'issues': []
        }
        
        logger.critical(f"🔍 PRODUCER RESPONSE VALIDATION: Checking {len(results)} results for expected user_id={expected_user_id}")
        
        if not results:
            validation['issues'].append("No results returned from API")
            return validation
        
        # Analyze each offer for producer/user ID fields
        field_stats = {
            'user_id': {'count': 0, 'values': set()},
            'created_by_user_id': {'count': 0, 'values': set()},
            'user.id': {'count': 0, 'values': set()},
            'producer_id': {'count': 0, 'values': set()},
            'owner_id': {'count': 0, 'values': set()}
        }
        
        for offer in results:
            # Check multiple possible field names for user/producer ID
            user_id = offer.get('user_id')
            created_by_id = offer.get('created_by_user_id')
            user_obj_id = offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None
            producer_id = offer.get('producer_id')
            owner_id = offer.get('owner_id')
            
            # Track field usage and values
            if user_id is not None:
                field_stats['user_id']['count'] += 1
                field_stats['user_id']['values'].add(user_id)
                validation['found_user_ids'].add(user_id)
                
            if created_by_id is not None:
                field_stats['created_by_user_id']['count'] += 1
                field_stats['created_by_user_id']['values'].add(created_by_id)
                validation['found_user_ids'].add(created_by_id)
                
            if user_obj_id is not None:
                field_stats['user.id']['count'] += 1
                field_stats['user.id']['values'].add(user_obj_id)
                validation['found_user_ids'].add(user_obj_id)
                
            if producer_id is not None:
                field_stats['producer_id']['count'] += 1
                field_stats['producer_id']['values'].add(producer_id)
                validation['found_user_ids'].add(producer_id)
                
            if owner_id is not None:
                field_stats['owner_id']['count'] += 1
                field_stats['owner_id']['values'].add(owner_id)
                validation['found_user_ids'].add(owner_id)
            
            # Check if this offer matches the expected user ID
            offer_user_ids = [user_id, created_by_id, user_obj_id, producer_id, owner_id]
            if expected_user_id in [id for id in offer_user_ids if id is not None]:
                validation['matching_offers'] += 1
        
        # Convert sets to lists for JSON serialization
        for field, stats in field_stats.items():
            stats['values'] = list(stats['values'])
            
        validation['field_analysis'] = field_stats
        validation['found_user_ids'] = list(validation['found_user_ids'])
        
        # Determine if validation passed
        if validation['matching_offers'] == 0 and validation['total_results'] > 0:
            validation['validation_passed'] = False
            validation['issues'].append(
                f"CRITICAL: Expected user_id {expected_user_id} but found {validation['found_user_ids']}"
            )
            logger.error(f"🚨 PRODUCER FILTER FAILED: Expected {expected_user_id}, found {validation['found_user_ids']}")
        elif validation['matching_offers'] < validation['total_results']:
            validation['validation_passed'] = False
            validation['issues'].append(
                f"PARTIAL MATCH: Only {validation['matching_offers']}/{validation['total_results']} offers match expected user_id"
            )
            logger.warning(f"⚠️ PRODUCER FILTER PARTIAL: {validation['matching_offers']}/{validation['total_results']} matches")
        else:
            validation['validation_passed'] = True
            logger.critical(f"✅ PRODUCER FILTER OK: All {validation['matching_offers']} offers match expected user_id {expected_user_id}")
        
        # Recommend which field seems to be the primary user ID field
        primary_field = self._identify_primary_user_field(field_stats)
        validation['recommended_primary_field'] = primary_field
        
        return validation
    
    def _identify_primary_user_field(self, field_stats: Dict) -> str:
        """Identify which field seems to be the primary user ID field based on usage"""
        
        # Find field with highest usage count and most consistent values
        best_field = None
        best_score = 0
        
        for field_name, stats in field_stats.items():
            if stats['count'] > 0:
                # Score based on usage count and value consistency
                score = stats['count'] * (1.0 / len(stats['values']) if stats['values'] else 0)
                if score > best_score:
                    best_score = score
                    best_field = field_name
        
        return best_field or 'user_id'  # Default fallback