"""
User preferences module for offer management.
Handles loading and saving user-specific settings like default filters.
"""
import streamlit as st
import logging
import json
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple, Union
import uuid

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.session import get_user_id
    from streamlit_app.api.users import get_user_preferences, update_user_preferences
except ImportError:
    try:
        # Try regular app-relative import
        from utils.session import get_user_id
        from api.users import get_user_preferences, update_user_preferences
    except ImportError:
        # Minimal implementations if all imports fail
        logging.warning("Could not import necessary modules in user_preferences.py, using minimal implementations")
        
        def get_user_id():
            """Mock function to get the current user ID."""
            return st.session_state.get("user_id", None)
            
        def get_user_preferences(user_id=None):
            """Mock function to get user preferences."""
            # For development, store preferences in session state
            if not user_id:
                user_id = get_user_id()
                
            if not user_id:
                return None, "No user ID available"
                
            # Return mock data from session state
            preferences = st.session_state.get(f"user_preferences_{user_id}", {})
            return preferences, None
            
        def update_user_preferences(preferences, user_id=None):
            """Mock function to update user preferences."""
            if not user_id:
                user_id = get_user_id()
                
            if not user_id:
                return False, "No user ID available"
                
            # Store in session state for development
            st.session_state[f"user_preferences_{user_id}"] = preferences
            return True, None

# Setup logging
logger = logging.getLogger(__name__)

def get_current_user():
    """
    Get information about the current logged-in user.
    
    Returns:
        dict: User information including ID, role, etc., or None if not logged in
    """
    # Get user ID from session
    user_id = get_user_id()
    
    if not user_id:
        return None
        
    # In a real implementation, you might want to fetch more user details here
    return {
        "id": user_id,
        "role": st.session_state.get("user_role", "operator")
    }

def get_default_filter_for_user(user_id: Optional[str] = None) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """
    Get the default filter settings for a specific user.
    
    Args:
        user_id: Optional user ID. If not provided, will use the current user.
        
    Returns:
        Tuple of (filter_data, error_message)
    """
    if not user_id:
        user = get_current_user()
        if not user:
            return None, "User not logged in"
        user_id = user["id"]
    
    # Get user preferences from API
    preferences, error = get_user_preferences(user_id)
    
    if error:
        logger.error(f"Error fetching user preferences: {error}")
        return None, f"Error fetching user preferences: {error}"
    
    # Check if preferences has default filter
    if not preferences or "default_filter" not in preferences:
        return None, None  # No error, just no default filter set
    
    return preferences["default_filter"], None

def set_default_filter_for_user(filter_id: str, filter_data: Dict[str, Any], user_id: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """
    Set the default filter for a specific user.
    
    Args:
        filter_id: The ID of the filter to set as default
        filter_data: The filter data to save
        user_id: Optional user ID. If not provided, will use the current user.
        
    Returns:
        Tuple of (success, error_message)
    """
    if not user_id:
        user = get_current_user()
        if not user:
            return False, "User not logged in"
        user_id = user["id"]
    
    # Get existing preferences
    preferences, error = get_user_preferences(user_id)
    
    if error:
        logger.error(f"Error fetching user preferences: {error}")
        return False, f"Error fetching user preferences: {error}"
    
    # Initialize preferences if needed
    if not preferences:
        preferences = {}
    
    # Set default filter
    preferences["default_filter"] = {
        "id": filter_id,
        "filter_data": filter_data,
        "updated_at": datetime.now().isoformat()
    }
    
    # Update preferences
    success, error = update_user_preferences(preferences, user_id)
    
    if error:
        logger.error(f"Error updating user preferences: {error}")
        return False, f"Error updating user preferences: {error}"
    
    return True, None

def clear_default_filter_for_user(user_id: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """
    Clear the default filter for a specific user.
    
    Args:
        user_id: Optional user ID. If not provided, will use the current user.
        
    Returns:
        Tuple of (success, error_message)
    """
    if not user_id:
        user = get_current_user()
        if not user:
            return False, "User not logged in"
        user_id = user["id"]
    
    # Get existing preferences
    preferences, error = get_user_preferences(user_id)
    
    if error:
        logger.error(f"Error fetching user preferences: {error}")
        return False, f"Error fetching user preferences: {error}"
    
    # If no preferences or no default filter, nothing to clear
    if not preferences or "default_filter" not in preferences:
        return True, None
    
    # Remove default filter
    preferences.pop("default_filter", None)
    
    # Update preferences
    success, error = update_user_preferences(preferences, user_id)
    
    if error:
        logger.error(f"Error updating user preferences: {error}")
        return False, f"Error updating user preferences: {error}"
    
    return True, None

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="User Preferences Test", layout="wide")
    
    st.title("User Preferences Test")
    
    # Initialize session state for testing
    if "user_id" not in st.session_state:
        st.session_state["user_id"] = "test_user_123"
    
    # Display current user
    user = get_current_user()
    st.markdown("### Current User")
    st.write(user)
    
    # Test getting default filter
    st.markdown("### Default Filter")
    default_filter, error = get_default_filter_for_user()
    
    if error:
        st.error(f"Error getting default filter: {error}")
    elif default_filter:
        st.write(default_filter)
    else:
        st.info("No default filter set")
    
    # Test setting default filter
    st.markdown("### Set Default Filter")
    if st.button("Set Test Default Filter"):
        test_filter = {
            "basic_filters": {
                "status": "CREATED",
                "date_from": datetime.now().date().isoformat(),
                "date_to": datetime.now().date().isoformat()
            }
        }
        success, error = set_default_filter_for_user("test_filter_123", test_filter)
        
        if error:
            st.error(f"Error setting default filter: {error}")
        else:
            st.success("Default filter set successfully")
            st.rerun()
    
    # Test clearing default filter
    if default_filter and st.button("Clear Default Filter"):
        success, error = clear_default_filter_for_user()
        
        if error:
            st.error(f"Error clearing default filter: {error}")
        else:
            st.success("Default filter cleared successfully")
            st.rerun()