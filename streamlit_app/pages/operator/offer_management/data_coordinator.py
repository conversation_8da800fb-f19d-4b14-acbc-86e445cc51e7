"""
Coordinates data loading operations with proper caching and error handling
"""

import streamlit as st
import logging
import json
import sys
import traceback
from datetime import datetime, date
from collections import defaultdict

# Python version-independent typing imports
try:
    from typing import Dict, List, Any, Tuple, Set, Optional, Union
except ImportError:
    # Fallback for older Python versions
    from typing import Any, List, Optional, Union, Tuple, Set
    Dict = dict

# Additional safety check for Dict
if 'Dict' not in globals():
    Dict = dict

from .integrated_state import OfferStateManager
from .api_parameter_converter import APIParameterConverter
from .widget_key_manager import get_unique_widget_key, cleanup_widget_keys, emergency_widget_cleanup, generate_unique_widget_key, cleanup_widget_keys_in_session

logger = logging.getLogger(__name__)


def validate_typing_imports():
    """Validate that typing imports are working correctly"""
    try:
        # Test Dict usage
        test_dict: Dict[str, Any] = {"test": "value"}
        logger.debug("✅ Dict type hint validation successful")
        return True
    except NameError as e:
        logger.error(f"❌ Dict type hint validation failed: {e}")
        logger.info(f"Python version: {sys.version}")
        
        # Emergency fallback
        if 'Dict' not in globals():
            globals()['Dict'] = dict
            logger.warning("🔧 Applied emergency Dict fallback")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected typing validation error: {e}")
        return False


def safe_json_display(data, title="JSON Data"):
    """Biztonságos JSON megjelenítés Streamlit-ben"""
    
    def json_serializer(obj):
        """Custom JSON serializer date/datetime objektumokhoz"""
        if isinstance(obj, (datetime, date)):
            return obj.strftime('%Y-%m-%d')
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    try:
        # Streamlit kompatibilis formátum készítése
        display_data = {}
        for key, value in data.items():
            if hasattr(value, 'strftime'):  # Date/datetime objektum
                display_data[key] = value.strftime('%Y-%m-%d')
            elif isinstance(value, list):
                display_data[key] = value
            elif value is None:
                display_data[key] = None
            else:
                display_data[key] = value
        
        # Próbáljuk meg natív st.json()-nal
        st.json(display_data)
    except Exception:
        try:
            # Ha nem sikerül, akkor JSON string-ként
            json_str = json.dumps(data, default=json_serializer, indent=2, ensure_ascii=False)
            st.code(json_str, language='json')
        except Exception as e:
            # Végső fallback
            st.text(f"{title}: {data}")
            st.error(f"JSON megjelenítés hiba: {e}")


class DataCoordinator:
    """Coordinates data loading with caching and state management"""
    
    def __init__(self, state_manager: OfferStateManager):
        # 🚨 CRITICAL FIX: Validate typing imports before initialization
        if not validate_typing_imports():
            logger.warning("⚠️ Typing validation failed, proceeding with fallbacks")
        
        self.state_manager = state_manager
        self.api_converter = APIParameterConverter()
        
        # 🚨 CRITICAL FIX: Preventive cleanup of potentially duplicate widget keys
        self._cleanup_old_widget_keys()
    
    def _cleanup_old_widget_keys(self):
        """Preventive cleanup of old widget keys to prevent duplicates"""
        try:
            # List of widget key prefixes that might cause duplicates
            problematic_prefixes = [
                "trace_details_",
                "show_trace_",
                "trace_info_", 
                "debug_",
                "filter_debug_",
                "api_debug_"
            ]
            
            total_cleaned = 0
            for prefix in problematic_prefixes:
                cleaned = cleanup_widget_keys_in_session(prefix)
                total_cleaned += cleaned
            
            if total_cleaned > 0:
                logger.info(f"🧹 DataCoordinator init: Cleaned up {total_cleaned} old widget keys")
                
        except Exception as e:
            logger.warning(f"Widget cleanup failed during init: {e}")
    
    def load_offers(self, ui_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Load offers with smart retry mechanism for producer filters"""
        try:
            start_time = datetime.now()
            
            # Set loading state
            self.state_manager.set_loading_state('loading')
            
            # Validate filters first
            validated_filters = self.state_manager.filter_manager.validate_filters(ui_filters)
            
            # Check validation errors
            validation_errors = self.state_manager.filter_manager.get_validation_errors()
            if validation_errors:
                self._display_validation_errors(validation_errors)
            
            # Check if refresh is needed
            if not self.state_manager.should_reload_offers(validated_filters):
                cached_offers = self._get_cached_offers(validated_filters)
                if cached_offers:
                    self.state_manager.set_loading_state('success')
                    logger.info(f"Using filter-specific cached offers: {len(cached_offers)} items")
                    return cached_offers
            
            # 🚨 NEW: Use smart retry mechanism
            offers = self.load_offers_with_smart_retry(ui_filters)
            
            # Update cache
            self._update_cache(offers, validated_filters)
            
            # Update performance metrics
            load_time = (datetime.now() - start_time).total_seconds()
            self.state_manager.update_performance_metrics(load_time)
            
            # Set success state
            self.state_manager.set_loading_state('success')
            
            logger.info(f"Successfully loaded {len(offers)} offers in {load_time:.2f}s")
            return offers
            
        except Exception as e:
            error_msg = f"Error loading offers: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self.state_manager.set_loading_state('error', error_msg)
            st.error(error_msg)
            return []
    
    def load_offers_with_smart_retry(self, ui_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Load offers with smart retry mechanism for producer filters
        """
        # First attempt with current conversion
        api_params = self.convert_filters_to_api_params(ui_filters)
        success, results = self._load_offers_raw(api_params)
        
        if success:
            # Validate producer filter if present
            if 'producer_filter' in ui_filters:
                validation_result = self._validate_producer_results(
                    ui_filters['producer_filter'], 
                    results
                )
                
                if validation_result['is_valid']:
                    logger.info(f"✅ Producer filter validation passed: {len(results)} offers")
                    return results
                else:
                    # SMART RETRY with alternative parameters
                    logger.warning(f"⚠️ Producer filter validation failed: {validation_result['reason']}")
                    
                    return self._retry_with_alternative_parameters(ui_filters)
        
        return results if success else []

    def _load_offers_raw(self, api_params: Dict[str, Any]) -> Tuple[bool, List[Dict[str, Any]]]:
        """Raw offer loading with tracing"""
        
        # 🚨 CRITICAL: Real-time filter chain tracing
        trace_id = self._start_filter_trace({}, {}, api_params)
        
        # Validate API parameters
        is_valid, param_errors = self.api_converter.validate_api_params(api_params)
        if not is_valid:
            self._log_trace_error(trace_id, f"API parameter validation failed: {param_errors}")
            self._display_parameter_errors(param_errors)
            return False, []
        
        # Load from API with tracing
        offers = self._fetch_from_api_with_trace(api_params, trace_id)
        
        # 🚨 CRITICAL: Validate results consistency
        validation_result = self._validate_results_vs_filters(offers, api_params, trace_id)
        
        # Complete trace
        self._complete_filter_trace(trace_id, offers, 0.0, validation_result)
        
        return True, offers

    def _validate_producer_results(self, producer_filter: Any, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate that results match the producer filter"""
        
        validation = {
            'is_valid': True,
            'reason': '',
            'expected_producer_id': None,
            'found_producer_ids': [],
            'matching_offers': 0
        }
        
        if not isinstance(producer_filter, tuple) or len(producer_filter) != 2:
            validation['is_valid'] = False
            validation['reason'] = f"Invalid producer filter format: {producer_filter}"
            return validation
        
        name, producer_id = producer_filter
        
        try:
            expected_producer_id = int(producer_id)
            validation['expected_producer_id'] = expected_producer_id
        except (ValueError, TypeError):
            validation['is_valid'] = False
            validation['reason'] = f"Invalid producer ID: {producer_id}"
            return validation
        
        # Check results for matching producer ID
        found_producer_ids = set()
        matching_count = 0
        
        for offer in results:
            # Try multiple possible field names for producer ID
            offer_producer_id = (
                offer.get('user_id') or
                offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None or
                offer.get('producer_id')
            )
            
            if offer_producer_id:
                found_producer_ids.add(int(offer_producer_id))
                if int(offer_producer_id) == expected_producer_id:
                    matching_count += 1
        
        validation['found_producer_ids'] = list(found_producer_ids)
        validation['matching_offers'] = matching_count
        
        # CRITICAL CHECK: Zero matches means filter didn't work
        if matching_count == 0 and len(results) > 0:
            validation['is_valid'] = False
            validation['reason'] = f"Expected producer_id {expected_producer_id} but found {list(found_producer_ids)}"
        
        return validation

    def _retry_with_alternative_parameters(self, ui_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Retry with alternative parameter strategies
        """
        producer_data = ui_filters['producer_filter']
        name, producer_id = producer_data
        
        logger.info(f"🔄 Retrying producer filter with alternative strategies for {name} (ID: {producer_id})")
        
        # Strategy 1: user_id only
        retry_params = {'user_id': int(producer_id)}
        success, results = self._load_offers_raw(retry_params)
        
        if success and self._validate_producer_in_results(producer_id, results):
            logger.info(f"✅ Producer filter retry successful with user_id: {len(results)} offers")
            return results
        
        # Strategy 2: producer_id only  
        retry_params = {'producer_id': int(producer_id)}
        success, results = self._load_offers_raw(retry_params)
        
        if success and self._validate_producer_in_results(producer_id, results):
            logger.info(f"✅ Producer filter retry successful with producer_id: {len(results)} offers")
            return results
        
        # Strategy 3: Use diagnostic tool recommendation
        try:
            from .filter_diagnostic_tool import FilterDiagnosticTool
            diagnostic_tool = FilterDiagnosticTool(self.state_manager, self)
            recommendation = diagnostic_tool.recommend_producer_parameters(int(producer_id))
            
            if recommendation.get('validated') and recommendation.get('expected_results', 0) > 0:
                recommended_params = recommendation['parameters']
                success, results = self._load_offers_raw(recommended_params)
                
                if success:
                    logger.info(f"✅ Producer filter retry successful with diagnostic recommendation: {len(results)} offers")
                    return results
                    
        except Exception as e:
            logger.error(f"Diagnostic tool retry failed: {e}")
        
        # Strategy 4: Fallback - return original results with warning
        logger.warning(f"⚠️ All retry strategies failed, returning original results")
        original_params = self.convert_filters_to_api_params(ui_filters)
        success, results = self._load_offers_raw(original_params)
        
        return results if success else []

    def _validate_producer_in_results(self, producer_id: Any, results: List[Dict[str, Any]]) -> bool:
        """Quick validation that producer ID exists in results"""
        try:
            expected_id = int(producer_id)
            
            for result in results:
                result_producer_id = (
                    result.get('user_id') or
                    result.get('user', {}).get('id') if isinstance(result.get('user'), dict) else None or
                    result.get('producer_id')
                )
                
                if result_producer_id and int(result_producer_id) == expected_id:
                    return True
            
            return False
            
        except (ValueError, TypeError):
            return False
    
    def _get_cached_offers(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Get offers from cache with filter-specific lookup"""
        return self.state_manager.get_cached_offers(filters)
    
    def _fetch_from_api(self, api_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch offers from API with error handling and session cache"""
        try:
            # Import API client
            from .api_client import get_offers_with_cache

            logger.info(f"Making API call with parameters: {api_params}")

            # Use the cached API call
            success, result = get_offers_with_cache(api_params)

            if success and isinstance(result, list):
                logger.info(f"API call successful: {len(result)} offers received (with cache)")
                return result
            else:
                error_msg = f"API call failed: {result}"
                logger.error(error_msg)
                st.error(error_msg)
                return []

        except ImportError as e:
            error_msg = f"Could not import API client: {e}"
            logger.error(error_msg)
            st.error(error_msg)
            return []
        except Exception as e:
            error_msg = f"Unexpected error during API call: {e}"
            logger.error(error_msg, exc_info=True)
            st.error(error_msg)
            return []
    
    def _update_cache(self, offers: List[Dict[str, Any]], filters: Dict[str, Any]):
        """Update offer cache"""
        self.state_manager.update_offers_cache(offers, filters)
    
    def _display_validation_errors(self, errors: List[str]):
        """Display validation errors to user"""
        if errors:
            with st.expander("⚠️ Szűrő validációs figyelmeztetések", expanded=False):
                for error in errors:
                    st.warning(error)
    
    def _display_parameter_errors(self, errors: List[str]):
        """Display parameter validation errors"""
        if errors:
            with st.expander("❌ API paraméter hibák", expanded=True):
                for error in errors:
                    st.error(error)
    
    def _display_debug_info(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]):
        """Display debug information - JAVÍTOTT verzió duplikált widget key javítással"""
        
        # 🚨 CRITICAL FIX: Cleanup old widget keys to prevent duplicates
        cleanup_count = cleanup_widget_keys_in_session("trace_details_")
        if cleanup_count > 0:
            logger.debug(f"🧹 Cleaned up {cleanup_count} old trace detail widget keys")
        
        # Additional cleanup for other potential duplicated keys
        for prefix in ["show_trace_", "trace_info_", "debug_"]:
            additional_cleanup = cleanup_widget_keys_in_session(prefix)
            if additional_cleanup > 0:
                logger.debug(f"🧹 Cleaned up {additional_cleanup} old {prefix} widget keys")
        with st.expander("🔧 Data Coordinator Debug", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("UI Szűrők")
                safe_json_display(ui_filters, "UI Szűrők")
                
                # Filter analysis
                st.subheader("Szűrő Analízis")
                filter_count = sum(1 for v in ui_filters.values() if v is not None and v != [] and v != '')
                st.write(f"Aktív szűrők száma: {filter_count}")
                
                for key, value in ui_filters.items():
                    if value is not None and value != [] and value != '':
                        st.write(f"✅ {key}: {value} ({type(value).__name__})")
                
            with col2:
                st.subheader("API Paraméterek") 
                safe_json_display(api_params, "API Paraméterek")
                
                # API parameter analysis
                st.subheader("API Analízis")
                param_count = len(api_params)
                st.write(f"API paraméterek száma: {param_count}")
                
                if param_count == 0:
                    st.warning("⚠️ Üres API paraméterek - minden ajánlat betöltődik")
                else:
                    for key, value in api_params.items():
                        st.write(f"📤 {key}: {value}")
            
            # Performance metrics
            metrics = self.state_manager.get_performance_metrics()
            if metrics:
                st.subheader("Teljesítmény Metrikák")
                col3, col4 = st.columns(2)
                with col3:
                    st.metric("API hívások", metrics.get('api_calls_count', 0))
                    st.metric("Cache találatok", metrics.get('cache_hits', 0))
                with col4:
                    st.metric("Cache kihagyások", metrics.get('cache_misses', 0))
                    avg_time = metrics.get('average_load_time', 0)
                    st.metric("Átlagos idő (s)", f"{avg_time:.2f}")
            
            # 🚨 CRITICAL: Filter traces display
            filter_traces = self.get_filter_traces()
            if filter_traces:
                st.subheader("🔍 Filter Traces (Real-time)")
                
                for trace in filter_traces[-2:]:  # Show last 2 traces
                    trace_id = trace['trace_id']
                    completed = trace.get('completed', False)
                    errors_count = len(trace.get('errors', []))
                    warnings_count = len(trace.get('warnings', []))
                    
                    status_icon = "✅" if completed and errors_count == 0 else "🚨" if errors_count > 0 else "⏳"
                    
                    # Use container with checkbox instead of nested expander to avoid StreamlitAPIException
                    trace_container = st.container()
                    with trace_container:
                        st.write(f"{status_icon} **Trace {trace_id[:8]}** ({errors_count} errors, {warnings_count} warnings)")
                        
                        # 🚨 CRITICAL FIX: Use unique widget key to prevent duplicates
                        unique_key = generate_unique_widget_key("trace_details", trace_id, "debug_info")
                        show_trace_details = st.checkbox(
                            f"Show details", 
                            key=unique_key, 
                            value=False
                        )
                        
                        if show_trace_details:
                            col_trace1, col_trace2 = st.columns(2)
                            
                            with col_trace1:
                                st.write("**Trace Info:**")
                                start_time = trace['start_time']
                                if hasattr(start_time, 'strftime'):
                                    st.write(f"Start: {start_time.strftime('%H:%M:%S')}")
                                else:
                                    st.write(f"Start: {start_time}")
                                
                                if completed:
                                    st.write(f"Duration: {trace.get('load_time_seconds', 0):.2f}s")
                                    st.write(f"Results: {trace.get('final_results_count', 0)} offers")
                                
                                # Show API params
                                api_params = trace.get('api_params', {})
                                if api_params:
                                    st.write("**API Params:**")
                                    for key, value in list(api_params.items())[:3]:  # Show first 3
                                        st.write(f"- {key}: {value}")
                            
                            with col_trace2:
                                # Show validation results
                                validation = trace.get('validation_result', {})
                                if validation and validation.get('filter_matches'):
                                    st.write("**Filter Match Rates:**")
                                    filter_matches = validation.get('filter_matches', {})
                                    
                                    for filter_name, match_data in filter_matches.items():
                                        if isinstance(match_data, dict):
                                            match_rate = match_data.get('match_rate', 0)
                                            matching = match_data.get('matching', 0)
                                            total = match_data.get('total', 0)
                                            st.write(f"- {filter_name}: {matching}/{total} ({match_rate:.1%})")
                                
                                # Show critical issues
                                if validation and validation.get('critical_issues'):
                                    st.write("**Critical Issues:**")
                                    for issue in validation['critical_issues'][:2]:  # First 2
                                        st.error(f"❌ {issue}")
                            
                            # Show warnings
                            if validation and validation.get('inconsistencies'):
                                st.write("**Warnings:**")
                                for warning in validation['inconsistencies'][:2]:  # First 2
                                    st.warning(f"⚠️ {warning}")
                
                if st.button("🔄 Refresh Traces", key="refresh_traces_dc"):
                    st.rerun()
            
            # State summary
            state_summary = self.state_manager.get_state_summary()
            st.subheader("Állapot Összefoglaló")
            
            # Show key state info in a more readable format
            if state_summary:
                st.write(f"🏗️ **Inicializálva**: {state_summary.get('initialized', 'N/A')}")
                st.write(f"💾 **Cache állapot**: {state_summary.get('cache_status', 'N/A')}")
                st.write(f"⚡ **Betöltési állapot**: {state_summary.get('loading_state', 'N/A')}")
                st.write(f"❌ **Hibák száma**: {state_summary.get('error_count', 0)}")
                st.write(f"📱 **Eszköz típus**: {state_summary.get('device_type', 'N/A')}")
                
                if st.checkbox("Teljes állapot JSON", key="full_state_debug"):
                    st.json(state_summary)
    
    def refresh_data(self, ui_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Force refresh data ignoring cache"""
        # Clear cache first
        self.state_manager.clear_offers_cache()
        
        # Load fresh data
        return self.load_offers(ui_filters)
    
    def get_filter_summary(self, ui_filters: Dict[str, Any]) -> str:
        """Get human-readable filter summary"""
        return self.api_converter.get_filter_summary(ui_filters)
    
    def validate_and_convert_filters(self, ui_filters: Dict[str, Any]) -> Tuple[Dict[str, Any], List[str]]:
        """Validate UI filters and convert to API parameters"""
        # Validate filters
        validated_filters = self.state_manager.filter_manager.validate_filters(ui_filters)
        validation_errors = self.state_manager.filter_manager.get_validation_errors()
        
        # Convert to API parameters
        api_params = self.api_converter.convert_filters(validated_filters)
        
        # Validate API parameters
        is_valid, param_errors = self.api_converter.validate_api_params(api_params)
        
        # Combine all errors
        all_errors = validation_errors + param_errors
        
        return api_params, all_errors
    
    def get_loading_state(self) -> str:
        """Get current loading state"""
        return self.state_manager.get_loading_state()
    
    def clear_cache(self):
        """Clear all cached data"""
        self.state_manager.clear_offers_cache()
        st.success("Cache törölve!")
    
    def export_debug_data(self) -> Dict[str, Any]:
        """Export debug data for troubleshooting"""
        return {
            'timestamp': datetime.now().isoformat(),
            'state_export': self.state_manager.export_state_for_debug(),
            'performance_metrics': self.state_manager.get_performance_metrics(),
            'error_messages': self.state_manager.get_error_messages(),
            'loading_state': self.get_loading_state(),
            'filter_traces': getattr(self, 'filter_traces', [])[-5:]  # Last 5 traces
        }
    
    def _start_filter_trace(self, ui_filters: Dict[str, Any], validated_filters: Dict[str, Any], api_params: Dict[str, Any]) -> str:
        """Start real-time filter chain tracing"""
        import uuid
        
        trace_id = f"trace_{uuid.uuid4().hex[:8]}"
        trace_data = {
            'trace_id': trace_id,
            'start_time': datetime.now(),
            'ui_filters': ui_filters.copy(),
            'validated_filters': validated_filters.copy(),
            'api_params': api_params.copy(),
            'errors': [],
            'warnings': [],
            'api_call_started': None,
            'api_call_completed': None,
            'results_count': None,
            'validation_result': None,
            'completed': False
        }
        
        # Initialize traces list if not exists
        if not hasattr(self, 'filter_traces'):
            self.filter_traces = []
        
        self.filter_traces.append(trace_data)
        
        # Keep only last 20 traces
        if len(self.filter_traces) > 20:
            self.filter_traces = self.filter_traces[-20:]
        
        logger.info(f"🔍 Filter trace started [{trace_id}]: UI filters -> API params")
        return trace_id
    
    def _log_trace_error(self, trace_id: str, error_message: str):
        """Log error to trace"""
        if hasattr(self, 'filter_traces'):
            for trace in self.filter_traces:
                if trace['trace_id'] == trace_id:
                    trace['errors'].append({
                        'timestamp': datetime.now(),
                        'message': error_message
                    })
                    logger.error(f"🚨 Trace [{trace_id}] Error: {error_message}")
                    break
    
    def _log_trace_warning(self, trace_id: str, warning_message: str):
        """Log warning to trace"""
        if hasattr(self, 'filter_traces'):
            for trace in self.filter_traces:
                if trace['trace_id'] == trace_id:
                    trace['warnings'].append({
                        'timestamp': datetime.now(),
                        'message': warning_message
                    })
                    logger.warning(f"⚠️ Trace [{trace_id}] Warning: {warning_message}")
                    break
    
    def _fetch_from_api_with_trace(self, api_params: Dict[str, Any], trace_id: str) -> List[Dict[str, Any]]:
        """Fetch offers from API with trace logging"""
        
        # Update trace with API call start
        if hasattr(self, 'filter_traces'):
            for trace in self.filter_traces:
                if trace['trace_id'] == trace_id:
                    trace['api_call_started'] = datetime.now()
                    break
        
        # 🚀 DEBUG POINT 4: API hívás előtt
        logger.critical(f"🚀 DEBUG 4 - API CALL STARTING with params: {api_params}")
        
        # Specifically log user_id (backend expects user_id for producer filtering)
        if 'user_id' in api_params:
            logger.critical(f"🎯 DEBUG 4 - USER_ID being sent to API: {api_params['user_id']}")
        else:
            logger.error(f"❌ DEBUG 4 - NO USER_ID in API call!")
        
        try:
            # Import API client
            from .api_client import get_offers
            
            logger.info(f"Making API call with parameters: {api_params}")
            success, offers = get_offers(api_params)
            
            # 📊 DEBUG POINT 5: API válasz után
            logger.critical(f"📊 DEBUG 5 - API RESPONSE: success={success}, offers_count={len(offers) if success and offers else 0}")
            
            if success and offers:
                # Check if results match producer filter (backend uses user_id for filtering)
                if 'user_id' in api_params:
                    expected_user_id = api_params['user_id']
                    matching_offers = 0
                    found_user_ids = set()
                    
                    for offer in offers:
                        # Backend szűrés Offer.user_id alapján történik
                        offer_user_id = (
                            offer.get('user_id') or
                            offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None
                        )
                        
                        if offer_user_id:
                            found_user_ids.add(int(offer_user_id))
                            if int(offer_user_id) == int(expected_user_id):
                                matching_offers += 1
                    
                    logger.critical(f"🔍 DEBUG 5 - USER VALIDATION: Expected user_id {expected_user_id}, Found user_ids {list(found_user_ids)}, Matching offers: {matching_offers}")
            
            # Update trace with API call completion
            if hasattr(self, 'filter_traces'):
                for trace in self.filter_traces:
                    if trace['trace_id'] == trace_id:
                        trace['api_call_completed'] = datetime.now()
                        trace['results_count'] = len(offers) if success else 0
                        if not success:
                            trace['errors'].append({
                                'timestamp': datetime.now(),
                                'message': f"API call failed: {offers}"
                            })
                        break
            
            if success:
                logger.info(f"✅ Trace [{trace_id}] API call successful: {len(offers)} offers received")
                return offers
            else:
                error_msg = f"API call failed: {offers}"
                self._log_trace_error(trace_id, error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            self._log_trace_error(trace_id, f"API call exception: {str(e)}")
            logger.error(f"Error fetching offers: {e}")
            return []
    
    def _validate_results_vs_filters(self, offers: List[Dict[str, Any]], api_params: Dict[str, Any], trace_id: str) -> Dict[str, Any]:
        """Enhanced validation that results match applied filters with critical issue detection"""
        
        validation = {
            'timestamp': datetime.now().isoformat(),
            'trace_id': trace_id,
            'total_offers': len(offers),
            'filter_matches': {},
            'inconsistencies': [],
            'critical_issues': [],
            'is_valid': True,
            'errors': []
        }
        
        logger.critical(f"🔍 RESULT VALIDATION [{trace_id}]: Validating {len(offers)} offers against API params: {api_params}")
        
        # CRITICAL: User ID validation (Backend szűrés user_id alapján)
        if 'user_id' in api_params:
            expected_user_id = api_params['user_id']
            matching_count = 0
            found_user_ids = set()
            
            logger.critical(f"🎯 USER VALIDATION [{trace_id}]: Expected user_id: {expected_user_id}")
            
            for offer in offers:
                # Backend szűrés Offer.user_id mezőn történik
                offer_user_id = (
                    offer.get('user_id') or 
                    offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None
                )
                
                if offer_user_id:
                    found_user_ids.add(int(offer_user_id))
                    if int(offer_user_id) == int(expected_user_id):
                        matching_count += 1
            
            validation['filter_matches']['user_id'] = {
                'expected': expected_user_id,
                'matching': matching_count,
                'total': len(offers),
                'match_rate': matching_count / len(offers) if offers else 0,
                'found_user_ids': list(found_user_ids)
            }
            
            # CRITICAL CHECK: Zero matches means API filter didn't work
            if matching_count == 0 and len(offers) > 0:
                critical_issue = f"CRITICAL: Expected user_id {expected_user_id} but found {list(found_user_ids)}"
                validation['critical_issues'].append(critical_issue)
                validation['is_valid'] = False
                validation['errors'].append(critical_issue)
                
                logger.error(f"🚨 CRITICAL ISSUE [{trace_id}]: {critical_issue}")
                
                # Log trace error
                self._log_trace_error(trace_id, critical_issue)
                
                # Store validation error for UI display
                if 'data_coordinator_errors' not in st.session_state:
                    st.session_state.data_coordinator_errors = []
                    
                st.session_state.data_coordinator_errors.append({
                    'timestamp': datetime.now().isoformat(),
                    'trace_id': trace_id,
                    'type': 'USER_FILTER_MISMATCH',
                    'expected_user_id': expected_user_id,
                    'found_user_ids': list(found_user_ids),
                    'offer_count': len(offers)
                })
                
                # ENHANCED: Show critical error in UI immediately
                st.error("🚨 KRITIKUS HIBA: A user szűrő nem működik megfelelően!")
                with st.expander("🔍 User szűrő hiba részletei", expanded=True):
                    st.write(f"**Várt User ID:** {expected_user_id}")
                    st.write(f"**Talált User ID-k:** {list(found_user_ids)}")
                    st.write(f"**Ajánlatok száma:** {len(offers)}")
                    st.write(f"**Egyező ajánlatok:** {matching_count}")
                    
                    if offers:
                        st.write("**Első ajánlat mintája:**")
                        st.json(offers[0])
            
            elif matching_count < len(offers):
                # Partial matches - warning but not critical
                warning = f"WARNING: Only {matching_count}/{len(offers)} offers match user_id {expected_user_id}"
                validation['inconsistencies'].append(warning)
                self._log_trace_warning(trace_id, warning)
                
            else:
                # All good
                logger.critical(f"✅ USER VALIDATION [{trace_id}]: All {matching_count} offers match expected user_id {expected_user_id}")
        
        # Additional filter validations can be added here...
        
        # Final validation summary
        if validation['critical_issues']:
            validation['is_valid'] = False
            logger.error(f"🚨 VALIDATION FAILED [{trace_id}]: {len(validation['critical_issues'])} critical issues found")
        else:
            logger.critical(f"✅ VALIDATION PASSED [{trace_id}]: All filter validations successful")
        
        return validation
    
    def _complete_filter_trace(self, trace_id: str, offers: List[Dict[str, Any]], load_time: float, validation_result: Dict[str, Any]):
        """Complete filter trace with results"""
        if hasattr(self, 'filter_traces'):
            for trace in self.filter_traces:
                if trace['trace_id'] == trace_id:
                    trace['completed'] = True
                    trace['completion_time'] = datetime.now()
                    trace['load_time_seconds'] = load_time
                    trace['final_results_count'] = len(offers)
                    trace['validation_result'] = validation_result
                    
                    # Summary logging
                    total_issues = len(validation_result.get('critical_issues', [])) + len(validation_result.get('inconsistencies', []))
                    
                    if validation_result.get('critical_issues'):
                        logger.error(f"🚨 Trace [{trace_id}] COMPLETED WITH CRITICAL ISSUES: {len(offers)} offers, {total_issues} validation issues")
                    elif validation_result.get('inconsistencies'):
                        logger.warning(f"⚠️ Trace [{trace_id}] completed with warnings: {len(offers)} offers, {total_issues} validation issues")
                    else:
                        logger.info(f"✅ Trace [{trace_id}] completed successfully: {len(offers)} offers, {load_time:.2f}s")
                    break
    
    def get_filter_traces(self) -> List[Dict[str, Any]]:
        """Get recent filter traces for debugging"""
        if hasattr(self, 'filter_traces'):
            return self.filter_traces[-10:]  # Last 10 traces
        return []
    
    def convert_filters_to_api_params(self, ui_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Convert UI filters to API parameters"""
        # 🔧 CRITICAL FIX: Remove internal validation fields
        clean_filters = {}
        for key, value in ui_filters.items():
            # Skip internal fields (starting with _)
            if key.startswith('_'):
                logger.debug(f"Skipping internal field: {key}")
                continue
            # Skip None values
            if value is None:
                continue
            clean_filters[key] = value
        logger.info(f"Cleaned filters: removed {len(ui_filters) - len(clean_filters)} internal/null fields")
        logger.debug(f"Clean filters: {clean_filters}")
        # Convert using API converter
        api_params = self.api_converter.convert_filters(clean_filters)
        # Log the conversion result
        logger.info(f"API parameters after conversion: {api_params}")
        # 🔧 PRODUCER FILTER OVERRIDE - Ha van aktív override a session state-ben
        if 'producer_filter_override' in st.session_state:
            override = st.session_state.producer_filter_override
            if override.get('enabled', False):
                recommended_params = override.get('recommended_params', {})
                logger.warning(f"🔧 Applying producer filter override: {recommended_params}")
                api_params.update(recommended_params)
                logger.info(f"✅ Producer filter override applied from: {override.get('source', 'unknown')}")
        return api_params
    
    def get_producers(self) -> List[Dict[str, Any]]:
        """Get producers list for validation"""
        try:
            # Try to get from cache first
            if hasattr(self.state_manager, 'get_cached_producers'):
                producers = self.state_manager.get_cached_producers()
                if producers:
                    return producers
            
            # Fallback: get from session state or return empty
            producers_cache = st.session_state.get('producers_cache', [])
            return producers_cache
            
        except Exception as e:
            logger.error(f"Error getting producers: {e}")
            return []
    
    def get_products(self) -> List[Dict[str, Any]]:
        """Get products list for validation"""
        try:
            # Try to get from cache first
            if hasattr(self.state_manager, 'get_cached_products'):
                products = self.state_manager.get_cached_products()
                if products:
                    return products
            
            # Fallback: get from session state or return empty  
            products_cache = st.session_state.get('products_cache', [])
            return products_cache
            
        except Exception as e:
            logger.error(f"Error getting products: {e}")
            return []
    
    def get_api_client(self):
        """Get API client for direct access"""
        if hasattr(self.state_manager, 'api_client'):
            return self.state_manager.api_client
        return None
    
    def validate_filter_to_result_consistency(self, ui_filters: Dict, api_params: Dict, results: List[Dict]) -> Dict[str, Any]:
        """Validate consistency between filters, API params, and results"""
        
        validation = {
            'timestamp': datetime.now().isoformat(),
            'ui_filters': ui_filters.copy(),
            'api_params': api_params.copy(),
            'results_count': len(results),
            'validation_issues': [],
            'producer_validation': {},
            'filter_effectiveness': {}
        }
        
        # Producer validation - JAVÍTOTT: Backend user_id paramétert használ!
        expected_producer_id = api_params.get('user_id')
        
        # DEBUG logging
        logger.info(f"🔍 Producer validation - expected_user_id: {expected_producer_id}")
        logger.info(f"📋 API params: {api_params}")
        
        if expected_producer_id is not None:  # Explicit None check!
            result_producer_ids = set()
            result_producer_names = set()
            
            for result in results:
                # Try different possible field names for producer ID
                producer_id = None
                producer_name = None
                
                # Multiple field name attempts
                if 'user' in result and isinstance(result['user'], dict):
                    producer_id = result['user'].get('id')
                    producer_name = result['user'].get('contact_name') or result['user'].get('company_name')
                elif 'user_id' in result:
                    producer_id = result.get('user_id')
                elif 'producer_id' in result:
                    producer_id = result.get('producer_id')
                    
                if 'user_name' in result:
                    producer_name = result.get('user_name')
                elif 'producer_name' in result:
                    producer_name = result.get('producer_name')
                
                if producer_id is not None:
                    result_producer_ids.add(producer_id)
                if producer_name:
                    result_producer_names.add(producer_name)
            
            validation['producer_validation'] = {
                'expected_user_id': expected_producer_id,
                'found_producer_ids': list(result_producer_ids),
                'found_producer_names': list(result_producer_names),
                'id_match': expected_producer_id in result_producer_ids,
                'results_count': len(results)
            }
            
            # CRITICAL CHECK
            if expected_producer_id not in result_producer_ids:
                validation['validation_issues'].append(
                    f"CRITICAL: Expected user_id {expected_producer_id} but found {list(result_producer_ids)}"
                )
            
            # DEBUG logging  
            logger.info(f"✅ Producer validation result: {validation['producer_validation']}")
        else:
            # Ha nincs user_id az API params-ban
            validation['validation_issues'].append("No user_id found in API parameters")
            logger.warning("⚠️ No user_id in API parameters")
        
        # Filter effectiveness
        active_filters = sum(1 for v in ui_filters.values() if v not in [None, '', [], False, ('', '')])
        validation['filter_effectiveness'] = {
            'active_filter_count': active_filters,
            'results_count': len(results),
            'avg_results_per_filter': len(results) / max(active_filters, 1)
        }
        
        return validation
