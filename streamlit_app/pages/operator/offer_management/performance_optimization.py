"""
Performance Optimization Module for Enhanced Offer Management
Implements advanced caching, lazy loading, and performance monitoring
"""
import streamlit as st
import logging
import time
import hashlib
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from functools import wraps, lru_cache
import threading
from concurrent.futures import ThreadPoolExecutor
import pickle

logger = logging.getLogger(__name__)

class PerformanceCache:
    """
    Advanced caching system with TTL, LRU eviction, and memory management.
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_times = {}
        self.creation_times = {}
        self.lock = threading.Lock()
        
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate a unique cache key."""
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else []
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> tuple[bool, Any]:
        """Get item from cache with TTL check."""
        with self.lock:
            if key not in self.cache:
                return False, None
                
            # Check TTL
            creation_time = self.creation_times.get(key, 0)
            if time.time() - creation_time > self.default_ttl:
                self._remove_key(key)
                return False, None
                
            # Update access time for LRU
            self.access_times[key] = time.time()
            return True, self.cache[key]
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put item in cache with size management."""
        with self.lock:
            # Remove old entry if exists
            if key in self.cache:
                self._remove_key(key)
                
            # Manage cache size
            if len(self.cache) >= self.max_size:
                self._evict_lru()
                
            # Add new entry
            self.cache[key] = value
            self.access_times[key] = time.time()
            self.creation_times[key] = time.time()
    
    def _remove_key(self, key: str) -> None:
        """Remove key from all tracking dictionaries."""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.creation_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """Evict least recently used items."""
        if not self.access_times:
            return
            
        # Remove 20% of least recently used items
        items_to_remove = max(1, len(self.access_times) // 5)
        sorted_keys = sorted(self.access_times.items(), key=lambda x: x[1])
        
        for key, _ in sorted_keys[:items_to_remove]:
            self._remove_key(key)
    
    def clear(self) -> None:
        """Clear entire cache."""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.creation_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1),
                'oldest_entry': min(self.creation_times.values()) if self.creation_times else None
            }

# Global cache instance
performance_cache = PerformanceCache(max_size=2000, default_ttl=300)

def cached_function(ttl: int = 300, use_session_state: bool = True):
    """
    Decorator for caching function results with TTL support.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = performance_cache._generate_key(func.__name__, args, kwargs)
            
            # Try session state cache first if enabled
            if use_session_state:
                session_cache_key = f"cache_{cache_key}"
                if session_cache_key in st.session_state:
                    cached_data = st.session_state[session_cache_key]
                    if time.time() - cached_data.get('timestamp', 0) < ttl:
                        return cached_data['result']
            
            # Try performance cache
            hit, result = performance_cache.get(cache_key)
            if hit:
                performance_cache._hit_count = getattr(performance_cache, '_hit_count', 0) + 1
                return result
            
            # Cache miss - execute function
            performance_cache._access_count = getattr(performance_cache, '_access_count', 0) + 1
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                
                # Store in caches
                performance_cache.put(cache_key, result, ttl)
                
                if use_session_state:
                    st.session_state[session_cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                
                execution_time = time.time() - start_time
                logger.debug(f"Function {func.__name__} executed in {execution_time:.3f}s")
                
                return result
                
            except Exception as e:
                logger.error(f"Error in cached function {func.__name__}: {e}")
                raise
                
        return wrapper
    return decorator

class LazyLoader:
    """
    Lazy loading system for data-heavy components.
    """
    
    def __init__(self):
        self.loaded_components = set()
        self.loading_states = {}
        
    def lazy_load(self, component_id: str, loader_func: Callable, 
                  trigger_threshold: float = 0.8) -> tuple[bool, Any]:
        """
        Lazy load component data when needed.
        
        Args:
            component_id: Unique identifier for component
            loader_func: Function to load data
            trigger_threshold: Scroll threshold to trigger loading (0.0-1.0)
        """
        if component_id in self.loaded_components:
            return True, None
            
        # Check if component is in viewport or should be loaded
        should_load = self._should_load_component(component_id, trigger_threshold)
        
        if should_load and component_id not in self.loading_states:
            self.loading_states[component_id] = True
            
            try:
                result = loader_func()
                self.loaded_components.add(component_id)
                self.loading_states.pop(component_id, None)
                return True, result
                
            except Exception as e:
                logger.error(f"Error lazy loading {component_id}: {e}")
                self.loading_states.pop(component_id, None)
                return False, str(e)
                
        return False, None
    
    def _should_load_component(self, component_id: str, threshold: float) -> bool:
        """Determine if component should be loaded based on viewport."""
        # For now, we'll use simple heuristics
        # In a real implementation, this would check scroll position
        
        # Load critical components immediately
        if 'filter' in component_id or 'stats' in component_id:
            return True
            
        # Load offer components progressively
        if 'offer_card' in component_id:
            # Simulate progressive loading based on component index
            try:
                index = int(component_id.split('_')[-1])
                return index < 20  # Load first 20 items
            except (ValueError, IndexError):
                return True
                
        return True
    
    def reset(self):
        """Reset lazy loader state."""
        self.loaded_components.clear()
        self.loading_states.clear()

# Global lazy loader instance
lazy_loader = LazyLoader()

class PerformanceMonitor:
    """
    Performance monitoring and optimization suggestions.
    """
    
    def __init__(self):
        self.metrics = {
            'component_render_times': {},
            'api_call_times': {},
            'cache_stats': {},
            'memory_usage': [],
            'user_interactions': []
        }
        
    def measure_component_render(self, component_name: str):
        """Decorator to measure component render time."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                render_time = end_time - start_time
                self._record_metric('component_render_times', component_name, render_time)
                
                # Log slow renders
                if render_time > 1.0:
                    logger.warning(f"Slow component render: {component_name} took {render_time:.3f}s")
                
                return result
            return wrapper
        return decorator
    
    def measure_api_call(self, api_name: str):
        """Decorator to measure API call time."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                call_time = end_time - start_time
                self._record_metric('api_call_times', api_name, call_time)
                
                # Log slow API calls
                if call_time > 2.0:
                    logger.warning(f"Slow API call: {api_name} took {call_time:.3f}s")
                
                return result
            return wrapper
        return decorator
    
    def _record_metric(self, category: str, name: str, value: float):
        """Record performance metric."""
        if category not in self.metrics:
            self.metrics[category] = {}
            
        if name not in self.metrics[category]:
            self.metrics[category][name] = []
            
        self.metrics[category][name].append({
            'value': value,
            'timestamp': time.time()
        })
        
        # Keep only last 100 measurements per metric
        if len(self.metrics[category][name]) > 100:
            self.metrics[category][name] = self.metrics[category][name][-100:]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report."""
        report = {
            'cache_stats': performance_cache.get_stats(),
            'slow_components': [],
            'slow_api_calls': [],
            'recommendations': []
        }
        
        # Analyze component render times
        for component, times in self.metrics.get('component_render_times', {}).items():
            if times:
                avg_time = sum(t['value'] for t in times[-10:]) / len(times[-10:])
                if avg_time > 0.5:
                    report['slow_components'].append({
                        'component': component,
                        'avg_time': avg_time,
                        'recent_times': [t['value'] for t in times[-5:]]
                    })
        
        # Analyze API call times
        for api, times in self.metrics.get('api_call_times', {}).items():
            if times:
                avg_time = sum(t['value'] for t in times[-10:]) / len(times[-10:])
                if avg_time > 1.0:
                    report['slow_api_calls'].append({
                        'api': api,
                        'avg_time': avg_time,
                        'recent_times': [t['value'] for t in times[-5:]]
                    })
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        # Cache recommendations
        cache_stats = report.get('cache_stats', {})
        if cache_stats.get('hit_rate', 0) < 0.5:
            recommendations.append("Consider increasing cache TTL or improving cache key generation")
        
        # Component recommendations
        slow_components = report.get('slow_components', [])
        if slow_components:
            recommendations.append(f"Optimize {len(slow_components)} slow components using lazy loading or memoization")
        
        # API recommendations
        slow_api_calls = report.get('slow_api_calls', [])
        if slow_api_calls:
            recommendations.append(f"Consider caching or optimizing {len(slow_api_calls)} slow API calls")
        
        return recommendations

# Global performance monitor
performance_monitor = PerformanceMonitor()

class OptimizedDataLoader:
    """
    Optimized data loading with batching, prefetching, and pagination.
    """
    
    def __init__(self, batch_size: int = 50):
        self.batch_size = batch_size
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.prefetch_cache = {}
        
    @cached_function(ttl=180)
    def load_offers_batch(self, filters: Dict[str, Any], page: int = 1, 
                         page_size: int = 50) -> tuple[bool, List[Dict]]:
        """Load offers in batches with caching."""
        try:
            # Import API client
            from .api_integration import get_offers_for_filters
            
            # Add pagination to filters
            paginated_filters = filters.copy()
            paginated_filters.update({
                'page': page,
                'page_size': page_size
            })
            
            return get_offers_for_filters(paginated_filters)
            
        except Exception as e:
            logger.error(f"Error loading offers batch: {e}")
            return False, []
    
    def prefetch_next_batch(self, filters: Dict[str, Any], current_page: int):
        """Prefetch next batch of data in background."""
        next_page = current_page + 1
        cache_key = f"prefetch_{hash(str(filters))}_{next_page}"
        
        if cache_key not in self.prefetch_cache:
            future = self.executor.submit(
                self.load_offers_batch, 
                filters, 
                next_page, 
                self.batch_size
            )
            self.prefetch_cache[cache_key] = future
    
    def get_prefetched_data(self, filters: Dict[str, Any], page: int) -> Optional[List[Dict]]:
        """Get prefetched data if available."""
        cache_key = f"prefetch_{hash(str(filters))}_{page}"
        
        if cache_key in self.prefetch_cache:
            try:
                future = self.prefetch_cache.pop(cache_key)
                success, data = future.result(timeout=0.1)
                return data if success else None
            except Exception:
                return None
        
        return None

# Global optimized data loader
optimized_loader = OptimizedDataLoader()

# Utility functions for performance optimization

def debounce(wait_time: float):
    """
    Debounce decorator to limit function calls.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = f"debounce_{func.__name__}"
            current_time = time.time()
            
            if key in st.session_state:
                last_call = st.session_state[key]
                if current_time - last_call < wait_time:
                    return
            
            st.session_state[key] = current_time
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def memoize_component_state(component_id: str, state_key: str, 
                           factory_func: Callable, ttl: int = 300):
    """
    Memoize component state with TTL.
    """
    cache_key = f"component_state_{component_id}_{state_key}"
    
    if cache_key in st.session_state:
        cached_data = st.session_state[cache_key]
        if time.time() - cached_data.get('timestamp', 0) < ttl:
            return cached_data['value']
    
    # Generate new state
    new_value = factory_func()
    
    st.session_state[cache_key] = {
        'value': new_value,
        'timestamp': time.time()
    }
    
    return new_value

def optimize_large_list_rendering(items: List[Any], 
                                virtual_window_size: int = 20,
                                component_renderer: Callable = None):
    """
    Optimize rendering of large lists using virtual scrolling concept.
    """
    if len(items) <= virtual_window_size:
        # Render all items if list is small
        for i, item in enumerate(items):
            if component_renderer:
                component_renderer(item, i)
        return
    
    # Virtual scrolling for large lists
    if 'virtual_scroll_offset' not in st.session_state:
        st.session_state.virtual_scroll_offset = 0
    
    offset = st.session_state.virtual_scroll_offset
    end_index = min(offset + virtual_window_size, len(items))
    
    # Render visible window
    for i in range(offset, end_index):
        if component_renderer:
            component_renderer(items[i], i)
    
    # Pagination controls
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col1:
        if st.button("⬆️ Előző", disabled=offset == 0):
            st.session_state.virtual_scroll_offset = max(0, offset - virtual_window_size)
            st.rerun()
    
    with col2:
        st.write(f"Elemek {offset + 1}-{end_index} / {len(items)}")
    
    with col3:
        if st.button("⬇️ Következő", disabled=end_index >= len(items)):
            st.session_state.virtual_scroll_offset = min(len(items) - virtual_window_size, offset + virtual_window_size)
            st.rerun()

# Performance monitoring UI component
def render_performance_debug_panel():
    """Render performance debug panel for development."""
    if st.session_state.get('show_performance_debug', False):
        with st.expander("🔧 Performance Debug", expanded=False):
            report = performance_monitor.get_performance_report()
            
            st.subheader("Cache Statistics")
            cache_stats = report.get('cache_stats', {})
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Cache Size", cache_stats.get('size', 0))
            with col2:
                st.metric("Hit Rate", f"{cache_stats.get('hit_rate', 0):.2%}")
            with col3:
                st.metric("Max Size", cache_stats.get('max_size', 0))
            
            st.subheader("Performance Issues")
            
            slow_components = report.get('slow_components', [])
            if slow_components:
                st.warning(f"Found {len(slow_components)} slow components")
                for comp in slow_components:
                    st.write(f"- {comp['component']}: {comp['avg_time']:.3f}s average")
            
            slow_apis = report.get('slow_api_calls', [])
            if slow_apis:
                st.warning(f"Found {len(slow_apis)} slow API calls")
                for api in slow_apis:
                    st.write(f"- {api['api']}: {api['avg_time']:.3f}s average")
            
            st.subheader("Recommendations")
            recommendations = report.get('recommendations', [])
            for rec in recommendations:
                st.info(f"💡 {rec}")
            
            if st.button("Clear Cache"):
                performance_cache.clear()
                st.success("Cache cleared!")
                st.rerun()

# Export performance utilities
__all__ = [
    'PerformanceCache',
    'performance_cache',
    'cached_function',
    'LazyLoader',
    'lazy_loader',
    'PerformanceMonitor',
    'performance_monitor',
    'OptimizedDataLoader',
    'optimized_loader',
    'debounce',
    'memoize_component_state',
    'optimize_large_list_rendering',
    'render_performance_debug_panel'
]