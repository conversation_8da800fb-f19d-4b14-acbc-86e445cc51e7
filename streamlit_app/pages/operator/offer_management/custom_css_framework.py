"""
Egyedi CSS keretrendszer az ajánlatkezelő modulhoz.

Ez a modul egy központosított, új<PERSON><PERSON><PERSON>használható CSS keretrendszert biztosít,
amely következetes vizuális stílust és komponenseket kínál az egész modulban.
"""
import streamlit as st
import uuid
import logging
from typing import Dict, Any, Optional, Callable

logger = logging.getLogger(__name__)

# Színpaletta definíciók - újrafelhasználható színváltozók
COLORS = {
    # Alapszínek
    "primary": "#3584e4",     # Kék - fő szín
    "secondary": "#f9c440",   # S<PERSON>rga - másodlagos szín
    "accent": "#26a269",      # <PERSON>ö<PERSON> - <PERSON><PERSON><PERSON><PERSON> sz<PERSON>
    "danger": "#e01b24",      # <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>, törl<PERSON>
    "warning": "#ffa348",     # Narancssárga - figyelmeztetés
    "success": "#33d17a",     # Zöld - siker<PERSON> művelet
    "info": "#3584e4",        # Kék - információs
    
    # Semleges színek
    "white": "#ffffff",
    "lightgray": "#f9f9f9",
    "midgray": "#dcdcdc", 
    "gray": "#9e9e9e",
    "darkgray": "#5e5e5e",
    "black": "#241f31",
    
    # Státusz színek - az API státuszokhoz
    "status_created": "#c0c0c0",                # Létrehozva
    "status_confirmed_by_company": "#ffc107",   # Megerősítve a cég által
    "status_accepted_by_user": "#4caf50",       # Elfogadva a felhasználó által
    "status_rejected_by_user": "#f44336",       # Elutasítva a felhasználó által
    "status_finalized": "#2196f3",              # Véglegesítve
}

# Árnyékok - különböző méretű dobozárnyékok
SHADOWS = {
    "small": "0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08)",
    "medium": "0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12)",
    "large": "0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.10)",
    "inset": "inset 0 1px 3px rgba(0, 0, 0, 0.12), inset 0 1px 2px rgba(0, 0, 0, 0.08)",
}

# Betűméret skála
TYPOGRAPHY = {
    "xs": "0.75rem",    # 12px
    "sm": "0.875rem",   # 14px
    "md": "1rem",       # 16px
    "lg": "1.125rem",   # 18px
    "xl": "1.25rem",    # 20px
    "2xl": "1.5rem",    # 24px
    "3xl": "1.875rem",  # 30px
    "4xl": "2.25rem",   # 36px
}

# Térközök és margók
SPACING = {
    "xs": "0.25rem",    # 4px
    "sm": "0.5rem",     # 8px
    "md": "1rem",       # 16px
    "lg": "1.5rem",     # 24px
    "xl": "2rem",       # 32px
    "2xl": "3rem",      # 48px
}

# Lekerekítések
BORDER_RADIUS = {
    "sm": "0.125rem",    # 2px
    "md": "0.25rem",     # 4px
    "lg": "0.5rem",      # 8px
    "xl": "1rem",        # 16px
    "full": "9999px",    # Teljesen kerek
}

# Animációk és átmenetek
ANIMATIONS = {
    "fast": "0.15s ease-in-out",
    "normal": "0.3s ease-in-out",
    "slow": "0.5s ease-in-out",
    "bounce": "0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)",
}

# Töréspontok a reszponzív tervezéshez
BREAKPOINTS = {
    "xs": "0px",      # Extra kicsi - mobilok
    "sm": "576px",    # Kicsi - nagyobb mobilok
    "md": "768px",    # Közepes - tabletek
    "lg": "992px",    # Nagy - kis laptopok
    "xl": "1200px",   # Extra nagy - asztali 
    "2xl": "1400px",  # Extra extra nagy - nagy monitorok
}

def inject_base_styles():
    """
    Alap CSS stílusok injektálása a Streamlit alkalmazásba.
    
    Ezek a stílusok alapvető tipográfiát, színeket és elrendezési osztályokat tartalmaznak.
    """
    css = f"""
    <style>
    /* ===== ALAP STÍLUSOK ===== */
    
    /* Változó definíciók a :root-on */
    :root {{
        /* Színek */
        --color-primary: {COLORS["primary"]};
        --color-secondary: {COLORS["secondary"]};
        --color-accent: {COLORS["accent"]};
        --color-danger: {COLORS["danger"]};
        --color-warning: {COLORS["warning"]};
        --color-success: {COLORS["success"]};
        --color-info: {COLORS["info"]};
        
        /* Semleges színek */
        --color-white: {COLORS["white"]};
        --color-lightgray: {COLORS["lightgray"]};
        --color-midgray: {COLORS["midgray"]};
        --color-gray: {COLORS["gray"]};
        --color-darkgray: {COLORS["darkgray"]};
        --color-black: {COLORS["black"]};
        
        /* Státusz színek */
        --color-status-created: {COLORS["status_created"]};
        --color-status-confirmed-by-company: {COLORS["status_confirmed_by_company"]};
        --color-status-accepted-by-user: {COLORS["status_accepted_by_user"]};
        --color-status-rejected-by-user: {COLORS["status_rejected_by_user"]};
        --color-status-finalized: {COLORS["status_finalized"]};
        
        /* Árnyékok */
        --shadow-small: {SHADOWS["small"]};
        --shadow-medium: {SHADOWS["medium"]};
        --shadow-large: {SHADOWS["large"]};
        --shadow-inset: {SHADOWS["inset"]};
        
        /* Betűméretek */
        --font-size-xs: {TYPOGRAPHY["xs"]};
        --font-size-sm: {TYPOGRAPHY["sm"]};
        --font-size-md: {TYPOGRAPHY["md"]};
        --font-size-lg: {TYPOGRAPHY["lg"]};
        --font-size-xl: {TYPOGRAPHY["xl"]};
        --font-size-2xl: {TYPOGRAPHY["2xl"]};
        --font-size-3xl: {TYPOGRAPHY["3xl"]};
        --font-size-4xl: {TYPOGRAPHY["4xl"]};
        
        /* Térközök */
        --spacing-xs: {SPACING["xs"]};
        --spacing-sm: {SPACING["sm"]};
        --spacing-md: {SPACING["md"]};
        --spacing-lg: {SPACING["lg"]};
        --spacing-xl: {SPACING["xl"]};
        --spacing-2xl: {SPACING["2xl"]};
        
        /* Lekerekítések */
        --radius-sm: {BORDER_RADIUS["sm"]};
        --radius-md: {BORDER_RADIUS["md"]};
        --radius-lg: {BORDER_RADIUS["lg"]};
        --radius-xl: {BORDER_RADIUS["xl"]};
        --radius-full: {BORDER_RADIUS["full"]};
        
        /* Animációk */
        --animation-fast: {ANIMATIONS["fast"]};
        --animation-normal: {ANIMATIONS["normal"]};
        --animation-slow: {ANIMATIONS["slow"]};
        --animation-bounce: {ANIMATIONS["bounce"]};
    }}
    
    /* Alap tipográfia felülírások */
    .streamlit-container {{
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }}
    
    /* Fejlécek */
    .st-bx {{
        margin-bottom: var(--spacing-md);
    }}
    
    h1, h2, h3, h4, h5, h6 {{
        margin-top: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        font-weight: 600;
        color: var(--color-black);
    }}
    
    /* Gombok és interaktív elemek */
    button, .stButton>button, .stDownloadButton>button {{
        transition: all var(--animation-normal);
        font-weight: 500;
    }}
    
    button:hover, .stButton>button:hover, .stDownloadButton>button:hover {{
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }}
    
    /* Elsődleges gomb */
    .primary-button {{
        background-color: var(--color-primary) !important;
        color: white !important;
        border: none !important;
        padding: 0.5rem 1rem !important;
        border-radius: var(--radius-md) !important;
    }}
    
    /* Másodlagos gomb */
    .secondary-button {{
        background-color: var(--color-white) !important;
        color: var(--color-primary) !important;
        border: 1px solid var(--color-primary) !important;
        padding: 0.5rem 1rem !important;
        border-radius: var(--radius-md) !important;
    }}
    
    /* Veszély gomb */
    .danger-button {{
        background-color: var(--color-danger) !important;
        color: white !important;
        border: none !important;
        padding: 0.5rem 1rem !important;
        border-radius: var(--radius-md) !important;
    }}
    
    /* Elrendezés segédosztályok */
    .flex-row {{
        display: flex;
        flex-direction: row;
        align-items: center;
    }}
    
    .flex-col {{
        display: flex;
        flex-direction: column;
    }}
    
    .items-center {{
        align-items: center;
    }}
    
    .justify-between {{
        justify-content: space-between;
    }}
    
    .justify-center {{
        justify-content: center;
    }}
    
    .gap-xs {{
        gap: var(--spacing-xs);
    }}
    
    .gap-sm {{
        gap: var(--spacing-sm);
    }}
    
    .gap-md {{
        gap: var(--spacing-md);
    }}
    
    .gap-lg {{
        gap: var(--spacing-lg);
    }}
    
    .w-full {{
        width: 100%;
    }}
    
    .text-center {{
        text-align: center;
    }}
    
    .text-right {{
        text-align: right;
    }}
    
    /* Margó segédosztályok */
    .m-xs {{ margin: var(--spacing-xs); }}
    .m-sm {{ margin: var(--spacing-sm); }}
    .m-md {{ margin: var(--spacing-md); }}
    .m-lg {{ margin: var(--spacing-lg); }}
    
    .mt-xs {{ margin-top: var(--spacing-xs); }}
    .mt-sm {{ margin-top: var(--spacing-sm); }}
    .mt-md {{ margin-top: var(--spacing-md); }}
    .mt-lg {{ margin-top: var(--spacing-lg); }}
    
    .mb-xs {{ margin-bottom: var(--spacing-xs); }}
    .mb-sm {{ margin-bottom: var(--spacing-sm); }}
    .mb-md {{ margin-bottom: var(--spacing-md); }}
    .mb-lg {{ margin-bottom: var(--spacing-lg); }}
    
    /* Padding segédosztályok */
    .p-xs {{ padding: var(--spacing-xs); }}
    .p-sm {{ padding: var(--spacing-sm); }}
    .p-md {{ padding: var(--spacing-md); }}
    .p-lg {{ padding: var(--spacing-lg); }}
    
    /* Típusszínek */
    .text-primary {{ color: var(--color-primary); }}
    .text-secondary {{ color: var(--color-secondary); }}
    .text-accent {{ color: var(--color-accent); }}
    .text-danger {{ color: var(--color-danger); }}
    .text-warning {{ color: var(--color-warning); }}
    .text-success {{ color: var(--color-success); }}
    .text-info {{ color: var(--color-info); }}
    
    .text-white {{ color: var(--color-white); }}
    .text-lightgray {{ color: var(--color-lightgray); }}
    .text-gray {{ color: var(--color-gray); }}
    .text-darkgray {{ color: var(--color-darkgray); }}
    .text-black {{ color: var(--color-black); }}
    
    /* Betűméret segédosztályok */
    .text-xs {{ font-size: var(--font-size-xs); }}
    .text-sm {{ font-size: var(--font-size-sm); }}
    .text-md {{ font-size: var(--font-size-md); }}
    .text-lg {{ font-size: var(--font-size-lg); }}
    .text-xl {{ font-size: var(--font-size-xl); }}
    .text-2xl {{ font-size: var(--font-size-2xl); }}
    .text-3xl {{ font-size: var(--font-size-3xl); }}
    
    /* Háttérszínek */
    .bg-primary {{ background-color: var(--color-primary); }}
    .bg-secondary {{ background-color: var(--color-secondary); }}
    .bg-accent {{ background-color: var(--color-accent); }}
    .bg-danger {{ background-color: var(--color-danger); }}
    .bg-warning {{ background-color: var(--color-warning); }}
    .bg-success {{ background-color: var(--color-success); }}
    .bg-info {{ background-color: var(--color-info); }}
    
    .bg-white {{ background-color: var(--color-white); }}
    .bg-lightgray {{ background-color: var(--color-lightgray); }}
    .bg-midgray {{ background-color: var(--color-midgray); }}
    .bg-gray {{ background-color: var(--color-gray); }}
    .bg-darkgray {{ background-color: var(--color-darkgray); }}
    .bg-black {{ background-color: var(--color-black); }}
    
    /* Árnyék segédosztályok */
    .shadow-sm {{ box-shadow: var(--shadow-small); }}
    .shadow-md {{ box-shadow: var(--shadow-medium); }}
    .shadow-lg {{ box-shadow: var(--shadow-large); }}
    .shadow-inset {{ box-shadow: var(--shadow-inset); }}
    </style>
    """
    try:
        st.markdown(css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba az alap stílusok injektálásakor: {str(e)}")

def inject_card_styles():
    """
    Kártya komponensek stílusainak injektálása a Streamlit alkalmazásba.
    
    Ezek a stílusok különböző típusú kártyákat definiálnak, árnyékokkal és animációkkal.
    """
    css = """
    <style>
    /* ===== KÁRTYA STÍLUSOK ===== */
    
    /* Alap kártya */
    .card {
        border-radius: var(--radius-lg);
        background-color: var(--color-white);
        box-shadow: var(--shadow-medium);
        transition: all var(--animation-normal);
        overflow: hidden;
        margin-bottom: var(--spacing-md);
    }
    
    .card:hover {
        box-shadow: var(--shadow-large);
        transform: translateY(-2px);
    }
    
    /* Kártya fejléc */
    .card-header {
        padding: var(--spacing-md);
        background-color: var(--color-lightgray);
        border-bottom: 1px solid var(--color-midgray);
        font-weight: 600;
        font-size: var(--font-size-lg);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    /* Kártya tartalom */
    .card-body {
        padding: var(--spacing-md);
    }
    
    /* Kártya lábléc */
    .card-footer {
        padding: var(--spacing-md);
        background-color: var(--color-lightgray);
        border-top: 1px solid var(--color-midgray);
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-sm);
    }
    
    /* Kártya színvariációk */
    .card-primary {
        border-top: 4px solid var(--color-primary);
    }
    
    .card-primary .card-header {
        background-color: var(--color-primary);
        color: white;
    }
    
    .card-secondary {
        border-top: 4px solid var(--color-secondary);
    }
    
    .card-secondary .card-header {
        background-color: var(--color-secondary);
        color: white;
    }
    
    .card-success {
        border-top: 4px solid var(--color-success);
    }
    
    .card-success .card-header {
        background-color: var(--color-success);
        color: white;
    }
    
    .card-danger {
        border-top: 4px solid var(--color-danger);
    }
    
    .card-danger .card-header {
        background-color: var(--color-danger);
        color: white;
    }
    
    .card-warning {
        border-top: 4px solid var(--color-warning);
    }
    
    .card-warning .card-header {
        background-color: var(--color-warning);
        color: white;
    }
    
    .card-info {
        border-top: 4px solid var(--color-info);
    }
    
    .card-info .card-header {
        background-color: var(--color-info);
        color: white;
    }
    
    /* Összecsukható kártya */
    .card-collapsible .card-header {
        cursor: pointer;
    }
    
    .card-collapsible .card-toggle-icon {
        transition: transform var(--animation-normal);
        font-size: var(--font-size-lg);
    }
    
    .card-collapsible.collapsed .card-toggle-icon {
        transform: rotate(-90deg);
    }
    
    /* Kártya elrendezés variációk */
    .card-horizontal {
        display: flex;
        flex-direction: row;
    }
    
    .card-horizontal .card-image {
        width: 33%;
        object-fit: cover;
    }
    
    .card-horizontal .card-content {
        width: 67%;
        display: flex;
        flex-direction: column;
    }
    
    /* Reszponzív kezelés */
    @media screen and (max-width: 768px) {
        .card-horizontal {
            flex-direction: column;
        }
        
        .card-horizontal .card-image,
        .card-horizontal .card-content {
            width: 100%;
        }
        
        .card-header, .card-body, .card-footer {
            padding: var(--spacing-sm);
        }
    }
    </style>
    """
    try:
        st.markdown(css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a kártya stílusok injektálásakor: {str(e)}")

def inject_filter_styles():
    """
    Szűrőpanel és szűrő komponensek stílusainak injektálása.
    
    Ezek a stílusok szűrőpaneleket, címkéket és interaktív szűrő komponenseket tartalmaznak.
    """
    css = """
    <style>
    /* ===== SZŰRŐ STÍLUSOK ===== */
    
    /* Szűrő panel */
    .filter-panel {
        padding: var(--spacing-md);
        background-color: var(--color-white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-medium);
        margin-bottom: var(--spacing-lg);
        transition: all var(--animation-normal);
    }
    
    .filter-panel:hover {
        box-shadow: var(--shadow-large);
    }
    
    .filter-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--color-midgray);
    }
    
    .filter-panel-title {
        font-size: var(--font-size-xl);
        font-weight: 600;
        color: var(--color-primary);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .filter-panel-actions {
        display: flex;
        gap: var(--spacing-sm);
    }
    
    /* Aktív szűrők címkéi */
    .filter-badges {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
        margin: var(--spacing-md) 0;
    }
    
    .filter-badge {
        display: inline-flex;
        align-items: center;
        background-color: var(--color-lightgray);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
        color: var(--color-darkgray);
        gap: var(--spacing-xs);
        transition: all var(--animation-fast);
    }
    
    .filter-badge:hover {
        background-color: var(--color-midgray);
    }
    
    .filter-badge-label {
        font-weight: 500;
    }
    
    .filter-badge-value {
        font-weight: 400;
    }
    
    .filter-badge-clear {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: var(--color-midgray);
        color: var(--color-white);
        margin-left: var(--spacing-xs);
        font-size: 10px;
        transition: all var(--animation-fast);
    }
    
    .filter-badge-clear:hover {
        background-color: var(--color-danger);
    }
    
    /* Szűrő formok */
    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }
    
    .filter-input-group {
        margin-bottom: var(--spacing-md);
    }
    
    .filter-label {
        display: block;
        margin-bottom: var(--spacing-xs);
        font-weight: 500;
        font-size: var(--font-size-sm);
        color: var(--color-darkgray);
    }
    
    /* Dátumszűrő panel */
    .date-filter {
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-md);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .date-filter-title {
        font-weight: 500;
        margin-bottom: var(--spacing-sm);
        color: var(--color-darkgray);
    }
    
    .date-range-slider {
        margin: var(--spacing-sm) 0;
    }
    
    /* Reszponzív kezelés */
    @media screen and (max-width: 768px) {
        .filter-form {
            grid-template-columns: 1fr;
        }
        
        .filter-panel {
            padding: var(--spacing-sm);
        }
        
        .filter-badge {
            font-size: var(--font-size-xs);
        }
    }
    </style>
    """
    try:
        st.markdown(css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a szűrő stílusok injektálásakor: {str(e)}")

def inject_notification_styles():
    """
    Értesítések és üzenetek stílusainak injektálása.
    
    Ezek a stílusok különböző típusú értesítéseket és visszajelzéseket definiálnak.
    """
    css = """
    <style>
    /* ===== ÉRTESÍTÉS STÍLUSOK ===== */
    
    /* Alap értesítés */
    .notification {
        display: flex;
        align-items: flex-start;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-md);
        animation: slide-in var(--animation-normal);
        border-left: 4px solid var(--color-primary);
    }
    
    @keyframes slide-in {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .notification-icon {
        margin-right: var(--spacing-sm);
        font-size: var(--font-size-xl);
        line-height: 1;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-title {
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }
    
    .notification-message {
        color: var(--color-darkgray);
    }
    
    .notification-close {
        cursor: pointer;
        font-size: var(--font-size-lg);
        color: var(--color-darkgray);
        transition: color var(--animation-fast);
    }
    
    .notification-close:hover {
        color: var(--color-black);
    }
    
    /* Értesítés típusok */
    .notification-info {
        background-color: rgba(53, 132, 228, 0.1);
        border-left-color: var(--color-info);
    }
    
    .notification-info .notification-icon {
        color: var(--color-info);
    }
    
    .notification-success {
        background-color: rgba(51, 209, 122, 0.1);
        border-left-color: var(--color-success);
    }
    
    .notification-success .notification-icon {
        color: var(--color-success);
    }
    
    .notification-warning {
        background-color: rgba(255, 163, 72, 0.1);
        border-left-color: var(--color-warning);
    }
    
    .notification-warning .notification-icon {
        color: var(--color-warning);
    }
    
    .notification-error {
        background-color: rgba(224, 27, 36, 0.1);
        border-left-color: var(--color-danger);
    }
    
    .notification-error .notification-icon {
        color: var(--color-danger);
    }
    
    /* Banner értesítés (teljes szélességű) */
    .notification-banner {
        margin: -var(--spacing-md) -var(--spacing-md) var(--spacing-md) -var(--spacing-md);
        border-radius: 0;
    }
    
    /* Inline értesítés (kisebb) */
    .notification-inline {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .notification-inline .notification-icon {
        font-size: var(--font-size-lg);
    }
    </style>
    """
    try:
        st.markdown(css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba az értesítés stílusok injektálásakor: {str(e)}")

def inject_all_styles():
    """
    Az összes stílus injektálása a Streamlit alkalmazásba.
    
    Ez a függvény összegyűjti és megjeleníti az összes CSS stílust.
    """
    try:
        inject_base_styles()
        inject_card_styles()
        inject_filter_styles()
        inject_notification_styles()
        logger.info("Minden stílus sikeresen injektálva")
    except Exception as e:
        logger.error(f"Hiba a stílusok injektálásakor: {str(e)}")

# Segédfüggvények CSS osztállyal ellátott HTML generálásához

def create_card(content: str, title: Optional[str] = None, 
                card_type: str = "primary", collapsible: bool = False,
                collapsed: bool = False, footer: Optional[str] = None,
                id: Optional[str] = None) -> str:
    """
    Modern kártya komponens létrehozása.
    
    Args:
        content: A kártya tartalma (HTML)
        title: A kártya címe (opcionális)
        card_type: A kártya típusa/színe (primary, secondary, success, danger, warning, info)
        collapsible: Összecsukható legyen-e
        collapsed: Alapértelmezetten összecsukott legyen-e
        footer: Kártya lábléc tartalma (opcionális)
        id: Egyedi azonosító (ha None, generálunk egyet)
        
    Returns:
        str: A kártya HTML kódja
    """
    if id is None:
        id = f"card_{str(uuid.uuid4())[:8]}"
    
    # CSS osztályok összeállítása
    card_classes = [f"card card-{card_type}"]
    if collapsible:
        card_classes.append("card-collapsible")
    if collapsed and collapsible:
        card_classes.append("collapsed")
    
    card_classes_str = " ".join(card_classes)
    
    header = ""
    if title:
        toggle_icon = '<span class="card-toggle-icon">▼</span>' if collapsible else ""
        header = f'<div class="card-header" data-target="card-body-{id}">{title}{toggle_icon}</div>'
    
    body_style = 'style="display: none;"' if collapsed and collapsible else ''
    body = f'<div class="card-body" id="card-body-{id}" {body_style}>{content}</div>'
    
    footer_html = f'<div class="card-footer">{footer}</div>' if footer else ""
    
    toggle_script = ""
    if collapsible:
        toggle_script = f"""
        <script>
            // Kártya összecsukás/kinyitás
            document.addEventListener('DOMContentLoaded', function() {{
                const header = document.querySelector('[data-target="card-body-{id}"]');
                if (header) {{
                    header.addEventListener('click', function() {{
                        const target = document.getElementById('card-body-{id}');
                        const card = header.closest('.card');
                        if (target.style.display === 'none') {{
                            target.style.display = 'block';
                            card.classList.remove('collapsed');
                        }} else {{
                            target.style.display = 'none';
                            card.classList.add('collapsed');
                        }}
                    }});
                }}
            }});
        </script>
        """
    
    return f"""
    <div class="{card_classes_str}" id="{id}">
        {header}
        {body}
        {footer_html}
    </div>
    {toggle_script}
    """

def create_filter_panel(content: str, title: str = "Szűrés",
                        icon: str = "🔍", collapsible: bool = True,
                        collapsed: bool = False, reset_button: bool = True,
                        search_button: bool = True, id: Optional[str] = None) -> str:
    """
    Modern szűrőpanel komponens létrehozása.
    
    Args:
        content: A szűrőpanel tartalma (HTML)
        title: A panel címe
        icon: A panel ikonja
        collapsible: Összecsukható legyen-e
        collapsed: Alapértelmezetten összecsukott legyen-e
        reset_button: Legyen-e reset gomb
        search_button: Legyen-e kereső gomb
        id: Egyedi azonosító (ha None, generálunk egyet)
        
    Returns:
        str: A szűrőpanel HTML kódja
    """
    if id is None:
        id = f"filter_{str(uuid.uuid4())[:8]}"
    
    # Gombok
    buttons = ""
    if reset_button:
        buttons += '<button class="filter-reset">🔄 Alaphelyzet</button>'
    if search_button:
        buttons += '<button class="filter-search">🔍 Keresés</button>'
    
    # Fejléc
    toggle_icon = '<span class="filter-toggle">▼</span>' if collapsible else ""
    header = f"""
    <div class="filter-panel-header">
        <div class="filter-panel-title">{icon} {title}</div>
        <div class="filter-panel-actions">
            {buttons}
            {toggle_icon}
        </div>
    </div>
    """
    
    # Tartalom
    body_style = 'style="display: none;"' if collapsed and collapsible else ''
    body = f'<div class="filter-panel-content" id="filter-content-{id}" {body_style}>{content}</div>'
    
    # Összeállítás
    filter_html = f"""
    <div class="filter-panel" id="{id}">
        {header}
        {body}
    </div>
    """
    
    # JavaScript a kollapsz működéséhez
    toggle_script = ""
    if collapsible:
        toggle_script = f"""
        <script>
            document.addEventListener('DOMContentLoaded', function() {{
                const toggle = document.querySelector('#filter-content-{id}').previousElementSibling.querySelector('.filter-toggle');
                if (toggle) {{
                    toggle.addEventListener('click', function() {{
                        const content = document.getElementById('filter-content-{id}');
                        if (content.style.display === 'none') {{
                            content.style.display = 'block';
                            toggle.textContent = '▼';
                        }} else {{
                            content.style.display = 'none';
                            toggle.textContent = '▶';
                        }}
                    }});
                }}
            }});
        </script>
        """
    
    return filter_html + toggle_script

def create_filter_badge(label: str, value: str, 
                        removable: bool = True, id: Optional[str] = None) -> str:
    """
    Szűrőcímke létrehozása.
    
    Args:
        label: A címke neve (pl. "Státusz")
        value: A címke értéke (pl. "Aktív")
        removable: Eltávolítható legyen-e a címke
        id: Egyedi azonosító (ha None, generálunk egyet)
        
    Returns:
        str: A szűrőcímke HTML kódja
    """
    if id is None:
        id = f"badge_{str(uuid.uuid4())[:8]}"
    
    close_button = '<span class="filter-badge-clear">✕</span>' if removable else ''
    
    return f"""
    <div class="filter-badge" id="{id}">
        <span class="filter-badge-label">{label}:</span>
        <span class="filter-badge-value">{value}</span>
        {close_button}
    </div>
    """

def create_notification(message: str, title: Optional[str] = None,
                        type: str = "info", closable: bool = True,
                        inline: bool = False, banner: bool = False,
                        auto_close: bool = False, close_delay: int = 5000,
                        id: Optional[str] = None) -> str:
    """
    Modern értesítés komponens létrehozása.
    
    Args:
        message: Az értesítés üzenete
        title: Az értesítés címe (opcionális)
        type: Az értesítés típusa (info, success, warning, error)
        closable: Bezárható legyen-e
        inline: Inline megjelenítés (kisebb méret)
        banner: Teljes szélességű banner megjelenítés
        auto_close: Automatikusan bezáródjon-e
        close_delay: Automatikus bezáródás késleltetése ms-ben
        id: Egyedi azonosító (ha None, generálunk egyet)
        
    Returns:
        str: Az értesítés HTML kódja
    """
    if id is None:
        id = f"notification_{str(uuid.uuid4())[:8]}"
    
    # Ikonok a különböző típusokhoz
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    
    icon = icons.get(type, icons["info"])
    
    # CSS osztályok
    classes = [f"notification notification-{type}"]
    if inline:
        classes.append("notification-inline")
    if banner:
        classes.append("notification-banner")
    
    classes_str = " ".join(classes)
    
    # Cím komponens
    title_html = f'<div class="notification-title">{title}</div>' if title else ''
    
    # Bezáró gomb
    close_button = '<div class="notification-close" onclick="this.parentElement.remove()">×</div>' if closable else ''
    
    # HTML összeállítása
    notification_html = f"""
    <div class="{classes_str}" id="{id}">
        <div class="notification-icon">{icon}</div>
        <div class="notification-content">
            {title_html}
            <div class="notification-message">{message}</div>
        </div>
        {close_button}
    </div>
    """
    
    # Auto-close script
    autoclose_script = ""
    if auto_close:
        autoclose_script = f"""
        <script>
            // Automatikus bezárás {close_delay} ms után
            setTimeout(function() {{
                const notification = document.getElementById('{id}');
                if (notification) {{
                    notification.style.animation = 'fade-out 0.5s';
                    setTimeout(function() {{
                        notification.remove();
                    }}, 500);
                }}
            }}, {close_delay});
            
            @keyframes fade-out {{
                from {{ opacity: 1; }}
                to {{ opacity: 0; }}
            }}
        </script>
        """
    
    return notification_html + autoclose_script

# Egyszerű Streamlit helper függvények

def display_card(title: str, content_func: Callable, 
                 card_type: str = "primary", collapsible: bool = True,
                 expanded: bool = True, key: Optional[str] = None):
    """
    Streamlit kártya komponens megjelenítése.
    
    Args:
        title: A kártya címe
        content_func: Függvény, ami a kártya tartalmát rendereli
        card_type: A kártya típusa (primary, secondary, success, danger, warning, info)
        collapsible: Összecsukható legyen-e
        expanded: Alapértelmezetten kinyitott legyen-e
        key: Streamlit widget kulcs
    """
    try:
        card_id = key or f"card_{str(uuid.uuid4())[:8]}"
        
        # Ellenőrizzük, hogy a kártya állapotát tároltuk-e már
        card_state_key = f"{card_id}_expanded"
        if card_state_key not in st.session_state:
            st.session_state[card_state_key] = expanded
        
        # Összeállítjuk a CSS kártyaosztályokat
        card_classes = ["card", f"card-{card_type}"]
        if collapsible:
            card_classes.append("card-collapsible")
        if not st.session_state[card_state_key]:
            card_classes.append("collapsed")
        
        # A kártya HTML keretet rendereljük
        st.markdown(f"""
        <div class="{' '.join(card_classes)}" id="{card_id}">
            <div class="card-header" id="card-header-{card_id}">
                {title}
                {f'<span class="card-toggle-icon">{"▼" if st.session_state[card_state_key] else "▶"}</span>' if collapsible else ''}
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Ha a kártya ki van nyitva, akkor rendereljük a tartalmát
        if st.session_state[card_state_key]:
            # Használjuk a st.container-t, hogy a tartalmat a kártyán belül jelenítsük meg
            content_container = st.container()
            with content_container:
                content_func()  # Meghívjuk a tartalmat renderelő függvényt
        
        # JavaScript az összecsukáshoz/kinyitáshoz
        if collapsible:
            js_code = f"""
            <script>
                document.addEventListener('DOMContentLoaded', () => {{
                    const header = document.getElementById('card-header-{card_id}');
                    
                    if (header) {{
                        header.style.cursor = 'pointer';
                        header.addEventListener('click', () => {{
                            // Streamlit komponens átkattintás egy rejtett gombon keresztül
                            document.getElementById('toggle_button_{card_id}').click();
                        }});
                    }}
                }});
            </script>
            """
            st.markdown(js_code, unsafe_allow_html=True)
            
            # Rejtett gomb a kártya állapotának megváltoztatásához
            if st.button("Toggle", key=f"toggle_button_{card_id}", 
                         help="Kártya összecsukása/kinyitása", 
                         label_visibility="collapsed"):
                st.session_state[card_state_key] = not st.session_state[card_state_key]
                st.rerun()
    except Exception as e:
        logger.error(f"Hiba a kártya megjelenítésekor: {str(e)}")
        st.error(f"Nem sikerült megjeleníteni a kártyát: {str(e)}")

def display_filter_panel(title: str, content_func: Callable,
                         icon: str = "🔍", collapsible: bool = True,
                         expanded: bool = True, key: Optional[str] = None):
    """
    Streamlit szűrőpanel megjelenítése.
    
    Args:
        title: A panel címe
        content_func: Függvény, ami a panel tartalmát rendereli
        icon: A panel ikonja
        collapsible: Összecsukható legyen-e
        expanded: Alapértelmezetten kinyitott legyen-e
        key: Streamlit widget kulcs
    """
    try:
        # CSS osztályok egy szűrőpanelhez
        filter_classes = ["filter-panel"]
        if not expanded:
            filter_classes.append("collapsed")
        
        # Egyedi kulcs kezelése
        panel_id = key or f"filter_{str(uuid.uuid4())[:8]}"
        
        # Állapot kulcs a session state-ben
        panel_state_key = f"{panel_id}_expanded"
        if panel_state_key not in st.session_state:
            st.session_state[panel_state_key] = expanded
        
        # Panel fejléc
        st.markdown(f"""
        <div class="{' '.join(filter_classes)}" id="{panel_id}">
            <div class="filter-panel-header" id="filter-header-{panel_id}">
                <div class="filter-panel-title">{icon} {title}</div>
                {f'<div class="filter-panel-toggle">{"▼" if st.session_state[panel_state_key] else "▶"}</div>' if collapsible else ''}
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Panel tartalma, ha ki van nyitva
        if st.session_state[panel_state_key]:
            panel_container = st.container()
            with panel_container:
                content_func()  # Tartalom renderelése
        
        # JavaScript a panel összecsukásához/kinyitásához
        if collapsible:
            js_code = f"""
            <script>
                document.addEventListener('DOMContentLoaded', () => {{
                    const header = document.getElementById('filter-header-{panel_id}');
                    
                    if (header) {{
                        header.style.cursor = 'pointer';
                        header.addEventListener('click', () => {{
                            document.getElementById('panel_toggle_button_{panel_id}').click();
                        }});
                    }}
                }});
            </script>
            """
            st.markdown(js_code, unsafe_allow_html=True)
            
            # Rejtett gomb a panel állapotának megváltoztatásához
            if st.button("Toggle Panel", key=f"panel_toggle_button_{panel_id}", 
                          help="Panel összecsukása/kinyitása",
                          label_visibility="collapsed"):
                st.session_state[panel_state_key] = not st.session_state[panel_state_key]
                st.rerun()
    except Exception as e:
        logger.error(f"Hiba a szűrőpanel megjelenítésekor: {str(e)}")
        st.error(f"Nem sikerült megjeleníteni a szűrőpanelt: {str(e)}")

def display_badges(badges: Dict[str, Any], removable: bool = True, key_prefix: str = "badge"):
    """
    Szűrőcímkék megjelenítése.
    
    Args:
        badges: Címkék szótára {címke: érték} formában
        removable: Eltávolíthatók legyenek-e a címkék
        key_prefix: Kulcs előtag a Streamlit widgetekhez
    """
    if not badges:
        return
    
    try:
        # Címkék HTML generálása
        badges_html = '<div class="filter-badges">'
        badge_keys = []
        
        for idx, (label, value) in enumerate(badges.items()):
            if value is None or value == "":
                continue
                
            badge_id = f"{key_prefix}_{idx}"
            badge_keys.append(badge_id)
            
            close_button = '<span class="filter-badge-clear">✕</span>' if removable else ''
            badges_html += f"""
            <div class="filter-badge" id="{badge_id}">
                <span class="filter-badge-label">{label}:</span>
                <span class="filter-badge-value">{value}</span>
                {close_button}
            </div>
            """
        
        badges_html += '</div>'
        
        # Csak akkor jelenítjük meg, ha van legalább egy címke
        if badge_keys:
            st.markdown(badges_html, unsafe_allow_html=True)
            
            # JavaScript a címkék eltávolításához
            if removable:
                js_code = """
                <script>
                document.addEventListener('DOMContentLoaded', () => {
                    const clearButtons = document.querySelectorAll('.filter-badge-clear');
                    clearButtons.forEach(button => {
                        button.addEventListener('click', () => {
                            const badge = button.closest('.filter-badge');
                            // Itt megtaláljuk és elmentjük a badge ID-t
                            const badgeId = badge.id;
                            // Streamlit gomb aktiválása
                            document.getElementById('clear_' + badgeId).click();
                        });
                    });
                });
                </script>
                """
                st.markdown(js_code, unsafe_allow_html=True)
                
                # Rejtett gombok minden címkéhez
                for badge_id in badge_keys:
                    if st.button("Clear", key=f"clear_{badge_id}", 
                                label_visibility="collapsed"):
                        # Itt kezelnénk a törlési logikát, de egyelőre csak állapotváltozást mutatunk
                        st.success(f"'{badge_id}' címke törölve! (A valós implementációban eltávolítaná a szűrőt)")
                        st.rerun()
    except Exception as e:
        logger.error(f"Hiba a szűrőcímkék megjelenítésekor: {str(e)}")

def show_notification(message: str, type: str = "info", title: Optional[str] = None,
                     auto_dismiss: bool = False, dismiss_seconds: int = 5,
                     inline: bool = False, key: Optional[str] = None):
    """
    Értesítés megjelenítése.
    
    Args:
        message: Az értesítés üzenete
        type: Az értesítés típusa (info, success, warning, error)
        title: Az értesítés címe (opcionális)
        auto_dismiss: Automatikusan eltűnjön-e
        dismiss_seconds: Másodpercek az automatikus eltűnésig
        inline: Kisebb, inline megjelenítés
        key: Streamlit widget kulcs
    """
    try:
        # Kontroll kulcs
        notification_id = key or f"notification_{str(uuid.uuid4())[:8]}"
        notification_state_key = f"{notification_id}_shown"
        
        # Alapértelmezetten az értesítést megjelenítjük
        if notification_state_key not in st.session_state:
            st.session_state[notification_state_key] = True
        
        # Csak akkor jelenítjük meg, ha be van kapcsolva
        if st.session_state[notification_state_key]:
            # Ikonok a különböző típusokhoz
            icons = {
                "info": "ℹ️",
                "success": "✅",
                "warning": "⚠️",
                "error": "❌"
            }
            
            icon = icons.get(type, icons["info"])
            
            # CSS osztályok
            classes = [f"notification notification-{type}"]
            if inline:
                classes.append("notification-inline")
            
            # Cím komponens
            title_html = f'<div class="notification-title">{title}</div>' if title else ''
            
            # HTML összeállítása
            notification_html = f"""
            <div class="{' '.join(classes)}" id="{notification_id}">
                <div class="notification-icon">{icon}</div>
                <div class="notification-content">
                    {title_html}
                    <div class="notification-message">{message}</div>
                </div>
                <div class="notification-close" id="close-{notification_id}">×</div>
            </div>
            """
            
            # JavaScript a bezáráshoz
            js_code = f"""
            <script>
                document.addEventListener('DOMContentLoaded', () => {{
                    const closeBtn = document.getElementById('close-{notification_id}');
                    if (closeBtn) {{
                        closeBtn.addEventListener('click', () => {{
                            document.getElementById('notification_close_button_{notification_id}').click();
                        }});
                    }}
                    
                    {f"setTimeout(() => {{ document.getElementById('notification_close_button_{notification_id}').click(); }}, {dismiss_seconds * 1000});" if auto_dismiss else ""}
                }});
            </script>
            """
            
            # Megjelenítés
            st.markdown(notification_html, unsafe_allow_html=True)
            st.markdown(js_code, unsafe_allow_html=True)
            
            # Rejtett gomb a bezáráshoz
            if st.button("Close Notification", key=f"notification_close_button_{notification_id}", 
                        label_visibility="collapsed"):
                st.session_state[notification_state_key] = False
                st.rerun()
    except Exception as e:
        logger.error(f"Hiba az értesítés megjelenítésekor: {str(e)}")
        # Fallback: standard Streamlit értesítés
        if type == "info":
            st.info(message)
        elif type == "success":
            st.success(message)
        elif type == "warning":
            st.warning(message)
        elif type == "error":
            st.error(message)
        else:
            st.write(message)