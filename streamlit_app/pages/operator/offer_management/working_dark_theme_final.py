"""
Végleges Működő Sötét Téma - st.html() és Natív Komponensek
Working Dark Theme Final - Guaranteed to work with proper HTML rendering
"""
import streamlit as st
import streamlit.components.v1 as components
from datetime import datetime
import plotly.graph_objects as go

class WorkingDarkThemeOffer:
    """Működő sötét témájú ajánlat részletező - végleges verzió"""
    
    def __init__(self, offer_data):
        self.offer = offer_data
        self.inject_base_styles()
        
    def inject_base_styles(self):
        """Alap stílusok alkalmazása"""
        # Globális stílusok st.markdown-nal
        st.markdown("""
        <style>
            /* Sötét háttér */
            .stApp {
                background-color: #0a0a0a;
            }
            
            /* Streamlit komponensek testreszabása */
            .stButton > button {
                background-color: #1a1a1a;
                border: 1px solid #2a2a2a;
                color: white;
                border-radius: 6px;
                padding: 0.5rem 1rem;
                font-weight: 500;
                transition: all 0.2s;
            }
            
            .stButton > button:hover {
                background-color: #2a2a2a;
                border-color: #3a3a3a;
                transform: translateY(-1px);
            }
            
            /* Metrics */
            [data-testid="metric-container"] {
                background-color: #1a1a1a;
                border: 1px solid #2a2a2a;
                padding: 1rem;
                border-radius: 8px;
                margin-bottom: 1rem;
            }
            
            [data-testid="metric-container"] [data-testid="metric-label"] {
                color: #808080 !important;
            }
            
            [data-testid="metric-container"] [data-testid="metric-value"] {
                color: #ffffff !important;
            }
            
            /* Expanderek */
            .streamlit-expanderHeader {
                background-color: #1a1a1a !important;
                border: 1px solid #2a2a2a !important;
                border-radius: 8px !important;
                color: white !important;
                font-weight: 600 !important;
            }
            
            .streamlit-expanderContent {
                background-color: #1a1a1a !important;
                border: 1px solid #2a2a2a !important;
                border-top: none !important;
            }
            
            /* Progress bar */
            .stProgress > div > div {
                background-color: #10dc60;
            }
            
            /* Headers */
            h1, h2, h3, h4, h5, h6 {
                color: #ffffff !important;
            }
            
            /* Text */
            p {
                color: #e0e0e0;
            }
            
            /* Success/Info messages */
            .stSuccess, .stInfo, .stWarning {
                background-color: rgba(26, 26, 26, 0.8) !important;
                border: 1px solid #2a2a2a !important;
            }
        </style>
        """, unsafe_allow_html=True)
    
    def render(self):
        """Fő renderelő metódus"""
        # Fejléc HTML-lel
        self.render_header_with_html()
        
        # Műveleti gombok
        self.render_action_buttons()
        
        # Elválasztó
        st.markdown("---")
        
        # Fő tartalom
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_offer_info()
            self.render_delivery_info()
            self.render_timeline()
        
        with col2:
            self.render_confirmation()
            self.render_producer_info()
            self.render_price_chart()
        
        # Állapot indikátorok
        self.render_status_indicators()
    
    def render_header_with_html(self):
        """Fejléc st.html() használatával"""
        status = self.offer.get('status', 'CREATED')
        status_config = self._get_status_config(status)
        
        header_html = f"""
        <div style="background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
                    border: 1px solid #2a2a2a;
                    border-radius: 12px;
                    padding: 2rem;
                    margin-bottom: 2rem;
                    position: relative;">
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px;
                        background: linear-gradient(90deg, #10dc60, #ffce00, #ff8c1a);"></div>
            
            <h1 style="color: #ffffff; font-size: 2.5rem; margin: 0;">
                Ajánlat #{self.offer.get('id', 'N/A')}
            </h1>
            
            <p style="color: #808080; margin: 0.5rem 0;">
                Utoljára módosítva: {self._format_datetime(self.offer.get('updated_at') or self.offer.get('created_at'))}
            </p>
            
            <div style="display: inline-flex; align-items: center; gap: 0.5rem;
                        padding: 0.5rem 1rem; border-radius: 20px;
                        background: {status_config['bg_color']};
                        border: 1px solid {status_config['border_color']};
                        margin-top: 1rem;">
                <span style="font-size: 1.2rem;">{status_config['icon']}</span>
                <span style="color: {status_config['color']}; font-weight: 600;">
                    {status_config['text']}
                </span>
            </div>
        </div>
        """
        
        # Használjuk st.html() ha elérhető, különben fallback
        try:
            st.html(header_html)
        except (AttributeError, NameError):
            # Ha nincs st.html, használjunk components.html-t
            components.html(header_html, height=200)
    
    def render_action_buttons(self):
        """Műveleti gombok natív Streamlit komponensekkel"""
        st.markdown("#### ⚡ Gyors műveletek")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("✏️ Szerkesztés", use_container_width=True, key="edit_offer"):
                st.session_state.edit_mode = True
                st.success("Szerkesztési mód aktiválva")
        
        with col2:
            if st.button("📋 Másolás", use_container_width=True, key="copy_offer"):
                st.success("Link másolva a vágólapra!")
        
        with col3:
            if st.button("📊 Export", use_container_width=True, key="export_offer"):
                st.info("Export funkció fejlesztés alatt")
        
        with col4:
            if st.button("🗑️ Törlés", use_container_width=True, key="delete_offer", type="secondary"):
                st.warning("Biztosan törölni szeretnéd?")
    
    def render_offer_info(self):
        """Ajánlat információk - Expander használatával"""
        with st.expander("📋 Ajánlat adatai", expanded=True):
            # HTML táblázat az adatokhoz
            product = self.offer.get('product_type', {})
            category = product.get('category', {}) if product else {}
            
            info_html = f"""
            <table style="width: 100%; color: #e0e0e0; border-collapse: collapse;">
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Azonosító:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {self.offer.get('id', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Beszállítás:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {self._format_date(self.offer.get('delivery_date'))}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Termék:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {product.get('name', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Kategória:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {category.get('name', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080;">Mennyiség:</td>
                    <td style="padding: 0.75rem 0; text-align: right; color: #0099e0; font-weight: 600;">
                        {self._format_quantity(self.offer.get('quantity_in_kg'))} kg
                    </td>
                </tr>
            </table>
            """
            
            try:
                st.html(info_html)
            except (AttributeError, NameError):
                # Fallback: használjunk natív Streamlit komponenseket
                col1, col2 = st.columns([1, 1])
                with col1:
                    st.write("**Azonosító:**")
                    st.write("**Beszállítás:**")
                    st.write("**Termék:**") 
                    st.write("**Kategória:**")
                    st.write("**Mennyiség:**")
                with col2:
                    st.write(f"{self.offer.get('id', 'N/A')}")
                    st.write(f"{self._format_date(self.offer.get('delivery_date'))}")
                    st.write(f"{product.get('name', 'N/A')}")
                    st.write(f"{category.get('name', 'N/A')}")
                    st.write(f"**{self._format_quantity(self.offer.get('quantity_in_kg'))} kg**")
    
    def render_delivery_info(self):
        """Szállítási információk"""
        with st.expander("🚚 Szállítási információk", expanded=True):
            delivery_date = self.offer.get('delivery_date')
            delivery_status = self._get_delivery_status(delivery_date)
            
            # HTML táblázat
            delivery_html = f"""
            <table style="width: 100%; color: #e0e0e0; border-collapse: collapse;">
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Szállítási dátum:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {self._format_date(delivery_date)}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Státusz:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {delivery_status}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Szállítási cím:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        Központi raktár
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080;">Szállítási mód:</td>
                    <td style="padding: 0.75rem 0; text-align: right;">
                        Saját szállítás
                    </td>
                </tr>
            </table>
            """
            
            try:
                st.html(delivery_html)
            except (AttributeError, NameError):
                # Fallback
                col1, col2 = st.columns([1, 1])
                with col1:
                    st.write("**Szállítási dátum:**")
                    st.write("**Státusz:**")
                    st.write("**Szállítási cím:**")
                    st.write("**Szállítási mód:**")
                with col2:
                    st.write(f"{self._format_date(delivery_date)}")
                    st.write(f"{delivery_status}")
                    st.write("Központi raktár")
                    st.write("Saját szállítás")
    
    def render_confirmation(self):
        """Visszaigazolás - Metrics és Progress bar"""
        with st.expander("📊 Visszaigazolás", expanded=True):
            quantity = self._to_float(self.offer.get('quantity_in_kg', 0))
            confirmed_quantity = self._to_float(self.offer.get('confirmed_quantity', quantity))
            confirmed_price = self._to_float(self.offer.get('confirmed_price', self.offer.get('price', 0)))
            progress = (confirmed_quantity / quantity) if quantity > 0 else 0
            
            # Metrics használata
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric(
                    label="Eredeti mennyiség",
                    value=f"{self._format_quantity(quantity)} kg"
                )
                st.metric(
                    label="Eredeti ár", 
                    value=f"{self._format_price(self.offer.get('price', 0))}/kg"
                )
            
            with col2:
                st.metric(
                    label="Visszaigazolt",
                    value=f"{self._format_quantity(confirmed_quantity)} kg",
                    delta=f"{(progress*100):.0f}%"
                )
                st.metric(
                    label="Visszaigazolt ár",
                    value=f"{self._format_price(confirmed_price)}/kg"
                )
            
            # Progress bar
            st.progress(progress)
            st.markdown(f"<p style='text-align: center; color: #808080;'>{progress*100:.1f}% teljesítve</p>", 
                       unsafe_allow_html=True)
            
            # Összérték
            total_value = confirmed_quantity * confirmed_price
            st.markdown(f"""
            <div style='text-align: center; margin-top: 1rem; padding: 1rem; 
                        background: rgba(0, 153, 224, 0.1); border-radius: 8px; 
                        border: 1px solid rgba(0, 153, 224, 0.3);'>
                <div style='color: #808080; font-size: 0.875rem; margin-bottom: 0.5rem;'>
                    Összérték
                </div>
                <div style='color: #0099e0; font-size: 1.5rem; font-weight: 600;'>
                    {self._format_price(total_value)}
                </div>
            </div>
            """, unsafe_allow_html=True)
    
    def render_timeline(self):
        """Idősor - Egyszerű HTML listával"""
        with st.expander("📅 Állapotváltozási napló", expanded=False):
            events = [
                {
                    'date': self.offer.get('created_at'),
                    'title': 'Létrehozva',
                    'user': 'Rendszer',
                    'color': '#ffce00'
                },
                {
                    'date': self.offer.get('confirmed_at'),
                    'title': 'Cég által visszaigazolva',
                    'user': 'Kiss Péter',
                    'color': '#ff8c1a'
                },
                {
                    'date': self.offer.get('accepted_at'),
                    'title': 'Elfogadva',
                    'user': self.offer.get('user', {}).get('contact_name', 'N/A'),
                    'color': '#10dc60'
                }
            ]
            
            timeline_html = "<div style='padding: 1rem;'>"
            
            for event in events:
                if event['date']:
                    timeline_html += f"""
                    <div style='display: flex; gap: 1rem; margin-bottom: 1.5rem; 
                                padding: 0.75rem; background: rgba(255,255,255,0.02); 
                                border-radius: 8px; border: 1px solid #2a2a2a;'>
                        <div style='width: 12px; height: 12px; background: {event['color']};
                                    border-radius: 50%; margin-top: 0.5rem; flex-shrink: 0;'></div>
                        <div style='flex: 1;'>
                            <div style='color: #ffffff; font-weight: 500; margin-bottom: 0.25rem;'>
                                {event['title']}
                            </div>
                            <div style='color: #808080; font-size: 0.875rem;'>
                                {self._format_datetime(event['date'])} • {event['user']}
                            </div>
                        </div>
                    </div>
                    """
            
            timeline_html += "</div>"
            
            try:
                st.html(timeline_html)
            except (AttributeError, NameError):
                # Fallback: egyszerű lista
                for event in events:
                    if event['date']:
                        st.markdown(f"**{event['title']}**  \n"
                                  f"_{self._format_datetime(event['date'])} • {event['user']}_")
    
    def render_producer_info(self):
        """Termelő információk"""
        with st.expander("👤 Termelő adatai", expanded=True):
            user = self.offer.get('user', {})
            
            # HTML táblázat
            producer_html = f"""
            <table style="width: 100%; color: #e0e0e0; border-collapse: collapse;">
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Név:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {user.get('contact_name', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Cégnév:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {user.get('company_name', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080; border-bottom: 1px solid #2a2a2a;">Telefon:</td>
                    <td style="padding: 0.75rem 0; text-align: right; border-bottom: 1px solid #2a2a2a;">
                        {user.get('phone', 'N/A')}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 0.75rem 0; color: #808080;">Email:</td>
                    <td style="padding: 0.75rem 0; text-align: right;">
                        <a href="mailto:{user.get('email', '')}" style="color: #0099e0; text-decoration: none;">
                            {user.get('email', 'N/A')}
                        </a>
                    </td>
                </tr>
            </table>
            """
            
            try:
                st.html(producer_html)
            except (AttributeError, NameError):
                # Fallback
                col1, col2 = st.columns([1, 1])
                with col1:
                    st.write("**Név:**")
                    st.write("**Cégnév:**")
                    st.write("**Telefon:**")
                    st.write("**Email:**")
                with col2:
                    st.write(f"{user.get('contact_name', 'N/A')}")
                    st.write(f"{user.get('company_name', 'N/A')}")
                    st.write(f"{user.get('phone', 'N/A')}")
                    email = user.get('email', 'N/A')
                    if email != 'N/A' and '@' in email:
                        st.markdown(f"[{email}](mailto:{email})")
                    else:
                        st.write(email)
    
    def render_price_chart(self):
        """Ár összehasonlítás - Plotly chart"""
        with st.expander("📈 Ár összehasonlítás", expanded=False):
            fig = go.Figure()
            
            categories = ['Eredeti', 'Visszaigazolt']
            values = [
                self._to_float(self.offer.get('price', 0)),
                self._to_float(self.offer.get('confirmed_price', self.offer.get('price', 0)))
            ]
            
            fig.add_trace(go.Bar(
                x=categories,
                y=values,
                marker_color=['#ff8c1a', '#10dc60'],
                text=[f'{v:,.0f} Ft' for v in values],
                textposition='outside',
                textfont=dict(color='#ffffff', size=14)
            ))
            
            fig.update_layout(
                plot_bgcolor='#1a1a1a',
                paper_bgcolor='#1a1a1a',
                font=dict(color='#ffffff'),
                showlegend=False,
                height=300,
                margin=dict(l=20, r=20, t=30, b=20),
                yaxis=dict(
                    gridcolor='#2a2a2a',
                    title='Egységár (Ft/kg)',
                    titlefont=dict(color='#808080')
                ),
                xaxis=dict(
                    gridcolor='#2a2a2a'
                )
            )
            
            st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
    
    def render_status_indicators(self):
        """Állapot indikátorok - components.html használatával"""
        st.markdown("### 📊 Státusz előzmények")
        
        # Státusz alapú progresszió
        current_status = self.offer.get('status', 'CREATED')
        status_progression = ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'FINALIZED']
        
        try:
            current_index = status_progression.index(current_status)
        except ValueError:
            current_index = 0
        
        states = [
            ("Létrehozva", 0),
            ("Visszaigazolva", 1),
            ("Elfogadva", 2),
            ("Véglegesítve", 3)
        ]
        
        indicators_html = """
        <div style='display: flex; gap: 1rem; margin-top: 1rem;'>
        """
        
        for label, index in states:
            active = index <= current_index
            bg_color = "rgba(16, 220, 96, 0.1)" if active else "#1a1a1a"
            border_color = "#10dc60" if active else "#2a2a2a"
            text_color = "#10dc60" if active else "#808080"
            value = "✓" if active else "-"
            
            indicators_html += f"""
            <div style='flex: 1; background: {bg_color}; border: 1px solid {border_color};
                        border-radius: 8px; padding: 1rem; text-align: center;
                        transition: all 0.3s ease;'>
                <div style='color: #808080; font-size: 0.75rem; text-transform: uppercase;
                           margin-bottom: 0.5rem;'>{label}</div>
                <div style='color: {text_color}; font-size: 1.5rem; font-weight: 600;'>
                    {value}
                </div>
            </div>
            """
        
        indicators_html += "</div>"
        
        # Használjuk components.html-t nagyobb kontrollért
        components.html(indicators_html, height=100)
    
    # Segéd metódusok
    def _to_float(self, value):
        """Érték biztonságos float konvertálása"""
        try:
            if value is None:
                return 0.0
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
                return float(cleaned) if cleaned else 0.0
            return 0.0
        except (ValueError, TypeError):
            return 0.0
    
    def _get_status_config(self, status):
        """Státusz konfiguráció"""
        status_configs = {
            'CREATED': {
                'color': '#ffce00',
                'bg_color': 'rgba(255, 206, 0, 0.2)',
                'border_color': 'rgba(255, 206, 0, 0.3)',
                'icon': '🆕',
                'text': 'Létrehozva'
            },
            'CONFIRMED_BY_COMPANY': {
                'color': '#ff8c1a',
                'bg_color': 'rgba(255, 140, 26, 0.2)',
                'border_color': 'rgba(255, 140, 26, 0.3)',
                'icon': '✅',
                'text': 'Visszaigazolva'
            },
            'ACCEPTED_BY_USER': {
                'color': '#10dc60',
                'bg_color': 'rgba(16, 220, 96, 0.2)',
                'border_color': 'rgba(16, 220, 96, 0.3)',
                'icon': '✔️',
                'text': 'Elfogadva'
            },
            'REJECTED_BY_USER': {
                'color': '#ff5045',
                'bg_color': 'rgba(255, 80, 69, 0.2)',
                'border_color': 'rgba(255, 80, 69, 0.3)',
                'icon': '❌',
                'text': 'Elutasítva'
            },
            'FINALIZED': {
                'color': '#0099e0',
                'bg_color': 'rgba(0, 153, 224, 0.2)',
                'border_color': 'rgba(0, 153, 224, 0.3)',
                'icon': '🔒',
                'text': 'Véglegesítve'
            }
        }
        
        return status_configs.get(status, status_configs['CREATED'])
    
    def _get_delivery_status(self, delivery_date):
        """Szállítási státusz meghatározása"""
        if not delivery_date:
            return "Nincs megadva"
        
        try:
            if isinstance(delivery_date, str):
                delivery_dt = datetime.fromisoformat(delivery_date.replace('Z', '+00:00'))
            else:
                delivery_dt = delivery_date
            
            days_until = (delivery_dt - datetime.now()).days
            
            if days_until < 0:
                return f"Lejárt ({abs(days_until)} napja)"
            elif days_until == 0:
                return "Ma esedékes"
            elif days_until == 1:
                return "Holnap esedékes"
            else:
                return f"{days_until} nap múlva esedékes"
        except:
            return "Nincs megadva"
    
    def _format_quantity(self, value):
        """Mennyiség formázása"""
        numeric_value = self._to_float(value)
        return f"{numeric_value:,.0f}"
    
    def _format_price(self, value):
        """Ár formázása"""
        numeric_value = self._to_float(value)
        return f"{numeric_value:,.0f} Ft"
    
    def _format_date(self, value):
        """Dátum formázása"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d.")
            return "-"
        except:
            return "-"
    
    def _format_datetime(self, value):
        """Dátum és idő formázása"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y. %m. %d. %H:%M")
            return "-"
        except:
            return "-"

# Convenience function for direct use
def render_working_dark_theme_offer(offer):
    """Direct function to render the working dark theme"""
    working_theme = WorkingDarkThemeOffer(offer)
    working_theme.render()