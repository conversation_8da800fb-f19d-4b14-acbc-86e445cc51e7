"""
API client functions for the offer management page.
Contains wrappers around the API calls to the backend.
"""
import streamlit as st
import logging
import sys
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flexible API imports
try:
    # Try Docker path first
    from pages.api import offers as offers_api
    from pages.api import products as products_api
    from pages.api import users as users_api
    logger.info("Successfully imported API modules from Docker path")
except ImportError:
    try:
        # Try direct import
        from api import offers as offers_api
        from api import products as products_api
        from api import users as users_api
        logger.info("Successfully imported API modules directly")
    except ImportError:
        try:
            # Try fully qualified path
            from streamlit_app.api import offers as offers_api
            from streamlit_app.api import products as products_api
            from streamlit_app.api import users as users_api
            logger.info("Successfully imported API modules from streamlit_app path")
        except ImportError:
            logger.error("Failed to import API modules. Application will likely fail.")
            # Create minimal placeholder modules to prevent complete failure
            class DummyAPI:
                def __getattr__(self, name):
                    def dummy_func(*args, **kwargs):
                        return False, "API import error: Module not available"
                    return dummy_func
            offers_api = products_api = users_api = DummyAPI()

from datetime import datetime, timedelta

# Python version-independent typing imports
try:
    from typing import Dict, List, Any, Tuple, Optional, Union
except ImportError:
    # Fallback for older Python versions
    from typing import Any, List, Optional, Union, Tuple
    Dict = dict

# Additional safety check for Dict
if 'Dict' not in globals():
    Dict = dict

# Import helper functions with flexible paths
try:
    # Try Docker path
    from pages.utils.api_client import safe_api_call, handle_api_error, fetch_data_with_progress
    logger.info("Successfully imported API client utils from Docker path")
except ImportError:
    try:
        # Try direct import
        from utils.api_client import safe_api_call, handle_api_error, fetch_data_with_progress
        logger.info("Successfully imported API client utils directly")
    except ImportError:
        try:
            # Try fully qualified path
            from streamlit_app.utils.api_client import safe_api_call, handle_api_error, fetch_data_with_progress
            logger.info("Successfully imported API client utils from streamlit_app path")
        except ImportError:
            try:
                # Try local import as fallback
                from api_client import safe_api_call, handle_api_error, fetch_data_with_progress
                logger.info("Successfully imported API client utils from local module")
            except ImportError:
                # Implement minimal versions of the helper functions
                logger.warning("Using fallback implementations for API client utils")
                
                def safe_api_call(func, operation_name, *args, **kwargs):
                    """Fallback safe API call implementation"""
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        logger.error(f"Error in {operation_name}: {str(e)}")
                        return False, f"Hiba: {str(e)}"
                
                def handle_api_error(error, operation_name):
                    """Fallback error handler implementation"""
                    return f"Hiba a(z) {operation_name} során: {str(error)}"
                
                def fetch_data_with_progress(api_func, *args, **kwargs):
                    """Fallback progress implementation"""
                    return api_func(*args, **kwargs)

def get_offers(query_params=None):
    """
    ENHANCED Ajánlatok lekérése az API-n keresztül - comprehensive debug és producer validation
    
    Args:
        query_params (dict, optional): Szűrési paraméterek. Defaults to None.
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a válasz vagy hibaüzenet
    """
    
    # Enhanced debug logging
    call_timestamp = datetime.now()
    logger.critical(f"🚀 API CALL START: get_offers at {call_timestamp.strftime('%H:%M:%S.%f')[:-3]}")
    logger.critical(f"🔍 API CALL PARAMS: {query_params}")
    
    try:
        import streamlit as st
        if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
            st.write(f"🔍 **get_offers() hívás**: {call_timestamp.strftime('%H:%M:%S.%f')[:-3]}")
            st.write(f"**Bemenő paraméterek**: {query_params}")
            
            # Log producer-related parameters specifically
            if query_params:
                producer_params = {k: v for k, v in query_params.items() 
                                 if any(term in k.lower() for term in ['user', 'producer', 'owner', 'created_by'])}
                if producer_params:
                    st.write(f"🎯 **Producer/User kapcsolatos paraméterek**: {producer_params}")
    except ImportError:
        pass
    
    try:
        params = query_params.copy() if query_params else {}
        
        # Fix field names if needed
        if 'from_date' in params:
            params['date_from'] = params.pop('from_date')
        if 'to_date' in params:
            params['date_to'] = params.pop('to_date')
        
        # Extract producer-related params for validation
        producer_params_to_validate = {}
        for param_name in ['user_id', 'producer_id', 'created_by_user_id', 'filter_user_id', 'owner_id']:
            if param_name in params:
                producer_params_to_validate[param_name] = params[param_name]
        
        logger.critical(f"🔍 FINAL API PARAMS: {params}")
        logger.critical(f"🎯 PRODUCER PARAMS TO VALIDATE: {producer_params_to_validate}")
        
        # Make the API call
        result = safe_api_call(offers_api.get_offers, "ajánlatok betöltése", params)
        
        # Enhanced response analysis
        if isinstance(result, tuple):
            success, data = result
        else:
            success, data = True, result
        
        response_timestamp = datetime.now()
        call_duration = (response_timestamp - call_timestamp).total_seconds()
        
        # Safe count calculation
        data_count = 0
        if success and data:
            if isinstance(data, list):
                data_count = len(data)
            else:
                data_count = f"Error: {data}"
        
        logger.critical(f"📊 API RESPONSE: Success={success}, Count={data_count}, Duration={call_duration:.3f}s")
        
        # CRITICAL: Validate producer filter if we have producer parameters
        if success and data and producer_params_to_validate:
            validation_result = validate_producer_filter_response(producer_params_to_validate, data)
            
            # Log validation results
            logger.critical(f"🔍 PRODUCER VALIDATION: {validation_result}")
            
            # Display validation in UI if available
            try:
                if hasattr(st, 'session_state'):
                    st.write("**🎯 Producer Filter Validation:**")
                    
                    if validation_result.get('validation_passed'):
                        st.success(f"✅ Producer filter működik: {validation_result.get('matching_offers', 0)} egyező ajánlat")
                    else:
                        st.error("❌ Producer filter NEM működik megfelelően!")
                        for issue in validation_result.get('issues', []):
                            st.error(f"⚠️ {issue}")
                        
                        # Show field analysis
                        field_analysis = validation_result.get('field_analysis', {})
                        if field_analysis:
                            st.write("**Talált mezők az ajánlatokban:**")
                            for field, stats in field_analysis.items():
                                if stats['count'] > 0:
                                    st.write(f"- {field}: {stats['count']} ajánlat, értékek: {stats['values']}")
                        
                        # Show recommended primary field
                        recommended_field = validation_result.get('recommended_primary_field')
                        if recommended_field:
                            st.info(f"💡 Ajánlott elsődleges mező: {recommended_field}")
            except ImportError:
                pass
        
        # Enhanced debug output (csak debug módban)
        try:
            import streamlit as st
            if hasattr(st, 'session_state') and st.session_state.get("debug_mode", False):
                # Safe size calculation for UI
                if success and data and isinstance(data, list):
                    api_response_size = len(data)
                elif success and data:
                    api_response_size = f"Error: {type(data).__name__}"
                else:
                    api_response_size = 0
                    
                st.write(f"**API válasz mérete**: {api_response_size}")
                st.write(f"**API hívás időtartama**: {call_duration:.3f} másodperc")
                
                if success and data and isinstance(data, list) and len(data) > 0:
                    sample_offer = data[0]
                    st.write("**Minta ajánlat mezői:**")
                    st.write(f"Elérhető mezők: {list(sample_offer.keys())}")
                    
                    # Show user-related fields specifically
                    user_fields = {k: v for k, v in sample_offer.items() 
                                 if any(term in k.lower() for term in ['user', 'producer', 'owner', 'created'])}
                    if user_fields:
                        st.write(f"User/Producer mezők: {user_fields}")
        except ImportError:
            pass
        
        return result
        
    except Exception as e:
        error_timestamp = datetime.now()
        error_duration = (error_timestamp - call_timestamp).total_seconds()
        
        logger.error(f"🚨 API CALL ERROR after {error_duration:.3f}s: {str(e)}")
        return False, f"Hiba az ajánlatok betöltése során: {str(e)}"

def validate_producer_filter_response(producer_params: Dict[str, int], results: List[Dict]) -> Dict[str, Any]:
    """Producer filter működésének validálása API válaszon"""
    
    validation = {
        'timestamp': datetime.now().isoformat(),
        'producer_params': producer_params,
        'total_results': len(results),
        'validation_passed': False,
        'matching_offers': 0,
        'found_user_ids': set(),
        'field_analysis': {},
        'issues': [],
        'recommended_primary_field': None
    }
    
    if not results:
        validation['issues'].append("No results returned from API")
        return validation
    
    # Get the expected user ID (should be the same across all producer params)
    expected_user_ids = set(producer_params.values())
    if len(expected_user_ids) != 1:
        validation['issues'].append(f"Inconsistent producer IDs in params: {expected_user_ids}")
        return validation
    
    expected_user_id = list(expected_user_ids)[0]
    
    # Analyze field usage in results
    field_stats = {
        'user_id': {'count': 0, 'values': set()},
        'created_by_user_id': {'count': 0, 'values': set()},
        'user.id': {'count': 0, 'values': set()},
        'producer_id': {'count': 0, 'values': set()},
        'owner_id': {'count': 0, 'values': set()}
    }
    
    for offer in results:
        # Check all possible user/producer ID fields
        user_id = offer.get('user_id')
        created_by_id = offer.get('created_by_user_id')
        user_obj_id = offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None
        producer_id = offer.get('producer_id')
        owner_id = offer.get('owner_id')
        
        # Track field usage
        if user_id is not None:
            field_stats['user_id']['count'] += 1
            field_stats['user_id']['values'].add(user_id)
            validation['found_user_ids'].add(user_id)
            
        if created_by_id is not None:
            field_stats['created_by_user_id']['count'] += 1
            field_stats['created_by_user_id']['values'].add(created_by_id)
            validation['found_user_ids'].add(created_by_id)
            
        if user_obj_id is not None:
            field_stats['user.id']['count'] += 1
            field_stats['user.id']['values'].add(user_obj_id)
            validation['found_user_ids'].add(user_obj_id)
            
        if producer_id is not None:
            field_stats['producer_id']['count'] += 1
            field_stats['producer_id']['values'].add(producer_id)
            validation['found_user_ids'].add(producer_id)
            
        if owner_id is not None:
            field_stats['owner_id']['count'] += 1
            field_stats['owner_id']['values'].add(owner_id)
            validation['found_user_ids'].add(owner_id)
        
        # Check if this offer matches expected user ID
        offer_user_ids = [user_id, created_by_id, user_obj_id, producer_id, owner_id]
        if expected_user_id in [id for id in offer_user_ids if id is not None]:
            validation['matching_offers'] += 1
    
    # Convert sets to lists for JSON serialization
    validation['found_user_ids'] = list(validation['found_user_ids'])
    for field, stats in field_stats.items():
        stats['values'] = list(stats['values'])
    
    validation['field_analysis'] = field_stats
    
    # Determine validation result
    if validation['matching_offers'] == 0:
        validation['validation_passed'] = False
        validation['issues'].append(
            f"CRITICAL: No offers match expected user_id {expected_user_id}, found {validation['found_user_ids']}"
        )
    elif validation['matching_offers'] < validation['total_results']:
        validation['validation_passed'] = False
        validation['issues'].append(
            f"PARTIAL: Only {validation['matching_offers']}/{validation['total_results']} offers match"
        )
    else:
        validation['validation_passed'] = True
    
    # Identify most promising field
    best_field = None
    best_score = 0
    for field_name, stats in field_stats.items():
        if stats['count'] > 0:
            score = stats['count']
            if score > best_score:
                best_score = score
                best_field = field_name
    
    validation['recommended_primary_field'] = best_field
    
    return validation

def get_offer(offer_id):
    """
    Egy ajánlat részletes adatainak lekérése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result az ajánlat vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching offer details for ID: {offer_id}")
        return safe_api_call(offers_api.get_offer, "ajánlat részleteinek betöltése", offer_id)
    except Exception as e:
        logger.error(f"Error fetching offer details: {str(e)}")
        return False, f"Hiba az ajánlat részleteinek betöltése során: {str(e)}"

def update_offer(offer_id, data):
    """
    Ajánlat frissítése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        data (dict): Az új ajánlatadatok
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a frissített ajánlat vagy hibaüzenet
    """
    try:
        logger.info(f"Updating offer ID: {offer_id} with data: {data}")
        return safe_api_call(offers_api.update_offer, "ajánlat frissítése", offer_id, data)
    except Exception as e:
        logger.error(f"Error updating offer: {str(e)}")
        return False, f"Hiba az ajánlat frissítése során: {str(e)}"

def update_offer_status(offer_id, status, confirmation_data=None):
    """
    Ajánlat státuszának frissítése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        status (str): Az új státusz
        confirmation_data (dict, optional): Visszaigazolási adatok (confirmed_quantity, confirmed_price).
                                           Szükséges CONFIRMED_BY_COMPANY státusznál.
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a frissített ajánlat vagy hibaüzenet
    """
    try:
        logger.info(f"Updating offer status for ID: {offer_id} to: {status}")
        
        # Ha CONFIRMED_BY_COMPANY státuszra váltunk, szükségünk van a confirmation_data-ra
        if status == "CONFIRMED_BY_COMPANY":
            # Ha nem adták meg a confirmation_data-t, de van mennyiség és ár paraméter
            if isinstance(confirmation_data, (dict, type(None))):
                # Ha nem adták meg a szükséges adatokat, lekérjük az ajánlat adatait
                if confirmation_data is None or "confirmed_quantity" not in confirmation_data or "confirmed_price" not in confirmation_data:
                    logger.warning("Hiányzó vagy hiányos confirmation_data")
                    
                    # Lekérjük az ajánlat adatait, hogy legyen alapértelmezett értékünk
                    success, offer = safe_api_call(offers_api.get_offer, "ajánlat részleteinek betöltése", offer_id)
                    if not success:
                        return False, f"Nem sikerült lekérni az ajánlat adatait a visszaigazoláshoz: {offer}"
                    
                    # Létrehozzuk vagy kiegészítjük a confirmation_data-t
                    if confirmation_data is None:
                        confirmation_data = {}
                    
                    # Ha nincs megadva a mennyiség, használjuk az eredeti mennyiséget
                    if "confirmed_quantity" not in confirmation_data:
                        confirmation_data["confirmed_quantity"] = offer.get("quantity_in_kg", 0)
                    
                    # Ha nincs megadva az ár, használjuk az eredeti árat vagy az alapértelmezett értéket
                    if "confirmed_price" not in confirmation_data:
                        confirmation_data["confirmed_price"] = offer.get("price", 100)  # 100 Ft/kg alapértelmezett ár
            
            # Ha a confirmation_data nem dictionary, de egyéb paraméterként kaptuk (back-compat)
            elif confirmation_data is not None and not isinstance(confirmation_data, dict):
                # A paraméter valószínűleg a régi note paraméter, ebben az esetben
                # figyelmeztetünk és figyelmen kívül hagyjuk
                logger.warning(f"Nem dictionary típusú confirmation_data: {type(confirmation_data)}. Figyelmen kívül hagyjuk.")
                confirmation_data = {
                    "confirmed_quantity": 0,
                    "confirmed_price": 100
                }
            
            logger.info(f"Confirming offer with data: {confirmation_data}")
            
            # Ha a confirmation_data még mindig None, alapértelmezett értékeket adunk meg
            if confirmation_data is None:
                confirmation_data = {
                    "confirmed_quantity": 0,
                    "confirmed_price": 100
                }
            
            return safe_api_call(offers_api.update_offer_status, "ajánlat státuszának frissítése", 
                                 offer_id, status, confirmation_data)
        else:
            # Más státuszoknál nincs szükség a visszaigazolási adatokra
            return safe_api_call(offers_api.update_offer_status, "ajánlat státuszának frissítése", 
                                 offer_id, status)
    except Exception as e:
        logger.error(f"Error updating offer status: {str(e)}")
        return False, f"Hiba az ajánlat státuszának frissítése során: {str(e)}"

def delete_offer(offer_id):
    """
    Ajánlat törlése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a válasz vagy hibaüzenet
    """
    try:
        logger.info(f"Deleting offer ID: {offer_id}")
        return safe_api_call(offers_api.delete_offer, "ajánlat törlése", offer_id)
    except Exception as e:
        logger.error(f"Error deleting offer: {str(e)}")
        return False, f"Hiba az ajánlat törlése során: {str(e)}"

def get_offer_logs(offer_id, log_type="status"):
    """
    Ajánlat naplóbejegyzéseinek lekérése az API-n keresztül.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        log_type (str, optional): A naplóbejegyzések típusa (status, audit). Defaults to "status".
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a naplóbejegyzések listája vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching offer logs for offer #{offer_id}, type: {log_type}")
        
        # A valós API hívás a megfelelő hibakapszulázással
        return safe_api_call(offers_api.get_offer_logs, "ajánlat naplóbejegyzéseinek betöltése", offer_id)
    except Exception as e:
        logger.exception(f"Error fetching offer logs: {str(e)}")
        return False, f"Hiba a naplóbejegyzések lekérésekor: {str(e)}"

def get_offer_attachments(offer_id):
    """
    Ajánlathoz tartozó csatolmányok lekérése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a csatolmányok vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching offer attachments for ID: {offer_id}")
        return safe_api_call(offers_api.get_offer_attachments, "ajánlat csatolmányainak betöltése", offer_id)
    except Exception as e:
        logger.error(f"Error fetching offer attachments: {str(e)}")
        return False, f"Hiba az ajánlat csatolmányainak betöltése során: {str(e)}"

def get_product_type(product_type_id):
    """
    Terméktípus részletes adatainak lekérése az API-n keresztül.
    
    Args:
        product_type_id (int/str): A terméktípus azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a terméktípus vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching product type details for ID: {product_type_id}")
        return safe_api_call(products_api.get_product_type, "terméktípus részleteinek betöltése", product_type_id)
    except Exception as e:
        logger.error(f"Error fetching product type details: {str(e)}")
        return False, f"Hiba a terméktípus részleteinek betöltése során: {str(e)}"

def get_quality_grade(quality_grade_id):
    """
    Minőségi osztály részletes adatainak lekérése az API-n keresztül.
    
    Args:
        quality_grade_id (int/str): A minőségi osztály azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a minőségi osztály vagy hibaüzenet
    """
    try:
        if hasattr(products_api, 'get_quality_grade'):
            logger.info(f"Fetching quality grade details for ID: {quality_grade_id}")
            return safe_api_call(products_api.get_quality_grade, "minőségi osztály részleteinek betöltése", quality_grade_id)
        else:
            logger.warning("get_quality_grade function not available in products_api")
            return False, "A minőségi osztály lekérdezési funkció nem elérhető"
    except Exception as e:
        logger.error(f"Error fetching quality grade details: {str(e)}")
        return False, f"Hiba a minőségi osztály részleteinek betöltése során: {str(e)}"

def get_product_types():
    """
    Összes terméktípus lekérése az API-n keresztül.
    
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a terméktípusok vagy hibaüzenet
    """
    try:
        logger.info("Fetching all product types")
        return safe_api_call(products_api.get_product_types, "terméktípusok betöltése")
    except Exception as e:
        logger.error(f"Error fetching product types: {str(e)}")
        return False, f"Hiba a terméktípusok betöltése során: {str(e)}"

def get_quality_grades():
    """
    Összes minőségi osztály lekérése az API-n keresztül.
    
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a minőségi osztályok vagy hibaüzenet
    """
    try:
        if hasattr(products_api, 'get_quality_grades'):
            logger.info("Fetching all quality grades")
            return safe_api_call(products_api.get_quality_grades, "minőségi osztályok betöltése")
        else:
            logger.warning("get_quality_grades function not available in products_api")
            return False, "A minőségi osztályok lekérdezési funkció nem elérhető"
    except Exception as e:
        logger.error(f"Error fetching quality grades: {str(e)}")
        return False, f"Hiba a minőségi osztályok betöltése során: {str(e)}"

def get_producer(producer_id):
    """
    Termelő részletes adatainak lekérése az API-n keresztül.
    
    Args:
        producer_id (int/str): A termelő azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a termelő vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching producer details for ID: {producer_id}")
        result = safe_api_call(users_api.get_user, "termelő részleteinek betöltése", producer_id)
        
        # Ensure we always return a tuple of exactly 2 elements
        if isinstance(result, tuple):
            if len(result) == 2:
                return result
            elif len(result) == 1:
                return True, result[0]  # Assume success if only one value
            elif len(result) > 2:
                logger.warning(f"get_producer returned {len(result)} values, expected 2. Taking first two.")
                return result[0], result[1]
            else:
                return False, "Empty result from get_producer"
        else:
            # If not a tuple, assume it's the result and success is True
            return True, result
    except Exception as e:
        logger.error(f"Error fetching producer details: {str(e)}")
        return False, f"Hiba a termelő részleteinek betöltése során: {str(e)}"

def get_producers():
    """Termelők lekérése - egyszerűsített verzió"""
    try:
        # Első próbálkozás: get_all_farmers API
        if hasattr(users_api, 'get_all_farmers'):
            return safe_api_call(users_api.get_all_farmers, "termelők betöltése")
        
        # Második próbálkozás: get_users termelő szerepkörrel
        params = {"role": "termelő", "is_active": True}
        return safe_api_call(users_api.get_users, "termelők betöltése", params)
        
    except Exception as e:
        # Fallback: ajánlatokból kinyerjük a termelőket
        return extract_producers_from_offers()

def extract_producers_from_offers():
    """Termelők kinyerése ajánlatokból - fallback megoldás"""
    try:
        success, offers = get_offers()
        if success and offers:
            producers = {}
            for offer in offers:
                producer_name = offer.get('producer_name')
                producer_id = offer.get('producer_id') or offer.get('user_id')
                if producer_name and producer_id:
                    producers[producer_id] = {
                        'id': producer_id,
                        'name': producer_name
                    }
            return True, list(producers.values())
        return False, "Nincsenek ajánlatok"
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_offers_paginated(page=1, page_size=20, params=None):
    """
    Ajánlatok lekérése az API-n keresztül lapozással.
    
    Args:
        page (int, optional): Az aktuális oldal száma. Defaults to 1.
        page_size (int, optional): Oldalméret. Defaults to 20.
        params (dict, optional): Szűrési paraméterek. Defaults to None.
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    try:
        # Lapozási paraméterek hozzáadása
        query_params = params.copy() if params else {}
        query_params["page"] = page
        query_params["limit"] = page_size
        
        # API hívás végrehajtása
        logger.info(f"Ajánlatok lekérése lapozással: {query_params}")
        success, result = get_offers(query_params)
        
        if success:
            # Ellenőrizzük, hogy az eredmény már tartalmazza-e a lapozási adatokat
            if isinstance(result, dict) and "items" in result:
                # Az API már lapozott formátumban adta vissza az eredményt
                return True, result
            elif isinstance(result, list):
                # A lista eredményt átalakítjuk lapozott formátummá
                total_items = len(result)
                total_pages = max(1, (total_items + page_size - 1) // page_size)
                
                # Kiszámítjuk a szükséges szeletét a listának
                start_idx = (page - 1) * page_size
                end_idx = min(start_idx + page_size, total_items)
                paged_items = result[start_idx:end_idx]
                
                # Visszaadjuk a lapozott formátumot
                return True, {
                    "items": paged_items,
                    "total": total_items,
                    "page": page,
                    "page_size": page_size,
                    "pages": total_pages
                }
            else:
                # Ha egy elem van, azt is listába csomagoljuk
                if isinstance(result, dict) and "id" in result:
                    return True, {
                        "items": [result],
                        "total": 1,
                        "page": 1,
                        "page_size": page_size,
                        "pages": 1
                    }
                
                # Egyéb esetben hiba
                return False, "Érvénytelen adatformátum a lapozáshoz"
        
        # Ha nem sikerült az adatlekérés, továbbítjuk a hibát
        return success, result
    except Exception as e:
        logger.error(f"Hiba az ajánlatok lapozott lekérésekor: {str(e)}")
        return False, f"Hiba az ajánlatok lapozott lekérésekor: {str(e)}"

def get_offer_details(offer_id):
    """
    Ajánlat részletek lekérése az API-n keresztül.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result az ajánlat részletei vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching offer details for offer #{offer_id}")
        
        # A valós API hívás a megfelelő hibakapszulázással
        return safe_api_call(offers_api.get_offer, "ajánlat részleteinek betöltése", offer_id)
    except Exception as e:
        logger.exception(f"Error fetching offer details: {str(e)}")
        return False, f"Hiba az ajánlat részletek lekérésekor: {str(e)}"

def get_related_offers(offer_id):
    """
    Az ajánlathoz kapcsolódó ajánlatok lekérése az API-n keresztül.
    
    Lekéri az összes olyan ajánlatot, amely hasonló paraméterekel rendelkezik
    (pl. ugyanaz a terméktípus, termelő vagy hasonló időszak) de nem ez az ajánlat.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a kapcsolódó ajánlatok listája vagy hibaüzenet
    """
    try:
        logger.info(f"Fetching related offers for offer #{offer_id}")
        
        # Először lekérjük a fő ajánlat adatait, hogy meghatározzuk, mi alapján keressünk kapcsolódókat
        success, main_offer = get_offer_details(offer_id)
        if not success:
            return False, f"Nem sikerült az eredeti ajánlat adatait lekérni: {main_offer}"
        
        # Kikeressük a szűréshez szükséges adatokat
        product_type_id = main_offer.get("product_type", {}).get("id")
        producer_id = main_offer.get("producer", {}).get("id") or main_offer.get("user", {}).get("id")
        delivery_month = main_offer.get("delivery_date", datetime.now()).month if isinstance(main_offer.get("delivery_date"), datetime) else None
        
        # Legalább egy feltétel kell a szűréshez
        if not (product_type_id or producer_id):
            return False, "Nem találhatók kapcsolódó ajánlatok, mert az eredeti ajánlat nem tartalmaz termék vagy termelő adatokat"
        
        # Kapcsolódó ajánlatok paraméterezése: azonos termék vagy termelő, de nem ez az ajánlat
        filter_params = {}
        
        # Ha van terméktípus, akkor azzal szűrünk
        if product_type_id:
            filter_params["product_type_id"] = product_type_id
        
        # Ha van termelő, akkor azzal is szűrünk
        if producer_id:
            filter_params["user_id"] = producer_id
            
        # API hívás a szűrt ajánlatok lekéréséhez
        success, offers = safe_api_call(offers_api.get_offers, "kapcsolódó ajánlatok lekérése", filter_params)
        
        if not success:
            return False, f"Nem sikerült a kapcsolódó ajánlatok lekérése: {offers}"
        
        # Eltávolítjuk az aktuális ajánlatot a listából
        if isinstance(offers, list):
            related_offers = [offer for offer in offers if str(offer.get("id")) != str(offer_id)]
            
            # Csak az első 5 ajánlatot adjuk vissza
            related_offers = related_offers[:5]
            
            return True, related_offers
        else:
            return False, "Érvénytelen adatformátum a kapcsolódó ajánlatok lekérésekor"
    except Exception as e:
        logger.exception(f"Error fetching related offers: {str(e)}")
        return False, f"Hiba a kapcsolódó ajánlatok lekérésekor: {str(e)}"

def get_average_confirmed_price(product_id=None, category_id=None, quality_grade_id=None):
    """
    Átlagos visszaigazolt ár lekérése az API statistics endpoint-ján keresztül.
    
    A hierarchia: termék + minőségi osztály -> termék -> kategória
    
    Args:
        product_id (int, optional): Termék azonosító (product_type_id)
        category_id (int, optional): Kategória azonosító
        quality_grade_id (int, optional): Minőségi osztály azonosító
        
    Returns:
        tuple: (success, average_price) ahol success egy boolean érték, average_price az átlagár vagy None
    """
    try:
        # 1. Legspecifikusabb: termék + minőségi osztály
        if product_id is not None and quality_grade_id is not None:
            logger.info(f"Átlagár keresése statistics API-val: product_type_id={product_id}, quality_grade_id={quality_grade_id}")
            
            success, avg_price = get_statistics_average_price(
                product_type_id=product_id, 
                quality_grade_id=quality_grade_id
            )
            
            # Debug: log the result regardless of success
            logger.info(f"Termék+minőség API eredmény: success={success}, avg_price={avg_price}")
            
            if success:
                if avg_price and avg_price > 0:
                    logger.info(f"Talált átlagár (termék+minőség): {avg_price} Ft/kg")
                    return True, avg_price
                else:
                    # Ha a termék+minőség kombináció létezik, de átlagár 0, azt elfogadjuk
                    logger.info(f"Termék+minőség kombináció létezik, de átlagár 0 - ezt elfogadjuk")
                    return True, 0.0
            else:
                logger.info(f"Termék+minőség szint sikertelen, próbáljuk a következő szintet")
        
        # 2. Középső szint: csak termék
        if product_id is not None:
            logger.info(f"Átlagár keresése statistics API-val: product_type_id={product_id}")
            
            success, avg_price = get_statistics_average_price(product_type_id=product_id)
            
            # Debug: log the result regardless of success
            logger.info(f"Termék API eredmény: success={success}, avg_price={avg_price}")
            
            if success:
                if avg_price and avg_price > 0:
                    logger.info(f"Talált átlagár (termék): {avg_price} Ft/kg")
                    return True, avg_price
                else:
                    # Ha a termék létezik, de átlagár 0, azt elfogadjuk - NEM megyünk kategória szintre
                    logger.info(f"✅ Termék létezik, de átlagár 0 - ezt elfogadjuk, NEM fallback kategóriára")
                    return True, 0.0
            else:
                logger.info(f"Termék szint sikertelen, próbáljuk a kategória szintet")
        
        # 3. Legáltalánosabb: kategória
        if category_id is not None:
            logger.info(f"Átlagár keresése statistics API-val: category_id={category_id}")
            
            success, avg_price = get_statistics_average_price(category_id=category_id)
            
            # Debug: log the result regardless of success
            logger.info(f"Kategória API eredmény: success={success}, avg_price={avg_price}")
            
            if success and avg_price and avg_price > 0:
                logger.info(f"✅ SIKERES átlagár (kategória): {avg_price} Ft/kg")
                return True, avg_price
            else:
                logger.info(f"Kategória szint is sikertelen")
        
        # Ha nem találtunk átlagárat
        logger.info("Nem található átlagár a megadott kritériumokkal")
        return False, None
        
    except Exception as e:
        logger.error(f"Hiba az átlagár lekérdezése során: {str(e)}")
        return False, None

def get_statistics_average_price(product_type_id=None, category_id=None, quality_grade_id=None, date_from=None, date_to=None):
    """
    Átlagár lekérése a /api/offers/statistics endpoint-ról.
    
    Args:
        product_type_id (int, optional): Termék típus azonosító
        category_id (int, optional): Kategória azonosító
        quality_grade_id (int, optional): Minőségi osztály azonosító
        date_from (str, optional): Kezdő dátum
        date_to (str, optional): Záró dátum
        
    Returns:
        tuple: (success, average_price) ahol success egy boolean érték, average_price az átlagár vagy None
    """
    try:
        # Build query parameters
        params = {}
        if product_type_id is not None:
            params['product_type_id'] = product_type_id
        if category_id is not None:
            params['category_id'] = category_id
        if quality_grade_id is not None:
            params['quality_grade_id'] = quality_grade_id
        if date_from is not None:
            params['date_from'] = date_from
        if date_to is not None:
            params['date_to'] = date_to
        
        # FONTOS: Explicit státusz szűrés a visszaigazolt ajánlatokra
        # Biztosítjuk, hogy az újonnan véglegesített ajánlatok azonnal beszámítsanak az átlagárba
        # JAVÍTÁS: Aktiváljuk a státusz szűrést a cache/update probléma megoldására
        params['status'] = 'CONFIRMED_BY_COMPANY,ACCEPTED_BY_USER,FINALIZED'
        
        logger.info(f"Statistics API hívás paraméterekkel: {params}")
        
        # Try to use the existing API structure
        if hasattr(offers_api, 'get_offer_statistics'):
            success, result = safe_api_call(offers_api.get_offer_statistics, "ajánlat statisztikák lekérése", params)
        else:
            # Fallback to manual implementation if needed
            logger.warning("get_offer_statistics nem elérhető, fallback módszer használata")
            return False, None
        
        if success and result:
            # Extract average_price from statistics
            average_price_str = result.get('average_price')
            total_offers = result.get('total_offers', 0)
            confirmed_offers_count = result.get('confirmed_offers_count', 0)
            calculation_details = result.get('calculation_details', {})
            
            # MANUEL SZÁMÍTÁS: Ha a backend nem adja vissza a confirmed_offers_count-ot,
            # számítsuk ki a status_counts alapján
            if confirmed_offers_count == 0 and 'status_counts' in result:
                status_counts = result['status_counts']
                confirmed_offers_count = (
                    status_counts.get('CONFIRMED_BY_COMPANY', 0) + 
                    status_counts.get('ACCEPTED_BY_USER', 0) + 
                    status_counts.get('FINALIZED', 0)
                )
                logger.info(f"Frontend számítás: confirmed_offers_count = {confirmed_offers_count}")
            
            if average_price_str:
                try:
                    # Convert string to float (API returns as string)
                    average_price = float(average_price_str)
                    logger.info(f"Statistics API átlagár: {average_price} Ft/kg")
                    logger.info(f"Statistics API számítás alapja: {confirmed_offers_count} ajánlat ({total_offers} összes)")
                    
                    # NO UI OUTPUT IN API FUNCTIONS - moved to logger instead
                    logger.info(f"Átlagár számítás alapja: {confirmed_offers_count} ajánlat felhasználva")
                    if total_offers != confirmed_offers_count:
                        excluded_count = total_offers - confirmed_offers_count
                        logger.info(f"Összes találat: {total_offers} ajánlat (ebből {excluded_count} kiszűrve)")
                    
                    # Log calculation details
                    if calculation_details:
                        logger.info(f"Figyelembe vett státuszok: {calculation_details.get('filtering_criteria', 'N/A')}")
                        logger.info(f"Kizárt státuszok: {calculation_details.get('excluded_statuses', 'N/A')}")
                    
                    # Log status breakdown if available
                    if 'status_counts' in result:
                        logger.info(f"Státusz bontás: {result['status_counts']}")
                        
                    # Log special case for 0 average price
                    if average_price == 0:
                        logger.info(f"Termék létezik, de nincs visszaigazolt ára - 0 Ft/kg használata")
                    
                    # Ha total_offers > 0, akkor a termék létezik, visszaadjuk az átlagárat (akár 0 is)
                    if total_offers > 0:
                        return True, average_price
                    else:
                        # Ha total_offers == 0, akkor a termék nem létezik
                        logger.info("Statistics API: termék nem létezik")
                        return False, None
                except (ValueError, TypeError) as e:
                    logger.error(f"Nem sikerült az átlagár konvertálása: {average_price_str}, hiba: {e}")
                    return False, None
            else:
                # Ha nincs average_price, de vannak ajánlatok, akkor 0-t adunk vissza
                if total_offers > 0:
                    logger.info("Statistics API: nincs average_price, de vannak ajánlatok - 0 átlagár")
                    # NO UI OUTPUT - just log the warning
                    logger.warning(f"Termék létezik ({total_offers} ajánlat), de nincs average_price - 0 Ft/kg használata")
                    return True, 0.0
                else:
                    logger.info("Statistics API nem tartalmazott average_price mezőt és nincs ajánlat")
                    return False, None
        else:
            logger.info(f"Statistics API hívás sikertelen: {result}")
            return False, None
            
    except Exception as e:
        logger.error(f"Hiba a statistics API hívás során: {str(e)}")
        return False, None

def calculate_average_price(offers, price_field="confirmed_price"):
    """
    Átlagár számítása ajánlatok listájából.
    
    Args:
        offers (list): Ajánlatok listája
        price_field (str): Az ár mező neve (confirmed_price, price)
        
    Returns:
        float: Átlagár vagy 0 ha nincs érvényes adat
    """
    try:
        prices = []
        
        for offer in offers:
            # Ellenőrizzük mind a confirmed_price-t, mind a price-t
            price = offer.get(price_field) or offer.get('price')
            
            if price is not None:
                try:
                    price_float = float(price)
                    if price_float > 0:  # Csak pozitív árak
                        prices.append(price_float)
                except (ValueError, TypeError):
                    continue
        
        if prices:
            avg_price = sum(prices) / len(prices)
            logger.info(f"Átlagár számítás: {len(prices)} árakból = {avg_price:.2f} Ft/kg")
            return avg_price
        
        return 0.0
        
    except Exception as e:
        logger.error(f"Hiba az átlagár számítása során: {str(e)}")
        return 0.0

def get_offers_with_cache(query_params=None):
    """Get offers with session state caching"""
    import streamlit as st
    from datetime import datetime
    import hashlib
    import json
    
    # Generate cache key from parameters
    cache_key = 'offers_cache'
    cache_params_key = 'offers_cache_params'
    cache_timestamp_key = 'offers_cache_timestamp'
    cache_duration = 60  # 1 minute cache
    
    current_time = datetime.now()
    
    # Generate unique key for this parameter combination
    param_str = json.dumps(query_params or {}, sort_keys=True, default=str)
    param_hash = hashlib.md5(param_str.encode()).hexdigest()
    
    # Check if cache is valid
    if (cache_key in st.session_state and
        cache_params_key in st.session_state and
        cache_timestamp_key in st.session_state):
        
        # Check if parameters match and cache is not expired
        if (st.session_state[cache_params_key] == param_hash and
            (current_time - st.session_state[cache_timestamp_key]).total_seconds() < cache_duration):
            
            # Increment cache hit counter
            if 'cache_hits' not in st.session_state:
                st.session_state['cache_hits'] = 0
            st.session_state['cache_hits'] += 1
            
            logger.debug(f"Cache hit for offers with params hash: {param_hash[:8]}")
            return True, st.session_state[cache_key]
    
    # Cache miss - fetch from API
    if 'cache_misses' not in st.session_state:
        st.session_state['cache_misses'] = 0
    st.session_state['cache_misses'] += 1
    
    logger.debug(f"Cache miss for offers with params hash: {param_hash[:8]}")
    
    # Call the original get_offers function
    success, offers = get_offers(query_params)
    
    if success:
        # Update cache
        st.session_state[cache_key] = offers
        st.session_state[cache_params_key] = param_hash
        st.session_state[cache_timestamp_key] = current_time
        
        logger.info(f"Cached {len(offers) if isinstance(offers, list) else 0} offers for {cache_duration} seconds")
    
    return success, offers

def clear_offers_cache():
    """Clear offers cache"""
    cache_keys = ['offers_cache', 'offers_cache_params', 'offers_cache_timestamp']
    
    cleared = False
    for key in cache_keys:
        if key in st.session_state:
            del st.session_state[key]
            cleared = True
    
    if cleared:
        logger.info("Offers cache cleared")
    
    return cleared