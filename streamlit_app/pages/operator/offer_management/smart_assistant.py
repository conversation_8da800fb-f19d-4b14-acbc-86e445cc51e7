"""
Smart Offer Assistant - Intelligens Ajánlatkezelő Asszisztens
Integrálja az összes fejlett funkciót egy intelligens asszisztens modulba

Főbb komponensek:
- Intelligens keresés fuzzy matching-gel
- Dinamikus szűrő builder
- Státusz vizualizáció és timeline
- Kontextus-érzékeny ajánlások
- Teljesítmény monitoring
- Billentyűparancsok
- Összetett szűrők vizuális <PERSON>píté<PERSON>
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import hashlib
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
import json
from dataclasses import dataclass, asdict
from collections import Counter, defaultdict
import time

logger = logging.getLogger(__name__)

@dataclass
class FilterGroup:
    """Szűrő csoport adatstruktúra"""
    field: str = ""
    operator: str = ""
    value: str = ""
    logic_operator: str = "AND"
    
@dataclass
class SortingRule:
    """Rendezési szabály adatstruktúra"""
    field: str
    direction: str = "asc"
    priority: int = 1
    
@dataclass
class Recommendation:
    """Ajánlás adatstruktúra"""
    id: str
    title: str
    description: str
    action_type: str
    action_data: Dict[str, Any]
    confidence: float
    category: str

class AdvancedSorter:
    """Fejlett többszintű rendezés"""
    
    def __init__(self):
        self.available_fields = [
            "id", "created_at", "updated_at", "status", 
            "producer_name", "product_name", "quantity", 
            "price_per_unit", "total_value", "delivery_date"
        ]
        
        self.field_types = {
            "id": "numeric",
            "created_at": "datetime", 
            "updated_at": "datetime",
            "delivery_date": "datetime",
            "status": "categorical",
            "producer_name": "text",
            "product_name": "text", 
            "quantity": "numeric",
            "price_per_unit": "numeric",
            "total_value": "numeric"
        }
    
    def get_available_operators(self, field: str) -> List[str]:
        """Mező típusa alapján elérhető operátorok"""
        field_type = self.field_types.get(field, "text")
        
        operators = {
            "numeric": ["=", "!=", ">", ">=", "<", "<=", "between"],
            "text": ["contains", "equals", "starts_with", "ends_with", "regex"],
            "datetime": ["=", "!=", ">", ">=", "<", "<=", "between", "today", "this_week", "this_month"],
            "categorical": ["=", "!=", "in", "not_in"]
        }
        
        return operators.get(field_type, operators["text"])
    
    def apply_multi_level_sort(self, offers: List[Dict], sorting_rules: List[SortingRule]) -> List[Dict]:
        """Többszintű rendezés alkalmazása"""
        if not offers or not sorting_rules:
            return offers
            
        df = pd.DataFrame(offers)
        
        # Rendezési szabályok prioritás szerint
        sorted_rules = sorted(sorting_rules, key=lambda x: x.priority)
        
        sort_columns = []
        sort_ascending = []
        
        for rule in sorted_rules:
            if rule.field in df.columns:
                sort_columns.append(rule.field)
                sort_ascending.append(rule.direction == "asc")
        
        if sort_columns:
            df_sorted = df.sort_values(
                by=sort_columns,
                ascending=sort_ascending
            )
            return df_sorted.to_dict('records')
        
        return offers

class FullTextSearchEngine:
    """Teljes szöveges keresés fuzzy matching-gel"""
    
    def __init__(self):
        self.searchable_fields = [
            "producer_name", "product_name", "description", 
            "location", "quality_grade", "packaging_type"
        ]
    
    def fuzzy_match(self, query: str, text: str, threshold: float = 0.6) -> bool:
        """Egyszerű fuzzy matching implementáció"""
        if not query or not text:
            return False
            
        query = query.lower().strip()
        text = text.lower().strip()
        
        # Exact match
        if query in text:
            return True
            
        # Character similarity
        common_chars = set(query) & set(text)
        similarity = len(common_chars) / len(set(query)) if query else 0
        
        return similarity >= threshold
    
    def search(self, offers: List[Dict], query: str, fields: Optional[List[str]] = None) -> List[Dict]:
        """Teljes szöveges keresés végrehajtása"""
        if not query or not offers:
            return offers
            
        search_fields = fields or self.searchable_fields
        results = []
        
        for offer in offers:
            match_found = False
            
            for field in search_fields:
                field_value = offer.get(field, "")
                if isinstance(field_value, str):
                    if self.fuzzy_match(query, field_value):
                        match_found = True
                        break
                elif isinstance(field_value, (int, float)):
                    if query.isdigit() and str(field_value) == query:
                        match_found = True
                        break
            
            if match_found:
                results.append(offer)
                
        return results
    
    def get_search_suggestions(self, query: str, offers: List[Dict], limit: int = 5) -> List[str]:
        """Keresési javaslatok generálása"""
        if not query or len(query) < 2:
            return []
            
        suggestions = set()
        query_lower = query.lower()
        
        for offer in offers:
            for field in self.searchable_fields:
                value = offer.get(field, "")
                if isinstance(value, str) and len(value) > 2:
                    if query_lower in value.lower():
                        suggestions.add(value)
                        
        return list(suggestions)[:limit]

class StatusVisualizer:
    """Státusz vizualizáció és timeline"""
    
    def __init__(self):
        self.status_colors = {
            "CREATED": "#FFA726",
            "CONFIRMED_BY_COMPANY": "#42A5F5", 
            "ACCEPTED_BY_USER": "#66BB6A",
            "REJECTED_BY_USER": "#EF5350",
            "FINALIZED": "#26A69A",
            "CANCELLED": "#BDBDBD"
        }
        
        self.status_icons = {
            "CREATED": "📝",
            "CONFIRMED_BY_COMPANY": "✅", 
            "ACCEPTED_BY_USER": "👍",
            "REJECTED_BY_USER": "👎",
            "FINALIZED": "🎉",
            "CANCELLED": "❌"
        }
    
    def generate_status_flow_diagram(self, offers: List[Dict]) -> go.Figure:
        """Státusz flow diagram generálása"""
        if not offers:
            return go.Figure()
            
        status_counts = Counter(offer.get('status', 'UNKNOWN') for offer in offers)
        
        # Sankey diagram data
        statuses = list(status_counts.keys())
        values = list(status_counts.values())
        
        fig = go.Figure(data=[go.Pie(
            labels=[f"{self.status_icons.get(s, '❓')} {s}" for s in statuses],
            values=values,
            hole=0.3,
            marker_colors=[self.status_colors.get(s, '#BDBDBD') for s in statuses]
        )])
        
        fig.update_layout(
            title="📊 Ajánlatok Státusz Megoszlása",
            height=400,
            showlegend=True
        )
        
        return fig
    
    def render_status_timeline(self, offer: Dict) -> go.Figure:
        """Ajánlat státusz timeline renderelése"""
        # Mock timeline data - valós implementációban az offer history-ból jönne
        timeline_events = [
            {"date": "2024-01-15", "status": "CREATED", "description": "Ajánlat létrehozva"},
            {"date": "2024-01-16", "status": "CONFIRMED_BY_COMPANY", "description": "Cég által megerősítve"},
            {"date": "2024-01-18", "status": "ACCEPTED_BY_USER", "description": "Felhasználó által elfogadva"},
        ]
        
        fig = go.Figure()
        
        for i, event in enumerate(timeline_events):
            fig.add_trace(go.Scatter(
                x=[event["date"]],
                y=[i],
                mode='markers+text',
                marker=dict(
                    size=20,
                    color=self.status_colors.get(event["status"], '#BDBDBD')
                ),
                text=[f"{self.status_icons.get(event['status'], '❓')} {event['status']}"],
                textposition="middle right",
                name=event["description"],
                showlegend=False
            ))
        
        fig.update_layout(
            title=f"📈 Ajánlat Timeline - {offer.get('id', 'N/A')}",
            xaxis_title="Dátum",
            yaxis=dict(showticklabels=False),
            height=300
        )
        
        return fig

class SuggestionEngine:
    """Intelligens javaslatok motorja"""
    
    def __init__(self):
        self.user_actions = []
        self.recommendation_cache = {}
        
    def track_user_action(self, action_type: str, context: Dict[str, Any]):
        """Felhasználói műveletek követése"""
        self.user_actions.append({
            "timestamp": datetime.now(),
            "action": action_type,
            "context": context
        })
        
        # Limit history
        if len(self.user_actions) > 100:
            self.user_actions = self.user_actions[-50:]
    
    def get_recommendations(self, 
                          user_behavior: List[Dict], 
                          current_filters: Dict[str, Any],
                          offer_data: List[Dict]) -> List[Recommendation]:
        """Kontextus-érzékeny ajánlások generálása"""
        recommendations = []
        
        # 1. Gyakran használt szűrők ajánlása
        if user_behavior:
            frequent_filters = self._analyze_frequent_filters(user_behavior)
            for filter_combo in frequent_filters[:3]:
                recommendations.append(Recommendation(
                    id=f"freq_filter_{hashlib.md5(str(filter_combo).encode()).hexdigest()[:8]}",
                    title=f"Gyakran használt szűrő: {filter_combo['name']}",
                    description=f"Ezt a szűrőt {filter_combo['count']} alkalommal használtad",
                    action_type="apply_filter",
                    action_data=filter_combo['filters'],
                    confidence=0.8,
                    category="efficiency"
                ))
        
        # 2. Adatok alapú javaslatok
        if offer_data:
            data_recommendations = self._generate_data_insights(offer_data)
            recommendations.extend(data_recommendations)
        
        # 3. Teljesítmény javaslatok
        perf_recommendations = self._generate_performance_suggestions(current_filters)
        recommendations.extend(perf_recommendations)
        
        return sorted(recommendations, key=lambda x: x.confidence, reverse=True)[:5]
    
    def _analyze_frequent_filters(self, user_behavior: List[Dict]) -> List[Dict]:
        """Gyakran használt szűrők elemzése"""
        filter_combinations = defaultdict(int)
        
        for action in user_behavior:
            if action.get('action') == 'apply_filter':
                filter_key = str(sorted(action.get('context', {}).items()))
                filter_combinations[filter_key] += 1
        
        frequent = []
        for filter_combo, count in filter_combinations.items():
            if count >= 3:  # Legalább 3x használt
                frequent.append({
                    'name': f"Szűrő kombináció #{len(frequent)+1}",
                    'count': count,
                    'filters': eval(filter_combo)  # Gyakorlatban biztonságosabb parsing
                })
        
        return sorted(frequent, key=lambda x: x['count'], reverse=True)
    
    def _generate_data_insights(self, offer_data: List[Dict]) -> List[Recommendation]:
        """Adatok alapú betekintések generálása"""
        recommendations = []
        
        if len(offer_data) > 10:
            # Státusz eloszlás elemzése
            status_dist = Counter(offer.get('status', '') for offer in offer_data)
            most_common_status = status_dist.most_common(1)[0]
            
            if most_common_status[1] > len(offer_data) * 0.6:
                recommendations.append(Recommendation(
                    id="status_insight",
                    title=f"Státusz koncentráció: {most_common_status[0]}",
                    description=f"Az ajánlatok {most_common_status[1]/len(offer_data):.1%}-a '{most_common_status[0]}' státuszban van",
                    action_type="filter_by_status", 
                    action_data={"status": most_common_status[0]},
                    confidence=0.7,
                    category="insight"
                ))
        
        return recommendations
    
    def _generate_performance_suggestions(self, current_filters: Dict[str, Any]) -> List[Recommendation]:
        """Teljesítmény javaslatok generálása"""
        recommendations = []
        
        # Ha túl sok szűrő van aktív
        if len(current_filters) > 5:
            recommendations.append(Recommendation(
                id="reduce_filters",
                title="Túl sok aktív szűrő",
                description="Csökkentsd a szűrők számát a jobb teljesítményért",
                action_type="optimize_filters",
                action_data={"suggestion": "reduce_count"},
                confidence=0.6,
                category="performance"
            ))
        
        return recommendations

class PerformanceCache:
    """Teljesítmény optimalizáció és monitoring"""
    
    def __init__(self):
        self.cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_requests": 0
        }
        self.performance_metrics = {
            "avg_response_time": 0,
            "cache_hit_rate": 0,
            "memory_usage_mb": 0
        }
    
    def get_cache_key(self, params: Dict[str, Any]) -> str:
        """Cache kulcs generálása paraméterekből"""
        return hashlib.md5(str(sorted(params.items())).encode()).hexdigest()
    
    def get_cached_result(self, cache_key: str) -> Optional[Any]:
        """Cached eredmény lekérése"""
        self.cache_stats["total_requests"] += 1
        
        if cache_key in self.cache:
            self.cache_stats["hits"] += 1
            return self.cache[cache_key]["data"]
        else:
            self.cache_stats["misses"] += 1
            return None
    
    def cache_result(self, cache_key: str, data: Any, ttl_seconds: int = 300):
        """Eredmény cache-elése"""
        self.cache[cache_key] = {
            "data": data,
            "timestamp": datetime.now(),
            "ttl": ttl_seconds
        }
        
        # Cleanup old entries
        self._cleanup_expired_cache()
    
    def _cleanup_expired_cache(self):
        """Lejárt cache bejegyzések törlése"""
        now = datetime.now()
        expired_keys = []
        
        for key, value in self.cache.items():
            if (now - value["timestamp"]).seconds > value["ttl"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Teljesítmény metrikák lekérése"""
        total_requests = self.cache_stats["total_requests"]
        
        if total_requests > 0:
            hit_rate = self.cache_stats["hits"] / total_requests
        else:
            hit_rate = 0
        
        # Simulate other metrics
        import sys
        memory_usage = sys.getsizeof(self.cache) / 1024 / 1024  # MB
        
        self.performance_metrics.update({
            "cache_hit_rate": hit_rate,
            "cache_hit_delta": hit_rate - self.performance_metrics.get("cache_hit_rate", 0),
            "avg_response_time": 0.15,  # Mock value
            "response_time_delta": -0.02,  # Mock improvement
            "memory_usage_mb": memory_usage,
            "memory_delta": memory_usage - self.performance_metrics.get("memory_usage_mb", 0)
        })
        
        return self.performance_metrics

class ComplexFilterBuilder:
    """Összetett szűrők vizuális építése"""
    
    def __init__(self):
        self.filter_groups = []
        self.available_fields = [
            "id", "status", "producer_name", "product_name", 
            "quantity", "price_per_unit", "created_at", "delivery_date"
        ]
    
    def render_filter_builder(self) -> List[FilterGroup]:
        """Vizuális szűrő builder renderelése"""
        st.markdown("### 🎯 Összetett Szűrő Készítő")
        
        # Initialize session state for filter groups
        if 'filter_groups' not in st.session_state:
            st.session_state.filter_groups = []
        
        # Add new filter group button
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            if st.button("➕ Új feltétel hozzáadása", key="add_filter_group"):
                st.session_state.filter_groups.append(FilterGroup())
                st.rerun()
        
        with col2:
            if st.button("🔄 Szűrők törlése", key="clear_filters") and st.session_state.filter_groups:
                st.session_state.filter_groups = []
                st.rerun()
        
        with col3:
            if st.button("💾 Szűrő mentése", key="save_filter"):
                st.info("Szűrő mentési funkció hamarosan elérhető!")
        
        # Render each filter group
        groups_to_remove = []
        
        for i, group in enumerate(st.session_state.filter_groups):
            with st.container():
                st.markdown("---")
                
                col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 1, 1])
                
                with col1:
                    field = st.selectbox(
                        "Mező",
                        options=self.available_fields,
                        index=self.available_fields.index(group.field) if group.field in self.available_fields else 0,
                        key=f"field_{i}"
                    )
                    group.field = field
                
                with col2:
                    operators = self._get_operators_for_field(field)
                    operator = st.selectbox(
                        "Művelet",
                        options=operators,
                        index=operators.index(group.operator) if group.operator in operators else 0,
                        key=f"op_{i}"
                    )
                    group.operator = operator
                
                with col3:
                    value = st.text_input(
                        "Érték",
                        value=group.value,
                        key=f"val_{i}"
                    )
                    group.value = value
                
                with col4:
                    if i > 0:  # Don't show logic operator for first group
                        logic_op = st.selectbox(
                            "Logika",
                            options=["AND", "OR"],
                            index=["AND", "OR"].index(group.logic_operator),
                            key=f"logic_{i}"
                        )
                        group.logic_operator = logic_op
                
                with col5:
                    if st.button("❌", key=f"del_{i}"):
                        groups_to_remove.append(i)
        
        # Remove marked groups
        for i in reversed(groups_to_remove):
            st.session_state.filter_groups.pop(i)
            st.rerun()
        
        return st.session_state.filter_groups
    
    def _get_operators_for_field(self, field: str) -> List[str]:
        """Mező típusa alapján elérhető operátorok"""
        numeric_fields = ["id", "quantity", "price_per_unit"]
        date_fields = ["created_at", "delivery_date", "updated_at"]
        text_fields = ["producer_name", "product_name", "description"]
        
        if field in numeric_fields:
            return ["=", "!=", ">", ">=", "<", "<="]
        elif field in date_fields:
            return ["=", "!=", ">", ">=", "<", "<=", "ma", "ez a hét", "ez a hónap"]
        elif field in text_fields:
            return ["tartalmazza", "egyezik", "kezdődik", "végződik"]
        else:
            return ["=", "!=", "tartalmazza"]
    
    def apply_complex_filters(self, offers: List[Dict], filter_groups: List[FilterGroup]) -> List[Dict]:
        """Összetett szűrők alkalmazása"""
        if not offers or not filter_groups:
            return offers
        
        df = pd.DataFrame(offers)
        result_mask = pd.Series([True] * len(df))
        
        for i, group in enumerate(filter_groups):
            if not group.field or not group.operator or not group.value:
                continue
                
            group_mask = self._apply_single_filter(df, group)
            
            if i == 0:
                result_mask = group_mask
            else:
                if group.logic_operator == "AND":
                    result_mask = result_mask & group_mask
                else:  # OR
                    result_mask = result_mask | group_mask
        
        filtered_df = df[result_mask]
        return filtered_df.to_dict('records')
    
    def _apply_single_filter(self, df: pd.DataFrame, filter_group: FilterGroup) -> pd.Series:
        """Egyetlen szűrő alkalmazása"""
        field = filter_group.field
        operator = filter_group.operator
        value = filter_group.value
        
        if field not in df.columns:
            return pd.Series([True] * len(df))
        
        series = df[field]
        
        try:
            # Numeric operations
            if operator in ["=", "!=", ">", ">=", "<", "<="]:
                if pd.api.types.is_numeric_dtype(series):
                    numeric_value = float(value)
                    if operator == "=":
                        return series == numeric_value
                    elif operator == "!=":
                        return series != numeric_value
                    elif operator == ">":
                        return series > numeric_value
                    elif operator == ">=":
                        return series >= numeric_value
                    elif operator == "<":
                        return series < numeric_value
                    elif operator == "<=":
                        return series <= numeric_value
                else:
                    # String comparison
                    if operator == "=":
                        return series.astype(str) == value
                    elif operator == "!=":
                        return series.astype(str) != value
            
            # Text operations
            elif operator == "tartalmazza":
                return series.astype(str).str.contains(value, case=False, na=False)
            elif operator == "egyezik":
                return series.astype(str).str.lower() == value.lower()
            elif operator == "kezdődik":
                return series.astype(str).str.startswith(value, na=False)
            elif operator == "végződik":
                return series.astype(str).str.endswith(value, na=False)
            
        except (ValueError, TypeError):
            pass
        
        return pd.Series([True] * len(df))

class SmartAssistant:
    """Főosztály - Intelligens Ajánlatkezelő Asszisztens"""
    
    def __init__(self):
        self.sorter = AdvancedSorter()
        self.search_engine = FullTextSearchEngine()
        self.status_viz = StatusVisualizer()
        self.suggestion_engine = SuggestionEngine()
        self.perf_cache = PerformanceCache()
        self.complex_filters = ComplexFilterBuilder()
        
        # Initialize session state
        self._init_session_state()
    
    def _init_session_state(self):
        """Session state inicializálása"""
        if 'smart_assistant_initialized' not in st.session_state:
            st.session_state.smart_assistant_initialized = True
            st.session_state.user_actions = []
            st.session_state.smart_search_query = ""
            st.session_state.active_sorting_rules = []
    
    def _get_widget_key(self, base_key: str) -> str:
        """Egyedi widget kulcs generálása"""
        return hashlib.md5(f"smart_assistant_{base_key}".encode()).hexdigest()[:8]
    
    def render_smart_search(self) -> Optional[str]:
        """Intelligens keresés widget renderelése"""
        st.markdown("### 🔍 Intelligens Keresés")
        
        # Search input with keyboard shortcut hint
        search_query = st.text_input(
            "",
            placeholder="Keress bármire... (Ctrl+K a gyors fókuszhoz)",
            key=self._get_widget_key("smart_search"),
            help="Fuzzy keresés minden mezőben. Használj részleges szavakat is!"
        )
        
        # Real-time suggestions
        if search_query and len(search_query) >= 2:
            current_offers = st.session_state.get('current_offers', [])
            suggestions = self.search_engine.get_search_suggestions(search_query, current_offers)
            
            if suggestions:
                st.markdown("**💡 Javaslatok:**")
                suggestion_cols = st.columns(min(len(suggestions), 3))
                
                for i, suggestion in enumerate(suggestions[:3]):
                    with suggestion_cols[i]:
                        if st.button(f"🎯 {suggestion[:20]}...", key=f"sugg_{i}"):
                            st.session_state[self._get_widget_key("smart_search")] = suggestion
                            # Track user action
                            self.suggestion_engine.track_user_action("search_suggestion_used", {
                                "original_query": search_query,
                                "selected_suggestion": suggestion
                            })
                            st.rerun()
        
        return search_query if search_query else None
    
    def render_sorting_controls(self) -> List[SortingRule]:
        """Fejlett rendezési vezérlők renderelése"""
        with st.expander("📊 Fejlett Rendezés", expanded=False):
            st.markdown("**Többszintű rendezés beállítása**")
            
            # Initialize sorting rules in session state
            if 'sorting_rules' not in st.session_state:
                st.session_state.sorting_rules = []
            
            # Add new sorting rule
            col1, col2 = st.columns([3, 1])
            
            with col1:
                if st.button("➕ Új rendezési szabály", key="add_sort_rule"):
                    st.session_state.sorting_rules.append(SortingRule(
                        field=self.sorter.available_fields[0],
                        direction="asc",
                        priority=len(st.session_state.sorting_rules) + 1
                    ))
                    st.rerun()
            
            with col2:
                if st.button("🔄 Törlés", key="clear_sort_rules"):
                    st.session_state.sorting_rules = []
                    st.rerun()
            
            # Render sorting rules
            rules_to_remove = []
            
            for i, rule in enumerate(st.session_state.sorting_rules):
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
                    
                    with col1:
                        field = st.selectbox(
                            "Mező",
                            options=self.sorter.available_fields,
                            index=self.sorter.available_fields.index(rule.field),
                            key=f"sort_field_{i}"
                        )
                        rule.field = field
                    
                    with col2:
                        direction = st.selectbox(
                            "Irány",
                            options=["asc", "desc"],
                            format_func=lambda x: "🔼 Növekvő" if x == "asc" else "🔽 Csökkenő",
                            index=0 if rule.direction == "asc" else 1,
                            key=f"sort_dir_{i}"
                        )
                        rule.direction = direction
                    
                    with col3:
                        priority = st.number_input(
                            "Prioritás",
                            min_value=1,
                            max_value=10,
                            value=rule.priority,
                            key=f"sort_prio_{i}"
                        )
                        rule.priority = priority
                    
                    with col4:
                        if st.button("❌", key=f"del_sort_{i}"):
                            rules_to_remove.append(i)
            
            # Remove marked rules
            for i in reversed(rules_to_remove):
                st.session_state.sorting_rules.pop(i)
                st.rerun()
            
            return st.session_state.sorting_rules
    
    def render_status_dashboard(self) -> None:
        """Státusz dashboard renderelése"""
        st.markdown("### 📊 Státusz Áttekintő")
        
        current_offers = st.session_state.get('current_offers', [])
        
        if current_offers:
            # Status flow diagram
            status_flow = self.status_viz.generate_status_flow_diagram(current_offers)
            st.plotly_chart(status_flow, use_container_width=True)
            
            # Status timeline for selected offer
            if len(current_offers) > 0:
                st.markdown("#### 📈 Ajánlat Timeline")
                
                selected_offer_id = st.selectbox(
                    "Válassz ajánlatot a történet megtekintéséhez:",
                    options=[offer.get('id', 'N/A') for offer in current_offers],
                    format_func=lambda x: f"#{x} - {next((o.get('product_name', 'N/A') for o in current_offers if o.get('id') == x), 'N/A')}",
                    key=self._get_widget_key("timeline_offer_select")
                )
                
                if selected_offer_id:
                    selected_offer = next((o for o in current_offers if o.get('id') == selected_offer_id), None)
                    if selected_offer:
                        timeline = self.status_viz.render_status_timeline(selected_offer)
                        st.plotly_chart(timeline, use_container_width=True)
        else:
            st.info("Nincsenek ajánlatok a státusz megjelenítéshez")
    
    def render_smart_recommendations(self) -> None:
        """Intelligens ajánlások panel renderelése"""
        st.markdown("### 🤖 Intelligens Ajánlások")
        
        # Get context-aware recommendations
        user_behavior = st.session_state.get('user_actions', [])
        current_filters = st.session_state.get('active_filters', {})
        offer_data = st.session_state.get('current_offers', [])
        
        recommendations = self.suggestion_engine.get_recommendations(
            user_behavior=user_behavior,
            current_filters=current_filters,
            offer_data=offer_data
        )
        
        if recommendations:
            for rec in recommendations:
                with st.container():
                    col1, col2 = st.columns([4, 1])
                    
                    with col1:
                        # Category icon
                        category_icons = {
                            "efficiency": "⚡",
                            "insight": "💡", 
                            "performance": "🚀"
                        }
                        icon = category_icons.get(rec.category, "💡")
                        
                        st.write(f"**{icon} {rec.title}**")
                        st.caption(rec.description)
                        
                        # Confidence indicator
                        conf_color = "green" if rec.confidence > 0.7 else "orange" if rec.confidence > 0.5 else "red"
                        st.markdown(f"<small style='color: {conf_color}'>Megbízhatóság: {rec.confidence:.1%}</small>", 
                                  unsafe_allow_html=True)
                    
                    with col2:
                        if st.button("Alkalmaz", key=f"apply_{rec.id}"):
                            self._apply_recommendation(rec)
                            st.success("Ajánlás alkalmazva!")
                            st.rerun()
                
                st.markdown("---")
        else:
            st.info("🎯 Jelenleg nincsenek személyre szabott ajánlások")
            st.markdown("""
            **Ajánlások generálásához:**
            - Használj különböző szűrőket
            - Végezz kereséseket
            - Rendezd az ajánlatokat
            """)
    
    def render_performance_monitor(self) -> None:
        """Teljesítmény monitor widget renderelése"""
        if st.checkbox("🚀 Teljesítmény Monitor", key=self._get_widget_key("perf_monitor")):
            metrics = self.perf_cache.get_performance_metrics()
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                delta_color = "normal" if abs(metrics.get('cache_hit_delta', 0)) < 0.1 else "inverse"
                st.metric(
                    "💾 Cache találat",
                    f"{metrics['cache_hit_rate']:.1%}",
                    delta=f"{metrics.get('cache_hit_delta', 0):.1%}",
                    delta_color=delta_color
                )
            
            with col2:
                delta_color = "inverse" if metrics.get('response_time_delta', 0) > 0 else "normal"
                st.metric(
                    "⚡ Válaszidő",
                    f"{metrics['avg_response_time']:.2f}s",
                    delta=f"{metrics.get('response_time_delta', 0):.2f}s",
                    delta_color=delta_color
                )
            
            with col3:
                delta_color = "inverse" if metrics.get('memory_delta', 0) > 0 else "normal"
                st.metric(
                    "🧠 Memória",
                    f"{metrics['memory_usage_mb']:.1f} MB",
                    delta=f"{metrics.get('memory_delta', 0):.1f} MB",
                    delta_color=delta_color
                )
            
            # Performance tips
            if metrics['cache_hit_rate'] < 0.5:
                st.warning("💡 **Tipp**: Alacsony cache találati arány. Próbálj hasonló szűrőket használni!")
            
            if metrics['avg_response_time'] > 1.0:
                st.warning("💡 **Tipp**: Lassú válaszidő. Csökkentsd a szűrők számát!")
    
    def _apply_recommendation(self, recommendation: Recommendation):
        """Ajánlás alkalmazása"""
        if recommendation.action_type == "apply_filter":
            # Apply recommended filter
            for key, value in recommendation.action_data.items():
                st.session_state[f"filter_{key}"] = value
        
        elif recommendation.action_type == "filter_by_status":
            # Filter by specific status
            status = recommendation.action_data.get("status")
            if status:
                st.session_state["filter_status"] = status
        
        elif recommendation.action_type == "optimize_filters":
            # Performance optimization suggestion
            st.info("Teljesítmény optimalizálási javaslat alkalmazva!")
        
        # Track that recommendation was applied
        self.suggestion_engine.track_user_action("recommendation_applied", {
            "recommendation_id": recommendation.id,
            "action_type": recommendation.action_type
        })
    
    def apply_search_filter(self, offers: List[Dict], search_query: str) -> List[Dict]:
        """Keresési szűrő alkalmazása"""
        if not search_query:
            return offers
            
        # Track search action
        self.suggestion_engine.track_user_action("search_performed", {
            "query": search_query,
            "result_count": len(offers)
        })
        
        return self.search_engine.search(offers, search_query)
    
    def apply_complex_filters(self, offers: List[Dict]) -> List[Dict]:
        """Összetett szűrők alkalmazása"""
        filter_groups = st.session_state.get('filter_groups', [])
        
        if not filter_groups:
            return offers
            
        # Track filter usage
        self.suggestion_engine.track_user_action("complex_filter_applied", {
            "filter_count": len(filter_groups),
            "filters": [asdict(group) for group in filter_groups]
        })
        
        return self.complex_filters.apply_complex_filters(offers, filter_groups)
    
    def apply_sorting(self, offers: List[Dict]) -> List[Dict]:
        """Rendezés alkalmazása"""
        sorting_rules = st.session_state.get('sorting_rules', [])
        
        if not sorting_rules:
            return offers
            
        # Track sorting usage
        self.suggestion_engine.track_user_action("sorting_applied", {
            "rules_count": len(sorting_rules),
            "rules": [asdict(rule) for rule in sorting_rules]
        })
        
        return self.sorter.apply_multi_level_sort(offers, sorting_rules)

def inject_keyboard_shortcuts():
    """Billentyűparancsok beinjektálása"""
    st.markdown("""
    <script>
    document.addEventListener('keydown', function(e) {
        // Ctrl+K - Focus search
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[placeholder*="Keress"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Alt+N - New offer (placeholder)
        if (e.altKey && e.key === 'n') {
            e.preventDefault();
            console.log('New offer shortcut triggered');
        }
        
        // F1 - Toggle help
        if (e.key === 'F1') {
            e.preventDefault();
            console.log('Help shortcut triggered');
        }
        
        // Alt+F - Focus filters
        if (e.altKey && e.key === 'f') {
            e.preventDefault();
            const filterExpander = document.querySelector('[data-testid="stExpander"] summary');
            if (filterExpander) {
                filterExpander.click();
            }
        }
    });
    </script>
    """, unsafe_allow_html=True)

# Factory function for easy integration
def create_smart_assistant() -> SmartAssistant:
    """Smart Assistant factory function"""
    return SmartAssistant()

if __name__ == "__main__":
    # Testing purposes
    assistant = create_smart_assistant()
    st.write("Smart Assistant initialized successfully!")