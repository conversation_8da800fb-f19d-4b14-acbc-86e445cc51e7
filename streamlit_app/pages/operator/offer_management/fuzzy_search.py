"""
Fuzzy search functionality for the offer management page.
Provides approximate string matching to handle typos and spelling variations.
"""
import streamlit as st
import logging
import re
from difflib import SequenceMatcher
from Levenshtein import distance as levenshtein_distance

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.fulltext_search import (
        get_nested_value,
        extract_all_searchable_text,
        SEARCHABLE_FIELDS,
        highlight_matches
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.fulltext_search import (
            get_nested_value,
            extract_all_searchable_text,
            SEARCHABLE_FIELDS,
            highlight_matches
        )
    except ImportError:
        try:
            # Try direct local import
            from fulltext_search import (
                get_nested_value,
                extract_all_searchable_text,
                SEARCHABLE_FIELDS,
                highlight_matches
            )
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in fuzzy_search.py, using minimal implementations")
            
            def get_nested_value(obj, path):
                """Get a value from a nested object using a dot-separated path."""
                try:
                    parts = path.split('.')
                    value = obj
                    for part in parts:
                        if isinstance(value, dict):
                            value = value.get(part)
                        else:
                            return None
                    return value
                except (AttributeError, KeyError, TypeError):
                    return None
            
            def extract_all_searchable_text(offer):
                """Extract all searchable text from an offer into a single string."""
                texts = []
                for field in ["id", "status", "product_type.name", "quantity_in_kg", "user.contact_name"]:
                    value = get_nested_value(offer, field)
                    if value:
                        texts.append(str(value))
                return " ".join(texts)
            
            SEARCHABLE_FIELDS = {
                "id": {"display_name": "Azonosító", "field": "id", "weight": 5},
                "status": {"display_name": "Státusz", "field": "status", "weight": 3},
                "product_name": {"display_name": "Termék", "field": "product_type.name", "weight": 4},
                "producer_name": {"display_name": "Termelő", "field": "user.contact_name", "weight": 4},
            }
            
            def highlight_matches(text, query):
                """Highlight query matches in text with HTML formatting."""
                return text

# Logger setup
logger = logging.getLogger(__name__)

def fuzzy_token_sort_ratio(s1, s2):
    """
    Calculate fuzzy token sort ratio between two strings.
    
    Sorts the tokens in each string alphabetically before comparison,
    which helps in situations where word order differs.
    
    Args:
        s1 (str): First string
        s2 (str): Second string
        
    Returns:
        float: Similarity ratio between 0 and 1
    """
    # Sort words alphabetically
    s1_sorted = ' '.join(sorted(s1.lower().split()))
    s2_sorted = ' '.join(sorted(s2.lower().split()))
    
    # Calculate similarity ratio
    return SequenceMatcher(None, s1_sorted, s2_sorted).ratio()

def fuzzy_token_set_ratio(s1, s2):
    """
    Calculate fuzzy token set ratio between two strings.
    
    Computes intersection of tokens to account for partial matches.
    
    Args:
        s1 (str): First string
        s2 (str): Second string
        
    Returns:
        float: Similarity ratio between 0 and 1
    """
    # Convert to sets of words
    s1_set = set(s1.lower().split())
    s2_set = set(s2.lower().split())
    
    # Find intersection and union
    intersection = s1_set.intersection(s2_set)
    union = s1_set.union(s2_set)
    
    # If no common words, use sequence matcher
    if not intersection:
        return SequenceMatcher(None, s1.lower(), s2.lower()).ratio()
    
    # Calculate Jaccard similarity (intersection / union)
    return len(intersection) / len(union)

def calculate_word_distance(word1, word2, max_distance=2):
    """
    Calculate Levenshtein distance between words.
    
    Args:
        word1 (str): First word
        word2 (str): Second word
        max_distance (int): Maximum edit distance to consider
        
    Returns:
        int: Edit distance or -1 if above max_distance
    """
    # Quick length check to avoid unnecessary calculations
    if abs(len(word1) - len(word2)) > max_distance:
        return -1
    
    # Calculate Levenshtein distance
    distance = levenshtein_distance(word1, word2)
    
    return distance if distance <= max_distance else -1

def fuzzy_word_match(text, query_term, max_distance=2):
    """
    Find fuzzy matches of a query term in text.
    
    Args:
        text (str): Text to search in
        query_term (str): Term to search for
        max_distance (int): Maximum edit distance to consider
        
    Returns:
        list: List of (word, distance) tuples for matches
    """
    # Skip if either is empty
    if not text or not query_term:
        return []
    
    # Normalize
    text = text.lower()
    query_term = query_term.lower()
    
    # Split text into words
    words = re.findall(r'\b\w+\b', text)
    
    # Find matches within edit distance
    matches = []
    for word in words:
        distance = calculate_word_distance(word, query_term, max_distance)
        if distance != -1:
            matches.append((word, distance))
    
    return matches

def find_fuzzy_matches(text, query_terms, max_distance=2):
    """
    Find all fuzzy matches of query terms in text.
    
    Args:
        text (str): Text to search in
        query_terms (list): List of terms to search for
        max_distance (int): Maximum edit distance to consider
        
    Returns:
        dict: Dictionary mapping query terms to lists of matches
    """
    matches = {}
    for term in query_terms:
        term_matches = fuzzy_word_match(text, term, max_distance)
        if term_matches:
            matches[term] = term_matches
    
    return matches

def fuzzy_search_offers(offers, query, threshold=0.5, max_distance=2):
    """
    Search offers using fuzzy matching to handle typos and minor variations.
    
    Args:
        offers (list): List of offer dictionaries
        query (str): Search query
        threshold (float): Matching score threshold
        max_distance (int): Maximum edit distance for fuzzy matching
        
    Returns:
        list: List of matching offers with scores
    """
    if not query or not offers:
        return offers
    
    # Normalize query
    query = query.lower().strip()
    query_terms = query.split()
    
    # Calculate scores for each offer
    results = []
    for offer in offers:
        # Extract searchable text for the offer
        offer_text = extract_all_searchable_text(offer).lower()
        
        # Calculate basic score
        score = 0
        matches = []
        
        # Find fuzzy matches for all query terms
        fuzzy_matches = find_fuzzy_matches(offer_text, query_terms, max_distance)
        
        # Field-specific matching with weights
        for field_key, field_info in SEARCHABLE_FIELDS.items():
            field_path = field_info["field"]
            weight = field_info["weight"]
            
            # Get raw value
            value = get_nested_value(offer, field_path)
            
            # Skip if no value
            if value is None:
                continue
                
            # Format the value
            formatted_value = str(value).lower()
            
            # Process each query term
            for term in query_terms:
                # Exact match
                if term == formatted_value:
                    score += weight * 2.0
                    matches.append(f"Pontos egyezés: {field_info['display_name']}")
                    continue
                
                # Check for fuzzy matches
                term_matches = fuzzy_word_match(formatted_value, term, max_distance)
                if term_matches:
                    # Sort by distance (closest first)
                    term_matches.sort(key=lambda x: x[1])
                    best_match, distance = term_matches[0]
                    
                    # Score inversely proportional to distance
                    match_score = weight * (1.0 - (distance / (max_distance + 1)))
                    score += match_score
                    
                    matches.append(f"Hasonló a(z) '{best_match}' szóhoz ({field_info['display_name']})")
        
        # Overall text similarity calculation
        if len(query) > 3:  # Only for longer queries
            # Calculate similarity ratios
            token_sort_similarity = fuzzy_token_sort_ratio(offer_text, query)
            token_set_similarity = fuzzy_token_set_ratio(offer_text, query)
            
            # Use the better of the two measures
            text_similarity = max(token_sort_similarity, token_set_similarity)
            
            # Add to score (max contribution of 2.0)
            similarity_score = text_similarity * 2.0
            score += similarity_score
            
            if text_similarity > 0.7:
                matches.append(f"Szöveg hasonlóság: {int(text_similarity * 100)}%")
        
        # Normalize score based on number of query terms
        if query_terms:
            score = score / len(query_terms) / 10.0  # Normalize to 0-1 range
        
        # Add to results if score is above threshold
        if score >= threshold:
            # Store score and match info in the offer object
            offer_copy = offer.copy()
            offer_copy["_search_score"] = score
            offer_copy["_search_matches"] = matches
            offer_copy["_fuzzy_search"] = True  # Mark as fuzzy search result
            results.append(offer_copy)
    
    # Sort by score (descending)
    results.sort(key=lambda x: x.get("_search_score", 0), reverse=True)
    
    return results

def render_fuzzy_search_ui():
    """
    Render UI controls for fuzzy search.
    
    Returns:
        tuple: (query, use_fuzzy, max_distance)
    """
    # Get previous search query and settings from session state
    previous_query = st.session_state.get("fuzzy_search_query", "")
    use_fuzzy = st.session_state.get("use_fuzzy", True)
    max_distance = st.session_state.get("fuzzy_max_distance", 2)
    
    # Create columns for the UI
    search_col, settings_col = st.columns([3, 1])
    
    with search_col:
        # Search input
        query = st.text_input(
            "Keresés az ajánlatokban (fuzzy keresés)",
            value=previous_query,
            placeholder="Írja be a keresett szöveget... (elírások megengedettek)",
            key="fuzzy_search_input"
        )
    
    with settings_col:
        # Fuzzy search settings
        use_fuzzy = st.checkbox("Fuzzy keresés", value=use_fuzzy, key="use_fuzzy_checkbox")
        if use_fuzzy:
            max_distance = st.slider(
                "Maximális távolság",
                min_value=1,
                max_value=3,
                value=max_distance,
                key="fuzzy_max_distance_slider",
                help="Nagyobb érték több elírást enged meg, de több téves találatot eredményezhet"
            )
    
    # Search button
    search_button = st.button("🔍 Keresés", key="fuzzy_search_button")
    
    # Only trigger search on button click or Enter key
    if search_button and query:
        # Store query and settings in session state
        st.session_state["fuzzy_search_query"] = query
        st.session_state["use_fuzzy"] = use_fuzzy
        st.session_state["fuzzy_max_distance"] = max_distance
    
    return query, use_fuzzy, max_distance

def render_fuzzy_search_results_info(results, query, total_count, use_fuzzy):
    """
    Render information about fuzzy search results.
    
    Args:
        results (list): Search results
        query (str): Search query
        total_count (int): Total number of items before search
        use_fuzzy (bool): Whether fuzzy search was used
    """
    if not query:
        return
    
    results_count = len(results)
    
    if results_count == 0:
        st.warning(f"Nincs találat a keresett kifejezésre: '{query}'" + 
                  (" (fuzzy keresés használatával)" if use_fuzzy else ""))
    else:
        percentage = int(results_count / total_count * 100) if total_count > 0 else 0
        
        # Differently styled info for exact vs fuzzy search
        if use_fuzzy:
            st.markdown(
                f"""
                <div style="margin: 10px 0; padding: 8px 12px; background-color: #E8EAF6; border-radius: 4px; 
                            border-left: 4px solid #3F51B5;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="font-weight: 500;">Fuzzy keresés:</span> 
                            <span>'{query}'</span>
                            <span style="font-style: italic; color: #666; margin-left: 8px;">(elírások megengedve)</span>
                        </div>
                        <div>
                            <span style="font-weight: 500;">{results_count} találat</span>
                            <span style="color: #666; margin-left: 5px;">({percentage}%)</span>
                        </div>
                    </div>
                </div>
                """,
                unsafe_allow_html=True
            )
        else:
            st.markdown(
                f"""
                <div style="margin: 10px 0; padding: 8px 12px; background-color: #E3F2FD; border-radius: 4px; 
                            border-left: 4px solid #1976D2;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span style="font-weight: 500;">Keresési eredmények:</span> 
                            <span>'{query}'</span>
                        </div>
                        <div>
                            <span style="font-weight: 500;">{results_count} találat</span>
                            <span style="color: #666; margin-left: 5px;">({percentage}%)</span>
                        </div>
                    </div>
                </div>
                """,
                unsafe_allow_html=True
            )

def render_fuzzy_match_indicators(offer, query):
    """
    Render visual indicators for fuzzy search matches in an offer.
    
    Args:
        offer (dict): Offer with search matches
        query (str): Search query
        
    Returns:
        str: HTML for match indicators
    """
    search_score = offer.get("_search_score", 0)
    matches = offer.get("_search_matches", [])
    is_fuzzy = offer.get("_fuzzy_search", False)
    
    if not search_score or not matches:
        return ""
    
    # Create score indicator (0-5 dots)
    score_dots = int(min(search_score * 10, 5))
    
    # Use different color for fuzzy matches
    score_color = "#3F51B5" if is_fuzzy else "#1976D2"
    score_html = f'<span style="color: {score_color};">' + "●" * score_dots + "○" * (5 - score_dots) + '</span>'
    
    # Create matches list (max 3 unique matches)
    unique_matches = []
    for match in matches:
        if match not in unique_matches:
            unique_matches.append(match)
        if len(unique_matches) >= 3:
            break
    
    matches_html = ", ".join(unique_matches[:3])
    if len(matches) > 3:
        matches_html += f" és még {len(matches) - 3} találat"
    
    # Combine score and matches with a fuzzy search indicator if applicable
    fuzzy_indicator = """
    <span style="background-color: #E8EAF6; color: #3F51B5; padding: 2px 4px; 
                border-radius: 4px; font-size: 0.7em; margin-left: 5px;">
        FUZZY
    </span>
    """ if is_fuzzy else ""
    
    return f"""
    <div style="font-size: 0.85em; margin: 5px 0;">
        <div>{score_html} Relevancia: {int(search_score * 100)}%{fuzzy_indicator}</div>
        <div style="color: #666;">{matches_html}</div>
    </div>
    """

def apply_fuzzy_search(offers):
    """
    Apply fuzzy search to offers and render UI.
    
    Args:
        offers (list): List of offer dictionaries
        
    Returns:
        list: Filtered offers matching the search query
    """
    # Store original offers count
    total_count = len(offers)
    
    # Create styled search UI
    st.markdown("""
    <style>
    .fuzzy-search-container {
        margin: 15px 0;
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }
    .fuzzy-search-title {
        font-weight: 500;
        margin-bottom: 10px;
        color: #333;
        display: flex;
        align-items: center;
    }
    .fuzzy-badge {
        background-color: #E8EAF6;
        color: #3F51B5;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8em;
        margin-left: 8px;
    }
    </style>
    <div class="fuzzy-search-container">
        <div class="fuzzy-search-title">
            Intelligens keresés
            <span class="fuzzy-badge">FUZZY</span>
        </div>
    """, unsafe_allow_html=True)
    
    # Render search input and settings
    query, use_fuzzy, max_distance = render_fuzzy_search_ui()
    
    # Close the container div
    st.markdown("</div>", unsafe_allow_html=True)
    
    # Apply search if query exists
    results = offers
    if query:
        # Choose search method based on settings
        if use_fuzzy:
            results = fuzzy_search_offers(offers, query, threshold=0.3, max_distance=max_distance)
        else:
            # Import regular search if available, otherwise fall back
            try:
                from fulltext_search import search_offers
                results = search_offers(offers, query, threshold=0.5)
            except ImportError:
                st.error("Could not import regular search, using fuzzy search instead")
                results = fuzzy_search_offers(offers, query, threshold=0.3, max_distance=max_distance)
        
        # Show search results info
        render_fuzzy_search_results_info(results, query, total_count, use_fuzzy)
    
    return results

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Fuzzy Search Test", layout="wide")
    
    st.title("Fuzzy Search Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"contact_name": "Mezőgazda Márton", "email": "<EMAIL>"},
            "description": "Kiváló minőségű, zamatos körte."
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-04-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Nagy mennyiségű étkezési alma, hosszú tárolhatósággal."
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-03-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"contact_name": "Gyümölcsös Gábor", "email": "<EMAIL>"},
            "description": "Befőzésre alkalmas szilva, magas cukortartalommal."
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-04-25T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"contact_name": "Almás Anna", "email": "<EMAIL>"},
            "description": "Friss nyári körte, egyenesen a fáról."
        }
    ]
    
    # Apply fuzzy search
    results = apply_fuzzy_search(sample_offers)
    
    # Display results
    st.markdown("### Keresési eredmények")
    
    # Display search results with fuzziness indicators
    if results:
        # Check if search results contain relevance info
        has_search_results = any("_search_score" in offer for offer in results)
        
        # Display in a nice format
        for i, offer in enumerate(results):
            query = st.session_state.get("fuzzy_search_query", "")
            
            with st.expander(f"#{offer['id']} - {offer['product_type']['name']} - {offer['user']['contact_name']}", expanded=i==0):
                # Show match indicators if available
                if has_search_results and "_search_score" in offer:
                    st.markdown(render_fuzzy_match_indicators(offer, query), unsafe_allow_html=True)
                
                # Display main offer info
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown(f"**Státusz:** {offer['status']}")
                    st.markdown(f"**Beszállítás:** {offer['delivery_date']}")
                    st.markdown(f"**Mennyiség:** {offer['quantity_in_kg']} kg")
                
                with col2:
                    st.markdown(f"**Ár:** {offer['price']} Ft/kg")
                    st.markdown(f"**Termelő:** {offer['user']['contact_name']}")
                    st.markdown(f"**Létrehozva:** {offer['created_at']}")
                
                # Description with highlighted matches if available
                description = offer.get("description", "")
                if description and query:
                    st.markdown("**Leírás:**")
                    highlighted = highlight_matches(description, query)
                    st.markdown(highlighted, unsafe_allow_html=True)
                else:
                    st.markdown(f"**Leírás:** {description}")
    else:
        st.info("Nincs megjeleníthető eredmény")
        
    # Show sample search examples
    st.sidebar.markdown("### Példa keresések")
    st.sidebar.markdown("""
    Próbálja ki a fuzzy keresést ezekkel:
    - alma (pontos)
    - alma (pontos)
    - alna (elírás az 'alma' szóban)
    - gyümölc (részleges egyezés)
    - termeló (ékezethiba)
    - tams (hiányzó betű a 'Tamás' névből)
    """)
    
    # Show explanation
    st.sidebar.markdown("### Mi az a fuzzy keresés?")
    st.sidebar.markdown("""
    A fuzzy keresés lehetővé teszi, hogy megtalálja a keresett szöveghez hasonló találatokat is, 
    még akkor is, ha a keresés nem pontos. Ez hasznos például elírások, ékezethibák vagy 
    hiányzó betűk esetén.
    
    **Hogyan működik?**
    
    A rendszer kiszámítja a szerkesztési távolságot (Levenshtein-távolság) a keresett szó és 
    a lehetséges egyezések között. Ha ez a távolság a megadott maximális távolságon belül van, 
    akkor a rendszer találatként jeleníti meg.
    
    **Példák:**
    - "alma" → "alma" (távolság: 0, pontos egyezés)
    - "alma" → "alna" (távolság: 1, 1 betű eltérés)
    - "Tamás" → "Tamas" (távolság: 1, ékezethiány)
    - "Körte" → "kőrte" (távolság: 1, kis/nagybetű + ékezet különbség)
    """)