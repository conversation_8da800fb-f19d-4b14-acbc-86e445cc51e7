#!/usr/bin/env python3
"""
Unified Action Panel - Használati példa
Egyszerű integráció és használat az operator offer management oldalon
"""
import streamlit as st
from typing import Dict, Any, List, Callable

# Import a komponenst
from .unified_action_panel import UnifiedActionPanel, get_default_actions, get_default_callbacks

def example_usage_basic():
    """
    Alapvető használati példa
    """
    # Példa ajánlat adat
    offer_data = {
        'id': 123,
        'status': 'CREATED',
        'producer_name': 'Kiss János',
        'product_name': 'Paradicsom',
        'quantity': 500,
        'price': 350.0
    }
    
    # Panel létrehozása
    panel = UnifiedActionPanel(
        offer_id=offer_data['id'],
        offer_data=offer_data
    )
    
    # Alapértelmezett műveletek és callback-ek
    actions = get_default_actions(offer_data)
    callbacks = get_default_callbacks()
    
    # Panel renderelése
    results = panel.render(
        actions=actions,
        title="<PERSON><PERSON><PERSON><PERSON> művelete<PERSON>",
        show_status_info=True,
        callbacks=callbacks
    )
    
    # Eredmények kezel<PERSON>e
    if results:
        st.success(f"Művelet végrehajtva: {list(results.keys())}")

def example_usage_custom():
    """
    Testreszabott használati példa
    """
    # Példa ajánlat adat
    offer_data = {
        'id': 456,
        'status': 'CONFIRMED_BY_COMPANY',
        'producer_name': 'Nagy Péter',
        'product_name': 'Alma',
        'quantity': 1000,
        'price': 280.0
    }
    
    # Testreszabott műveletek
    custom_actions = [
        {
            'type': 'approve',
            'label': 'Jóváhagyás',
            'icon': '✅',
            'description': 'Az ajánlat jóváhagyása',
            'enabled': True,
            'style': 'primary'
        },
        {
            'type': 'reject',
            'label': 'Elutasítás',
            'icon': '❌',
            'description': 'Az ajánlat elutasítása',
            'enabled': True,
            'style': 'secondary'
        },
        {
            'type': 'negotiate',
            'label': 'Tárgyalás',
            'icon': '💬',
            'description': 'Ár vagy mennyiség tárgyalása',
            'enabled': True,
            'style': 'secondary'
        }
    ]
    
    # Testreszabott callback-ek
    def approve_callback(offer_id: int, action: Dict[str, Any]):
        st.success(f"Ajánlat {offer_id} jóváhagyva!")
        # Itt hívható az API
        return {"approved": True}
    
    def reject_callback(offer_id: int, action: Dict[str, Any]):
        st.error(f"Ajánlat {offer_id} elutasítva!")
        # Itt hívható az API
        return {"rejected": True}
    
    def negotiate_callback(offer_id: int, action: Dict[str, Any]):
        st.info(f"Tárgyalás indítva az ajánlat {offer_id} esetében")
        # Itt nyílhat meg egy dialógus
        return {"negotiating": True}
    
    custom_callbacks = {
        'approve': approve_callback,
        'reject': reject_callback,
        'negotiate': negotiate_callback
    }
    
    # Panel létrehozása
    panel = UnifiedActionPanel(
        offer_id=offer_data['id'],
        offer_data=offer_data
    )
    
    # Panel renderelése
    results = panel.render(
        actions=custom_actions,
        title="Egyedi műveletek",
        show_status_info=True,
        callbacks=custom_callbacks
    )
    
    # Eredmények kezelése
    if results:
        for action_type, result in results.items():
            st.json({"action": action_type, "result": result})

def example_integration_offer_detail():
    """
    Példa az integráció módja az offer_detail komponensben
    """
    # Ez a kód az offer_detail.py fájlban használható
    
    # Ajánlat adatok (API-ból)
    offer_data = st.session_state.get('current_offer_data', {})
    
    if not offer_data:
        st.error("Nincs ajánlat adat!")
        return
    
    # Ajánlat részletek megjelenítése
    st.header(f"Ajánlat #{offer_data.get('id', 'N/A')}")
    
    # ... más ajánlat részletek ...
    
    # Műveleti panel
    st.subheader("Elérhető műveletek")
    
    panel = UnifiedActionPanel(
        offer_id=offer_data['id'],
        offer_data=offer_data
    )
    
    # Státusz alapú műveletek
    actions = get_default_actions(offer_data)
    
    # Speciális callback-ek hozzáadása
    callbacks = get_default_callbacks()
    
    # Státusz változtatás callback testreszabása
    def enhanced_status_change(offer_id: int, action: Dict[str, Any]):
        # Státusz váltás dialógus megnyitása
        st.session_state[f"show_status_dialog_{offer_id}"] = True
        st.session_state["status_change_source"] = "unified_panel"
        st.rerun()
    
    callbacks['status_change'] = enhanced_status_change
    
    # Panel renderelése
    results = panel.render(
        actions=actions,
        title="Ajánlat műveletek",
        show_status_info=True,
        callbacks=callbacks
    )
    
    # Eredmények kezelése
    if results:
        # Logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Unified panel action executed: {results}")

def example_mobile_responsive():
    """
    Mobil-barát megjelenítés példa
    """
    # Mobil eszköz szimuláció
    st.session_state['is_mobile'] = True
    
    offer_data = {
        'id': 789,
        'status': 'ACCEPTED_BY_USER',
        'producer_name': 'Szabó Anna',
        'product_name': 'Paprika',
        'quantity': 300,
        'price': 400.0
    }
    
    # Mobil-optimalizált műveletek (kevesebb gomb)
    mobile_actions = [
        {
            'type': 'back',
            'label': 'Vissza',
            'icon': '←',
            'description': 'Vissza a listához',
            'enabled': True,
            'style': 'secondary'
        },
        {
            'type': 'finalize',
            'label': 'Véglegesítés',
            'icon': '✅',
            'description': 'Ajánlat véglegesítése',
            'enabled': True,
            'style': 'primary'
        },
        {
            'type': 'more',
            'label': 'További műveletek',
            'icon': '⋯',
            'description': 'További opciók megjelenítése',
            'enabled': True,
            'style': 'secondary'
        }
    ]
    
    panel = UnifiedActionPanel(
        offer_id=offer_data['id'],
        offer_data=offer_data
    )
    
    # Mobil callback-ek
    def finalize_callback(offer_id: int, action: Dict[str, Any]):
        st.success("Ajánlat véglegesítve!")
        return {"finalized": True}
    
    def more_callback(offer_id: int, action: Dict[str, Any]):
        st.session_state["show_more_actions"] = True
        st.rerun()
    
    mobile_callbacks = {
        'back': get_default_callbacks()['back'],
        'finalize': finalize_callback,
        'more': more_callback
    }
    
    # Mobil panel renderelése
    results = panel.render(
        actions=mobile_actions,
        title="Műveletek",
        show_status_info=True,
        callbacks=mobile_callbacks
    )
    
    # További műveletek dialógus
    if st.session_state.get("show_more_actions", False):
        with st.expander("További műveletek", expanded=True):
            if st.button("Export", key="mobile_export"):
                st.success("Export elindítva!")
            if st.button("Részletek", key="mobile_details"):
                st.info("Részletek megjelenítve!")
            if st.button("Bezárás", key="mobile_close"):
                st.session_state["show_more_actions"] = False
                st.rerun()

# Fő demo függvény
def main():
    """
    Fő demo függvény - példák futtatása
    """
    st.title("Unified Action Panel - Demonstráció")
    
    demo_type = st.selectbox(
        "Válassz demo típust:",
        ["Alapvető használat", "Testreszabott műveletek", "Mobil-barát", "Integráció példa"]
    )
    
    st.divider()
    
    if demo_type == "Alapvető használat":
        st.subheader("Alapvető használat")
        example_usage_basic()
    
    elif demo_type == "Testreszabott műveletek":
        st.subheader("Testreszabott műveletek")
        example_usage_custom()
    
    elif demo_type == "Mobil-barát":
        st.subheader("Mobil-barát megjelenítés")
        example_mobile_responsive()
    
    elif demo_type == "Integráció példa":
        st.subheader("Integráció módja")
        st.code("""
# Az offer_detail.py fájlban:
from .unified_action_panel import UnifiedActionPanel, get_default_actions, get_default_callbacks

# Ajánlat adatok betöltése után:
panel = UnifiedActionPanel(
    offer_id=offer_data['id'],
    offer_data=offer_data
)

actions = get_default_actions(offer_data)
callbacks = get_default_callbacks()

# Testreszabott callback-ek hozzáadása
def custom_status_change(offer_id, action):
    # Saját logika
    pass

callbacks['status_change'] = custom_status_change

# Renderelés
results = panel.render(
    actions=actions,
    callbacks=callbacks
)
        """, language="python")
        
        example_integration_offer_detail()

if __name__ == "__main__":
    main()