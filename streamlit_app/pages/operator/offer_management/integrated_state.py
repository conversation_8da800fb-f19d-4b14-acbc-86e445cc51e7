"""
Integrated state management for offer management
Extends the existing state_management.py with offer-specific functionality
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, Any
from .filter_manager import FilterManager

logger = logging.getLogger(__name__)

# Try to import existing state management
try:
    from .state_management import init_page_state, inject_keyboard_shortcuts
    HAS_EXISTING_STATE_MGMT = True
    logger.info("Successfully imported existing state_management module")
except ImportError:
    logger.warning("Could not import existing state_management, using fallback")
    HAS_EXISTING_STATE_MGMT = False
    
    # Fallback implementations
    def init_page_state():
        """Fallback page state initialization"""
        if 'page_state_initialized' not in st.session_state:
            st.session_state.page_state_initialized = True
            logger.info("Fallback page state initialized")
    
    def inject_keyboard_shortcuts():
        """Fallback keyboard shortcuts"""
        pass


class OfferStateManager:
    """Centralized state management for offer operations"""
    
    def __init__(self):
        self.filter_manager = FilterManager()
        self.initialize_state()
    
    def initialize_state(self):
        """Initialize all offer management state"""
        # Use existing state_management if available
        try:
            init_page_state()
            if HAS_EXISTING_STATE_MGMT:
                inject_keyboard_shortcuts()
        except Exception as e:
            logger.error(f"Error initializing existing state management: {e}")
        
        # Add offer-specific state
        self.init_offer_specific_state()
    
    def init_offer_specific_state(self):
        """Initialize offer-specific session state variables"""
        if 'offer_state_manager' not in st.session_state:
            st.session_state.offer_state_manager = {
                'initialized': True,
                'initialization_time': datetime.now(),
                'offers_cache': None,
                'cache_timestamp': None,
                'last_api_call': None,
                'data_loading_state': 'idle',  # idle, loading, success, error
                'error_messages': [],
                'performance_metrics': {
                    'api_calls_count': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'average_load_time': 0
                }
            }
        
        # Initialize device detection state
        if 'device_type' not in st.session_state:
            st.session_state.device_type = 'desktop'  # Default, can be overridden by JS
        
        logger.info("Offer-specific state initialized")
    
    def get_current_filters(self) -> Dict[str, Any]:
        """Get current filter state through filter manager"""
        return self.filter_manager.get_ui_filters()
    
    def should_reload_offers(self, filters: Dict[str, Any]) -> bool:
        """Determine if offers need reloading"""
        return self.filter_manager.should_refresh_data(filters)
    
    def update_offers_cache(self, offers: list, filters: Dict[str, Any]):
        """Update offers cache with filter-specific key"""
        state = st.session_state.offer_state_manager
        timestamp = datetime.now()
        
        # Generate filter-specific cache key
        cache_key = self._generate_cache_key(filters)
        
        # Initialize cache dict if not exists
        if 'offers_cache_dict' not in state:
            state['offers_cache_dict'] = {}
        
        # Store with filter-specific key
        state['offers_cache_dict'][cache_key] = {
            'offers': offers,
            'timestamp': timestamp,
            'filters': filters.copy()
        }
        
        # Maintain backward compatibility
        state['offers_cache'] = offers
        state['cache_timestamp'] = timestamp
        state['last_api_call'] = timestamp
        
        # Update filter manager cache
        self.filter_manager.update_filter_cache(filters, timestamp)
        
        # Update performance metrics
        state['performance_metrics']['cache_misses'] += 1
        
        logger.info(f"Offers cache updated with {len(offers)} offers at {timestamp} for key: {cache_key}")
    
    def _generate_cache_key(self, filters: Dict[str, Any]) -> str:
        """Generate a unique cache key based on filters"""
        import json
        from datetime import date, datetime
        
        def serialize_for_key(obj):
            """Convert objects to JSON-serializable format for cache key"""
            if isinstance(obj, (date, datetime)):
                return obj.strftime('%Y-%m-%d')
            elif isinstance(obj, dict):
                return {k: serialize_for_key(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize_for_key(item) for item in obj]
            else:
                return obj
        
        # Create a normalized version of filters for consistent key generation
        normalized_filters = serialize_for_key(filters)
        
        # Sort keys for consistent ordering
        cache_key = json.dumps(normalized_filters, sort_keys=True)
        
        # Use hash for shorter keys
        import hashlib
        return hashlib.md5(cache_key.encode()).hexdigest()[:12]
    
    def get_cached_offers(self, filters: Dict[str, Any] = None) -> list:
        """Get offers from cache with filter-specific lookup"""
        state = st.session_state.offer_state_manager
        
        if filters:
            # Try filter-specific cache first
            cache_key = self._generate_cache_key(filters)
            cache_dict = state.get('offers_cache_dict', {})
            
            if cache_key in cache_dict:
                cached_entry = cache_dict[cache_key]
                
                # Check if cache is still valid (5 minute expiry)
                cache_age = (datetime.now() - cached_entry['timestamp']).total_seconds()
                if cache_age < 300:  # 5 minutes
                    state['performance_metrics']['cache_hits'] += 1
                    logger.debug(f"Cache hit for key: {cache_key}, age: {cache_age:.1f}s")
                    return cached_entry['offers']
                else:
                    logger.debug(f"Cache expired for key: {cache_key}, age: {cache_age:.1f}s")
                    # Remove expired entry
                    del cache_dict[cache_key]
        
        # Fallback to legacy cache
        offers = state.get('offers_cache', [])
        
        if offers:
            # Update performance metrics
            state['performance_metrics']['cache_hits'] += 1
            logger.debug(f"Cache hit: returned {len(offers)} offers")
        else:
            logger.debug("Cache miss: no cached offers")
        
        return offers or []
    
    def set_loading_state(self, loading_state: str, error_message: str = None):
        """Set data loading state"""
        state = st.session_state.offer_state_manager
        state['data_loading_state'] = loading_state
        
        if error_message:
            state['error_messages'].append({
                'timestamp': datetime.now(),
                'message': error_message
            })
            logger.error(f"Loading state error: {error_message}")
        
        logger.debug(f"Loading state changed to: {loading_state}")
    
    def get_loading_state(self) -> str:
        """Get current loading state"""
        return st.session_state.offer_state_manager.get('data_loading_state', 'idle')
    
    def clear_offers_cache(self):
        """Clear all offers cache"""
        state = st.session_state.offer_state_manager
        state['offers_cache'] = None
        state['cache_timestamp'] = None
        state['last_api_call'] = None
        state['error_messages'] = []
        
        # Clear filter-specific cache dict
        state['offers_cache_dict'] = {}
        
        # Clear filter manager cache
        self.filter_manager.clear_cache()
        
        logger.info("All offers cache cleared (including filter-specific cache)")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return st.session_state.offer_state_manager.get('performance_metrics', {})
    
    def update_performance_metrics(self, api_call_time: float):
        """Update performance metrics with new API call time"""
        state = st.session_state.offer_state_manager
        metrics = state['performance_metrics']
        
        metrics['api_calls_count'] += 1
        
        # Calculate average load time
        current_avg = metrics.get('average_load_time', 0)
        count = metrics['api_calls_count']
        new_avg = ((current_avg * (count - 1)) + api_call_time) / count
        metrics['average_load_time'] = new_avg
        
        logger.debug(f"Performance metrics updated: API calls={count}, avg_time={new_avg:.2f}s")
    
    def get_error_messages(self) -> list:
        """Get recent error messages"""
        state = st.session_state.offer_state_manager
        return state.get('error_messages', [])
    
    def clear_error_messages(self):
        """Clear error messages"""
        state = st.session_state.offer_state_manager
        state['error_messages'] = []
        logger.debug("Error messages cleared")
    
    def get_state_summary(self) -> Dict[str, Any]:
        """Get summary of current state for debugging"""
        state = st.session_state.offer_state_manager
        filter_errors = self.filter_manager.get_validation_errors()
        
        # Filter-specific cache info
        cache_dict = state.get('offers_cache_dict', {})
        filter_cache_count = len(cache_dict)
        
        return {
            'initialized': state.get('initialized', False),
            'initialization_time': state.get('initialization_time'),
            'cache_status': 'active' if state.get('offers_cache') else 'empty',
            'cache_timestamp': state.get('cache_timestamp'),
            'filter_cache_count': filter_cache_count,
            'filter_cache_keys': list(cache_dict.keys())[:5],  # Show first 5 keys
            'loading_state': state.get('data_loading_state', 'unknown'),
            'error_count': len(state.get('error_messages', [])),
            'filter_errors': filter_errors,
            'performance_metrics': state.get('performance_metrics', {}),
            'device_type': st.session_state.get('device_type', 'unknown')
        }
    
    def reset_state(self):
        """Reset all state to initial values"""
        # Clear session state
        keys_to_clear = [
            'offer_state_manager',
            'filter_manager_state', 
            'device_type'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
        
        # Clear filter UI state
        filter_keys = [k for k in st.session_state.keys() if k.startswith('modern_')]
        for key in filter_keys:
            del st.session_state[key]
        
        # Reinitialize
        self.initialize_state()
        
        logger.info("State completely reset")
    
    def export_state_for_debug(self) -> Dict[str, Any]:
        """Export state for debugging purposes"""
        return {
            'session_state_keys': list(st.session_state.keys()),
            'offer_state': st.session_state.get('offer_state_manager', {}),
            'filter_state': st.session_state.get('filter_manager_state', {}),
            'ui_filter_keys': [k for k in st.session_state.keys() if k.startswith('modern_')],
            'current_filters': self.get_current_filters(),
            'state_summary': self.get_state_summary()
        }