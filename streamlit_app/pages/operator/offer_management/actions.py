"""
Business logic for the offer management page.
Contains functions for handling offer actions and status transitions.
"""
import streamlit as st
import logging
import uuid
from datetime import datetime
# Try to import notification components with fallbacks
try:
    # Try absolute import first
    from streamlit_app.components.notification import show_info, show_error, show_success
except ImportError:
    try:
        # Try regular app-relative import
        from components.notification import show_info, show_error, show_success
    except ImportError:
        # Fallback notification functions
        logging.warning("Could not import notification components in actions.py, using fallbacks")
        show_info = lambda msg: st.info(msg)
        show_error = lambda msg: st.error(msg)
        show_success = lambda msg: st.success(msg)

# Import enhanced UI components with comprehensive fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        render_confirmation_dialog,
        open_confirmation_dialog,
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            render_confirmation_dialog,
            open_confirmation_dialog,
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                render_confirmation_dialog,
                open_confirmation_dialog,
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
        except ImportError:
            # Fallback implementations for essential functions
            logging.warning("Could not import enhanced_ui_components in actions.py, using fallbacks")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            def render_confirmation_dialog(title, message, confirm_text="Yes", cancel_text="No", key=None):
                st.write(f"**{title}**")
                st.write(message)
                col1, col2 = st.columns(2)
                with col1:
                    cancel = st.button(cancel_text, key=f"cancel_{uuid.uuid4()}")
                with col2:
                    confirm = st.button(confirm_text, key=f"confirm_{uuid.uuid4()}")
                return confirm
                
            def open_confirmation_dialog(title, message, confirm_text="Yes", cancel_text="No", key=None):
                pass

# Import API client functions with comprehensive fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.api_client import update_offer, update_offer_status, delete_offer
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.api_client import update_offer, update_offer_status, delete_offer
    except ImportError:
        try:
            # Try direct local import
            from api_client import update_offer, update_offer_status, delete_offer
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import api_client functions in actions.py, using fallbacks")
            
            def update_offer(offer_id, data):
                """Fallback update offer function"""
                return False, "API client not available"
                
            def update_offer_status(offer_id, status, note=None):
                """Fallback update offer status function"""
                return False, "API client not available"
                
            def delete_offer(offer_id):
                """Fallback delete offer function"""
                return False, "API client not available"

# Import visual feedback components with fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.visual_feedback import show_operation_feedback, display_status_change_feedback, create_styled_button, create_action_button_group
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.visual_feedback import show_operation_feedback, display_status_change_feedback, create_styled_button, create_action_button_group
    except ImportError:
        try:
            # Try direct local import
            from visual_feedback import show_operation_feedback, display_status_change_feedback, create_styled_button, create_action_button_group
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import visual_feedback functions in actions.py, using fallbacks")
            
            def show_operation_feedback(success, message, operation_type="Operation"):
                """Fallback operation feedback function"""
                if success:
                    st.success(f"{operation_type} successful: {message}")
                else:
                    st.error(f"{operation_type} failed: {message}")
                    
            def display_status_change_feedback(success, message):
                """Fallback status change feedback function"""
                show_operation_feedback(success, message, "Status change")
                
            def create_styled_button(label, key, **kwargs):
                """Fallback styled button function"""
                return st.button(label, key=key, **kwargs)
                
            def create_action_button_group(actions, key_prefix):
                """Fallback action button group function"""
                cols = st.columns(len(actions))
                for i, (label, on_click) in enumerate(actions):
                    with cols[i]:
                        if st.button(label, key=f"{key_prefix}_{i}"):
                            on_click()

# Import validation functions with fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.validation import (
        validate_offer_data,
        validate_email,
        validate_phone,
        validate_safe_filename,
        sanitize_html,
        show_validation_errors
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.validation import (
            validate_offer_data,
            validate_email,
            validate_phone,
            validate_safe_filename,
            sanitize_html,
            show_validation_errors
        )
    except ImportError:
        try:
            # Try direct local import
            from validation import (
                validate_offer_data,
                validate_email,
                validate_phone,
                validate_safe_filename,
                sanitize_html,
                show_validation_errors
            )
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import validation functions in actions.py, using fallbacks")
            
            def validate_offer_data(data):
                """Fallback validation function"""
                return True, []
                
            def validate_email(email):
                """Fallback email validation function"""
                return "@" in email
                
            def validate_phone(phone):
                """Fallback phone validation function"""
                return len(phone) >= 8
                
            def validate_safe_filename(filename):
                """Fallback filename validation function"""
                return filename and len(filename) > 0
                
            def sanitize_html(html):
                """Fallback HTML sanitization function"""
                return html
                
            def show_validation_errors(errors):
                """Fallback validation error display function"""
                for error in errors:
                    st.error(error)

logger = logging.getLogger(__name__)

def display_offer_products(offer):
    """
    Ajánlathoz tartozó termékek táblázatos megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    st.subheader("Termékek")
    
    # Debug info
    logger.debug(f"Displaying products for offer: {offer.get('id')}")
    
    # Get product details
    products = []
    product_type_id = offer.get('product_type_id')
    quality_grade_id = offer.get('quality_grade_id')
    
    logger.debug(f"Product type ID: {product_type_id}")
    logger.debug(f"Quality grade ID: {quality_grade_id}")
    
    if product_type_id:
        try:
            # Termék típus részletek
            try:
                from pages.operator.offer_management.api_client import get_product_type, get_quality_grade
            except ImportError:
                from api_client import get_product_type, get_quality_grade
            
            success, product_type = get_product_type(product_type_id)
            if success:
                logger.debug(f"Product type details: {product_type}")
                
                # Kategória név kinyerése
                category_name = "N/A"
                if "category" in product_type and product_type["category"]:
                    category_name = product_type["category"].get("name", "N/A")
                
                # Get quality grade details if available
                quality_grade_name = "N/A"
                quality_grade = None
                
                # 1. Próba: API hívás, ha implementálva van
                if quality_grade_id:
                    try:
                        logger.debug("Fetching quality grade details...")
                        success, quality_grade = get_quality_grade(quality_grade_id)
                        if success:
                            quality_grade_name = quality_grade.get('name', 'N/A')
                            logger.debug(f"Quality grade details: {quality_grade}")
                    except Exception as e:
                        logger.error(f"Error fetching quality grade: {str(e)}")
                
                # 2. Alternatíva: Minőségi osztály adatok kinyerése az ajánlatból
                if quality_grade_name == "N/A" and "quality_grade" in offer and offer["quality_grade"]:
                    quality_grade = offer["quality_grade"]
                    quality_grade_name = quality_grade.get("name", "N/A")
                    logger.debug(f"Using quality grade from offer: {quality_grade_name}")
                
                # Get quantity and price
                quantity = offer.get('quantity_in_kg', 0)
                price = offer.get('confirmed_price') or offer.get('price') or 0
                
                logger.debug(f"Quantity: {quantity}")
                logger.debug(f"Price: {price}")
                
                # Create product entry - Always use 0 instead of None for total_price
                # Make sure both price and quantity are numeric types before multiplication
                try:
                    price_numeric = float(price) if price is not None else 0
                    quantity_numeric = float(quantity) if quantity is not None else 0
                    total_price = price_numeric * quantity_numeric
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert price or quantity to number: price={price}, quantity={quantity}")
                    total_price = 0
                
                product_entry = {
                    "name": product_type.get("name", "Ismeretlen termék"),
                    "category": category_name,
                    "quality_grade": quality_grade_name,
                    "unit_price": price,
                    "quantity": quantity,
                    "unit": "kg",
                    "total_price": total_price
                }
                
                products.append(product_entry)
            else:
                logger.error(f"Failed to get product type details: {product_type}")
        except Exception as e:
            logger.error(f"Error fetching product details: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    if products:
        # Termékek DataFrame létrehozása
        import pandas as pd
        df_products = pd.DataFrame(products)
        
        # Oszlopok átnevezése
        rename_dict = {
            "name": "Termék neve",
            "category": "Kategória",
            "quality_grade": "Minőségi osztály",
            "unit_price": "Egységár",
            "quantity": "Mennyiség",
            "unit": "Me.",
            "total_price": "Összérték"
        }
        
        available_columns = [col for col in rename_dict.keys() if col in df_products.columns]
        rename_available = {k: v for k, v in rename_dict.items() if k in available_columns}
        
        df_products.rename(columns=rename_available, inplace=True)
        
        # Formázások alkalmazása
        from utils.formatting import format_price, format_quantity
        
        if "Egységár" in df_products.columns:
            df_products["Egységár"] = df_products["Egységár"].apply(
                lambda x: format_price(x) if x is not None and x != '' else "Nincs ár"
            )
        
        if "Mennyiség" in df_products.columns:
            df_products["Mennyiség"] = df_products["Mennyiség"].apply(
                lambda x: format_quantity(x) if x is not None and x != '' else "Nincs megadva"
            )
        
        if "Összérték" in df_products.columns:
            df_products["Összérték"] = df_products["Összérték"].apply(
                lambda x: format_price(x) if x is not None and x != '' and x != 0 else "Nincs ár"
            )
        
        # Táblázat megjelenítése
        display_columns = [col for col in ["Termék neve", "Kategória", "Minőségi osztály", "Mennyiség", "Me.", 
                                         "Egységár", "Összérték"] 
                         if col in df_products.columns]
        
        st.dataframe(df_products[display_columns], use_container_width=True)
        
        # Összesítés - Teljes védelem a különböző értéktípusok ellen
        try:
            # Szigorú védelem a None, '', vagy nem konvertálható értékek ellen
            def safe_price(p):
                try:
                    price_val = p.get("total_price")
                    # Ha None vagy üres string, 0-t használunk
                    if price_val is None or price_val == '':
                        return 0
                    # Biztosítsuk, hogy szám legyen
                    return float(price_val)
                except (ValueError, TypeError):
                    logger.warning(f"Nem számszerű érték a total_price-ben: {p.get('total_price')}")
                    return 0
                
            total_price = sum([safe_price(p) for p in products])
            
            # Csak akkor jelenítjük meg, ha pozitív az érték
            if total_price > 0:
                from utils.formatting import format_price
                st.markdown(f"**Ajánlat összértéke**: {format_price(total_price)}")
            else:
                st.info("Az ajánlat értéke nem határozható meg, hiányzó vagy nulla áradatok miatt.")
        except Exception as e:
            logger.error(f"Hiba az összérték számításánál: {str(e)}")
            st.info("Az ajánlat értékét nem sikerült kiszámítani.")
    else:
        st.info("Nincsenek termékadatok ehhez az ajánlathoz.")

def render_offer_edit_form(offer, offer_id):
    """
    Ajánlat szerkesztési űrlap megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    st.subheader("Ajánlat szerkesztése")
    
    # Terméktípusok és minőségi osztályok betöltése
    try:
        from pages.operator.offer_management.api_client import get_product_types, get_quality_grades
    except ImportError:
        from api_client import get_product_types, get_quality_grades
    
    success, product_types = get_product_types()
    if not success:
        st.error(f"Hiba a terméktípusok betöltésekor: {product_types}")
        return
    
    # Aktuális terméktípus és árak
    current_product_type_id = offer.get("product_type_id")
    current_quantity = offer.get("quantity_in_kg", 0)
    current_price = offer.get("price", 0)
    current_note = offer.get("note", "")
    
    # Beszállítási dátum
    delivery_date_str = offer.get("delivery_date", "")
    current_delivery_date = None
    
    if delivery_date_str:
        try:
            current_delivery_date = datetime.strptime(delivery_date_str, "%Y-%m-%d").date()
        except Exception as e:
            logger.error(f"Error parsing delivery date {delivery_date_str}: {str(e)}")
            current_delivery_date = datetime.now().date()
    else:
        current_delivery_date = datetime.now().date()
    
    # Űrlap létrehozása
    with st.form("offer_edit_form"):
        st.markdown("#### Ajánlat adatai")
        
        # Terméktípus kiválasztása
        selected_product_type = st.selectbox(
            "Terméktípus",
            options=[pt["id"] for pt in product_types],
            format_func=lambda x: next((pt["name"] for pt in product_types if pt["id"] == x), "Ismeretlen"),
            index=next((i for i, pt in enumerate(product_types) if pt["id"] == current_product_type_id), 0)
        )
        
        # Mennyiség beállítása
        try:
            from pages.operator.offer_management.ui_components import render_quantity_input
        except ImportError:
            from ui_components import render_quantity_input
        
        quantity = render_quantity_input(
            "Mennyiség (kg)",
            key="quantity_input",
            value=current_quantity,
            min_value=0.1,
            max_value=100000,
            help="A termék mennyisége kilogrammban."
        )
        
        # Egységár beállítása
        price = st.number_input(
            "Egységár (Ft/kg)",
            min_value=0.0,
            max_value=1000000.0,
            value=float(current_price) if current_price else 0.0,
            step=1.0,
            format="%.2f"
        )
        
        # Beszállítási dátum
        delivery_date = st.date_input(
            "Beszállítási dátum",
            value=current_delivery_date
        )
        
        # Megjegyzés szerkesztése
        note = st.text_area(
            "Megjegyzés",
            value=current_note
        )
        
        # Mentés gomb
        if st.form_submit_button("Mentés"):
            # Adatok összeállítása
            updated_data = {
                "product_type_id": selected_product_type,
                "quantity_in_kg": quantity,
                "price": price,
                "delivery_date": delivery_date.strftime("%Y-%m-%d"),
                "note": sanitize_html(note)  # Sanitize HTML content for security
            }
            
            # Validate the data before sending to API
            is_valid, errors, warnings = validate_offer_data(updated_data, is_new=False)
            
            # Show validation errors and warnings if any
            if errors or warnings:
                show_validation_errors(errors, warnings)
            
            # Only proceed if data is valid
            if is_valid:
                # API hívás a frissítéshez
                success, result = update_offer(offer_id, updated_data)
                
                if success:
                    show_success("Az ajánlat sikeresen frissítve!")
                    # Cache frissítése
                    st.session_state.offer_cache[offer_id] = result
                    # Szerkesztési mód kikapcsolása
                    del st.session_state["edit_mode"]
                    st.rerun()
                else:
                    show_error(f"Hiba az ajánlat frissítésekor: {result}")
            else:
                logger.warning(f"Validation failed for offer #{offer_id}: {errors}")

def handle_status_transitions(offer, offer_id):
    """
    Ajánlat státusz átmenetek és műveletek kezelése.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    # Státusz kezelés és műveletek
    st.subheader("Műveletek")
    
    # Elérhető státusz átmenetek az aktuális státusz alapján
    available_transitions = {
        "CREATED": ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"],
        "CONFIRMED_BY_COMPANY": ["ACCEPTED_BY_USER", "REJECTED_BY_USER"],
        "ACCEPTED_BY_USER": ["FINALIZED"],
        "REJECTED_BY_USER": ["CREATED"],
        "FINALIZED": [],
        "pending": ["approved", "rejected", "in_progress"],
        "approved": ["in_progress", "completed", "rejected"],
        "rejected": ["pending", "approved"],
        "in_progress": ["approved", "completed", "rejected"],
        "completed": [],
        "canceled": []
    }
    
    # Státusz emberi olvasható magyarázata
    status_explanations = {
        "CONFIRMED_BY_COMPANY": "Az ajánlat megerősítésre került a vállalat által. Most már a termelő elfogadhatja vagy elutasíthatja.",
        "ACCEPTED_BY_USER": "A termelő elfogadta az ajánlatot. Az ajánlat most már véglegesíthető.",
        "REJECTED_BY_USER": "A termelő elutasította az ajánlatot. Az ajánlatot újra lehet nyitni.",
        "FINALIZED": "Az ajánlat véglegesítésre került. További módosítás nem lehetséges.",
        "approved": "Az ajánlat jóváhagyásra került. A termelő értesítést kapott.",
        "in_progress": "Az ajánlat feldolgozás alatt áll.",
        "completed": "Az ajánlat teljesítésre került és a folyamat lezárult.",
        "rejected": "Az ajánlat elutasításra került.",
        "canceled": "Az ajánlat törölve lett."
    }
    
    # Státusz gomb stílusok
    status_button_styles = {
        "CONFIRMED_BY_COMPANY": "primary",
        "ACCEPTED_BY_USER": "success",
        "REJECTED_BY_USER": "danger",
        "FINALIZED": "info",
        "approved": "success",
        "in_progress": "primary",
        "completed": "info",
        "rejected": "danger",
        "canceled": "dark"
    }
    
    # Az aktuális státusz lekérése
    current_status = offer.get("status", "CREATED")
    
    # Ha vannak elérhető átmenetek az aktuális státuszból
    if current_status in available_transitions and available_transitions[current_status]:
        # Státusz átmenetek megjelenítése
        st.markdown("#### Státusz átmenetek")
        
        # Státusz kiválasztása
        selected_status = st.selectbox(
            "Új státusz:",
            options=available_transitions[current_status],
            format_func=lambda x: f"{x} - {status_explanations.get(x, '')}"
        )
        
        # Megerősítés kérése
        st.info(f"A kiválasztott új státusz: **{selected_status}**\n\n{status_explanations.get(selected_status, '')}")
        
        # Lehetőség megjegyzés hozzáadására
        status_note = st.text_area("Megjegyzés a státuszváltozáshoz:", key="status_note")
        
        # Megerősítő gomb
        button_style = status_button_styles.get(selected_status, "primary")
        if st.button(f"Státusz módosítása: {selected_status}", type=button_style, key="status_change_btn"):
            # Megerősítő checkbox
            confirm = st.checkbox("Megerősítem a státuszváltoztatást", key="confirm_status_change")
            
            if confirm:
                try:
                    # Státusz változtatás API hívás
                    success, result = update_offer_status(offer_id, selected_status, status_note)
                    
                    if success:
                        # Sikeres státuszváltás kezelése
                        confirmation_data = {
                            "status": selected_status
                        }
                        
                        display_status_change_feedback(current_status, selected_status, success)
                        # Cache frissítése
                        if offer_id in st.session_state.offer_cache:
                            st.session_state.offer_cache[offer_id]["status"] = selected_status
                            if confirmation_data:
                                st.session_state.offer_cache[offer_id].update(confirmation_data)
                        st.rerun()
                    else:
                        show_error(f"Hiba a státusz frissítésekor: {result}")
                except Exception as e:
                    logger.error(f"Error updating offer status: {str(e)}")
                    show_error(f"Hiba a státusz frissítésekor: {str(e)}")
    else:
        if current_status in ["FINALIZED", "completed", "canceled"]:
            st.info("Ez az ajánlat már véglegesítve vagy lezárva van, további státuszváltás nem lehetséges.")
        else:
            st.warning("Az aktuális státuszból nincs elérhető státuszváltás.")

def handle_additional_actions(offer, offer_id):
    """
    További ajánlatkezelési műveletek implementálása.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    # További műveletek
    st.subheader("További műveletek")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Ajánlat részletes nyomtatása"):
            # TODO: Nyomtatási funkció
            show_info("A nyomtatási funkció jelenleg fejlesztés alatt áll.")
    
    with col2:
        current_status = offer.get("status")
        if current_status not in ["ACCEPTED_BY_USER", "FINALIZED", "completed"]:
            if st.button("Ajánlat törlése", type="primary", use_container_width=True):
                # Törlés megerősítése
                confirm = st.checkbox("Erősítse meg a törlést!")
                
                if confirm:
                    try:
                        success, result = delete_offer(offer_id)
                        
                        if success:
                            show_operation_feedback("ajánlat törlése", True, result_message="Az ajánlat sikeresen törölve!")
                            # Cache frissítése
                            if "offer_cache" in st.session_state and offer_id in st.session_state.offer_cache:
                                del st.session_state.offer_cache[offer_id]
                            
                            # Visszatérés a listához
                            if "selected_offer_id" in st.session_state:
                                del st.session_state.selected_offer_id
                            st.rerun()
                        else:
                            show_error(f"Hiba az ajánlat törlésekor: {result}")
                    except Exception as e:
                        logger.error(f"Error deleting offer: {str(e)}")
                        show_error(f"Hiba az ajánlat törlésekor: {str(e)}")

def bulk_status_update(offer_ids, new_status):
    """
    Több ajánlat státuszának egyszerre történő frissítése.
    
    Args:
        offer_ids (list): Az ajánlat azonosítók listája
        new_status (str): Az új státusz kód
        
    Returns:
        dict: Eredmények szótára sikeres és sikertelen műveletekkel
    """
    results = {
        "success": [],
        "failed": []
    }
    
    for offer_id in offer_ids:
        try:
            success, result = update_offer_status(
                offer_id=offer_id,
                status=new_status,
                note=f"Tömeges státuszfrissítés: {new_status}"
            )
            
            if success:
                results["success"].append(offer_id)
                # Cache frissítése
                if "offer_cache" in st.session_state and offer_id in st.session_state.offer_cache:
                    st.session_state.offer_cache[offer_id]["status"] = new_status
            else:
                results["failed"].append({"id": offer_id, "error": result})
        except Exception as e:
            logger.error(f"Hiba a tömeges státuszfrissítéskor ajánlat #{offer_id}: {str(e)}")
            results["failed"].append({"id": offer_id, "error": str(e)})
    
    return results

def add_bulk_operation_buttons(offers):
    """
    Tömeges műveleti gombok megjelenítése több kiválasztott ajánlathoz.
    
    Args:
        offers (list): Az ajánlatok listája
    """
    if not offers:
        st.info("Nincsenek ajánlatok a tömeges műveletekhez.")
        return
    
    st.markdown("### Tömeges műveletek")
    
    # Ajánlatok többszörös kiválasztása
    offer_options = [f"#{o.get('id')} - {o.get('product_type', {}).get('name', 'Ismeretlen')} ({format_date(o.get('delivery_date', ''))})" for o in offers]
    
    # Kiválasztó widget - multi=True a többszörös kiválasztáshoz
    selected_indices = st.multiselect(
        "Válasszon ajánlatokat:",
        options=range(len(offer_options)),
        format_func=lambda i: offer_options[i],
        key="selected_offers_for_bulk_actions"
    )
    
    # Ha nincsenek kiválasztott ajánlatok, nem jelenítünk meg gombokat
    if not selected_indices:
        st.info("Válasszon ki legalább egy ajánlatot a tömeges műveletekhez.")
        return
    
    # Kiválasztott ajánlatok és azok azonosítói
    selected_offers = [offers[i] for i in selected_indices]
    selected_ids = [offer.get("id") for offer in selected_offers]
    
    # Státuszok ellenőrzése a lehetséges műveletek meghatározásához
    statuses = [offer.get("status") for offer in selected_offers]
    
    # Tömeges műveleti gombok oszlopokban
    st.write(f"**{len(selected_ids)} ajánlat kiválasztva**")
    
    # Státusz választó és műveleti gombok
    col1, col2 = st.columns(2)
    
    with col1:
        # Státusz opciók
        # Alap státusz opciók a backendben használt nevezéktan szerint
        if all(s in ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"] for s in statuses if s):
            status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
        else:
            # Modern státusz opciók
            status_options = ["pending", "approved", "rejected", "in_progress", "completed", "canceled"]
            
        # Státusz magyarázatok
        status_explanations = {
            "CREATED": "Létrehozva - Kezdeti állapot",
            "CONFIRMED_BY_COMPANY": "Megerősítve vállalat által",
            "ACCEPTED_BY_USER": "Elfogadva termelő által",
            "REJECTED_BY_USER": "Elutasítva termelő által",
            "FINALIZED": "Véglegesítve",
            "pending": "Függőben - Feldolgozásra vár",
            "approved": "Jóváhagyva - Elfogadva",
            "rejected": "Elutasítva",
            "in_progress": "Folyamatban",
            "completed": "Teljesítve",
            "canceled": "Törölve"
        }
        
        selected_status = st.selectbox(
            "Új státusz tömeges frissítéshez:",
            options=status_options,
            format_func=lambda x: f"{x} - {status_explanations.get(x, x)}",
            key="bulk_status_choice"
        )
        
        # Státusz megerősítés
        st.markdown(f"""
        <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>Kiválasztott új státusz:</strong> {selected_status}<br>
            <strong>Leírás:</strong> {status_explanations.get(selected_status, "-")}
        </div>
        """, unsafe_allow_html=True)
    
    # Művelet gombok
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Tömeges elfogadás gomb - csak olyan ajánlatokra, amelyek CONFIRMED_BY_COMPANY státuszban vannak
        can_accept = all(status == "CONFIRMED_BY_COMPANY" for status in statuses)
        if st.button("Tömeges elfogadás", disabled=not can_accept, use_container_width=True):
            if can_accept:
                # Megerősítés dialógus importálása
                try:
                    from .ui_components import open_confirmation_dialog, render_confirmation_dialog
                except ImportError:
                    from ui_components import open_confirmation_dialog, render_confirmation_dialog
                
                # Dialógus kulcs
                dialog_key = "bulk_accept_dialog"
                
                # Ha a dialógus még nem nyitott, megnyitjuk
                if f"{dialog_key}_open" not in st.session_state:
                    open_confirmation_dialog(
                        title="Ajánlatok tömeges elfogadása", 
                        message=f"Biztosan elfogadja mind a {len(selected_ids)} kiválasztott ajánlatot? Ez a művelet nem vonható vissza.",
                        confirm_text="Elfogadom",
                        cancel_text="Mégsem",
                        key=dialog_key
                    )
                
                # Dialógus renderelése és eredmény kezelése
                confirmed = render_confirmation_dialog(
                    title="Ajánlatok tömeges elfogadása", 
                    message=f"Biztosan elfogadja mind a {len(selected_ids)} kiválasztott ajánlatot? Ez a művelet nem vonható vissza.",
                    confirm_text="Elfogadom",
                    cancel_text="Mégsem",
                    key=dialog_key
                )
                
                if confirmed:
                    with st.spinner("Ajánlatok elfogadása folyamatban..."):
                        results = bulk_status_update(selected_ids, "ACCEPTED_BY_USER")
                        
                        # Eredmények megjelenítése
                        if results["success"]:
                            show_operation_feedback("tömeges státuszfrissítés", True, 
                                      result_message=f"{len(results['success'])} ajánlat sikeresen elfogadva!")
                        
                        if results["failed"]:
                            st.error(f"{len(results['failed'])} ajánlat elfogadása sikertelen volt.")
                            for failed in results["failed"]:
                                show_operation_feedback("tömeges művelet", False, 
                                                  error_message=f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                        
                        # Oldal újratöltése 1 másodperc késleltetéssel
                        import time
                        time.sleep(1)
                        st.rerun()
            else:
                from .ui_components import show_inline_warning
                show_inline_warning("Csak CONFIRMED_BY_COMPANY státuszú ajánlatokat fogadhat el tömegesen.")
    
    with col2:
        # Tömeges státuszfrissítés gomb
        if st.button("Státusz frissítése", key="bulk_update_btn", use_container_width=True):
            # Megerősítés dialógus importálása
            try:
                from .ui_components import open_confirmation_dialog, render_confirmation_dialog
            except ImportError:
                from ui_components import open_confirmation_dialog, render_confirmation_dialog
            
            # Dialógus kulcs
            dialog_key = "bulk_status_update_dialog"
            
            # Ha a dialógus még nem nyitott, megnyitjuk
            if f"{dialog_key}_open" not in st.session_state:
                open_confirmation_dialog(
                    title="Státuszok tömeges frissítése", 
                    message=f"Biztosan frissíti {len(selected_ids)} ajánlat státuszát erre: {selected_status}? Ez a művelet nem vonható vissza.",
                    confirm_text="Frissítés",
                    cancel_text="Mégsem",
                    key=dialog_key
                )
            
            # Dialógus renderelése és eredmény kezelése
            confirmed = render_confirmation_dialog(
                title="Státuszok tömeges frissítése", 
                message=f"Biztosan frissíti {len(selected_ids)} ajánlat státuszát erre: {selected_status}? Ez a művelet nem vonható vissza.",
                confirm_text="Frissítés",
                cancel_text="Mégsem",
                key=dialog_key
            )
            
            if confirmed:
                with st.spinner(f"{len(selected_ids)} ajánlat frissítése..."):
                    results = bulk_status_update(selected_ids, selected_status)
                    
                    # Eredmények megjelenítése
                    if results["success"]:
                        show_operation_feedback("tömeges státuszfrissítés", True, 
                                  result_message=f"{len(results['success'])} ajánlat sikeresen frissítve!")
                    
                    if results["failed"]:
                        st.error(f"{len(results['failed'])} ajánlat frissítése sikertelen volt.")
                        for failed in results["failed"]:
                            st.error(f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                    
                    # Oldal újratöltése 1 másodperc késleltetéssel
                    import time
                    time.sleep(1)
                    st.rerun()
    
    with col3:
        # Tömeges törlés gomb
        if st.button("Tömeges törlés", key="bulk_delete_btn", type="secondary", use_container_width=True):
            # Megerősítés dialógus importálása
            try:
                from .ui_components import open_confirmation_dialog, render_confirmation_dialog
            except ImportError:
                from ui_components import open_confirmation_dialog, render_confirmation_dialog
            
            # Dialógus kulcs
            dialog_key = "bulk_delete_dialog"
            
            # Ha a dialógus még nem nyitott, megnyitjuk
            if f"{dialog_key}_open" not in st.session_state:
                open_confirmation_dialog(
                    title="Ajánlatok tömeges törlése", 
                    message=f"Biztosan törli mind a {len(selected_ids)} kiválasztott ajánlatot? Ez a művelet nem vonható vissza!",
                    confirm_text="Törlés",
                    cancel_text="Mégsem",
                    icon="⚠️",
                    key=dialog_key
                )
            
            # Dialógus renderelése és eredmény kezelése
            confirmed = render_confirmation_dialog(
                title="Ajánlatok tömeges törlése", 
                message=f"Biztosan törli mind a {len(selected_ids)} kiválasztott ajánlatot? Ez a művelet nem vonható vissza!",
                confirm_text="Törlés",
                cancel_text="Mégsem",
                icon="⚠️",
                key=dialog_key
            )
            
            if confirmed:
                with st.spinner("Ajánlatok törlése folyamatban..."):
                    results = bulk_status_update(selected_ids, "canceled")
                    
                    # Eredmények megjelenítése
                    if results["success"]:
                        show_operation_feedback("tömeges törlés", True, 
                                  result_message=f"{len(results['success'])} ajánlat sikeresen törölve!")
                    
                    if results["failed"]:
                        st.error(f"{len(results['failed'])} ajánlat törlése sikertelen volt.")
                        for failed in results["failed"]:
                            st.error(f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                    
                    # Oldal újratöltése 1 másodperc késleltetéssel
                    import time
                    time.sleep(1)
                    st.rerun()
    
    # Exportálási lehetőségek
    st.markdown("#### Kiválasztott ajánlatok exportálása")
    
    # Exportálási funkció importálása
    try:
        from .data_processing import render_export_buttons
    except ImportError:
        from data_processing import render_export_buttons
    
    # Csak a kiválasztott ajánlatokat küldjük át az exportáláshoz
    render_export_buttons([offers[i] for i in selected_indices])