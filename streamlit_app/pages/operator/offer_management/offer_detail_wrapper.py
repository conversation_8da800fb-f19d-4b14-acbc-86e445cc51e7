"""
Wrapper modul az offer_detail.py s<PERSON><PERSON><PERSON><PERSON>, amely seg<PERSON>t meg<PERSON>i a relatív import problémákat.
"""
import os
import sys
import importlib
import logging

logger = logging.getLogger(__name__)

# Biztosítsuk, hogy ez a könyvtár a sys.path-ban van
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)
    logger.info(f"Added {current_dir} to sys.path")

# Most importáljuk közvetlenül az api_client modult
import api_client

# Ez a függvény az offer_detail.show_offer_detail függvény wrapperje
def show_offer_detail(offer_id):
    """
    Az offer_detail.show_offer_detail függvény biztonságos wrapper függvénye.
    
    Args:
        offer_id: Az ajánlat azonosítója
    """
    try:
        # Próbáljuk meg dinamikusan betölteni az offer_detail modult
        spec = importlib.util.spec_from_file_location(
            "offer_detail", 
            os.path.join(current_dir, "offer_detail.py")
        )
        offer_detail_module = importlib.util.module_from_spec(spec)
        
        # Adjuk hozzá a szükséges függvényeket a modulhoz még betöltés előtt
        # hogy elkerüljük a relatív import problémát
        offer_detail_module.get_offer_details = api_client.get_offer_details
        offer_detail_module.get_offer_logs = api_client.get_offer_logs
        offer_detail_module.update_offer_status = api_client.update_offer_status
        offer_detail_module.get_offer_attachments = api_client.get_offer_attachments
        offer_detail_module.get_related_offers = api_client.get_related_offers
        
        # Most töltsük be a modult
        spec.loader.exec_module(offer_detail_module)
        
        # Hívjuk meg a függvényt
        return offer_detail_module.show_offer_detail(offer_id)
    except Exception as e:
        import streamlit as st
        logger.error(f"Error in offer_detail_wrapper: {str(e)}")
        st.error(f"Hiba az ajánlat részletek megjelenítése során: {str(e)}")
        return None 