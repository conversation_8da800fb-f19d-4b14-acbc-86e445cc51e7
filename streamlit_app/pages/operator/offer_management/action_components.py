"""
Action component implementations based on the component_base architecture.

This module provides reusable action components that follow the ComponentBase architecture.
These components handle user interactions like buttons, menus, confirmation dialogs,
and other UI elements for performing actions on offers.

Backward compatibility is maintained with the previous implementation through adapter classes.
"""
import streamlit as st
import logging
from datetime import datetime
import uuid
from typing import Dict, Any, Optional, List, Callable, Union, Tuple, TypeVar, cast

# Import base component architecture
from .component_base import ComponentBase, CompositeComponent

logger = logging.getLogger(__name__)

class ActionBar(ComponentBase):
    """
    A unified action bar component for displaying primary actions in a sticky header.
    
    This component shows the most important actions in a fixed header that stays
    visible as the user scrolls.
    """
    
    def __init__(
        self,
        offer_id: int,
        offer_status: Optional[str] = None,
        permissions: Optional[Dict[str, bool]] = None,
        component_id: Optional[str] = None,
    ):
        """
        Initialize the action bar.
        
        Args:
            offer_id: The offer ID
            offer_status: The current status of the offer
            permissions: User permissions for the offer
            component_id: Optional unique ID for the component
        """
        props = {
            'offer_id': offer_id,
            'offer_status': offer_status,
            'permissions': permissions or {},
        }
        super().__init__(props, component_id)
        
        # Generate a unique key prefix for child components
        self._key_prefix = f"action_bar_{offer_id}_{str(uuid.uuid4())[:6]}"
    
    def render(
        self,
        on_back: Optional[Callable[[], None]] = None,
        on_status_change: Optional[Callable[[str, Optional[str], Optional[float], Optional[int]], None]] = None,
        on_edit: Optional[Callable[[], None]] = None,
        on_export: Optional[Callable[[str], None]] = None,
        on_more_actions: Optional[Callable[[str], None]] = None
    ) -> None:
        """
        Render the action bar.
        
        Args:
            on_back: Callback for back button
            on_status_change: Callback for status change
            on_edit: Callback for edit button
            on_export: Callback for export button
            on_more_actions: Callback for more actions button
        """
        # CSS for the sticky header
        st.markdown("""
        <style>
        .sticky-action-bar {
            position: sticky;
            top: 0;
            z-index: 999;
            background-color: white;
            padding: 10px 0;
            border-bottom: 1px solid #e6e6e6;
            margin-bottom: 20px;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Check for mobile view
        is_mobile = st.session_state.get('is_mobile', False)
        
        # Display header container
        st.markdown(f"<div class='sticky-action-bar'></div>", unsafe_allow_html=True)
        
        # Display buttons
        if is_mobile:
            # On mobile, use a 2x2 grid layout
            row1_cols = st.columns(2)
            row2_cols = st.columns(2)
            
            # First row
            with row1_cols[0]:
                self._render_back_button(on_back)
            with row1_cols[1]:
                self._render_status_button(on_status_change)
                
            # Second row
            with row2_cols[0]:
                self._render_edit_button(on_edit)
            with row2_cols[1]:
                self._render_more_actions_button(on_more_actions, on_export)
        else:
            # On desktop, use a single row layout
            cols = st.columns(5)
            
            with cols[0]:
                self._render_back_button(on_back)
            with cols[1]:
                self._render_status_button(on_status_change)
            with cols[2]:
                self._render_edit_button(on_edit)
            with cols[3]:
                self._render_export_button(on_export)
            with cols[4]:
                self._render_more_actions_button(on_more_actions)
    
    def _render_back_button(self, on_back: Optional[Callable[[], None]]) -> None:
        """
        Render the back button.
        
        Args:
            on_back: Callback function to be called when the back button is clicked
        """
        # Generate a unique key for the button
        unique_button_key = f"back_button_{self.props['offer_id']}_{self._key_prefix}"
        
        if st.button("← Back", key=unique_button_key):
            # Detailed logging for tracking
            logger.info(f"Back button clicked, offer_id: {self.props['offer_id']}")
            
            if on_back and callable(on_back):
                # If there's a custom callback, call it
                logger.info("Calling custom back button callback")
                on_back()
            else:
                # Default behavior: clear session state variables and navigate back
                # IMPORTANT: First set the "go_back" flag so offer_management.py detects it
                st.session_state["go_back_to_offer_list"] = True
                logger.info("Set go_back_to_offer_list flag to True")
                
                # Clear all relevant session state variables
                keys_to_delete = []
                
                # Collect all items to delete
                # CRITICAL: Don't delete selected_offer_id yet, that will be handled by the main page
                for key in list(st.session_state.keys()):
                    if (key.startswith(f"status_change_") or
                        key.startswith(f"show_status_dialog_") or
                        key.startswith(f"new_status_") or
                        key == "fixed_quantity_value" or
                        key == "fixed_price_value" or
                        key == "quantity_input_field" or 
                        key == "price_input_field"):
                        keys_to_delete.append(key)
                
                # Delete the collected keys
                for key in keys_to_delete:
                    try:
                        del st.session_state[key]
                        logger.info(f"Deleted session state key: {key}")
                    except Exception as e:
                        logger.warning(f"Failed to delete session state key: {key}, error: {str(e)}")
                
                # Rerun to refresh the page
                logger.info("Triggering rerun after back button click")
                st.rerun()
    
    def _render_status_button(self, on_status_change: Optional[Callable[[str, Optional[str], Optional[float], Optional[int]], None]]) -> None:
        """
        Render the status change button.
        
        Args:
            on_status_change: Callback function to be called when the status is changed
        """
        # Check permissions
        can_change_status = self.props['permissions'].get("can_change_status", True)
        
        if not can_change_status:
            st.button("Status", disabled=True, key=f"{self._key_prefix}_status_disabled")
            return
            
        # Get status options based on current status
        status_options = self._get_next_status_options()
        
        if not status_options:
            st.button("Status", disabled=True, key=f"{self._key_prefix}_status_no_options")
            return
            
        # Status change button and dropdown
        status_button_key = f"{self._key_prefix}_status"
        
        # First item in dropdown is "Change Status...", which can't be selected
        all_options = ["Change Status..."] + status_options
        
        # The selectbox on_change parameter is called only after st.session_state changes
        # so we create a separate function to handle the change
        def handle_status_change():
            selected = st.session_state.get(status_button_key)
            if selected != all_options[0]:
                if on_status_change and callable(on_status_change):
                    on_status_change(selected, None, None, None)
                else:
                    # Default behavior: show status change dialog
                    st.session_state[f"show_status_dialog_{self.props['offer_id']}"] = True
                    st.session_state[f"new_status_{self.props['offer_id']}"] = selected
                    st.rerun()
        
        selected_status = st.selectbox(
            label="",
            options=all_options,
            key=status_button_key,
            label_visibility="collapsed",
            on_change=handle_status_change
        )
    
    def _render_edit_button(self, on_edit: Optional[Callable[[], None]]) -> None:
        """
        Render the edit button.
        
        Args:
            on_edit: Callback function to be called when the edit button is clicked
        """
        # Check permissions
        can_edit = self.props['permissions'].get("can_edit", True)
        
        if st.button("Edit", disabled=not can_edit, key=f"{self._key_prefix}_edit"):
            if on_edit and callable(on_edit):
                on_edit()
            else:
                # Default behavior: enable edit mode
                st.session_state[f"edit_mode_{self.props['offer_id']}"] = True
                st.rerun()
    
    def _render_export_button(self, on_export: Optional[Callable[[str], None]]) -> None:
        """
        Fixed export button with working functionality using button-based approach.
        
        Args:
            on_export: Callback function to be called when an export option is selected
        """
        export_key = f"{self._key_prefix}_export"
        
        # Use a button with popover instead of selectbox
        if st.button("📤 Export", key=f"{export_key}_btn"):
            st.session_state[f"{export_key}_show"] = not st.session_state.get(f"{export_key}_show", False)
        
        if st.session_state.get(f"{export_key}_show", False):
            with st.container():
                st.write("Válassz export formátumot:")
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    if st.button("📄 PDF", key=f"{export_key}_pdf"):
                        if on_export:
                            on_export("pdf")
                        else:
                            self._handle_export("pdf")
                        st.session_state[f"{export_key}_show"] = False
                        
                with col2:
                    if st.button("📊 Excel", key=f"{export_key}_excel"):
                        if on_export:
                            on_export("excel")
                        else:
                            self._handle_export("excel")
                        st.session_state[f"{export_key}_show"] = False
                            
                with col3:
                    if st.button("📋 CSV", key=f"{export_key}_csv"):
                        if on_export:
                            on_export("csv")
                        else:
                            self._handle_export("csv")
                        st.session_state[f"{export_key}_show"] = False
    
    def _handle_export(self, format: str):
        """Handle export action"""
        try:
            # Import export functions
            from .export_functions import export_offer_to_pdf, export_offer_to_excel, export_offer_to_csv
            
            offer_id = self.props['offer_id']
            
            # Get offer details
            from .api_client import get_offer_details
            success, offer_data = get_offer_details(offer_id)
            
            if success:
                if format == "pdf":
                    export_offer_to_pdf(offer_data)
                elif format == "excel":
                    export_offer_to_excel(offer_data)
                elif format == "csv":
                    export_offer_to_csv(offer_data)
                
                st.success(f"Sikeres exportálás: {format.upper()}")
            else:
                st.error(f"Hiba az exportálás során: {offer_data}")
                
        except Exception as e:
            st.error(f"Export hiba: {str(e)}")
    
    def _render_more_actions_button(
        self, 
        on_more_actions: Optional[Callable[[str], None]], 
        on_export: Optional[Callable[[str], None]] = None
    ) -> None:
        """
        Fixed more actions button with working functionality using button-based approach.
        
        Args:
            on_more_actions: Callback function to be called when an action is selected
            on_export: Optional callback function for export actions (used on mobile)
        """
        more_key = f"{self._key_prefix}_more"
        is_mobile = st.session_state.get('is_mobile', False)
        
        # Use a button with popover instead of selectbox
        if st.button("⚙️ More", key=f"{more_key}_btn"):
            st.session_state[f"{more_key}_show"] = not st.session_state.get(f"{more_key}_show", False)
        
        if st.session_state.get(f"{more_key}_show", False):
            with st.container():
                st.write("További műveletek:")
                
                # Standard actions
                if st.button("🖨️ Print", key=f"{more_key}_print"):
                    if on_more_actions:
                        on_more_actions("print")
                    else:
                        self._handle_more_action("print")
                    st.session_state[f"{more_key}_show"] = False
                
                if st.button("🗑️ Delete", key=f"{more_key}_delete"):
                    if on_more_actions:
                        on_more_actions("delete")
                    else:
                        self._handle_more_action("delete")
                    st.session_state[f"{more_key}_show"] = False
                
                if st.button("📧 Send Notification", key=f"{more_key}_notification"):
                    if on_more_actions:
                        on_more_actions("send_notification")
                    else:
                        self._handle_more_action("send_notification")
                    st.session_state[f"{more_key}_show"] = False
                
                # Export actions on mobile
                if is_mobile and on_export:
                    st.markdown("---")
                    st.write("Export:")
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        if st.button("📄 PDF", key=f"{more_key}_mobile_pdf"):
                            on_export("pdf")
                            st.session_state[f"{more_key}_show"] = False
                    
                    with col2:
                        if st.button("📊 Excel", key=f"{more_key}_mobile_excel"):
                            on_export("excel")
                            st.session_state[f"{more_key}_show"] = False
                    
                    with col3:
                        if st.button("📋 CSV", key=f"{more_key}_mobile_csv"):
                            on_export("csv")
                            st.session_state[f"{more_key}_show"] = False
    
    def _handle_more_action(self, action: str):
        """Handle more actions"""
        if action == "print":
            st.info("Nyomtatási nézet...")
            # TODO: Implement print view
        elif action == "delete":
            st.warning("Törlés megerősítés szükséges")
            # TODO: Implement delete confirmation
        elif action == "send_notification":
            st.info("Értesítés küldése...")
            # TODO: Implement notification sending
    
    def _get_next_status_options(self) -> List[str]:
        """
        Get the next possible statuses based on the current status.
        
        Returns:
            List of possible next statuses
        """
        # Status transitions definition according to API documentation
        # Flow: CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
        status_transitions = {
            "CREATED": ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"],
            "CONFIRMED_BY_COMPANY": ["ACCEPTED_BY_USER", "REJECTED_BY_USER"],
            "ACCEPTED_BY_USER": ["FINALIZED", "REJECTED_BY_USER"],
            "REJECTED_BY_USER": ["CREATED"],
            "FINALIZED": []  # Final status, no further steps
        }
        
        # Handle potential status display formatting
        # If the status doesn't exactly match an API status, try to determine it
        actual_status = self.props['offer_status']
        
        # If the status might be a formatted value from a status display
        if actual_status and actual_status not in status_transitions:
            # Map of possible status pairs
            status_display_map = {
                "Létrehozva": "CREATED",
                "LÉTREHOZVA": "CREATED",
                "Created": "CREATED",
                "Megerősítve": "CONFIRMED_BY_COMPANY",
                "Cég által visszaigazolva": "CONFIRMED_BY_COMPANY",
                "MEGERŐSÍTVE": "CONFIRMED_BY_COMPANY",
                "VISSZAIGAZOLVA": "CONFIRMED_BY_COMPANY",
                "Confirmed": "CONFIRMED_BY_COMPANY",
                "Elfogadva": "ACCEPTED_BY_USER",
                "ELFOGADVA": "ACCEPTED_BY_USER",
                "Accepted": "ACCEPTED_BY_USER",
                "Elutasítva": "REJECTED_BY_USER",
                "ELUTASÍTVA": "REJECTED_BY_USER",
                "Rejected": "REJECTED_BY_USER",
                "Teljesítve": "FINALIZED",
                "Véglegesítve": "FINALIZED",
                "TELJESÍTVE": "FINALIZED",
                "VÉGLEGESÍTVE": "FINALIZED",
                "Finalized": "FINALIZED"
            }
            
            # Try to convert the status back to API format
            if actual_status in status_display_map:
                actual_status = status_display_map[actual_status]
            else:
                # If mapping failed, log and return default options
                logger.warning(f"Failed to map status to API status: {self.props['offer_status']}")
                return ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"]
        
        # If there's no status or it's still unknown, use default options
        if not actual_status or actual_status not in status_transitions:
            return ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"]
            
        # Return the possible next statuses
        return status_transitions.get(actual_status, [])


class StatusTransitionModal(ComponentBase):
    """
    Status transition confirmation dialog component.
    
    This component displays a confirmation dialog for changing the status of an offer,
    with appropriate input fields for different status transitions.
    """
    
    def __init__(
        self, 
        offer_id: int, 
        current_status: str, 
        new_status: str, 
        offer_data: Optional[Dict[str, Any]] = None,
        component_id: Optional[str] = None
    ):
        """
        Initialize the status transition modal.
        
        Args:
            offer_id: The offer ID
            current_status: The current status of the offer
            new_status: The new status to transition to
            offer_data: Optional offer data, if available
            component_id: Optional unique ID for the component
        """
        props = {
            'offer_id': offer_id,
            'current_status': current_status,
            'new_status': new_status,
            'offer_data': offer_data or {},
        }
        super().__init__(props, component_id)
        
        # Store as instance variables for backward compatibility
        self.offer_id = offer_id
        self.current_status = current_status
        self.new_status = new_status
        self.offer_data = offer_data or {}
        
        # Generate a unique key prefix for child components
        self._key_prefix = f"status_change_{offer_id}_{str(uuid.uuid4())}"
    
    def render(self, on_confirm=None, on_cancel=None):
        """
        Megjeleníti a státuszváltás ablakot.
        
        Args:
            on_confirm (callable, optional): Megerősítés gomb callback függvénye. Defaults to None.
            on_cancel (callable, optional): Mégsem gomb callback függvénye. Defaults to None.
            
        Returns:
            tuple: (confirmed, note, confirmed_quantity, confirmed_price) - megerősítették-e a váltást, 
                   megjegyzés és a visszaigazolási adatok CONFIRMED_BY_COMPANY státusz esetén
        """
        try:
            # Try to import our HTML rendering module first
            try:
                from .html_rendering import safe_markdown
            except ImportError:
                try:
                    from html_rendering import safe_markdown
                except ImportError:
                    import html
                    def safe_markdown(text, is_html=False):
                        if not text:
                            return
                        if is_html:
                            st.markdown(text, unsafe_allow_html=True)
                        else:
                            escaped_text = html.escape(str(text))
                            st.markdown(escaped_text)
                
            # Try absolute import for formatting
            try:
                from streamlit_app.utils.formatting import format_status, format_price, format_quantity
            except ImportError:
                try:
                    # Try regular app-relative import
                    from utils.formatting import format_status, format_price, format_quantity
                except ImportError:
                    # Alapértelmezett formázás, ha nincs meg a modul
                    format_status = lambda s: s if s else "Ismeretlen"
                    format_price = lambda p: f"{p:,.0f} Ft" if p else "-"
                    format_quantity = lambda q: f"{q:,.2f}" if q else "-"
        
            st.markdown("### Státuszváltás megerősítése")
            st.markdown("Az ajánlat státusza változni fog:")
            
            # HTML escape a státusz neveknek
            import html
            
            # Bizonyosodjunk meg róla, hogy az eredeti API státuszokat használjuk
            # API státusz nevei: CREATED, CONFIRMED_BY_COMPANY, ACCEPTED_BY_USER, REJECTED_BY_USER, FINALIZED
            status_display_map = {
                "Létrehozva": "CREATED",
                "LÉTREHOZVA": "CREATED",
                "Megerősítve": "CONFIRMED_BY_COMPANY",
                "Cég által visszaigazolva": "CONFIRMED_BY_COMPANY",
                "MEGERŐSÍTVE": "CONFIRMED_BY_COMPANY",
                "VISSZAIGAZOLVA": "CONFIRMED_BY_COMPANY",
                "Elfogadva": "ACCEPTED_BY_USER",
                "ELFOGADVA": "ACCEPTED_BY_USER",
                "Elutasítva": "REJECTED_BY_USER",
                "ELUTASÍTVA": "REJECTED_BY_USER",
                "Teljesítve": "FINALIZED",
                "Véglegesítve": "FINALIZED",
                "TELJESÍTVE": "FINALIZED",
                "VÉGLEGESÍTVE": "FINALIZED"
            }
            
            # Ellenőrizzük, hogy a státusz értékek API formátumúak-e, ha nem, konvertáljuk
            # Ez biztosítja, hogy amikor a callback meghívódik, a helyes API státuszokkal dolgozunk
            if self.current_status in status_display_map:
                self.current_status = status_display_map[self.current_status]
            
            if self.new_status in status_display_map:
                self.new_status = status_display_map[self.new_status]
                
            # Ezt csak a megjelenítésre használjuk
            current_status_text = format_status(self.current_status)
            new_status_text = format_status(self.new_status)
            
            # Logoljuk a státuszokat a hibakereséshez
            logger.info(f"Státuszváltás kezdeményezése: {self.current_status} → {self.new_status}")
            logger.info(f"Megjelenített formátum: {current_status_text} → {new_status_text}")
            
            # Biztonságos megjelenítés
            safe_markdown(f"**{current_status_text}** → **{new_status_text}**")
            
            # Visszaigazolási adatok bekérése, ha CONFIRMED_BY_COMPANY státuszra váltunk
            confirmed_quantity = None
            confirmed_price = None
            
            if self.new_status == "CONFIRMED_BY_COMPANY":
                st.markdown("### Visszaigazolási adatok")
                st.markdown("Az alábbi adatok szükségesek a visszaigazoláshoz:")
                
                # Eredeti mennyiség és ár megjelenítése, ha rendelkezésre áll
                original_quantity = None
                original_price = None
                
                if self.offer_data:
                    original_quantity = self.offer_data.get('quantity_in_kg')
                    original_price = self.offer_data.get('price')
                    
                    if original_quantity is not None:
                        st.markdown(f"**Eredeti mennyiség:** {format_quantity(original_quantity)} kg")
                    if original_price is not None:
                        st.markdown(f"**Eredeti ár:** {format_price(original_price)}/kg")
                
                # Mennyiség bekérése - teljesen átdolgozva a session state kezelést
                # Globálisan egyedi session state kulcsok elkerülése
                fixed_quantity_key = "fixed_quantity_value"
                
                # Kezdeti értékek beállítása az eredeti mennyiségre
                # Ezt csak egyszer tesszük meg a form első megjelenítésekor
                if fixed_quantity_key not in st.session_state:
                    quantity_default = original_quantity if original_quantity is not None else 0
                    try:
                        st.session_state[fixed_quantity_key] = float(quantity_default)
                    except (ValueError, TypeError):
                        st.session_state[fixed_quantity_key] = 0.0
                
                # Az aktuális értéket használjuk a megjelenítéshez
                confirmed_quantity = st.number_input(
                    "Visszaigazolt mennyiség (kg)",
                    min_value=0.0,
                    value=st.session_state[fixed_quantity_key],
                    step=10.0,
                    format="%.2f",
                    key="quantity_input_field"
                )
                
                # Frissítjük a session state értéket közvetlenül a number_input eredménye alapján
                st.session_state[fixed_quantity_key] = confirmed_quantity
                
                # Ár bekérése - hasonlóan átdolgozva
                fixed_price_key = "fixed_price_value"
                
                # Kezdeti értékek beállítása az eredeti árra
                if fixed_price_key not in st.session_state:
                    price_default = original_price if original_price is not None else 100
                    try:
                        st.session_state[fixed_price_key] = int(price_default) if price_default else 100
                    except (ValueError, TypeError):
                        st.session_state[fixed_price_key] = 100
                
                # Az aktuális értéket használjuk a megjelenítéshez
                confirmed_price = st.number_input(
                    "Visszaigazolt ár (Ft/kg)",
                    min_value=0,
                    value=st.session_state[fixed_price_key],
                    step=10,
                    key="price_input_field"
                )
                
                # Frissítjük a session state értéket közvetlenül a number_input eredménye alapján
                st.session_state[fixed_price_key] = confirmed_price
                
                # Összérték kiszámítása
                if confirmed_quantity and confirmed_price:
                    total_value = confirmed_quantity * confirmed_price
                    st.metric("Összérték", f"{format_price(total_value)}")
            
            # Megjegyzés bekérése - egyedi kulcsot használunk
            note_key = f"{self._key_prefix}_note"
            note = st.text_area("Megjegyzés (opcionális)", key=note_key)
            
            # Callback függvények a gombokhoz
            def confirm_callback():
                if on_confirm and callable(on_confirm):
                    # Ha cég általi megerősítés, akkor visszaadjuk a mennyiséget és árat is
                    if self.new_status == "CONFIRMED_BY_COMPANY":
                        # Használjuk a fix session state kulcsokat
                        on_confirm(
                            self.new_status, 
                            st.session_state.get(note_key, ""),
                            st.session_state.get("fixed_quantity_value", confirmed_quantity),
                            st.session_state.get("fixed_price_value", confirmed_price)
                        )
                    else:
                        # Egyéb státuszoknál csak az alap paramétereket adjuk vissza
                        on_confirm(self.new_status, st.session_state.get(note_key, ""))
            
            def cancel_callback():
                if on_cancel and callable(on_cancel):
                    on_cancel()
            
            # Gombok - egyedi kulcsokat használunk
            col1, col2 = st.columns(2)
            
            confirmed = False
            with col1:
                confirm_button_key = f"{self._key_prefix}_confirm"
                # A gomb egyedi kulcsot kap a konkurencia elkerülésére
                if st.button("Megerősítés", key=confirm_button_key, type="primary", on_click=confirm_callback):
                    confirmed = True
            
            with col2:
                cancel_button_key = f"{self._key_prefix}_cancel"
                st.button("Mégsem", key=cancel_button_key, on_click=cancel_callback)
            
            # Visszaadjuk az összes szükséges adatot
            return confirmed, note, confirmed_quantity, confirmed_price
            
        except Exception as e:
            logging.error(f"Hiba a státuszváltás ablak megjelenítésekor: {str(e)}")
            st.error(f"Nem sikerült megjeleníteni a státuszváltás ablakot: {str(e)}")
            return False, "", None, None


class ConfirmationModal:
    """
    Általános megerősítő ablak komponens.
    
    A komponens megjeleníti a műveletek megerősítő ablakát.
    """
    
    def __init__(self, title, question, action_name="Megerősítés", danger=False):
        """
        Inicializálja a megerősítő ablakot.
        
        Args:
            title (str): Az ablak címe
            question (str): A megerősítendő kérdés
            action_name (str, optional): A megerősítő gomb szövege. Defaults to "Megerősítés".
            danger (bool, optional): Veszélyes művelet-e. Defaults to False.
        """
        self.title = title
        self.question = question
        self.action_name = action_name
        self.danger = danger
        self.key_prefix = f"confirm_{str(uuid.uuid4())[:8]}"
    
    def render(self, on_confirm=None, on_cancel=None):
        """
        Megjeleníti a megerősítő ablakot.
        
        Args:
            on_confirm (callable, optional): Megerősítés callback függvénye. Defaults to None.
            on_cancel (callable, optional): Mégsem callback függvénye. Defaults to None.
            
        Returns:
            bool: True ha megerősítették, False egyébként
        """
        st.markdown(f"### {self.title}")
        st.markdown(self.question)
        
        # Gombok
        col1, col2 = st.columns(2)
        
        confirmed = False
        with col1:
            # Veszélyes műveletnél extra kiemelés
            if self.danger:
                st.markdown(
                    f"""
                    <style>
                    div[data-testid="stButton"] button[kind="primary"]:has(div:contains("{self.action_name}")) {{
                        background-color: #d9534f;
                        color: white;
                    }}
                    </style>
                    """,
                    unsafe_allow_html=True
                )
            
            if st.button(self.action_name, key=f"{self.key_prefix}_confirm"):
                if on_confirm and callable(on_confirm):
                    on_confirm()
                confirmed = True
        
        with col2:
            if st.button("Mégsem", key=f"{self.key_prefix}_cancel"):
                if on_cancel and callable(on_cancel):
                    on_cancel()
        
        return confirmed 