"""
A<PERSON><PERSON>lat részletek megjelenítése.
Az új komponenseket használó ajánlat részletező oldal.
"""
import streamlit as st
import logging
from datetime import datetime, timedelta

# Rugalmas importálás - először megpróbáljuk a Docker-kompatibilis útvonalról importálni
try:
    # Docker környezetben /app a gyökér, nem /streamlit_app
    from pages.operator.offer_management.detail_components import (
        DetailContainer, 
        StatusIndicator, 
        EntityCard, 
        Timeline, 
        ActivityLog
    )
    from pages.operator.offer_management.action_components import (
        ActionBar,
        StatusTransitionModal,
        ConfirmationModal
    )
except ImportError:
    # Ha nem működik, próbáljuk meg közvetlenül importálni
    try:
        from detail_components import (
            DetailContainer, 
            StatusIndicator, 
            EntityCard, 
            Timeline, 
            ActivityLog
        )
        from action_components import (
            ActionBar,
            StatusTransitionModal,
            ConfirmationModal
        )
    except ImportError:
        # Ha még mindig nem működik, használunk relatív importot
        logging.warning("Using fallback relative imports for detail_components and action_components")
        from .detail_components import (
            DetailContainer, 
            StatusIndicator, 
            EntityCard, 
            Timeline, 
            ActivityLog
        )
        from .action_components import (
            ActionBar,
            StatusTransitionModal,
            ConfirmationModal
        )

# Rugalmas importálás a API klienshez is - Docker-kompatibilis sorrendben
try:
    # Docker környezetben ez a helyes út
    from pages.operator.offer_management.api_client import (
        get_offer_details,
        get_offer_logs,
        update_offer_status,
        get_offer_attachments,
        get_related_offers
    )
    logging.info("Successfully imported API client from Docker path (pages)")
except ImportError:
    try:
        # Közvetlenül az aktuális könyvtárból
        from api_client import (
            get_offer_details,
            get_offer_logs,
            update_offer_status,
            get_offer_attachments,
            get_related_offers
        )
        logging.info("Successfully imported API client directly")
    except ImportError:
        try:
            # Streamlit app relatív útvonal (fejlesztői környezetben)
            from streamlit_app.pages.operator.offer_management.api_client import (
                get_offer_details,
                get_offer_logs,
                update_offer_status,
                get_offer_attachments,
                get_related_offers
            )
            logging.info("Successfully imported API client from streamlit_app path")
        except ImportError:
            try:
                # Relatív import, ha csomagként van használva
                from .api_client import (
                    get_offer_details,
                    get_offer_logs,
                    update_offer_status,
                    get_offer_attachments,
                    get_related_offers
                )
                logging.info("Successfully imported API client using relative import")
            except ImportError:
                # Végső fallback: sys.path manipuláció
                logging.warning("Using sys.path manipulation for api_client import")
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.append(current_dir)
                from api_client import (
                    get_offer_details,
                    get_offer_logs,
                    update_offer_status,
                    get_offer_attachments,
                    get_related_offers
                )

# Formázó függvények importálása - Docker-kompatibilis sorrendben
try:
    # Docker környezetben ez a helyes út
    from pages.utils.formatting import (
        format_status, 
        format_datetime, 
        format_date, 
        format_price, 
        format_quantity
    )
except ImportError:
    try:
        # Közvetlenül
        from utils.formatting import (
            format_status, 
            format_datetime, 
            format_date, 
            format_price, 
            format_quantity
        )
    except ImportError:
        try:
            # Streamlit app relatív útvonal
            from streamlit_app.utils.formatting import (
                format_status, 
                format_datetime, 
                format_date, 
                format_price, 
                format_quantity
            )
        except ImportError:
            # Ha nem találjuk a formázó függvényeket, alapértelmezett implementáció
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

logger = logging.getLogger(__name__)

def show_offer_detail(offer_id):
    """
    Ajánlat részleteinek megjelenítése.
    
    Újratervezett, strukturált megjelenítés az ajánlat részleteiről.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    logger.info(f"Ajánlat részletek megjelenítése: #{offer_id}")
    
    # Mobilnézet ellenőrzése
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    
    # Ajánlat adatok betöltése
    offer = _load_offer_details(offer_id)
    
    if not offer:
        st.error(f"Az ajánlat (#{offer_id}) nem található vagy hiba történt a betöltés során.")
        if st.button("Vissza"):
            if "selected_offer_id" in st.session_state:
                del st.session_state.selected_offer_id
            st.rerun()
        return
    
    # Inicializáljuk a session state-et a betöltési állapotokhoz
    for key in ['loading_logs', 'loading_attachments', 'loading_related']:
        if key not in st.session_state:
            st.session_state[key] = False
    
    # Kezelés, ha státuszváltást kértek
    _handle_status_change(offer_id, offer['status'])
    
    # Műveleti sáv megjelenítése
    _render_action_bar(offer_id, offer['status'])
    
    # Ajánlat fejléc információk megjelenítése
    _render_offer_header(offer)
    
    # Panel elrendezés a képernyőméret függvényében
    if is_mobile:
        # Mobilon egymás alatt jelennek meg a panelek
        _render_basic_info_panel(offer)
        _render_timeline_panel(offer)
        _render_product_panel(offer)
        _render_related_entities_panel(offer)
        _render_activities_panel(offer_id)
    elif is_tablet:
        # Tableten 2 oszlopos elrendezés
        col1, col2 = st.columns(2)
        
        with col1:
            _render_basic_info_panel(offer)
            _render_product_panel(offer)
            _render_activities_panel(offer_id)
        
        with col2:
            _render_timeline_panel(offer)
            _render_related_entities_panel(offer)
    else:
        # Asztali nézeten 3 oszlopos elrendezés
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col1:
            _render_basic_info_panel(offer)
            _render_product_panel(offer)
        
        with col2:
            _render_timeline_panel(offer)
            _render_related_entities_panel(offer)
        
        with col3:
            _render_activities_panel(offer_id)


def _load_offer_details(offer_id):
    """
    Ajánlat részletek betöltése az API-n keresztül.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        dict: Az ajánlat adatai vagy None hiba esetén
    """
    try:
        success, result = get_offer_details(offer_id)
        if success:
            return result
        else:
            logger.error(f"Hiba az ajánlat betöltése során: {result}")
            st.error(f"Nem sikerült betölteni az ajánlatot: {result}")
            return None
    except Exception as e:
        logger.exception(f"Kivétel az ajánlat betöltése során: {str(e)}")
        st.error(f"Hiba történt az ajánlat betöltése során: {str(e)}")
        return None


def _handle_status_change(offer_id, current_status):
    """
    Státuszváltás kezelése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        current_status (str): Az ajánlat jelenlegi státusza
    """
    # Ellenőrizzük, hogy van-e függőben lévő státuszváltás
    show_dialog_key = f"show_status_dialog_{offer_id}"
    new_status_key = f"new_status_{offer_id}"
    
    if st.session_state.get(show_dialog_key, False):
        new_status = st.session_state.get(new_status_key)
        
        # Ha van új státusz, megjelenítjük a megerősítő ablakot
        if new_status:
            st.info("Kérjük, erősítsd meg a státuszváltást:")
            
            # Megerősítő ablak
            status_modal = StatusTransitionModal(offer_id, current_status, new_status)
            confirmed, note = status_modal.render(
                on_confirm=lambda status, note: _confirm_status_change(offer_id, status, note),
                on_cancel=lambda: _cancel_status_change(offer_id)
            )
        else:
            # Ha nincs új státusz, töröljük a dialógus állapotot
            del st.session_state[show_dialog_key]
            if new_status_key in st.session_state:
                del st.session_state[new_status_key]


def _confirm_status_change(offer_id, new_status, note):
    """
    Státuszváltás megerősítése és végrehajtása.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        new_status (str): Az új státusz
        note (str): Megjegyzés a státuszváltáshoz
    """
    try:
        # API hívás a státusz frissítéséhez
        success, result = update_offer_status(offer_id, new_status, note)
        
        if success:
            st.success(f"A státusz sikeresen módosítva: {format_status(new_status)}")
            # Töröljük a dialógus állapotot
            dialog_key = f"show_status_dialog_{offer_id}"
            status_key = f"new_status_{offer_id}"
            if dialog_key in st.session_state:
                del st.session_state[dialog_key]
            if status_key in st.session_state:
                del st.session_state[status_key]
            # Újratöltjük az oldalt
            st.rerun()
        else:
            st.error(f"Hiba a státusz módosítása során: {result}")
    except Exception as e:
        logger.exception(f"Kivétel a státusz módosítása során: {str(e)}")
        st.error(f"Hiba történt a státusz módosítása során: {str(e)}")


def _cancel_status_change(offer_id):
    """
    Státuszváltás megszakítása.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Töröljük a dialógus állapotot
    dialog_key = f"show_status_dialog_{offer_id}"
    status_key = f"new_status_{offer_id}"
    if dialog_key in st.session_state:
        del st.session_state[dialog_key]
    if status_key in st.session_state:
        del st.session_state[status_key]
    # Újratöltjük az oldalt
    st.rerun()


def _render_action_bar(offer_id, status):
    """
    Műveleti sáv megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        status (str): Az ajánlat státusza
    """
    # Jogosultságok beállítása - ezeket valós rendszerben az API-tól kell lekérni
    permissions = {
        "can_edit": status not in ["FINALIZED", "REJECTED_BY_USER"],
        "can_change_status": True,
        "can_delete": status != "FINALIZED"
    }
    
    # ActionBar komponens inicializálása és megjelenítése
    action_bar = ActionBar(offer_id, status, permissions)
    action_bar.render()


def _render_offer_header(offer):
    """
    Ajánlat fejléc információk megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Ajánlat azonosító és státusz
    st.markdown(f"## Ajánlat #{offer.get('id', '-')}")
    
    # Státusz indikátor
    StatusIndicator(
        status=offer.get('status', '-'),
        timestamp=offer.get('status_changed_at') or offer.get('updated_at') or offer.get('created_at'),
        description=offer.get('status_note')
    ).render()


def _render_basic_info_panel(offer):
    """
    Alapadatok panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    basic_info_container = DetailContainer(
        title="Alapadatok",
        icon="📄",
        expandable=True,
        expanded=True,
        color="#3584e4"
    )
    
    # Panel megjelenítése
    basic_info_container.render(lambda: _render_basic_info_content(offer))


def _render_basic_info_content(offer):
    """
    Alapadatok panel tartalmának megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Mobilnézet ellenőrzése
    is_mobile = st.session_state.get('is_mobile', False)
    
    # Termelő adatok
    producer = offer.get('producer', {})
    
    if is_mobile:
        # Mobilon egymás alatt jelennek meg az adatok
        st.markdown(f"**Termelő:** {producer.get('contact_name', '-')}")
        if producer.get('company_name'):
            st.markdown(f"**Cég:** {producer.get('company_name', '-')}")
        
        st.markdown(f"**Termék:** {offer.get('product_type', {}).get('name', '-')}")
        st.markdown(f"**Mennyiség:** {format_quantity(offer.get('quantity_in_kg', '-'))} kg")
        
        st.markdown(f"**Létrehozás:** {format_datetime(offer.get('created_at', '-'))}")
        st.markdown(f"**Beszállítás:** {format_date(offer.get('delivery_date', '-'))}")
        
        if offer.get('price') is not None:
            st.markdown(f"**Ár:** {format_price(offer.get('price', '-'))}")
        
        if offer.get('notes'):
            st.markdown("**Megjegyzés:**")
            st.markdown(f"*{offer.get('notes', '')}*")
    else:
        # Asztali nézeten két oszlopban jelennek meg az adatok
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Termelő:**")
            st.markdown("**Cég:**")
            st.markdown("**Termék:**")
            st.markdown("**Mennyiség:**")
            
            if offer.get('price') is not None:
                st.markdown("**Ár:**")
        
        with col2:
            st.markdown(f"{producer.get('contact_name', '-')}")
            st.markdown(f"{producer.get('company_name', '-')}")
            st.markdown(f"{offer.get('product_type', {}).get('name', '-')}")
            st.markdown(f"{format_quantity(offer.get('quantity_in_kg', '-'))} kg")
            
            if offer.get('price') is not None:
                st.markdown(f"{format_price(offer.get('price', '-'))}")
        
        # Új sor a dátumokkal
        st.markdown("---")
        date_cols = st.columns(2)
        
        with date_cols[0]:
            st.markdown("**Létrehozás:**")
            st.markdown("**Beszállítás:**")
        
        with date_cols[1]:
            st.markdown(f"{format_datetime(offer.get('created_at', '-'))}")
            st.markdown(f"{format_date(offer.get('delivery_date', '-'))}")
        
        # Megjegyzés külön blokkban
        if offer.get('notes'):
            st.markdown("---")
            st.markdown("**Megjegyzés:**")
            st.markdown(f"*{offer.get('notes', '')}*")


def _render_timeline_panel(offer):
    """
    Idősor panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    timeline_container = DetailContainer(
        title="Idősor",
        icon="🕒",
        expandable=True,
        expanded=True,
        color="#33a02c"
    )
    
    # Idősor események összeállítása
    events = _create_timeline_events(offer)
    
    # Panel megjelenítése
    timeline_container.render(lambda: Timeline(events).render())


def _create_timeline_events(offer):
    """
    Idősor események összeállítása az ajánlat adataiból.
    
    Args:
        offer (dict): Az ajánlat adatai
        
    Returns:
        list: Idősor események listája
    """
    events = []
    
    # Létrehozás dátuma
    if offer.get('created_at'):
        events.append({
            "date": offer.get('created_at'),
            "label": "Létrehozva",
            "description": f"Létrehozta: {offer.get('creator', {}).get('contact_name', 'Rendszer')}"
        })
    
    # Megerősítés dátuma
    if offer.get('confirmed_at'):
        events.append({
            "date": offer.get('confirmed_at'),
            "label": "Megerősítve",
            "description": None
        })
    
    # Elfogadás dátuma
    if offer.get('accepted_at'):
        events.append({
            "date": offer.get('accepted_at'),
            "label": "Elfogadva",
            "description": None
        })
    
    # Elutasítás dátuma
    if offer.get('rejected_at'):
        events.append({
            "date": offer.get('rejected_at'),
            "label": "Elutasítva",
            "description": offer.get('rejection_reason', None)
        })
    
    # Véglegesítés dátuma
    if offer.get('finalized_at'):
        events.append({
            "date": offer.get('finalized_at'),
            "label": "Véglegesítve",
            "description": None
        })
    
    # Beszállítás dátuma
    if offer.get('delivery_date'):
        # Ellenőrizzük, hogy ez a jövőben van-e
        is_future = (
            isinstance(offer.get('delivery_date'), datetime) and 
            offer.get('delivery_date') > datetime.now()
        )
        
        events.append({
            "date": offer.get('delivery_date'),
            "label": "Beszállítás",
            "description": "Tervezett" if is_future else None
        })
    
    return events


def _render_product_panel(offer):
    """
    Termék panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    product_container = DetailContainer(
        title="Termék adatok",
        icon="🥦",
        expandable=True,
        expanded=True,
        color="#e66100"
    )
    
    # Panel megjelenítése
    product_container.render(lambda: _render_product_content(offer))


def _render_product_content(offer):
    """
    Termék panel tartalmának megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Termék adatok
    product = offer.get('product_type', {})
    
    # Entitás kártya használata
    product_card = EntityCard(
        title="",  # Nincs külön cím a kártyának
        data=product,
        entity_type="product"
    )
    product_card.render()
    
    # Termék részletek táblázat
    st.markdown("### Termék adatok")
    
    # Termék mennyiség és ár
    cols = st.columns(3)
    with cols[0]:
        st.metric(
            label="Mennyiség", 
            value=f"{format_quantity(offer.get('quantity_in_kg', 0))} kg"
        )
    with cols[1]:
        if offer.get('price') is not None:
            st.metric(
                label="Egységár", 
                value=f"{format_price(offer.get('price', 0))}/kg"
            )
    with cols[2]:
        if offer.get('price') is not None and offer.get('quantity_in_kg') is not None:
            total_price = offer.get('price', 0) * offer.get('quantity_in_kg', 0)
            st.metric(
                label="Összérték", 
                value=f"{format_price(total_price)}"
            )
    
    # Termék minőségi paraméterek, ha vannak
    if offer.get('quality_parameters'):
        st.markdown("---")
        st.markdown("### Minőségi paraméterek")
        
        params = offer.get('quality_parameters', {})
        if isinstance(params, dict) and params:
            for name, value in params.items():
                st.markdown(f"**{name}:** {value}")
        else:
            st.info("Nincsenek minőségi paraméterek megadva.")


def _render_related_entities_panel(offer):
    """
    Kapcsolódó entitások panel megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Konténer inicializálása
    related_container = DetailContainer(
        title="Kapcsolódó entitások",
        icon="🔗",
        expandable=True,
        expanded=True,
        color="#5e35b1"
    )
    
    # Panel megjelenítése
    related_container.render(lambda: _render_related_entities_content(offer))


def _render_related_entities_content(offer):
    """
    Kapcsolódó entitások panel tartalmának megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Tab-ok létrehozása: Termelő, Termék, Kapcsolódó ajánlatok
    tabs = st.tabs(["Termelő", "Termék részletek", "Kapcsolódó ajánlatok"])
    
    # Termelő tab
    with tabs[0]:
        producer = offer.get('producer', {})
        if producer:
            EntityCard(
                title="Termelő adatok",
                data=producer,
                entity_type="producer"
            ).render()
        else:
            st.info("Nincs termelő adat.")
    
    # Termék részletek tab
    with tabs[1]:
        product = offer.get('product_type', {})
        if product:
            st.markdown("#### Termék részletek")
            
            # További termék részletek
            st.markdown(f"**Kategória:** {product.get('category', {}).get('name', '-')}")
            
            if product.get('description'):
                st.markdown("**Leírás:**")
                st.markdown(product.get('description', ''))
            
            if product.get('growing_parameters'):
                st.markdown("**Termesztési paraméterek:**")
                params = product.get('growing_parameters', {})
                for name, value in params.items():
                    st.markdown(f"- **{name}:** {value}")
        else:
            st.info("Nincs termék részlet adat.")
    
    # Kapcsolódó ajánlatok tab
    with tabs[2]:
        # Betöltési állapot beállítása
        loading_key = 'loading_related'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Kapcsolódó ajánlatok betöltése...")
        else:
            # Kapcsolódó ajánlatok betöltése
            try:
                st.session_state[loading_key] = True
                success, related_offers = get_related_offers(offer.get('id'))
                
                if success and related_offers:
                    for related in related_offers:
                        EntityCard(
                            title=f"Ajánlat #{related.get('id')}",
                            data=related,
                            entity_type="related_offer"
                        ).render()
                        st.markdown("---")
                else:
                    st.info("Nincsenek kapcsolódó ajánlatok.")
            except Exception as e:
                logger.exception(f"Hiba a kapcsolódó ajánlatok betöltése során: {str(e)}")
                st.error(f"Hiba történt a kapcsolódó ajánlatok betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False


def _render_activities_panel(offer_id):
    """
    Tevékenységek panel megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Konténer inicializálása
    activities_container = DetailContainer(
        title="Tevékenységek",
        icon="🔔",
        expandable=True,
        expanded=True,
        color="#9c27b0"
    )
    
    # Panel megjelenítése
    activities_container.render(lambda: _render_activities_content(offer_id))


def _render_activities_content(offer_id):
    """
    Tevékenységek panel tartalmának megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Tab-ok létrehozása: Státusztörténet, Csatolmányok, Audit napló
    tabs = st.tabs(["Státusztörténet", "Csatolmányok", "Audit napló"])
    
    # Státusztörténet tab
    with tabs[0]:
        # Betöltési állapot beállítása
        loading_key = 'loading_logs'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Státusztörténet betöltése...")
        else:
            # Státuszváltozások betöltése
            try:
                st.session_state[loading_key] = True
                success, logs = get_offer_logs(offer_id, log_type="status")
                
                if success and logs:
                    ActivityLog(logs, log_type="status").render()
                else:
                    st.info("Nincs státusztörténet adat.")
            except Exception as e:
                logger.exception(f"Hiba a státusztörténet betöltése során: {str(e)}")
                st.error(f"Hiba történt a státusztörténet betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False
    
    # Csatolmányok tab
    with tabs[1]:
        # Betöltési állapot beállítása
        loading_key = 'loading_attachments'
        if loading_key not in st.session_state:
            st.session_state[loading_key] = False
            
        if st.session_state[loading_key]:
            st.info("Csatolmányok betöltése...")
        else:
            # Csatolmányok betöltése
            try:
                st.session_state[loading_key] = True
                success, attachments = get_offer_attachments(offer_id)
                
                if success and attachments:
                    ActivityLog(attachments, log_type="attachment").render()
                else:
                    st.info("Nincsenek csatolmányok.")
                    
                # Új csatolmány feltöltése gomb
                st.markdown("---")
                st.file_uploader("Új csatolmány feltöltése", key=f"attachment_upload_{offer_id}")
                
            except Exception as e:
                logger.exception(f"Hiba a csatolmányok betöltése során: {str(e)}")
                st.error(f"Hiba történt a csatolmányok betöltése során: {str(e)}")
            finally:
                st.session_state[loading_key] = False
    
    # Audit napló tab
    with tabs[2]:
        # Audit napló betöltése
        try:
            success, logs = get_offer_logs(offer_id, log_type="audit")
            
            if success and logs:
                ActivityLog(logs, log_type="audit").render()
            else:
                st.info("Nincs audit napló adat.")
        except Exception as e:
            logger.exception(f"Hiba az audit napló betöltése során: {str(e)}")
            st.error(f"Hiba történt az audit napló betöltése során: {str(e)}") 