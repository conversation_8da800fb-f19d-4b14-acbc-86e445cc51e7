#!/usr/bin/env python3
"""
Test script for user-specific default filter functionality.
"""
import streamlit as st
import logging
from datetime import datetime, date
import uuid
import os
import sys

# Add parent directory to path for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import modules
try:
    # First try import with local path
    from filter_persistence import (
        extract_current_filters,
        apply_saved_filter,
        check_and_apply_default_filter,
        get_saved_filters
    )
    from user_preferences import (
        get_default_filter_for_user,
        set_default_filter_for_user,
        clear_default_filter_for_user,
        get_current_user
    )
    from enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    # If that fails, try with app-relative path
    try:
        from pages.operator.offer_management.filter_persistence import (
            extract_current_filters,
            apply_saved_filter,
            check_and_apply_default_filter,
            get_saved_filters
        )
        from pages.operator.offer_management.user_preferences import (
            get_default_filter_for_user,
            set_default_filter_for_user,
            clear_default_filter_for_user,
            get_current_user
        )
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
    except ImportError:
        # Final fallback to full paths
        from streamlit_app.pages.operator.offer_management.filter_persistence import (
            extract_current_filters,
            apply_saved_filter,
            check_and_apply_default_filter,
            get_saved_filters
        )
        from streamlit_app.pages.operator.offer_management.user_preferences import (
            get_default_filter_for_user,
            set_default_filter_for_user,
            clear_default_filter_for_user,
            get_current_user
        )
        from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def main():
    st.set_page_config(page_title="User Default Filter Test", layout="wide")
    
    st.title("User Default Filter Functionality Test")
    st.markdown("""
    Tesztalkalmazás a felhasználónként alapértelmezett szűrő funkció teszteléséhez.
    """)
    
    # Initialize session state for testing
    if "user_id" not in st.session_state:
        st.session_state["user_id"] = "test_user_123"
        
    if "user_role" not in st.session_state:
        st.session_state["user_role"] = "operator"
        
    if "producer_filter_om" not in st.session_state:
        st.session_state["producer_filter_om"] = None
        
    if "product_filter_om" not in st.session_state:
        st.session_state["product_filter_om"] = None
        
    if "status_filter_om" not in st.session_state:
        st.session_state["status_filter_om"] = None
        
    if "from_date_filter_om" not in st.session_state:
        st.session_state["from_date_filter_om"] = date.today().replace(day=1)
        
    if "to_date_filter_om" not in st.session_state:
        st.session_state["to_date_filter_om"] = date.today()
    
    # Create two columns for the test UI
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Current User")
        user = get_current_user()
        if user:
            st.write(f"User ID: {user['id']}")
            st.write(f"User Role: {user['role']}")
        else:
            st.error("No user logged in")
            
        st.markdown("### Session State Filters")
        st.json({
            "producer_filter_om": str(st.session_state.get("producer_filter_om")),
            "product_filter_om": str(st.session_state.get("product_filter_om")),
            "status_filter_om": str(st.session_state.get("status_filter_om")),
            "from_date_filter_om": str(st.session_state.get("from_date_filter_om")),
            "to_date_filter_om": str(st.session_state.get("to_date_filter_om"))
        })
        
        st.markdown("### Current Filters Extracted")
        extracted_filters = extract_current_filters()
        st.json(extracted_filters)
        
        st.markdown("### Actions")
        
        # Create mock filter
        if st.button("Create Mock Filter"):
            filter_data = {
                "id": "mock_filter_1",
                "name": "Mock Test Filter",
                "filter_data": {
                    "basic_filters": {
                        "user_id": None,
                        "product_type_id": None,
                        "status": "CREATED",
                        "date_from": date.today().replace(day=1).isoformat(),
                        "date_to": date.today().isoformat()
                    },
                    "column_filters": [],
                    "complex_filters": None,
                    "search_query": "",
                    "sort_fields": []
                },
                "created_at": datetime.now().isoformat(),
                "is_default": False
            }
            st.session_state["mock_filter"] = filter_data
            show_inline_success("Mock filter created!")
            
        # Set as user default
        if "mock_filter" in st.session_state and st.button("Set Mock Filter as User Default"):
            filter_data = st.session_state["mock_filter"]["filter_data"]
            filter_id = st.session_state["mock_filter"]["id"]
            success, error = set_default_filter_for_user(filter_id, filter_data)
            if error:
                show_inline_error(f"Error: {error}")
            else:
                show_inline_success("Mock filter set as user default!")
                st.rerun()
        
        # Clear user default
        if st.button("Clear User Default Filter"):
            success, error = clear_default_filter_for_user()
            if error:
                show_inline_error(f"Error: {error}")
            else:
                show_inline_success("User default filter cleared!")
                st.rerun()
                
        # Reset session state filters
        if st.button("Reset Session State Filters"):
            st.session_state["producer_filter_om"] = None
            st.session_state["product_filter_om"] = None
            st.session_state["status_filter_om"] = None
            st.session_state["from_date_filter_om"] = date.today().replace(day=1)
            st.session_state["to_date_filter_om"] = date.today()
            show_inline_success("Session state filters reset!")
            st.rerun()
        
        # Apply default filter
        if st.button("Check and Apply Default Filter"):
            applied = check_and_apply_default_filter()
            if applied:
                show_inline_success("Default filter applied!")
            else:
                show_inline_warning("No default filter applied or no default filter available.")
            st.rerun()
    
    with col2:
        st.markdown("### User Default Filter")
        user_default, error = get_default_filter_for_user()
        if error:
            st.error(f"Error: {error}")
        elif user_default:
            st.write(f"Filter ID: {user_default.get('id')}")
            st.write(f"Updated: {user_default.get('updated_at')}")
            st.markdown("#### Filter Data")
            st.json(user_default.get("filter_data", {}))
        else:
            st.info("No user default filter set")
            
        st.markdown("### Global Default Filters")
        filters, error = get_saved_filters()
        if error:
            st.error(f"Error: {error}")
        else:
            default_filters = [f for f in filters if f.get("is_default", False)]
            if default_filters:
                for df in default_filters:
                    st.write(f"Filter ID: {df.get('id')}")
                    st.write(f"Name: {df.get('name')}")
                    st.write(f"Created: {df.get('created_at')}")
                    st.markdown("#### Filter Data")
                    st.json(df.get("filter_data", {}))
            else:
                st.info("No global default filters set")
        
        st.markdown("### Test User Switcher")
        new_user_id = st.text_input("Enter user ID to switch to", value=st.session_state.get("user_id", ""))
        new_user_role = st.selectbox("Select user role", 
                                    options=["admin", "operator", "producer"],
                                    index=1)
        
        if st.button("Switch User"):
            st.session_state["user_id"] = new_user_id
            st.session_state["user_role"] = new_user_role
            show_inline_success(f"User switched to {new_user_id} with role {new_user_role}")
            # Clear the user default filter check flag to force recheck
            if "user_default_filter_checked" in st.session_state:
                del st.session_state["user_default_filter_checked"]
            st.rerun()

if __name__ == "__main__":
    main()