"""
Unified Offer Management Dashboard
Egységes Ajánlatkezelő Irányítópult

Kreatív, modern dashboard megoldás amely integrálja az összes meglévő komponenst
egy átfogó, felhasználóbarát felületen keresztül.
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
import hashlib

# Configure logger first
logger = logging.getLogger(__name__)

# Import existing components with error handling
try:
    from .statistics_overview import render_statistics_overview
except ImportError:
    render_statistics_overview = None

try:
    from .saved_filter_ui import render_saved_filters_management
except ImportError:
    render_saved_filters_management = None

try:
    from .export_functions import render_export_buttons
except ImportError:
    render_export_buttons = None

try:
    from .filter_diagnostic_tool import FilterDiagnosticTool
except ImportError:
    FilterDiagnosticTool = None

try:
    from .api_client import get_offers
    # Create a wrapper function to match expected interface
    def fetch_offers_data(params=None):
        """Wrapper around get_offers to match expected interface"""
        try:
            return get_offers(params)
        except Exception as e:
            return False, f"API hiba: {str(e)}"
    
    # Only log after successful import
    _api_available = True
except ImportError:
    fetch_offers_data = None
    _api_available = False

try:
    from .modern_ui_simple import render_offers_display
except ImportError:
    render_offers_display = None

try:
    from .customizable_table import CustomizableTable
except ImportError:
    CustomizableTable = None

try:
    from .user_preferences import UserPreferences
except ImportError:
    UserPreferences = None

try:
    from .smart_assistant import SmartAssistant, inject_keyboard_shortcuts
except ImportError:
    SmartAssistant = None
    inject_keyboard_shortcuts = None

class UnifiedDashboard:
    """Unified Dashboard for Offer Management"""
    
    def __init__(self):
        # Initialize with fallbacks for missing components
        self.user_prefs = UserPreferences() if UserPreferences else None
        self.diagnostic_tool = FilterDiagnosticTool() if FilterDiagnosticTool else None
        self.customizable_table = CustomizableTable() if CustomizableTable else None
        self.smart_assistant = SmartAssistant() if SmartAssistant else None
        
        # Log component availability after initialization
        logger.info(f"Dashboard initialized - API: {_api_available}, Stats: {render_statistics_overview is not None}, Smart Assistant: {SmartAssistant is not None}")
        
        # Initialize session state
        self._init_session_state()
    
    def _init_session_state(self):
        """Initialize dashboard session state"""
        if 'dashboard_tab' not in st.session_state:
            st.session_state.dashboard_tab = "📊 Áttekintés"
        
        if 'dashboard_refresh_interval' not in st.session_state:
            st.session_state.dashboard_refresh_interval = 30
        
        if 'dashboard_auto_refresh' not in st.session_state:
            st.session_state.dashboard_auto_refresh = False
        
        if 'dashboard_last_refresh' not in st.session_state:
            st.session_state.dashboard_last_refresh = datetime.now()
    
    def _get_widget_key(self, base_key: str) -> str:
        """Generate unique widget key for dashboard components"""
        return hashlib.md5(f"unified_dashboard_{base_key}".encode()).hexdigest()[:8]
    
    def clear_offer_detail_state(self):
        """Clear offer detail related session state"""
        keys_to_clear = [
            'selected_offer_id',
            'loading_logs',
            'loading_attachments', 
            'loading_related',
            'offer_detail_tab',
            'edit_mode'
        ]
        
        # Clear keys that match patterns
        for key in list(st.session_state.keys()):
            if any(pattern in key for pattern in ['show_status_dialog_', 'new_status_', 'offer_detail_']):
                del st.session_state[key]
        
        # Clear specific keys
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
        
        logger.info("Offer detail session state cleared")
    
    def render_breadcrumb_navigation(self):
        """Render breadcrumb navigation for offer details"""
        if 'selected_offer_id' in st.session_state:
            st.markdown("### 🧭 Navigáció")
            
            col1, col2, col3, col4 = st.columns([1, 1, 1, 5])
            
            with col1:
                if st.button("🏠 Főoldal", key=self._get_widget_key("nav_home")):
                    self.clear_offer_detail_state()
                    st.rerun()
            
            with col2:
                if st.button("📋 Ajánlatok", key=self._get_widget_key("nav_offers")):
                    self.clear_offer_detail_state()
                    st.rerun()
            
            with col3:
                if st.button("🎯 Dashboard", key=self._get_widget_key("nav_dashboard")):
                    self.clear_offer_detail_state()
                    # Switch to dashboard view if we're in list view
                    st.session_state.offer_management_view = '🎯 Dashboard nézet'
                    st.rerun()
            
            with col4:
                selected_id = st.session_state.get('selected_offer_id', 'N/A')
                st.markdown(f"**› Ajánlat #{selected_id} részletei**")
            
            st.divider()
    
    def render_dashboard_header(self):
        """Render dashboard header with controls"""
        col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
        
        with col1:
            st.markdown("### 🎯 Egységes Ajánlatkezelő Irányítópult")
            
        with col2:
            # Auto-refresh toggle
            auto_refresh = st.checkbox(
                "🔄 Auto frissítés",
                value=st.session_state.dashboard_auto_refresh,
                key=self._get_widget_key("auto_refresh"),
                help="Automatikus adatfrissítés bekapcsolása"
            )
            st.session_state.dashboard_auto_refresh = auto_refresh
            
        with col3:
            # Refresh interval
            if st.session_state.dashboard_auto_refresh:
                interval = st.selectbox(
                    "Frissítési időköz",
                    options=[10, 30, 60, 300],
                    index=1,
                    format_func=lambda x: f"{x}s" if x < 60 else f"{x//60}m",
                    key=self._get_widget_key("refresh_interval")
                )
                st.session_state.dashboard_refresh_interval = interval
        
        with col4:
            # Manual refresh button
            if st.button("🔄 Frissítés", key=self._get_widget_key("manual_refresh")):
                st.session_state.dashboard_last_refresh = datetime.now()
                st.rerun()
        
        # Last refresh info
        last_refresh = st.session_state.dashboard_last_refresh
        time_diff = datetime.now() - last_refresh
        st.caption(f"Utolsó frissítés: {time_diff.seconds}s ago")
        
        st.divider()
    
    def render_fab_menu(self):
        """Render Floating Action Button menu"""
        with st.sidebar:
            st.markdown("### ⚡ Gyors műveletek")
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📊 Exportálás", key=self._get_widget_key("fab_export")):
                    st.session_state.dashboard_tab = "⚙️ Beállítások"
                    st.rerun()
                    
                if st.button("📋 Új szűrő", key=self._get_widget_key("fab_filter")):
                    st.session_state.dashboard_tab = "📋 Ajánlatok"
                    st.rerun()
            
            with col2:
                if st.button("📈 Statisztika", key=self._get_widget_key("fab_stats")):
                    st.session_state.dashboard_tab = "📈 Statisztikák"
                    st.rerun()
                    
                if st.button("🔧 Debug", key=self._get_widget_key("fab_debug")):
                    debug_mode = st.session_state.get('debug_mode', False)
                    st.session_state.debug_mode = not debug_mode
                    st.rerun()
    
    def render_overview_tab(self):
        """Render Overview tab - Áttekintés"""
        st.markdown("## 📊 Rendszer Áttekintés")
        
        # Smart search at the top
        if self.smart_assistant:
            search_query = self.smart_assistant.render_smart_search()
            if search_query:
                st.session_state.global_search_query = search_query
        
        # Two-column layout: metrics + recommendations
        overview_col1, overview_col2 = st.columns([3, 1])
        
        with overview_col1:
            # Key metrics row
            metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
        
            try:
                # Fetch basic data for metrics
                if fetch_offers_data:
                    success, offers_data = fetch_offers_data()
                
                    if success and offers_data and isinstance(offers_data, list):
                        df = pd.DataFrame(offers_data)
                
                        with metric_col1:
                            total_offers = len(df)
                            st.metric("📋 Összes ajánlat", total_offers)
                    
                        with metric_col2:
                            if 'status' in df.columns:
                                active_offers = len(df[df['status'].isin(['CREATED', 'CONFIRMED_BY_COMPANY'])])
                                st.metric("⚡ Aktív ajánlatok", active_offers)
                            else:
                                st.metric("⚡ Aktív ajánlatok", "N/A")
                        
                        with metric_col3:
                            if 'created_at' in df.columns:
                                today = datetime.now().date()
                                df['created_date'] = pd.to_datetime(df['created_at']).dt.date
                                today_offers = len(df[df['created_date'] == today])
                                st.metric("📅 Ma létrehozott", today_offers)
                            else:
                                st.metric("📅 Ma létrehozott", "N/A")
                        
                        with metric_col4:
                            if 'producer_name' in df.columns:
                                unique_producers = df['producer_name'].nunique()
                                st.metric("👥 Termelők száma", unique_producers)
                            else:
                                st.metric("👥 Termelők száma", "N/A")
                    elif success and offers_data:
                        # Data exists but is not a list (probably error message)
                        for col in [metric_col1, metric_col2, metric_col3, metric_col4]:
                            with col:
                                st.metric("API hiba", "N/A")
                    else:
                        # API returned no data
                        for col in [metric_col1, metric_col2, metric_col3, metric_col4]:
                            with col:
                                st.metric("Loading...", "N/A")
                else:
                    # API client not available
                    for col in [metric_col1, metric_col2, metric_col3, metric_col4]:
                        with col:
                            st.metric("API nem elérhető", "N/A")
            
            except Exception as e:
                logger.error(f"Error in overview metrics: {e}")
                st.error("Hiba történt a metrikák betöltése során")
        
        # Add recommendations panel in the right column
        with overview_col2:
            if self.smart_assistant:
                self.smart_assistant.render_smart_recommendations()
            else:
                st.markdown("#### 🤖 Intelligens Ajánlások")
                st.info("Smart Assistant nem elérhető")
        
        st.divider()
        
        # Quick access panels
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 🚀 Gyors hozzáférés")
            
            if st.button("📋 Ajánlatok megtekintése", key=self._get_widget_key("quick_offers")):
                st.session_state.dashboard_tab = "📋 Ajánlatok"
                st.rerun()
            
            if st.button("📈 Részletes statisztikák", key=self._get_widget_key("quick_stats")):
                st.session_state.dashboard_tab = "📈 Statisztikák"
                st.rerun()
            
            if st.button("💾 Mentett szűrők", key=self._get_widget_key("quick_saved_filters")):
                st.session_state.dashboard_tab = "⚙️ Beállítások"
                st.rerun()
        
        with col2:
            st.markdown("#### 📊 Rendszer állapot")
            
            # System health indicators
            health_col1, health_col2 = st.columns(2)
            
            with health_col1:
                st.success("🟢 API kapcsolat")
                st.info("🔵 Adatbázis")
            
            with health_col2:
                st.success("🟢 Frontend")
                
                # Debug mode indicator
                if st.session_state.get('debug_mode', False):
                    st.warning("🟡 Debug mód aktív")
                else:
                    st.success("🟢 Produkció mód")
        
        # Recent activity (if available)
        st.markdown("#### 📈 Legutóbbi aktivitás")
        
        try:
            if success and offers_data:
                # Show recent offers
                recent_df = df.head(5)
                if not recent_df.empty:
                    display_columns = ['id', 'producer_name', 'product_name', 'status']
                    available_columns = [col for col in display_columns if col in recent_df.columns]
                    
                    if available_columns:
                        st.dataframe(
                            recent_df[available_columns],
                            use_container_width=True,
                            hide_index=True
                        )
                    else:
                        st.info("Nincsenek megjeleníthető oszlopok")
                else:
                    st.info("Nincsenek ajánlatok")
            else:
                st.info("Adatok betöltése...")
        
        except Exception as e:
            logger.error(f"Error in recent activity: {e}")
            st.info("A legutóbbi aktivitás betöltése nem sikerült")
    
    def render_offers_tab(self):
        """Render Offers tab - Ajánlatok"""
        st.markdown("## 📋 Ajánlatok Kezelése")
        
        # Smart Assistant features
        if self.smart_assistant:
            # Dynamic filter builder
            st.markdown("### 🎯 Intelligens Szűrők")
            filter_groups = self.smart_assistant.complex_filters.render_filter_builder()
            
            # Advanced sorting controls
            sorting_rules = self.smart_assistant.render_sorting_controls()
            
            # Performance monitor
            self.smart_assistant.render_performance_monitor()
            
            st.divider()
        
        # Enhanced offers view with integrated filters and table
        try:
            if render_offers_display and fetch_offers_data:
                # Get offers data first
                success, offers_data = fetch_offers_data()
                if success and offers_data and isinstance(offers_data, list):
                    # Apply smart assistant processing if available
                    processed_offers = offers_data
                    
                    if self.smart_assistant:
                        # Apply search filter if there's a global search query
                        global_search = st.session_state.get('global_search_query', '')
                        if global_search:
                            processed_offers = self.smart_assistant.apply_search_filter(processed_offers, global_search)
                            st.info(f"🔍 Keresési szűrő aktív: '{global_search}' - {len(processed_offers)} találat")
                        
                        # Apply complex filters
                        processed_offers = self.smart_assistant.apply_complex_filters(processed_offers)
                        
                        # Apply sorting
                        processed_offers = self.smart_assistant.apply_sorting(processed_offers)
                        
                        # Update session state with processed offers for other components
                        st.session_state.current_offers = processed_offers
                    
                    # Render with device detection
                    device_type = st.session_state.get('device_type', 'desktop')
                    render_offers_display(processed_offers, device_type)
                elif success and offers_data:
                    st.error(f"API adatformátum hiba: {offers_data}")
                else:
                    st.info("Nincsenek elérhető ajánlatok")
            else:
                # Fallback basic table
                st.markdown("### Alapvető nézet")
                if fetch_offers_data:
                    success, offers_data = fetch_offers_data()
                    if success and offers_data and isinstance(offers_data, list):
                        df = pd.DataFrame(offers_data)
                        st.dataframe(df, use_container_width=True)
                    elif success and offers_data:
                        st.error(f"API adatformátum hiba: {offers_data}")
                    else:
                        st.info("Nincsenek elérhető ajánlatok")
                else:
                    st.error("API kliens nem elérhető")
            
        except Exception as e:
            logger.error(f"Error rendering offers tab: {e}")
            st.error("Hiba történt az ajánlatok betöltése során")
            
            # Ultimate fallback
            st.markdown("### Alapvető nézet")
            st.info("A teljes ajánlat megjelenítő nem elérhető")
    
    def render_statistics_tab(self):
        """Render Statistics tab - Statisztikák"""
        st.markdown("## 📈 Statisztikák és Jelentések")
        
        # Smart Assistant status dashboard
        if self.smart_assistant:
            self.smart_assistant.render_status_dashboard()
            st.divider()
        
        try:
            # Debug step-by-step execution
            if st.session_state.get('debug_mode', False):
                st.write("🔧 Debug: Starting statistics rendering...")
                st.write(f"🔧 render_statistics_overview available: {render_statistics_overview is not None}")
                st.write(f"🔧 fetch_offers_data available: {fetch_offers_data is not None}")
            
            if render_statistics_overview and fetch_offers_data:
                if st.session_state.get('debug_mode', False):
                    st.write("🔧 Debug: Attempting to fetch offers data...")
                
                # Fetch offers data first and pass it to statistics
                try:
                    success, offers_data = fetch_offers_data()
                    
                    if st.session_state.get('debug_mode', False):
                        st.write(f"🔧 Debug: API call success: {success}")
                        # Safe data count check
                        if success and offers_data and isinstance(offers_data, list):
                            data_count = len(offers_data)
                        elif success and offers_data:
                            data_count = f"Data type: {type(offers_data).__name__}"
                        else:
                            data_count = "N/A"
                        st.write(f"🔧 Debug: Data count: {data_count}")
                    
                    if success and offers_data and isinstance(offers_data, list):
                        if st.session_state.get('debug_mode', False):
                            st.write("🔧 Debug: Calling render_statistics_overview with data...")
                        
                        # Pass the data to avoid API calls within statistics
                        render_statistics_overview(offers=offers_data, show_filters=True)
                    elif success and offers_data:
                        # Data is not a list, probably an error message
                        if st.session_state.get('debug_mode', False):
                            st.error(f"🔧 Debug: Unexpected data type: {type(offers_data)} - {offers_data}")
                        st.warning(f"⚠️ API visszatérési hiba: {offers_data}")
                        self._render_fallback_statistics()
                    else:
                        st.warning("⚠️ Nem sikerült betölteni az ajánlat adatokat a statisztikákhoz")
                        st.info("📊 Alapvető statisztikákra váltás...")
                        # Show fallback statistics
                        self._render_fallback_statistics()
                        
                except Exception as fetch_error:
                    if st.session_state.get('debug_mode', False):
                        st.error(f"🔧 Debug: Fetch error: {fetch_error}")
                    st.warning(f"⚠️ Hiba az adatok lekérésekor: {fetch_error}")
                    self._render_fallback_statistics()
                    
            elif render_statistics_overview:
                if st.session_state.get('debug_mode', False):
                    st.write("🔧 Debug: Trying statistics without pre-fetched data...")
                
                # Try without data - let it handle its own API calls
                try:
                    render_statistics_overview()
                except Exception as api_error:
                    if st.session_state.get('debug_mode', False):
                        st.error(f"🔧 Debug: Statistics component error: {api_error}")
                    st.warning(f"API hiba a statisztikákban: {api_error}")
                    self._render_fallback_statistics()
            else:
                if st.session_state.get('debug_mode', False):
                    st.write("🔧 Debug: No statistics component, using fallback...")
                
                # No statistics component available
                self._render_fallback_statistics()
            
        except Exception as e:
            logger.error(f"Error rendering statistics tab: {e}", exc_info=True)
            st.error("Hiba történt a statisztikák betöltése során")
            
            # Show detailed error in debug mode
            if st.session_state.get('debug_mode', False):
                st.error(f"🔧 Debug error details: {str(e)}")
                st.exception(e)
            
            # Show fallback
            st.info("🔄 Alapvető statisztikákra váltás...")
            try:
                self._render_fallback_statistics()
            except Exception as fallback_error:
                logger.error(f"Even fallback statistics failed: {fallback_error}")
                st.error("🚨 Minden statisztikai megjelenítés sikertelen")
                st.markdown("""
                **Nincs elérhető statisztikai adat**
                
                🔧 Próbálj meg:
                1. Debug mód bekapcsolása a részletes hibák megtekintéséhez
                2. Lista nézetre váltás az ajánlatok megtekintéséhez
                3. Oldal frissítése
                """)
    
    def _render_fallback_statistics(self):
        """Render fallback basic statistics when main component is not available"""
        try:
            st.markdown("### Alapvető statisztikák")
            
            if st.session_state.get('debug_mode', False):
                st.write("🔧 Debug: Starting fallback statistics...")
                st.write(f"🔧 Debug: fetch_offers_data available: {fetch_offers_data is not None}")
            
            if not fetch_offers_data:
                st.info("🔌 API kliens nem elérhető")
                st.markdown("""
                **Statisztikák jelenleg nem elérhetők.**
                
                Lehetséges okok:
                - Az API kapcsolat nem elérhető
                - A backend szerver nem fut
                - Hálózati kapcsolat probléma
                
                📋 Próbáld ki a **Lista nézetet** az ajánlatok eléréséhez.
                """)
                return
            
            if st.session_state.get('debug_mode', False):
                st.write("🔧 Debug: Fetching data for fallback statistics...")
            
            success, offers_data = fetch_offers_data()
            
            if st.session_state.get('debug_mode', False):
                st.write(f"🔧 Debug: Fallback fetch success: {success}")
                # Safe data count check for fallback
                if success and offers_data and isinstance(offers_data, list):
                    fallback_data_count = len(offers_data)
                elif success and offers_data:
                    fallback_data_count = f"Data type: {type(offers_data).__name__}"
                else:
                    fallback_data_count = "N/A"
                st.write(f"🔧 Debug: Fallback data count: {fallback_data_count}")
            
            if success and offers_data and isinstance(offers_data, list):
                try:
                    df = pd.DataFrame(offers_data)
                    
                    if st.session_state.get('debug_mode', False):
                        st.write(f"🔧 Debug: DataFrame created with shape: {df.shape}")
                        st.write(f"🔧 Debug: DataFrame columns: {list(df.columns)}")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("#### Ajánlatok státusz szerint")
                        if 'status' in df.columns:
                            try:
                                status_counts = df['status'].value_counts()
                                st.bar_chart(status_counts)
                            except Exception as chart_error:
                                if st.session_state.get('debug_mode', False):
                                    st.error(f"🔧 Debug: Status chart error: {chart_error}")
                                st.info("Státusz chart generálási hiba")
                        else:
                            st.info("Státusz adatok nem elérhetők")
                    
                    with col2:
                        st.markdown("#### Termelők aktivitása")
                        if 'producer_name' in df.columns:
                            try:
                                producer_counts = df['producer_name'].value_counts().head(10)
                                st.bar_chart(producer_counts)
                            except Exception as chart_error:
                                if st.session_state.get('debug_mode', False):
                                    st.error(f"🔧 Debug: Producer chart error: {chart_error}")
                                st.info("Termelő chart generálási hiba")
                        else:
                            st.info("Termelő adatok nem elérhetők")
                            
                except Exception as df_error:
                    if st.session_state.get('debug_mode', False):
                        st.error(f"🔧 Debug: DataFrame creation error: {df_error}")
                    st.error("Hiba az adatok feldolgozásakor")
                    
            elif success and offers_data:
                # Data is not a list, probably an error message
                if st.session_state.get('debug_mode', False):
                    st.error(f"🔧 Debug: Fallback unexpected data type: {type(offers_data)} - {offers_data}")
                st.error(f"API adatformátum hiba: {offers_data}")
                    
            else:
                st.info("Nincsenek elérhető adatok a statisztikákhoz")
                
        except Exception as fallback_error:
            logger.error(f"Error in fallback statistics: {fallback_error}", exc_info=True)
            if st.session_state.get('debug_mode', False):
                st.error(f"🔧 Debug: Fallback statistics error: {fallback_error}")
                st.exception(fallback_error)
            st.error("🚨 Alapvető statisztikák is sikertelenek")
            st.markdown("**Minden statisztikai megjelenítés sikertelen** - ellenőrizd a debug módot a részletekért.")
    
    def render_settings_tab(self):
        """Render Settings tab - Beállítások"""
        st.markdown("## ⚙️ Beállítások és Eszközök")
        
        # Create sub-tabs for different settings sections
        settings_tab1, settings_tab2, settings_tab3, settings_tab4 = st.tabs([
            "💾 Mentett szűrők", 
            "📤 Exportálás", 
            "🔧 Debug eszközök",
            "👤 Felhasználói beállítások"
        ])
        
        with settings_tab1:
            st.markdown("### 💾 Mentett szűrők kezelése")
            try:
                if render_saved_filters_management:
                    render_saved_filters_management()
                else:
                    st.info("Mentett szűrők komponens nem elérhető")
                    st.write("Ez a funkció a teljes rendszerben lesz elérhető.")
            except Exception as e:
                logger.error(f"Error rendering saved filters: {e}")
                st.error("Hiba történt a mentett szűrők betöltése során")
        
        with settings_tab2:
            st.markdown("### 📤 Adatok exportálása")
            try:
                if render_export_buttons and fetch_offers_data:
                    # For export functions, we need to get offers data first
                    success, offers_data = fetch_offers_data()
                    if success and offers_data and isinstance(offers_data, list):
                        render_export_buttons(offers_data)
                    elif success and offers_data:
                        st.error(f"API adatformátum hiba az exportáláshoz: {offers_data}")
                    else:
                        st.info("Nincsenek elérhető adatok az exportáláshoz")
                        st.write("Kérlek alkalmazz szűrőket az Ajánlatok fülön, majd térj vissza ide.")
                else:
                    st.info("Export funkciók nem elérhetők")
                    st.write("CSV és Excel export a teljes rendszerben lesz elérhető.")
            except Exception as e:
                logger.error(f"Error rendering export functions: {e}")
                st.error("Hiba történt az export funkciók betöltése során")
        
        with settings_tab3:
            st.markdown("### 🔧 Debug eszközök")
            
            # Debug mode toggle
            debug_mode = st.session_state.get('debug_mode', False)
            new_debug_mode = st.checkbox(
                "Debug mód engedélyezése",
                value=debug_mode,
                key=self._get_widget_key("settings_debug_toggle")
            )
            
            if new_debug_mode != debug_mode:
                st.session_state.debug_mode = new_debug_mode
                st.success(f"Debug mód {'bekapcsolva' if new_debug_mode else 'kikapcsolva'}")
                st.rerun()
            
            if st.session_state.get('debug_mode', False):
                st.markdown("#### 🔍 Szűrő diagnosztika")
                try:
                    if self.diagnostic_tool:
                        # Use existing diagnostic tool
                        diagnostic_results = self.diagnostic_tool.run_comprehensive_diagnostic()
                        
                        if diagnostic_results:
                            for category, results in diagnostic_results.items():
                                with st.expander(f"📊 {category}"):
                                    for result in results:
                                        if result.get('success', False):
                                            st.success(f"✅ {result.get('message', 'OK')}")
                                        else:
                                            st.error(f"❌ {result.get('message', 'Error')}")
                        else:
                            st.info("Nincs diagnosztikai információ")
                    else:
                        st.info("Diagnosztikai eszköz nem elérhető")
                        st.write("A teljes debug eszköztár a fő lista nézetben érhető el.")
                        
                except Exception as e:
                    logger.error(f"Error in diagnostic tool: {e}")
                    st.error("Hiba történt a diagnosztikai eszköz futtatása során")
        
        with settings_tab4:
            st.markdown("### 👤 Felhasználói beállítások")
            
            # Dashboard preferences
            st.markdown("#### 🎛️ Dashboard beállítások")
            
            col1, col2 = st.columns(2)
            
            with col1:
                default_tab = st.selectbox(
                    "Alapértelmezett fül",
                    options=["📊 Áttekintés", "📋 Ajánlatok", "📈 Statisztikák", "⚙️ Beállítások"],
                    index=0,
                    key=self._get_widget_key("default_tab")
                )
                
                theme_preference = st.selectbox(
                    "Téma preferencia",
                    options=["Automatikus", "Világos", "Sötét"],
                    index=0,
                    key=self._get_widget_key("theme")
                )
            
            with col2:
                table_page_size = st.number_input(
                    "Táblázat oldalméret",
                    min_value=10,
                    max_value=100,
                    value=25,
                    step=5,
                    key=self._get_widget_key("table_page_size")
                )
                
                notification_level = st.selectbox(
                    "Értesítési szint",
                    options=["Minden", "Fontos", "Kritikus"],
                    index=1,
                    key=self._get_widget_key("notification_level")
                )
            
            if st.button("💾 Beállítások mentése", key=self._get_widget_key("save_settings")):
                # Save user preferences
                preferences = {
                    'default_tab': default_tab,
                    'theme': theme_preference,
                    'table_page_size': table_page_size,
                    'notification_level': notification_level
                }
                
                try:
                    if self.user_prefs:
                        self.user_prefs.save_preferences(preferences)
                        st.success("✅ Beállítások sikeresen mentve!")
                    else:
                        # Save to session state as fallback
                        st.session_state.dashboard_preferences = preferences
                        st.success("✅ Beállítások ideiglenesen mentve session state-be!")
                        st.info("A teljes rendszerben a beállítások tartósan mentésre kerülnek.")
                except Exception as e:
                    logger.error(f"Error saving preferences: {e}")
                    st.error("❌ Hiba történt a beállítások mentése során")
    
    def render_keyboard_shortcuts_info(self):
        """Render keyboard shortcuts information"""
        with st.sidebar:
            with st.expander("⌨️ Billentyűparancsok"):
                st.markdown("""
                **Dashboard navigáció:**
                - `1` - Áttekintés
                - `2` - Ajánlatok  
                - `3` - Statisztikák
                - `4` - Beállítások
                
                **Gyors műveletek:**
                - `R` - Frissítés
                - `E` - Exportálás
                - `F` - Szűrő
                - `D` - Debug mód
                """)
    
    def render_offer_detail_view(self):
        """Render offer detail view with navigation"""
        try:
            # Render breadcrumb navigation
            self.render_breadcrumb_navigation()
            
            # Try to import and render offer details
            try:
                from .offer_detail import show_offer_detail
                offer_id = st.session_state.get('selected_offer_id')
                
                if offer_id:
                    show_offer_detail(offer_id)
                else:
                    st.error("Nincs kiválasztott ajánlat")
                    self.clear_offer_detail_state()
                    st.rerun()
                    
            except ImportError as e:
                logger.error(f"Cannot import offer_detail: {e}")
                st.error("❌ Ajánlat részletek modul nem elérhető")
                
                # Fallback offer detail view
                self.render_fallback_offer_detail()
                
            except Exception as e:
                logger.error(f"Error rendering offer detail: {e}")
                st.error(f"Hiba az ajánlat megjelenítésekor: {e}")
                
                # Back button for error recovery
                if st.button("🔙 Vissza az ajánlatokhoz", key="error_back_button"):
                    self.clear_offer_detail_state()
                    st.rerun()
                    
        except Exception as e:
            logger.error(f"Critical error in offer detail view: {e}")
            st.error("Kritikus hiba történt")
            
            # Emergency back button
            if st.button("🆘 Vészhelyzeti visszatérés", key="emergency_back"):
                self.clear_offer_detail_state()
                st.rerun()
    
    def render_fallback_offer_detail(self):
        """Fallback offer detail view when main component is not available"""
        offer_id = st.session_state.get('selected_offer_id')
        
        st.markdown(f"## 📋 Ajánlat #{offer_id} Részletei")
        st.info("🔧 Részletes nézet fejlesztés alatt")
        
        # Basic offer info if we can fetch it
        if fetch_offers_data:
            try:
                success, offers_data = fetch_offers_data()
                if success and offers_data:
                    # Find the specific offer
                    selected_offer = next((offer for offer in offers_data if str(offer.get('id')) == str(offer_id)), None)
                    
                    if selected_offer:
                        st.markdown("### 📊 Alapvető Információk")
                        
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.markdown("**Ajánlat adatok:**")
                            st.write(f"🆔 **ID**: {selected_offer.get('id')}")
                            st.write(f"🌾 **Termék**: {selected_offer.get('product_name', 'N/A')}")
                            st.write(f"📦 **Mennyiség**: {selected_offer.get('quantity_in_kg', 'N/A')} kg")
                            st.write(f"💰 **Ár**: {selected_offer.get('price', selected_offer.get('confirmed_price', 'N/A'))} Ft/kg")
                        
                        with col2:
                            st.markdown("**Státusz és dátumok:**")
                            st.write(f"📊 **Státusz**: {selected_offer.get('status', 'N/A')}")
                            st.write(f"📅 **Létrehozva**: {selected_offer.get('created_at', 'N/A')}")
                            st.write(f"👤 **Termelő**: {selected_offer.get('producer_name', selected_offer.get('user_name', 'N/A'))}")
                        
                        # Show full data in debug mode
                        if st.session_state.get('debug_mode', False):
                            with st.expander("🔧 Debug - Teljes adat", expanded=False):
                                st.json(selected_offer)
                    else:
                        st.error(f"Ajánlat #{offer_id} nem található")
                else:
                    st.error("Nem sikerült betölteni az ajánlatokat")
                    
            except Exception as e:
                st.error(f"Hiba az ajánlat betöltésekor: {e}")
        else:
            st.error("API kliens nem elérhető")
        
        # Back button
        st.markdown("---")
        if st.button("🔙 Vissza az ajánlatokhoz", key="fallback_back_button", type="primary"):
            self.clear_offer_detail_state()
            st.rerun()
    
    def render(self):
        """Main render method for the unified dashboard"""
        try:
            # Check if we should render offer details instead of the dashboard
            if 'selected_offer_id' in st.session_state:
                self.render_offer_detail_view()
                return
            
            # Debug info for component availability (only in debug mode)
            if st.session_state.get('debug_mode', False):
                with st.expander("🔧 Dashboard Component Status", expanded=False):
                    st.write("**Component Availability:**")
                    components_status = {
                        "fetch_offers_data": fetch_offers_data is not None,
                        "render_statistics_overview": render_statistics_overview is not None,
                        "render_saved_filters_management": render_saved_filters_management is not None,
                        "render_export_buttons": render_export_buttons is not None,
                        "render_offers_display": render_offers_display is not None,
                        "FilterDiagnosticTool": FilterDiagnosticTool is not None,
                        "CustomizableTable": CustomizableTable is not None,
                        "UserPreferences": UserPreferences is not None,
                        "SmartAssistant": SmartAssistant is not None
                    }
                    
                    for component, available in components_status.items():
                        status_icon = "✅" if available else "❌"
                        st.write(f"{status_icon} {component}")
            
            # Inject keyboard shortcuts
            if inject_keyboard_shortcuts:
                inject_keyboard_shortcuts()
            
            # Render header
            self.render_dashboard_header()
            
            # Auto-refresh logic
            if st.session_state.dashboard_auto_refresh:
                time_since_refresh = datetime.now() - st.session_state.dashboard_last_refresh
                if time_since_refresh.seconds >= st.session_state.dashboard_refresh_interval:
                    st.session_state.dashboard_last_refresh = datetime.now()
                    st.rerun()
            
            # Main tab navigation
            tab1, tab2, tab3, tab4 = st.tabs([
                "📊 Áttekintés", 
                "📋 Ajánlatok", 
                "📈 Statisztikák", 
                "⚙️ Beállítások"
            ])
            
            with tab1:
                self.render_overview_tab()
            
            with tab2:
                self.render_offers_tab()
            
            with tab3:
                self.render_statistics_tab()
            
            with tab4:
                self.render_settings_tab()
            
            # Render sidebar components
            self.render_fab_menu()
            self.render_keyboard_shortcuts_info()
            
        except Exception as e:
            logger.error(f"Error rendering unified dashboard: {e}")
            st.error("Hiba történt a dashboard betöltése során")
            
            # Fallback minimal interface
            st.markdown("## ⚠️ Alapvető nézet")
            st.info("A teljes dashboard nem elérhető, alapvető funkciók:")
            
            if st.button("📋 Egyszerű ajánlatlista"):
                success, offers_data = fetch_offers_data()
                if success and offers_data:
                    df = pd.DataFrame(offers_data)
                    st.dataframe(df, use_container_width=True)
                else:
                    st.error("Nem sikerült betölteni az ajánlatokat")

# Factory function for easy integration
def render_unified_dashboard():
    """Factory function to render the unified dashboard"""
    dashboard = UnifiedDashboard()
    dashboard.render()

if __name__ == "__main__":
    # For testing purposes
    render_unified_dashboard()