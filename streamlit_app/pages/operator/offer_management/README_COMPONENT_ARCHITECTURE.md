# Component Architecture Documentation

This document outlines the new component architecture implemented for the Offer Management module as part of **TASK-3.1.1: UI Components Reorganization**.

## Overview

The new component architecture follows a React-like pattern with a focus on reusability, maintainability, and clarity. It is designed to standardize how UI components are developed and used throughout the application while maintaining backward compatibility with existing code.

## Core Principles

1. **Component-Based Design**: Each UI element is a self-contained component with its own properties, state, and rendering logic.
2. **State Management**: Components manage their own state, with clear patterns for initializing and updating state.
3. **Props vs. State**: Clear separation between input properties (props) and internal state.
4. **Hierarchical Composition**: Components can be composed together to build complex UIs from simple building blocks.
5. **Consistent Lifecycle**: Components follow a consistent lifecycle with predictable behavior.

## Component Base Classes

The foundation of the architecture is the `ComponentBase` class in `component_base.py`, which defines the core interface and behavior for all components.

### ComponentBase

All UI components extend this base class which provides:

- Properties management via `props` dictionary
- State management via session state
- Unique component identification
- Lifecycle methods (mount/unmount)
- Standardized rendering pattern

```python
class ComponentBase(ABC):
    def __init__(self, props: Optional[PropDict] = None, component_id: Optional[ComponentId] = None):
        self.props = props or {}
        self.component_id = component_id or self._generate_component_id()
        self._initialize_state()
        self.is_mounted = False
        self._did_render = False

    @abstractmethod
    def render(self) -> Any:
        pass
```

### CompositeComponent

Extends ComponentBase to support parent-child relationships, enabling component composition:

```python
class CompositeComponent(ComponentBase):
    def add_child(self, child: ComponentBase, child_id: Optional[str] = None) -> ComponentId:
        # Add a child component
        
    def remove_child(self, child_id: ComponentId) -> None:
        # Remove a child component
        
    def get_child(self, child_id: ComponentId) -> Optional[ComponentBase]:
        # Get a child component by ID
        
    def render_children(self) -> Dict[ComponentId, Any]:
        # Render all child components
```

### FormComponent and FormGroup

Specialized components for form handling with validation, error management, and form submission:

```python
class FormComponent(ComponentBase):
    # Base class for form input components
    
class FormGroup(CompositeComponent):
    # Manages a group of form components
```

## Component Categories

Components are organized into logical categories based on their purpose:

### Form Components (`form_components.py`)

Input components for collecting user data:

- `TextInput`: Text input fields
- `NumberInput`: Numeric input fields
- `SelectInput`: Dropdown select fields
- `MultiSelectInput`: Multi-select fields
- `DateInput`: Date picker fields
- `DateRangeInput`: Date range pickers
- `CheckboxInput`: Checkboxes
- `RadioInput`: Radio button groups
- `SliderInput`: Slider inputs
- `FileInput`: File upload fields

### Display Components (`display_components.py`)

Components for displaying data:

- `Card`: Content cards with headers and optional collapsing
- `StatusBadge`: Status indicators with appropriate styling
- `DataTable`: Data tables with sorting and filtering
- `MetricCard`: Metric displays with optional trend indicators
- `Tooltip`: Tooltip overlays for additional information
- `ProgressBar`: Progress indicators
- `Alert`: Alert/notification messages
- `TabContainer`: Tabbed content containers
- `Accordion`: Collapsible content sections
- `Timeline`: Chronological event displays

### Action Components (`action_components.py`)

Components for user interactions:

- `Button`: Customizable buttons with various styles
- `DropdownMenu`: Dropdown menus for selecting actions
- `SearchInput`: Search input fields with suggestions
- `IconButton`: Icon-only buttons for compact UIs
- `ButtonGroup`: Groups of related buttons
- `FilterToggle`: Toggle buttons for filtering
- `Menu`: Context menus and dropdown lists
- `ActionBar`: Action bars for offer management

### Confirmation Dialogs (`confirmation_dialog.py`)

Components for confirming user actions:

- `ConfirmationDialog`: General confirmation dialogs
- `StatusChangeDialog`: Dialogs for changing item status
- `DeleteConfirmation`: Specialized dialogs for delete operations

## Usage Example

Here's an example of creating and using a component:

```python
from .component_base import ComponentBase
from .form_components import TextInput, NumberInput, SelectInput
from .display_components import Card, StatusBadge
from .action_components import Button

# Create a component that collects product information
class ProductForm(ComponentBase):
    def __init__(self, on_submit=None, initial_values=None, component_id=None):
        props = {
            'on_submit': on_submit,
            'initial_values': initial_values or {}
        }
        super().__init__(props, component_id)
    
    def render(self):
        # Get initial values from props
        initial = self.props.get('initial_values', {})
        
        # Render input fields
        name = TextInput(
            label="Product Name",
            value=initial.get('name', ''),
            required=True,
            component_id=f"{self.component_id}_name"
        ).render()
        
        quantity = NumberInput(
            label="Quantity (kg)",
            value=initial.get('quantity', 0),
            min_value=0,
            step=0.1,
            component_id=f"{self.component_id}_quantity"
        ).render()
        
        category = SelectInput(
            label="Category",
            options=[
                {'label': 'Fruit', 'value': 'fruit'},
                {'label': 'Vegetable', 'value': 'vegetable'},
                {'label': 'Grain', 'value': 'grain'}
            ],
            value=initial.get('category'),
            component_id=f"{self.component_id}_category"
        ).render()
        
        # Add a submit button
        if Button(
            label="Submit",
            type="primary",
            component_id=f"{self.component_id}_submit"
        ).render():
            # Call the submit callback if provided
            if self.props.get('on_submit'):
                self.props['on_submit']({
                    'name': name,
                    'quantity': quantity,
                    'category': category
                })
            
        return {
            'name': name,
            'quantity': quantity,
            'category': category
        }
```

## Using Helper Functions

The architecture provides helper functions for common component operations:

```python
from .component_base import create_component, render_component, with_props

# Create a component using the factory function
button = create_component(Button, label="Click Me", type="primary")

# Render a component
result = render_component(button)

# Create a component factory with fixed props
create_danger_button = with_props(Button, type="danger")
delete_button = create_danger_button(label="Delete")
```

## Migration Path

Existing code can gradually adopt the new component architecture through the following steps:

1. **Identify UI Patterns**: Analyze existing UI code to identify repeating patterns
2. **Create Components**: Replace patterns with components from the new architecture
3. **Adapt State Management**: Update state management to use the component state pattern
4. **Preserve Compatibility**: Maintain backward compatibility with existing code

## Best Practices

1. **Keep Components Focused**: Components should have a single responsibility
2. **Consistent Naming**: Follow the established naming conventions:
   - UI rendering functions begin with `render_`
   - Components have descriptive, noun-based names
   - Props and state keys use camelCase
3. **Document Components**: Include docstrings explaining the component's purpose and usage
4. **Validate Inputs**: Use type hints and validation for component props
5. **Handle Errors Gracefully**: Components should fail gracefully when inputs are invalid
6. **Test Components**: Create tests for components to ensure they behave as expected

## Next Steps

The next phase of development (TASK-3.1.2) will focus on enhancing this architecture with:

1. Component prop type validation
2. Enhanced state management patterns
3. Performance optimizations
4. Additional composite components
5. Styling system improvements

## Conclusion

This new component architecture provides a solid foundation for building maintainable, reusable UI components in Streamlit. By following these patterns, we can create more consistent, reliable, and easy-to-use interfaces for the Offer Management module and beyond.