"""
Utility functions for the offer management page.
Contains helper functions for validation, formatting, and other general purpose tools.
"""
import streamlit as st
import logging
import re
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Importáljuk a validációs függvényeket az új modulból
try:
    from pages.operator.offer_management.validation import (
        validate_offer_data,
        validate_email,
        validate_phone,
        validate_safe_filename,
        sanitize_html,
        show_validation_errors
    )
except ImportError:
    from validation import (
        validate_offer_data,
        validate_email,
        validate_phone,
        validate_safe_filename,
        sanitize_html,
        show_validation_errors
    )

def validate_offer_data(offer_data, is_new=True):
    """
    Ajánlat adatok kliens oldali validálása beküldés előtt.
    
    Ez a függvény ellenőrzi az ajánlat adatainak érvényességét mielőtt
    elküldenénk azokat az API-nak. Visszaadja a hibákat és figyelmeztetéseket.
    
    Args:
        offer_data (dict): <PERSON>z ellenő<PERSON>endő ajánlat adatok.
        is_new (bool, optional): Új ajánlat létrehozása vagy meglévő módosítása. Defaults to True.
    
    Returns:
        tuple: (érvényes, hibák, figyelmeztetések) ahol:
            - érvényes (bool): True ha az adatok érvényesek, False egyébként
            - hibák (dict): Mező név és hibaüzenetek szótára
            - figyelmeztetések (dict): Mező név és figyelmeztető üzenetek szótára
    """
    errors = {}
    warnings = {}
    
    # Kötelező mezők ellenőrzése
    required_fields = {
        "product_type_id": "Termék típus",
        "quantity_in_kg": "Mennyiség",
        "delivery_date": "Beszállítási dátum",
    }
    
    if is_new:
        required_fields["user_id"] = "Termelő"
    
    # Kötelező mezők meglétének ellenőrzése
    for field, field_name in required_fields.items():
        if field not in offer_data or not offer_data[field]:
            errors[field] = f"A(z) {field_name} mező kitöltése kötelező."
    
    # Mennyiség validálása
    if "quantity_in_kg" in offer_data and offer_data.get("quantity_in_kg"):
        try:
            qty = float(offer_data["quantity_in_kg"])
            if qty <= 0:
                errors["quantity_in_kg"] = "A mennyiségnek pozitív számnak kell lennie."
            elif qty < 1:
                warnings["quantity_in_kg"] = "1 kg-nál kevesebb mennyiség nem tipikus. Biztos ebben?"
            elif qty > 10000:
                warnings["quantity_in_kg"] = "Szokatlanul nagy mennyiség. Biztos helyesen adta meg?"
        except ValueError:
            errors["quantity_in_kg"] = "A mennyiségnek számnak kell lennie."
    
    # Ár validálása (ha meg lett adva)
    if "price" in offer_data and offer_data.get("price"):
        try:
            price = float(offer_data["price"])
            if price < 0:
                errors["price"] = "Az ár nem lehet negatív."
            elif price == 0:
                warnings["price"] = "Ingyenes ajánlatot ad meg. Biztos ebben?"
            elif price > 100000:
                warnings["price"] = "Szokatlanul magas ár. Biztos helyesen adta meg?"
        except ValueError:
            errors["price"] = "Az árnak számnak kell lennie."
    
    # Dátum validálása
    if "delivery_date" in offer_data and offer_data.get("delivery_date"):
        try:
            if isinstance(offer_data["delivery_date"], str):
                delivery_date = datetime.strptime(offer_data["delivery_date"], "%Y-%m-%d").date()
            else:
                delivery_date = offer_data["delivery_date"]
                
            today = datetime.now().date()
            
            if delivery_date < today:
                errors["delivery_date"] = "A beszállítási dátum nem lehet múltbeli."
            elif delivery_date == today:
                warnings["delivery_date"] = "A beszállítás a mai napra van ütemezve. Biztos benne?"
            elif delivery_date > today + timedelta(days=365):
                warnings["delivery_date"] = "A beszállítási dátum több mint egy év múlva van."
        except ValueError:
            errors["delivery_date"] = "Érvénytelen dátumformátum. Használja az ÉÉÉÉ-HH-NN formátumot."
    
    # Megjegyzés hosszának ellenőrzése
    if "note" in offer_data and offer_data.get("note") and len(offer_data["note"]) > 500:
        errors["note"] = "A megjegyzés túl hosszú (maximum 500 karakter)."
    
    # Termelő ellenőrzése új ajánlatnál
    if is_new and "user_id" in offer_data:
        if not offer_data["user_id"]:
            errors["user_id"] = "A termelő kiválasztása kötelező új ajánlatnál."
    
    # Érvényesség meghatározása
    is_valid = len(errors) == 0
    
    return is_valid, errors, warnings


def render_quantity_input(label, key, value=None, min_value=0.1, max_value=10000.0, step=0.1, help=None):
    """
    Mennyiség beviteli mező renderelése beépített validációval.
    
    Ez a függvény egy mennyiség beviteli mezőt jelenít meg valós idejű validációval
    és vizuális visszajelzéssel a felhasználó számára.
    
    Args:
        label (str): A mező címkéje.
        key (str): Egyedi kulcs a komponenshez.
        value (float, optional): Alapértelmezett érték. Defaults to None.
        min_value (float, optional): Minimum megengedett érték. Defaults to 0.1.
        max_value (float, optional): Maximum megengedett érték. Defaults to 10000.0.
        step (float, optional): Lépésköz. Defaults to 0.1.
        help (str, optional): Segítő szöveg. Defaults to None.
        
    Returns:
        float/None: A megadott mennyiség vagy None, ha érvénytelen.
    """
    # Ha nincs megadva value, üres stringet használunk kezdőértéknek
    if value is None:
        value = ""
    
    # Előző érték nyilvántartása a session state-ben
    if f"{key}_prev" not in st.session_state:
        st.session_state[f"{key}_prev"] = value
    
    # Hiba állapot nyilvántartása
    if f"{key}_error" not in st.session_state:
        st.session_state[f"{key}_error"] = ""
    
    # A beviteli mező megjelenítése
    input_container = st.container()
    with input_container:
        input_col, error_col = st.columns([3, 2])
        
        # Beviteli mező saját column-ban
        with input_col:
            quantity_str = st.text_input(
                label=label,
                value=value,
                key=key,
                help=help or f"Adjon meg egy számot {min_value} és {max_value} között."
            )
        
        # Validáció
        quantity = None
        if quantity_str:
            try:
                # Vessző helyettesítése ponttal
                quantity_str = quantity_str.replace(',', '.')
                quantity = float(quantity_str)
                
                # Érték tartomány ellenőrzése
                if quantity < min_value:
                    st.session_state[f"{key}_error"] = f"A minimum érték {min_value}."
                elif quantity > max_value:
                    st.session_state[f"{key}_error"] = f"A maximum érték {max_value}."
                else:
                    st.session_state[f"{key}_error"] = ""
                    st.session_state[f"{key}_prev"] = quantity
            except ValueError:
                st.session_state[f"{key}_error"] = "A mennyiségnek számnak kell lennie."
                quantity = None
        else:
            st.session_state[f"{key}_error"] = "Ez a mező kötelező."
            quantity = None
        
        # Hibaüzenet megjelenítése
        with error_col:
            if st.session_state[f"{key}_error"]:
                st.markdown(f'<div style="color: red; margin-top: 25px;">{st.session_state[f"{key}_error"]}</div>', unsafe_allow_html=True)
        
    return quantity

# Az inline hibajelzés komponensek áthelyezve ui_components.py modulba
# Importálás az egységes használathoz, de kerüljük a körkörös importálást
# Inkább definiáljuk újra ezeket a segédfüggvényeket, ha szükséges
import streamlit as st

def show_inline_error(error_message):
    """Egyszerű inline hibaüzenet megjelenítése"""
    if error_message:
        st.error(error_message)

def show_inline_warning(warning_message):
    """Egyszerű inline figyelmeztető üzenet megjelenítése"""
    if warning_message:
        st.warning(warning_message)

def show_inline_success(success_message):
    """Egyszerű inline sikeres művelet üzenet megjelenítése"""
    if success_message:
        st.success(success_message)

def show_inline_info(info_message):
    """Egyszerű inline információs üzenet megjelenítése"""
    if info_message:
        st.info(info_message)

def parse_status_for_display(status_str):
    """
    Státusz szöveg formázása emberileg olvasható formátumra.
    
    Args:
        status_str (str): Az eredeti státusz string
        
    Returns:
        str: A felhasználóbarát formázott státusz
    """
    if not status_str:
        return "Ismeretlen"
        
    # Státusz átnevezések
    status_names = {
        "CREATED": "Létrehozva",
        "CONFIRMED_BY_COMPANY": "Megerősítve",
        "ACCEPTED_BY_USER": "Elfogadva",
        "REJECTED_BY_USER": "Elutasítva",
        "FINALIZED": "Véglegesítve",
        "pending": "Függőben",
        "approved": "Jóváhagyva",
        "rejected": "Elutasítva",
        "in_progress": "Folyamatban",
        "completed": "Teljesítve",
        "canceled": "Törölve"
    }
    
    # Ha a státusz közvetlenül megtalálható a szótárban
    if status_str in status_names:
        return status_names[status_str]
    
    # Próbáljuk meg a snake_case formátumot átalakítani
    if "_" in status_str:
        parts = status_str.split("_")
        return " ".join(part.capitalize() for part in parts)
    
    # Próbáljuk meg a SCREAMING_SNAKE_CASE formátumot átalakítani
    if status_str.isupper() and "_" in status_str:
        parts = status_str.split("_")
        return " ".join(part.capitalize() for part in parts)
    
    # Egyébként adjuk vissza az eredeti stringet első betűvel nagybetűvé alakítva
    return status_str[0].upper() + status_str[1:] if status_str else "Ismeretlen"

def format_currency(amount, currency="Ft"):
    """
    Pénzösszeg formázása olvasható formátumra.
    
    Args:
        amount (float/int): Az összeg
        currency (str, optional): A pénznem. Defaults to "Ft".
        
    Returns:
        str: A formázott összeg
    """
    if amount is None:
        return "-"
    
    try:
        # Egész számként formázzuk, ha nincs tizedes része
        if float(amount).is_integer():
            return f"{int(amount):,} {currency}".replace(",", " ")
        else:
            return f"{float(amount):,.2f} {currency}".replace(",", " ")
    except (ValueError, TypeError):
        return f"{amount} {currency}"

def is_valid_email(email):
    """
    Email cím validálása.
    
    Args:
        email (str): Az ellenőrizendő email cím
        
    Returns:
        bool: True, ha érvényes, False, ha nem
    """
    # Egyszerű regex az email formátum ellenőrzésére
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_valid_phone(phone):
    """
    Telefonszám validálása.
    
    Args:
        phone (str): Az ellenőrizendő telefonszám
        
    Returns:
        bool: True, ha érvényes, False, ha nem
    """
    # Megtisztítjuk a szóközöktől, kötőjelektől, zárójelektől
    phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Ellenőrizzük, hogy csak számjegyeket, és esetleg + jelet tartalmaz-e
    pattern = r'^\+?[0-9]{7,15}$'
    return re.match(pattern, phone) is not None

def estimate_read_time(text, words_per_minute=200):
    """
    Szöveg olvasási idejének becslése.
    
    Args:
        text (str): A szöveg
        words_per_minute (int, optional): Percenkénti szavak száma. Defaults to 200.
        
    Returns:
        str: A becsült olvasási idő (pl. "2 perc")
    """
    if not text:
        return "kevesebb, mint 1 perc"
    
    # Szavak megszámolása
    words = len(text.split())
    
    # Olvasási idő percben
    minutes = max(1, round(words / words_per_minute))
    
    if minutes == 1:
        return "kb. 1 perc"
    else:
        return f"kb. {minutes} perc"

def get_time_ago(date_time):
    """
    Visszaadja, hogy mennyi ideje történt egy esemény.
    
    Args:
        date_time (datetime): Az esemény időpontja
        
    Returns:
        str: Az eltelt idő szöveges formátumban (pl. "2 órája")
    """
    if not date_time:
        return "ismeretlen időpontban"
    
    now = datetime.now()
    diff = now - date_time
    
    if diff.days > 365:
        years = diff.days // 365
        return f"{years} éve"
    elif diff.days > 30:
        months = diff.days // 30
        return f"{months} hónapja"
    elif diff.days > 0:
        return f"{diff.days} napja"
    elif diff.seconds // 3600 > 0:
        hours = diff.seconds // 3600
        return f"{hours} órája"
    elif diff.seconds // 60 > 0:
        minutes = diff.seconds // 60
        return f"{minutes} perce"
    else:
        return "éppen most"

def format_file_size(size_bytes):
    """
    Fájlméret formázása emberileg olvasható formátumra.
    
    Args:
        size_bytes (int): A fájlméret bájtokban
        
    Returns:
        str: A formázott fájlméret (pl. "2.5 MB")
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def generate_unique_id(prefix=""):
    """
    Egyedi azonosító generálása.
    
    Args:
        prefix (str, optional): Az azonosító előtagja. Defaults to "".
        
    Returns:
        str: Az egyedi azonosító
    """
    import uuid
    return f"{prefix}{uuid.uuid4().hex[:8]}"