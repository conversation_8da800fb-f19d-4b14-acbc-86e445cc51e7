"""
Modern React-like components for the offer management system.
These components use the new component system to create reusable, stateful UI elements.
"""
import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional, Callable
import logging

from .react_like_components import (
    BaseComponent, 
    create_component, 
    with_props,
    component_registry
)

# Import performance optimizations
try:
    from .performance_optimization import (
        cached_function,
        performance_monitor,
        memoize_component_state,
        optimize_large_list_rendering
    )
    from .keyboard_accessibility import (
        accessibility_manager,
        announce_filter_change,
        announce_data_loaded
    )
    performance_available = True
except ImportError:
    performance_available = False
    def cached_function(ttl=300, use_session_state=True):
        def decorator(func): return func
        return decorator
    def memoize_component_state(comp_id, state_key, factory_func, ttl=300):
        return factory_func()
    def optimize_large_list_rendering(items, window_size=20, renderer=None):
        for i, item in enumerate(items[:window_size]):
            if renderer: renderer(item, i)
    def announce_filter_change(name, value): pass
    def announce_data_loaded(count, data_type="ajánlat"): pass

# Import state management hooks
try:
    from .state_hooks import (
        use_filters,
        use_offers,
        use_ui_state,
        use_statistics,
        use_notifications,
        use_theme,
        with_state_management
    )
    state_hooks_available = True
except ImportError:
    state_hooks_available = False
    # Create mock hooks
    def use_filters(component_id=None): return {}, {}
    def use_offers(component_id=None): return [], {}
    def use_ui_state(component_id=None): return {}, {}
    def use_statistics(component_id=None): return {}
    def use_notifications(component_id=None): return [], {}
    def use_theme(component_id=None): return "light", lambda x: None
    def with_state_management(hooks): return lambda f: f

logger = logging.getLogger(__name__)

# Try to import formatting utilities
try:
    from streamlit_app.utils.formatting import format_status, format_price, format_quantity, format_date
except ImportError:
    try:
        from utils.formatting import format_status, format_price, format_quantity, format_date
    except ImportError:
        # Fallback formatting functions
        format_status = lambda x: str(x) if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f} kg" if x else "-"
        format_date = lambda x: x.strftime("%Y-%m-%d") if x else "-"

class StatefulFilterPanel(BaseComponent):
    """
    State-aware filter panel that automatically syncs with global state.
    """
    def render(self):
        title = self.props.get('title', '🔍 Szűrők')
        
        # Use state hooks
        if state_hooks_available:
            filters, filter_actions = use_filters(self.component_id)
            ui_state, ui_actions = use_ui_state(self.component_id)
        else:
            filters = self.props.get('filters', {})
            filter_actions = {}
            ui_state = {'theme': 'light'}
            ui_actions = {}
        
        # Component state for UI interactions
        is_expanded, set_expanded = self.use_state(
            ui_state.get('filter_panel_expanded', True), 
            'expanded'
        )
        
        # Theme-aware styling
        theme = ui_state.get('theme', 'light')
        theme_colors = self._get_theme_colors(theme)
        
        # Custom CSS with theme support
        self._inject_themed_css(theme_colors)
        
        # Filter panel container
        st.markdown('<div class="modern-filter-panel">', unsafe_allow_html=True)
        
        # Header with toggle
        self._render_header(title, is_expanded, set_expanded, filters)
        
        if is_expanded:
            st.markdown('<div class="filter-body">', unsafe_allow_html=True)
            
            # Render filter sections with state management
            self._render_stateful_filters(filters, filter_actions)
            
            # Active filters display
            self._render_active_filters(filters, filter_actions)
            
            # Action buttons
            self._render_stateful_actions(filter_actions)
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _get_theme_colors(self, theme: str) -> Dict[str, str]:
        """Get color scheme based on theme."""
        if theme == 'dark':
            return {
                'background': '#1e1e1e',
                'surface': '#2d2d2d', 
                'primary': '#bb86fc',
                'text': '#ffffff',
                'text_secondary': '#b3b3b3',
                'border': '#404040'
            }
        else:
            return {
                'background': '#ffffff',
                'surface': '#f8f9fa',
                'primary': '#667eea',
                'text': '#1a1a1a',
                'text_secondary': '#666666',
                'border': '#e1e5e9'
            }
    
    def _inject_themed_css(self, colors: Dict[str, str]):
        """Inject theme-aware CSS."""
        st.markdown(f"""
        <style>
        .modern-filter-panel {{
            background: {colors['background']};
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid {colors['border']};
            margin: 1rem 0;
            overflow: hidden;
        }}
        .filter-header {{
            background: linear-gradient(135deg, {colors['primary']} 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }}
        .filter-body {{
            padding: 1.5rem;
            background: {colors['background']};
            color: {colors['text']};
        }}
        .filter-section {{
            margin-bottom: 1.5rem;
        }}
        .active-filter-badge {{
            display: inline-block;
            background: {colors['surface']};
            color: {colors['primary']};
            padding: 0.25rem 0.75rem;
            border-radius: 16px;
            font-size: 0.875rem;
            margin: 0.25rem 0.25rem 0.25rem 0;
            border: 1px solid {colors['border']};
        }}
        </style>
        """, unsafe_allow_html=True)
    
    def _render_stateful_filters(self, filters: Dict, filter_actions: Dict):
        """Render filters with state management."""
        # Producer filter
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**👤 Termelő**")
        
        producer_options = ["Kovács János", "Szabó Péter", "Nagy István", "Tóth Mária"]
        current_producer = filters.get('producer')
        
        selected_producer = st.selectbox(
            "Válasszon termelőt",
            [""] + producer_options,
            index=producer_options.index(current_producer) + 1 if current_producer in producer_options else 0,
            key=f"{self.component_id}_producer",
            label_visibility="collapsed"
        )
        
        if selected_producer != current_producer:
            if 'set_producer' in filter_actions:
                filter_actions['set_producer'](selected_producer or None)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Status filter with state management
        self._render_status_filter_stateful(filters, filter_actions)
        
        # Date filter with state management
        self._render_date_filter_stateful(filters, filter_actions)
        
        # Search filter with state management
        self._render_search_filter_stateful(filters, filter_actions)
    
    def _render_status_filter_stateful(self, filters: Dict, filter_actions: Dict):
        """Render status filter with state management."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**📊 Státusz**")
        
        status_options = {
            "CREATED": "Létrehozva",
            "CONFIRMED_BY_COMPANY": "Megerősítve", 
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Véglegesítve"
        }
        
        current_status = filters.get('status')
        
        selected_status = st.selectbox(
            "Válasszon státuszt",
            [""] + list(status_options.keys()),
            index=list(status_options.keys()).index(current_status) + 1 if current_status in status_options else 0,
            format_func=lambda x: status_options.get(x, "Nincs szűrés") if x else "Nincs szűrés",
            key=f"{self.component_id}_status",
            label_visibility="collapsed"
        )
        
        if selected_status != current_status:
            if 'set_status' in filter_actions:
                filter_actions['set_status'](selected_status or None)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_date_filter_stateful(self, filters: Dict, filter_actions: Dict):
        """Render date filter with state management."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**📅 Időszak**")
        
        date_range = filters.get('date_range', {})
        current_from_date = date_range.get('from_date', date.today() - timedelta(days=30))
        current_to_date = date_range.get('to_date', date.today())
        
        col1, col2 = st.columns(2)
        
        with col1:
            from_date = st.date_input(
                "Kezdő dátum",
                value=current_from_date,
                key=f"{self.component_id}_from_date",
                label_visibility="collapsed"
            )
            
        with col2:
            to_date = st.date_input(
                "Végső dátum",
                value=current_to_date,
                key=f"{self.component_id}_to_date",
                label_visibility="collapsed"
            )
        
        if from_date != current_from_date or to_date != current_to_date:
            if 'set_date_range' in filter_actions:
                filter_actions['set_date_range'](from_date, to_date)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_search_filter_stateful(self, filters: Dict, filter_actions: Dict):
        """Render search filter with state management."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**🔍 Keresés**")
        
        current_search = filters.get('search', '')
        
        search_term = st.text_input(
            "Keresés ajánlatokban...",
            value=current_search,
            key=f"{self.component_id}_search",
            placeholder="Termék neve, megjegyzés, stb.",
            label_visibility="collapsed"
        )
        
        if search_term != current_search:
            if 'set_search' in filter_actions:
                filter_actions['set_search'](search_term)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_stateful_actions(self, filter_actions: Dict):
        """Render action buttons with state management."""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Alaphelyzet", key=f"{self.component_id}_reset"):
                if 'clear_all' in filter_actions:
                    filter_actions['clear_all']()
                    
        with col2:
            if st.button("💾 Szűrő mentése", key=f"{self.component_id}_save"):
                st.info("Szűrő mentése funkció (fejlesztés alatt)")
                
        with col3:
            if st.button("🔍 Keresés", key=f"{self.component_id}_search_btn"):
                st.rerun()

class ModernFilterPanel(BaseComponent):
    """
    Modern filter panel component with collapsible sections and state management.
    Implements the design specification from UI_IMPLEMENTATION_PLAN.md
    """
    def render(self):
        title = self.props.get('title', '🔍 Ajánlatok szűrése')
        filters = self.props.get('filters', {})
        on_filter_change = self.props.get('on_filter_change', lambda x: None)
        available_producers = self.props.get('available_producers', [])
        available_products = self.props.get('available_products', [])
        saved_presets = self.props.get('saved_presets', [])
        
        # Component state
        is_expanded, set_expanded = self.use_state(True, 'expanded')
        active_filters, set_active_filters = self.use_state({}, 'active_filters')
        results_count, set_results_count = self.use_state(0, 'results_count')
        
        # Update active filters when filters prop changes
        self.use_effect(
            lambda: set_active_filters(filters),
            [filters],
            'sync_filters'
        )
        
        # Device detection for responsive layout
        device_type = self._detect_device_type()
        layout_config = self._get_layout_config(device_type)
        
        # Enhanced CSS for modern styling with keyboard navigation support
        st.markdown("""
        <style>
        .modern-filter-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* Keyboard navigation styles with animations */
        .modern-filter-panel *:focus {
            outline: 2px solid #1a73e8 !important;
            outline-offset: 2px !important;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.2) !important;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .keyboard-focused {
            outline: 2px solid #1a73e8 !important;
            outline-offset: 2px !important;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.2) !important;
            transform: translateY(-1px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .filter-section[tabindex="0"]:focus {
            background: #f8f9ff;
            border-color: #1a73e8;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Enhanced animations and transitions */
        .modern-filter-panel {
            animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInFromTop {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .filter-section {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .filter-section:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Enhanced Responsive Design */
        @media (max-width: 767px) {
            /* Mobile styles */
            .modern-filter-panel {
                margin: 0.5rem 0;
                border-radius: 8px;
            }
            
            .filter-header {
                padding: 1rem;
                font-size: 0.9rem;
            }
            
            .filter-body {
                padding: 1rem;
            }
            
            .filter-section {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }
            
            .status-checkboxes {
                grid-template-columns: 1fr;
                gap: 0.375rem;
            }
            
            .action-buttons {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }
            
            .metrics-row {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.75rem;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
        
        @media (min-width: 768px) and (max-width: 991px) {
            /* Tablet styles */
            .modern-filter-panel {
                margin: 0.75rem 0;
                border-radius: 10px;
            }
            
            .filter-header {
                padding: 1.125rem;
            }
            
            .filter-body {
                padding: 1.25rem;
            }
            
            .status-checkboxes {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .action-buttons {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .metrics-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 992px) {
            /* Desktop styles */
            .status-checkboxes {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            }
            
            .action-buttons {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .metrics-row {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        /* Touch-friendly enhancements for mobile */
        @media (max-width: 767px) and (pointer: coarse) {
            .action-btn, button {
                min-height: 44px;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
            
            input, select, textarea {
                min-height: 44px;
                padding: 0.75rem;
                font-size: 1rem;
            }
            
            .filter-clear-btn {
                min-width: 44px;
                min-height: 44px;
                padding: 0.5rem;
            }
        }
        
        .filter-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.25rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .filter-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .filter-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .filter-header:hover::before {
            left: 100%;
        }
        
        .filter-header:active {
            transform: translateY(0);
            transition: transform 0.1s ease;
        }
        .filter-body {
            padding: 1.5rem;
            background: #fafbfc;
        }
        .filter-section {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e8eaed;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .filter-section:last-child {
            margin-bottom: 0;
        }
        .filter-section-title {
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }
        .status-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        .status-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 6px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .status-checkbox:hover {
            background: #e9ecef;
            border-color: #dee2e6;
        }
        .status-checkbox.checked {
            background: #e3f2fd;
            border-color: #1976d2;
            color: #1976d2;
        }
        .date-range-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        .date-range-slider {
            width: 100%;
            margin: 0.5rem 0;
        }
        .date-range-display {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: #666;
            margin-top: 0.5rem;
        }
        .search-container {
            display: flex;
            gap: 0.5rem;
            align-items: stretch;
        }
        .search-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        .search-button {
            padding: 0.75rem 1rem;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: background 0.2s ease;
        }
        .search-button:hover {
            background: #1557b0;
        }
        .active-filters-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border: 1px solid #e9ecef;
        }
        .active-filter-badge {
            display: inline-flex;
            align-items: center;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            margin: 0.25rem 0.25rem 0.25rem 0;
            border: 1px solid #bbdefb;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: slideInFromLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-20px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }
        
        .active-filter-badge:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
            background: #bbdefb;
        }
        
        .filter-clear-btn {
            background: #ff5722;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 0.5rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .filter-clear-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translate(-50%, -50%);
        }
        
        .filter-clear-btn:hover {
            background: #e64a19;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
        }
        
        .filter-clear-btn:hover::before {
            width: 40px;
            height: 40px;
        }
        
        .filter-clear-btn:active {
            transform: scale(0.95);
        }
        .results-count {
            font-weight: 600;
            color: #1a73e8;
            background: #e8f0fe;
            padding: 0.75rem;
            border-radius: 6px;
            text-align: center;
            margin-top: 1rem;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 0.75rem;
            margin-top: 1.5rem;
        }
        .action-btn {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }
        
        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translate(-50%, -50%);
        }
        
        .action-btn:hover::before {
            width: 300px;
            height: 300px;
        }
        
        .action-btn-primary {
            background: #1a73e8;
            color: white;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
        }
        
        .action-btn-primary:hover {
            background: #1557b0;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 115, 232, 0.4);
        }
        
        .action-btn-primary:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
        }
        
        .action-btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #1a73e8;
        }
        
        .action-btn-secondary:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .active-count-badge {
            background: #ff5722;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            min-width: 1.5rem;
            text-align: center;
        }
        .refresh-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Filter panel container with ARIA attributes and keyboard support
        st.markdown(f'''
        <div class="modern-filter-panel" 
             role="region" 
             aria-label="Ajánlatok szűrése" 
             data-component-id="{self.component_id}"
             tabindex="0">
        ''', unsafe_allow_html=True)
        
        # Inject keyboard navigation support
        self._inject_keyboard_navigation()
        
        # Enhanced header with refresh button and active count
        self._render_enhanced_header(title, is_expanded, set_expanded, active_filters)
        
        if is_expanded:
            st.markdown('<div class="filter-body" role="form" aria-label="Szűrő beállítások">', unsafe_allow_html=True)
            
            # Render filter sections according to design spec
            self._render_enhanced_producer_filter(active_filters, on_filter_change, available_producers)
            self._render_enhanced_status_filter(active_filters, on_filter_change)
            self._render_enhanced_date_filter(active_filters, on_filter_change)
            self._render_enhanced_product_filter(active_filters, on_filter_change, available_products)
            self._render_enhanced_search_filter(active_filters, on_filter_change)
            self._render_saved_filters_section(active_filters, on_filter_change, saved_presets)
            
            # Active filters display
            self._render_enhanced_active_filters(active_filters, on_filter_change)
            
            # Results count display
            self._render_results_count(results_count)
            
            # Enhanced action buttons
            self._render_enhanced_action_buttons(active_filters, on_filter_change)
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_enhanced_header(self, title: str, is_expanded: bool, set_expanded: Callable, active_filters: Dict):
        """Render enhanced header with refresh button and active count according to design spec."""
        st.markdown('<div class="filter-header">', unsafe_allow_html=True)
        
        # Create columns for layout
        col1, col2 = st.columns([8, 1])
        
        with col1:
            # Header title with expand/collapse toggle
            if st.button(
                f"{title} {'▼' if is_expanded else '▶'}",
                key=f"{self.component_id}_toggle",
                help="Szűrők megjelenítése/elrejtése",
                use_container_width=False
            ):
                set_expanded(not is_expanded)
        
        with col2:
            # Header actions container
            actions_col1, actions_col2 = st.columns(2)
            
            with actions_col1:
                # Refresh button
                if st.button("🔄", key=f"{self.component_id}_refresh", help="Szűrők frissítése"):
                    st.rerun()
                    
            with actions_col2:
                # Active filter count badge
                active_count = len([v for v in active_filters.values() if v is not None and v != "" and v != []])
                if active_count > 0:
                    st.markdown(f'<span class="active-count-badge">{active_count}</span>', unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_producer_filter(self, filters: Dict, on_change: Callable, available_producers: List):
        """Render enhanced producer filter with search and accessibility according to design spec."""
        st.markdown('<div class="filter-section" role="group" aria-labelledby="producer-filter-title" tabindex="0">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title" id="producer-filter-title">👤 Termelő</div>', unsafe_allow_html=True)
        
        # Use available producers or fallback to demo data
        producer_options = available_producers if available_producers else [
            "Kovács János", "Szabó Péter", "Nagy István", "Tóth Mária", "Horváth Anna", "Németh Zoltán"
        ]
        
        selected_producer = st.selectbox(
            "Válasszon termelőt",
            ["Válasszon termelőt ▼"] + producer_options,
            index=0 if not filters.get('producer') else producer_options.index(filters.get('producer')) + 1 if filters.get('producer') in producer_options else 0,
            key=f"{self.component_id}_producer_enhanced",
            label_visibility="collapsed"
        )
        
        if selected_producer != filters.get('producer', '') and selected_producer != "Válasszon termelőt ▼":
            new_filters = filters.copy()
            new_filters['producer'] = selected_producer if selected_producer != "Válasszon termelőt ▼" else None
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_status_filter(self, filters: Dict, on_change: Callable):
        """Render enhanced status filter with checkboxes and accessibility according to design spec."""
        st.markdown('<div class="filter-section" role="group" aria-labelledby="status-filter-title" tabindex="0">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title" id="status-filter-title">📊 Státusz</div>', unsafe_allow_html=True)
        
        status_options = {
            "CREATED": "✓ Létrehozva",
            "CONFIRMED_BY_COMPANY": "✓ Megerősítve",
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Véglegesítve"
        }
        
        current_statuses = filters.get('statuses', [])
        if isinstance(current_statuses, str):
            current_statuses = [current_statuses] if current_statuses else []
        
        # Create a two-column layout for checkboxes as per design
        col1, col2 = st.columns(2)
        
        selected_statuses = []
        
        with col1:
            for i, (status_key, status_label) in enumerate(list(status_options.items())[:3]):
                if st.checkbox(
                    status_label,
                    value=status_key in current_statuses,
                    key=f"{self.component_id}_status_{status_key}"
                ):
                    selected_statuses.append(status_key)
                    
        with col2:
            for i, (status_key, status_label) in enumerate(list(status_options.items())[3:]):
                if st.checkbox(
                    status_label,
                    value=status_key in current_statuses,
                    key=f"{self.component_id}_status_{status_key}"
                ):
                    selected_statuses.append(status_key)
        
        # Include already selected statuses that weren't unchecked
        for status in current_statuses:
            if status in status_options and st.session_state.get(f"{self.component_id}_status_{status}", False):
                if status not in selected_statuses:
                    selected_statuses.append(status)
        
        if selected_statuses != current_statuses:
            new_filters = filters.copy()
            new_filters['statuses'] = selected_statuses
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_date_filter(self, filters: Dict, on_change: Callable):
        """Render enhanced date filter with slider according to design spec."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title">📅 Időszak</div>', unsafe_allow_html=True)
        
        # Date range with visual representation
        st.markdown('<div class="date-range-container">', unsafe_allow_html=True)
        
        current_from_date = filters.get('from_date', date.today() - timedelta(days=30))
        current_to_date = filters.get('to_date', date.today())
        
        # Date range inputs
        col1, col2 = st.columns(2)
        
        with col1:
            from_date = st.date_input(
                "Kezdő dátum",
                value=current_from_date,
                key=f"{self.component_id}_from_date_enhanced"
            )
            
        with col2:
            to_date = st.date_input(
                "Végső dátum", 
                value=current_to_date,
                key=f"{self.component_id}_to_date_enhanced"
            )
        
        # Visual date range display as per design spec
        st.markdown(f'''
        <div class="date-range-display">
            <span>{from_date.strftime("%Y.%m.%d")}</span>
            <span>{to_date.strftime("%Y.%m.%d")}</span>
        </div>
        ''', unsafe_allow_html=True)
        
        if from_date != current_from_date or to_date != current_to_date:
            new_filters = filters.copy()
            new_filters['from_date'] = from_date
            new_filters['to_date'] = to_date
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_product_filter(self, filters: Dict, on_change: Callable, available_products: List):
        """Render enhanced product type filter according to design spec."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title">🍎 Termék</div>', unsafe_allow_html=True)
        
        # Use available products or fallback to demo data
        product_options = available_products if available_products else [
            "Alma", "Szőlő", "Burgonya", "Paradicsom", "Hagyma", "Répa"
        ]
        
        selected_product = st.selectbox(
            "Válasszon termék típust",
            ["Válasszon termék típust ▼"] + product_options,
            index=0 if not filters.get('product_type') else product_options.index(filters.get('product_type')) + 1 if filters.get('product_type') in product_options else 0,
            key=f"{self.component_id}_product_enhanced",
            label_visibility="collapsed"
        )
        
        if selected_product != filters.get('product_type', '') and selected_product != "Válasszon termék típust ▼":
            new_filters = filters.copy()
            new_filters['product_type'] = selected_product if selected_product != "Válasszon termék típust ▼" else None
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_search_filter(self, filters: Dict, on_change: Callable):
        """Render enhanced search filter with button according to design spec."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title">🔍 Keresés</div>', unsafe_allow_html=True)
        
        # Search container with input and button
        col1, col2 = st.columns([4, 1])
        
        with col1:
            search_term = st.text_input(
                "Keresés",
                value=filters.get('search', ''),
                placeholder="Keresés ajánlatokban...",
                key=f"{self.component_id}_search_enhanced",
                label_visibility="collapsed"
            )
        
        with col2:
            search_button = st.button(
                "🔍 Keresés",
                key=f"{self.component_id}_search_btn_enhanced",
                help="Keresés indítása"
            )
        
        if search_term != filters.get('search', '') or search_button:
            new_filters = filters.copy()
            new_filters['search'] = search_term
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_saved_filters_section(self, filters: Dict, on_change: Callable, saved_presets: List):
        """Render saved filters section according to design spec."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title">💾 Mentett szűrők</div>', unsafe_allow_html=True)
        
        # Saved filters dropdown
        preset_options = saved_presets if saved_presets else ["Mai ajánlatok", "Heti összesítő", "Függőben lévő"]
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            selected_preset = st.selectbox(
                "Mentett szűrők",
                ["Válasszon mentett szűrőt ▼"] + preset_options,
                key=f"{self.component_id}_saved_filter",
                label_visibility="collapsed"
            )
        
        with col2:
            if st.button("💾 Mentés", key=f"{self.component_id}_save_filter", help="Aktuális szűrők mentése"):
                st.info("Szűrő mentése funkció (fejlesztés alatt)")
        
        if selected_preset and selected_preset != "Válasszon mentett szűrőt ▼":
            # Load saved filter (implementation would load actual saved filter data)
            st.info(f"'{selected_preset}' szűrő betöltése (fejlesztés alatt)")
            
        st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_enhanced_active_filters(self, filters: Dict, on_change: Callable):
        """Render enhanced active filters display with remove buttons according to design spec."""
        active_filters = [(k, v) for k, v in filters.items() if v and v != "" and v != []]
        
        if active_filters:
            st.markdown('<div class="active-filters-section">', unsafe_allow_html=True)
            st.markdown("**🏷️ Aktív szűrők:**")
            
            # Render filter badges with remove buttons
            for key, value in active_filters:
                display_value = self._format_filter_value(key, value)
                
                col1, col2 = st.columns([10, 1])
                with col1:
                    st.markdown(f'<span class="active-filter-badge">{display_value}</span>', unsafe_allow_html=True)
                with col2:
                    if st.button("✕", key=f"{self.component_id}_clear_{key}", help=f"{key} szűrő eltávolítása"):
                        new_filters = filters.copy()
                        new_filters[key] = None if key != 'statuses' else []
                        on_change(new_filters)
                        st.rerun()
            
            st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_results_count(self, count: int):
        """Render results count display according to design spec.""" 
        st.markdown(f'''
        <div class="results-count">
            Találatok: {count} ajánlat
        </div>
        ''', unsafe_allow_html=True)
    
    def _render_enhanced_action_buttons(self, filters: Dict, on_change: Callable):
        """Render enhanced action buttons with filter presets according to design spec."""
        st.markdown('<div class="action-buttons">', unsafe_allow_html=True)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            # Add custom HTML button with data-testid for keyboard shortcuts
            st.markdown(f'''
            <button class="action-btn action-btn-secondary" 
                    data-testid="clear-filters"
                    onclick="document.getElementById('{self.component_id}_reset_enhanced').click()"
                    aria-label="Összes szűrő törlése (Alt+C)">
                🔄 Alaphelyzet
            </button>
            ''', unsafe_allow_html=True)
            
            if st.button("", key=f"{self.component_id}_reset_enhanced", help="Összes szűrő törlése", label_visibility="collapsed"):
                on_change({})
                
        with col2:
            st.markdown(f'''
            <button class="action-btn action-btn-primary" 
                    data-testid="save-filters"
                    onclick="document.getElementById('{self.component_id}_save_enhanced').click()"
                    aria-label="Aktuális szűrők mentése (Alt+S)">
                💾 Mentés
            </button>
            ''', unsafe_allow_html=True)
            
            if st.button("", key=f"{self.component_id}_save_enhanced", help="Aktuális szűrők mentése", label_visibility="collapsed"):
                self._show_save_preset_modal()
                
        with col3:
            if st.button("⚙️ Kezelés", key=f"{self.component_id}_manage_enhanced", help="Mentett szűrők kezelése", type="secondary"):
                self._show_manage_presets_modal()
                
        with col4:
            if st.button("🔍 Keresés", key=f"{self.component_id}_search_action", help="Keresés indítása aktív szűrőkkel", type="primary"):
                st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Render saved filter presets quick access
        self._render_saved_presets_quick_access()
        
        # Handle modal states
        self._handle_preset_modals(filters, on_change)

    def _render_producer_filter(self, filters: Dict, on_change: Callable):
        """Render producer filter section."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**👤 Termelő**")
        
        # Get producer options (this would come from API in real implementation)
        producer_options = ["Kovács János", "Szabó Péter", "Nagy István", "Tóth Mária"]
        
        selected_producer = st.selectbox(
            "Válasszon termelőt",
            [""] + producer_options,
            index=0 if not filters.get('producer') else producer_options.index(filters.get('producer')) + 1,
            key=f"{self.component_id}_producer",
            label_visibility="collapsed"
        )
        
        if selected_producer != filters.get('producer', ''):
            new_filters = filters.copy()
            new_filters['producer'] = selected_producer if selected_producer else None
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_status_filter(self, filters: Dict, on_change: Callable):
        """Render status filter section."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**📊 Státusz**")
        
        status_options = {
            "CREATED": "Létrehozva",
            "CONFIRMED_BY_COMPANY": "Megerősítve",
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Véglegesítve"
        }
        
        current_statuses = filters.get('statuses', [])
        if isinstance(current_statuses, str):
            current_statuses = [current_statuses] if current_statuses else []
            
        selected_statuses = st.multiselect(
            "Válasszon státuszokat",
            list(status_options.keys()),
            default=current_statuses,
            format_func=lambda x: status_options[x],
            key=f"{self.component_id}_status",
            label_visibility="collapsed"
        )
        
        if selected_statuses != current_statuses:
            new_filters = filters.copy()
            new_filters['statuses'] = selected_statuses
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_date_filter(self, filters: Dict, on_change: Callable):
        """Render date range filter section."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**📅 Időszak**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            from_date = st.date_input(
                "Kezdő dátum",
                value=filters.get('from_date', date.today() - timedelta(days=30)),
                key=f"{self.component_id}_from_date",
                label_visibility="collapsed"
            )
            
        with col2:
            to_date = st.date_input(
                "Végső dátum",
                value=filters.get('to_date', date.today()),
                key=f"{self.component_id}_to_date",
                label_visibility="collapsed"
            )
            
        if from_date != filters.get('from_date') or to_date != filters.get('to_date'):
            new_filters = filters.copy()
            new_filters['from_date'] = from_date
            new_filters['to_date'] = to_date
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_search_filter(self, filters: Dict, on_change: Callable):
        """Render search filter section."""
        st.markdown('<div class="filter-section">', unsafe_allow_html=True)
        st.markdown("**🔍 Keresés**")
        
        search_term = st.text_input(
            "Keresés ajánlatokban...",
            value=filters.get('search', ''),
            key=f"{self.component_id}_search",
            placeholder="Termék neve, megjegyzés, stb.",
            label_visibility="collapsed"
        )
        
        if search_term != filters.get('search', ''):
            new_filters = filters.copy()
            new_filters['search'] = search_term
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)
        
    def _render_active_filters(self, filters: Dict, on_change: Callable):
        """Render active filters display."""
        active_filters = [(k, v) for k, v in filters.items() if v and v != ""]
        
        if active_filters:
            st.markdown("**🏷️ Aktív szűrők:**")
            filter_html = ""
            
            for key, value in active_filters:
                display_value = self._format_filter_value(key, value)
                filter_html += f'''
                <span class="active-filter-badge">
                    {display_value}
                    <button class="filter-clear-btn" onclick="clearFilter('{key}')">✕</button>
                </span>
                '''
            
            st.markdown(filter_html, unsafe_allow_html=True)
            
            # JavaScript for clearing individual filters
            st.markdown(f"""
            <script>
            function clearFilter(filterKey) {{
                // This would need to be implemented with Streamlit's rerun mechanism
                console.log('Clearing filter:', filterKey);
            }}
            </script>
            """, unsafe_allow_html=True)
            
    def _render_action_buttons(self, filters: Dict, on_change: Callable):
        """Render action buttons."""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Alaphelyzet", key=f"{self.component_id}_reset"):
                on_change({})
                
        with col2:
            if st.button("💾 Szűrő mentése", key=f"{self.component_id}_save"):
                # This would open a modal to save the current filter configuration
                st.info("Szűrő mentése funkció (fejlesztés alatt)")
                
        with col3:
            if st.button("🔍 Keresés", key=f"{self.component_id}_search_btn"):
                # Trigger search with current filters
                st.rerun()
                
    def _format_filter_value(self, key: str, value: Any) -> str:
        """Format filter value for display."""
        if key == 'producer':
            return f"Termelő: {value}"
        elif key == 'statuses':
            if isinstance(value, list):
                return f"Státusz: {', '.join(value)}"
            return f"Státusz: {value}"
        elif key == 'from_date':
            return f"Kezdő: {format_date(value)}"
        elif key == 'to_date':
            return f"Végső: {format_date(value)}"
        elif key == 'search':
            return f"Keresés: {value}"
        else:
            return f"{key}: {value}"
    
    def _show_save_preset_modal(self):
        """Show modal for saving current filter as preset."""
        # Set modal state to trigger display
        st.session_state[f"{self.component_id}_show_save_modal"] = True
    
    def _show_manage_presets_modal(self):
        """Show modal for managing filter presets."""
        # Set modal state to trigger display
        st.session_state[f"{self.component_id}_show_manage_modal"] = True
    
    def _render_saved_presets_quick_access(self):
        """Render quick access bar for saved filter presets."""
        try:
            # Import the saved filter UI components
            from .saved_filter_ui import render_filter_quick_access_bar
            
            # Create callback for filter reload
            def on_reload():
                st.rerun()
            
            # Render the quick access bar
            render_filter_quick_access_bar(on_reload)
            
        except ImportError:
            # Fallback if saved_filter_ui is not available
            st.info("Mentett szűrők funkció nem elérhető")
        except Exception as e:
            logger.error(f"Error rendering saved presets quick access: {e}")
    
    def _handle_preset_modals(self, filters: Dict, on_change: Callable):
        """Handle preset modal states and interactions."""
        try:
            # Import the saved filter UI components
            from .saved_filter_ui import render_filter_management_modal
            from .filter_persistence import extract_current_filters, create_saved_filter
            
            # Save preset modal
            if st.session_state.get(f"{self.component_id}_show_save_modal", False):
                self._render_save_preset_modal_content(filters, on_change)
            
            # Manage presets modal 
            if st.session_state.get(f"{self.component_id}_show_manage_modal", False):
                render_filter_management_modal()
                
        except ImportError:
            # Fallback if modules are not available
            if st.session_state.get(f"{self.component_id}_show_save_modal", False):
                st.info("Szűrő mentése funkció nem elérhető")
                st.session_state[f"{self.component_id}_show_save_modal"] = False
            
            if st.session_state.get(f"{self.component_id}_show_manage_modal", False):
                st.info("Szűrők kezelése funkció nem elérhető")
                st.session_state[f"{self.component_id}_show_manage_modal"] = False
        except Exception as e:
            logger.error(f"Error handling preset modals: {e}")
    
    def _render_save_preset_modal_content(self, filters: Dict, on_change: Callable):
        """Render the save preset modal content."""
        st.markdown("### 💾 Szűrő mentése")
        
        with st.form(key=f"{self.component_id}_save_form"):
            filter_name = st.text_input("Szűrő nevének megadása:", key=f"{self.component_id}_name")
            filter_description = st.text_area("Leírás (opcionális):", key=f"{self.component_id}_desc")
            make_default = st.checkbox("Beállítás alapértelmezettként", key=f"{self.component_id}_default")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("💾 Mentés"):
                    if filter_name:
                        try:
                            # Import required functions
                            from .filter_persistence import extract_current_filters, create_saved_filter
                            
                            # Extract current filters
                            current_filters = extract_current_filters()
                            
                            # Create the saved filter
                            result, error = create_saved_filter(
                                name=filter_name,
                                filter_data=current_filters,
                                description=filter_description,
                                filter_type="offer",
                                is_default=make_default
                            )
                            
                            if error:
                                st.error(f"Hiba a szűrő mentése során: {error}")
                            else:
                                st.success(f"'{filter_name}' szűrő sikeresen elmentve!")
                                st.session_state[f"{self.component_id}_show_save_modal"] = False
                                st.rerun()
                                
                        except Exception as e:
                            st.error(f"Hiba történt: {e}")
                    else:
                        st.error("A szűrő nevének megadása kötelező!")
            
            with col2:
                if st.form_submit_button("❌ Mégse"):
                    st.session_state[f"{self.component_id}_show_save_modal"] = False
                    st.rerun()
    
    def _inject_keyboard_navigation(self):
        """Inject keyboard navigation JavaScript for the filter panel."""
        st.markdown(f"""
        <script>
        (function() {{
            const filterId = '{self.component_id}';
            const filterPanel = document.querySelector('[data-component-id="' + filterId + '"]');
            
            if (!filterPanel) return;
            
            // Add keyboard event listeners
            filterPanel.addEventListener('keydown', function(event) {{
                handleFilterPanelKeydown(event, filterId);
            }});
            
            function handleFilterPanelKeydown(event, filterId) {{
                const focusableElements = filterPanel.querySelectorAll(
                    'input, select, button, textarea, [tabindex]:not([tabindex="-1"])'
                );
                
                const currentFocusIndex = Array.from(focusableElements).indexOf(document.activeElement);
                
                switch(event.key) {{
                    case 'Tab':
                        // Enhanced tab navigation
                        if (event.shiftKey) {{
                            // Shift+Tab - go to previous
                            if (currentFocusIndex === 0) {{
                                event.preventDefault();
                                focusableElements[focusableElements.length - 1].focus();
                            }}
                        }} else {{
                            // Tab - go to next
                            if (currentFocusIndex === focusableElements.length - 1) {{
                                event.preventDefault();
                                focusableElements[0].focus();
                            }}
                        }}
                        break;
                        
                    case 'ArrowDown':
                        // Move to next focusable element
                        if (currentFocusIndex < focusableElements.length - 1) {{
                            event.preventDefault();
                            focusableElements[currentFocusIndex + 1].focus();
                        }}
                        break;
                        
                    case 'ArrowUp':
                        // Move to previous focusable element
                        if (currentFocusIndex > 0) {{
                            event.preventDefault();
                            focusableElements[currentFocusIndex - 1].focus();
                        }}
                        break;
                        
                    case 'Home':
                        // Move to first focusable element
                        event.preventDefault();
                        focusableElements[0].focus();
                        break;
                        
                    case 'End':
                        // Move to last focusable element
                        event.preventDefault();
                        focusableElements[focusableElements.length - 1].focus();
                        break;
                        
                    case 'Escape':
                        // Close filter panel or clear focus
                        event.preventDefault();
                        filterPanel.blur();
                        break;
                        
                    case 'Enter':
                    case ' ':
                        // Activate focused element if it's a button
                        const activeEl = document.activeElement;
                        if (activeEl && activeEl.tagName === 'BUTTON') {{
                            event.preventDefault();
                            activeEl.click();
                        }}
                        break;
                }}
            }}
            
            // Add keyboard shortcuts for filter actions
            document.addEventListener('keydown', function(event) {{
                // Alt+F to focus filter panel
                if (event.altKey && event.key === 'F') {{
                    event.preventDefault();
                    filterPanel.focus();
                }}
                
                // Alt+C to clear all filters
                if (event.altKey && event.key === 'C') {{
                    event.preventDefault();
                    const clearBtn = filterPanel.querySelector('[data-testid="clear-filters"]');
                    if (clearBtn) clearBtn.click();
                }}
                
                // Alt+S to save current filters
                if (event.altKey && event.key === 'S') {{
                    event.preventDefault();
                    const saveBtn = filterPanel.querySelector('[data-testid="save-filters"]');
                    if (saveBtn) saveBtn.click();
                }}
            }});
            
            // Announce filter changes for screen readers
            function announceFilterChange(filterName, value) {{
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.style.position = 'absolute';
                announcement.style.left = '-10000px';
                announcement.textContent = `Szűrő megváltozott: ${{filterName}}, új érték: ${{value}}`;
                document.body.appendChild(announcement);
                
                setTimeout(() => {{
                    document.body.removeChild(announcement);
                }}, 1000);
            }}
            
            // Export function for use by filter components
            window.announceFilterChange = announceFilterChange;
        }})();
        </script>
        """, unsafe_allow_html=True)
    
    def _detect_device_type(self):
        """Detect the current device type based on session state."""
        is_mobile = st.session_state.get('is_mobile', False)
        is_tablet = st.session_state.get('is_tablet', False) 
        screen_width = st.session_state.get('screen_width', 1200)
        
        if is_mobile:
            return 'mobile'
        elif is_tablet:
            return 'tablet'
        else:
            return 'desktop'
    
    def _get_layout_config(self, device_type):
        """Get layout configuration based on device type."""
        configs = {
            'mobile': {
                'columns_status': 1,
                'columns_actions': 2,
                'compact_mode': True,
                'show_icons_only': True,
                'touch_friendly': True
            },
            'tablet': {
                'columns_status': 2,
                'columns_actions': 3,
                'compact_mode': False,
                'show_icons_only': False,
                'touch_friendly': True
            },
            'desktop': {
                'columns_status': 2,
                'columns_actions': 4,
                'compact_mode': False,
                'show_icons_only': False,
                'touch_friendly': False
            }
        }
        return configs.get(device_type, configs['desktop'])
    
    def _render_responsive_status_filter(self, filters: Dict, on_change: Callable, layout_config: Dict):
        """Render status filter with responsive layout."""
        st.markdown('<div class="filter-section" role="group" aria-labelledby="status-filter-title" tabindex="0">', unsafe_allow_html=True)
        st.markdown('<div class="filter-section-title" id="status-filter-title">📊 Státusz</div>', unsafe_allow_html=True)
        
        status_options = {
            "CREATED": "✓ Létrehozva",
            "CONFIRMED_BY_COMPANY": "✓ Megerősítve",
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Véglegesítve"
        }
        
        current_statuses = filters.get('statuses', [])
        if isinstance(current_statuses, str):
            current_statuses = [current_statuses] if current_statuses else []
        
        # Adaptive column layout based on device type
        columns = layout_config['columns_status']
        if columns == 1:
            # Mobile: Single column
            selected_statuses = []
            for status_key, status_label in status_options.items():
                if st.checkbox(
                    status_label,
                    value=status_key in current_statuses,
                    key=f"{self.component_id}_status_{status_key}_responsive",
                    help=f"Szűrés {status_label} állapotú ajánlatokra"
                ):
                    selected_statuses.append(status_key)
        else:
            # Tablet/Desktop: Multi-column layout
            selected_statuses = []
            columns_list = st.columns(columns)
            
            for i, (status_key, status_label) in enumerate(status_options.items()):
                col_index = i % columns
                with columns_list[col_index]:
                    if st.checkbox(
                        status_label,
                        value=status_key in current_statuses,
                        key=f"{self.component_id}_status_{status_key}_responsive",
                        help=f"Szűrés {status_label} állapotú ajánlatokra"
                    ):
                        selected_statuses.append(status_key)
        
        # Update filters if changed
        if selected_statuses != current_statuses:
            new_filters = filters.copy()
            new_filters['statuses'] = selected_statuses
            on_change(new_filters)
            
        st.markdown('</div>', unsafe_allow_html=True)

class EnhancedOfferStatsCard(BaseComponent):
    """
    Enhanced statistics card component implementing the design specification.
    Features metrics display and interactive charts as per UI_IMPLEMENTATION_PLAN.md
    """
    def render(self):
        stats_data = self.props.get('stats_data', {})
        title = self.props.get('title', '📊 Statisztika')
        loading = self.props.get('loading', False)
        on_refresh = self.props.get('on_refresh', lambda: None)
        
        if loading:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem;
                border-radius: 12px;
                text-align: center;
                margin: 1rem 0;
            ">
                <div>📊 Statisztikák betöltése...</div>
            </div>
            """, unsafe_allow_html=True)
            return
        
        # Extract metrics from stats_data
        total_quantity = stats_data.get('total_quantity', 0)
        average_price = stats_data.get('average_price', 0)
        total_value = stats_data.get('total_value', 0)
        status_distribution = stats_data.get('status_distribution', {})
        product_distribution = stats_data.get('product_distribution', {})
        
        # Enhanced CSS for statistics card matching design spec
        st.markdown("""
        <style>
        .stats-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: slideInFromBottom 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes slideInFromBottom {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .stats-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats-body {
            padding: 1.5rem;
        }
        .metrics-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .metric-item {
            text-align: center;
            padding: 0.5rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .metric-item:hover {
            transform: translateY(-2px) scale(1.02);
            background: rgba(26, 115, 232, 0.05);
            box-shadow: 0 4px 12px rgba(26, 115, 232, 0.1);
        }
        
        .metric-value {
            font-size: 1.75rem;
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 0.25rem;
            display: block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: countUp 1s ease-out;
        }
        
        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .metric-value:hover {
            color: #1557b0;
            transform: scale(1.05);
        }
        .metric-label {
            font-size: 0.875rem;
            color: #666;
            font-weight: 500;
        }
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        /* Enhanced Responsive Design for Stats Card */
        @media (max-width: 767px) {
            /* Mobile styles */
            .stats-card {
                margin: 0.5rem 0;
                border-radius: 8px;
            }
            
            .stats-header {
                padding: 0.875rem 1rem;
                font-size: 1rem;
            }
            
            .stats-body {
                padding: 1rem;
            }
            
            .metrics-row {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.75rem;
                margin-bottom: 1.5rem;
            }
            
            .metric-value {
                font-size: 1.5rem;
            }
            
            .metric-label {
                font-size: 0.8rem;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .chart-container {
                min-height: 200px;
            }
        }
        
        @media (min-width: 768px) and (max-width: 991px) {
            /* Tablet styles */
            .stats-card {
                margin: 0.75rem 0;
                border-radius: 10px;
            }
            
            .stats-header {
                padding: 0.95rem 1.25rem;
                font-size: 1.05rem;
            }
            
            .stats-body {
                padding: 1.25rem;
            }
            
            .metrics-row {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.25rem;
                padding: 0.875rem;
            }
            
            .metric-value {
                font-size: 1.6rem;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
                gap: 1.25rem;
            }
        }
        
        @media (min-width: 992px) {
            /* Desktop styles */
            .metrics-row {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .charts-section {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        /* Touch-friendly enhancements for mobile */
        @media (max-width: 767px) and (pointer: coarse) {
            .stats-card {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            
            .metric-item {
                padding: 1rem 0.5rem;
                border-radius: 6px;
                background: rgba(26, 115, 232, 0.05);
                margin: 0.25rem 0;
            }
        }
            margin-top: 1rem;
        }
        .chart-container {
            background: #fafbfc;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #e8eaed;
        }
        .chart-title {
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 0.95rem;
        }
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            .metrics-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Stats card container
        st.markdown('<div class="stats-card">', unsafe_allow_html=True)
        
        # Header
        st.markdown(f'<div class="stats-header">{title}</div>', unsafe_allow_html=True)
        
        # Body
        st.markdown('<div class="stats-body">', unsafe_allow_html=True)
        
        # Summary metrics row as per design spec
        st.markdown(f'''
        <div class="metrics-row">
            <div class="metric-item">
                <span class="metric-value">{format_quantity(total_quantity)}</span>
                <span class="metric-label">Összes mennyiség</span>
            </div>
            <div class="metric-item">
                <span class="metric-value">{format_price(average_price)}</span>
                <span class="metric-label">Átlagár</span>
            </div>
            <div class="metric-item">
                <span class="metric-value">{format_price(total_value)}</span>
                <span class="metric-label">Összérték</span>
            </div>
        </div>
        ''', unsafe_allow_html=True)
        
        # Charts section as per design spec
        st.markdown('<div class="charts-section">', unsafe_allow_html=True)
        
        # Status distribution chart (left side)
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        st.markdown('<div class="chart-title">Státuszok megoszlása</div>', unsafe_allow_html=True)
        self._render_status_chart(status_distribution)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Product distribution chart (right side)
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        st.markdown('<div class="chart-title">Terméktípusok</div>', unsafe_allow_html=True)
        self._render_product_chart(product_distribution)
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)  # Close charts-section
        st.markdown('</div>', unsafe_allow_html=True)  # Close stats-body
        st.markdown('</div>', unsafe_allow_html=True)  # Close stats-card
    
    def _render_status_chart(self, status_data: Dict):
        """Render status distribution chart."""
        try:
            import plotly.express as px
            import plotly.graph_objects as go
            import pandas as pd
            
            if not status_data:
                # Demo data if no real data provided
                status_data = {
                    'Létrehozva': 15,
                    'Megerősítve': 8,
                    'Elfogadva': 12,
                    'Elutasítva': 3,
                    'Véglegesítve': 7
                }
            
            # Create pie chart
            df = pd.DataFrame(list(status_data.items()), columns=['Státusz', 'Darab'])
            
            fig = px.pie(
                df, 
                values='Darab', 
                names='Státusz',
                color_discrete_sequence=['#2196f3', '#ff9800', '#4caf50', '#f44336', '#9c27b0']
            )
            
            fig.update_layout(
                height=250,
                margin=dict(l=0, r=0, t=0, b=0),
                showlegend=True,
                legend=dict(
                    orientation="v",
                    yanchor="middle",
                    y=0.5,
                    xanchor="left",
                    x=1.02,
                    font=dict(size=10)
                )
            )
            
            fig.update_traces(
                textposition='inside',
                textinfo='percent',
                textfont_size=10
            )
            
            st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
            
        except ImportError:
            # Fallback to simple text display if plotly not available
            if status_data:
                for status, count in status_data.items():
                    percentage = (count / sum(status_data.values())) * 100 if status_data.values() else 0
                    st.markdown(f"**{status}:** {count} ({percentage:.1f}%)")
            else:
                st.info("Nincs adat a státuszok megoszlásához")
    
    def _render_product_chart(self, product_data: Dict):
        """Render product distribution chart."""
        try:
            import plotly.express as px
            import plotly.graph_objects as go
            import pandas as pd
            
            if not product_data:
                # Demo data if no real data provided
                product_data = {
                    'Alma': 18,
                    'Szőlő': 12,
                    'Burgonya': 8,
                    'Paradicsom': 6,
                    'Egyéb': 4
                }
            
            # Create horizontal bar chart
            df = pd.DataFrame(list(product_data.items()), columns=['Termék', 'Darab'])
            df = df.sort_values('Darab', ascending=True)
            
            fig = px.bar(
                df,
                x='Darab',
                y='Termék',
                orientation='h',
                color='Darab',
                color_continuous_scale='Blues'
            )
            
            fig.update_layout(
                height=250,
                margin=dict(l=0, r=0, t=0, b=0),
                showlegend=False,
                coloraxis_showscale=False,
                xaxis_title="",
                yaxis_title=""
            )
            
            fig.update_traces(
                texttemplate='%{x}',
                textposition='outside'
            )
            
            st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
            
        except ImportError:
            # Fallback to simple text display if plotly not available
            if product_data:
                for product, count in sorted(product_data.items(), key=lambda x: x[1], reverse=True):
                    st.markdown(f"**{product}:** {count} ajánlat")
            else:
                st.info("Nincs adat a termékek megoszlásához")

@create_component
def OfferStatsCard(ctx):
    """
    Statistics card component for offer overview.
    """
    stats = ctx['props'].get('stats', {})
    title = ctx['props'].get('title', 'Statisztikák')
    
    # Animation state
    animated, set_animated = ctx['use_state'](False)
    
    # Trigger animation on mount
    ctx['use_effect'](
        lambda: set_animated(True),
        [],
        'mount_animation'
    )
    
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        transform: {{'scale(1)' if animated else 'scale(0.95)'}};
        transition: transform 0.3s ease;
    ">
        <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem;">{title}</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {stats.get('total_count', 0)}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összes ajánlat</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_quantity(stats.get('total_quantity', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összes mennyiség</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_price(stats.get('total_value', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összérték</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_price(stats.get('avg_price', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Átlagár/kg</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

class EnhancedOfferCard(BaseComponent):
    """
    Enhanced offer card component supporting both list and card views as per design specification.
    Implements responsive design for desktop list view and mobile card view.
    """
    def render(self):
        offer = self.props.get('offer', {})
        on_edit = self.props.get('on_edit', lambda: None)
        on_view = self.props.get('on_view', lambda: None)
        on_status_change = self.props.get('on_status_change', lambda: None)
        view_mode = self.props.get('view_mode', 'card')  # 'list' or 'card'
        
        # Extract offer data
        offer_id = offer.get('id', 'N/A')
        date = offer.get('created_at', '2023-05-12')
        producer = offer.get('user', {}).get('name', 'Ismeretlen termelő')
        product = offer.get('product_type', {}).get('name', 'Ismeretlen termék')
        quantity = offer.get('quantity_in_kg', 0)
        price = offer.get('price', 0)
        status = offer.get('status', 'CREATED')
        status_display = self._get_status_display(status)
        
        if view_mode == 'list':
            self._render_list_view(offer_id, date, producer, product, quantity, price, status, status_display)
        else:
            self._render_card_view(offer_id, date, producer, product, quantity, price, status, status_display)
    
    def _render_list_view(self, offer_id, date, producer, product, quantity, price, status, status_display):
        """Render compact table row layout for desktop."""
        # Enhanced CSS for list view
        st.markdown("""
        <style>
        .offer-list-row {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 0.5rem 0;
            padding: 1rem;
            transition: all 0.2s ease;
            display: grid;
            grid-template-columns: auto 1fr 1fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: center;
            cursor: pointer;
        }
        .offer-list-row:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }
        .offer-expand-btn {
            background: none;
            border: none;
            font-size: 1rem;
            cursor: pointer;
            color: #666;
            transition: color 0.2s ease;
        }
        .offer-expand-btn:hover {
            color: #1a73e8;
        }
        .offer-id {
            font-weight: 600;
            color: #1a73e8;
        }
        .offer-date {
            color: #666;
            font-size: 0.875rem;
        }
        .offer-producer {
            font-weight: 500;
        }
        .offer-product {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .offer-quantity, .offer-price {
            font-weight: 500;
            text-align: right;
        }
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Format date
        formatted_date = self._format_date_display(date)
        status_color, status_icon = self._get_status_color_and_icon(status)
        
        # List row HTML
        st.markdown(f'''
        <div class="offer-list-row" onclick="handleOfferListClick('{offer_id}')">
            <button class="offer-expand-btn">▶️</button>
            <div class="offer-id">#{offer_id}</div>
            <div class="offer-date">📆 {formatted_date}</div>
            <div class="offer-producer">👤 {producer}</div>
            <div class="offer-product">🍎 {product}</div>
            <div class="offer-quantity">📦 {format_quantity(quantity)}</div>
            <div class="offer-price">💰 {format_price(price)}/kg</div>
            <div class="status-indicator" style="color: {status_color};">
                {status_icon} {status_display}
            </div>
        </div>
        ''', unsafe_allow_html=True)
        
        # Invisible button for click handling
        if st.button(f"List view {offer_id}", key=f"{self.component_id}_list_{offer_id}", label_visibility="collapsed"):
            self.props.get('on_view', lambda: None)()
    
    def _render_card_view(self, offer_id, date, producer, product, quantity, price, status, status_display):
        """Render compact card layout for mobile."""
        # Enhanced CSS for card view
        st.markdown("""
        <style>
        .offer-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .offer-card:hover {
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        .card-id-producer {
            flex: 1;
        }
        .card-id {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a73e8;
            margin: 0;
        }
        .card-producer {
            font-size: 0.9rem;
            color: #666;
            margin: 0.25rem 0 0 0;
        }
        .card-status {
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        .card-product {
            font-size: 1rem;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .card-detail-item {
            text-align: center;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .card-detail-value {
            font-weight: 600;
            color: #1a73e8;
            display: block;
            margin-bottom: 0.25rem;
        }
        .card-detail-label {
            font-size: 0.875rem;
            color: #666;
        }
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: #666;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Format date and get status styling
        formatted_date = self._format_date_display(date)
        status_color, status_icon = self._get_status_color_and_icon(status)
        
        # Card HTML
        st.markdown(f'''
        <div class="offer-card" onclick="handleOfferCardClick('{offer_id}')">
            <div class="card-header">
                <div class="card-id-producer">
                    <h4 class="card-id">#{offer_id} - {producer}</h4>
                </div>
                <div class="card-status" style="background: {status_color}20; color: {status_color}; border: 1px solid {status_color}40;">
                    {status_icon} {status_display}
                </div>
            </div>
            
            <div class="card-product">
                🍎 {product}
            </div>
            
            <div class="card-details">
                <div class="card-detail-item">
                    <span class="card-detail-value">{format_quantity(quantity)}</span>
                    <span class="card-detail-label">Mennyiség</span>
                </div>
                <div class="card-detail-item">
                    <span class="card-detail-value">{format_price(price)}/kg</span>
                    <span class="card-detail-label">Ár</span>
                </div>
            </div>
            
            <div class="card-footer">
                <span>📅 {formatted_date}</span>
                <span style="color: {status_color}; font-weight: 500;">👁️ Részletek</span>
            </div>
        </div>
        ''', unsafe_allow_html=True)
        
        # JavaScript for click handling
        st.markdown(f"""
        <script>
        function handleOfferCardClick(offerId) {{
            console.log('Clicked offer card:', offerId);
        }}
        function handleOfferListClick(offerId) {{
            console.log('Clicked offer list:', offerId);
        }}
        </script>
        """, unsafe_allow_html=True)
        
        # Invisible button for click handling
        if st.button(f"Card view {offer_id}", key=f"{self.component_id}_card_{offer_id}", label_visibility="collapsed"):
            self.props.get('on_view', lambda: None)()
    
    def _get_status_display(self, status: str) -> str:
        """Get human-readable status display."""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(status, status)
    
    def _get_status_color_and_icon(self, status: str) -> tuple:
        """Get status color and icon."""
        status_config = {
            'CREATED': ('#2196f3', '🟢'),
            'CONFIRMED_BY_COMPANY': ('#ff9800', '🟡'),
            'ACCEPTED_BY_USER': ('#4caf50', '🟢'),
            'REJECTED_BY_USER': ('#f44336', '🔴'),
            'FINALIZED': ('#9c27b0', '🟣')
        }
        return status_config.get(status, ('#757575', '⚪'))
    
    def _format_date_display(self, date_str: str) -> str:
        """Format date for display."""
        try:
            if isinstance(date_str, str):
                from datetime import datetime
                if 'T' in date_str:
                    dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                else:
                    dt = datetime.strptime(date_str, '%Y-%m-%d')
                return dt.strftime('%Y.%m.%d')
            return date_str
        except:
            return date_str

@create_component
def OfferCard(ctx):
    """
    Modern offer card component for mobile and card view.
    """
    offer = ctx['props'].get('offer', {})
    on_click = ctx['props'].get('on_click', lambda: None)
    index = ctx['props'].get('index', 0)
    
    # Get offer details
    offer_id = offer.get('id', 'N/A')
    producer_name = offer.get('user', {}).get('name', 'Ismeretlen termelő')
    product_name = offer.get('product_type', {}).get('name', 'Ismeretlen termék')
    quantity = offer.get('quantity_in_kg', 0)
    price = offer.get('price', 0)
    status = offer.get('status', 'UNKNOWN')
    created_at = offer.get('created_at', '')
    
    # Status colors
    status_colors = {
        'CREATED': '#2196f3',
        'CONFIRMED_BY_COMPANY': '#ff9800',
        'ACCEPTED_BY_USER': '#4caf50',
        'REJECTED_BY_USER': '#f44336',
        'FINALIZED': '#9c27b0'
    }
    
    status_color = status_colors.get(status, '#757575')
    
    # Hover state
    is_hovered, set_hovered = ctx['use_state'](False)
    
    card_key = f"{ctx['component_id']}_card_{offer_id}"
    
    st.markdown(f"""
    <div style="
        background: white;
        border-radius: 12px;
        box-shadow: {'0 8px 16px rgba(0, 0, 0, 0.15)' if is_hovered else '0 4px 6px rgba(0, 0, 0, 0.1)'};
        border-left: 4px solid {status_color};
        margin: 1rem 0;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #e1e5e9;
    " onclick="handleOfferClick('{offer_id}')">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
            <div>
                <h4 style="margin: 0; color: #1a1a1a; font-size: 1.125rem;">#{offer_id}</h4>
                <p style="margin: 0.25rem 0 0 0; color: #666; font-size: 0.875rem;">{producer_name}</p>
            </div>
            <span style="
                background: {status_color}; 
                color: white; 
                padding: 0.25rem 0.75rem; 
                border-radius: 16px; 
                font-size: 0.75rem;
                font-weight: 500;
            ">
                {format_status(status)}
            </span>
        </div>
        
        <div style="margin-bottom: 1rem;">
            <div style="font-size: 1rem; font-weight: 500; color: #1a1a1a; margin-bottom: 0.5rem;">
                🍎 {product_name}
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.875rem;">
                <div>
                    <span style="color: #666;">Mennyiség:</span><br>
                    <span style="font-weight: 500;">{format_quantity(quantity)}</span>
                </div>
                <div>
                    <span style="color: #666;">Ár:</span><br>
                    <span style="font-weight: 500;">{format_price(price)}/kg</span>
                </div>
            </div>
        </div>
        
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.75rem; color: #666;">
            <span>📅 {format_date(created_at)}</span>
            <span style="color: {status_color};">👁️ Részletek</span>
        </div>
    </div>
    
    <script>
    function handleOfferClick(offerId) {{
        // This would trigger the on_click callback in a real implementation
        console.log('Clicked offer:', offerId);
    }}
    </script>
    """, unsafe_allow_html=True)
    
    # Actual clickable area (invisible button)
    if st.button(
        f"Offer {offer_id}",
        key=card_key,
        label_visibility="collapsed",
        help=f"Ajánlat #{offer_id} részleteinek megtekintése"
    ):
        on_click()

@create_component
def LoadingSpinner(ctx):
    """
    Loading spinner component.
    """
    message = ctx['props'].get('message', 'Betöltés...')
    size = ctx['props'].get('size', 'medium')
    
    size_map = {
        'small': '1rem',
        'medium': '2rem',
        'large': '3rem'
    }
    
    spinner_size = size_map.get(size, '2rem')
    
    st.markdown(f"""
    <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
    ">
        <div style="
            width: {spinner_size};
            height: {spinner_size};
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        "></div>
        <p style="color: #666; margin: 0;">{message}</p>
    </div>
    
    <style>
    @keyframes spin {{
        0% {{ transform: rotate(0deg); }}
        100% {{ transform: rotate(360deg); }}
    }}
    </style>
    """, unsafe_allow_html=True)

@with_props(variant='primary', size='medium')
@create_component
def ModernButton(ctx):
    """
    Modern button component with variants and states.
    """
    label = ctx['props'].get('label', 'Button')
    on_click = ctx['props'].get('on_click', lambda: None)
    variant = ctx['props'].get('variant', 'primary')
    size = ctx['props'].get('size', 'medium')
    disabled = ctx['props'].get('disabled', False)
    loading = ctx['props'].get('loading', False)
    icon = ctx['props'].get('icon', '')
    
    # Button styles
    variants = {
        'primary': 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;',
        'secondary': 'background: #6c757d; color: white;',
        'success': 'background: #28a745; color: white;',
        'danger': 'background: #dc3545; color: white;',
        'warning': 'background: #ffc107; color: #212529;',
        'info': 'background: #17a2b8; color: white;',
        'outline': 'background: transparent; color: #667eea; border: 2px solid #667eea;'
    }
    
    sizes = {
        'small': 'padding: 0.5rem 1rem; font-size: 0.875rem;',
        'medium': 'padding: 0.75rem 1.5rem; font-size: 1rem;',
        'large': 'padding: 1rem 2rem; font-size: 1.125rem;'
    }
    
    base_style = variants.get(variant, variants['primary'])
    size_style = sizes.get(size, sizes['medium'])
    
    if disabled or loading:
        base_style += ' opacity: 0.5; cursor: not-allowed;'
    
    button_content = f"{icon} {label}" if icon else label
    
    if loading:
        button_content = "⏳ Betöltés..."
    
    if st.button(
        button_content,
        key=f"{ctx['component_id']}_button",
        disabled=disabled or loading,
        help=ctx['props'].get('help', '')
    ):
        if not loading and not disabled:
            on_click()

@with_state_management(['statistics'])
@create_component
def StatefulStatsCard(ctx):
    """
    State-aware statistics card that automatically updates from global state.
    """
    title = ctx['props'].get('title', '📊 Statisztikák')
    
    # Get statistics from state
    if state_hooks_available:
        stats = ctx.get('statistics', {})
    else:
        stats = ctx['props'].get('stats', {})
    
    # Get theme
    if state_hooks_available:
        theme, _ = use_theme(ctx['component_id'])
    else:
        theme = 'light'
    
    # Theme colors
    if theme == 'dark':
        bg_gradient = 'linear-gradient(135deg, #bb86fc 0%, #6200ea 100%)'
        text_color = '#ffffff'
    else:
        bg_gradient = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        text_color = '#ffffff'
    
    # Animation state
    animated, set_animated = ctx['use_state'](False)
    
    # Trigger animation on mount
    ctx['use_effect'](
        lambda: set_animated(True),
        [],
        'mount_animation'
    )
    
    st.markdown(f"""
    <div style="
        background: {bg_gradient};
        color: {text_color};
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        transform: {{'scale(1)' if animated else 'scale(0.95)'}};
        transition: transform 0.3s ease;
    ">
        <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem;">{title}</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {stats.get('total_count', 0)}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összes ajánlat</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_quantity(stats.get('total_quantity', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összes mennyiség</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_price(stats.get('total_value', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Összérték</div>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 2rem; font-weight: bold; margin-bottom: 0.25rem;">
                    {format_price(stats.get('avg_price', 0))}
                </div>
                <div style="font-size: 0.875rem; opacity: 0.9;">Átlagár/kg</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

@with_state_management(['ui', 'notifications'])
@create_component
def NotificationCenter(ctx):
    """
    Notification center component that displays and manages notifications.
    """
    if not state_hooks_available:
        return
    
    notifications = ctx.get('notifications', [])
    notification_actions = ctx.get('notification_actions', {})
    
    if not notifications:
        return
    
    # Display notifications
    for notification in notifications[-3:]:  # Show only last 3
        msg_type = notification.get('type', 'info')
        message = notification.get('message', '')
        notification_id = notification.get('id')
        
        # Color mapping
        colors = {
            'info': '#2196f3',
            'success': '#4caf50', 
            'warning': '#ff9800',
            'error': '#f44336'
        }
        
        color = colors.get(msg_type, '#2196f3')
        
        st.markdown(f"""
        <div style="
            background: {color};
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideIn 0.3s ease-out;
        ">
            <span>{message}</span>
            <button onclick="clearNotification('{notification_id}')" 
                    style="background: none; border: none; color: white; cursor: pointer; font-size: 1.2rem;">
                ×
            </button>
        </div>
        
        <style>
        @keyframes slideIn {{
            from {{ transform: translateX(100%); opacity: 0; }}
            to {{ transform: translateX(0); opacity: 1; }}
        }}
        </style>
        
        <script>
        function clearNotification(notificationId) {{
            // This would trigger the clear action in a real implementation
            console.log('Clearing notification:', notificationId);
        }}
        </script>
        """, unsafe_allow_html=True)

@with_state_management(['ui'])
@create_component
def ThemeToggle(ctx):
    """
    Theme toggle component for switching between light and dark themes.
    """
    if not state_hooks_available:
        return
    
    ui_state = ctx.get('ui', {})
    ui_actions = ctx.get('ui_actions', {})
    
    current_theme = ui_state.get('theme', 'light')
    
    # Toggle button
    if st.button(
        f"🌙 Dark" if current_theme == 'light' else f"☀️ Light",
        key=f"{ctx['component_id']}_theme_toggle",
        help=f"Switch to {'dark' if current_theme == 'light' else 'light'} theme"
    ):
        new_theme = 'dark' if current_theme == 'light' else 'light'
        if 'set_theme' in ui_actions:
            ui_actions['set_theme'](new_theme)

@create_component
def StateDebugPanel(ctx):
    """
    Debug panel for viewing current state (development only).
    """
    if not state_hooks_available:
        st.warning("State management not available")
        return
    
    # Import state debug hook
    try:
        from .state_hooks import use_state_debug
        debug_info = use_state_debug(ctx['component_id'])
    except ImportError:
        debug_info = {'error': 'Debug hooks not available'}
    
    with st.expander("🔧 State Debug Info"):
        st.json(debug_info)

# Export commonly used components
__all__ = [
    'ModernFilterPanel',
    'StatefulFilterPanel',
    'EnhancedOfferStatsCard',
    'OfferStatsCard',
    'StatefulStatsCard',
    'EnhancedOfferCard', 
    'OfferCard',
    'LoadingSpinner',
    'ModernButton',
    'NotificationCenter',
    'ThemeToggle',
    'StateDebugPanel'
]