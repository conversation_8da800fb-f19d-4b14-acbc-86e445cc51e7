"""
Data processing functions for the offer management page.
Contains functions for transforming and preparing data for display.
"""
import streamlit as st
import pandas as pd
import uuid
import logging
import io
import csv
import time
import json
from datetime import datetime, timedelta
from typing import Tuple, Any

# Try multiple import paths with fallbacks for formatting utilities
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        # Fallback formatting functions if import fails
        logging.warning("Could not import formatting functions in data_processing.py, using fallbacks")
        format_status = lambda x: x
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Try to import notification components with fallbacks
try:
    # Try absolute import first
    from streamlit_app.components.notification import show_info, show_error, show_success
except ImportError:
    try:
        # Try regular app-relative import
        from components.notification import show_info, show_error, show_success
    except ImportError:
        # Fallback notification functions
        logging.warning("Could not import notification components in data_processing.py, using fallbacks")
        show_info = lambda msg: st.info(msg)
        show_error = lambda msg: st.error(msg)
        show_success = lambda msg: st.success(msg)

# Import enhanced UI components with fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
        except ImportError:
            # If all imports fail, define minimal versions of these functions locally
            logging.warning("Could not import enhanced_ui_components in data_processing.py, using minimal local implementations")
            
            # Basic implementations of the most essential functions
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)

# Import export functions with better fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.export_functions import export_offers_to_csv, export_offers_to_excel, render_export_buttons
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.export_functions import export_offers_to_csv, export_offers_to_excel, render_export_buttons
    except ImportError:
        try:
            # Try direct local import
            from export_functions import export_offers_to_csv, export_offers_to_excel, render_export_buttons
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import export_functions, using fallbacks")
            
            def export_offers_to_csv(offers, filename=None):
                """Basic CSV export fallback"""
                buffer = io.StringIO()
                writer = csv.writer(buffer)
                writer.writerow(["ID", "Status", "Product", "Quantity", "Price"])
                for offer in offers:
                    writer.writerow([
                        offer.get("id", ""),
                        offer.get("status", ""),
                        offer.get("product_name", ""),
                        offer.get("quantity_in_kg", ""),
                        offer.get("price", "")
                    ])
                return buffer.getvalue()
                
            def export_offers_to_excel(offers, filename=None):
                """Basic Excel export fallback"""
                buffer = io.BytesIO()
                df = pd.DataFrame(offers)
                df.to_excel(buffer, index=False)
                return buffer.getvalue()
                
            def render_export_buttons(offers):
                """Basic export buttons fallback"""
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("CSV Exportálás"):
                        csv_data = export_offers_to_csv(offers)
                        st.download_button(
                            "Letöltés CSV", 
                            csv_data, 
                            "ajanlatok.csv",
                            "text/csv"
                        )
                with col2:
                    if st.button("Excel Exportálás"):
                        excel_data = export_offers_to_excel(offers)
                        st.download_button(
                            "Letöltés Excel", 
                            excel_data, 
                            "ajanlatok.xlsx",
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        )

# Import specific API client functions with better fallbacks
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.api_client import get_offers, get_producers, get_product_types, get_producer
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.api_client import get_offers, get_producers, get_product_types, get_producer
    except ImportError:
        try:
            # Try direct local import
            from api_client import get_offers, get_producers, get_product_types, get_producer
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import api_client functions, using fallbacks")
            
            def get_offers(query_params=None):
                """Basic offers API fallback"""
                return False, "API client not available"
                
            def get_producers():
                """Basic producers API fallback"""
                return False, "API client not available"
                
            def get_product_types():
                """Basic product types API fallback"""
                return False, "API client not available"
                
            def get_producer(producer_id):
                """Basic producer API fallback"""
                return False, "API client not available"

# Import API client utilities with better fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
except ImportError:
    try:
        # Try regular app-relative import
        from utils.api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
    except ImportError:
        try:
            # Try direct local import
            from api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import api_client utilities, using fallbacks")
            
            def fetch_data_with_progress(fetch_func, message="Loading data...", *args, **kwargs):
                """Basic data fetching with progress fallback"""
                with st.spinner(message):
                    return fetch_func(*args, **kwargs)
                    
            def safe_api_call(func, operation_name, *args, **kwargs):
                """Basic safe API call fallback"""
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    return False, f"Error during {operation_name}: {str(e)}"
                    
            def lazy_load_cache(key, loader_func, ttl=60):
                """Basic cache fallback"""
                if key not in st.session_state:
                    st.session_state[key] = loader_func()
                return st.session_state[key]

logger = logging.getLogger(__name__)

def format_date_for_api(date_obj, add_day=False):
    """
    Dátum formázása az API számára megfelelő formátumra.
    
    Args:
        date_obj (datetime.date): A formázandó dátum objektum
        add_day (bool, optional): Hozzáadjunk-e egy napot a dátumhoz. Defaults to False.
        
    Returns:
        str: Az API-nak megfelelő formátumú dátum
    """
    if add_day:
        date_obj = date_obj + timedelta(days=1)
    
    return date_obj.strftime("%Y-%m-%d")

def prepare_filter_params(selected_producer_id, selected_status, from_date, to_date):
    """
    API szűrési paraméterek előkészítése a felhasználói beállítások alapján.
    
    Args:
        selected_producer_id (int): A kiválasztott termelő ID-ja vagy None
        selected_status (str): A kiválasztott státusz vagy None
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Végső dátum
        
    Returns:
        dict: Az API híváshoz előkészített paraméterek
    """
    query_params = {}
    
    # Termelő szűrő
    if selected_producer_id:
        query_params["user_id"] = selected_producer_id
    
    # Státusz szűrő
    if selected_status:
        query_params["status"] = selected_status
    
    # Dátum szűrők
    if from_date:
        query_params["from_date"] = format_date_for_api(from_date)
    
    if to_date:
        # A 'to_date' paraméternél egy napot hozzáadunk, hogy a végső nap is belekerüljön
        query_params["to_date"] = format_date_for_api(to_date, add_day=True)
    
    return query_params

def load_offers_with_pagination(query_params=None, page=1, page_size=20):
    """
    Ajánlatok betöltése lapozási funkcióval.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek. Defaults to None.
        page (int, optional): Az aktuális oldal. Defaults to 1.
        page_size (int, optional): Az oldalméret. Defaults to 20.
        
    Returns:
        tuple: (success, offers, total_pages) ahol 
               success egy boolean érték, 
               offers az ajánlatok listája, 
               total_pages az összes oldal száma
    """
    # Add pagination parameters to query params if needed
    params = query_params.copy() if query_params else {}
    if page:
        params["page"] = page
    if page_size:
        params["limit"] = page_size
    
    # Call the API using the safe_api_call function
    success, result = safe_api_call(get_offers, "ajánlatok betöltése", params)
    
    if success:
        # Handle different API response formats
        if isinstance(result, dict) and "items" in result:
            offers = result.get("items", [])
            total = result.get("total", 0)
        elif isinstance(result, list):
            offers = result
            total = len(result)
        else:
            # Single offer returned as a dict
            if isinstance(result, dict):
                offers = [result]
                total = 1
            else:
                offers = []
                total = 0
        
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1
        return True, offers, total_pages
    else:
        logger.error(f"Failed to load offers: {result}")
        return False, result, 1

def fetch_offer_data_with_progress(query_params=None, page=1, page_size=20):
    """
    Ajánlatok betöltése folyamatjelzővel, 
    beleértve a termelők és termékek részletes adatait is.
    
    A módosított verzió már a központi fetch_data_with_progress függvényt használja
    a fejlettebb folyamatjelzéshez és reszponzív megjelenítéshez.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek. Defaults to None.
        page (int, optional): Az aktuális oldal. Defaults to 1.
        page_size (int, optional): Az oldalméret. Defaults to 20.
        
    Returns:
        tuple: (success, result, total_pages) ahol 
               success egy boolean érték, 
               result az ajánlatok listája vagy hibaüzenet,
               total_pages az összes oldal száma
    """
    # Képernyőméret információk lekérése (reszponzív megjelenítéshez)
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Eszközmérethez igazított oldalméret
    adjusted_page_size = 5 if is_mobile else (10 if is_tablet else page_size)
    
    # Lapozási paraméterek hozzáadása
    params = query_params.copy() if query_params else {}
    params["page"] = page
    params["limit"] = adjusted_page_size
    
    # Cache kulcs generálása a lekérdezési paraméterekből
    cache_params = params.copy()
    cache_key = f"offers_{json.dumps(cache_params, sort_keys=True)}"
    
    # Adatok betöltése a lazy_load_cache segítségével
    try:
        def data_loader():
            # Adatbetöltő függvény definíciója
            # 1. Ajánlatok betöltése
            success, offers, total_pages = load_offers_with_pagination(params, page, adjusted_page_size)
            
            if not success:
                return False, offers, 1
            
            # 2. Termelők adatainak betöltése
            producer_ids = list(set(offer.get("user_id") for offer in offers if offer.get("user_id")))
            
            # A producers_cache inicializálása, ha még nem létezik
            if "producers_cache" not in st.session_state:
                st.session_state.producers_cache = {}
            
            for producer_id in producer_ids:
                # Ellenőrizzük, hogy a termelő már a cache-ben van-e
                if producer_id not in st.session_state.producers_cache:
                    # Ha nincs, akkor betöltjük az API-n keresztül
                    producer_result = safe_api_call(get_producer, "termelő adatainak betöltése", producer_id)
                    if isinstance(producer_result, tuple) and len(producer_result) == 2:
                        success, producer = producer_result
                        if success:
                            st.session_state.producers_cache[producer_id] = producer
                    else:
                        # Handle case where get_producer returns something else
                        logger.warning(f"Unexpected result from get_producer: {producer_result}")
                        if producer_result and not isinstance(producer_result, tuple):
                            st.session_state.producers_cache[producer_id] = producer_result
            
            # 3. Termelői adatok hozzáadása az ajánlatokhoz
            for offer in offers:
                producer_id = offer.get("user_id")
                if producer_id and producer_id in st.session_state.producers_cache:
                    offer["user"] = st.session_state.producers_cache[producer_id]
            
            return True, offers, total_pages
        
        # Adatok betöltése fejlett folyamatjelzővel
        with st.spinner("Ajánlatok betöltése..."):
            # Adatok betöltése a lazy_load_cache segítségével
            cached_result = lazy_load_cache(
                cache_key=cache_key,
                data_loader_func=data_loader,
                cache_ttl=120  # 2 perces cache az ajánlatokhoz (gyakran változhat)
            )
            
            return cached_result
        
    except Exception as e:
        # Hiba esetén visszaadjuk a hibát
        logger.error(f"Error during offer data fetching: {str(e)}")
        return False, f"Hiba az ajánlatok betöltése során: {str(e)}", 1

# Import async_data_loading modul
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.async_data_loading import async_data_load, batch_async_data_load
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.async_data_loading import async_data_load, batch_async_data_load
    except ImportError:
        try:
            # Try direct local import
            from async_data_loading import async_data_load, batch_async_data_load
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import async_data_loading, using fallbacks")
            
            def async_data_load(load_function, operation_key, operation_name, *args, **kwargs):
                """Basic async data load fallback"""
                try:
                    with st.spinner(f"{operation_name} betöltése..."):
                        success, result = load_function(*args, **kwargs)
                        return True, result, f"{operation_name} betöltve."
                except Exception as e:
                    return True, None, f"Hiba: {str(e)}"
                    
            def batch_async_data_load(load_functions, show_progress=True):
                """Basic batch async data load fallback"""
                results = {}
                for key, (func, name, args, kwargs) in load_functions.items():
                    results[key] = async_data_load(func, key, name, *args, **kwargs)
                return results

def load_offers_with_filters(query_params: dict) -> list:
    """
    Ajánlatok betöltése a megadott szűrőkkel aszinkron vagy szinkron módon.
    
    Args:
        query_params (dict): A szűrési paraméterek.
        
    Returns:
        list: Az ajánlatok listája vagy üres lista hiba esetén.
              Visszaad egy üres listát, ha az adatbetöltés még folyamatban van (aszinkron esetben).
    """
    logger.debug(f"load_offers_with_filters called. Query: {query_params}")
    start_time = time.time()

    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    use_async = not (is_mobile or is_tablet)
    
    if use_async:
        logger.debug(f"Using asynchronous path for offer loading. Query: {query_params}")

        # Wrapper function for async_data_load, must return (bool, Any_data_or_error_message)
        def _fetch_data_for_async_load() -> Tuple[bool, Any]:
            # Indok: Ez a wrapper biztosítja, hogy a fetch_offer_data_with_progress által visszaadott
            # (success, payload, total_pages) formátumot átalakítsuk az async_data_load által
            # elvárt (success, data_or_error_message) formátumra.
            fodp_success, fodp_payload, fodp_total_pages = fetch_offer_data_with_progress(query_params)
            if fodp_success:
                # Sikeres esetben a payload tartalmazza az ajánlatok listáját.
                # A total_pages információt itt most nem adjuk tovább, mivel az async_data_load
                # egyetlen adat objektumot kezel. Ha a hívónak szüksége van rá, 
                # a fodp_payload lehetne egy tuple: (offers_list, total_pages).
                logger.debug(f"_fetch_data_for_async_load successful. Records: {len(fodp_payload) if isinstance(fodp_payload, list) else 'N/A'}")
                return True, fodp_payload  # fodp_payload is list of offers
            else:
                # Hiba esetén a fodp_payload tartalmazza a hibaüzenetet.
                logger.error(f"_fetch_data_for_async_load failed. Error: {fodp_payload}")
                return False, fodp_payload # fodp_payload is error message string

        # Call async_data_load with the wrapper
        # async_data_load returns: (is_operation_done, data_if_success_or_None_if_error, message_from_adl)
        is_operation_done, data_from_adl, message_from_adl = async_data_load(
            load_function=_fetch_data_for_async_load,
            operation_key=f"offers_query_{hash(json.dumps(query_params, sort_keys=True))}", # Consistent hash
            operation_name="Ajánlatok"
        )

        if is_operation_done:
            elapsed_time = time.time() - start_time
            if data_from_adl is not None:
                # Az async_data_load sikeresen lefutott ÉS a _fetch_data_for_async_load is sikert jelzett.
                # A data_from_adl tartalmazza az ajánlatok listáját.
                logger.info(f"Async offers loaded successfully in {elapsed_time:.2f}s. Records: {len(data_from_adl)}. Query: {query_params}")
                return data_from_adl
            else:
                # Az async_data_load sikeresen lefutott, DE a _fetch_data_for_async_load hibát jelzett.
                # A message_from_adl tartalmazza a hibaüzenetet.
                logger.error(f"Async offer loading failed in {elapsed_time:.2f}s: {message_from_adl}. Query: {query_params}")
                show_error(f"Hiba az ajánlatok aszinkron betöltése során: {message_from_adl}")
                return []
        else:
            # Az async_data_load szerint a művelet még folyamatban van.
            # A message_from_adl ilyenkor pl. "Ajánlatok betöltése folyamatban...".
            # A fő oldalon (offer_management.py) lévő st.spinner kezeli a várakozást.
            logger.debug(f"Async offer loading in progress: {message_from_adl}. Query: {query_params}")
            return [] # Return empty list to keep spinner active on calling page
    
    else:  # Synchronous path (e.g., for mobile/tablet)
        logger.debug(f"Using synchronous path for offer loading. Query: {query_params}")
        fodp_success, fodp_payload, fodp_total_pages = fetch_offer_data_with_progress(query_params)
        elapsed_time = time.time() - start_time
        if fodp_success:
            logger.info(f"Sync offers loaded successfully in {elapsed_time:.2f}s. Records: {len(fodp_payload)}. Query: {query_params}")
            return fodp_payload  # fodp_payload is list of offers
        else:
            logger.error(f"Sync offer loading failed in {elapsed_time:.2f}s: {fodp_payload}. Query: {query_params}")
            show_error(f"Hiba az ajánlatok szinkron betöltése során: {fodp_payload}") # fodp_payload is error message
            return []
