#!/usr/bin/env python3
"""
Real-time szűrő diagnosztikai eszköz
Kritikus szűrő hibák azonosítására és javítására
"""

import streamlit as st
import logging
from datetime import datetime, date
from typing import Dict, Any, List, Tuple, Optional
import json

logger = logging.getLogger(__name__)


class FilterDiagnosticTool:
    """Real-time szűrő diagnosztikai eszköz"""
    
    def __init__(self, state_manager=None, data_coordinator=None):
        """Initialize diagnostic tool"""
        self.state_manager = state_manager
        self.data_coordinator = data_coordinator
        self.diagnostic_session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.trace_log = []
        
    def diagnose_filter_chain(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any], results: List[Dict]) -> Dict[str, Any]:
        """<PERSON>jes s<PERSON><PERSON><PERSON> l<PERSON> diagnosztiká<PERSON> - ez a fő hiba feltáró function"""
        
        diagnosis = {
            'session_id': self.diagnostic_session_id,
            'timestamp': datetime.now().isoformat(),
            'ui_filters': ui_filters.copy(),
            'api_params': api_params.copy(),
            'results_count': len(results),
            'issues_found': [],
            'validation_status': 'unknown',
            'producer_analysis': {},
            'conversion_issues': [],
            'result_validation': {}
        }
        
        # 1. UI Filter Analysis
        logger.info("🔍 Starting filter chain diagnosis...")
        diagnosis['ui_analysis'] = self._analyze_ui_filters(ui_filters)
        
        # 2. API Conversion Analysis  
        diagnosis['conversion_analysis'] = self._analyze_api_conversion(ui_filters, api_params)
        
        # 3. Producer Mapping Validation
        # JAVÍTÁS: Backend user_id paramétert használ producer szűréshez
        if 'producer_filter' in ui_filters or 'user_id' in api_params:
            diagnosis['producer_analysis'] = self._validate_producer_mapping(ui_filters, api_params)
            
        # 4. Result Validation - használjuk a DataCoordinator metódusát
        if self.data_coordinator:
            try:
                diagnosis['result_validation'] = self.data_coordinator.validate_filter_to_result_consistency(
                    ui_filters, api_params, results
                )
            except Exception as e:
                logger.error(f"Error in result validation: {e}")
                diagnosis['result_validation'] = {'error': str(e)}
        else:
            diagnosis['result_validation'] = self._validate_results_vs_filters(results, ui_filters, api_params)
        
        # 5. Issue Summary
        diagnosis['issues_found'] = self._compile_issues(diagnosis)
        diagnosis['validation_status'] = 'failed' if diagnosis['issues_found'] else 'passed'
        
        # Store for history
        self.trace_log.append(diagnosis)
        
        logger.info(f"🎯 Diagnosis complete: {diagnosis['validation_status']} - {len(diagnosis['issues_found'])} issues found")
        
        return diagnosis
    
    def _analyze_ui_filters(self, ui_filters: Dict) -> Dict[str, Any]:
        """UI szűrők elemzése"""
        
        analysis = {
            'filter_count': len(ui_filters),
            'active_filters': [],
            'producer_filter_details': {},
            'other_filters': {},
            'potential_issues': []
        }
        
        for key, value in ui_filters.items():
            if value is not None and value != '' and value != []:
                analysis['active_filters'].append(key)
                
                # Producer filter special analysis
                if 'producer' in key.lower():
                    analysis['producer_filter_details'] = {
                        'key': key,
                        'value': value,
                        'type': type(value).__name__,
                        'is_tuple': isinstance(value, tuple),
                        'length': len(value) if isinstance(value, (list, tuple)) else 1
                    }
                    
                    # Check for potential issues
                    if isinstance(value, tuple) and len(value) != 2:
                        analysis['potential_issues'].append(f"Producer tuple has {len(value)} elements, expected 2")
                    elif not isinstance(value, (tuple, int, str)):
                        analysis['potential_issues'].append(f"Producer filter has unexpected type: {type(value)}")
                else:
                    analysis['other_filters'][key] = {
                        'value': value,
                        'type': type(value).__name__
                    }
        
        return analysis
    
    def _analyze_api_conversion(self, ui_filters: Dict, api_params: Dict) -> Dict[str, Any]:
        """API konverzió elemzése"""
        
        analysis = {
            'conversion_successful': True,
            'ui_filter_count': len([k for k, v in ui_filters.items() if v not in [None, '', []]]),
            'api_param_count': len(api_params),
            'missing_conversions': [],
            'unexpected_params': [],
            'producer_conversion': {}
        }
        
        # Check producer conversion specifically
        # JAVÍTÁS: Backend user_id paramétert használ producer szűréshez
        producer_in_ui = any('producer' in k.lower() for k, v in ui_filters.items() if v not in [None, '', []])
        producer_in_api = 'user_id' in api_params
        
        analysis['producer_conversion'] = {
            'ui_has_producer': producer_in_ui,
            'api_has_user_id': producer_in_api,
            'conversion_match': producer_in_ui == producer_in_api
        }
        
        if producer_in_ui and not producer_in_api:
            analysis['missing_conversions'].append('producer_filter -> user_id')
            analysis['conversion_successful'] = False
            
        if not producer_in_ui and producer_in_api:
            analysis['unexpected_params'].append('user_id (no UI filter)')
            
        return analysis
    
    def _validate_producer_mapping(self, ui_filters: Dict, api_params: Dict) -> Dict[str, Any]:
        """Producer mapping validation wrapper"""
        
        # Extract producer info from UI filters
        producer_name = None
        producer_id_from_ui = None
        
        for key, value in ui_filters.items():
            if 'producer' in key.lower() and value not in [None, '', []]:
                if isinstance(value, tuple) and len(value) == 2:
                    producer_name, producer_id_from_ui = value
                    break
                    
        # Get producer ID from API params
        # JAVÍTÁS: Backend user_id paramétert használ producer szűréshez
        user_id_from_api = api_params.get('user_id')
        
        # Validate mapping (use existing validate_producer_mapping method)
        # Note: api parameter is user_id but we call it api_producer_id for compatibility
        validation = self.validate_producer_mapping(
            producer_id_from_ui, producer_name, user_id_from_api, []
        )
        
        # Add conversion consistency check
        validation['ui_producer_id'] = producer_id_from_ui
        validation['api_user_id'] = user_id_from_api
        validation['id_consistency'] = producer_id_from_ui == user_id_from_api
        
        if not validation['id_consistency']:
            validation['errors'].append(f"ID mismatch: UI={producer_id_from_ui} vs API={user_id_from_api}")
            
        return validation
    
    def _compile_issues(self, diagnosis: Dict) -> List[str]:
        """Összes talált hiba összegyűjtése"""
        
        issues = []
        
        # UI analysis issues
        if 'ui_analysis' in diagnosis:
            issues.extend(diagnosis['ui_analysis'].get('potential_issues', []))
            
        # Conversion issues
        if 'conversion_analysis' in diagnosis:
            conv = diagnosis['conversion_analysis']
            if not conv.get('conversion_successful', True):
                issues.extend(conv.get('missing_conversions', []))
                
        # Producer mapping issues
        if 'producer_analysis' in diagnosis:
            issues.extend(diagnosis['producer_analysis'].get('errors', []))
            
        # Result validation issues
        if 'result_validation' in diagnosis:
            if 'validation_issues' in diagnosis['result_validation']:
                issues.extend(diagnosis['result_validation']['validation_issues'])
            
        return issues
    
    def _validate_results_vs_filters(self, results: List[Dict], ui_filters: Dict, api_params: Dict) -> Dict[str, Any]:
        """Eredmények vs szűrők validálása - fallback ha nincs data_coordinator"""
        
        validation = {
            'total_results': len(results),
            'producer_validation': {},
            'validation_issues': [],
            'sample_results': results[:3] if results else []
        }
        
        # Producer validation
        # JAVÍTÁS: Backend user_id paramétert használ producer szűréshez
        expected_user_id = api_params.get('user_id')
        if expected_user_id and results:
            producer_ids_in_results = []
            producer_names_in_results = []
            
            for result in results:
                # Try different possible field names - Backend szűrés user_id alapján
                result_user_id = result.get('user_id') or result.get('user', {}).get('id') if isinstance(result.get('user'), dict) else None
                result_producer_name = result.get('user_name') or result.get('user', {}).get('contact_name') if isinstance(result.get('user'), dict) else None
                
                if result_user_id:
                    producer_ids_in_results.append(result_user_id)
                if result_producer_name:
                    producer_names_in_results.append(result_producer_name)
            
            validation['producer_validation'] = {
                'expected_user_id': expected_user_id,
                'found_user_ids': list(set(producer_ids_in_results)),
                'found_producer_names': list(set(producer_names_in_results)),
                'id_match': expected_user_id in producer_ids_in_results if producer_ids_in_results else False,
                'result_count_per_producer': len(producer_ids_in_results)
            }
            
            # CRITICAL CHECK
            if not validation['producer_validation']['id_match']:
                validation['validation_issues'].append(
                    f"CRITICAL: Expected user_id {expected_user_id} but found {validation['producer_validation']['found_user_ids']}"
                )
        
        return validation    
    def validate_producer_mapping(self, ui_producer_id: Any, ui_producer_name: str, api_user_id: Any, results: List[Dict]) -> Dict[str, Any]:
        """Termelő ID és név leképezés validálása - JAVÍTÁS: Backend user_id paramétert használ"""
        
        validation = {
            'ui_producer_id': ui_producer_id,
            'ui_producer_name': ui_producer_name, 
            'api_user_id': api_user_id,  # Backend user_id paramétert használ!
            'has_issues': False,
            'errors': [],
            'actual_producers_in_results': [],
            'expected_vs_actual': {}
        }
        
        # Eredményekben szereplő termelők elemzése
        actual_producers = set()
        for result in results:
            producer_name = (result.get('user', {}).get('contact_name') or 
                           result.get('user_name') or 
                           result.get('user', {}).get('company_name'))
            producer_id = (result.get('user', {}).get('id') or 
                          result.get('user_id'))
            
            if producer_name:
                actual_producers.add((producer_name, producer_id))
        
        validation['actual_producers_in_results'] = list(actual_producers)
        
        # Kritikus ellenőrzések - JAVÍTÁS: Pozitív üzenet amikor működik
        if ui_producer_id is not None and api_user_id is not None:
            if ui_producer_id == api_user_id:
                # ✅ Sikeres konverzió - nincs hiba
                validation['errors'].append(f"✅ Producer filter conversion: UI producer_id={ui_producer_id} → API user_id={api_user_id}")
            else:
                validation['has_issues'] = True
                validation['errors'].append(f"❌ Producer ID mismatch: UI producer_id={ui_producer_id} vs API user_id={api_user_id}")
        elif ui_producer_id is not None and api_user_id is None:
            validation['has_issues'] = True
            validation['errors'].append(f"❌ UI has producer_id={ui_producer_id} but API user_id is missing")
        elif ui_producer_id is None and api_user_id is not None:
            validation['errors'].append(f"ℹ️ API user_id={api_user_id} set without UI producer filter")
        
        # Ha van producer szűrő, de az eredményekben más termelők szerepelnek
        if api_user_id and results:
            expected_found = False
            for producer_name, producer_id in actual_producers:
                if producer_id == api_user_id:
                    expected_found = True
                    break
            
            if not expected_found:
                validation['has_issues'] = True
                validation['errors'].append(f"❌ Producer ID {api_user_id} not found in results, but {len(actual_producers)} other producers found")
        
        # Név konzisztencia ellenőrzés
        if ui_producer_name and actual_producers:
            name_found = False
            for producer_name, _ in actual_producers:
                if producer_name == ui_producer_name:
                    name_found = True
                    break
            
            if not name_found and api_user_id:
                validation['has_issues'] = True
                validation['errors'].append(f"Expected producer name '{ui_producer_name}' not found in results")
        
        validation['expected_vs_actual'] = {
            'expected_name': ui_producer_name,
            'expected_user_id': api_user_id,  # Backend user_id paramétert használ!
            'actual_producers': validation['actual_producers_in_results'][:5]  # First 5
        }
        
        return validation
    
    def validate_ui_to_api_mapping(self, ui_filters: Dict[str, Any], api_params: Dict[str, Any]) -> Dict[str, Any]:
        """UI szűrők és API paraméterek közötti leképezés validálása"""
        
        validation = {
            'has_issues': False,
            'issues': [],
            'mapping_analysis': {}
        }
        
        # Producer mapping ellenőrzés - JAVÍTÁS: Backend user_id paramétert használ
        if 'producer_id' in ui_filters or 'producer_display_name' in ui_filters:
            ui_prod_id = ui_filters.get('producer_id')
            api_user_id = api_params.get('user_id')  # Backend user_id-t vár!
            
            validation['mapping_analysis']['producer'] = {
                'ui_has_producer': ui_prod_id is not None,
                'api_has_user_id': api_user_id is not None,
                'ids_match': ui_prod_id == api_user_id
            }
            
            if ui_prod_id and not api_user_id:
                validation['has_issues'] = True
                validation['issues'].append("UI has producer_id but API user_id parameter missing")
            elif ui_prod_id != api_user_id:
                validation['has_issues'] = True
                validation['issues'].append(f"Producer ID mismatch: UI={ui_prod_id}, API user_id={api_user_id}")
        
        # Status mapping ellenőrzés
        ui_status = ui_filters.get('status')
        api_status = api_params.get('status')
        
        validation['mapping_analysis']['status'] = {
            'ui_status': ui_status,
            'api_status': api_status,
            'conversion_correct': self._validate_status_conversion(ui_status, api_status)
        }
        
        # Date mapping ellenőrzés
        ui_from = ui_filters.get('from_date')
        ui_to = ui_filters.get('to_date')
        api_from = api_params.get('date_from')
        api_to = api_params.get('date_to')
        
        validation['mapping_analysis']['dates'] = {
            'ui_from_date': str(ui_from) if ui_from else None,
            'ui_to_date': str(ui_to) if ui_to else None,
            'api_from_date': api_from,
            'api_to_date': api_to,
            'conversion_correct': self._validate_date_conversion(ui_from, ui_to, api_from, api_to)
        }
        
        return validation
    
    def validate_results_consistency(self, api_params: Dict[str, Any], results: List[Dict]) -> Dict[str, Any]:
        """Eredmények konzisztenciájának ellenőrzése az API paraméterekkel"""
        
        validation = {
            'total_results': len(results),
            'consistency_checks': {},
            'inconsistencies': []
        }
        
        if not results:
            return validation
        
        # User ID konzisztencia (Backend szűrés user_id alapján)
        if 'user_id' in api_params:
            expected_user_id = api_params['user_id']
            matching_count = 0
            
            for result in results:
                result_user_id = (result.get('user', {}).get('id') or 
                                result.get('user_id'))
                if result_user_id == expected_user_id:
                    matching_count += 1
            
            validation['consistency_checks']['user_id'] = {
                'expected_id': expected_user_id,
                'matching_results': matching_count,
                'total_results': len(results),
                'consistency_rate': matching_count / len(results) if results else 0
            }
            
            if matching_count == 0:
                validation['inconsistencies'].append(f"No results match expected user_id {expected_user_id}")
            elif matching_count < len(results):
                validation['inconsistencies'].append(f"Only {matching_count}/{len(results)} results match user_id")
        
        # Status konzisztencia  
        if 'status' in api_params:
            expected_status = api_params['status']
            matching_statuses = sum(1 for result in results if result.get('status') == expected_status)
            
            validation['consistency_checks']['status'] = {
                'expected_status': expected_status,
                'matching_results': matching_statuses,
                'consistency_rate': matching_statuses / len(results) if results else 0
            }
            
            if matching_statuses < len(results):
                validation['inconsistencies'].append(f"Only {matching_statuses}/{len(results)} results match status {expected_status}")
        
        return validation
    
    def analyze_filter_effectiveness(self, ui_filters: Dict[str, Any], results: List[Dict]) -> Dict[str, Any]:
        """Szűrők hatékonyságának elemzése"""
        
        active_filters = {k: v for k, v in ui_filters.items() if v is not None and v != [] and v != ''}
        
        effectiveness = {
            'active_filter_count': len(active_filters),
            'results_count': len(results),
            'filter_density': len(active_filters) / max(len(results), 1),
            'active_filters': list(active_filters.keys())
        }
        
        # Szűrő típusok hatékonyság elemzése
        if active_filters:
            effectiveness['filter_analysis'] = {}
            
            for filter_name, filter_value in active_filters.items():
                effectiveness['filter_analysis'][filter_name] = {
                    'value': str(filter_value),
                    'type': type(filter_value).__name__,
                    'is_restrictive': self._is_filter_restrictive(filter_name, filter_value)
                }
        
        return effectiveness    
    def _validate_status_conversion(self, ui_status: Any, api_status: str) -> bool:
        """Státusz konverzió validálása"""
        if not ui_status:
            return api_status is None
        
        if isinstance(ui_status, list):
            return len(ui_status) == 1 and ui_status[0] == api_status
        
        return ui_status == api_status
    
    def _validate_date_conversion(self, ui_from: Any, ui_to: Any, api_from: str, api_to: str) -> bool:
        """Dátum konverzió validálása"""
        try:
            if ui_from and hasattr(ui_from, 'strftime'):
                expected_from = ui_from.strftime('%Y-%m-%d')
                if expected_from != api_from:
                    return False
            
            if ui_to and hasattr(ui_to, 'strftime'):
                expected_to = ui_to.strftime('%Y-%m-%d')
                if expected_to != api_to:
                    return False
            
            return True
        except:
            return False
    
    def _is_filter_restrictive(self, filter_name: str, filter_value: Any) -> bool:
        """Megállapítja, hogy a szűrő korlátozó-e"""
        if filter_name in ['search_term', 'product_name'] and filter_value:
            return True
        if filter_name in ['producer_id', 'producer_display_name'] and filter_value:
            return True
        if filter_name in ['status'] and filter_value:
            return True
        if filter_name in ['from_date', 'to_date'] and filter_value:
            return True
        return False
    
    def trace_api_execution(self, api_params: Dict[str, Any]) -> Dict[str, Any]:
        """API hívás nyomon követése"""
        
        trace = {
            'timestamp': datetime.now().isoformat(),
            'api_params': api_params.copy(),
            'execution_id': f"trace_{len(self.trace_log)}",
            'pre_execution_state': self._capture_system_state()
        }
        
        # Log API parameters for tracing
        logger.info(f"API Execution Trace [{trace['execution_id']}]: {api_params}")
        
        return trace
    
    def compare_expected_vs_actual(self, filters: Dict[str, Any], results: List[Dict]) -> Dict[str, Any]:
        """Várt vs tényleges eredmények összehasonlítása"""
        
        comparison = {
            'timestamp': datetime.now().isoformat(),
            'filters_applied': filters,
            'actual_results_count': len(results),
            'expected_patterns': {},
            'actual_patterns': {},
            'discrepancies': []
        }
        
        # Producer pattern elemzés
        if 'producer_id' in filters or 'producer_display_name' in filters:
            expected_producer = filters.get('producer_display_name')
            actual_producers = list(set([
                (result.get('user', {}).get('contact_name') or 
                 result.get('user_name') or 
                 result.get('user', {}).get('company_name'))
                for result in results
            ]))
            
            comparison['expected_patterns']['producer'] = expected_producer
            comparison['actual_patterns']['producer'] = actual_producers
            
            if expected_producer and expected_producer not in actual_producers:
                comparison['discrepancies'].append({
                    'type': 'producer_mismatch',
                    'expected': expected_producer,
                    'actual': actual_producers
                })
        
        return comparison
    
    def _capture_system_state(self) -> Dict[str, Any]:
        """Rendszer állapot rögzítése"""
        return {
            'session_state_keys': list(st.session_state.keys()),
            'cache_info': {
                'producers_cache': 'producers_cache' in st.session_state,
                'products_cache': 'products_cache' in st.session_state,
                'offers_cache': 'offers_cache' in st.session_state,
            }
        }
    
    def render_diagnostic_ui(self) -> None:
        """Diagnosztikai UI megjelenítése Streamlit-ben"""
        
        st.subheader("🔧 Filter Diagnostic Tool")
        
        # Get current state - használjuk a session state-et közvetlenül
        try:
            if 'filter_panel' in st.session_state:
                ui_filters = st.session_state.filter_panel.get_current_filters()
            else:
                ui_filters = {}
                st.warning("Filter panel not found in session state")
        except Exception as e:
            st.error(f"Error getting current filters: {str(e)}")
            ui_filters = {}
            
        # Manual diagnostic trigger
        if st.button("🚨 Run Full Diagnostic", type="primary"):
            
            with st.spinner("Running filter chain diagnosis..."):
                try:
                    # Get API params
                    if self.data_coordinator:
                        api_params = self.data_coordinator.convert_filters_to_api_params(ui_filters)
                        
                        # Get current results
                        results = self.data_coordinator.load_offers(ui_filters)
                        
                        # Run diagnosis
                        diagnosis = self.diagnose_filter_chain(ui_filters, api_params, results)
                        
                        # Display results
                        self._render_diagnosis_results(diagnosis)
                        
                    else:
                        st.error("Data coordinator not available")
                        
                except Exception as e:
                    st.error(f"Diagnostic failed: {str(e)}")
                    logger.error(f"Diagnostic error: {e}", exc_info=True)
        
        # Show current filter state
        with st.expander("Current Filter State", expanded=False):
            st.json(ui_filters)
    
    def _render_diagnosis_results(self, diagnosis: Dict):
        """Diagnózis eredmények megjelenítése"""
        
        # Status header
        status = diagnosis.get('validation_status', 'unknown')
        status_icon = "🔴" if status == 'failed' else "🟢"
        issue_count = len(diagnosis.get('issues_found', []))
        
        st.markdown(f"### {status_icon} Diagnózis Eredmény: **{status.upper()}** ({issue_count} hiba)")
        
        # Critical issues
        if diagnosis.get('issues_found'):
            st.error("**TALÁLT HIBÁK:**")
            for i, issue in enumerate(diagnosis['issues_found'], 1):
                st.error(f"{i}. {issue}")
        
        # Raw data for debugging
        with st.expander("🔧 Raw Diagnosis Data", expanded=False):
            st.json(diagnosis)
    
    def get_diagnostic_report(self) -> Dict:
        """Diagnosztikai jelentés generálása"""
        return {
            'session_id': self.diagnostic_session_id,
            'timestamp': datetime.now().isoformat(),
            'trace_log_count': len(self.trace_log),
            'has_state_manager': self.state_manager is not None,
            'has_data_coordinator': self.data_coordinator is not None
        }
    
    def get_real_time_producer_status(self) -> Dict[str, Any]:
        """Real-time producer filter status check"""
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'ui_producer_filter': None,
            'api_producer_id': None,
            'consistency_check': 'unknown',
            'validation_metadata': None,
            'errors': []
        }
        
        try:
            # Get current UI state
            if 'modern_producer_filter' in st.session_state:
                status['ui_producer_filter'] = st.session_state.modern_producer_filter
                logger.info(f"🔍 Real-time status: Found UI producer filter: {status['ui_producer_filter']}")
            
            # Get current filter manager state
            if hasattr(self.state_manager, 'filter_manager'):
                ui_filters = self.state_manager.filter_manager.get_ui_filters()
                logger.info(f"🔍 Real-time status: UI filters from manager: {ui_filters}")
                
                # Convert to API params
                from .api_parameter_converter import APIParameterConverter
                converter = APIParameterConverter()
                api_params = converter.convert_filters(ui_filters)
                
                # JAVÍTÁS: Backend user_id paramétert használ producer szűréshez
                status['api_user_id'] = api_params.get('user_id')
                status['validation_metadata'] = api_params.get('_producer_validation_metadata')
                
                logger.info(f"🔍 Real-time status: API user_id: {status['api_user_id']}")
                
                # Check consistency
                if status['ui_producer_filter'] and status['api_user_id']:
                    if isinstance(status['ui_producer_filter'], tuple):
                        name, ui_id = status['ui_producer_filter']
                        consistency = str(ui_id) == str(status['api_user_id'])
                        status['consistency_check'] = 'PASS' if consistency else 'FAIL'
                        
                        if not consistency:
                            status['errors'].append(f"UI ID {ui_id} != API user_id {status['api_user_id']}")
                    else:
                        status['errors'].append(f"UI producer filter not tuple: {type(status['ui_producer_filter'])}")
                        status['consistency_check'] = 'FAIL'
                else:
                    status['consistency_check'] = 'NO_DATA'
            
        except Exception as e:
            status['errors'].append(f"Status check failed: {str(e)}")
            status['consistency_check'] = 'ERROR'
            logger.error(f"Real-time producer status check failed: {e}")
        
        return status
    
    def test_producer_filter_variants(self, producer_id: int) -> Dict[str, Any]:
        """Különböző API paraméter változatok tesztelése automatikusan"""
        
        test_cases = [
            {'user_id': producer_id},
            {'producer_id': producer_id},
            {'created_by_user_id': producer_id},
            {'filter_user_id': producer_id},
            {'owner_id': producer_id}
        ]
        
        results = {
            'test_timestamp': datetime.now().isoformat(),
            'tested_producer_id': producer_id,
            'test_cases': {},
            'summary': {
                'total_tests': len(test_cases),
                'successful_tests': 0,
                'failed_tests': 0,
                'best_test': None
            }
        }
        
        logger.critical(f"🧪 TESTING PRODUCER VARIANTS: Starting {len(test_cases)} tests for producer_id={producer_id}")
        
        for i, params in enumerate(test_cases):
            test_name = f"test_{i}_{list(params.keys())[0]}"
            
            try:
                # Import API client dynamically
                from .api_client import get_offers
                
                logger.info(f"🔬 Test {test_name}: Testing with params {params}")
                
                success, data = get_offers(params)
                
                if success and data:
                    # Count matching offers for this producer ID
                    matching_offers = self._count_matching_offers(data, producer_id)
                    
                    results['test_cases'][test_name] = {
                        'params': params,
                        'api_success': True,
                        'total_offers': len(data),
                        'matching_offers': matching_offers,
                        'match_rate': matching_offers / len(data) if data else 0,
                        'test_success': matching_offers > 0,
                        'sample_offer_fields': list(data[0].keys()) if data else []
                    }
                    
                    if matching_offers > 0:
                        results['summary']['successful_tests'] += 1
                        logger.info(f"✅ Test {test_name}: SUCCESS - {matching_offers} matching offers")
                    else:
                        results['summary']['failed_tests'] += 1
                        logger.warning(f"⚠️ Test {test_name}: FAILED - No matching offers")
                        
                else:
                    results['test_cases'][test_name] = {
                        'params': params,
                        'api_success': False,
                        'error': data if not success else 'Unknown error',
                        'test_success': False
                    }
                    results['summary']['failed_tests'] += 1
                    logger.error(f"❌ Test {test_name}: API ERROR - {data}")
                    
            except Exception as e:
                results['test_cases'][test_name] = {
                    'params': params,
                    'api_success': False,
                    'exception': str(e),
                    'test_success': False
                }
                results['summary']['failed_tests'] += 1
                logger.error(f"🚨 Test {test_name}: EXCEPTION - {str(e)}")
        
        # Identify best test result
        best_test = None
        best_score = 0
        
        for test_name, test_result in results['test_cases'].items():
            if test_result.get('test_success') and test_result.get('matching_offers', 0) > best_score:
                best_score = test_result['matching_offers']
                best_test = {
                    'test_name': test_name,
                    'params': test_result['params'],
                    'matching_offers': test_result['matching_offers'],
                    'total_offers': test_result['total_offers']
                }
        
        results['summary']['best_test'] = best_test
        
        logger.critical(f"🎯 PRODUCER VARIANT TESTING COMPLETE: {results['summary']['successful_tests']}/{results['summary']['total_tests']} tests successful")
        
        return results
    
    def _count_matching_offers(self, offers: List[Dict], expected_producer_id: int) -> int:
        """Megszámolja a producer ID-nak megfelelő ajánlatokat különböző mezőkben"""
        
        count = 0
        expected_str = str(expected_producer_id)
        
        for offer in offers:
            # Különböző mezők ellenőrzése
            user_id = offer.get('user_id')
            created_by_id = offer.get('created_by_user_id')
            user_obj_id = offer.get('user', {}).get('id') if isinstance(offer.get('user'), dict) else None
            producer_id = offer.get('producer_id')
            owner_id = offer.get('owner_id')
            
            offer_producer_ids = [
                str(user_id) if user_id else None,
                str(created_by_id) if created_by_id else None,
                str(user_obj_id) if user_obj_id else None,
                str(producer_id) if producer_id else None,
                str(owner_id) if owner_id else None
            ]
            
            if expected_str in [id for id in offer_producer_ids if id]:
                count += 1
        
        return count
    
    def run_comprehensive_producer_test(self, ui_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Komprehenzív producer szűrő teszt - ez a fő diagnosztikai function"""
        
        logger.critical(f"🚀 COMPREHENSIVE PRODUCER TEST STARTED: {datetime.now().isoformat()}")
        
        if 'producer_filter' not in ui_filters:
            return {
                'error': 'No producer filter in UI filters',
                'ui_filters': ui_filters
            }
        
        producer_data = ui_filters['producer_filter']
        if not isinstance(producer_data, tuple) or len(producer_data) != 2:
            return {
                'error': f'Invalid producer data format: {producer_data}',
                'expected_format': 'tuple(name, id)'
            }
        
        name, producer_id = producer_data
        
        try:
            producer_id_int = int(producer_id)
        except (ValueError, TypeError):
            return {
                'error': f'Producer ID is not a valid integer: {producer_id}',
                'producer_data': producer_data
            }
        
        # 1. Get current API parameters
        try:
            current_api_params = self.data_coordinator.convert_filters_to_api_params(ui_filters)
        except Exception as e:
            current_api_params = {'error': str(e)}
        
        # 2. Test different API parameter variants
        variant_results = self.test_producer_filter_variants(producer_id_int)
        
        # 3. Get current API call results for comparison
        try:
            current_results = self.data_coordinator.load_offers(ui_filters)
            current_matching = self._count_matching_offers(current_results, producer_id_int)
        except Exception as e:
            current_results = []
            current_matching = 0
            logger.error(f"Error getting current results: {e}")
        
        # 4. Analyze field usage in current results
        field_analysis = {}
        if current_results:
            field_analysis = self._analyze_field_usage_in_results(current_results)
        
        # 5. Generate recommendations
        recommendation = self._generate_producer_fix_recommendation(variant_results, current_matching)
        
        comprehensive_result = {
            'test_timestamp': datetime.now().isoformat(),
            'producer_info': {
                'name': name,
                'id': producer_id_int,
                'ui_data': producer_data
            },
            'current_situation': {
                'api_params': current_api_params,
                'results_count': len(current_results),
                'matching_offers': current_matching,
                'field_analysis': field_analysis
            },
            'variant_test_results': variant_results,
            'recommendation': recommendation,
            'summary': {
                'test_successful': recommendation['status'] == 'fix_available',
                'current_filter_working': current_matching > 0,
                'improvement_available': recommendation.get('expected_results', 0) > current_matching
            }
        }
        
        logger.critical(f"🎯 COMPREHENSIVE TEST COMPLETE: Status={recommendation['status']}, Current matches={current_matching}")
        
        return comprehensive_result
    
    def _analyze_field_usage_in_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Elemzi a mezők használatát az eredményekben"""
        
        field_analysis = {
            'total_offers': len(results),
            'field_usage': {},
            'user_id_distribution': {},
            'most_common_fields': []
        }
        
        # Track field usage
        field_counts = {}
        user_id_values = {}
        
        for offer in results:
            # Check all user/producer related fields
            fields_to_check = ['user_id', 'created_by_user_id', 'producer_id', 'owner_id']
            for field in fields_to_check:
                if field in offer and offer[field] is not None:
                    field_counts[field] = field_counts.get(field, 0) + 1
                    
                    if field not in user_id_values:
                        user_id_values[field] = {}
                    
                    value = offer[field]
                    user_id_values[field][value] = user_id_values[field].get(value, 0) + 1
            
            # Check nested user object
            if 'user' in offer and isinstance(offer['user'], dict) and 'id' in offer['user']:
                field_counts['user.id'] = field_counts.get('user.id', 0) + 1
                
                if 'user.id' not in user_id_values:
                    user_id_values['user.id'] = {}
                
                value = offer['user']['id']
                user_id_values['user.id'][value] = user_id_values['user.id'].get(value, 0) + 1
        
        field_analysis['field_usage'] = field_counts
        field_analysis['user_id_distribution'] = user_id_values
        
        # Find most commonly used fields
        sorted_fields = sorted(field_counts.items(), key=lambda x: x[1], reverse=True)
        field_analysis['most_common_fields'] = sorted_fields[:3]
        
        return field_analysis
    
    def _generate_producer_fix_recommendation(self, variant_results: Dict, current_matching: int) -> Dict[str, Any]:
        """Producer szűrő javítási javaslat generálása"""
        
        recommendation = {
            'status': 'no_fix_found',
            'message': 'Nincs működő megoldás találva',
            'current_performance': current_matching,
            'recommended_params': None,
            'expected_results': 0,
            'confidence': 'low'
        }
        
        # Find the best performing test
        best_test = variant_results['summary'].get('best_test')
        
        if best_test and best_test['matching_offers'] > current_matching:
            recommendation = {
                'status': 'fix_available',
                'message': f'Találtunk jobb megoldást: {best_test["matching_offers"]} egyező ajánlat',
                'current_performance': current_matching,
                'recommended_params': best_test['params'],
                'expected_results': best_test['matching_offers'],
                'confidence': 'high' if best_test['matching_offers'] > best_test['total_offers'] * 0.5 else 'medium',
                'improvement': best_test['matching_offers'] - current_matching
            }
        elif best_test and best_test['matching_offers'] == current_matching > 0:
            recommendation = {
                'status': 'alternative_available',
                'message': f'Alternatív megoldás ugyanolyan eredménnyel: {best_test["matching_offers"]} ajánlat',
                'current_performance': current_matching,
                'recommended_params': best_test['params'],
                'expected_results': best_test['matching_offers'],
                'confidence': 'medium'
            }
        elif current_matching > 0:
            recommendation = {
                'status': 'current_working',
                'message': f'A jelenlegi szűrő működik: {current_matching} egyező ajánlat',
                'current_performance': current_matching,
                'recommended_params': None,
                'expected_results': current_matching,
                'confidence': 'high'
            }
        
        return recommendation
    
    def recommend_producer_parameters(self, producer_id: int) -> Dict[str, Any]:
        """
        Smart producer parameter recommendation based on testing
        """
        recommendations = {
            'primary_strategy': 'user_id_only',
            'parameters': {'user_id': producer_id},
            'confidence': 'high',
            'reasoning': 'user_id parameter proven to work in diagnostic tests',
            'validated': False,
            'expected_results': 0
        }
        
        logger.info(f"🎯 Generating producer parameter recommendations for ID {producer_id}")
        
        # Test the recommendation
        try:
            test_result = self._test_producer_parameter_combination({'user_id': producer_id})
            
            if test_result['success'] and test_result['matching_offers'] > 0:
                recommendations['validated'] = True
                recommendations['expected_results'] = test_result['matching_offers']
                logger.info(f"✅ Primary recommendation validated: {test_result['matching_offers']} matching offers")
            else:
                # Fallback strategy
                logger.warning("⚠️ Primary recommendation failed, trying fallback strategy")
                recommendations['primary_strategy'] = 'dual_parameters'
                recommendations['parameters'] = {
                    'user_id': producer_id,
                    'producer_id': producer_id
                }
                recommendations['reasoning'] = 'Dual parameter strategy as fallback'
                
                # Test fallback
                fallback_result = self._test_producer_parameter_combination(recommendations['parameters'])
                if fallback_result['success']:
                    recommendations['validated'] = True
                    recommendations['expected_results'] = fallback_result['matching_offers']
                    recommendations['confidence'] = 'medium'
                
        except Exception as e:
            logger.error(f"Parameter recommendation test failed: {e}")
            recommendations['error'] = str(e)
            recommendations['confidence'] = 'low'
            
        return recommendations
    
    def _test_producer_parameter_combination(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Test a specific parameter combination"""
        try:
            from .api_client import get_offers
            
            logger.info(f"🔬 Testing parameter combination: {params}")
            success, data = get_offers(params)
            
            if success and data:
                # Count matching offers - use the producer_id from params for counting
                producer_id = params.get('user_id') or params.get('producer_id')
                matching_offers = self._count_matching_offers(data, producer_id) if producer_id else 0
                
                result = {
                    'success': True,
                    'total_offers': len(data),
                    'matching_offers': matching_offers,
                    'match_rate': matching_offers / len(data) if data else 0,
                    'params_tested': params
                }
                
                logger.info(f"✅ Test successful: {matching_offers}/{len(data)} matching offers")
                return result
            else:
                logger.error(f"❌ API call failed: {data}")
                return {
                    'success': False,
                    'error': data,
                    'params_tested': params
                }
                
        except Exception as e:
            logger.error(f"🚨 Test exception: {e}")
            return {
                'success': False,
                'exception': str(e),
                'params_tested': params
            }


# STANDALONE FUNCTION (class-on kívül):
def render_filter_diagnostic_panel(diagnostic_tool: FilterDiagnosticTool, ui_filters: Dict[str, Any], api_params: Dict[str, Any], results: List[Dict]):
    """Diagnosztikai panel megjelenítése"""
    
    with st.expander("🚨 Filter Diagnostic Tool", expanded=False):
        st.write("**Real-time szűrő diagnosztika**")
        st.info("ℹ️ **Megjegyzés:** A backend API `user_id` paramétert használ a termelő azonosításához (nem `producer_id`-t)")
        
        # Diagnosztika futtatása
        if st.button("🔍 Teljes diagnosztika futtatása", key="run_full_diagnostic"):
            with st.spinner("Diagnosztika futtatása..."):
                diagnosis = diagnostic_tool.diagnose_filter_chain(ui_filters, api_params, results)
                
                # Eredmények megjelenítése
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**Validációs hibák:**")
                    if diagnosis.get('issues_found'):
                        for error in diagnosis['issues_found']:
                            st.error(f"❌ {error}")
                    else:
                        st.success("✅ Nincsenek validációs hibák")
                
                with col2:
                    st.write("**Producer validáció:**")
                    if 'producer_analysis' in diagnosis:
                        prod_analysis = diagnosis['producer_analysis']
                        if prod_analysis.get('errors'):
                            for issue in prod_analysis['errors']:
                                # Megkülönbözteti a sikeres vs hibás üzeneteket
                                if issue.startswith("✅"):
                                    st.success(issue)
                                elif issue.startswith("ℹ️"):
                                    st.info(issue)
                                elif issue.startswith("❌"):
                                    st.error(issue)
                                else:
                                    st.warning(f"⚠️ {issue}")
                        else:
                            st.success("✅ Producer mapping OK")
                
                # Szűrő hatékonyság
                if 'result_validation' in diagnosis and 'validation_issues' in diagnosis['result_validation']:
                    result_issues = diagnosis['result_validation']['validation_issues']
                    if result_issues:
                        st.write("**Kritikus eredmény problémák:**")
                        for issue in result_issues:
                            st.error(f"🚨 {issue}")
        
        # Diagnosztikai jelentés
        if st.button("📋 Diagnosztikai jelentés", key="diagnostic_report"):
            report = diagnostic_tool.get_diagnostic_report()
            st.json(report)
