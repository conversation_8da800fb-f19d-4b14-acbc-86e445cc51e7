"""
Advanced multi-level sorting functionality for the offer management page.
Provides enhanced data sorting capabilities with multiple criteria.
"""
import streamlit as st
import pandas as pd
import logging
import uuid
from datetime import datetime

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in advanced_sorting.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Logger setup
logger = logging.getLogger(__name__)

# Define available sort options with display names and field paths
SORT_OPTIONS = {
    "id": {
        "display_name": "Azonosító",
        "field": "id",
        "default_order": "ascending"
    },
    "status": {
        "display_name": "Státusz",
        "field": "status",
        "default_order": "ascending"
    },
    "delivery_date": {
        "display_name": "Beszállítás dátuma",
        "field": "delivery_date",
        "default_order": "ascending"
    },
    "product_name": {
        "display_name": "Termék",
        "field": "product_type.name",
        "default_order": "ascending"
    },
    "quantity_in_kg": {
        "display_name": "Mennyiség",
        "field": "quantity_in_kg",
        "default_order": "descending"
    },
    "price": {
        "display_name": "Ár",
        "field": "price",
        "default_order": "descending"
    },
    "created_at": {
        "display_name": "Létrehozva",
        "field": "created_at",
        "default_order": "descending"
    },
    "producer_name": {
        "display_name": "Termelő",
        "field": "user.contact_name",
        "default_order": "ascending"
    }
}

def generate_unique_key(base_name, suffix=None):
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name (str): Base name for the key
        suffix (str, optional): Optional suffix to add. Defaults to None.
        
    Returns:
        str: Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def get_nested_value(obj, path):
    """
    Get a value from a nested object using a dot-separated path.
    
    Args:
        obj (dict): Object to extract value from
        path (str): Dot-separated path to the value
        
    Returns:
        any: The extracted value or None if not found
    """
    try:
        parts = path.split('.')
        value = obj
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            else:
                return None
        return value
    except (AttributeError, KeyError, TypeError):
        return None

def sort_offers(offers, sort_criteria):
    """
    Sort offers based on multiple criteria.
    
    Args:
        offers (list): List of offer dictionaries
        sort_criteria (list): List of (field, order) tuples
        
    Returns:
        list: Sorted offers
    """
    if not offers or not sort_criteria:
        return offers
    
    # Convert to DataFrame for easier sorting
    df = pd.DataFrame(offers)
    
    # For nested fields, we need to extract values first
    for field, order in sort_criteria:
        if "." in field:
            # Extract the nested field value
            field_name = field.replace(".", "_")
            df[field_name] = df.apply(lambda row: get_nested_value(row.to_dict(), field), axis=1)
            # Use this extracted field for sorting
            field = field_name
        
        # Convert string date fields to datetime for proper sorting
        if field in ["delivery_date", "created_at", "updated_at"] or field.endswith("_date"):
            df[field] = pd.to_datetime(df[field], errors='coerce')
    
    # Build sort specification
    sort_spec = []
    for field, order in sort_criteria:
        # Replace dots with underscores for extracted nested fields
        if "." in field:
            field = field.replace(".", "_")
        
        ascending = order.lower() == "ascending"
        sort_spec.append((field, ascending))
    
    # Apply sorting
    if sort_spec:
        # Convert sort_spec to the format expected by sort_values
        sort_columns = [spec[0] for spec in sort_spec]
        sort_ascending = [spec[1] for spec in sort_spec]
        
        df = df.sort_values(by=sort_columns, ascending=sort_ascending)
    
    # Convert back to list of dictionaries
    return df.to_dict('records')

def render_sort_field_selector(index, existing_value=None):
    """
    Render a dropdown selector for sort field.
    
    Args:
        index (int): Index of the sort criteria
        existing_value (str, optional): Currently selected field. Defaults to None.
        
    Returns:
        tuple: (selected_field, selected_field_path)
    """
    options = list(SORT_OPTIONS.keys())
    
    # Format function to display the friendly names
    def format_option(option):
        return SORT_OPTIONS[option]["display_name"]
    
    # Unique key for this selector
    key = generate_unique_key(f"sort_field_{index}")
    
    # Set default value based on index or existing value
    if existing_value and existing_value in options:
        default_value = existing_value
    elif index < len(options):
        default_value = options[index]
    else:
        default_value = options[0]
    
    # Create the dropdown
    selected_field = st.selectbox(
        f"Rendezési szempont {index+1}",
        options=options,
        format_func=format_option,
        key=key,
        index=options.index(default_value)
    )
    
    # Return both the selected field key and its actual path
    return selected_field, SORT_OPTIONS[selected_field]["field"]

def render_sort_order_selector(index, field=None, existing_order=None):
    """
    Render a radio button selector for sort order.
    
    Args:
        index (int): Index of the sort criteria
        field (str, optional): Selected field. Defaults to None.
        existing_order (str, optional): Currently selected order. Defaults to None.
        
    Returns:
        str: Selected order ("ascending" or "descending")
    """
    # Unique key for this selector
    key = generate_unique_key(f"sort_order_{index}")
    
    # Determine default selection based on field or existing value
    default_order = "ascending"
    if existing_order:
        default_order = existing_order
    elif field and field in SORT_OPTIONS:
        default_order = SORT_OPTIONS[field]["default_order"]
    
    # Map to indices for default selection
    order_options = ["ascending", "descending"]
    default_index = 0 if default_order == "ascending" else 1
    
    # Create the radio buttons
    selected_order = st.radio(
        "Rendezés iránya:",
        options=order_options,
        index=default_index,
        format_func=lambda x: "Növekvő ▲" if x == "ascending" else "Csökkenő ▼",
        key=key,
        horizontal=True
    )
    
    return selected_order

def render_sort_criteria_ui(max_levels=3):
    """
    Render UI for setting multiple sort criteria.
    
    Args:
        max_levels (int, optional): Maximum number of sort levels. Defaults to 3.
        
    Returns:
        list: List of (field_path, order) tuples
    """
    # Check if we have existing sort criteria in session state
    if "sort_criteria" not in st.session_state:
        st.session_state["sort_criteria"] = []
    
    # Store current criteria count
    if "sort_criteria_count" not in st.session_state:
        st.session_state["sort_criteria_count"] = min(1, len(st.session_state["sort_criteria"]))
    
    # Add/remove buttons logic
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("### Többszintű rendezés")
        
    with col2:
        # Add new criteria button
        if st.session_state["sort_criteria_count"] < max_levels:
            if st.button("+ Új szempont hozzáadása", key=generate_unique_key("add_criteria")):
                st.session_state["sort_criteria_count"] += 1
                st.rerun()
        
        # Remove criteria button
        if st.session_state["sort_criteria_count"] > 1:
            if st.button("- Szempont eltávolítása", key=generate_unique_key("remove_criteria")):
                st.session_state["sort_criteria_count"] -= 1
                # Also remove from actual criteria if needed
                if len(st.session_state["sort_criteria"]) >= st.session_state["sort_criteria_count"]:
                    st.session_state["sort_criteria"] = st.session_state["sort_criteria"][:st.session_state["sort_criteria_count"]]
                st.rerun()
    
    # Display sort criteria UI
    criteria = []
    for i in range(st.session_state["sort_criteria_count"]):
        st.markdown(f"#### {i+1}. szintű rendezés")
        
        # Get existing values if available
        existing_field = None
        existing_order = None
        if i < len(st.session_state["sort_criteria"]):
            existing_field, existing_order = st.session_state["sort_criteria"][i]
        
        # Render field and order selectors in columns
        col1, col2 = st.columns([3, 2])
        with col1:
            field_key, field_path = render_sort_field_selector(i, existing_field)
        with col2:
            order = render_sort_order_selector(i, field_key, existing_order)
        
        # Add to criteria list
        criteria.append((field_path, order))
        
        # Add separator except for last item
        if i < st.session_state["sort_criteria_count"] - 1:
            st.markdown("---")
    
    # Apply button
    if st.button("Rendezés alkalmazása", key=generate_unique_key("apply_sort")):
        st.session_state["sort_criteria"] = criteria
        
        # Show success message
        if len(criteria) > 0:
            criteria_desc = ", ".join([
                f"{SORT_OPTIONS[field]['display_name']} ({'növekvő' if order == 'ascending' else 'csökkenő'})"
                for field, order in criteria
            ])
            show_inline_success(f"Rendezés alkalmazva: {criteria_desc}")
    
    return st.session_state["sort_criteria"]

def render_sort_indicator(active_criteria):
    """
    Render visual indicator for active sort criteria.
    
    Args:
        active_criteria (list): List of active (field, order) tuples
    """
    if not active_criteria:
        return
        
    # Build HTML for sort badges
    html = """
    <style>
    .sort-indicators {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin: 10px 0;
    }
    .sort-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        background-color: #f0f2f6;
        border-radius: 4px;
        font-size: 0.8em;
        color: #333;
    }
    .sort-level {
        background-color: #1976D2;
        color: white;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 6px;
        font-size: 0.7em;
        font-weight: bold;
    }
    .sort-order {
        margin-left: 4px;
        color: #666;
    }
    </style>
    
    <div class="sort-indicators">
    """
    
    for i, (field, order) in enumerate(active_criteria):
        # Find display name for the field
        field_key = next((k for k in SORT_OPTIONS if SORT_OPTIONS[k]["field"] == field), None)
        if field_key:
            display_name = SORT_OPTIONS[field_key]["display_name"]
        else:
            # Fall back to field path if not found
            display_name = field.replace(".", " ")
        
        # Add order icon
        order_icon = "▲" if order == "ascending" else "▼"
        
        # Add badge HTML
        html += f"""
        <div class="sort-badge">
            <span class="sort-level">{i+1}</span>
            <span>{display_name}</span>
            <span class="sort-order">{order_icon}</span>
        </div>
        """
    
    html += "</div>"
    
    # Render the HTML
    st.markdown(html, unsafe_allow_html=True)

def render_quick_sort_buttons(common_sorts):
    """
    Render quick buttons for common sort combinations.
    
    Args:
        common_sorts (dict): Dictionary of named sort combinations
        
    Returns:
        bool: True if a quick sort was applied
    """
    st.markdown("### Gyors rendezés")
    
    # Create buttons in a row
    columns = st.columns(len(common_sorts))
    
    sort_applied = False
    for i, (name, criteria) in enumerate(common_sorts.items()):
        with columns[i]:
            if st.button(name, key=f"quick_sort_{i}"):
                st.session_state["sort_criteria"] = criteria
                st.session_state["sort_criteria_count"] = len(criteria)
                show_inline_success(f"Rendezés alkalmazva: {name}")
                sort_applied = True
    
    return sort_applied

def apply_multi_level_sorting(offers, rerender=False):
    """
    Apply multi-level sorting to offers and render UI controls.
    
    Args:
        offers (list): List of offer dictionaries
        rerender (bool, optional): Force UI rerender. Defaults to False.
        
    Returns:
        list: Sorted offers
    """
    # Common predefined sort combinations
    common_sorts = {
        "Legújabbak először": [
            ("created_at", "descending")
        ],
        "Beszállítási dátum szerint": [
            ("delivery_date", "ascending")
        ],
        "Legnagyobb mennyiség": [
            ("quantity_in_kg", "descending")
        ],
        "Státusz és dátum": [
            ("status", "ascending"),
            ("delivery_date", "ascending")
        ]
    }
    
    # Render quick sort buttons
    with st.expander("Rendezési beállítások", expanded=rerender):
        quick_sort_applied = render_quick_sort_buttons(common_sorts)
        
        st.markdown("---")
        
        # Render detailed sort criteria UI
        sort_criteria = render_sort_criteria_ui()
        
        # If no criteria are set, use a default sort
        if not sort_criteria:
            sort_criteria = [("created_at", "descending")]
    
    # Render active sort indicator if we have criteria
    if sort_criteria:
        if st.session_state.get("sort_display_toggle", True):
            render_sort_indicator(sort_criteria)
    
    # Apply sorting
    sorted_offers = sort_offers(offers, sort_criteria)
    
    # Rerun if quick sort was applied to update UI
    if quick_sort_applied:
        st.rerun()
    
    return sorted_offers

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Multi-level Sorting Test", layout="wide")
    
    st.title("Multi-level Sorting Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"contact_name": "Termelő Tamás"}
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"contact_name": "Mezőgazda Márton"}
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-04-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"contact_name": "Termelő Tamás"}
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-03-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"contact_name": "Gyümölcsös Gábor"}
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-04-25T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"contact_name": "Almás Anna"}
        }
    ]
    
    # Apply sorting
    sorted_offers = apply_multi_level_sorting(sample_offers, rerender=True)
    
    # Display sorted data
    st.markdown("### Rendezett ajánlatok")
    
    # Convert to DataFrame for display
    df = pd.DataFrame([
        {
            "ID": offer["id"],
            "Státusz": format_status(offer["status"]),
            "Beszállítás": format_date(offer["delivery_date"]),
            "Termék": offer["product_type"]["name"],
            "Mennyiség": f"{offer['quantity_in_kg']} kg",
            "Termelő": offer["user"]["contact_name"],
            "Létrehozva": format_datetime(offer["created_at"])
        }
        for offer in sorted_offers
    ])
    
    st.dataframe(
        df,
        hide_index=True,
        use_container_width=True
    )