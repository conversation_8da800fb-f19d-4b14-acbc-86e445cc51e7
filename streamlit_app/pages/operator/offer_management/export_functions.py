"""
Exportálási funkciók az ajánlatkezeléshez.
Tartalmazza a CSV és Excel formátumú exportálás funkcióit.
"""
import streamlit as st
import pandas as pd
import base64
import io
import logging
from datetime import datetime

# Try different import paths for formatting functions
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_date, format_quantity, format_price, format_datetime
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_date, format_quantity, format_price, format_datetime
    except ImportError:
        # Fallback formatting functions if imports fail
        logging.warning("Could not import formatting functions, using fallbacks")
        format_status = lambda x: x if x else "Ismeretlen"
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

logger = logging.getLogger(__name__)

def export_offers_to_csv(offers):
    """
    Ajánlatok exportálása CSV formátumba.
    
    Ez a függvény az ajánlatok listáját CSV formátumba konvertálja,
    és egy letölthető linket ad vissza. A táblázat tartalmazza az összes
    releváns adatot, megfelelően formázva.
    
    Args:
        offers (list): Az exportálandó ajánlatok listája
        
    Returns:
        str: HTML kód a letöltési linkkel
    """
    import pandas as pd
    import base64
    from io import StringIO
    
    # Ellenőrizzük, hogy van-e exportálandó adat
    if not offers:
        return "<p style='color:red'>Nincs exportálható adat</p>"
    
    try:
        # Adatok előkészítése a DataFrame-hez
        export_data = []
        for offer in offers:
            # Alapadatok kinyerése
            # Termék információ
            product_type = offer.get("product_type", {})
            product_name = product_type.get("name", "Ismeretlen termék") if product_type else "Ismeretlen termék"
            product_unit = product_type.get("unit", "kg") if product_type else "kg"
            
            # Termelő információ
            user = offer.get("user", {})
            company_name = user.get("company_name", "") if user else ""
            contact_name = user.get("contact_name", "Ismeretlen termelő") if user else "Ismeretlen termelő"
            producer_name = company_name or contact_name
            producer_email = user.get("email", "") if user else ""
            producer_phone = user.get("phone", "") if user else ""
            
            # Minőségi osztály
            quality_grade = offer.get("quality_grade", {})
            quality_grade_name = quality_grade.get("name", "") if quality_grade else ""
            
            # Biztonságos konverziók
            try:
                quantity = float(offer.get("quantity_in_kg", 0))
            except (ValueError, TypeError):
                quantity = 0
                
            try:
                confirmed_quantity = float(offer.get("confirmed_quantity", 0))
            except (ValueError, TypeError):
                confirmed_quantity = 0
                
            try:
                price = float(offer.get("price", 0))
            except (ValueError, TypeError):
                price = 0
                
            try:
                confirmed_price = float(offer.get("confirmed_price", 0))
            except (ValueError, TypeError):
                confirmed_price = 0
            
            # Időbélyegek
            created_at = format_datetime(offer.get("created_at", ""))
            updated_at = format_datetime(offer.get("updated_at", ""))
            delivery_date = format_date(offer.get("delivery_date", ""))
            
            # Megjegyzés
            note = offer.get("note", "")
            
            # Csatolmányok száma
            attachments_count = len(offer.get("attachments", []))
            
            # Státusz és ID
            status = format_status(offer.get("status", ""))
            offer_id = offer.get("id", "")
            
            # Ajánlat adatok előkészítése exporthoz
            export_row = {
                "Azonosító": offer_id,
                "Státusz": status,
                "Termék": product_name,
                "Termelő neve": producer_name,
                "Termelő email": producer_email,
                "Termelő telefon": producer_phone,
                "Mennyiség (kg)": quantity,
                "Visszaigazolt mennyiség (kg)": confirmed_quantity,
                "Egységár (Ft/kg)": price,
                "Visszaigazolt egységár (Ft/kg)": confirmed_price,
                "Egység": product_unit,
                "Minőségi osztály": quality_grade_name,
                "Beszállítási dátum": delivery_date,
                "Létrehozva": created_at,
                "Utoljára módosítva": updated_at,
                "Csatolmányok száma": attachments_count,
                "Megjegyzés": note
            }
            
            export_data.append(export_row)
        
        # DataFrame létrehozása
        df = pd.DataFrame(export_data)
        
        # CSV konvertálás
        csv = df.to_csv(index=False, encoding='utf-8-sig')  # UTF-8 BOM a magyar karakterek megfelelő megjelenítéséhez
        
        # Base64 kódolás a letöltéshez
        b64 = base64.b64encode(csv.encode()).decode()
        
        # Fájlnév generálása
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ajanlatok_export_{now}.csv"
        
        # Letöltési link generálása
        href = f'''
        <a href="data:file/csv;base64,{b64}" download="{filename}" 
           style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; 
                  color: white; text-decoration: none; border-radius: 4px; 
                  text-align: center; margin-right: 10px;">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="vertical-align: text-bottom; margin-right: 5px;">
                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
            </svg>
            CSV letöltése
        </a>
        '''
        
        return href
    
    except Exception as e:
        logger.error(f"Hiba az CSV exportálás során: {str(e)}")
        return f"<p style='color:red'>Hiba az exportálás során: {str(e)}</p>"

def export_offers_to_excel(offers):
    """
    Ajánlatok exportálása Excel formátumba.
    
    Ez a függvény az ajánlatok listáját Excel (.xlsx) formátumba konvertálja,
    és egy letölthető linket ad vissza. A táblázat tartalmazza az összes
    releváns adatot, megfelelően formázva és szűrési lehetőségekkel.
    
    Args:
        offers (list): Az exportálandó ajánlatok listája
        
    Returns:
        str: HTML kód a letöltési linkkel
    """
    import pandas as pd
    import base64
    from io import BytesIO
    
    # Ellenőrizzük, hogy van-e exportálandó adat
    if not offers:
        return "<p style='color:red'>Nincs exportálható adat</p>"
    
    try:
        # Adatok előkészítése a DataFrame-hez
        export_data = []
        for offer in offers:
            # Alapadatok kinyerése
            # Termék információ
            product_type = offer.get("product_type", {})
            product_name = product_type.get("name", "Ismeretlen termék") if product_type else "Ismeretlen termék"
            product_unit = product_type.get("unit", "kg") if product_type else "kg"
            
            # Termelő információ
            user = offer.get("user", {})
            company_name = user.get("company_name", "") if user else ""
            contact_name = user.get("contact_name", "Ismeretlen termelő") if user else "Ismeretlen termelő"
            producer_name = company_name or contact_name
            producer_email = user.get("email", "") if user else ""
            producer_phone = user.get("phone", "") if user else ""
            
            # Minőségi osztály
            quality_grade = offer.get("quality_grade", {})
            quality_grade_name = quality_grade.get("name", "") if quality_grade else ""
            
            # Biztonságos konverziók
            try:
                quantity = float(offer.get("quantity_in_kg", 0))
            except (ValueError, TypeError):
                quantity = 0
                
            try:
                confirmed_quantity = float(offer.get("confirmed_quantity", 0))
            except (ValueError, TypeError):
                confirmed_quantity = 0
                
            try:
                price = float(offer.get("price", 0))
            except (ValueError, TypeError):
                price = 0
                
            try:
                confirmed_price = float(offer.get("confirmed_price", 0))
            except (ValueError, TypeError):
                confirmed_price = 0
            
            # Időbélyegek
            created_at = offer.get("created_at", "")
            updated_at = offer.get("updated_at", "")
            delivery_date = offer.get("delivery_date", "")
            
            # Megjegyzés
            note = offer.get("note", "")
            
            # Csatolmányok száma
            attachments_count = len(offer.get("attachments", []))
            
            # Státusz és ID
            status = offer.get("status", "")
            offer_id = offer.get("id", "")
            
            # Kiegészítő számolt mezők
            total_value = quantity * price if quantity and price else 0
            confirmed_total_value = confirmed_quantity * confirmed_price if confirmed_quantity and confirmed_price else 0
            
            # Ajánlat adatok előkészítése exporthoz
            export_row = {
                "Azonosító": offer_id,
                "Státusz kód": status,
                "Státusz": format_status(status),
                "Termék": product_name,
                "Termelő neve": producer_name,
                "Termelő email": producer_email,
                "Termelő telefon": producer_phone,
                "Mennyiség (kg)": quantity,
                "Visszaigazolt mennyiség (kg)": confirmed_quantity,
                "Egységár (Ft/kg)": price,
                "Visszaigazolt egységár (Ft/kg)": confirmed_price,
                "Összérték (Ft)": total_value,
                "Visszaigazolt összérték (Ft)": confirmed_total_value,
                "Egység": product_unit,
                "Minőségi osztály": quality_grade_name,
                "Beszállítási dátum": delivery_date,
                "Létrehozva": created_at,
                "Utoljára módosítva": updated_at,
                "Csatolmányok száma": attachments_count,
                "Megjegyzés": note
            }
            
            export_data.append(export_row)
        
        # DataFrame létrehozása
        df = pd.DataFrame(export_data)
        
        # Excel konvertálás
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Ajánlatok', index=False)
            
            # Automatikus oszlopszélesség beállítása
            worksheet = writer.sheets['Ajánlatok']
            
            # Munkalap formázása
            workbook = writer.book
            
            # Oszlopszélesség beállítása
            for i, col in enumerate(df.columns):
                # Az oszlop szélességét a maximális tartalom hosszához igazítjuk
                # (vagy az oszlop nevének hosszához, ha az hosszabb)
                column_width = max(
                    df[col].astype(str).map(len).max(),
                    len(col)
                ) + 2  # Extra margó
                worksheet.set_column(i, i, column_width)
            
            # Fejléc formázás
            header_format = workbook.add_format({
                'bold': True,
                'fg_color': '#D7E4BC',
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'text_wrap': True
            })
            
            # Fejléc formázás alkalmazása
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
                
            # Tartalom formázása
            cell_format = workbook.add_format({
                'border': 1,
                'align': 'left',
                'valign': 'vcenter',
                'text_wrap': True
            })
            
            # Dátum formátum
            date_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': 'yyyy-mm-dd'
            })
            
            # Szám formátum
            number_format = workbook.add_format({
                'border': 1,
                'align': 'right',
                'valign': 'vcenter',
                'num_format': '# ##0.00'
            })
            
            # Pénz formátum
            money_format = workbook.add_format({
                'border': 1,
                'align': 'right',
                'valign': 'vcenter',
                'num_format': '# ##0 Ft'
            })
            
            # Státusz formátumok
            status_format_map = {}
            for status_code, status_details in {s: {'name': format_status(s), 'color': '#6c757d'} for s in set(df['Státusz kód'])}.items():
                color = '#6c757d'  # Alapértelmezett szürke
                if status_code in ['CONFIRMED_BY_COMPANY', 'approved']:
                    color = '#28A745'  # Zöld
                elif status_code in ['REJECTED_BY_USER', 'rejected']:
                    color = '#DC3545'  # Piros
                elif status_code in ['CREATED', 'pending']:
                    color = '#FFC107'  # Sárga
                elif status_code in ['ACCEPTED_BY_USER', 'in_progress']:
                    color = '#17A2B8'  # Kék
                
                status_format_map[status_code] = workbook.add_format({
                    'border': 1,
                    'align': 'center',
                    'valign': 'vcenter',
                    'bg_color': color,
                    'color': 'white',
                    'bold': True
                })
            
            # Formátumok alkalmazása a megfelelő oszlopokra
            for row in range(1, len(df) + 1):
                for col_num, col_name in enumerate(df.columns):
                    value = df.iloc[row-1, col_num]
                    
                    # Státusz formázás
                    if col_name == "Státusz":
                        status_code = df.iloc[row-1, df.columns.get_loc("Státusz kód")]
                        if status_code in status_format_map:
                            worksheet.write(row, col_num, value, status_format_map[status_code])
                        else:
                            worksheet.write(row, col_num, value, cell_format)
                    
                    # Dátum formázás
                    elif col_name in ["Beszállítási dátum", "Létrehozva", "Utoljára módosítva"]:
                        try:
                            worksheet.write(row, col_num, value, date_format)
                        except:
                            worksheet.write(row, col_num, value, cell_format)
                    
                    # Szám formázás
                    elif col_name in ["Mennyiség (kg)", "Visszaigazolt mennyiség (kg)", "Egységár (Ft/kg)", "Visszaigazolt egységár (Ft/kg)"]:
                        try:
                            worksheet.write_number(row, col_num, float(value) if value else 0, number_format)
                        except:
                            worksheet.write(row, col_num, value, cell_format)
                    
                    # Pénz formázás
                    elif col_name in ["Összérték (Ft)", "Visszaigazolt összérték (Ft)"]:
                        try:
                            worksheet.write_number(row, col_num, float(value) if value else 0, money_format)
                        except:
                            worksheet.write(row, col_num, value, cell_format)
                    
                    # Alapértelmezett formázás
                    else:
                        worksheet.write(row, col_num, value, cell_format)
            
            # Auto filter hozzáadása a fejléchez
            worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
            
            # Munkalap tulajdonságok beállítása
            worksheet.freeze_panes(1, 0)  # Fejléc rögzítése
        
        # Puffer pozícionálása az elejére
        output.seek(0)
        
        # Base64 kódolás a letöltéshez
        b64 = base64.b64encode(output.getvalue()).decode()
        
        # Fájlnév generálása
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ajanlatok_export_{now}.xlsx"
        
        # Letöltési link generálása
        href = f'''
        <a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" 
           download="{filename}" 
           style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; 
                  color: white; text-decoration: none; border-radius: 4px; 
                  text-align: center;">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="vertical-align: text-bottom; margin-right: 5px;">
                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
            </svg>
            Excel letöltése
        </a>
        '''
        
        return href
    
    except Exception as e:
        logger.error(f"Hiba az Excel exportálás során: {str(e)}")
        return f"<p style='color:red'>Hiba az exportálás során: {str(e)}</p>"

def render_export_buttons(offers):
    """
    Exportálási gombok megjelenítése.
    
    Ez a függvény megjelenít két gombot (CSV és Excel) az ajánlatok exportálásához,
    és kezeli az exportálási folyamatot.
    
    Args:
        offers (list): Az exportálandó ajánlatok listája
    """
    if not offers:
        st.warning("Nincs exportálható adat. Először végezzen keresést!")
        return
    
    st.markdown("### Adatok exportálása")
    
    # Konténer a gomboknak és az exportálási eredményeknek
    export_container = st.container()
    
    with export_container:
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("CSV exportálása", key="csv_export", use_container_width=True):
                with st.spinner("CSV exportálása folyamatban..."):
                    csv_link = export_offers_to_csv(offers)
                    st.markdown(csv_link, unsafe_allow_html=True)
        
        with col2:
            if st.button("Excel exportálása", key="excel_export", use_container_width=True):
                with st.spinner("Excel exportálása folyamatban..."):
                    excel_link = export_offers_to_excel(offers)
                    st.markdown(excel_link, unsafe_allow_html=True)
        
        # Segítség szöveg
        st.markdown("""
        <div style="background-color: #e8f4f8; padding: 10px; border-radius: 5px; margin-top: 10px; border-left: 4px solid #17a2b8;">
            <strong>ℹ️ Tipp:</strong> Az exportált fájlok tartalmazzák az összes jelenleg szűrt ajánlatot.
            A szűrők módosításával megváltoztathatja az exportálandó adatokat.
        </div>
        """, unsafe_allow_html=True)


# Single offer export functions
def export_offer_to_pdf(offer_data):
    """Export single offer to PDF"""
    # TODO: Implement PDF generation with reportlab or similar
    st.info("PDF export még nincs implementálva")
    logger.info(f"PDF export requested for offer {offer_data.get('id')}")
    

def export_offer_to_excel(offer_data):
    """Export single offer to Excel"""
    try:
        # Create DataFrame from offer data
        df = pd.DataFrame([{
            'ID': offer_data.get('id'),
            'Termelő': offer_data.get('user', {}).get('contact_name'),
            'Cég': offer_data.get('user', {}).get('company_name'),
            'Termék': offer_data.get('product_type', {}).get('name'),
            'Mennyiség (kg)': offer_data.get('quantity_in_kg'),
            'Ár (Ft/kg)': offer_data.get('price'),
            'Státusz': format_status(offer_data.get('status')),
            'Létrehozva': format_datetime(offer_data.get('created_at')),
            'Beszállítás': format_date(offer_data.get('delivery_date')),
            'Megjegyzés': offer_data.get('note', '')
        }])
        
        # Create Excel file
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Ajánlat', index=False)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Ajánlat']
            
            # Format headers
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # Write headers with formatting
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust column widths
            for i, col in enumerate(df.columns):
                column_len = max(df[col].astype(str).str.len().max(), len(col) + 2)
                worksheet.set_column(i, i, min(column_len, 50))
        
        # Download button
        st.download_button(
            label="📥 Excel letöltése",
            data=output.getvalue(),
            file_name=f"ajanlat_{offer_data.get('id')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        logger.info(f"Excel export successful for offer {offer_data.get('id')}")
        
    except Exception as e:
        st.error(f"Hiba az Excel exportálás során: {str(e)}")
        logger.error(f"Excel export failed for offer {offer_data.get('id')}: {str(e)}")


def export_offer_to_csv(offer_data):
    """Export single offer to CSV"""
    try:
        # Create DataFrame
        df = pd.DataFrame([{
            'ID': offer_data.get('id'),
            'Termelő': offer_data.get('user', {}).get('contact_name'),
            'Cég': offer_data.get('user', {}).get('company_name'),
            'Termék': offer_data.get('product_type', {}).get('name'),
            'Mennyiség (kg)': offer_data.get('quantity_in_kg'),
            'Ár (Ft/kg)': offer_data.get('price'),
            'Státusz': format_status(offer_data.get('status')),
            'Létrehozva': format_datetime(offer_data.get('created_at')),
            'Beszállítás': format_date(offer_data.get('delivery_date')),
            'Megjegyzés': offer_data.get('note', '')
        }])
        
        # Convert to CSV
        csv = df.to_csv(index=False, encoding='utf-8-sig')  # Use utf-8-sig for Excel compatibility
        
        # Download button
        st.download_button(
            label="📥 CSV letöltése",
            data=csv,
            file_name=f"ajanlat_{offer_data.get('id')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
        
        logger.info(f"CSV export successful for offer {offer_data.get('id')}")
        
    except Exception as e:
        st.error(f"Hiba a CSV exportálás során: {str(e)}")
        logger.error(f"CSV export failed for offer {offer_data.get('id')}: {str(e)}")