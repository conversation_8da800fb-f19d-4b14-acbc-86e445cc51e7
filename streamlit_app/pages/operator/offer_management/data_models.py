#!/usr/bin/env python3
"""
Data Models for Offer Management
Adatmodellek a tiszta adatkezeléshez
"""
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class OfferStatus(Enum):
    """Aj<PERSON>lat státusz enum"""
    CREATED = "CREATED"
    CONFIRMED_BY_COMPANY = "CONFIRMED_BY_COMPANY"
    ACCEPTED_BY_USER = "ACCEPTED_BY_USER"
    REJECTED_BY_USER = "REJECTED_BY_USER"
    FINALIZED = "FINALIZED"
    CANCELLED = "CANCELLED"
    PENDING = "PENDING"
    
    @property
    def display_name(self) -> str:
        """Státusz megjelenítési neve"""
        mapping = {
            self.CREATED: "Létrehozva",
            self.CONFIRMED_BY_COMPANY: "Megerősítve",
            self.ACCEPTED_BY_USER: "Elfogadva",
            self.REJECTED_BY_USER: "Elutasítva",
            self.FINALIZED: "Véglegesítve",
            self.CANCELLED: "Törölve",
            self.PENDING: "Függőben"
        }
        return mapping.get(self, self.value)
    
    @property
    def color_indicator(self) -> str:
        """Státusz színes jelzője"""
        mapping = {
            self.CREATED: "🟢",
            self.CONFIRMED_BY_COMPANY: "🟡",
            self.ACCEPTED_BY_USER: "🟢",
            self.REJECTED_BY_USER: "🔴",
            self.FINALIZED: "✅",
            self.CANCELLED: "⚫",
            self.PENDING: "🟠"
        }
        return mapping.get(self, "⚪")

@dataclass
class Producer:
    """Termelő adatmodell"""
    id: int
    name: str
    email: Optional[str] = ""
    phone: Optional[str] = ""
    address: Optional[str] = ""
    active: bool = True
    
    def __str__(self) -> str:
        return self.name

@dataclass
class Product:
    """Termék adatmodell"""
    id: int
    name: str
    category: Optional[str] = ""
    unit: str = "kg"
    description: Optional[str] = ""
    active: bool = True
    
    def __str__(self) -> str:
        return self.name

@dataclass
class Offer:
    """Ajánlat adatmodell"""
    id: int
    producer_id: int
    producer_name: str
    product_id: int
    product_name: str
    quantity_value: float  # Updated from quantity_in_kg
    quantity_unit: str = "kg"  # New field for unit
    price: float
    status: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    description: Optional[str] = ""
    notes: Optional[str] = ""
    
    # Backward compatibility property
    @property
    def quantity_in_kg(self) -> float:
        """Backward compatibility: convert to kg for legacy code"""
        if self.quantity_unit == "tonna":
            return self.quantity_value * 1000
        elif self.quantity_unit == "kg":
            return self.quantity_value
        else:  # "db" or other units
            return self.quantity_value
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'Offer':
        """
        API válasz alapján Offer objektum létrehozása
        
        Args:
            data: API válasz dictionary
            
        Returns:
            Offer: Offer objektum
        """
        try:
            # Dátumok kezelése
            created_at = None
            updated_at = None
            
            if data.get('created_at'):
                try:
                    if isinstance(data['created_at'], str):
                        created_at = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
                    elif isinstance(data['created_at'], datetime):
                        created_at = data['created_at']
                except Exception as e:
                    logger.warning(f"Could not parse created_at: {e}")
            
            if data.get('updated_at'):
                try:
                    if isinstance(data['updated_at'], str):
                        updated_at = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
                    elif isinstance(data['updated_at'], datetime):
                        updated_at = data['updated_at']
                except Exception as e:
                    logger.warning(f"Could not parse updated_at: {e}")
            
            # Handle both old and new quantity field formats
            quantity_value = data.get('quantity_value', data.get('quantity_in_kg', 0))
            quantity_unit = data.get('quantity_unit', 'kg')
            
            return cls(
                id=int(data.get('id', 0)),
                producer_id=int(data.get('producer_id', 0)),
                producer_name=str(data.get('producer_name', 'Ismeretlen termelő')),
                product_id=int(data.get('product_id', 0)),
                product_name=str(data.get('product_name', 'Ismeretlen termék')),
                quantity_value=float(quantity_value),
                quantity_unit=str(quantity_unit),
                price=float(data.get('price', 0)),
                status=str(data.get('status', 'UNKNOWN')),
                created_at=created_at,
                updated_at=updated_at,
                description=str(data.get('description', '')),
                notes=str(data.get('notes', ''))
            )
            
        except Exception as e:
            logger.error(f"Error creating Offer from API data: {e}")
            raise ValueError(f"Invalid API data for Offer: {e}")
    
    @property
    def status_enum(self) -> OfferStatus:
        """Státusz enum objektum"""
        try:
            return OfferStatus(self.status)
        except ValueError:
            logger.warning(f"Unknown status: {self.status}")
            return OfferStatus.PENDING
    
    @property
    def status_display(self) -> str:
        """Státusz megjelenítési neve"""
        return self.status_enum.display_name
    
    @property
    def status_indicator(self) -> str:
        """Státusz színes jelzője"""
        return self.status_enum.color_indicator
    
    @property
    def total_value(self) -> float:
        """Teljes érték (mennyiség * ár)"""
        # For "db" units, use direct value; for weight units, convert to kg for price calculation
        if self.quantity_unit == "db":
            return self.quantity_value * self.price
        else:
            return self.quantity_in_kg * self.price
    
    @property
    def formatted_date(self) -> str:
        """Formázott létrehozási dátum"""
        if self.created_at:
            return self.created_at.strftime('%Y.%m.%d')
        return 'N/A'
    
    @property
    def formatted_quantity(self) -> str:
        """Formázott mennyiség"""
        # Import here to avoid circular imports
        try:
            from utils.formatting import format_quantity
            return format_quantity(self.quantity_value, self.quantity_unit)
        except ImportError:
            # Fallback formatting if import fails
            if self.quantity_unit == "db":
                return f"{self.quantity_value:,.0f} db"
            elif self.quantity_unit == "tonna":
                return f"{self.quantity_value:,.2f} tonna"
            else:
                return f"{self.quantity_value:,.2f} {self.quantity_unit}"
    
    @property
    def formatted_price(self) -> str:
        """Formázott ár"""
        # Price unit depends on the quantity unit
        if self.quantity_unit == "db":
            return f"{self.price:,.0f} Ft/db"
        elif self.quantity_unit == "tonna":
            return f"{self.price:,.0f} Ft/tonna"
        else:
            return f"{self.price:,.0f} Ft/{self.quantity_unit}"
    
    @property
    def formatted_total_value(self) -> str:
        """Formázott teljes érték"""
        return f"{self.total_value:,.0f} Ft"
    
    def to_dict(self) -> Dict[str, Any]:
        """Offer objektum dictionary-vé alakítása"""
        return {
            'id': self.id,
            'producer_id': self.producer_id,
            'producer_name': self.producer_name,
            'product_id': self.product_id,
            'product_name': self.product_name,
            'quantity_value': self.quantity_value,
            'quantity_unit': self.quantity_unit,
            'quantity_in_kg': self.quantity_in_kg,  # Keep for backward compatibility
            'price': self.price,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'description': self.description,
            'notes': self.notes,
            'total_value': self.total_value,
            'status_display': self.status_display,
            'status_indicator': self.status_indicator,
            'formatted_date': self.formatted_date,
            'formatted_quantity': self.formatted_quantity,
            'formatted_price': self.formatted_price,
            'formatted_total_value': self.formatted_total_value
        }

@dataclass
class FilterParams:
    """Szűrési paraméterek adatmodell"""
    producer_id: Optional[int] = None
    producer_name: Optional[str] = None
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    status: Optional[str] = None
    statuses: List[str] = field(default_factory=list)
    from_date: Optional[date] = None
    to_date: Optional[date] = None
    search_term: Optional[str] = None
    min_quantity: Optional[float] = None
    max_quantity: Optional[float] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    page: int = 1
    page_size: int = 50
    sort_by: str = "created_at"
    sort_order: str = "desc"
    
    def to_api_params(self) -> Dict[str, Any]:
        """Szűrési paraméterek API formátumba alakítása"""
        params = {}
        
        # Termelő szűrő
        if self.producer_id:
            params['producer_id'] = self.producer_id
        elif self.producer_name:
            params['producer_name'] = self.producer_name
        
        # Termék szűrő
        if self.product_id:
            params['product_id'] = self.product_id
        elif self.product_name:
            params['product_name'] = self.product_name
        
        # Státusz szűrő
        if self.status:
            params['status'] = self.status
        elif self.statuses:
            params['statuses'] = self.statuses
        
        # Dátum szűrők
        if self.from_date:
            params['from_date'] = self.from_date.isoformat()
        if self.to_date:
            params['to_date'] = self.to_date.isoformat()
        
        # Keresési kifejezés
        if self.search_term:
            params['search'] = self.search_term
        
        # Mennyiség szűrők
        if self.min_quantity is not None:
            params['min_quantity'] = self.min_quantity
        if self.max_quantity is not None:
            params['max_quantity'] = self.max_quantity
        
        # Ár szűrők
        if self.min_price is not None:
            params['min_price'] = self.min_price
        if self.max_price is not None:
            params['max_price'] = self.max_price
        
        # Lapozás és rendezés
        params['page'] = self.page
        params['page_size'] = self.page_size
        params['sort_by'] = self.sort_by
        params['sort_order'] = self.sort_order
        
        # Üres értékek kiszűrése
        return {k: v for k, v in params.items() if v is not None}
    
    @classmethod
    def from_session_state(cls, session_state: Dict[str, Any]) -> 'FilterParams':
        """Session state alapján FilterParams létrehozása"""
        return cls(
            producer_id=session_state.get('producer_filter_om'),
            product_id=session_state.get('product_filter_om'), 
            status=session_state.get('status_filter_om'),
            from_date=session_state.get('from_date_filter_om'),
            to_date=session_state.get('to_date_filter_om'),
            search_term=session_state.get('search_filter_om', ''),
            page=session_state.get('page_filter_om', 1),
            page_size=session_state.get('page_size_filter_om', 50),
            sort_by=session_state.get('sort_by_filter_om', 'created_at'),
            sort_order=session_state.get('sort_order_filter_om', 'desc')
        )
    
    def is_empty(self) -> bool:
        """Ellenőrzi, hogy vannak-e aktív szűrők"""
        return not any([
            self.producer_id,
            self.producer_name,
            self.product_id,
            self.product_name,
            self.status,
            self.statuses,
            self.from_date,
            self.to_date,
            self.search_term,
            self.min_quantity is not None,
            self.max_quantity is not None,
            self.min_price is not None,
            self.max_price is not None
        ])
    
    def get_active_filters_summary(self) -> List[str]:
        """Aktív szűrők összefoglalója"""
        active = []
        
        if self.producer_name:
            active.append(f"Termelő: {self.producer_name}")
        if self.product_name:
            active.append(f"Termék: {self.product_name}")
        if self.status:
            try:
                status_display = OfferStatus(self.status).display_name
                active.append(f"Státusz: {status_display}")
            except ValueError:
                active.append(f"Státusz: {self.status}")
        if self.from_date and self.to_date:
            active.append(f"Időszak: {self.from_date} - {self.to_date}")
        elif self.from_date:
            active.append(f"Kezdő dátum: {self.from_date}")
        elif self.to_date:
            active.append(f"Végső dátum: {self.to_date}")
        if self.search_term:
            active.append(f"Keresés: {self.search_term}")
        
        return active

@dataclass
class OfferStatistics:
    """Ajánlatok statisztikai adatai"""
    total_count: int = 0
    total_quantity: float = 0
    average_price: float = 0
    total_value: float = 0
    status_distribution: Dict[str, int] = field(default_factory=dict)
    product_distribution: Dict[str, int] = field(default_factory=dict)
    producer_distribution: Dict[str, int] = field(default_factory=dict)
    
    @classmethod
    def from_offers(cls, offers: List[Offer]) -> 'OfferStatistics':
        """Ajánlatok listájából statisztika számítása"""
        if not offers:
            return cls()
        
        total_count = len(offers)
        # For statistics, convert all quantities to kg for consistency
        total_quantity = sum(offer.quantity_in_kg for offer in offers)
        total_value = sum(offer.total_value for offer in offers)
        average_price = total_value / total_quantity if total_quantity > 0 else 0
        
        # Megoszlások számítása
        status_distribution = {}
        product_distribution = {}
        producer_distribution = {}
        
        for offer in offers:
            # Státusz megoszlás
            status_display = offer.status_display
            status_distribution[status_display] = status_distribution.get(status_display, 0) + 1
            
            # Termék megoszlás
            product_distribution[offer.product_name] = product_distribution.get(offer.product_name, 0) + 1
            
            # Termelő megoszlás
            producer_distribution[offer.producer_name] = producer_distribution.get(offer.producer_name, 0) + 1
        
        return cls(
            total_count=total_count,
            total_quantity=total_quantity,
            average_price=average_price,
            total_value=total_value,
            status_distribution=status_distribution,
            product_distribution=product_distribution,
            producer_distribution=producer_distribution
        )
    
    @property
    def formatted_total_quantity(self) -> str:
        """Formázott összes mennyiség"""
        return f"{self.total_quantity:,.0f} kg"
    
    @property
    def formatted_average_price(self) -> str:
        """Formázott átlagár"""
        return f"{self.average_price:,.0f} Ft/kg"
    
    @property
    def formatted_total_value(self) -> str:
        """Formázott összes érték"""
        return f"{self.total_value:,.0f} Ft"

# Utility függvények a modellekkel való munkához
def offers_from_api_response(api_response: List[Dict[str, Any]]) -> List[Offer]:
    """
    API válasz listából Offer objektumok listája
    
    Args:
        api_response: API válasz lista
        
    Returns:
        List[Offer]: Offer objektumok listája
    """
    offers = []
    for item in api_response:
        try:
            offer = Offer.from_api_response(item)
            offers.append(offer)
        except Exception as e:
            logger.warning(f"Could not create Offer from API item: {e}")
            continue
    
    return offers

def filter_offers_by_params(offers: List[Offer], filters: FilterParams) -> List[Offer]:
    """
    Ajánlatok szűrése FilterParams alapján (kliens oldali szűrés)
    
    Args:
        offers: Ajánlatok listája
        filters: Szűrési paraméterek
        
    Returns:
        List[Offer]: Szűrt ajánlatok
    """
    filtered = offers.copy()
    
    # Termelő szűrő
    if filters.producer_id:
        filtered = [o for o in filtered if o.producer_id == filters.producer_id]
    elif filters.producer_name:
        filtered = [o for o in filtered if filters.producer_name.lower() in o.producer_name.lower()]
    
    # Termék szűrő
    if filters.product_id:
        filtered = [o for o in filtered if o.product_id == filters.product_id]
    elif filters.product_name:
        filtered = [o for o in filtered if filters.product_name.lower() in o.product_name.lower()]
    
    # Státusz szűrő
    if filters.status:
        filtered = [o for o in filtered if o.status == filters.status]
    elif filters.statuses:
        filtered = [o for o in filtered if o.status in filters.statuses]
    
    # Keresési kifejezés
    if filters.search_term:
        search_lower = filters.search_term.lower()
        filtered = [
            o for o in filtered 
            if (search_lower in o.producer_name.lower() or 
                search_lower in o.product_name.lower() or
                search_lower in o.description.lower() or
                search_lower in o.notes.lower())
        ]
    
    # Dátum szűrők
    if filters.from_date and filters.to_date:
        filtered = [
            o for o in filtered 
            if o.created_at and filters.from_date <= o.created_at.date() <= filters.to_date
        ]
    elif filters.from_date:
        filtered = [
            o for o in filtered 
            if o.created_at and o.created_at.date() >= filters.from_date
        ]
    elif filters.to_date:
        filtered = [
            o for o in filtered 
            if o.created_at and o.created_at.date() <= filters.to_date
        ]
    
    return filtered