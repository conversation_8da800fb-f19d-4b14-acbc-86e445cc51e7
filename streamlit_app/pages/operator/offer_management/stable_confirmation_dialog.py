"""
Stable Confirmation Dialog Component - Visszaigazolás funkció
Stabil visszaigazolási dialógus komponens amely nem tűnik el értékváltoztatáskor
"""
import streamlit as st
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

def render_stable_confirmation_dialog(offer: Dict[str, Any], offer_id: int) -> Tuple[bool, Optional[float], Optional[float]]:
    """
    Render stable confirmation dialog that doesn't disappear on value changes.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        
    Returns:
        Tuple of (confirmed, quantity, price)
        - confirmed: True if user confirmed the action
        - quantity: Confirmed quantity in kg
        - price: Confirmed price per kg
    """
    # Initialize session state for persistent values
    if f"conf_quantity_{offer_id}" not in st.session_state:
        st.session_state[f"conf_quantity_{offer_id}"] = float(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))
    if f"conf_price_{offer_id}" not in st.session_state:
        st.session_state[f"conf_price_{offer_id}"] = float(offer.get('price', 0))
    if f"conf_note_{offer_id}" not in st.session_state:
        st.session_state[f"conf_note_{offer_id}"] = ""
    
    # Get original values from offer data
    original_quantity = float(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))
    original_price = float(offer.get('price', 0))
    
    # Create a stable container with border
    st.markdown("""
    <style>
    .stable-dialog {
        background: #1a1a1a;
        border: 2px solid #0099e0;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .dialog-header {
        background: rgba(0, 153, 224, 0.1);
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Dialog container
    st.markdown('<div class="stable-dialog">', unsafe_allow_html=True)
    
    # Header
    st.markdown(f"""
    <div class="dialog-header">
        <strong>📋 Ajánlat #{offer_id}</strong> • 
        <strong>{offer.get('product_type', {}).get('name', 'N/A')}</strong> • 
        {offer.get('user', {}).get('contact_name', 'N/A')}
    </div>
    """, unsafe_allow_html=True)
    
    # Original values display
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Eredeti mennyiség", f"{original_quantity:,.0f} kg")
    with col2:
        st.metric("Eredeti ár", f"{original_price:,.0f} Ft/kg")
    with col3:
        st.metric("Eredeti összérték", f"{(original_quantity * original_price):,.0f} Ft")
    
    st.markdown("---")
    
    # Input section
    st.markdown("#### 📝 Visszaigazolási adatok")
    
    # Two column layout for inputs
    col1, col2 = st.columns(2)
    
    with col1:
        # Quantity input - using session state
        confirmed_quantity = st.number_input(
            "Visszaigazolt mennyiség (kg):",
            min_value=0.0,
            max_value=original_quantity * 1.1,  # Allow 10% over original
            value=st.session_state[f"conf_quantity_{offer_id}"],
            step=1.0,
            key=f"quantity_input_{offer_id}",
            help="A visszaigazolt mennyiség nem lehet negatív"
        )
        # Update session state
        st.session_state[f"conf_quantity_{offer_id}"] = confirmed_quantity
    
    with col2:
        # Price input - using session state
        confirmed_price = st.number_input(
            "Visszaigazolt egységár (Ft/kg):",
            min_value=0.0,
            value=st.session_state[f"conf_price_{offer_id}"],
            step=1.0,
            key=f"price_input_{offer_id}",
            help="A visszaigazolt ár nem lehet negatív"
        )
        # Update session state
        st.session_state[f"conf_price_{offer_id}"] = confirmed_price
    
    # Calculate changes
    if original_quantity > 0:
        quantity_percentage = (confirmed_quantity / original_quantity) * 100
    else:
        quantity_percentage = 0
        
    if original_price > 0:
        price_change = ((confirmed_price - original_price) / original_price) * 100
    else:
        price_change = 0
    
    # Show changes
    col1, col2 = st.columns(2)
    with col1:
        if quantity_percentage != 100:
            if quantity_percentage < 100:
                st.warning(f"⚠️ Mennyiség: {quantity_percentage:.1f}% az eredetiből")
            else:
                st.info(f"📈 Mennyiség: {quantity_percentage:.1f}% az eredetiből")
    
    with col2:
        if price_change != 0:
            if price_change < 0:
                st.warning(f"📉 Ár változás: {price_change:.1f}%")
            else:
                st.success(f"📈 Ár változás: +{price_change:.1f}%")
    
    # Total value
    st.markdown("---")
    total_confirmed = confirmed_quantity * confirmed_price
    
    col1, col2, col3 = st.columns(3)
    with col2:
        st.metric(
            "Visszaigazolt összérték",
            f"{total_confirmed:,.0f} Ft",
            delta=f"{total_confirmed - (original_quantity * original_price):,.0f} Ft"
        )
    
    # Note field - using session state
    note = st.text_area(
        "Megjegyzés (opcionális):",
        value=st.session_state[f"conf_note_{offer_id}"],
        placeholder="Pl. Minőségi kifogás, részleges teljesítés oka, stb.",
        height=80,
        key=f"note_input_{offer_id}"
    )
    st.session_state[f"conf_note_{offer_id}"] = note
    
    # Action buttons
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 1, 2])
    
    action_taken = False
    confirmed = None
    
    with col1:
        if st.button("✅ Visszaigazolás", type="primary", use_container_width=True, key=f"confirm_btn_{offer_id}"):
            action_taken = True
            confirmed = True
    
    with col2:
        if st.button("❌ Mégse", use_container_width=True, key=f"cancel_btn_{offer_id}"):
            action_taken = True
            confirmed = False
    
    with col3:
        st.info("💡 A visszaigazolás után a termelő elfogadhatja vagy elutasíthatja.")
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Handle actions
    if action_taken:
        if confirmed:
            # Validation
            if confirmed_quantity <= 0:
                st.error("❌ A mennyiség nem lehet 0 vagy negatív!")
                return None, None, None
            elif confirmed_price <= 0:
                st.error("❌ Az ár nem lehet 0 vagy negatív!")
                return None, None, None
            else:
                # Store the note if provided
                if note:
                    st.session_state[f"confirmation_note_{offer_id}"] = note
                
                # Clean up session state
                cleanup_confirmation_state(offer_id)
                
                return True, confirmed_quantity, confirmed_price
        else:
            # Cancelled
            cleanup_confirmation_state(offer_id)
            return False, None, None
    
    # No action taken yet
    return None, None, None


def cleanup_confirmation_state(offer_id: int):
    """Clean up session state after confirmation action"""
    keys_to_remove = [
        f"conf_quantity_{offer_id}",
        f"conf_price_{offer_id}",
        f"conf_note_{offer_id}",
        f"show_confirmation_dialog_{offer_id}",
        f"show_confirmation_modal_{offer_id}",
        f"quick_confirm_clicked_{offer_id}",
        f"inline_conf_active_{offer_id}"
    ]
    
    for key in keys_to_remove:
        if key in st.session_state:
            del st.session_state[key]


def render_inline_confirmation_form(offer: Dict[str, Any], offer_id: int) -> Tuple[bool, Optional[float], Optional[float]]:
    """
    Render a simpler inline confirmation form without form submission.
    This version stays visible and doesn't disappear on value changes.
    """
    # Initialize session state
    if f"inline_conf_active_{offer_id}" not in st.session_state:
        st.session_state[f"inline_conf_active_{offer_id}"] = True
    
    if not st.session_state[f"inline_conf_active_{offer_id}"]:
        return None, None, None
    
    # Get original values from offer data
    original_quantity = float(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))
    original_price = float(offer.get('price', 0))
    
    # Create bordered container
    with st.container():
        st.markdown("""
        <div style='background: #1a1a1a; border: 2px solid #0099e0; 
                    border-radius: 12px; padding: 1.5rem; margin: 1rem 0;'>
            <h4 style='color: #0099e0; margin-top: 0;'>🔄 Visszaigazolás</h4>
        </div>
        """, unsafe_allow_html=True)
        
        # Show offer info
        st.info(f"📋 Ajánlat #{offer_id} • {offer.get('product_type', {}).get('name', 'N/A')} • {offer.get('user', {}).get('contact_name', 'N/A')}")
        
        # Show original values from actual offer data
        st.markdown("#### 📄 Eredeti ajánlat adatok")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Eredeti mennyiség", f"{original_quantity:,.0f} kg")
        with col2:
            st.metric("Eredeti ár", f"{original_price:,.0f} Ft/kg")
        with col3:
            st.metric("Eredeti összérték", f"{(original_quantity * original_price):,.0f} Ft")
        
        st.markdown("---")
        
        # Input section for confirmation
        st.markdown("#### 📝 Visszaigazolási adatok")
        col1, col2 = st.columns(2)
        
        with col1:
            confirmed_quantity = st.number_input(
                "Visszaigazolt mennyiség (kg)",
                min_value=0.0,
                max_value=original_quantity * 1.1,
                value=original_quantity,
                step=1.0,
                key=f"inline_qty_{offer_id}"
            )
            
            # Show percentage
            if original_quantity > 0:
                pct = (confirmed_quantity / original_quantity) * 100
                if pct < 100:
                    st.caption(f"⚠️ {pct:.1f}% az eredetiből")
                elif pct > 100:
                    st.caption(f"📈 {pct:.1f}% az eredetiből")
        
        with col2:
            confirmed_price = st.number_input(
                "Visszaigazolt ár (Ft/kg)",
                min_value=0.0,
                value=original_price,
                step=1.0,
                key=f"inline_price_{offer_id}"
            )
            
            # Show price change
            if original_price > 0:
                change = ((confirmed_price - original_price) / original_price) * 100
                if change < 0:
                    st.caption(f"📉 {change:.1f}%")
                elif change > 0:
                    st.caption(f"📈 +{change:.1f}%")
        
        # Total value display
        total = confirmed_quantity * confirmed_price
        st.metric(
            "Visszaigazolt összérték",
            f"{total:,.0f} Ft",
            delta=f"{total - (original_quantity * original_price):,.0f} Ft"
        )
        
        # Note
        note = st.text_area(
            "Megjegyzés (opcionális)",
            placeholder="Pl. Minőségi kifogás, részleges teljesítés oka",
            height=60,
            key=f"inline_note_{offer_id}"
        )
        
        # Action buttons
        col1, col2, _ = st.columns([1, 1, 2])
        
        with col1:
            if st.button("✅ Megerősítés", type="primary", key=f"inline_confirm_{offer_id}"):
                if confirmed_quantity <= 0 or confirmed_price <= 0:
                    st.error("❌ Az értékek nem lehetnek 0 vagy negatívak!")
                else:
                    if note:
                        st.session_state[f"confirmation_note_{offer_id}"] = note
                    st.session_state[f"inline_conf_active_{offer_id}"] = False
                    st.session_state[f"show_confirmation_dialog_{offer_id}"] = False
                    return True, confirmed_quantity, confirmed_price
        
        with col2:
            if st.button("❌ Mégse", key=f"inline_cancel_{offer_id}"):
                st.session_state[f"inline_conf_active_{offer_id}"] = False
                st.session_state[f"show_confirmation_dialog_{offer_id}"] = False
                return False, None, None
    
    return None, None, None


def show_confirmation_modal(offer_id: int) -> bool:
    """
    Show confirmation modal in a popup-like container.
    Returns True if the modal should be shown.
    """
    # Check if modal should be shown
    show_modal_key = f"show_confirmation_modal_{offer_id}"
    
    if st.session_state.get(show_modal_key, False):
        # Create a full-screen overlay effect
        st.markdown("""
        <style>
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 998;
            backdrop-filter: blur(4px);
        }
        </style>
        <div class="modal-overlay"></div>
        """, unsafe_allow_html=True)
        
        return True
    
    return False


def handle_confirmation_action(offer: Dict[str, Any], offer_id: int) -> Optional[Tuple[float, float, str]]:
    """
    Handle the confirmation action from Quick Action Bar.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        
    Returns:
        Tuple of (quantity, price, note) if confirmed, None otherwise
    """
    # Set the modal to show
    show_modal_key = f"show_confirmation_modal_{offer_id}"
    
    # If the confirmation button was clicked
    if st.session_state.get(f"quick_confirm_clicked_{offer_id}", False):
        st.session_state[show_modal_key] = True
        # Reset the click state
        del st.session_state[f"quick_confirm_clicked_{offer_id}"]
    
    # If modal is showing
    if st.session_state.get(show_modal_key, False):
        # Show the dialog using the stable version
        confirmed, quantity, price = render_stable_confirmation_dialog(offer, offer_id)
        
        if confirmed is not None:
            # Modal was closed (either confirmed or cancelled)
            del st.session_state[show_modal_key]
            
            if confirmed:
                # Get the note if any
                note = st.session_state.get(f"confirmation_note_{offer_id}", "")
                if f"confirmation_note_{offer_id}" in st.session_state:
                    del st.session_state[f"confirmation_note_{offer_id}"]
                
                logger.info(f"Offer {offer_id} confirmed with quantity={quantity}, price={price}")
                return quantity, price, note
            else:
                logger.info(f"Offer {offer_id} confirmation cancelled")
                return None
    
    return None


# Integration helper for offer_detail.py
def integrate_confirmation_dialog():
    """
    Helper function to integrate confirmation dialog into offer_detail.py
    
    Usage in offer_detail.py:
    ```python
    if action == "confirm":
        from .stable_confirmation_dialog import handle_confirmation_action
        result = handle_confirmation_action(offer, offer_id)
        if result:
            quantity, price, note = result
            # Call the API to update status
            update_offer_status(offer_id, "CONFIRMED_BY_COMPANY", {
                "confirmed_quantity": quantity,
                "confirmed_price": price,
                "note": note
            })
    ```
    """
    pass