"""
Biztonságos HTML renderelési segédmodul.

Ez a modul biztonságos HTML megjelenítési funkciókat biztosít,
megelőzve a nyers HTML kódok megjelenését a felhasználói felületen.
"""
import html
import streamlit as st
import streamlit.components.v1 as components
import logging

logger = logging.getLogger(__name__)

def safe_markdown(text, is_html=False):
    """
    Biztonságos markdown renderelés HTML escape-pel.
    
    Args:
        text (str): Megjelenítendő szöveg
        is_html (bool, optional): HTML kódot tartalmaz-e. Defaults to False.
    """
    if not text:
        return
        
    try:
        if is_html:
            # Ha explicit HTML tartalmat akarunk megjeleníteni
            # For complex HTML, use components.html
            components.html(text, height=300)
        else:
            # Egyébként escape-eljük a HTML tageket
            escaped_text = html.escape(str(text))
            st.markdown(escaped_text)
    except Exception as e:
        logger.error(f"Hiba a biztonságos markdown megjelenítésekor: {str(e)}")
        # Fallback: egyszerű szövegként jelenítjük meg
        st.text(str(text))

def render_labeled_value(label, value, is_html=False):
    """
    Címke-érték pár megjelenítése biztonságos HTML kezeléssel.
    
    Args:
        label (str): Címke
        value (str): Érték
        is_html (bool, optional): HTML tartalomként kezelje-e. Defaults to False.
    """
    try:
        label_escaped = html.escape(str(label))
        
        if is_html:
            st.markdown(f"**{label_escaped}:** {value}", unsafe_allow_html=True)
        else:
            value_escaped = html.escape(str(value) if value is not None else "-")
            st.markdown(f"**{label_escaped}:** {value_escaped}")
    except Exception as e:
        logger.error(f"Hiba a címke-érték megjelenítésekor: {str(e)}")
        # Fallback: egyszerű szövegként jelenítjük meg
        st.text(f"{label}: {value}")

def render_notes(notes):
    """
    Megjegyzések biztonságos megjelenítése.
    
    Args:
        notes (str): Megjegyzések szövege
    """
    if not notes:
        st.markdown("*Nincs megjegyzés*")
        return
    
    try:
        # HTML escape az esetleges HTML kódok ellen
        escaped_notes = html.escape(str(notes))
        st.markdown(f"*{escaped_notes}*")
    except Exception as e:
        logger.error(f"Hiba a megjegyzések megjelenítésekor: {str(e)}")
        # Fallback: egyszerű szövegként jelenítjük meg
        st.text(str(notes))

def render_status_dot(color, status_text, timestamp_text=None):
    """
    Státusz pont biztonságos megjelenítése.
    
    Args:
        color (str): Státusz színe
        status_text (str): Státusz szövege
        timestamp_text (str, optional): Időbélyeg szövege. Defaults to None.
    """
    try:
        # Escape az összes szöveghez
        color_escaped = html.escape(color)
        status_text_escaped = html.escape(str(status_text))
        timestamp_html = ""
        
        if timestamp_text:
            timestamp_text_escaped = html.escape(str(timestamp_text))
            timestamp_html = f'<div style="margin-left: 10px; color: #666; font-size: 0.8em;">{timestamp_text_escaped}</div>'
        
        # HTML kód a státusz megjelenítésére
        st.markdown(f"""
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="width: 15px; height: 15px; background-color: {color_escaped}; 
                        border-radius: 50%; margin-right: 10px;"></div>
            <div style="font-weight: bold; font-size: 1.2em;">{status_text_escaped}</div>
            {timestamp_html}
        </div>
        """, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a státusz pont megjelenítésekor: {str(e)}")
        # Fallback: egyszerű szövegként jelenítjük meg
        st.text(f"Státusz: {status_text}" + (f" ({timestamp_text})" if timestamp_text else ""))

def inject_styles():
    """
    Központosított stílusok injektálása az alkalmazásba.
    """
    try:
        st.markdown("""
        <style>
        /* Általános stílusok */
        .card {
            border: 1px solid #e6e6e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .card:hover {
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
        }
        
        /* Státusz indikátorok */
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .status-dot {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 10px;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        }
        
        .status-text {
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .status-timestamp {
            margin-left: 10px;
            color: #666;
            font-size: 0.8em;
        }
        
        /* Sticky fejléc */
        .sticky-action-bar {
            position: sticky;
            top: 0;
            z-index: 999;
            background-color: white;
            padding: 10px 0;
            border-bottom: 1px solid #e6e6e6;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* Üzenetek és értesítések */
        .notification {
            padding: 10px 15px;
            border-radius: 4px;
            margin: 8px 0;
            font-weight: normal;
        }
        
        .notification-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .notification-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        
        .notification-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .notification-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        /* Gomb stílusok */
        .stButton button {
            border-radius: 4px;
            padding: 0.25rem 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .stButton button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        /* Mobilnézet specifikus stílusok */
        @media (max-width: 768px) {
            .card {
                padding: 10px;
                margin-bottom: 10px;
            }
            
            .sticky-action-bar {
                padding: 5px 0;
            }
            
            .status-text {
                font-size: 1em;
            }
            
            /* Kisebb betűméret az elféréshez */
            .stMarkdown p {
                font-size: 0.95em;
            }
            
            /* Gombok adaptálása */
            .stButton button {
                padding: 0.15rem 0.8rem;
                font-size: 0.9em;
                width: 100%;
            }
        }
        
        /* Tablet nézet */
        @media (min-width: 769px) and (max-width: 992px) {
            .card {
                padding: 12px;
            }
            
            .stMarkdown p {
                font-size: 0.97em;
            }
        }
        </style>
        """, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a stílusok injektálásakor: {str(e)}")