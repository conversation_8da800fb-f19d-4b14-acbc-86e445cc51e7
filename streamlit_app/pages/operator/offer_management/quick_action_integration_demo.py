"""
Quick Action Bar Integration Demo
Demonstrating direct integration with handle_confirmation_action
"""
import streamlit as st
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

def demo_quick_action_integration(offer: Dict[str, Any], offer_id: int) -> Optional[str]:
    """
    Demo of direct Quick Action Bar integration with stable confirmation dialog.
    
    This shows an alternative approach to the current implementation in offer_detail.py
    """
    st.markdown("### 🎯 Quick Action Bar Direct Integration Demo")
    
    # Simulate Quick Action Bar buttons
    col1, col2, col3, col4 = st.columns(4)
    
    action = None
    
    with col1:
        if st.button("← Vissza", key=f"demo_back_{offer_id}"):
            action = "back"
    
    with col2:
        if st.button("✓ Visszaigazolás", key=f"demo_confirm_{offer_id}", type="primary"):
            action = "confirm"
    
    with col3:
        if st.button("✔️ Elfogadás", key=f"demo_accept_{offer_id}"):
            action = "accept"
    
    with col4:
        if st.button("✗ Elutasítás", key=f"demo_reject_{offer_id}"):
            action = "reject"
    
    # Handle actions
    if action == "confirm":
        # Method 1: Direct integration with handle_confirmation_action
        try:
            from .stable_confirmation_dialog import handle_confirmation_action
            
            # Set the quick confirm clicked state
            st.session_state[f"quick_confirm_clicked_{offer_id}"] = True
            
            # Handle the confirmation action
            result = handle_confirmation_action(offer, offer_id)
            
            if result:
                quantity, price, note = result
                st.success(f"""
                ### ✅ Direct Integration Success!
                - **Quantity**: {quantity:,.0f} kg
                - **Price**: {price:,.0f} Ft/kg
                - **Total**: {(quantity * price):,.0f} Ft
                """)
                if note:
                    st.info(f"**Note**: {note}")
                
                # Here you would call the API
                st.code(f"""
                # API call would be:
                update_offer_status(
                    offer_id={offer_id},
                    new_status="CONFIRMED_BY_COMPANY",
                    confirmation_data={{
                        "confirmed_quantity": {quantity},
                        "confirmed_price": {price},
                        "note": "{note}"
                    }}
                )
                """)
                
        except Exception as e:
            st.error(f"Error in direct integration: {e}")
    
    elif action == "back":
        st.info("🔙 Back action")
    elif action == "accept":
        st.success("✅ Accept action")
    elif action == "reject":
        st.warning("❌ Reject action")
    
    return action


def demo_current_integration(offer: Dict[str, Any], offer_id: int) -> Optional[str]:
    """
    Demo of current integration approach (like in offer_detail.py)
    """
    st.markdown("### 🔄 Current Integration Approach")
    
    # Simulate the current approach
    if st.button("✓ Visszaigazolás (Current Method)", key=f"current_confirm_{offer_id}", type="primary"):
        # Set flag like in offer_detail.py
        st.session_state[f"show_confirmation_dialog_{offer_id}"] = True
        st.rerun()
    
    # Check if dialog should be shown (like in offer_detail.py)
    if st.session_state.get(f"show_confirmation_dialog_{offer_id}", False):
        try:
            from .stable_confirmation_dialog import render_inline_confirmation_form
            
            confirmed, quantity, price = render_inline_confirmation_form(offer, offer_id)
            
            if confirmed:
                st.success(f"""
                ### ✅ Current Integration Success!
                - **Quantity**: {quantity:,.0f} kg
                - **Price**: {price:,.0f} Ft/kg
                - **Total**: {(quantity * price):,.0f} Ft
                """)
                
                # Clear dialog state
                del st.session_state[f"show_confirmation_dialog_{offer_id}"]
                st.rerun()
                
            elif confirmed is False:
                st.info("ℹ️ Confirmation cancelled")
                del st.session_state[f"show_confirmation_dialog_{offer_id}"]
                st.rerun()
                
        except Exception as e:
            st.error(f"Error in current integration: {e}")


def compare_integration_methods(offer: Dict[str, Any], offer_id: int):
    """
    Compare both integration methods side by side
    """
    st.markdown("## 🔍 Quick Action Bar Integration Comparison")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### Method 1: Direct handle_confirmation_action")
        st.markdown("""
        **Pros:**
        - ✅ Direct API integration
        - ✅ Single function call
        - ✅ Cleaner code
        - ✅ Better separation of concerns
        
        **Code:**
        ```python
        if action == "confirm":
            st.session_state[f"quick_confirm_clicked_{offer_id}"] = True
            result = handle_confirmation_action(offer, offer_id)
            if result:
                quantity, price, note = result
                # Call API...
        ```
        """)
        
        demo_quick_action_integration(offer, offer_id + 1000)
    
    with col2:
        st.markdown("#### Method 2: Current Flag-based Approach")
        st.markdown("""
        **Pros:**
        - ✅ Currently implemented
        - ✅ Works with existing flow
        - ✅ Modal integration
        
        **Code:**
        ```python
        if action == "confirm":
            st.session_state[f"show_confirmation_dialog_{offer_id}"] = True
            st.rerun()
        
        # Later in the code...
        if st.session_state.get(f"show_confirmation_dialog_{offer_id}"):
            confirmed, quantity, price = render_inline_confirmation_form(offer, offer_id)
        ```
        """)
        
        demo_current_integration(offer, offer_id + 2000)
    
    st.markdown("---")
    st.markdown("""
    ### 🎯 Conclusion
    
    Both methods work well:
    - **Method 1** is more direct and cleaner for pure API integration
    - **Method 2** is better for complex UI flows and modal dialogs
    
    The current implementation uses **Method 2**, which is appropriate for the UI-heavy nature of the application.
    """)


# Integration test function
def test_quick_action_integration():
    """Test function for quick action integration"""
    st.title("🔧 Quick Action Bar Integration Test")
    
    # Test data
    test_offer = {
        'id': 456,
        'status': 'CREATED',
        'quantity_in_kg': 750,
        'price': 300,
        'product_type': {
            'name': 'Bio paradicsom',
            'category': {'name': 'Zöldségek'}
        },
        'user': {
            'contact_name': 'Nagy Péter',
            'company_name': 'Bio Farm Kft.',
            'email': '<EMAIL>',
            'phone': '+36301234567'
        },
        'delivery_date': '2025-06-20'
    }
    
    st.info("""
    ### 📋 Quick Action Bar Integration Testing
    
    This demo shows two different ways to integrate the stable confirmation dialog 
    with the Quick Action Bar system.
    """)
    
    compare_integration_methods(test_offer, test_offer['id'])