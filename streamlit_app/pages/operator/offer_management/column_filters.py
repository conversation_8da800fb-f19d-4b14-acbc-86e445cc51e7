"""
Excel-like column filtering functionality for the offer management page.
Provides enhanced data filtering capabilities similar to Excel's column filters.
"""
import streamlit as st
import pandas as pd
import logging
import uuid
from datetime import datetime
import re
import json

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in column_filters.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Logger setup
logger = logging.getLogger(__name__)

# Define column filter configurations
COLUMN_FILTERS = {
    "id": {
        "display_name": "Azonosító",
        "field": "id",
        "filter_type": "numeric",
        "operators": ["equals", "not_equals", "greater_than", "less_than", "between"]
    },
    "status": {
        "display_name": "Státusz",
        "field": "status",
        "filter_type": "categorical",
        "operators": ["equals", "not_equals", "in_list"]
    },
    "delivery_date": {
        "display_name": "Beszállítás dátuma",
        "field": "delivery_date",
        "filter_type": "date",
        "operators": ["equals", "not_equals", "after", "before", "between"]
    },
    "product_name": {
        "display_name": "Termék",
        "field": "product_type.name",
        "filter_type": "text",
        "operators": ["equals", "not_equals", "contains", "starts_with", "ends_with"]
    },
    "quantity_in_kg": {
        "display_name": "Mennyiség",
        "field": "quantity_in_kg",
        "filter_type": "numeric",
        "operators": ["equals", "not_equals", "greater_than", "less_than", "between"]
    },
    "price": {
        "display_name": "Ár",
        "field": "price",
        "filter_type": "numeric",
        "operators": ["equals", "not_equals", "greater_than", "less_than", "between"]
    },
    "created_at": {
        "display_name": "Létrehozva",
        "field": "created_at",
        "filter_type": "date",
        "operators": ["equals", "not_equals", "after", "before", "between"]
    },
    "producer_name": {
        "display_name": "Termelő",
        "field": "user.contact_name",
        "filter_type": "text",
        "operators": ["equals", "not_equals", "contains", "starts_with", "ends_with"]
    }
}

# Define operator display names and functions
OPERATORS = {
    "equals": {
        "display_name": "=",
        "full_name": "Egyenlő",
        "function": lambda field, value: field == value
    },
    "not_equals": {
        "display_name": "≠",
        "full_name": "Nem egyenlő",
        "function": lambda field, value: field != value
    },
    "greater_than": {
        "display_name": ">",
        "full_name": "Nagyobb mint",
        "function": lambda field, value: field > value
    },
    "less_than": {
        "display_name": "<",
        "full_name": "Kisebb mint",
        "function": lambda field, value: field < value
    },
    "between": {
        "display_name": "↔",
        "full_name": "Tartományban",
        "function": lambda field, min_value, max_value: min_value <= field <= max_value
    },
    "contains": {
        "display_name": "~",
        "full_name": "Tartalmazza",
        "function": lambda field, value: value.lower() in str(field).lower() if field else False
    },
    "starts_with": {
        "display_name": "^",
        "full_name": "Kezdődik",
        "function": lambda field, value: str(field).lower().startswith(value.lower()) if field else False
    },
    "ends_with": {
        "display_name": "$",
        "full_name": "Végződik",
        "function": lambda field, value: str(field).lower().endswith(value.lower()) if field else False
    },
    "in_list": {
        "display_name": "∈",
        "full_name": "Listában",
        "function": lambda field, value_list: field in value_list
    },
    "after": {
        "display_name": ">",
        "full_name": "Későbbi mint",
        "function": lambda field, value: field > value
    },
    "before": {
        "display_name": "<",
        "full_name": "Korábbi mint",
        "function": lambda field, value: field < value
    }
}

def generate_unique_key(base_name, suffix=None):
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name (str): Base name for the key
        suffix (str, optional): Optional suffix to add. Defaults to None.
        
    Returns:
        str: Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def get_nested_value(obj, path):
    """
    Get a value from a nested object using a dot-separated path.
    
    Args:
        obj (dict): Object to extract value from
        path (str): Dot-separated path to the value
        
    Returns:
        any: The extracted value or None if not found
    """
    try:
        parts = path.split('.')
        value = obj
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            else:
                return None
        return value
    except (AttributeError, KeyError, TypeError):
        return None

def render_filter_selection_ui():
    """
    Render UI for selecting which column to filter.
    
    Returns:
        str: Selected column key
    """
    options = list(COLUMN_FILTERS.keys())
    
    # Format function to display the friendly names
    def format_option(option):
        return COLUMN_FILTERS[option]["display_name"]
    
    # Unique key for this selector
    key = generate_unique_key("filter_column")
    
    # Create the dropdown
    selected_column = st.selectbox(
        "Oszlop kiválasztása",
        options=options,
        format_func=format_option,
        key=key
    )
    
    return selected_column

def render_operator_selection(filter_type, operators):
    """
    Render UI for selecting filter operator based on filter type.
    
    Args:
        filter_type (str): Type of filter (numeric, text, date, etc.)
        operators (list): List of available operators
        
    Returns:
        str: Selected operator key
    """
    # Unique key for this selector
    key = generate_unique_key("filter_operator")
    
    # Format function to display operator names
    def format_operator(op):
        return f"{OPERATORS[op]['display_name']} {OPERATORS[op]['full_name']}"
    
    # Create the dropdown
    selected_operator = st.selectbox(
        "Művelet",
        options=operators,
        format_func=format_operator,
        key=key
    )
    
    return selected_operator

def render_value_input(filter_type, operator, column_values=None):
    """
    Render appropriate input UI based on filter type and operator.
    
    Args:
        filter_type (str): Type of filter (numeric, text, date, etc.)
        operator (str): Selected operator
        column_values (list, optional): Available values for the column. Defaults to None.
        
    Returns:
        any: Input value(s) based on the operator and filter type
    """
    # Between operator needs two inputs
    if operator == "between":
        col1, col2 = st.columns(2)
        
        with col1:
            if filter_type == "numeric":
                min_value = st.number_input(
                    "Minimum érték",
                    key=generate_unique_key("filter_min_value"),
                    step=0.1
                )
            elif filter_type == "date":
                min_value = st.date_input(
                    "Kezdő dátum",
                    key=generate_unique_key("filter_min_date")
                )
            else:
                min_value = st.text_input(
                    "Minimum érték",
                    key=generate_unique_key("filter_min_text")
                )
        
        with col2:
            if filter_type == "numeric":
                max_value = st.number_input(
                    "Maximum érték",
                    key=generate_unique_key("filter_max_value"),
                    value=1000.0,
                    step=0.1
                )
            elif filter_type == "date":
                max_value = st.date_input(
                    "Záró dátum",
                    key=generate_unique_key("filter_max_date")
                )
            else:
                max_value = st.text_input(
                    "Maximum érték",
                    key=generate_unique_key("filter_max_text")
                )
        
        return (min_value, max_value)
    
    # In_list operator uses multiselect
    elif operator == "in_list" and column_values:
        return st.multiselect(
            "Értékek kiválasztása",
            options=column_values,
            key=generate_unique_key("filter_multiselect")
        )
    
    # Other operators need a single input
    else:
        if filter_type == "numeric":
            return st.number_input(
                "Érték",
                key=generate_unique_key("filter_value_numeric"),
                step=0.1
            )
        elif filter_type == "date":
            return st.date_input(
                "Dátum",
                key=generate_unique_key("filter_value_date")
            )
        elif filter_type == "categorical" and column_values:
            return st.selectbox(
                "Érték",
                options=column_values,
                key=generate_unique_key("filter_value_select")
            )
        else:
            return st.text_input(
                "Érték",
                key=generate_unique_key("filter_value_text")
            )

def build_filter_condition(column, operator, value):
    """
    Build a filter condition that can be represented as a tuple.
    
    Args:
        column (str): Column field path
        operator (str): Operator key
        value: Filter value
        
    Returns:
        tuple: Filter condition as (column, operator, value)
    """
    return (column, operator, value)

def apply_filter_condition(offer, condition):
    """
    Apply a filter condition to an offer.
    
    Args:
        offer (dict): Offer data
        condition (tuple): Filter condition as (column, operator, value)
        
    Returns:
        bool: True if offer passes the filter, False otherwise
    """
    column, operator, value = condition
    
    # Get the field value from the offer
    field_value = get_nested_value(offer, column)
    
    # Handle special cases for date fields
    if column in ["delivery_date", "created_at", "updated_at"] or column.endswith("_date"):
        # Convert string dates to datetime objects
        if isinstance(field_value, str):
            try:
                field_value = datetime.strptime(field_value, "%Y-%m-%d").date()
            except ValueError:
                try:
                    field_value = datetime.strptime(field_value, "%Y-%m-%dT%H:%M:%S").date()
                except ValueError:
                    pass
    
    # Apply the appropriate operator function
    operator_info = OPERATORS.get(operator)
    if not operator_info:
        return True
    
    operator_func = operator_info["function"]
    
    try:
        if operator == "between":
            min_val, max_val = value
            return operator_func(field_value, min_val, max_val)
        else:
            return operator_func(field_value, value)
    except (TypeError, ValueError) as e:
        # If there's an error in comparison, log it and default to including the item
        logger.warning(f"Filter comparison error: {e}")
        return True

def apply_filters(offers, filter_conditions):
    """
    Apply multiple filter conditions to a list of offers.
    
    Args:
        offers (list): List of offer dictionaries
        filter_conditions (list): List of filter conditions
        
    Returns:
        list: Filtered offers
    """
    if not offers or not filter_conditions:
        return offers
    
    filtered_offers = []
    for offer in offers:
        # An offer passes if it passes ALL filter conditions (AND logic)
        passes_all = True
        for condition in filter_conditions:
            if not apply_filter_condition(offer, condition):
                passes_all = False
                break
        
        if passes_all:
            filtered_offers.append(offer)
    
    return filtered_offers

def extract_unique_values(offers, column_path, limit=100):
    """
    Extract unique values for a column from offers.
    
    Args:
        offers (list): List of offer dictionaries
        column_path (str): Path to the column in the offer
        limit (int, optional): Max number of unique values to return. Defaults to 100.
        
    Returns:
        list: List of unique values
    """
    values = []
    for offer in offers:
        value = get_nested_value(offer, column_path)
        if value is not None and value not in values:
            values.append(value)
            if len(values) >= limit:
                break
    
    # Sort values for display
    try:
        return sorted(values)
    except TypeError:
        # If values can't be sorted (mixed types), return as is
        return values

def render_filter_badge(condition, column_info, index):
    """
    Render a visual badge for an active filter condition.
    
    Args:
        condition (tuple): Filter condition (column, operator, value)
        column_info (dict): Column configuration
        index (int): Index of the filter for uniqueness
        
    Returns:
        str: HTML for the filter badge
    """
    column, operator, value = condition
    column_display = column_info["display_name"]
    operator_display = OPERATORS[operator]["full_name"]
    
    # Format value for display
    if operator == "between":
        min_val, max_val = value
        if column_info["filter_type"] == "date":
            value_display = f"{min_val} - {max_val}"
        else:
            value_display = f"{min_val} - {max_val}"
    elif operator == "in_list":
        value_display = f"{len(value)} kiválasztva"
    else:
        if column_info["filter_type"] == "date" and isinstance(value, (datetime, datetime.date)):
            value_display = value.strftime("%Y-%m-%d")
        else:
            value_display = str(value)
    
    # Generate HTML for the badge
    badge_html = f"""
    <div class="filter-badge filter-badge-{index}" 
         style="display: inline-flex; align-items: center; background-color: #f0f2f6; 
                border-radius: 4px; padding: 4px 8px; margin: 4px; font-size: 0.85em;">
        <span style="font-weight: 500; margin-right: 4px;">{column_display}</span>
        <span style="color: #1976D2; margin-right: 4px;">{operator_display}</span>
        <span style="font-style: italic;">{value_display}</span>
        <button class="remove-filter-{index}" 
                style="background: none; border: none; color: #E53935; margin-left: 6px; 
                       cursor: pointer; font-weight: bold; padding: 0 4px;"
                onclick="removeFilter('{index}')">×</button>
    </div>
    """
    
    return badge_html

def render_active_filters(filter_conditions):
    """
    Render UI showing active filter conditions with remove buttons.
    
    Args:
        filter_conditions (list): List of active filter conditions
    """
    if not filter_conditions:
        return
    
    # Create JavaScript for filter removal
    js = """
    <script>
    function removeFilter(index) {
        // Set a hidden input value that Streamlit can detect
        const input = document.getElementById('remove_filter_' + index);
        if (input) {
            input.value = 'true';
            // Trigger change event to notify Streamlit
            input.dispatchEvent(new Event('change'));
        }
    }
    </script>
    """
    
    # Start HTML for filters container
    html = js + """
    <div style="margin: 10px 0;">
        <div style="font-weight: 500; margin-bottom: 5px;">Aktív szűrők:</div>
        <div style="display: flex; flex-wrap: wrap;">
    """
    
    # Add filter badges
    for i, condition in enumerate(filter_conditions):
        column, operator, value = condition
        column_info = next((info for key, info in COLUMN_FILTERS.items() if info["field"] == column), None)
        if column_info:
            html += render_filter_badge(condition, column_info, i)
    
    # Close container HTML
    html += """
        </div>
    </div>
    """
    
    # Render the HTML
    st.markdown(html, unsafe_allow_html=True)
    
    # Add hidden inputs for filter removal detection
    for i in range(len(filter_conditions)):
        if st.text_input(f"Remove filter {i}", value="false", key=f"remove_filter_{i}", label_visibility="collapsed") == "true":
            # If removal detected, trigger a callback to the main function
            st.session_state["remove_filter_index"] = i
            st.rerun()

def render_column_filter_ui(offers, existing_filters=None):
    """
    Render the Excel-like column filter UI.
    
    Args:
        offers (list): List of offer dictionaries
        existing_filters (list, optional): Existing filter conditions. Defaults to None.
        
    Returns:
        list: Updated filter conditions
    """
    # Initialize filter conditions in session state if not already present
    if "column_filter_conditions" not in st.session_state:
        st.session_state["column_filter_conditions"] = existing_filters or []
    
    # Handle filter removal
    if "remove_filter_index" in st.session_state:
        index = st.session_state["remove_filter_index"]
        if 0 <= index < len(st.session_state["column_filter_conditions"]):
            st.session_state["column_filter_conditions"].pop(index)
        del st.session_state["remove_filter_index"]
    
    # Render active filters UI
    render_active_filters(st.session_state["column_filter_conditions"])
    
    # Create an expander for the filter controls
    with st.expander("Excel-szerű oszlopszűrők", expanded=len(st.session_state["column_filter_conditions"]) == 0):
        st.markdown("### Új oszlopszűrő hozzáadása")
        
        # Column selection
        selected_column_key = render_filter_selection_ui()
        column_config = COLUMN_FILTERS.get(selected_column_key)
        
        if column_config:
            # Operator selection based on filter type
            selected_operator = render_operator_selection(
                column_config["filter_type"], 
                column_config["operators"]
            )
            
            # For categorical fields, extract unique values
            column_values = None
            if column_config["filter_type"] in ["categorical", "text"] and len(offers) > 0:
                column_values = extract_unique_values(offers, column_config["field"])
            
            # Value input based on filter type and operator
            selected_value = render_value_input(
                column_config["filter_type"],
                selected_operator,
                column_values
            )
            
            # Add filter button
            if st.button("Szűrő hozzáadása", key="add_column_filter"):
                # Create filter condition
                new_condition = build_filter_condition(
                    column_config["field"],
                    selected_operator,
                    selected_value
                )
                
                # Add to conditions list
                st.session_state["column_filter_conditions"].append(new_condition)
                
                # Show success message
                show_inline_success("Szűrő hozzáadva!")
                
                # Force refresh
                st.rerun()
        
        # Reset filters button
        if st.button("Szűrők törlése", key="reset_column_filters"):
            st.session_state["column_filter_conditions"] = []
            show_inline_info("Szűrők törölve!")
            st.rerun()
    
    # Apply filters and return updated list of filter conditions
    return st.session_state["column_filter_conditions"]

def apply_excel_like_filtering(offers):
    """
    Apply Excel-like column filtering to offers and render UI.
    
    Args:
        offers (list): List of offer dictionaries
        
    Returns:
        list: Filtered offers
    """
    # Render the filter UI and get conditions
    filter_conditions = render_column_filter_ui(offers)
    
    # Apply the filters
    filtered_offers = apply_filters(offers, filter_conditions)
    
    # Show count of filtered results if any filters applied
    if len(filter_conditions) > 0:
        total = len(offers)
        filtered = len(filtered_offers)
        st.markdown(f"**Szűrési eredmény:** {filtered} ajánlat a {total} közül ({round(filtered/total*100 if total > 0 else 0)}%)")
    
    return filtered_offers

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Excel-like Column Filtering Test", layout="wide")
    
    st.title("Excel-like Column Filtering Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"contact_name": "Termelő Tamás"}
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"contact_name": "Mezőgazda Márton"}
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-04-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"contact_name": "Termelő Tamás"}
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-03-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"contact_name": "Gyümölcsös Gábor"}
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-04-25T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"contact_name": "Almás Anna"}
        }
    ]
    
    # Apply filtering
    filtered_offers = apply_excel_like_filtering(sample_offers)
    
    # Display filtered data
    st.markdown("### Szűrt ajánlatok")
    
    # Convert to DataFrame for display
    df = pd.DataFrame([
        {
            "ID": offer["id"],
            "Státusz": format_status(offer["status"]),
            "Beszállítás": format_date(offer["delivery_date"]),
            "Termék": offer["product_type"]["name"],
            "Mennyiség": f"{offer['quantity_in_kg']} kg",
            "Ár": f"{offer['price']} Ft/kg",
            "Termelő": offer["user"]["contact_name"]
        }
        for offer in filtered_offers
    ])
    
    st.dataframe(
        df,
        hide_index=True,
        use_container_width=True
    )