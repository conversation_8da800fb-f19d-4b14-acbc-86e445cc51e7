"""
Filter persistence for offer management.
Handles saving, loading, and managing saved filters.
"""
import streamlit as st
import logging
import json
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple, Union
import uuid

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.api.offers import get_saved_filters, create_saved_filter, update_saved_filter, delete_saved_filter, set_default_filter
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
    from streamlit_app.pages.operator.offer_management.complex_filters import (
        FilterCondition,
        FilterGroup,
        LOGICAL_OPERATORS
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
        from api.offers import get_saved_filters, create_saved_filter, update_saved_filter, delete_saved_filter, set_default_filter
        from pages.operator.offer_management.complex_filters import (
            FilterCondition,
            FilterGroup,
            LOGICAL_OPERATORS
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            from complex_filters import (
                FilterCondition,
                FilterGroup,
                LOGICAL_OPERATORS
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
            
            # Mock API functions for development without the real API
            def get_saved_filters(filter_type=None):
                # Return mock data for development
                return [], None
                
            def create_saved_filter(name, filter_data, description=None, filter_type="offer", is_default=False):
                # Return mock data for development
                logger.warning("Using mock create_saved_filter function")
                return {"id": 1, "name": name, "filter_data": filter_data}, None
                
            def update_saved_filter(filter_id, data):
                # Return mock data for development
                logger.warning("Using mock update_saved_filter function")
                return {"id": filter_id, "name": data.get("name", "Unknown")}, None
                
            def delete_saved_filter(filter_id):
                # Return mock data for development
                logger.warning("Using mock delete_saved_filter function")
                return True, None
                
            def set_default_filter(filter_id):
                # Return mock data for development
                logger.warning("Using mock set_default_filter function")
                return {"id": filter_id, "is_default": True}, None
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in filter_persistence.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
            
            # Mock API functions
            def get_saved_filters(filter_type=None):
                return [], None
                
            def create_saved_filter(name, filter_data, description=None, filter_type="offer", is_default=False):
                return None, "API not available"
                
            def update_saved_filter(filter_id, data):
                return None, "API not available"
                
            def delete_saved_filter(filter_id):
                return False, "API not available"
                
            def set_default_filter(filter_id):
                return None, "API not available"
            
            class FilterCondition:
                def __init__(self, column, operator, value):
                    self.column = column
                    self.operator = operator
                    self.value = value
                    
                def to_dict(self):
                    return {
                        "column": self.column,
                        "operator": self.operator,
                        "value": self.value
                    }
            
            class FilterGroup:
                def __init__(self, operator="AND", conditions=None):
                    self.operator = operator
                    self.conditions = conditions or []
                    
                def to_dict(self):
                    return {
                        "operator": self.operator,
                        "conditions": [
                            {"type": "condition", "data": c.to_dict()} 
                            if isinstance(c, FilterCondition)
                            else {"type": "group", "data": c.to_dict()}
                            for c in self.conditions
                        ]
                    }
            
            LOGICAL_OPERATORS = {
                "AND": {"display_name": "ÉS"},
                "OR": {"display_name": "VAGY"},
                "NOT": {"display_name": "NEM"}
            }

# Setup logging
logger = logging.getLogger(__name__)

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def date_serializer(obj):
    """Custom JSON serializer for datetime and date objects."""
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

def extract_current_filters() -> Dict[str, Any]:
    """
    Extract current filter settings from session state.
    
    Returns:
        Dictionary with current filter configuration
    """
    # Basic filters
    basic_filters = {
        "user_id": st.session_state.get("producer_filter_om"),
        "product_type_id": st.session_state.get("product_filter_om"),
        "status": st.session_state.get("status_filter_om"),
        "date_from": st.session_state.get("from_date_filter_om"),
        "date_to": st.session_state.get("to_date_filter_om")
    }
    
    # Column filters
    column_filters = []
    if "column_filter_conditions" in st.session_state:
        column_filters = st.session_state["column_filter_conditions"]
    
    # Complex filters
    complex_filters = None
    if "complex_filter_root" in st.session_state:
        complex_filters = st.session_state["complex_filter_root"].to_dict()
    
    # Search query
    search_query = st.session_state.get("fuzzy_search_query", "")
    
    # Sort fields
    sort_fields = []
    if "sort_fields" in st.session_state:
        sort_fields = [
            {"field": field, "direction": direction}
            for field, direction in st.session_state["sort_fields"]
        ]
    
    return {
        "basic_filters": basic_filters,
        "column_filters": column_filters,
        "complex_filters": complex_filters,
        "search_query": search_query,
        "sort_fields": sort_fields
    }

def apply_saved_filter(filter_data: Dict[str, Any]) -> None:
    """
    Apply a saved filter configuration to the UI.
    
    Args:
        filter_data: Filter configuration to apply
    """
    # Apply basic filters
    basic_filters = filter_data.get("basic_filters", {})
    if basic_filters:
        if "user_id" in basic_filters and basic_filters["user_id"] is not None:
            st.session_state["producer_filter_om"] = basic_filters["user_id"]
        
        if "product_type_id" in basic_filters and basic_filters["product_type_id"] is not None:
            st.session_state["product_filter_om"] = basic_filters["product_type_id"]
        
        if "status" in basic_filters and basic_filters["status"] is not None:
            st.session_state["status_filter_om"] = basic_filters["status"]
        
        if "date_from" in basic_filters and basic_filters["date_from"] is not None:
            # Convert ISO date string to date object if needed
            date_from = basic_filters["date_from"]
            if isinstance(date_from, str):
                date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00')).date()
            st.session_state["from_date_filter_om"] = date_from
        
        if "date_to" in basic_filters and basic_filters["date_to"] is not None:
            # Convert ISO date string to date object if needed
            date_to = basic_filters["date_to"]
            if isinstance(date_to, str):
                date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00')).date()
            st.session_state["to_date_filter_om"] = date_to
    
    # Apply column filters
    column_filters = filter_data.get("column_filters", [])
    if column_filters:
        st.session_state["column_filter_conditions"] = column_filters
    
    # Apply complex filters
    complex_filters = filter_data.get("complex_filters")
    if complex_filters:
        try:
            # Convert the dictionary back to a FilterGroup object
            root_group = FilterGroup.from_dict(complex_filters)
            st.session_state["complex_filter_root"] = root_group
        except Exception as e:
            logger.error(f"Error reconstructing complex filters: {e}")
            show_inline_error(f"Hiba a komplex szűrők betöltése során: {e}")
    
    # Apply search query
    search_query = filter_data.get("search_query", "")
    if search_query:
        st.session_state["fuzzy_search_query"] = search_query
    
    # Apply sort fields
    sort_fields = filter_data.get("sort_fields", [])
    if sort_fields:
        try:
            # Convert the list of dictionaries to the expected format
            st.session_state["sort_fields"] = [
                (field_data["field"], field_data["direction"])
                for field_data in sort_fields
            ]
        except Exception as e:
            logger.error(f"Error setting sort fields: {e}")
            show_inline_error(f"Hiba a rendezési beállítások betöltése során: {e}")

def render_saved_filters_ui():
    """
    Render UI for managing saved filters.
    """
    # Load saved filters from API
    filters, error = get_saved_filters(filter_type="offer")
    
    if error:
        show_inline_warning(f"Hiba a mentett szűrők betöltése során: {error}")
        filters = []
    
    # Create container for saved filters UI
    with st.expander("Mentett Szűrők", expanded=False):
        st.markdown("### Mentett szűrők kezelése")
        
        # Create two columns: left for loading filters, right for saving new ones
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### Betöltés és kezelés")
            
            if not filters:
                st.info("Nincsenek mentett szűrők. Használja a 'Szűrő mentése' funkciót a jelenlegi beállítások elmentéséhez.")
            else:
                # Create a selectbox with saved filters
                filter_options = [f["name"] for f in filters]
                selected_filter_idx = st.selectbox(
                    "Válasszon mentett szűrőt",
                    range(len(filter_options)),
                    format_func=lambda i: filter_options[i],
                    key=generate_unique_key("saved_filter_select")
                )
                
                selected_filter = filters[selected_filter_idx]
                
                # Show filter details
                st.markdown(f"**Leírás:** {selected_filter.get('description', '-')}")
                st.markdown(f"**Létrehozva:** {format_datetime(selected_filter.get('created_at', '-'))}")
                
                # Load, set default, and delete buttons
                button_col1, button_col2, button_col3 = st.columns(3)
                
                with button_col1:
                    if st.button("Betöltés", key=generate_unique_key("load_filter")):
                        try:
                            # Load the selected filter
                            filter_data = selected_filter.get("filter_data", {})
                            apply_saved_filter(filter_data)
                            show_inline_success(f"'{selected_filter['name']}' szűrő betöltve!")
                            st.rerun()
                        except Exception as e:
                            logger.error(f"Error loading filter: {e}")
                            show_inline_error(f"Hiba a szűrő betöltése során: {e}")
                
                with button_col2:
                    if not selected_filter.get("is_default", False):
                        if st.button("Alapértelmezett", key=generate_unique_key("set_default_filter")):
                            result, error = set_default_filter(selected_filter["id"])
                            if error:
                                show_inline_error(f"Hiba az alapértelmezett beállítása során: {error}")
                            else:
                                show_inline_success(f"'{selected_filter['name']}' beállítva alapértelmezettként!")
                                st.rerun()
                    else:
                        st.info("Ez az alapértelmezett szűrő")
                
                with button_col3:
                    if st.button("Törlés", key=generate_unique_key("delete_filter")):
                        success, error = delete_saved_filter(selected_filter["id"])
                        if error:
                            show_inline_error(f"Hiba a törlés során: {error}")
                        else:
                            show_inline_success(f"'{selected_filter['name']}' sikeresen törölve!")
                            st.rerun()
        
        with col2:
            st.markdown("#### Új szűrő mentése")
            
            # Form for saving current filters
            with st.form("save_filter_form"):
                filter_name = st.text_input("Szűrő neve", key=generate_unique_key("filter_name"))
                filter_description = st.text_area("Leírás (opcionális)", key=generate_unique_key("filter_desc"))
                make_default = st.checkbox("Beállítás alapértelmezettként", key=generate_unique_key("make_default"))
                
                save_submitted = st.form_submit_button("Mentés")
                
                if save_submitted:
                    if not filter_name:
                        show_inline_error("A szűrő nevének megadása kötelező!")
                    else:
                        try:
                            # Extract current filter settings
                            current_filters = extract_current_filters()
                            
                            # Save the filter
                            result, error = create_saved_filter(
                                name=filter_name,
                                description=filter_description,
                                filter_data=current_filters,
                                filter_type="offer",
                                is_default=make_default
                            )
                            
                            if error:
                                show_inline_error(f"Hiba a szűrő mentése során: {error}")
                            else:
                                show_inline_success(f"'{filter_name}' szűrő sikeresen elmentve!")
                                st.rerun()
                        except Exception as e:
                            logger.error(f"Error saving filter: {e}")
                            show_inline_error(f"Hiba a szűrő mentése során: {e}")

def check_and_apply_default_filter():
    """
    Check for and apply the default filter if no other filters are set.
    First checks for user-specific default filter, then falls back to global default.
    
    Returns:
        bool: True if default filter was applied, False otherwise
    """
    # Check if basic filters are set
    basic_filters_set = any([
        st.session_state.get("producer_filter_om") is not None,
        st.session_state.get("product_filter_om") is not None,
        st.session_state.get("status_filter_om") is not None,
        # Don't check date filters as they usually have defaults
    ])
    
    # Check if other filter types are set
    other_filters_set = any([
        "column_filter_conditions" in st.session_state and st.session_state["column_filter_conditions"],
        "complex_filter_root" in st.session_state and st.session_state["complex_filter_root"].conditions,
        st.session_state.get("fuzzy_search_query", ""),
    ])
    
    # If any filters are already set, don't apply default
    if basic_filters_set or other_filters_set:
        return False
    
    # Try to import user_preferences module
    try:
        # Try different import paths
        try:
            # Absolute import
            from streamlit_app.pages.operator.offer_management.user_preferences import get_default_filter_for_user
        except ImportError:
            try:
                # Relative import
                from pages.operator.offer_management.user_preferences import get_default_filter_for_user
            except ImportError:
                # Local import
                from user_preferences import get_default_filter_for_user
                
        # First try to get user-specific default filter
        user_default, error = get_default_filter_for_user()
        
        if not error and user_default:
            # Apply user-specific default filter
            try:
                filter_data = user_default.get("filter_data", {})
                apply_saved_filter(filter_data)
                logger.info(f"Applied user-specific default filter (ID: {user_default['id']})")
                show_inline_info("Személyes alapértelmezett szűrő betöltve")
                return True
            except Exception as e:
                logger.error(f"Error applying user-specific default filter: {e}")
                # Fall through to try global default
    except ImportError:
        logger.warning("Could not import user_preferences module, falling back to global default filter")
    except Exception as e:
        logger.error(f"Error getting user-specific default filter: {e}")
    
    # If user-specific default filter couldn't be applied, try global default
    
    # Load saved filters from API
    filters, error = get_saved_filters(filter_type="offer")
    
    if error or not filters:
        return False
    
    # Look for global default filter
    default_filter = next((f for f in filters if f.get("is_default", False)), None)
    
    if not default_filter:
        return False
    
    # Apply the global default filter
    try:
        filter_data = default_filter.get("filter_data", {})
        apply_saved_filter(filter_data)
        logger.info(f"Applied global default filter: {default_filter['name']}")
        show_inline_info(f"Alapértelmezett szűrő betöltve: {default_filter['name']}")
        return True
    except Exception as e:
        logger.error(f"Error applying global default filter: {e}")
        return False

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Filter Persistence Test", layout="wide")
    
    st.title("Filter Persistence Test")
    
    # Initialize session state for testing
    if "producer_filter_om" not in st.session_state:
        st.session_state["producer_filter_om"] = None
    if "product_filter_om" not in st.session_state:
        st.session_state["product_filter_om"] = None
    if "status_filter_om" not in st.session_state:
        st.session_state["status_filter_om"] = "CREATED"
    if "from_date_filter_om" not in st.session_state:
        st.session_state["from_date_filter_om"] = date.today().replace(day=1)
    if "to_date_filter_om" not in st.session_state:
        st.session_state["to_date_filter_om"] = date.today()
    
    # Mock complex filters for testing
    if "complex_filter_root" not in st.session_state:
        root_group = FilterGroup("AND")
        root_group.add_condition(FilterCondition("status", "equals", "CREATED"))
        st.session_state["complex_filter_root"] = root_group
    
    # Create test UI
    st.markdown("### Current Filters")
    st.write({
        "producer_filter_om": st.session_state.get("producer_filter_om"),
        "product_filter_om": st.session_state.get("product_filter_om"),
        "status_filter_om": st.session_state.get("status_filter_om"),
        "from_date_filter_om": st.session_state.get("from_date_filter_om"),
        "to_date_filter_om": st.session_state.get("to_date_filter_om"),
    })
    
    # Extract and display current filters
    current_filters = extract_current_filters()
    st.markdown("### Extracted Current Filters")
    
    # Convert date objects to strings for st.json compatibility
    def make_json_compatible(obj):
        if isinstance(obj, dict):
            return {k: make_json_compatible(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [make_json_compatible(item) for item in obj]
        elif isinstance(obj, (date, datetime)):
            return obj.strftime('%Y-%m-%d')
        else:
            return obj
    
    compatible_filters = make_json_compatible(current_filters)
    st.json(compatible_filters)
    
    # Render saved filters UI
    render_saved_filters_ui()
    
    # Check if default filter should be applied
    should_apply_default = st.checkbox("Check and apply default filter", value=False)
    if should_apply_default:
        applied = check_and_apply_default_filter()
        st.write(f"Default filter applied: {applied}")