"""
Widget Key Manager - Duplicate Widget Key Prevention System
🚨 CRITICAL: Prevents StreamlitAPIException for duplicate widget keys

This module provides utilities to generate unique widget keys and cleanup old keys
to prevent the common Streamlit issue of duplicate widget IDs.
"""

import streamlit as st
import uuid
import hashlib
import time
import logging
from typing import Set, List, Dict, Any

logger = logging.getLogger(__name__)


class WidgetKeyManager:
    """Centralized widget key management to prevent duplicates"""
    
    def __init__(self):
        self.active_keys: Set[str] = set()
        self.key_history: Dict[str, float] = {}  # key -> timestamp
        self.cleanup_interval = 300  # 5 minutes
        
    def generate_unique_key(self, base_key: str, identifier: str = "", context: str = "") -> str:
        """
        Generate a guaranteed unique widget key
        
        Args:
            base_key: Base name for the key (e.g., 'trace_details')
            identifier: Unique identifier (e.g., trace_id, user_id)
            context: Additional context (e.g., 'debug', 'main')
            
        Returns:
            str: Guaranteed unique widget key
        """
        # Create a comprehensive unique string
        timestamp = str(int(time.time() * 1000000))  # Microsecond precision
        random_component = str(uuid.uuid4())[:8]
        
        # Combine all components
        full_string = f"{base_key}_{identifier}_{context}_{timestamp}_{random_component}"
        
        # Create hash to avoid extremely long keys
        hash_suffix = hashlib.md5(full_string.encode()).hexdigest()[:12]
        
        # Final key format: base_component_hash
        unique_key = f"{base_key}_{hash_suffix}"
        
        # Track the key
        self.active_keys.add(unique_key)
        self.key_history[unique_key] = time.time()
        
        # Periodic cleanup
        self._cleanup_old_keys()
        
        logger.debug(f"Generated unique key: {unique_key} for {base_key}")
        return unique_key
    
    def cleanup_keys_by_prefix(self, prefix: str) -> int:
        """
        Remove all session state keys starting with the given prefix
        
        Args:
            prefix: Key prefix to cleanup (e.g., 'trace_details_')
            
        Returns:
            int: Number of keys removed
        """
        if not hasattr(st, 'session_state'):
            return 0
            
        keys_to_remove = [key for key in st.session_state.keys() if key.startswith(prefix)]
        
        for key in keys_to_remove:
            try:
                del st.session_state[key]
                self.active_keys.discard(key)
                self.key_history.pop(key, None)
            except Exception as e:
                logger.warning(f"Failed to remove key {key}: {e}")
        
        if keys_to_remove:
            logger.info(f"🧹 Cleaned up {len(keys_to_remove)} keys with prefix '{prefix}'")
            
        return len(keys_to_remove)
    
    def _cleanup_old_keys(self):
        """Internal cleanup of old keys based on age"""
        if len(self.key_history) < 100:  # Only cleanup when we have many keys
            return
            
        current_time = time.time()
        old_keys = [
            key for key, timestamp in self.key_history.items()
            if current_time - timestamp > self.cleanup_interval
        ]
        
        for key in old_keys:
            self.active_keys.discard(key)
            self.key_history.pop(key, None)
            
        if old_keys:
            logger.debug(f"Cleaned up {len(old_keys)} old keys from history")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about widget key usage"""
        return {
            'active_keys_count': len(self.active_keys),
            'history_count': len(self.key_history),
            'session_state_keys': len(st.session_state.keys()) if hasattr(st, 'session_state') else 0
        }
    
    def emergency_cleanup(self) -> int:
        """Emergency cleanup of all tracked keys - use as last resort"""
        total_cleaned = 0
        
        # Common problematic prefixes
        problematic_prefixes = [
            "trace_details_",
            "show_trace_", 
            "trace_info_",
            "debug_",
            "filter_debug_",
            "api_debug_",
            "producer_test_",
            "diagnosis_",
            "validation_"
        ]
        
        for prefix in problematic_prefixes:
            total_cleaned += self.cleanup_keys_by_prefix(prefix)
        
        # Clear internal tracking
        self.active_keys.clear()
        self.key_history.clear()
        
        logger.warning(f"🚨 Emergency cleanup: Removed {total_cleaned} keys")
        return total_cleaned


# Global instance for the application
widget_key_manager = WidgetKeyManager()


def get_unique_widget_key(base_key: str, identifier: str = "", context: str = "") -> str:
    """
    Convenience function to generate unique widget key
    
    Args:
        base_key: Base name for the key
        identifier: Unique identifier 
        context: Additional context
        
    Returns:
        str: Unique widget key
    """
    return widget_key_manager.generate_unique_key(base_key, identifier, context)


def cleanup_widget_keys(prefix: str) -> int:
    """
    Convenience function to cleanup widget keys by prefix
    
    Args:
        prefix: Key prefix to cleanup
        
    Returns:
        int: Number of keys cleaned up
    """
    return widget_key_manager.cleanup_keys_by_prefix(prefix)


def emergency_widget_cleanup() -> int:
    """
    Emergency cleanup function for severe widget key issues
    
    Returns:
        int: Number of keys cleaned up
    """
    return widget_key_manager.emergency_cleanup()


# Compatibility functions for existing code
def generate_unique_widget_key(base_key: str, identifier: str, context: str = "") -> str:
    """Legacy compatibility function"""
    return get_unique_widget_key(base_key, identifier, context)


def cleanup_widget_keys_in_session(key_prefix: str) -> int:
    """Legacy compatibility function"""
    return cleanup_widget_keys(key_prefix)