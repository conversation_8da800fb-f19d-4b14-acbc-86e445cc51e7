"""
<PERSON><PERSON><PERSON><PERSON><PERSON>, direkt dátumszűrő komponens garantált működéssel.
Az előre definiált szűrők automatikusan beállítják a dátum mezőket.
"""
import streamlit as st
from datetime import datetime, timed<PERSON><PERSON>

def render_date_filter(page_id):
    """
    <PERSON><PERSON><PERSON><PERSON>, robusztus dátum szűrő komponens.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        tuple: (from_date, to_date) a kiválasztott dátumtartomány.
    """
    # Mai dátum
    today = datetime.now().date()
    
    # Alapértelmezett értékek
    default_from = today - timedelta(days=30)
    default_to = today
    
    # Session state kulcsok
    from_date_key = f"from_date_filter_{page_id}"
    to_date_key = f"to_date_filter_{page_id}"
    
    # Inicializálás, ha szükséges
    if from_date_key not in st.session_state:
        st.session_state[from_date_key] = default_from
    
    if to_date_key not in st.session_state:
        st.session_state[to_date_key] = default_to
        
    # Előre definiált időszakok gombjai
    st.markdown("**Előre definiált időszakok:**")
    col1, col2, col3 = st.columns(3)
    
    # Első sor
    with col1:
        if st.button("Előző hónap", key="btn_prev_month", use_container_width=True):
            st.session_state[from_date_key] = today - timedelta(days=30)
            st.session_state[to_date_key] = today
            st.rerun()
            
    with col2:
        if st.button("Előző 2 hét", key="btn_prev_2_weeks", use_container_width=True):
            st.session_state[from_date_key] = today - timedelta(days=14)
            st.session_state[to_date_key] = today
            st.rerun()
            
    with col3:
        if st.button("Mai nap", key="btn_today", use_container_width=True):
            st.session_state[from_date_key] = today
            st.session_state[to_date_key] = today
            st.rerun()
    
    # Második sor
    col4, col5, col6 = st.columns(3)
    
    with col4:
        if st.button("Következő hét", key="btn_next_week", use_container_width=True):
            st.session_state[from_date_key] = today
            st.session_state[to_date_key] = today + timedelta(days=7)
            st.rerun()
            
    with col5:
        if st.button("Következő 2 hét", key="btn_next_2_weeks", use_container_width=True):
            st.session_state[from_date_key] = today
            st.session_state[to_date_key] = today + timedelta(days=14)
            st.rerun()
            
    with col6:
        if st.button("Következő hónap", key="btn_next_month", use_container_width=True):
            st.session_state[from_date_key] = today
            st.session_state[to_date_key] = today + timedelta(days=30)
            st.rerun()
    
    # Dátum mezők közvetlenül
    st.markdown("**Dátumtartomány finomhangolása:**")
    
    # Képernyőméret ellenőrzése
    is_mobile = st.session_state.get('is_mobile', False)
    
    # Dátummezők elrendezése képernyőméret szerint
    if is_mobile:
        # Egymás alatt
        from_date = st.date_input(
            "Kezdő dátum:",
            value=st.session_state[from_date_key],
            key=from_date_key
        )
        
        to_date = st.date_input(
            "Végső dátum:",
            value=st.session_state[to_date_key], 
            key=to_date_key
        )
    else:
        # Két oszlopban
        col1, col2 = st.columns(2)
        
        with col1:
            from_date = st.date_input(
                "Kezdő dátum:",
                value=st.session_state[from_date_key],
                key=from_date_key
            )
        
        with col2:
            to_date = st.date_input(
                "Végső dátum:",
                value=st.session_state[to_date_key],
                key=to_date_key
            )
    
    # Validálás
    if from_date > to_date:
        st.warning("A kezdő dátum nem lehet későbbi, mint a végső dátum! Automatikusan korrigálva.")
        from_date = to_date - timedelta(days=1)
        st.session_state[from_date_key] = from_date
    
    return from_date, to_date