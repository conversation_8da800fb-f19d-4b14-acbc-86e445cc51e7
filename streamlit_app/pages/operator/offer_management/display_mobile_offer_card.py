"""
Mobile view card component for displaying offers with the enhanced status visualization.
"""
import streamlit as st
import logging

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_date, format_quantity, format_status
    from streamlit_app.pages.operator.offer_management.status_visualization import render_status_pill
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.status_visualization import render_status_pill
        from utils.formatting import format_date, format_quantity, format_status
    except ImportError:
        try:
            # Try direct local import
            from status_visualization import render_status_pill
            
            # Fallback formatting functions
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
            format_status = lambda x: x
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in status_visualization.py, using minimal implementations")
            
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
            format_status = lambda x: x
            
            # Simple status pill fallback
            def render_status_pill(status, size="medium", with_icon=True, with_text=True):
                return f"<span style='color: #333;'>{format_status(status)}</span>"

# Logger setup
logger = logging.getLogger(__name__)

def display_enhanced_mobile_offer_card(offer, index, on_click_func=None):
    """
    Enhanced mobile offer card with improved status visualization.
    
    Args:
        offer (dict): The offer data dictionary
        index (int): Index for the card (used for unique keys)
        on_click_func (callable, optional): Function to call when card is clicked
    """
    # Get offer data
    offer_id = offer.get("id", "N/A")
    status = offer.get("status", "")
    delivery_date = format_date(offer.get("delivery_date", ""))
    product_name = offer.get("product_type", {}).get("name", "Ismeretlen termék")
    
    # Handle both old and new quantity formats with proper unit support
    quantity_value = offer.get("quantity_value", offer.get("quantity_in_kg", 0))
    quantity_unit = offer.get("quantity_unit", "kg")
    quantity = format_quantity(quantity_value, quantity_unit)
    
    producer_name = offer.get("user", {}).get("contact_name", "Ismeretlen termelő")
    
    # Create the card
    card_html = f"""
    <style>
    .mobile-offer-card-{index} {{
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #1976D2;
        transition: transform 0.2s, box-shadow 0.2s;
    }}
    .mobile-offer-card-{index}:hover {{
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }}
    .card-header-{index} {{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }}
    .card-id-{index} {{
        font-weight: bold;
        font-size: 1.1rem;
    }}
    .card-body-{index} {{
        margin-bottom: 10px;
    }}
    .card-row-{index} {{
        display: flex;
        margin-bottom: 5px;
    }}
    .card-label-{index} {{
        font-weight: 500;
        width: 100px;
        color: #666;
    }}
    .card-value-{index} {{
        font-weight: 400;
    }}
    .card-footer-{index} {{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }}
    </style>
    
    <div class="mobile-offer-card-{index}">
        <div class="card-header-{index}">
            <div class="card-id-{index}">#{offer_id}</div>
            {render_status_pill(status)}
        </div>
        <div class="card-body-{index}">
            <div class="card-row-{index}">
                <div class="card-label-{index}">Termék:</div>
                <div class="card-value-{index}">{product_name}</div>
            </div>
            <div class="card-row-{index}">
                <div class="card-label-{index}">Mennyiség:</div>
                <div class="card-value-{index}">{quantity}</div>
            </div>
            <div class="card-row-{index}">
                <div class="card-label-{index}">Beszállítás:</div>
                <div class="card-value-{index}">{delivery_date}</div>
            </div>
            <div class="card-row-{index}">
                <div class="card-label-{index}">Termelő:</div>
                <div class="card-value-{index}">{producer_name}</div>
            </div>
        </div>
    </div>
    """
    
    # Render the HTML
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Add detail button
    if on_click_func and st.button("Részletek megjelenítése", key=f"view_details_{offer_id}_{index}"):
        on_click_func()