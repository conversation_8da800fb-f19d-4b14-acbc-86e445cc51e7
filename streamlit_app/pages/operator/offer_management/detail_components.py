"""
Ajánlat részletek megjelenítésére szolgá<PERSON>ó komponen<PERSON>k.
Egységes, újrafelhasználható komponensek a részletek struktúrált megjelenítéséhez.
"""
import streamlit as st
import streamlit.components.v1 as components
import uuid
import logging
import html
from datetime import datetime

# Try multiple import paths with fallbacks for formatting utilities
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_datetime, format_date, format_price, format_quantity
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_datetime, format_date, format_price, format_quantity
    except ImportError:
        # Fallback formatting functions if import fails
        logging.warning("Could not import formatting functions in detail_components.py, using fallbacks")
        format_status = lambda x: x
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Try to import our custom HTML rendering module
try:
    from .html_rendering import safe_markdown, render_labeled_value, render_notes, render_status_dot, inject_styles
except ImportError:
    try:
        from html_rendering import safe_markdown, render_labeled_value, render_notes, render_status_dot, inject_styles
    except ImportError:
        # Fallback implementations if import fails
        logging.warning("Could not import html_rendering module, using fallbacks")
        
        def safe_markdown(text, is_html=False):
            if not text:
                return
            if is_html:
                components.html(text, height=200)
            else:
                escaped_text = html.escape(str(text))
                st.markdown(escaped_text)
        
        def render_labeled_value(label, value, is_html=False):
            label_escaped = html.escape(str(label))
            if is_html:
                st.markdown(f"**{label_escaped}:** {value}", unsafe_allow_html=True)
            else:
                value_escaped = html.escape(str(value) if value is not None else "-")
                st.markdown(f"**{label_escaped}:** {value_escaped}")
        
        def render_notes(notes):
            if not notes:
                st.markdown("*Nincs megjegyzés*")
                return
            escaped_notes = html.escape(str(notes))
            st.markdown(f"*{escaped_notes}*")
        
        def render_status_dot(color, status_text, timestamp_text=None):
            timestamp_html = ""
            if timestamp_text:
                timestamp_html = f'<div style="margin-left: 10px; color: #666; font-size: 0.8em;">{html.escape(str(timestamp_text))}</div>'
            st.markdown(f"""
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="width: 15px; height: 15px; background-color: {html.escape(color)}; 
                            border-radius: 50%; margin-right: 10px;"></div>
                <div style="font-weight: bold; font-size: 1.2em;">{html.escape(str(status_text))}</div>
                {timestamp_html}
            </div>
            """, unsafe_allow_html=True)
        
        def inject_styles():
            st.markdown("""
            <style>
            .sticky-action-bar {
                position: sticky;
                top: 0;
                z-index: 999;
                background-color: white;
                padding: 10px 0;
                border-bottom: 1px solid #e6e6e6;
                margin-bottom: 20px;
            }
            </style>
            """, unsafe_allow_html=True)

logger = logging.getLogger(__name__)

class DetailContainer:
    """
    Egyszerűsített részlet konténer komponens natív Streamlit komponensekkel.
    
    Ajánlat részleteinek megjelenítéséhez használható panel, HTML renderelés helyett
    Streamlit natív komponenseket használ a jobb kompatibilitás érdekében.
    """
    
    def __init__(self, title, icon=None, expandable=True, expanded=True, loading_key=None, key=None, color="#3584e4"):
        """
        Inicializálja a részlet konténert.
        
        Args:
            title (str): A panel címe
            icon (str, optional): Emoji ikon. Defaults to None.
            expandable (bool, optional): Összecsukható-e a panel. Defaults to True.
            expanded (bool, optional): Alapértelmezetten kinyitott-e. Defaults to True.
            loading_key (str, optional): Session state kulcs a betöltési állapothoz. Defaults to None.
            key (str, optional): Egyedi azonosító. Defaults to None.
            color (str, optional): A panel fejlécének színe (nem használt az egyszerűsített verzióban). Defaults to "#3584e4".
        """
        self.title = title
        self.icon = icon or "📋"
        self.expandable = expandable
        self.expanded = expanded
        self.loading_key = loading_key
        self.key = key or f"detail_container_{str(uuid.uuid4())[:8]}"
        self.color = color
    
    def render(self, content_callback, loading_text="Betöltés..."):
        """Egyszerűsített rendering Streamlit natív komponensekkel"""
        
        # Betöltési állapot ellenőrzése
        loading = st.session_state.get(self.loading_key, False) if self.loading_key else False
        
        # Debug opció (egyszerűsített)
        debug_enabled = st.checkbox(f"🔍 Debug {self.title}", key=f"debug_{self.key}", value=False)
        
        if debug_enabled:
            st.info(f"**Debug Info:** Container '{self.title}' - Key: {self.key}")
            
            # Callback teszt
            try:
                if callable(content_callback):
                    st.success("✅ Content callback is callable")
                else:
                    st.error(f"❌ Content callback is not callable: {type(content_callback)}")
            except Exception as e:
                st.error(f"❌ Error checking callback: {str(e)}")
        
        # Tartalom renderelése
        try:
            if self.expandable:
                # Streamlit natív expander használata
                with st.expander(f"{self.icon} {self.title}", expanded=self.expanded):
                    if loading:
                        st.info(loading_text)
                        self._render_skeleton()
                    else:
                        if debug_enabled:
                            st.info("📋 Executing content callback...")
                        content_callback()
                        if debug_enabled:
                            st.success("✅ Content callback executed successfully")
            else:
                # Egyszerű container címmel
                st.subheader(f"{self.icon} {self.title}")
                
                with st.container():
                    if loading:
                        st.info(loading_text)
                        self._render_skeleton()
                    else:
                        if debug_enabled:
                            st.info("📋 Executing content callback...")
                        content_callback()
                        if debug_enabled:
                            st.success("✅ Content callback executed successfully")
                            
        except Exception as e:
            logger.error(f"Hiba a DetailContainer rendereléskor: {str(e)}")
            st.error(f"Hiba történt a tartalom megjelenítésekor: {str(e)}")
            if debug_enabled:
                st.exception(e)
    
    def _render_skeleton(self):
        """Egyszerűsített skeleton loader Streamlit komponensekkel"""
        with st.container():
            # Skeleton placeholder-ek
            st.empty()
            with st.spinner(""):
                # Üres placeholder sorok
                for i in range(3):
                    cols = st.columns([3, 1])
                    with cols[0]:
                        st.write("⠀" * 20)  # Invisible unicode spaces
                    with cols[1]:
                        st.write("⠀" * 8)


class StatusIndicator:
    """
    Egyszerűsített státusz megjelenítő komponens natív Streamlit komponensekkel.
    
    Ajánlat státuszát vizuálisan megjelenítő komponens, HTML renderelés helyett
    Streamlit natív komponenseket használ.
    """
    
    def __init__(self, status, timestamp=None, description=None):
        """
        Inicializálja a státusz indikátort.
        
        Args:
            status (str): Az ajánlat státusza
            timestamp (str/datetime, optional): Státusz időbélyege. Defaults to None.
            description (str, optional): Opcionális státuszleírás. Defaults to None.
        """
        self.status = status
        self.timestamp = timestamp
        self.description = description
    
    def render(self):
        """Státusz indikátor natív Streamlit komponensekkel"""
        try:
            # Státusz konfiguráció
            status_config = {
                "CREATED": {"icon": "🔵", "label": "Létrehozva", "type": "info"},
                "CONFIRMED_BY_COMPANY": {"icon": "🟡", "label": "Cég által visszaigazolva", "type": "warning"},
                "ACCEPTED_BY_USER": {"icon": "🟢", "label": "Elfogadva", "type": "success"},
                "REJECTED_BY_USER": {"icon": "🔴", "label": "Elutasítva", "type": "error"},
                "FINALIZED": {"icon": "✅", "label": "Véglegesítve", "type": "success"},
            }
            
            config = status_config.get(self.status, {
                "icon": "⚪", 
                "label": self.status,
                "type": "info"
            })
            
            # Natív Streamlit komponensekkel megjelenítés
            status_text = f"{config['icon']} **{config['label']}**"
            
            if config['type'] == "success":
                st.success(status_text)
            elif config['type'] == "warning":
                st.warning(status_text)
            elif config['type'] == "error":
                st.error(status_text)
            else:
                st.info(status_text)
            
            # Időbélyeg és leírás
            if self.timestamp:
                st.caption(f"📅 {format_datetime(self.timestamp)}")
            if self.description:
                st.info(f"💬 {self.description}")
                
        except Exception as e:
            logger.error(f"Hiba a státusz megjelenítésekor: {str(e)}")
            st.error(f"Nem sikerült megjeleníteni a státuszt: {str(e)}")



class EntityCard:
    """
    Kapcsolódó entitás adatkártya.
    
    Termelők, termékek, kapcsolódó ajánlatok adatainak megjelenítésére szolgáló komponens.
    """
    
    def __init__(self, title, data, entity_type):
        """
        Inicializálja az entitás kártyát.
        
        Args:
            title (str): A kártya címe
            data (dict): Az entitás adatai
            entity_type (str): Az entitás típusa ("producer", "product", "related_offer")
        """
        self.title = title
        self.data = data
        self.entity_type = entity_type
    
    def render(self):
        """Entitás kártya megjelenítése szép HTML design-nal"""
        # HTML kártya kezdete
        st.markdown(f"""
        <div style="
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        ">
            <h4 style="margin-top: 0; color: #333;">{self.title}</h4>
        </div>
        """, unsafe_allow_html=True)
        
        if not self.data:
            st.info(f"Nincs elérhető {self.entity_type} adat.")
            return
        
        # Tartalom renderelése
        with st.container():
            try:
                if self.entity_type == "producer":
                    self._render_producer_html()
                elif self.entity_type == "product":
                    self._render_product_html()
                elif self.entity_type == "related_offer":
                    self._render_related_offer_html()
                else:
                    st.warning(f"Ismeretlen entitástípus: {self.entity_type}")
            except Exception as e:
                logger.error(f"Hiba az entitás megjelenítésekor: {str(e)}")
                st.error(f"Nem sikerült megjeleníteni az entitás adatait: {str(e)}")
    
    def _render_producer(self):
        """FIXED: Termelő adatok megjelenítése st.write-tal"""
        is_mobile = st.session_state.get('is_mobile', False)
        
        if is_mobile:
            # Egymás alatt megjelenítve mobilon
            st.write(f"**Cégnév:** {self.data.get('company_name', '-')}")
            st.write(f"**Kapcsolattartó:** {self.data.get('contact_name', '-')}")
            st.write(f"**Email:** {self.data.get('email', '-')}")
            st.write(f"**Telefon:** {self.data.get('phone', '-')}")
            if self.data.get('address'):
                st.write(f"**Cím:** {self.data.get('address')}")
        else:
            # Két oszlopban megjelenítve asztali nézeten
            col1, col2 = st.columns(2)
            with col1:
                st.write("**Cégnév:**")
                st.write("**Kapcsolattartó:**")
                st.write("**Email:**")
                st.write("**Telefon:**")
                if self.data.get('address'):
                    st.write("**Cím:**")
            with col2:
                st.write(self.data.get('company_name', '-'))
                st.write(self.data.get('contact_name', '-'))
                st.write(self.data.get('email', '-'))
                st.write(self.data.get('phone', '-'))
                if self.data.get('address'):
                    st.write(self.data.get('address'))
    
    def _render_product(self):
        """Termék adatok megjelenítése"""
        # Mobilnézet ellenőrzése
        is_mobile = st.session_state.get('is_mobile', False)
        
        if is_mobile:
            # Egymás alatt megjelenítve mobilon
            st.markdown(f"**Termék:** {self.data.get('name', '-')}")
            st.markdown(f"**Kategória:** {self.data.get('category', {}).get('name', '-')}")
            
            # Leírás (ha van)
            if self.data.get("description"):
                st.markdown("**Leírás:**")
                st.markdown(f"{self.data['description']}")
                
            # Mértékegység (ha van)
            if "unit" in self.data:
                st.markdown(f"**Mértékegység:** {self.data['unit']}")
        else:
            # Két oszlopban megjelenítve asztali nézeten
            cols = st.columns(2)
            with cols[0]:
                st.markdown("**Termék:**")
                st.markdown("**Kategória:**")
                if "unit" in self.data:
                    st.markdown("**Mértékegység:**")
            with cols[1]:
                st.markdown(f"{self.data.get('name', '-')}")
                st.markdown(f"{self.data.get('category', {}).get('name', '-')}")
                if "unit" in self.data:
                    st.markdown(f"{self.data['unit']}")
            
            # Leírás (ha van)
            if self.data.get("description"):
                st.markdown("**Leírás:**")
                st.markdown(f"{self.data['description']}")
    
    def _render_related_offer(self):
        """Kapcsolódó ajánlat megjelenítése"""
        # Mobilnézet ellenőrzése
        is_mobile = st.session_state.get('is_mobile', False)
        
        # Status indicator az ajánlat státuszához
        StatusIndicator(self.data.get('status', '-')).render()
        
        # Importáljuk a html_rendering függvényeket, ha lehetséges
        try:
            from .html_rendering import safe_markdown, render_labeled_value
        except ImportError:
            try:
                from html_rendering import safe_markdown, render_labeled_value
            except ImportError:
                import html
                def safe_markdown(text, is_html=False):
                    if not text:
                        return
                    if is_html:
                        components.html(text, height=200)
                    else:
                        escaped_text = html.escape(str(text))
                        st.markdown(escaped_text)
                
                def render_labeled_value(label, value, is_html=False):
                    label_escaped = html.escape(str(label))
                    if is_html:
                        st.markdown(f"**{label_escaped}:** {value}", unsafe_allow_html=True)
                    else:
                        value_escaped = html.escape(str(value) if value is not None else "-")
                        st.markdown(f"**{label_escaped}:** {value_escaped}")
        
        if is_mobile:
            # Egymás alatt megjelenítve mobilon
            render_labeled_value("Ajánlat #", self.data.get('id', '-'))
            render_labeled_value("Termék", self.data.get('product_type', {}).get('name', '-'))
            render_labeled_value("Mennyiség", f"{format_quantity(self.data.get('quantity_in_kg', '-'))} kg")
            
            # Dátumok
            if "created_at" in self.data:
                render_labeled_value("Létrehozva", format_date(self.data['created_at']))
            if "delivery_date" in self.data:
                render_labeled_value("Beszállítás", format_date(self.data['delivery_date']))
                
            # Ár, ha van
            if "price" in self.data:
                render_labeled_value("Ár", format_price(self.data['price']))
        else:
            # Két oszlopban asztali nézeten
            cols = st.columns(2)
            with cols[0]:
                st.markdown(f"**Ajánlat #{self.data.get('id', '-')}**")
                st.markdown(f"**Termék:** {self.data.get('product_type', {}).get('name', '-')}")
                st.markdown(f"**Mennyiség:** {format_quantity(self.data.get('quantity_in_kg', '-'))} kg")
            with cols[1]:
                if "created_at" in self.data:
                    st.markdown(f"**Létrehozva:** {format_date(self.data['created_at'])}")
                if "delivery_date" in self.data:
                    st.markdown(f"**Beszállítás:** {format_date(self.data['delivery_date'])}")
                if "price" in self.data:
                    st.markdown(f"**Ár:** {format_price(self.data['price'])}")
                    
        # Gomb az ajánlat megtekintéséhez - teljesen egyedi kulcsot használunk
        button_key = f"related_offer_{self.data.get('id', '')}_{str(uuid.uuid4())}"
        
        # Callback függvény a gombhoz
        def view_offer_callback():
            offer_id = self.data.get('id')
            if offer_id is not None:
                st.session_state.selected_offer_id = offer_id
                st.rerun()
            else:
                st.warning("Hiányzó ajánlat azonosító")
        
        st.button("Megtekintés", key=button_key, on_click=view_offer_callback)
    
    def _render_producer_html(self):
        """Termelő adatok HTML megjelenítése"""
        html_content = f"""
        <div style="padding: 0 16px;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Cégnév:</td>
                    <td style="padding: 8px 0;">{self.data.get('company_name', '-')}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Kapcsolattartó:</td>
                    <td style="padding: 8px 0;">{self.data.get('contact_name', '-')}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Email:</td>
                    <td style="padding: 8px 0;">
                        <a href="mailto:{self.data.get('email', '')}" style="color: #1976D2;">
                            {self.data.get('email', '-')}
                        </a>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Telefon:</td>
                    <td style="padding: 8px 0;">
                        <a href="tel:{self.data.get('phone', '')}" style="color: #1976D2;">
                            {self.data.get('phone', '-')}
                        </a>
                    </td>
                </tr>
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Cím:</td>
                    <td style="padding: 8px 0;">{self.data.get('address')}</td>
                </tr>''' if self.data.get('address') else ''}
            </table>
        </div>
        """
        components.html(html_content, height=300)
    
    def _render_product_html(self):
        """Termék adatok HTML megjelenítése"""
        html_content = f"""
        <div style="padding: 0 16px;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Termék:</td>
                    <td style="padding: 8px 0;">{self.data.get('name', '-')}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Kategória:</td>
                    <td style="padding: 8px 0;">{self.data.get('category', {}).get('name', '-')}</td>
                </tr>
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Leírás:</td>
                    <td style="padding: 8px 0;">{self.data.get('description')}</td>
                </tr>''' if self.data.get('description') else ''}
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Mértékegység:</td>
                    <td style="padding: 8px 0;">{self.data.get('unit')}</td>
                </tr>''' if self.data.get('unit') else ''}
            </table>
        </div>
        """
        components.html(html_content, height=300)
    
    def _render_related_offer_html(self):
        """Kapcsolódó ajánlat HTML megjelenítése"""
        # Status indicator
        StatusIndicator(self.data.get('status', '-')).render()
        
        html_content = f"""
        <div style="padding: 0 16px;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Ajánlat #:</td>
                    <td style="padding: 8px 0;">{self.data.get('id', '-')}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Termék:</td>
                    <td style="padding: 8px 0;">{self.data.get('product_type', {}).get('name', '-')}</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Mennyiség:</td>
                    <td style="padding: 8px 0;">{format_quantity(self.data.get('quantity_in_kg', 0))} kg</td>
                </tr>
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Létrehozva:</td>
                    <td style="padding: 8px 0;">{format_date(self.data.get('created_at'))}</td>
                </tr>''' if self.data.get('created_at') else ''}
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Beszállítás:</td>
                    <td style="padding: 8px 0;">{format_date(self.data.get('delivery_date'))}</td>
                </tr>''' if self.data.get('delivery_date') else ''}
                {f'''<tr>
                    <td style="padding: 8px 0; font-weight: bold;">Ár:</td>
                    <td style="padding: 8px 0;">{format_price(self.data.get('price'))}</td>
                </tr>''' if self.data.get('price') else ''}
            </table>
        </div>
        """
        components.html(html_content, height=300)


class Timeline:
    """
    Idővonal megjelenítő komponens.
    
    Ajánlat fontosabb dátumainak idővonalas megjelenítése.
    """
    
    def __init__(self, events):
        """
        Inicializálja az idővonalat.
        
        Args:
            events (list): Lista az események adataival, ahol minden elem dict:
                           {"date": datetime, "label": str, "description": str (optional)}
        """
        self.events = [e for e in events if e["date"]]
        self.events.sort(key=lambda x: x["date"])
    
    def render(self):
        """FIXED: Idősor megjelenítése"""
        if not self.events:
            st.info("Nincs megjeleníthető esemény.")
            return
        
        try:
            # Események rendezése időrend szerint
            sorted_events = sorted(self.events, key=lambda x: x.get("date", datetime.min))
            
            for i, event in enumerate(sorted_events):
                # Dátum formázása
                date_str = format_datetime(event.get("date", ""))
                label = event.get("label", "")
                description = event.get("description", "")
                
                # Esemény megjelenítése
                col1, col2 = st.columns([1, 4])
                
                with col1:
                    if i == 0:
                        st.write("🟢")  # Első esemény
                    elif i == len(sorted_events) - 1:
                        st.write("🏁")  # Utolsó esemény
                    else:
                        st.write("📍")  # Köztes események
                
                with col2:
                    st.write(f"**{label}**")
                    st.caption(date_str)
                    if description:
                        st.caption(description)
                
                # Vonal az események között
                if i < len(sorted_events) - 1:
                    st.markdown("---")
                    
        except Exception as e:
            logger.error(f"Hiba az idősor megjelenítésekor: {str(e)}")
            st.error(f"Nem sikerült megjeleníteni az idősort: {str(e)}")


class ActivityLog:
    """
    Tevékenységi napló megjelenítő komponens.
    
    Státuszváltozásokat, csatolmányokat és egyéb tevékenységeket megjelenítő komponens.
    """
    
    def __init__(self, logs, log_type="status"):
        """
        Inicializálja a tevékenységi naplót.
        
        Args:
            logs (list): Lista a naplóbejegyzésekkel
            log_type (str, optional): A napló típusa ("status", "attachment", "audit"). Defaults to "status".
        """
        self.logs = logs if logs else []
        self.log_type = log_type
    
    def render(self):
        """Tevékenységi napló megjelenítése"""
        if not self.logs:
            st.info(f"Nincsenek {self._get_log_type_name()} bejegyzések.")
            return
        
        try:
            # Rendezzük a bejegyzéseket dátum szerint csökkenő sorrendbe
            sorted_logs = sorted(self.logs, key=lambda x: x.get("created_at", ""), reverse=True)
            
            # Különböző típusú naplók megjelenítése
            if self.log_type == "status":
                self._render_status_logs(sorted_logs)
            elif self.log_type == "attachment":
                self._render_attachment_logs(sorted_logs)
            elif self.log_type == "audit":
                self._render_audit_logs(sorted_logs)
            else:
                st.warning(f"Ismeretlen naplótípus: {self.log_type}")
        except Exception as e:
            logger.error(f"Hiba a tevékenységi napló megjelenítésekor: {str(e)}")
            st.error(f"Nem sikerült megjeleníteni a tevékenységi naplót: {str(e)}")
    
    def _get_log_type_name(self):
        """A naplótípus emberi olvasható neve"""
        type_names = {
            "status": "státuszváltozás",
            "attachment": "csatolmány",
            "audit": "változtatás"
        }
        return type_names.get(self.log_type, "napló")
    
    def _render_status_logs(self, logs):
        """Státusz változási napló megjelenítése"""
        for i, log in enumerate(logs):
            # Státuszváltozás vizuális megjelenítése
            cols = st.columns([1, 3])
            with cols[0]:
                # Dátum és felhasználó
                st.markdown(f"**{format_datetime(log.get('created_at', ''))}**")
                st.caption(f"{log.get('user', {}).get('contact_name', 'Rendszer')}")
            with cols[1]:
                # Státusz változás
                st.markdown(f"**{format_status(log.get('from_status', '-'))}** → **{format_status(log.get('to_status', '-'))}**")
                # Megjegyzés, ha van
                if log.get("note"):
                    st.markdown(f"*{log.get('note')}*")
            
            # Elválasztó vonal, kivéve az utolsó elemnél
            if i < len(logs) - 1:
                st.markdown("---")
    
    def _render_attachment_logs(self, logs):
        """Csatolmányok naplójának megjelenítése"""
        for i, attachment in enumerate(logs):
            # Csatolmány vizuális megjelenítése
            cols = st.columns([1, 3])
            with cols[0]:
                # Dátum és feltöltő
                st.markdown(f"**{format_datetime(attachment.get('created_at', ''))}**")
                st.caption(f"Feltöltötte: {attachment.get('user', {}).get('contact_name', 'Ismeretlen')}")
            with cols[1]:
                # Fájl neve és letöltés gomb
                st.markdown(f"**{attachment.get('filename', 'Csatolmány')}**")
                st.download_button(
                    label="Letöltés",
                    data=attachment.get("content", b""),
                    file_name=attachment.get("filename", "attachment.bin"),
                    mime=attachment.get("mime_type", "application/octet-stream"),
                    key=f"download_{i}_{attachment.get('id', uuid.uuid4())}"
                )
            
            # Elválasztó vonal, kivéve az utolsó elemnél
            if i < len(logs) - 1:
                st.markdown("---")
    
    def _render_audit_logs(self, logs):
        """Audit napló megjelenítése"""
        for i, log in enumerate(logs):
            # Audit bejegyzés vizuális megjelenítése
            cols = st.columns([1, 3])
            with cols[0]:
                # Dátum és felhasználó
                st.markdown(f"**{format_datetime(log.get('created_at', ''))}**")
                st.caption(f"{log.get('user', {}).get('contact_name', 'Rendszer')}")
            with cols[1]:
                # Művelet
                st.markdown(f"**{log.get('action', 'Művelet')}**")
                # Részletek, ha vannak
                if log.get("details"):
                    st.code(log.get("details"))
            
            # Elválasztó vonal, kivéve az utolsó elemnél
            if i < len(logs) - 1:
                st.markdown("---") 