"""
Confirmation Dialog Component - Visszaigazolás funkció
Visszaigazolási dialógus komponens mennyiség és ár megadásával
"""
import streamlit as st
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

def render_confirmation_dialog(offer: Dict[str, Any], offer_id: int) -> Tuple[bool, Optional[float], Optional[float]]:
    """
    Render confirmation dialog for CONFIRMED_BY_COMPANY status.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        
    Returns:
        Tuple of (confirmed, quantity, price)
        - confirmed: True if user confirmed the action
        - quantity: Confirmed quantity in kg
        - price: Confirmed price per kg
    """
    # Container to keep dialog in one place
    dialog_container = st.container()
    
    with dialog_container:
        # Create form to prevent reruns on input changes
        with st.form(key=f"confirmation_form_{offer_id}", clear_on_submit=False):
            # Custom styling
            st.markdown("""
            <style>
            .confirmation-container {
                background: #1a1a1a;
                border: 2px solid #0099e0;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 0.5rem 0;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Show current offer details in a compact way
            # Handle both old and new quantity field formats for backward compatibility
            original_quantity = float(offer.get('quantity_value', offer.get('quantity_in_kg', 0)))
            original_price = float(offer.get('price', 0))
            
            # Compact header with key info
            st.markdown(f"""
            <div style='background: rgba(0, 153, 224, 0.1); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;'>
                <strong>📋 Ajánlat #{offer_id}</strong> • 
                <strong>{offer.get('product_type', {}).get('name', 'N/A')}</strong> • 
                {offer.get('user', {}).get('contact_name', 'N/A')}
            </div>
            """, unsafe_allow_html=True)
            
            # Original values in metrics
            unit = offer.get('quantity_unit', 'kg')
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Eredeti mennyiség", f"{original_quantity:,.0f} {unit}")
            with col2:
                st.metric("Eredeti ár", f"{original_price:,.0f} Ft/{unit}")
            with col3:
                st.metric("Eredeti összérték", f"{(original_quantity * original_price):,.0f} Ft")
            
            st.markdown("---")
            
            # Input fields for confirmation
            st.markdown("#### 📝 Visszaigazolási adatok")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Quantity input with validation
                confirmed_quantity = st.number_input(
                    f"Visszaigazolt mennyiség ({unit}):",
                    min_value=0.0,
                    max_value=original_quantity * 1.1,  # Allow 10% over original
                    value=original_quantity,
                    step=1.0,
                    help="A visszaigazolt mennyiség nem lehet negatív"
                )
            
            with col2:
                # Price input with validation
                confirmed_price = st.number_input(
                    f"Visszaigazolt egységár (Ft/{unit}):",
                    min_value=0.0,
                    value=original_price,
                    step=1.0,
                    help="A visszaigazolt ár nem lehet negatív"
                )
            
            # Show changes in real-time
            if original_quantity > 0:
                quantity_percentage = (confirmed_quantity / original_quantity) * 100
            else:
                quantity_percentage = 0
                
            if original_price > 0:
                price_change = ((confirmed_price - original_price) / original_price) * 100
            else:
                price_change = 0
            
            # Show calculated values
            col1, col2 = st.columns(2)
            with col1:
                if quantity_percentage != 100:
                    if quantity_percentage < 100:
                        st.warning(f"⚠️ Mennyiség: {quantity_percentage:.1f}% az eredetiből")
                    else:
                        st.info(f"📈 Mennyiség: {quantity_percentage:.1f}% az eredetiből")
            
            with col2:
                if price_change != 0:
                    if price_change < 0:
                        st.warning(f"📉 Ár változás: {price_change:.1f}%")
                    else:
                        st.success(f"📈 Ár változás: +{price_change:.1f}%")
            
            # Total value comparison
            st.markdown("---")
            total_confirmed = confirmed_quantity * confirmed_price
            
            col1, col2, col3 = st.columns(3)
            with col2:
                st.metric(
                    "Visszaigazolt összérték",
                    f"{total_confirmed:,.0f} Ft",
                    delta=f"{total_confirmed - (original_quantity * original_price):,.0f} Ft"
                )
            
            # Optional note
            note = st.text_area(
                "Megjegyzés (opcionális):",
                placeholder="Pl. Minőségi kifogás, részleges teljesítés oka, stb.",
                height=80
            )
            
            # Form submit buttons
            st.markdown("---")
            col1, col2, col3 = st.columns([1, 1, 2])
            
            with col1:
                submitted = st.form_submit_button(
                    "✅ Visszaigazolás",
                    type="primary",
                    use_container_width=True
                )
            
            with col2:
                cancelled = st.form_submit_button(
                    "❌ Mégse",
                    use_container_width=True
                )
            
            with col3:
                st.info("💡 A visszaigazolás után a termelő elfogadhatja vagy elutasíthatja.")
    
    # Handle form submission - return values immediately
    if submitted:
        # Validation
        if confirmed_quantity <= 0:
            st.error("❌ A mennyiség nem lehet 0 vagy negatív!")
            return None, None, None
        elif confirmed_price <= 0:
            st.error("❌ Az ár nem lehet 0 vagy negatív!")
            return None, None, None
        else:
            # Store the note if provided
            if note:
                st.session_state[f"confirmation_note_{offer_id}"] = note
            return True, confirmed_quantity, confirmed_price
    
    elif cancelled:
        return False, None, None
    
    else:
        # Form not submitted yet
        return None, None, None


def show_confirmation_modal(offer_id: int) -> bool:
    """
    Show confirmation modal in a popup-like container.
    Returns True if the modal should be shown.
    """
    # Check if modal should be shown
    show_modal_key = f"show_confirmation_modal_{offer_id}"
    
    if st.session_state.get(show_modal_key, False):
        # Create a full-screen overlay effect
        st.markdown("""
        <style>
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 998;
            backdrop-filter: blur(4px);
        }
        </style>
        <div class="modal-overlay"></div>
        """, unsafe_allow_html=True)
        
        return True
    
    return False


def handle_confirmation_action(offer: Dict[str, Any], offer_id: int) -> Optional[Tuple[float, float, str]]:
    """
    Handle the confirmation action from Quick Action Bar.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        
    Returns:
        Tuple of (quantity, price, note) if confirmed, None otherwise
    """
    # Set the modal to show
    show_modal_key = f"show_confirmation_modal_{offer_id}"
    
    # If the confirmation button was clicked
    if st.session_state.get(f"quick_confirm_clicked_{offer_id}", False):
        st.session_state[show_modal_key] = True
        # Reset the click state
        del st.session_state[f"quick_confirm_clicked_{offer_id}"]
    
    # If modal is showing
    if st.session_state.get(show_modal_key, False):
        # Show the dialog
        confirmed, quantity, price = render_confirmation_dialog(offer, offer_id)
        
        if confirmed is not None:
            # Modal was closed (either confirmed or cancelled)
            del st.session_state[show_modal_key]
            
            if confirmed:
                # Get the note if any
                note = st.session_state.get(f"confirmation_note_{offer_id}", "")
                if f"confirmation_note_{offer_id}" in st.session_state:
                    del st.session_state[f"confirmation_note_{offer_id}"]
                
                logger.info(f"Offer {offer_id} confirmed with quantity={quantity}, price={price}")
                return quantity, price, note
            else:
                logger.info(f"Offer {offer_id} confirmation cancelled")
                return None
    
    return None


# Integration helper for offer_detail.py
def integrate_confirmation_dialog():
    """
    Helper function to integrate confirmation dialog into offer_detail.py
    
    Usage in offer_detail.py:
    ```python
    if action == "confirm":
        from .confirmation_dialog import handle_confirmation_action
        result = handle_confirmation_action(offer, offer_id)
        if result:
            quantity, price, note = result
            # Call the API to update status
            update_offer_status(offer_id, "CONFIRMED_BY_COMPANY", {
                "confirmed_quantity": quantity,
                "confirmed_price": price,
                "note": note
            })
    ```
    """
    pass