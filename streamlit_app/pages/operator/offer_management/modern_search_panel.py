"""
Modern kártyás keresőpanel implementáció.

Ez a modul egy viz<PERSON><PERSON><PERSON><PERSON>, modern kereső panel implementációt biztosít
az ajánlatok szűréséhez, amely a custom_css_framework.py-ban definiált
stílusokra épül.
"""
import streamlit as st
import logging
from datetime import datetime, timedelta
import uuid
from typing import Dict, List, Any, Optional, Tuple, Callable

# Az egyedi CSS keretrendszer importálása
try:
    from streamlit_app.pages.operator.offer_management.custom_css_framework import (
        inject_card_styles,
        inject_filter_styles,
        inject_base_styles,
        display_card,
        display_filter_panel,
        display_badges,
        COLORS,
        SHADOWS
    )
except ImportError:
    try:
        from pages.operator.offer_management.custom_css_framework import (
            inject_card_styles,
            inject_filter_styles,
            inject_base_styles,
            display_card,
            display_filter_panel,
            display_badges,
            COLORS,
            SHADOWS
        )
    except ImportError:
        try:
            from offer_management.custom_css_framework import (
                inject_card_styles,
                inject_filter_styles,
                inject_base_styles,
                display_card,
                display_filter_panel,
                display_badges,
                COLORS,
                SHADOWS
            )
        except ImportError:
            try:
                from custom_css_framework import (
                    inject_card_styles,
                    inject_filter_styles,
                    inject_base_styles,
                    display_card,
                    display_filter_panel,
                    display_badges,
                    COLORS,
                    SHADOWS
                )
            except ImportError:
                logging.error("Nem sikerült importálni a CSS keretrendszert")
                # Fallback minimális implementációk
                def inject_card_styles():
                    pass
                def inject_filter_styles():
                    pass
                def inject_base_styles():
                    pass
                def display_card(title, content_func, card_type="primary", collapsible=True, expanded=True, key=None):
                    st.subheader(title)
                    content_func()
                def display_filter_panel(title, content_func, icon="🔍", collapsible=True, expanded=True, key=None):
                    st.subheader(f"{icon} {title}")
                    content_func()
                def display_badges(badges, removable=True, key_prefix="badge"):
                    if badges:
                        st.write("Aktív szűrők: " + ", ".join([f"{k}: {v}" for k, v in badges.items() if v]))
                COLORS = {"primary": "#3584e4"}
                SHADOWS = {"medium": "0 3px 6px rgba(0, 0, 0, 0.15)"}

logger = logging.getLogger(__name__)

def inject_modern_search_styles():
    """
    Egyedi stílusok injektálása a modern keresőpanelhez.
    
    Ezek kiegészítik az alap CSS keretrendszert specifikus keresőpanel stílusokkal.
    """
    # Először az alap keretrendszer stílusokat injektáljuk
    inject_base_styles()
    inject_card_styles()
    inject_filter_styles()
    
    # Majd hozzáadjuk a keresőpanel specifikus stílusokat
    search_css = """
    <style>
    /* Modern keresőpanel specifikus stílusok */
    .search-panel {
        border-radius: var(--radius-lg);
        background-color: var(--color-white);
        box-shadow: var(--shadow-large);
        margin-bottom: var(--spacing-lg);
        overflow: hidden;
        transition: all var(--animation-normal);
    }
    
    .search-panel:hover {
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
        transform: translateY(-2px);
    }
    
    .search-panel-header {
        background: linear-gradient(to right, var(--color-primary), #4c9aff);
        color: white;
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .search-panel-title {
        font-size: var(--font-size-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .search-panel-icon {
        font-size: 1.5em;
        margin-right: var(--spacing-sm);
    }
    
    .search-panel-toggle {
        cursor: pointer;
        font-size: var(--font-size-xl);
        transition: transform var(--animation-normal);
    }
    
    .search-panel-toggle.collapsed {
        transform: rotate(-90deg);
    }
    
    .search-panel-body {
        padding: var(--spacing-lg);
        background-color: white;
    }
    
    .search-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
    
    .search-form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: var(--spacing-md);
        gap: var(--spacing-sm);
    }
    
    .search-button {
        background-color: var(--color-primary);
        color: white;
        border: none;
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        transition: all var(--animation-fast);
    }
    
    .search-button:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-medium);
    }
    
    .search-button.secondary {
        background-color: var(--color-white);
        color: var(--color-primary);
        border: 1px solid var(--color-primary);
    }
    
    .search-input-group {
        margin-bottom: var(--spacing-md);
    }
    
    .search-label {
        display: block;
        margin-bottom: var(--spacing-xs);
        font-weight: 500;
        font-size: var(--font-size-sm);
        color: var(--color-darkgray);
    }
    
    /* Predefined filter chip styles */
    .predefined-filters {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .predefined-filter {
        background-color: var(--color-lightgray);
        border: 1px solid var(--color-midgray);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all var(--animation-fast);
    }
    
    .predefined-filter:hover {
        background-color: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
    }
    
    .predefined-filter.active {
        background-color: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
    }
    
    /* Animation for panel expansion/collapse */
    @keyframes slide-down {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes slide-up {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-10px);
        }
    }
    
    .search-panel-body.expanded {
        animation: slide-down var(--animation-normal);
    }
    
    .search-panel-body.collapsed {
        animation: slide-up var(--animation-normal);
    }
    
    /* Responsive styles */
    @media (max-width: 768px) {
        .search-form {
            grid-template-columns: 1fr;
        }
        
        .search-panel-header {
            padding: var(--spacing-sm);
        }
        
        .search-panel-body {
            padding: var(--spacing-md);
        }
        
        .search-panel-title {
            font-size: var(--font-size-lg);
        }
        
        .search-form-actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .search-button {
            width: 100%;
            justify-content: center;
        }
    }
    </style>
    """
    
    try:
        st.markdown(search_css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a keresőpanel stílusok injektálásakor: {str(e)}")


def render_modern_search_panel(
    filter_components_func: Callable,
    title: str = "Ajánlatok szűrése", 
    icon: str = "🔍",
    collapsible: bool = True,
    expanded: bool = True,
    handle_search: Optional[Callable] = None,
    panel_id: Optional[str] = None
):
    """
    Modern, kártyás megjelenésű keresőpanel renderelése.
    
    Args:
        filter_components_func: Függvény, ami a szűrő komponenseket rendereli
        title: A panel címe
        icon: A panel ikonja
        collapsible: Legyen-e összecsukható
        expanded: Alapértelmezetten kinyitott-e
        handle_search: Keresőgomb eseménykezelő függvénye
        panel_id: Egyedi azonosító a panelhez (ha None, generálunk)
    """
    # Stílusok injektálása
    inject_modern_search_styles()
    
    # Panel egyedi azonosító
    if not panel_id:
        panel_id = f"search_{str(uuid.uuid4())[:8]}"
    
    # Panel állapot kezelése (nyitott/csukott)
    panel_state_key = f"{panel_id}_expanded"
    if panel_state_key not in st.session_state:
        st.session_state[panel_state_key] = expanded
    
    # Panel keret renderelése
    st.markdown(f"""
    <div class="search-panel" id="{panel_id}">
        <div class="search-panel-header" id="search-header-{panel_id}">
            <div class="search-panel-title">
                <span class="search-panel-icon">{icon}</span>
                {title}
            </div>
            {f'<div class="search-panel-toggle {="" collapsed"="" if="" not="" st.session_state[panel_state_key]="" else="" ""}">{"▼" if st.session_state[panel_state_key] else "▶"}</div>' if collapsible else ''}
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Panel tartalma, ha nyitva van
    if st.session_state[panel_state_key]:
        with st.container():
            # A szűrő komponensek renderelése
            filter_components_func()
            
            # Keresőgomb és keresés kezelése
            col1, col2 = st.columns([4, 1])
            with col2:
                if st.button("🔍 Keresés", key=f"search_button_{panel_id}", use_container_width=True, type="primary"):
                    if handle_search:
                        handle_search()
    
    # JavaScript a panel összecsukásához/kinyitásához
    if collapsible:
        js_code = f"""
        <script>
        document.addEventListener('DOMContentLoaded', () => {{
            const header = document.getElementById('search-header-{panel_id}');
            const toggleIcon = header.querySelector('.search-panel-toggle');
            
            if (header) {{
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {{
                    document.getElementById('toggle_search_panel_{panel_id}').click();
                }});
            }}
        }});
        </script>
        """
        st.markdown(js_code, unsafe_allow_html=True)
        
        # Rejtett gomb a panel állapotának váltásához
        if st.button("Toggle Panel", key=f"toggle_search_panel_{panel_id}", label_visibility="collapsed"):
            st.session_state[panel_state_key] = not st.session_state[panel_state_key]
            st.rerun()


def render_active_filter_badges(
    filters: Dict[str, Any],
    prefix: str = "",
    on_remove: Optional[Callable[[str], None]] = None
):
    """
    Aktív szűrők megjelenítése címkék formájában.
    
    Args:
        filters: A szűrők szótára {kulcs: érték} formában
        prefix: Előtag a session state kulcsokhoz
        on_remove: Függvény a címke törlésekor (paraméter: szűrő kulcsa)
    """
    # Először szűrjük a nem üres értékeket
    active_filters = {k: v for k, v in filters.items() if v is not None and v != ""}
    
    # Ha nincsenek aktív szűrők, nem jelenítünk meg semmit
    if not active_filters:
        return
    
    # Címkék HTML generálása
    badges_html = '<div class="filter-badges">'
    
    # Segédfüggvény a címke értékének formázásához
    def format_value(key, value):
        """Érték formázása a kulcs típusa alapján."""
        if key == "status" and value:
            try:
                from utils.formatting import format_status
                return format_status(value)
            except ImportError:
                return str(value)
        elif key in ["from_date", "to_date"] and value:
            if isinstance(value, datetime) or hasattr(value, 'strftime'):
                return value.strftime("%Y-%m-%d")
            return str(value)
        return str(value)
    
    # Segédfüggvény a címke nevének formázásához
    def format_key(key):
        """Kulcsnév felhasználóbarát formázása."""
        key_mappings = {
            "producer": "Termelő",
            "status": "Státusz",
            "from_date": "Kezdő dátum",
            "to_date": "Végső dátum",
            "product_type": "Terméktípus",
            "min_quantity": "Min mennyiség",
            "max_quantity": "Max mennyiség",
            "price": "Ár",
            "delivery_date": "Szállítási dátum",
            "created_at": "Létrehozva",
            "updated_at": "Frissítve",
        }
        return key_mappings.get(key, key.replace("_", " ").title())
    
    # Címkék generálása
    for idx, (key, value) in enumerate(active_filters.items()):
        badge_id = f"badge_{prefix}_{key}_{idx}"
        formatted_key = format_key(key)
        formatted_value = format_value(key, value)
        
        # A címke HTML-je
        badges_html += f"""
        <div class="filter-badge" id="{badge_id}">
            <span class="filter-badge-label">{formatted_key}:</span>
            <span class="filter-badge-value">{formatted_value}</span>
            <span class="filter-badge-clear" data-key="{key}">✕</span>
        </div>
        """
    
    badges_html += '</div>'
    
    # Címkék megjelenítése
    st.markdown(badges_html, unsafe_allow_html=True)
    
    # JavaScript a címkék eltávolításához
    if on_remove:
        js_code = f"""
        <script>
        document.addEventListener('DOMContentLoaded', () => {{
            const clearButtons = document.querySelectorAll('.filter-badge-clear');
            
            clearButtons.forEach(button => {{
                button.addEventListener('click', () => {{
                    const key = button.getAttribute('data-key');
                    if (key) {{
                        // Megfelelő rejtett gomb megtalálása és kattintás
                        document.getElementById('clear_filter_{prefix}_' + key).click();
                    }}
                }});
            }});
        }});
        </script>
        """
        st.markdown(js_code, unsafe_allow_html=True)
        
        # Rejtett gombok minden címkéhez
        for key in active_filters.keys():
            if st.button("Clear", key=f"clear_filter_{prefix}_{key}", label_visibility="collapsed"):
                if on_remove:
                    on_remove(key)
                st.rerun()


def render_predefined_filters(
    options: List[Dict[str, Any]],
    key_prefix: str = "predefined",
    on_select: Optional[Callable[[Dict[str, Any]], None]] = None
):
    """
    Előre definiált szűrőbeállítások megjelenítése választható címkékként.
    
    Args:
        options: Előre definiált szűrőbeállítások listája [{"label": "Label", "filters": {...}}]
        key_prefix: Előtag a session state kulcsokhoz
        on_select: Függvény, ami meghívódik a szűrőbeállítás kiválasztásakor
    """
    if not options:
        return
    
    # A kiválasztott szűrő nyilvántartása a session state-ben
    active_key = f"{key_prefix}_active"
    if active_key not in st.session_state:
        st.session_state[active_key] = None
    
    # Címkék HTML generálása
    filters_html = '<div class="predefined-filters">'
    
    for idx, option in enumerate(options):
        filter_id = f"{key_prefix}_{idx}"
        is_active = st.session_state[active_key] == idx
        active_class = "active" if is_active else ""
        
        filters_html += f"""
        <div class="predefined-filter {active_class}" id="{filter_id}">
            {option["label"]}
        </div>
        """
    
    filters_html += '</div>'
    
    # Címkék megjelenítése
    st.markdown(filters_html, unsafe_allow_html=True)
    
    # JavaScript a címkékre kattintáshoz
    js_code = f"""
    <script>
    document.addEventListener('DOMContentLoaded', () => {{
        const filterChips = document.querySelectorAll('.predefined-filter');
        
        filterChips.forEach((chip, idx) => {{
            chip.addEventListener('click', () => {{
                // Rejtett gomb aktiválása
                document.getElementById('select_predefined_' + idx).click();
            }});
        }});
    }});
    </script>
    """
    st.markdown(js_code, unsafe_allow_html=True)
    
    # Rejtett gombok minden előre definiált szűrőhöz
    for idx, option in enumerate(options):
        if st.button("Select", key=f"select_predefined_{idx}", label_visibility="collapsed"):
            st.session_state[active_key] = idx if st.session_state[active_key] != idx else None
            
            if on_select and st.session_state[active_key] is not None:
                on_select(options[idx]["filters"])
            
            st.rerun()


def render_status_filter(key_prefix: str = "status"):
    """
    Fejlett státusz szűrő létrehozása több kiválasztási lehetőséggel.
    
    Args:
        key_prefix: Előtag a session state kulcsokhoz
    
    Returns:
        list: A kiválasztott státuszok listája
    """
    # Session state kulcs
    state_key = f"{key_prefix}_filter"
    if state_key not in st.session_state:
        st.session_state[state_key] = []
    
    # Státusz opciók és leírások
    status_options = {
        "CREATED": "Létrehozva",
        "CONFIRMED_BY_COMPANY": "Megerősítve a cég által",
        "ACCEPTED_BY_USER": "Elfogadva a felhasználó által",
        "REJECTED_BY_USER": "Elutasítva a felhasználó által",
        "FINALIZED": "Véglegesítve"
    }
    
    # Státusz csoportok (opcionális)
    status_groups = {
        "Kezdeti státuszok": ["CREATED", "CONFIRMED_BY_COMPANY"],
        "Végső státuszok": ["ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
    }
    
    st.markdown('<div class="search-label">Státusz:</div>', unsafe_allow_html=True)
    
    # Opciók csoportok szerint vagy egyszerűen
    if status_groups:
        for group_name, group_statuses in status_groups.items():
            st.markdown(f"**{group_name}**")
            for status in group_statuses:
                status_label = status_options.get(status, status)
                if st.checkbox(status_label, key=f"{key_prefix}_{status}"):
                    if status not in st.session_state[state_key]:
                        st.session_state[state_key].append(status)
                else:
                    if status in st.session_state[state_key]:
                        st.session_state[state_key].remove(status)
    else:
        # Egyszerű lista, csoportosítás nélkül
        for status, label in status_options.items():
            if st.checkbox(label, key=f"{key_prefix}_{status}"):
                if status not in st.session_state[state_key]:
                    st.session_state[state_key].append(status)
            else:
                if status in st.session_state[state_key]:
                    st.session_state[state_key].remove(status)
    
    return st.session_state[state_key]


def render_date_range_filter(key_prefix: str = "date"):
    """
    Fejlett dátum tartomány szűrő csúszkával.
    
    Args:
        key_prefix: Előtag a session state kulcsokhoz
    
    Returns:
        tuple: (kezdő dátum, végső dátum) - datetime objektumok
    """
    # Session state kulcsok
    from_key = f"{key_prefix}_from"
    to_key = f"{key_prefix}_to"
    range_key = f"{key_prefix}_range"
    custom_key = f"{key_prefix}_custom"
    
    # Alapértelmezett dátumok beállítása, ha még nincsenek
    if from_key not in st.session_state:
        st.session_state[from_key] = datetime.now().date() - timedelta(days=30)
    
    if to_key not in st.session_state:
        st.session_state[to_key] = datetime.now().date()
    
    if range_key not in st.session_state:
        st.session_state[range_key] = "last30"
    
    if custom_key not in st.session_state:
        st.session_state[custom_key] = False
    
    st.markdown('<div class="search-label">Időszak:</div>', unsafe_allow_html=True)
    
    # Gyors időszak választók
    ranges = {
        "today": "Mai nap",
        "yesterday": "Tegnap",
        "last7": "Előző 7 nap",
        "last30": "Előző 30 nap",
        "thismonth": "Aktuális hónap",
        "lastmonth": "Előző hónap",
        "custom": "Egyedi időszak..."
    }
    
    selected_range = st.radio(
        "Válassz időszakot:",
        options=list(ranges.keys()),
        format_func=lambda x: ranges[x],
        key=range_key,
        label_visibility="collapsed"
    )
    
    # Dátumok beállítása a kiválasztott időszak alapján
    today = datetime.now().date()
    
    if selected_range == "today":
        from_date = today
        to_date = today
        st.session_state[custom_key] = False
    elif selected_range == "yesterday":
        from_date = today - timedelta(days=1)
        to_date = today - timedelta(days=1)
        st.session_state[custom_key] = False
    elif selected_range == "last7":
        from_date = today - timedelta(days=7)
        to_date = today
        st.session_state[custom_key] = False
    elif selected_range == "last30":
        from_date = today - timedelta(days=30)
        to_date = today
        st.session_state[custom_key] = False
    elif selected_range == "thismonth":
        from_date = today.replace(day=1)
        to_date = today
        st.session_state[custom_key] = False
    elif selected_range == "lastmonth":
        last_month = today.month - 1 if today.month > 1 else 12
        last_month_year = today.year if today.month > 1 else today.year - 1
        
        from_date = datetime(last_month_year, last_month, 1).date()
        if last_month == 12:
            to_date = datetime(last_month_year, 12, 31).date()
        else:
            to_date = datetime(last_month_year, last_month + 1, 1).date() - timedelta(days=1)
        
        st.session_state[custom_key] = False
    else:  # custom
        st.session_state[custom_key] = True
    
    # Egyedi dátumválasztók, ha az "Egyedi időszak" van kiválasztva
    if st.session_state[custom_key]:
        col1, col2 = st.columns(2)
        
        with col1:
            from_date = st.date_input("Kezdő dátum:", value=st.session_state[from_key], key=from_key)
        
        with col2:
            to_date = st.date_input("Végső dátum:", value=st.session_state[to_key], key=to_key)
        
        # Dátumok validálása
        if from_date > to_date:
            st.warning("A kezdő dátum nem lehet későbbi, mint a végső dátum!")
            to_date = from_date
    
    # Az aktuális dátumok elmentése a session state-be
    st.session_state[from_key] = from_date
    st.session_state[to_key] = to_date
    
    return from_date, to_date


def render_search_form(handle_search: Optional[Callable] = None, prefix: str = "search"):
    """
    Kereső űrlap renderelése az összes szűrési lehetőséggel.
    
    Args:
        handle_search: Keresőgomb eseménykezelő függvénye
        prefix: Előtag a session state kulcsokhoz
    
    Returns:
        dict: Az összes szűrőparaméter egy szótárban
    """
    # Session state kulcsok
    producer_key = f"{prefix}_producer"
    status_key = f"{prefix}_status"
    from_key = f"{prefix}_date_from"
    to_key = f"{prefix}_date_to"
    product_key = f"{prefix}_product"
    min_quantity_key = f"{prefix}_min_quantity"
    max_quantity_key = f"{prefix}_max_quantity"
    
    # Inicializálás, ha még nincs
    for key in [producer_key, product_key]:
        if key not in st.session_state:
            st.session_state[key] = None
    
    for key in [min_quantity_key, max_quantity_key]:
        if key not in st.session_state:
            st.session_state[key] = ""
    
    # Előre definiált szűrők
    predefined_filters = [
        {
            "label": "Mai ajánlatok",
            "filters": {
                from_key: datetime.now().date(),
                to_key: datetime.now().date()
            }
        },
        {
            "label": "Utolsó 7 nap",
            "filters": {
                from_key: datetime.now().date() - timedelta(days=7),
                to_key: datetime.now().date()
            }
        },
        {
            "label": "Függőben lévő ajánlatok",
            "filters": {
                status_key: ["CREATED", "CONFIRMED_BY_COMPANY"]
            }
        },
        {
            "label": "Lezárt ajánlatok",
            "filters": {
                status_key: ["ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            }
        }
    ]
    
    # Előre definiált szűrők megjelenítése
    st.markdown("#### Gyorsszűrők")
    render_predefined_filters(
        predefined_filters,
        key_prefix=f"{prefix}_predefined",
        on_select=lambda filters: st.session_state.update(filters)
    )
    
    st.markdown("---")
    
    # Szűrő komponensek rács elrendezésben
    st.markdown('<div class="search-form">', unsafe_allow_html=True)
    
    # Oszlopok és sorcsoportok definiálása a stílusos elrendezéshez
    col1, col2 = st.columns(2)
    
    # 1. oszlop - Termelő és státusz
    with col1:
        # Termelő választó
        try:
            from pages.operator.offer_management.ui_components import render_producer_filter
            selected_producer = render_producer_filter(prefix)
            st.session_state[producer_key] = selected_producer
        except ImportError:
            try:
                from ui_components import render_producer_filter
                selected_producer = render_producer_filter(prefix)
                st.session_state[producer_key] = selected_producer
            except ImportError:
                # Fallback
                st.text_input("Termelő azonosító:", key=producer_key)
        
        # Státusz szűrő
        selected_statuses = render_status_filter(key_prefix=f"{prefix}_status")
        status_value = selected_statuses[0] if selected_statuses else None
        st.session_state[status_key] = status_value
    
    # 2. oszlop - Dátumok
    with col2:
        # Dátum tartomány
        from_date, to_date = render_date_range_filter(key_prefix=f"{prefix}_date")
        st.session_state[from_key] = from_date
        st.session_state[to_key] = to_date
    
    # A formi lezárása
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Aktív szűrők megjelenítése
    active_filters = {
        "producer": st.session_state.get(producer_key),
        "status": st.session_state.get(status_key),
        "from_date": st.session_state.get(from_key),
        "to_date": st.session_state.get(to_key)
    }
    
    if any(v is not None and v != "" for v in active_filters.values()):
        st.markdown("#### Aktív szűrők")
        render_active_filter_badges(
            active_filters,
            prefix=prefix,
            on_remove=lambda key: setattr(st.session_state, f"{prefix}_{key}", None)
        )
    
    # Egyedi keresőgomb a szűrőkön kívül
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col3:
        search_clicked = st.button("🔍 Részletes keresés", key=f"{prefix}_search_button", use_container_width=True)
        if search_clicked and handle_search:
            handle_search()
    
    with col1:
        if st.button("🔄 Alaphelyzet", key=f"{prefix}_reset_button", use_container_width=True):
            # Minden szűrőparaméter törlése
            for key in [producer_key, status_key, product_key, min_quantity_key, max_quantity_key]:
                st.session_state[key] = None
            
            # Dátumok alaphelyzetbe állítása
            st.session_state[from_key] = datetime.now().date() - timedelta(days=30)
            st.session_state[to_key] = datetime.now().date()
            
            # Predefined szűrők törlése
            st.session_state[f"{prefix}_predefined_active"] = None
            
            st.rerun()
    
    # Az összes paraméter visszaadása
    params = {
        "producer_id": st.session_state.get(producer_key),
        "status": st.session_state.get(status_key),
        "from_date": st.session_state.get(from_key),
        "to_date": st.session_state.get(to_key),
        "product_type_id": st.session_state.get(product_key),
        "min_quantity": st.session_state.get(min_quantity_key),
        "max_quantity": st.session_state.get(max_quantity_key)
    }
    
    return params


def render_modern_search_panel_complete(
    title: str = "Ajánlatok szűrése", 
    icon: str = "🔍",
    collapsible: bool = True,
    expanded: bool = True,
    on_search: Optional[Callable[[Dict[str, Any]], None]] = None,
    panel_id: Optional[str] = None
):
    """
    Teljes, modern keresőpanel az összes szűrővel, komplett implementáció.
    
    Args:
        title: A panel címe
        icon: A panel ikonja
        collapsible: Összecsukható legyen-e
        expanded: Alapértelmezetten kinyitott-e
        on_search: Keresés eseménykezelő függvénye (paraméter: szűrő paraméterek)
        panel_id: Egyedi azonosító a panelhez
    
    Returns:
        dict: Az összes szűrőparaméter egy szótárban
    """
    # Stílusok injektálása
    inject_modern_search_styles()
    
    # Panel egyedi azonosító és állapot kezelése
    if not panel_id:
        panel_id = f"search_{str(uuid.uuid4())[:8]}"
    
    panel_state_key = f"{panel_id}_expanded"
    if panel_state_key not in st.session_state:
        st.session_state[panel_state_key] = expanded
    
    # Panel keret renderelése egyedi HTML-el
    st.markdown(f"""
    <div class="search-panel" id="{panel_id}">
        <div class="search-panel-header" id="search-header-{panel_id}">
            <div class="search-panel-title">
                <span class="search-panel-icon">{icon}</span>
                {title}
            </div>
            {f'<div class="search-panel-toggle {="" collapsed"="" if="" not="" st.session_state[panel_state_key]="" else="" ""}">{"▼" if st.session_state[panel_state_key] else "▶"}</div>' if collapsible else ''}
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Keresési paraméterek inicializálása
    search_params = {}
    
    # Panel tartalma, ha nyitva van
    if st.session_state[panel_state_key]:
        with st.container():
            # Keresőűrlap renderelése
            search_params = render_search_form(
                handle_search=lambda: on_search(search_params) if on_search else None,
                prefix=panel_id
            )
    
    # JavaScript a panel összecsukásához/kinyitásához
    if collapsible:
        js_code = f"""
        <script>
        document.addEventListener('DOMContentLoaded', () => {{
            const header = document.getElementById('search-header-{panel_id}');
            const toggleIcon = header.querySelector('.search-panel-toggle');
            
            if (header) {{
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {{
                    document.getElementById('toggle_search_panel_{panel_id}').click();
                }});
            }}
        }});
        </script>
        """
        st.markdown(js_code, unsafe_allow_html=True)
        
        # Rejtett gomb a panel állapotának váltásához
        if st.button("Toggle Panel", key=f"toggle_search_panel_{panel_id}", label_visibility="collapsed"):
            st.session_state[panel_state_key] = not st.session_state[panel_state_key]
            st.rerun()
    
    return search_params


# Példa használat, ha ezt a modult közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(page_title="Modern kereső panel", layout="wide")
    
    st.title("Modern kereső panel demó")
    
    # Példa kereséskezelő függvény
    def handle_search_demo(params):
        st.success(f"Keresés a következő paraméterekkel: {params}")
    
    # Teljes keresőpanel renderelése
    search_params = render_modern_search_panel_complete(
        title="Ajánlatok szűrése",
        icon="🔍",
        collapsible=True,
        expanded=True,
        on_search=handle_search_demo
    )
    
    # Szűrési paraméterek kijelzése
    with st.expander("Aktuális szűrési paraméterek"):
        st.json(search_params)