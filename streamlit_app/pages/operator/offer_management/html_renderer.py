"""
Universal HTML Renderer for Streamlit
Automatically uses st.html() when available, falls back to components.html()
"""
import streamlit as st
import streamlit.components.v1 as components
import logging

logger = logging.getLogger(__name__)

def render_html(html_content, height=None, key=None, scrolling=True):
    """
    Universal HTML rendering function that automatically chooses the best method.
    
    Args:
        html_content (str): The HTML content to render
        height (int, optional): Height for components.html fallback
        key (str, optional): Unique key for the component
        scrolling (bool): Enable scrolling for components.html
    
    Returns:
        bool: True if st.html was used, False if fallback was used
    """
    try:
        # Try to use st.html() if available
        if hasattr(st, 'html'):
            st.html(html_content)
            logger.debug("Successfully rendered with st.html()")
            return True
        else:
            # Fallback to components.html
            logger.debug("st.html() not available, using components.html()")
            if height is None:
                # Auto-calculate height based on content
                line_count = html_content.count('\n')
                char_count = len(html_content)
                # Estimate height: ~15px per line, min 300px, max 1000px
                estimated_height = max(300, min(1000, line_count * 15 + char_count // 100))
                height = estimated_height
            
            components.html(html_content, height=height, scrolling=scrolling)
            return False
            
    except AttributeError as e:
        # st.html might exist but not work properly
        logger.warning(f"st.html() failed with AttributeError: {e}, using fallback")
        if height is None:
            line_count = html_content.count('\n')
            char_count = len(html_content)
            height = max(300, min(1000, line_count * 15 + char_count // 100))
        
        components.html(html_content, height=height, scrolling=scrolling)
        return False
        
    except Exception as e:
        logger.error(f"HTML rendering error: {e}")
        # Last resort: try markdown
        st.markdown(html_content, unsafe_allow_html=True)
        return False


def render_css(css_content):
    """
    Render CSS styles. Always uses st.markdown for better compatibility.
    
    Args:
        css_content (str): CSS content including <style> tags
    """
    st.markdown(css_content, unsafe_allow_html=True)


def check_st_html_availability():
    """
    Check if st.html() is available and working.
    
    Returns:
        dict: Status information about st.html availability
    """
    info = {
        'has_attribute': hasattr(st, 'html'),
        'is_callable': False,
        'works': False,
        'streamlit_version': st.__version__
    }
    
    if info['has_attribute']:
        info['is_callable'] = callable(getattr(st, 'html', None))
        
        if info['is_callable']:
            try:
                # Test with a simple HTML
                test_html = "<div>Test</div>"
                st.html(test_html)
                info['works'] = True
            except:
                info['works'] = False
    
    return info