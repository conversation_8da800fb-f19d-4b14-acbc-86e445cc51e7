"""
Aszinkron adatbetöltés implementáció a Streamlit keretrendszerben.

Ez a modul aszinkron szerű adatbetöltési funkcionalitást nyújt a Streamlit környezetben,
amely alapvetően szinkron működést támogat.
"""
import streamlit as st
import logging
import time
import json
import threading
import uuid
from queue import Queue
from typing import Dict, Any, Optional, Tuple, Callable, List, Union
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import API client utilities with better fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
except ImportError:
    try:
        # Try regular app-relative import
        from utils.api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
    except ImportError:
        try:
            # Try direct local import
            from api_client import fetch_data_with_progress, safe_api_call, lazy_load_cache
        except ImportError:
            # Fallback implementations if import fails
            logging.warning("Could not import api_client utilities, using fallbacks")
            
            def fetch_data_with_progress(fetch_func, message="Loading data...", *args, **kwargs):
                """Basic data fetching with progress fallback"""
                with st.spinner(message):
                    return fetch_func(*args, **kwargs)
                    
            def safe_api_call(func, operation_name, *args, **kwargs):
                """Basic safe API call fallback"""
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    return False, f"Error during {operation_name}: {str(e)}"
                    
            def lazy_load_cache(key, loader_func, ttl=60):
                """Basic cache fallback"""
                if key not in st.session_state:
                    st.session_state[key] = loader_func()
                return st.session_state[key]


# Setup session state for tracking async operations
def init_async_state():
    """
    Inicializálja az aszinkron műveletek nyomon követéséhez szükséges állapotot.
    """
    if "async_operations" not in st.session_state:
        st.session_state.async_operations = {}


def async_data_load(
    load_function: Callable, 
    operation_key: str, 
    operation_name: str,
    *args, 
    **kwargs
) -> Tuple[bool, Any, str]:
    """
    Adatok aszinkron betöltése Streamlit környezetben.
    
    Ez a függvény egy pszeudo-aszinkron betöltési mechanizmust valósít meg, amely:
    1. Ellenőrzi, hogy egy adott művelet már folyamatban van-e
    2. Elindítja a betöltést, ha még nem kezdődött el
    3. Visszaadja a betöltési állapotot és az adatot, ha rendelkezésre áll
    
    Args:
        load_function (Callable): Az adatbetöltő függvény
        operation_key (str): A művelet egyedi azonosítója
        operation_name (str): A művelet olvasható neve
        *args: Továbbadandó pozíciós argumentumok
        **kwargs: Továbbadandó kulcsszavas argumentumok
        
    Returns:
        Tuple[bool, Any, str]: (kész_állapot, adat/None, állapot_üzenet)
    """
    # Inicializáljuk az aszinkron állapotot, ha még nem létezik
    init_async_state()
    
    operation_id = f"{operation_key}_{str(uuid.uuid4())[:8]}"
    
    # Ha nem létezik vagy már befejeződött a művelet
    if operation_key not in st.session_state.async_operations or \
       st.session_state.async_operations[operation_key].get("status") == "completed" or \
       st.session_state.async_operations[operation_key].get("status") == "error":
        
        # Új művelet létrehozása
        st.session_state.async_operations[operation_key] = {
            "id": operation_id,
            "status": "started",
            "start_time": datetime.now(),
            "data": None,
            "message": f"{operation_name} betöltése folyamatban...",
            "progress": 0
        }
        
        # Elvégezzük a betöltési műveletet
        success, result = load_function(*args, **kwargs)
        
        # Frissítjük az állapotot az eredmény alapján
        if success:
            st.session_state.async_operations[operation_key].update({
                "status": "completed",
                "data": result,
                "end_time": datetime.now(),
                "message": f"{operation_name} betöltése befejeződött.",
                "progress": 100
            })
            return True, result, f"{operation_name} betöltése befejeződött."
        else:
            st.session_state.async_operations[operation_key].update({
                "status": "error",
                "error_message": str(result),
                "end_time": datetime.now(),
                "message": f"Hiba a(z) {operation_name} betöltése során: {result}",
                "progress": 100
            })
            return True, None, f"Hiba a(z) {operation_name} betöltése során: {result}"
    
    # Ha már folyamatban van a művelet
    operation_data = st.session_state.async_operations[operation_key]
    
    if operation_data.get("status") == "started":
        # Visszaadjuk a folyamatban lévő művelet állapotát
        return False, None, operation_data.get("message", "Betöltés folyamatban...")
    
    elif operation_data.get("status") == "completed":
        # Visszaadjuk a befejezett művelet adatait
        return True, operation_data.get("data"), operation_data.get("message", "Betöltés befejeződött.")
    
    elif operation_data.get("status") == "error":
        # Visszaadjuk a hibás művelet adatait
        return True, None, operation_data.get("message", "Hiba történt a betöltés során.")
    
    # Váratlan állapot
    return False, None, "Ismeretlen állapot."


def batch_async_data_load(
    load_functions: Dict[str, Tuple[Callable, str, List, Dict]], 
    show_progress: bool = True
) -> Dict[str, Tuple[bool, Any, str]]:
    """
    Több aszinkron adatbetöltési művelet indítása és állapotuk lekérdezése.
    
    Args:
        load_functions (Dict[str, Tuple[Callable, str, List, Dict]]): 
            Kulcs: művelet azonosító
            Érték: (betöltő_függvény, művelet_neve, args, kwargs) 
        show_progress (bool): Jelenjen-e meg folyamatjelző
        
    Returns:
        Dict[str, Tuple[bool, Any, str]]: Művelet azonosító -> (kész_állapot, adat, állapot_üzenet)
    """
    # Inicializáljuk az aszinkron állapotot, ha még nem létezik
    init_async_state()
    
    results = {}
    all_completed = True
    
    # Progress bar container
    if show_progress:
        progress_placeholder = st.empty()
        progress_bars = {}
    
    # Minden függvényt végrehajtunk vagy lekérdezzük az állapotát
    for operation_key, (load_function, operation_name, args, kwargs) in load_functions.items():
        # Aszinkron művelet végrehajtása vagy állapotának lekérdezése
        is_completed, data, message = async_data_load(load_function, operation_key, operation_name, *args, **kwargs)
        
        # Eredmény mentése
        results[operation_key] = (is_completed, data, message)
        
        # Még nem fejeződött be minden művelet
        if not is_completed:
            all_completed = False
    
    # Folyamatjelzők megjelenítése
    if show_progress and not all_completed:
        progress_html = """
        <style>
            .async-progress-container {
                margin-bottom: 10px;
                background-color: #f0f2f6;
                border-radius: 5px;
                padding: 10px;
            }
            .async-progress-bar {
                height: 6px;
                background-color: #3366ff;
                border-radius: 3px;
                transition: width 0.5s;
            }
            .async-progress-label {
                font-size: 14px;
                color: #262730;
                margin-bottom: 5px;
            }
            .async-progress-status {
                font-size: 12px;
                color: #555;
                margin-top: 2px;
            }
        </style>
        <div style="padding: 10px; border-radius: 5px; background-color: #f9f9f9;">
        """
        
        for operation_key in load_functions.keys():
            operation_data = st.session_state.async_operations.get(operation_key, {})
            progress = operation_data.get("progress", 0)
            status = operation_data.get("status", "waiting")
            message = operation_data.get("message", "Várakozás...")
            operation_name = load_functions[operation_key][1]
            
            # Állapot szöveg meghatározása
            status_text = "Betöltés folyamatban..." if status == "started" else \
                          "Betöltés befejeződött." if status == "completed" else \
                          "Hiba történt." if status == "error" else "Várakozás..."
            
            # Dinamikus CSS osztály az állapot alapján
            status_class = "in-progress" if status == "started" else \
                          "completed" if status == "completed" else \
                          "error" if status == "error" else "waiting"
            
            # Progress bar HTML
            progress_html += f"""
            <div class="async-progress-container">
                <div class="async-progress-label">{operation_name}</div>
                <div class="async-progress-bar" style="width: {progress}%;"></div>
                <div class="async-progress-status {status_class}">{status_text} - {message}</div>
            </div>
            """
        
        progress_html += "</div>"
        progress_placeholder.markdown(progress_html, unsafe_allow_html=True)
    
    return results


def clear_async_operations():
    """
    Törli az összes aszinkron művelet állapotát a session state-ből.
    """
    if "async_operations" in st.session_state:
        st.session_state.async_operations = {}
        logger.info("All async operations cleared from session state")


def get_async_operation_status(operation_key: str) -> Tuple[str, Any, str]:
    """
    Visszaadja egy adott aszinkron művelet állapotát.
    
    Args:
        operation_key (str): A művelet egyedi azonosítója
        
    Returns:
        Tuple[str, Any, str]: (állapot, adat, üzenet)
    """
    if "async_operations" not in st.session_state or operation_key not in st.session_state.async_operations:
        return "unknown", None, "A művelet nem található."
    
    operation = st.session_state.async_operations[operation_key]
    return operation.get("status", "unknown"), operation.get("data"), operation.get("message", "")


def simulate_slow_operation(delay: float = 2.0) -> Tuple[bool, Any]:
    """
    Szimulál egy lassú műveletet tesztelési célokra.
    
    Args:
        delay (float): A késleltetés másodpercekben
        
    Returns:
        Tuple[bool, Any]: (success, result)
    """
    time.sleep(delay)
    return True, {"message": "Sikeres művelet", "timestamp": datetime.now().isoformat()}


# Példa az aszinkron betöltés használatára
# ----------------------------------------
# 
# # Függvény a betöltés indításához
# result = async_data_load(
#     lambda: get_offers({"status": "ACTIVE"}),
#     "active_offers", 
#     "Aktív ajánlatok"
# )
# 
# if result[0]:  # Ha kész
#     st.write("Ajánlatok betöltve:", result[1])
# else:  # Ha még folyamatban van
#     st.spinner(result[2])
# 
# # Több művelet indítása
# operations = {
#     "active_offers": (lambda: get_offers({"status": "ACTIVE"}), "Aktív ajánlatok", [], {}),
#     "new_offers": (lambda: get_offers({"status": "NEW"}), "Új ajánlatok", [], {}),
#     "producer_list": (get_producers, "Termelők", [], {})
# }
# 
# results = batch_async_data_load(operations)
# 
# # Eredmények feldolgozása
# for key, (is_completed, data, message) in results.items():
#     if is_completed:
#         st.write(f"{key}: {len(data) if data else 0} elem betöltve")
#     else:
#         st.spinner(message)