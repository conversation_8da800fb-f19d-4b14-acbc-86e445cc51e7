"""
State management hooks for React-like components.
Provides hooks that integrate with the global state manager.
"""
import streamlit as st
import logging
from typing import Dict, Any, Callable, Optional, Tuple, List
from datetime import date, datetime

try:
    from .state_manager import state_manager, ActionCreators, StateSelectors
except ImportError:
    try:
        from state_manager import state_manager, ActionCreators, StateSelectors
    except ImportError:
        # Fallback if state manager is not available
        logging.warning("State manager not available, using fallback implementations")
        
        class MockStateManager:
            def get_state(self): return {}
            def get_state_slice(self, path): return None
            def dispatch(self, action_type, payload=None, component_id=None): pass
            def subscribe(self, callback, selector=None): return "mock_id"
            def unsubscribe(self, subscription_id): pass
        
        class MockActionCreators:
            @staticmethod
            def set_producer_filter(producer_id): return ("MOCK", producer_id)
            @staticmethod
            def set_status_filter(status): return ("MOCK", status)
            @staticmethod
            def set_date_range_filter(from_date, to_date): return ("MOCK", None)
            @staticmethod
            def set_search_filter(search_term): return ("MOCK", search_term)
            @staticmethod
            def clear_all_filters(): return ("MOCK", None)
            @staticmethod
            def load_offers_start(): return ("MOCK", None)
            @staticmethod
            def load_offers_success(offers): return ("MOCK", offers)
            @staticmethod
            def load_offers_error(error): return ("MOCK", error)
            @staticmethod
            def select_offer(offer_id): return ("MOCK", offer_id)
            @staticmethod
            def set_view_mode(mode): return ("MOCK", mode)
            @staticmethod
            def set_mobile_mode(is_mobile): return ("MOCK", is_mobile)
            @staticmethod
            def show_notification(message, notification_type="info"): return ("MOCK", None)
            @staticmethod
            def set_theme(theme): return ("MOCK", theme)
        
        class MockStateSelectors:
            @staticmethod
            def get_filters(state): return {}
            @staticmethod
            def get_offers(state): return []
            @staticmethod
            def get_selected_offer_id(state): return None
            @staticmethod
            def get_selected_offer(state): return None
            @staticmethod
            def is_loading(state): return False
            @staticmethod
            def get_error(state): return None
            @staticmethod
            def get_view_mode(state): return "modern"
            @staticmethod
            def is_mobile(state): return False
            @staticmethod
            def get_notifications(state): return []
            @staticmethod
            def get_theme(state): return "light"
        
        state_manager = MockStateManager()
        ActionCreators = MockActionCreators()
        StateSelectors = MockStateSelectors()

logger = logging.getLogger(__name__)

def use_global_state(selector: Optional[Callable] = None, component_id: Optional[str] = None) -> Tuple[Any, Callable]:
    """
    Hook to access and subscribe to global state.
    
    Args:
        selector: Function to select specific state slice
        component_id: ID of the component using this hook
        
    Returns:
        Tuple of (current_state, dispatch_function)
    """
    # Get current state
    current_state = state_manager.get_state()
    
    # Apply selector if provided
    if selector:
        selected_state = selector(current_state)
    else:
        selected_state = current_state
    
    # Create dispatch function with component context
    def dispatch(action_type: str, payload: Any = None) -> None:
        state_manager.dispatch(action_type, payload, component_id)
    
    return selected_state, dispatch

def use_filters(component_id: Optional[str] = None) -> Tuple[Dict[str, Any], Dict[str, Callable]]:
    """
    Hook for managing filter state.
    
    Returns:
        Tuple of (current_filters, filter_actions)
    """
    # Get current filters
    filters, dispatch = use_global_state(StateSelectors.get_filters, component_id)
    
    # Create filter action creators
    filter_actions = {
        'set_producer': lambda producer_id: dispatch(*ActionCreators.set_producer_filter(producer_id)),
        'set_status': lambda status: dispatch(*ActionCreators.set_status_filter(status)),
        'set_date_range': lambda from_date, to_date: dispatch(*ActionCreators.set_date_range_filter(from_date, to_date)),
        'set_search': lambda search_term: dispatch(*ActionCreators.set_search_filter(search_term)),
        'clear_all': lambda: dispatch(*ActionCreators.clear_all_filters())
    }
    
    return filters, filter_actions

def use_offers(component_id: Optional[str] = None) -> Tuple[List[Dict[str, Any]], Dict[str, Callable]]:
    """
    Hook for managing offers data.
    
    Returns:
        Tuple of (current_offers, offer_actions)
    """
    # Get current offers
    offers, dispatch = use_global_state(StateSelectors.get_offers, component_id)
    
    # Create offer action creators
    offer_actions = {
        'load_start': lambda: dispatch(*ActionCreators.load_offers_start()),
        'load_success': lambda offers_data: dispatch(*ActionCreators.load_offers_success(offers_data)),
        'load_error': lambda error: dispatch(*ActionCreators.load_offers_error(error)),
        'select': lambda offer_id: dispatch(*ActionCreators.select_offer(offer_id))
    }
    
    return offers, offer_actions

def use_selected_offer(component_id: Optional[str] = None) -> Tuple[Optional[Dict[str, Any]], Callable]:
    """
    Hook for managing selected offer.
    
    Returns:
        Tuple of (selected_offer, select_offer_function)
    """
    # Get selected offer
    selected_offer, dispatch = use_global_state(StateSelectors.get_selected_offer, component_id)
    
    # Create select function
    def select_offer(offer_id: str) -> None:
        dispatch(*ActionCreators.select_offer(offer_id))
    
    return selected_offer, select_offer

def use_loading_state(component_id: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """
    Hook for accessing loading and error state.
    
    Returns:
        Tuple of (is_loading, error_message)
    """
    state, _ = use_global_state(component_id=component_id)
    
    is_loading = StateSelectors.is_loading(state)
    error = StateSelectors.get_error(state)
    
    return is_loading, error

def use_ui_state(component_id: Optional[str] = None) -> Tuple[Dict[str, Any], Dict[str, Callable]]:
    """
    Hook for managing UI state.
    
    Returns:
        Tuple of (ui_state, ui_actions)
    """
    # Get UI state
    state, dispatch = use_global_state(component_id=component_id)
    
    ui_state = {
        'view_mode': StateSelectors.get_view_mode(state),
        'is_mobile': StateSelectors.is_mobile(state),
        'theme': StateSelectors.get_theme(state),
        'notifications': StateSelectors.get_notifications(state)
    }
    
    # Create UI action creators
    ui_actions = {
        'set_view_mode': lambda mode: dispatch(*ActionCreators.set_view_mode(mode)),
        'set_mobile_mode': lambda is_mobile: dispatch(*ActionCreators.set_mobile_mode(is_mobile)),
        'set_theme': lambda theme: dispatch(*ActionCreators.set_theme(theme)),
        'show_notification': lambda message, msg_type="info": dispatch(*ActionCreators.show_notification(message, msg_type))
    }
    
    return ui_state, ui_actions

def use_notifications(component_id: Optional[str] = None) -> Tuple[List[Dict[str, Any]], Dict[str, Callable]]:
    """
    Hook for managing notifications.
    
    Returns:
        Tuple of (notifications, notification_actions)
    """
    # Get notifications
    notifications, dispatch = use_global_state(StateSelectors.get_notifications, component_id)
    
    # Create notification actions
    notification_actions = {
        'show': lambda message, msg_type="info": dispatch(*ActionCreators.show_notification(message, msg_type)),
        'clear': lambda notification_id: dispatch("CLEAR_NOTIFICATION", notification_id)
    }
    
    return notifications, notification_actions

def use_theme(component_id: Optional[str] = None) -> Tuple[str, Callable]:
    """
    Hook for managing theme state.
    
    Returns:
        Tuple of (current_theme, set_theme_function)
    """
    # Get current theme
    theme, dispatch = use_global_state(StateSelectors.get_theme, component_id)
    
    # Create theme setter
    def set_theme(new_theme: str) -> None:
        dispatch(*ActionCreators.set_theme(new_theme))
    
    return theme, set_theme

def use_statistics(component_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Hook for accessing computed statistics.
    
    Returns:
        Dictionary with computed statistics
    """
    # Get offers data
    offers, _ = use_global_state(StateSelectors.get_offers, component_id)
    
    # Compute statistics
    if not offers:
        return {
            'total_count': 0,
            'total_quantity': 0,
            'total_value': 0,
            'avg_price': 0
        }
    
    total_count = len(offers)
    total_quantity = sum(offer.get('quantity_in_kg', 0) for offer in offers)
    total_value = sum(offer.get('quantity_in_kg', 0) * offer.get('price', 0) for offer in offers)
    avg_price = sum(offer.get('price', 0) for offer in offers) / total_count if total_count > 0 else 0
    
    return {
        'total_count': total_count,
        'total_quantity': total_quantity,
        'total_value': total_value,
        'avg_price': avg_price
    }

def use_filtered_offers(component_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Hook for getting filtered offers based on current filter state.
    
    Returns:
        List of filtered offers
    """
    # Get offers and filters
    state, _ = use_global_state(component_id=component_id)
    offers = StateSelectors.get_offers(state)
    filters = StateSelectors.get_filters(state)
    
    # Apply filters
    filtered_offers = offers
    
    # Producer filter
    if filters.get('producer'):
        filtered_offers = [
            offer for offer in filtered_offers
            if offer.get('user', {}).get('name') == filters['producer']
        ]
    
    # Status filter
    if filters.get('status'):
        filtered_offers = [
            offer for offer in filtered_offers
            if offer.get('status') == filters['status']
        ]
    
    # Date range filter
    date_range = filters.get('date_range', {})
    if date_range.get('from_date') or date_range.get('to_date'):
        from_date = date_range.get('from_date')
        to_date = date_range.get('to_date')
        
        filtered_offers = [
            offer for offer in filtered_offers
            if _is_offer_in_date_range(offer, from_date, to_date)
        ]
    
    # Search filter
    search_term = filters.get('search', '').lower()
    if search_term:
        filtered_offers = [
            offer for offer in filtered_offers
            if _offer_matches_search(offer, search_term)
        ]
    
    return filtered_offers

def _is_offer_in_date_range(offer: Dict[str, Any], from_date: Optional[date], to_date: Optional[date]) -> bool:
    """Check if offer is within date range."""
    offer_date_str = offer.get('created_at')
    if not offer_date_str:
        return True
    
    try:
        # Parse offer date (assuming ISO format)
        if isinstance(offer_date_str, str):
            offer_date = datetime.fromisoformat(offer_date_str.replace('Z', '+00:00')).date()
        else:
            offer_date = offer_date_str
        
        if from_date and offer_date < from_date:
            return False
        if to_date and offer_date > to_date:
            return False
        
        return True
    except (ValueError, TypeError):
        return True

def _offer_matches_search(offer: Dict[str, Any], search_term: str) -> bool:
    """Check if offer matches search term."""
    searchable_fields = [
        offer.get('user', {}).get('name', ''),
        offer.get('product_type', {}).get('name', ''),
        offer.get('note', ''),
        str(offer.get('id', '')),
    ]
    
    search_text = ' '.join(searchable_fields).lower()
    return search_term in search_text

# Higher-order component decorator for automatic state management
def with_state_management(state_hooks: List[str]):
    """
    Decorator that automatically provides state hooks to components.
    
    Usage:
    @with_state_management(['filters', 'offers', 'ui'])
    @create_component
    def MyComponent(ctx):
        # ctx now has 'filters', 'offers', and 'ui' state
        filters = ctx['filters']
        offers = ctx['offers']
        ui = ctx['ui']
    """
    def decorator(component_func):
        def wrapper(ctx):
            # Add state hooks to context
            component_id = ctx.get('component_id', 'unknown')
            
            if 'filters' in state_hooks:
                filters, filter_actions = use_filters(component_id)
                ctx['filters'] = filters
                ctx['filter_actions'] = filter_actions
            
            if 'offers' in state_hooks:
                offers, offer_actions = use_offers(component_id)
                ctx['offers'] = offers
                ctx['offer_actions'] = offer_actions
            
            if 'ui' in state_hooks:
                ui_state, ui_actions = use_ui_state(component_id)
                ctx['ui'] = ui_state
                ctx['ui_actions'] = ui_actions
            
            if 'notifications' in state_hooks:
                notifications, notification_actions = use_notifications(component_id)
                ctx['notifications'] = notifications
                ctx['notification_actions'] = notification_actions
            
            if 'statistics' in state_hooks:
                ctx['statistics'] = use_statistics(component_id)
            
            if 'loading' in state_hooks:
                is_loading, error = use_loading_state(component_id)
                ctx['is_loading'] = is_loading
                ctx['error'] = error
            
            return component_func(ctx)
        return wrapper
    return decorator

# State debugging utilities
def use_state_debug(component_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Hook for debugging state in development.
    
    Returns:
        Debug information about current state
    """
    state, _ = use_global_state(component_id=component_id)
    
    return {
        'full_state': state,
        'state_size': len(str(state)),
        'offers_count': len(StateSelectors.get_offers(state)),
        'filters_active': len([v for v in StateSelectors.get_filters(state).values() if v]),
        'is_loading': StateSelectors.is_loading(state),
        'has_error': StateSelectors.get_error(state) is not None,
        'view_mode': StateSelectors.get_view_mode(state),
        'theme': StateSelectors.get_theme(state)
    }

# Export all hooks
__all__ = [
    'use_global_state',
    'use_filters',
    'use_offers',
    'use_selected_offer',
    'use_loading_state',
    'use_ui_state',
    'use_notifications',
    'use_theme',
    'use_statistics',
    'use_filtered_offers',
    'with_state_management',
    'use_state_debug'
]