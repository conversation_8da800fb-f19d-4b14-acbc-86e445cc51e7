"""
Enhanced UI components for saved filter management in the offer management page.
"""
import streamlit as st
import logging
import uuid
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
    from streamlit_app.api.saved_filters import (
        get_saved_filters as api_get_saved_filters,
        get_saved_filter as api_get_saved_filter,
        create_saved_filter as api_create_saved_filter,
        update_saved_filter as api_update_saved_filter,
        delete_saved_filter as api_delete_saved_filter,
        set_default_filter as api_set_default_filter
    )
    from streamlit_app.pages.operator.offer_management.filter_persistence import (
        extract_current_filters,
        apply_saved_filter,
        check_and_apply_default_filter
    )
    from streamlit_app.pages.operator.offer_management.user_preferences import (
        get_default_filter_for_user,
        set_default_filter_for_user,
        clear_default_filter_for_user
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
        from pages.operator.offer_management.filter_persistence import (
            extract_current_filters,
            apply_saved_filter,
            check_and_apply_default_filter,
            get_saved_filters as api_get_saved_filters,
            create_saved_filter as api_create_saved_filter,
            update_saved_filter as api_update_saved_filter,
            delete_saved_filter as api_delete_saved_filter,
            set_default_filter as api_set_default_filter
        )
        from pages.operator.offer_management.user_preferences import (
            get_default_filter_for_user,
            set_default_filter_for_user,
            clear_default_filter_for_user
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            from filter_persistence import (
                extract_current_filters,
                apply_saved_filter,
                check_and_apply_default_filter,
                get_saved_filters as api_get_saved_filters,
                create_saved_filter as api_create_saved_filter,
                update_saved_filter as api_update_saved_filter,
                delete_saved_filter as api_delete_saved_filter,
                set_default_filter as api_set_default_filter
            )
            try:
                from user_preferences import (
                    get_default_filter_for_user,
                    set_default_filter_for_user,
                    clear_default_filter_for_user
                )
            except ImportError:
                # Minimal implementations if user_preferences import fails
                def get_default_filter_for_user(user_id=None):
                    return None, None
                    
                def set_default_filter_for_user(filter_id, filter_data, user_id=None):
                    logger.warning("Using minimal implementation of set_default_filter_for_user")
                    return False, "Not implemented in minimal mode"
                    
                def clear_default_filter_for_user(user_id=None):
                    logger.warning("Using minimal implementation of clear_default_filter_for_user")
                    return False, "Not implemented in minimal mode"
                
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: "{:,.0f} Ft".format(x) if x else "-"
            format_quantity = lambda x: "{:,.2f}".format(x) if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in saved_filter_ui.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
            
            # Minimal implementations of filter persistence functions
            def extract_current_filters():
                return {}
                
            def apply_saved_filter(filter_data):
                pass
                
            def check_and_apply_default_filter():
                return False
                
            # Minimal API function implementations
            def api_get_saved_filters(filter_type=None):
                return []
                
            def api_create_saved_filter(name, description, filter_type, filter_data, is_default=False):
                return None
                
            def api_update_saved_filter(filter_id, **data):
                return None
                
            def api_delete_saved_filter(filter_id):
                return False
                
            def api_set_default_filter(filter_id):
                return None
                
            def get_saved_filters(filter_type=None):
                return [], None
                
            def create_saved_filter(name, filter_data, description=None, filter_type="offer", is_default=False):
                return None, "API not available"
                
            def update_saved_filter(filter_id, data):
                return None, "API not available"
                
            def delete_saved_filter(filter_id):
                return False, "API not available"
                
            def set_default_filter(filter_id):
                return None, "API not available"
                
            def get_default_filter_for_user(user_id=None):
                return None, None
                
            def set_default_filter_for_user(filter_id, filter_data, user_id=None):
                return False, "Not implemented in minimal mode"
                
            def clear_default_filter_for_user(user_id=None):
                return False, "Not implemented in minimal mode"

# Logger setup
logger = logging.getLogger(__name__)

# API wrapper functions to adapt the new backend API to the existing UI code
def get_saved_filters(filter_type=None):
    """Wrapper for new API - returns (filters, error) tuple"""
    try:
        result = api_get_saved_filters(filter_type)
        # Handle different return formats
        if isinstance(result, tuple):
            # If it already returns a tuple, use it directly
            if len(result) == 2:
                return result
            else:
                # If tuple has wrong length, extract what we can
                filters = result[0] if len(result) > 0 else []
                return filters, None
        else:
            # If it returns just the filters list
            return result if isinstance(result, list) else [], None
    except Exception as e:
        logger.error(f"Error getting saved filters: {e}")
        return [], str(e)

def create_saved_filter(name, filter_data, description=None, filter_type="offer", is_default=False):
    """Wrapper for new API - returns (result, error) tuple"""
    try:
        result = api_create_saved_filter(name, description, filter_type, filter_data, is_default)
        if result:
            return result, None
        else:
            return None, "Failed to create filter"
    except Exception as e:
        logger.error(f"Error creating saved filter: {e}")
        return None, str(e)

def update_saved_filter(filter_id, data):
    """Wrapper for new API - returns (result, error) tuple"""
    try:
        result = api_update_saved_filter(filter_id, **data)
        if result:
            return result, None
        else:
            return None, "Failed to update filter"
    except Exception as e:
        logger.error(f"Error updating saved filter: {e}")
        return None, str(e)

def delete_saved_filter(filter_id):
    """Wrapper for new API - returns (success, error) tuple"""
    try:
        success = api_delete_saved_filter(filter_id)
        return success, None if success else "Failed to delete filter"
    except Exception as e:
        logger.error(f"Error deleting saved filter: {e}")
        return False, str(e)

def set_default_filter(filter_id):
    """Wrapper for new API - returns (result, error) tuple"""
    try:
        result = api_set_default_filter(filter_id)
        if result:
            return result, None
        else:
            return None, "Failed to set default filter"
    except Exception as e:
        logger.error(f"Error setting default filter: {e}")
        return None, str(e)

def generate_unique_key(base_name: str, suffix: str = None) -> str:
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name: Base name for the key
        suffix: Optional suffix to add
        
    Returns:
        Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def inject_filter_quick_access_css():
    """Inject custom CSS for filter quick access buttons."""
    st.markdown("""
    <style>
    .filter-chip {
        display: inline-block;
        padding: 5px 12px;
        margin: 0 6px 6px 0;
        border-radius: 16px;
        background-color: #f0f2f6;
        color: #31333F;
        font-size: 0.85em;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid #e0e0e0;
        white-space: nowrap;
    }
    .filter-chip:hover {
        background-color: #e1e5eb;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .filter-chip.default {
        background-color: #e3f2fd;
        border-color: #90caf9;
        font-weight: 500;
    }
    .filter-chip.default:hover {
        background-color: #bbdefb;
    }
    .filter-chip-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin: 10px 0;
        padding: 5px 0;
        overflow-x: auto;
        max-width: 100%;
    }
    .filter-manage-btn {
        background-color: #f8f9fa;
        border: 1px dashed #ccc;
        color: #666;
        font-size: 0.85em;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 16px;
        margin-left: 6px;
    }
    .filter-manage-btn:hover {
        background-color: #eff1f3;
        color: #333;
    }
    
    /* Modal styles */
    .filter-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1000;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .filter-modal-content {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        width: 80%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .filter-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .filter-modal-title {
        font-size: 1.2em;
        font-weight: 500;
    }
    .filter-modal-close {
        cursor: pointer;
        font-size: 1.5em;
        color: #666;
    }
    </style>
    """, unsafe_allow_html=True)

def inject_modal_script():
    """Inject JavaScript for handling modal interactions."""
    st.markdown("""
    <script>
    // Function to show a filter management modal
    function showFilterModal() {
        document.getElementById('filter-management-modal').style.display = 'flex';
    }
    
    // Function to hide the modal
    function hideFilterModal() {
        document.getElementById('filter-management-modal').style.display = 'none';
    }
    
    // Function to load a filter by ID
    function loadFilter(filterId) {
        const input = document.getElementById('load-filter-' + filterId);
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    
    // Function to set global default filter
    function setDefaultFilter(filterId) {
        const input = document.getElementById('default-filter-' + filterId);
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    
    // Function to set user default filter
    function setUserDefaultFilter(filterId) {
        const input = document.getElementById('user-default-filter-' + filterId);
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    
    // Function to clear user default filter
    function clearUserDefaultFilter() {
        const input = document.getElementById('clear-user-default-filter');
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    
    // Function to delete a filter
    function deleteFilter(filterId) {
        if (confirm('Biztos, hogy törölni szeretné ezt a szűrőt?')) {
            const input = document.getElementById('delete-filter-' + filterId);
            if (input) {
                input.value = 'true';
                input.dispatchEvent(new Event('change'));
            }
        }
    }
    
    // Close modal when clicking outside content
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('filter-management-modal');
        const modalContent = document.getElementById('filter-modal-content');
        
        if (modal && event.target === modal) {
            hideFilterModal();
        }
    });
    </script>
    """, unsafe_allow_html=True)

def render_filter_quick_access_bar(on_reload: Optional[Callable] = None):
    """
    Render a quick access bar with saved filters and management button.
    
    Args:
        on_reload: Optional callback to trigger when a filter is applied
    """
    # Get saved filters
    filters, error = get_saved_filters(filter_type="offer")
    
    if error:
        # Just don't show the quick access bar if there's an error
        logger.warning(f"Error loading saved filters: {error}")
        return
    
    # Sort filters - default first, then alphabetically
    if filters and isinstance(filters, list):
        filters.sort(key=lambda x: (not x.get("is_default", False), x.get("name", "")))
    elif filters and not isinstance(filters, list):
        logger.warning(f"Expected filters to be a list, got {type(filters)}: {filters}")
        filters = []
    
    # Create JavaScript to handle filter clicks
    st.markdown("""
    <script>
    function quickLoadFilter(filterId) {
        const input = document.getElementById('quick-load-' + filterId);
        if (input) {
            input.value = 'true';
            input.dispatchEvent(new Event('change'));
        }
    }
    </script>
    """, unsafe_allow_html=True)
    
    # Create filter chips container
    filter_html = """
    <div class="filter-chip-container">
        <span style="margin-right: 8px; color: #666; font-size: 0.9em;">Mentett szűrők:</span>
    """
    
    # Add filter chips
    if filters:
        for filter_item in filters:
            # Determine if it's a default filter
            is_default = filter_item.get("is_default", False)
            default_class = "default" if is_default else ""
            default_icon = "★ " if is_default else ""
            
            filter_html += f"""
            <div class="filter-chip {default_class}" onclick="quickLoadFilter('{filter_item['id']}')">
                {default_icon}{filter_item['name']}
            </div>
            """
    else:
        filter_html += """
        <span style="color: #999; font-style: italic; font-size: 0.9em;">Nincsenek mentett szűrők</span>
        """
    
    # Add manage button
    filter_html += """
    <div class="filter-manage-btn" onclick="showFilterModal()">
        ⚙️ Kezelés
    </div>
    """
    
    # Close container
    filter_html += """
    </div>
    """
    
    # Render the HTML
    st.markdown(filter_html, unsafe_allow_html=True)
    
    # Add hidden inputs for handling clicks
    if filters:
        for filter_item in filters:
            filter_id = filter_item["id"]
            # Check if this filter was clicked
            if st.text_input(f"Quick load filter {filter_id}", value="false", key=f"quick-load-{filter_id}", label_visibility="collapsed") == "true":
                try:
                    # Apply the filter
                    filter_data = filter_item.get("filter_data", {})
                    apply_saved_filter(filter_data)
                    show_inline_success(f"'{filter_item['name']}' szűrő betöltve!")
                    
                    # Call reload callback if provided
                    if on_reload:
                        on_reload()
                    
                    # Force refresh
                    st.rerun()
                except Exception as e:
                    logger.error(f"Error applying filter: {e}")
                    show_inline_error(f"Hiba a szűrő betöltése során: {e}")

def render_filter_management_modal():
    # Get saved filters
    filters, error = get_saved_filters(filter_type="offer")
    
    if error:
        logger.warning(f"Error loading saved filters: {error}")
        filters = []
    
    # Create modal HTML - but this time we'll exclude the form section
    modal_html = """
    <div id="filter-management-modal" class="filter-modal" style="display: none;">
        <div id="filter-modal-content" class="filter-modal-content">
            <div class="filter-modal-header">
                <div class="filter-modal-title">Mentett szűrők kezelése</div>
                <div class="filter-modal-close" onclick="hideFilterModal()">×</div>
            </div>
            <div class="filter-modal-body">
    """
    
    # Add saved filters list header with HTML form
    modal_html += """
    <div id="save-filter-container">
        <h4 style="margin-bottom: 10px;">Jelenlegi szűrő mentése</h4>
        <form onsubmit="saveCurrentFilter(); return false;" style="margin-bottom: 20px;">
            <div style="margin-bottom: 10px;">
                <label for="new-filter-name" style="display: block; margin-bottom: 5px;">Szűrő neve:</label>
                <input type="text" id="new-filter-name" style="width: 100%; padding: 5px; border: 1px solid #ccc; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 10px;">
                <label style="display: flex; align-items: center;">
                    <input type="checkbox" id="make-default" style="margin-right: 5px;">
                    Beállítás alapértelmezettként
                </label>
            </div>
            <button type="submit" style="background-color: #1976D2; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                Mentés
            </button>
        </form>
    </div>
    <h4 style="margin-bottom: 10px;">Mentett szűrők</h4>
    """
    
    if not filters:
        modal_html += """
        <div style="padding: 15px; background-color: #f5f5f5; border-radius: 4px; text-align: center; color: #666;">
            Nincsenek mentett szűrők
        </div>
        """
    else:
        modal_html += """
        <div style="border: 1px solid #eee; border-radius: 4px; overflow: hidden;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="padding: 10px; text-align: left;">Név</th>
                        <th style="padding: 10px; text-align: left;">Létrehozva</th>
                        <th style="padding: 10px; text-align: center;">Alapértelmezett</th>
                        <th style="padding: 10px; text-align: right;">Műveletek</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for filter_item in filters:
            is_default = filter_item.get("is_default", False)
            default_mark = "✓" if is_default else ""
            created_date = filter_item.get("created_at", "")
            if isinstance(created_date, str):
                # Try to parse ISO date
                try:
                    created_date = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    pass
            created_str = format_datetime(created_date)
            
            # Create the default button HTML outside the f-string
            default_button = ""
            if not is_default:
                default_button = f'<button onclick="setDefaultFilter(\'{filter_item["id"]}\')" style="background: none; border: none; color: #FF9800; cursor: pointer; margin-right: 5px;">Globális alap</button>'
            
            modal_html += f"""
            <tr style="border-top: 1px solid #eee;">
                <td style="padding: 12px;">{filter_item['name']}</td>
                <td style="padding: 12px; color: #666; font-size: 0.9em;">{created_str}</td>
                <td style="padding: 12px; text-align: center; color: #1976D2; font-weight: 500;">{default_mark}</td>
                <td style="padding: 12px; text-align: right;">
                    <button onclick="loadFilter('{filter_item['id']}')" 
                            style="background: none; border: none; color: #1976D2; cursor: pointer; margin-right: 5px;">
                        Betöltés
                    </button>
                    {default_button}
                    <button onclick="setUserDefaultFilter('{filter_item['id']}')" 
                            style="background: none; border: none; color: #4CAF50; cursor: pointer; margin-right: 5px;">
                        Saját alap
                    </button>
                    <button onclick="deleteFilter('{filter_item['id']}')" 
                            style="background: none; border: none; color: #E53935; cursor: pointer;">
                        Törlés
                    </button>
                </td>
            </tr>
            """
        
        modal_html += """
                </tbody>
            </table>
        </div>
        """
    
    # Close modal HTML
    modal_html += """
            </div>
        </div>
    </div>
    """
    
    # Add JavaScript for new functions
    modal_html += """
    <script>
    function saveCurrentFilter() {
        const nameInput = document.getElementById('new-filter-name');
        const makeDefaultInput = document.getElementById('make-default');
        
        if (!nameInput || !nameInput.value.trim()) {
            alert('Kérjük, adjon meg egy nevet a szűrőnek!');
            return;
        }
        
        // Set hidden form inputs for Streamlit to detect
        document.getElementById('new-filter-name-input').value = nameInput.value.trim();
        document.getElementById('make-default-input').value = makeDefaultInput.checked;
        
        // Trigger the form submission
        document.getElementById('save-filter-button').click();
        
        // Close the modal
        hideFilterModal();
    }
    </script>
    """
    
    # Render the modal HTML
    st.markdown(modal_html, unsafe_allow_html=True)
    
    # Now create a proper Streamlit form that will appear inside the modal
    with st.form("modal_save_filter_form", clear_on_submit=True):
        st.markdown("<h4>Jelenlegi szűrő mentése</h4>", unsafe_allow_html=True)
        filter_name = st.text_input("Szűrő neve", key="modal-filter-name-input")
        make_default = st.checkbox("Beállítás alapértelmezettként", key="modal-make-default-input")
        # Add a proper submit button
        submit_button = st.form_submit_button("Mentés")
        
        if submit_button and filter_name:
            try:
                current_filters = extract_current_filters()
                result, error = create_saved_filter(
                    name=filter_name,
                    description="",
                    filter_data=current_filters,
                    filter_type="offer",
                    is_default=make_default
                )
                
                if error:
                    show_inline_error(f"Hiba a szűrő mentése során: {error}")
                else:
                    show_inline_success(f"'{filter_name}' szűrő sikeresen elmentve!")
                    st.rerun()
            except Exception as e:
                logger.error(f"Error saving filter: {e}")
                show_inline_error(f"Hiba a szűrő mentése során: {e}")
    
    # Add hidden inputs for handling filter actions
    if filters:
        for filter_item in filters:
            filter_id = filter_item["id"]
            
            # Load filter action
            if st.text_input(f"Load filter {filter_id}", value="false", key=f"load-filter-{filter_id}", label_visibility="collapsed") == "true":
                try:
                    # Apply the filter
                    filter_data = filter_item.get("filter_data", {})
                    apply_saved_filter(filter_data)
                    show_inline_success(f"'{filter_item['name']}' szűrő betöltve!")
                    st.rerun()
                except Exception as e:
                    logger.error(f"Error loading filter: {e}")
                    show_inline_error(f"Hiba a szűrő betöltése során: {e}")
            
            # Set global default filter action
            if st.text_input(f"Set default filter {filter_id}", value="false", key=f"default-filter-{filter_id}", label_visibility="collapsed") == "true":
                result, error = set_default_filter(filter_id)
                if error:
                    show_inline_error(f"Hiba a globális alapértelmezett beállítása során: {error}")
                else:
                    show_inline_success(f"'{filter_item['name']}' beállítva globális alapértelmezettként!")
                    st.rerun()
            
            # Set user default filter action
            if st.text_input(f"Set user default filter {filter_id}", value="false", key=f"user-default-filter-{filter_id}", label_visibility="collapsed") == "true":
                filter_data = filter_item.get("filter_data", {})
                success, error = set_default_filter_for_user(filter_id, filter_data)
                if error:
                    show_inline_error(f"Hiba a személyes alapértelmezett beállítása során: {error}")
                else:
                    show_inline_success(f"'{filter_item['name']}' beállítva személyes alapértelmezettként!")
                    st.rerun()
            
            # Delete filter action
            if st.text_input(f"Delete filter {filter_id}", value="false", key=f"delete-filter-{filter_id}", label_visibility="collapsed") == "true":
                success, error = delete_saved_filter(filter_id)
                if error:
                    show_inline_error(f"Hiba a törlés során: {error}")
                else:
                    # Check if this was the user's default, and if so, clear it
                    user_default, _ = get_default_filter_for_user()
                    if user_default and user_default.get("id") == filter_id:
                        clear_default_filter_for_user()
                        
                    show_inline_success(f"'{filter_item['name']}' sikeresen törölve!")
                    st.rerun()
        
        # Add hidden input for clearing user default filter
        if st.text_input("Clear user default filter", value="false", key="clear-user-default-filter", label_visibility="collapsed") == "true":
            success, error = clear_default_filter_for_user()
            if error:
                show_inline_error(f"Hiba a személyes alapértelmezett törlése során: {error}")
            else:
                show_inline_success("Személyes alapértelmezett szűrő sikeresen törölve!")
                st.rerun()

def render_save_filter_button():
    """
    Render a button to save the current filter.
    """
    if st.button("💾 Jelenlegi szűrő mentése", key=generate_unique_key("save_filter_btn")):
        # Show a form to enter filter details
        with st.form("save_filter_form", clear_on_submit=True):
            st.markdown("### Szűrő mentése")
            filter_name = st.text_input("Szűrő neve", key=generate_unique_key("filter_name"))
            filter_description = st.text_area("Leírás (opcionális)", key=generate_unique_key("filter_desc"))
            make_default = st.checkbox("Beállítás alapértelmezettként", key=generate_unique_key("make_default"))
            
            save_submitted = st.form_submit_button("Mentés")
            
            if save_submitted:
                if not filter_name:
                    show_inline_error("A szűrő nevének megadása kötelező!")
                else:
                    try:
                        # Extract current filter settings
                        current_filters = extract_current_filters()
                        
                        # Save the filter
                        result, error = create_saved_filter(
                            name=filter_name,
                            description=filter_description,
                            filter_data=current_filters,
                            filter_type="offer",
                            is_default=make_default
                        )
                        
                        if error:
                            show_inline_error(f"Hiba a szűrő mentése során: {error}")
                        else:
                            show_inline_success(f"'{filter_name}' szűrő sikeresen elmentve!")
                            st.rerun()
                    except Exception as e:
                        logger.error(f"Error saving filter: {e}")
                        show_inline_error(f"Hiba a szűrő mentése során: {e}")

def render_saved_filters_management(on_reload: Optional[Callable] = None):
    """
    Render the entire saved filters management UI components.
    
    Args:
        on_reload: Optional callback to trigger when a filter is applied
    """
    # Inject required CSS and JavaScript
    inject_filter_quick_access_css()
    inject_modal_script()
    
    # Render quick access bar
    render_filter_quick_access_bar(on_reload)
    
    # Render modal for filter management
    render_filter_management_modal()
    
    # Attempt to apply default filter if no filters are set
    if not st.session_state.get("_default_filter_checked", False):
        st.session_state["_default_filter_checked"] = True
        if check_and_apply_default_filter() and on_reload:
            on_reload()

# Test function 
if __name__ == "__main__":
    st.set_page_config(page_title="Saved Filters UI Test", layout="wide")
    
    st.title("Saved Filters UI Test")
    
    # Initialize session state for testing
    if "producer_filter_om" not in st.session_state:
        st.session_state["producer_filter_om"] = None
    if "product_filter_om" not in st.session_state:
        st.session_state["product_filter_om"] = None
    if "status_filter_om" not in st.session_state:
        st.session_state["status_filter_om"] = "CREATED"
    if "from_date_filter_om" not in st.session_state:
        st.session_state["from_date_filter_om"] = date.today().replace(day=1)
    if "to_date_filter_om" not in st.session_state:
        st.session_state["to_date_filter_om"] = date.today()
    
    # Reload callback for testing
    def on_reload():
        st.write("Filter applied - this is the reload callback being executed")
    
    # Render saved filters UI
    render_saved_filters_management(on_reload)
    
    # Display current filters for testing
    st.markdown("### Current Filters")
    st.write({
        "producer_filter_om": st.session_state.get("producer_filter_om"),
        "product_filter_om": st.session_state.get("product_filter_om"),
        "status_filter_om": st.session_state.get("status_filter_om"),
        "from_date_filter_om": st.session_state.get("from_date_filter_om"),
        "to_date_filter_om": st.session_state.get("to_date_filter_om"),
    })
    
    # Simple save button for testing
    render_save_filter_button()