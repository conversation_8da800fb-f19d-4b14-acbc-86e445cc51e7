"""
UI components for the offer management page.
Contains functions that render UI elements like filters, forms, and buttons.
"""
import streamlit as st
import uuid
import logging
from datetime import datetime, timedelta

# App imports with improved error handling and fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        # Fallback formatting functions if import fails
        logging.warning("Could not import formatting functions, using fallbacks")
        format_status = lambda x: x
        format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
        format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
        format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
        format_quantity = lambda x: f"{x:,.2f}" if x else "-"

try:
    from components.notification import show_info, show_error, show_success
except ImportError:
    # Fallback notification functions
    logging.warning("Could not import notification components, using fallbacks")
    show_info = lambda msg: st.info(msg)
    show_error = lambda msg: st.error(msg)
    show_success = lambda msg: st.success(msg)

# Import enhanced UI components - with standard fallbacks to prevent circular imports
try:
    # Try absolute import first
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        display_mobile_offer_card,
        render_quantity_input,
        render_section_card,
        render_confirmation_dialog,
        open_confirmation_dialog,
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info,
        render_active_filters_indicator,
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            display_mobile_offer_card,
            render_quantity_input,
            render_section_card,
            render_confirmation_dialog,
            open_confirmation_dialog,
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info,
            render_active_filters_indicator,
        )
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                display_mobile_offer_card,
                render_quantity_input,
                render_section_card,
                render_confirmation_dialog,
                open_confirmation_dialog,
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info,
                render_active_filters_indicator,
            )
        except ImportError:
            # If all imports fail, define minimal versions of these functions locally
            logging.warning("Could not import enhanced_ui_components, using minimal local implementations")
            
            # Basic implementations of the most essential functions
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            def render_confirmation_dialog(title, message, confirm_text="Yes", cancel_text="No", key=None):
                key = key or f"dialog_{uuid.uuid4()}"
                st.write(f"**{title}**")
                st.write(message)
                col1, col2 = st.columns(2)
                with col1:
                    cancel = st.button(cancel_text, key=f"{key}_cancel")
                with col2:
                    confirm = st.button(confirm_text, key=f"{key}_confirm")
                return confirm
                
            def open_confirmation_dialog(title, message, confirm_text="Yes", cancel_text="No", key=None):
                pass
                
            def render_section_card(title, content, color="#3584e4", icon=None, is_mobile=False, key=None, expanded=True):
                with st.expander(title, expanded=expanded):
                    content()
                    
            def display_mobile_offer_card(offer, index, on_click_func=None):
                st.write(f"**Offer #{offer.get('id')}**")
                st.write(f"Product: {offer.get('product_type', {}).get('name', 'Unknown')}")
                st.write(f"Status: {offer.get('status', 'Unknown')}")
                st.write(f"Quantity: {offer.get('quantity_in_kg', '0')} kg")
                if on_click_func and st.button("View Details", key=f"view_{offer.get('id')}_{index}"):
                    on_click_func()
                    
            def render_quantity_input(label, key, value=None, min_value=0.1, max_value=10000.0, step=0.1, help=None):
                return st.number_input(label, min_value=min_value, max_value=max_value, value=float(value) if value else min_value, step=step, help=help, key=key)
                
            def render_active_filters_indicator():
                filters = []
                if "status_filter_om" in st.session_state and st.session_state.status_filter_om:
                    filters.append(f"Status: {st.session_state.status_filter_om}")
                if filters:
                    st.write("**Active filters:** " + ", ".join(filters))

logger = logging.getLogger(__name__)

def render_producer_filter(page_id):
    """
    Termelő (létrehozó) alapján történő szűrés megjelenítése autocomplete funkcionalitással.
    
    Args:
        page_id (str): Egyedi azonosító a session state kulcsokhoz
        
    Returns:
        int/None: A kiválasztott termelő azonosítója vagy None, ha nincs kiválasztva
    """
    # Importáljuk a termelő autocomplete komponenst
    try:
        from pages.operator.offer_management.producer_autocomplete import render_producer_autocomplete
    except ImportError:
        try:
            from streamlit_app.pages.operator.offer_management.producer_autocomplete import render_producer_autocomplete
        except ImportError:
            try:
                from producer_autocomplete import render_producer_autocomplete
            except ImportError:
                # Ha nem tudjuk importálni az autocomplete komponenst, 
                # visszatérünk a régi selectbox alapú implementációhoz
                logger.warning("Nem sikerült importálni a termelő autocomplete komponenst, használjuk a régi megoldást")
                return render_producer_filter_legacy(page_id)
    
    try:
        from pages.operator.offer_management.data_processing import get_producers
    except ImportError:
        try:
            from data_processing import get_producers
        except ImportError:
            def get_producers():
                """Fallback producer API"""
                return False, "API client not available"
    
    # Create session state key for the filter
    filter_key = f"producer_filter_{page_id}"
    
    # Initialize session state for the filter if it doesn't exist
    if filter_key not in st.session_state:
        st.session_state[filter_key] = None
        
    # Próbáljuk betölteni a termelők listáját
    try:
        # Termelők betöltése
        success, producers_data = get_producers()
        
        # Ha sikeres a betöltés, használjuk az adatokat, egyébként üres lista
        producers = producers_data if success else []
        
        if producers:
            # Az új autocomplete komponens használata
            selected_producer_id = render_producer_autocomplete(
                producers=producers,
                default_id=st.session_state[filter_key],
                placeholder="Termelő keresése...",
                key=f"producer_autocomplete_{page_id}",
                include_all_option=True,
                label="Termelő:"
            )
            
            # Frissítjük a session state-et
            st.session_state[filter_key] = selected_producer_id
            
            return selected_producer_id
        else:
            # Ha üres a lista, de sikeres volt a lekérés
            if success:
                # Üres lista visszajelzés
                st.info("Nincsenek termelők a rendszerben.")
                selected_producer_id = None
                return selected_producer_id
            else:
                # Betöltési hiba visszajelzés
                show_inline_warning("Nem sikerült betölteni a termelők listáját.")
                
                # Egyszerűsített selectbox csak a "Mind" opcióval
                st.selectbox(
                    "Termelő:",
                    options=[None],
                    format_func=lambda x: "Minden termelő",
                    key=filter_key
                )
                return None
    
    except Exception as e:
        # Váratlan hiba esetén 
        logger.error(f"Hiba a termelők betöltésénél: {str(e)}")
        show_inline_error(f"Váratlan hiba történt a termelők betöltése során.")
        
        # Fallback selectbox, csak a "Mind" opcióval
        st.selectbox(
            "Termelő:",
            options=[None],
            format_func=lambda x: "Minden termelő",
            key=filter_key
        )
        return None


def render_producer_filter_legacy(page_id):
    """
    Termelő (létrehozó) alapján történő szűrés megjelenítése (régi verzió, selectbox alapú).
    
    Args:
        page_id (str): Egyedi azonosító a session state kulcsokhoz
        
    Returns:
        int/None: A kiválasztott termelő azonosítója vagy None, ha nincs kiválasztva
    """
    try:
        from pages.operator.offer_management.data_processing import get_producers
    except ImportError:
        from data_processing import get_producers
    
    # Create session state key for the filter
    filter_key = f"producer_filter_{page_id}"
    
    # Initialize session state for the filter if it doesn't exist
    if filter_key not in st.session_state:
        st.session_state[filter_key] = None
        
    # Próbáljuk betölteni a termelők listáját
    try:
        # Termelők betöltése
        success, producers_data = get_producers()
        
        # Ha sikeres a betöltés, használjuk az adatokat, egyébként üres lista
        producers = producers_data if success else []
        
        # Szűrő megjelenítése
        options = [None]  # Első opció: Nincs kiválasztva
        
        if producers:
            # Hozzáadjuk a termelő ID-kat, ha vannak termelők
            options += [p["id"] for p in producers]
            
            # Format function that handles empty producers list
            def format_producer(x):
                if x is None:
                    return "Minden termelő"  # Magyarított "Összes" opció
                
                # Termelő nevének meghatározása a különböző lehetséges mezők alapján
                for p in producers:
                    if p["id"] == x:
                        # Az API különböző mezőkben küldheti a nevet, ezeket ellenőrizzük
                        if "contact_name" in p and p["contact_name"]:
                            return p["contact_name"]
                        elif "company_name" in p and p["company_name"]:
                            return p["company_name"]
                        elif "full_name" in p and p["full_name"]:
                            return p["full_name"]
                        elif "name" in p and p["name"]:
                            return p["name"]
                        elif "email" in p and p["email"]:
                            return p["email"]
                        else:
                            return f"Termelő #{x}"
                
                # Ha nem találtuk meg a termelőt
                return f"Termelő #{x}"
            
            # Megjelenítjük a selectbox-ot
            selected_producer_id = st.selectbox(
                "Termelő:",
                options=options,
                format_func=format_producer,
                key=filter_key
            )
            
            return selected_producer_id
        else:
            # Ha üres a lista, de sikeres volt a lekérés
            if success:
                # Üres lista visszajelzés
                st.info("Nincsenek termelők a rendszerben.")
                selected_producer_id = None
                return selected_producer_id
            else:
                # Betöltési hiba visszajelzés
                show_inline_warning("Nem sikerült betölteni a termelők listáját.")
                
                # Egyszerűsített selectbox csak a "Mind" opcióval
                st.selectbox(
                    "Termelő:",
                    options=[None],
                    format_func=lambda x: "Minden termelő",
                    key=filter_key
                )
                return None
    
    except Exception as e:
        # Váratlan hiba esetén 
        logger.error(f"Hiba a termelők betöltésénél: {str(e)}")
        show_inline_error(f"Váratlan hiba történt a termelők betöltése során.")
        
        # Fallback selectbox, csak a "Mind" opcióval
        st.selectbox(
            "Termelő:",
            options=[None],
            format_func=lambda x: "Minden termelő",
            key=filter_key
        )
        return None

def render_status_filter(page_id):
    """
    Státusz alapján történő szűrés megjelenítése.
    
    Args:
        page_id (str): Egyedi azonosító a session state kulcsokhoz
        
    Returns:
        str/None: A kiválasztott státusz vagy None, ha nincs kiválasztva
    """
    # Create session state key for the filter
    filter_key = f"status_filter_{page_id}"
    
    # Initialize session state for the filter if it doesn't exist
    if filter_key not in st.session_state:
        st.session_state[filter_key] = None
    
    try:
        # Biztonságos státusz formázás
        def safe_format_status(status):
            try:
                from utils.formatting import format_status
                return "Minden státusz" if status is None else format_status(status)
            except Exception as e:
                logger.error(f"Error formatting status: {e}")
                return "Minden státusz" if status is None else str(status)
        
        # Státusz opciók - csak a hivatalos API státuszok
        status_options = [
            None, 
            "CREATED", 
            "CONFIRMED_BY_COMPANY", 
            "ACCEPTED_BY_USER", 
            "REJECTED_BY_USER", 
            "FINALIZED"
        ]
        
        # Selectbox megjelenítése a státusz kiválasztásához
        selected_status = st.selectbox(
            "Státusz:",
            options=status_options,
            format_func=safe_format_status,
            key=filter_key
        )
        
        return selected_status
        
    except Exception as e:
        # Hiba esetén naplózás és visszajelzés a felhasználónak
        logger.error(f"Hiba a státusz szűrő megjelenítésénél: {str(e)}")
        show_inline_error(f"Hiba történt a státusz szűrő betöltése során.")
        
        # Fallback selectbox csak a "Minden státusz" opcióval
        st.selectbox(
            "Státusz:",
            options=[None],
            format_func=lambda x: "Minden státusz",
            key=filter_key
        )
        return None

def render_date_filter(page_id):
    """
    Reszponzív dátum szűrő komponens renderelése.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        tuple: (from_date, to_date) a kiválasztott dátumtartomány.
    """
    try:
        # Try importing the simpler implementation first
        try:
            from pages.operator.offer_management.fixed_date_filter import render_date_filter as fixed_render_date_filter
            return fixed_render_date_filter(page_id)
        except ImportError:
            from fixed_date_filter import render_date_filter as fixed_render_date_filter
            return fixed_render_date_filter(page_id)
    except Exception as e:
        # Fallback to a very simple implementation if the import fails
        logger.error(f"Error using fixed_date_filter: {e}. Falling back to simple implementation.")
        
        # Képernyőméret ellenőrzése a reszponzív viselkedéshez
        is_mobile = st.session_state.get('is_mobile', False)
        
        # Mai dátum
        today = datetime.now().date()
        
        # Session state kulcsok
        from_date_key = f"from_date_filter_{page_id}"
        to_date_key = f"to_date_filter_{page_id}"
        
        # Alapértelmezett értékek beállítása, ha még nincsenek
        if from_date_key not in st.session_state:
            st.session_state[from_date_key] = today - timedelta(days=30)
        
        if to_date_key not in st.session_state:
            st.session_state[to_date_key] = today
        
        # Előre definiált tartományok selectbox
        options = ["Előző hónap", "Előző 2 hét", "Mai nap", "Következő hét", "Következő 2 hét", "Következő hónap"]
        selected = st.selectbox("Időszak:", options, index=0)
        
        # Dátum beállítása az előre definiált időszak alapján
        if selected == "Előző hónap":
            from_date = today - timedelta(days=30)
            to_date = today
        elif selected == "Előző 2 hét":
            from_date = today - timedelta(days=14)
            to_date = today
        elif selected == "Mai nap":
            from_date = today
            to_date = today
        elif selected == "Következő hét":
            from_date = today
            to_date = today + timedelta(days=7)
        elif selected == "Következő 2 hét":
            from_date = today
            to_date = today + timedelta(days=14)
        elif selected == "Következő hónap":
            from_date = today
            to_date = today + timedelta(days=30)
        
        # Egyedi dátum beállítás
        st.markdown("**Dátumtartomány finomhangolása:**")
        
        if is_mobile:
            # Mobilnézet - egymás alatt
            from_date = st.date_input("Kezdő dátum:", value=from_date, key=from_date_key)
            to_date = st.date_input("Végső dátum:", value=to_date, key=to_date_key)
        else:
            # Asztali nézet - egymás mellett
            col1, col2 = st.columns(2)
            with col1:
                from_date = st.date_input("Kezdő dátum:", value=from_date, key=from_date_key)
            with col2:
                to_date = st.date_input("Végső dátum:", value=to_date, key=to_date_key)
        
        # Dátumok validálása
        if from_date > to_date:
            st.warning("A kezdő dátum nem lehet későbbi, mint a végső dátum!")
            from_date = to_date - timedelta(days=1)
        
        return from_date, to_date

def render_offer_filters(page_id):
    """
    Az összes ajánlat szűrő megjelenítése.
    
    Args:
        page_id (str): Egyedi azonosító a session state kulcsokhoz
        
    Returns:
        tuple: (selected_producer_id, selected_status, from_date, to_date)
    """
    st.markdown("### Ajánlatok szűrése")
    
    # Initialize or reset session state first, before creating any widgets
    reset_pressed = False
    reset_key = f"reset_button_{page_id}"
    
    # Check if reset was pressed - we need to do this first before creating widgets
    if reset_key in st.session_state and st.session_state[reset_key]:
        # This is a hack to detect if reset was pressed in previous run
        reset_pressed = True
        # Clear the reset state for next run
        st.session_state[reset_key] = False
    
    # If reset was pressed, initialize the values BEFORE creating the widgets
    if reset_pressed:
        logger.info("Resetting filter values")
        producer_key = f"producer_filter_{page_id}"
        status_key = f"status_filter_{page_id}"
        from_date_key = f"from_date_filter_{page_id}"
        to_date_key = f"to_date_filter_{page_id}"
        
        # Set default values
        st.session_state[producer_key] = None
        st.session_state[status_key] = None
        st.session_state[from_date_key] = (datetime.now() - timedelta(days=30)).date()
        st.session_state[to_date_key] = datetime.now().date()
    
    # Now create the filter widgets
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        selected_producer_id = render_producer_filter(page_id)
    
    with col2:
        selected_status = render_status_filter(page_id)
    
    with col3:
        from_date, to_date = render_date_filter(page_id)
    
    # Extra keresési gombok
    search_button = st.button("🔍 Keresés", key=f"search_button_{page_id}")
    
    # Create the reset button and save its state for the next run
    reset_button = st.button("🔄 Alaphelyzet", key=reset_key)
    
    # Handle reset in the next run, we just store the button press
    if reset_button:
        st.rerun()
    
    # Ha a keresés gombra kattintottak vagy alapértelmezés szerint
    if search_button or "search_performed" not in st.session_state:
        st.session_state.search_performed = True
    
    return selected_producer_id, selected_status, from_date, to_date

def render_active_filters_indicator():
    """
    Az aktív szűrők megjelenítése címkékkel a keresőmezők alatt.
    
    Ez vizuális visszajelzést ad a felhasználónak, hogy milyen szűrési feltételek vannak érvényben.
    """
    active_filters = []
    
    try:
        # Biztonságos formázás
        def safe_format_status(status):
            try:
                from utils.formatting import format_status
                return format_status(status)
            except Exception as e:
                logger.error(f"Error formatting status in filter indicator: {e}")
                return str(status)
        
        # Termelő szűrő ellenőrzése
        if "producer_filter_om" in st.session_state and st.session_state.producer_filter_om is not None:
            active_filters.append(f"Termelő: {st.session_state.producer_filter_om}")
        
        # Státusz szűrő ellenőrzése
        if "status_filter_om" in st.session_state and st.session_state.status_filter_om is not None:
            active_filters.append(f"Státusz: {safe_format_status(st.session_state.status_filter_om)}")
        
        # Dátum szűrők ellenőrzése
        if "from_date_filter_om" in st.session_state and "to_date_filter_om" in st.session_state:
            from_date = st.session_state.from_date_filter_om
            to_date = st.session_state.to_date_filter_om
            
            # Csak akkor jelenítjük meg, ha nem az alapértelmezett 30 napos időszak
            default_from = (datetime.now() - timedelta(days=30)).date()
            default_to = datetime.now().date()
            
            if from_date != default_from or to_date != default_to:
                # Biztonságos dátum formázás
                try:
                    from_date_str = from_date.strftime('%Y-%m-%d')
                    to_date_str = to_date.strftime('%Y-%m-%d')
                    active_filters.append(f"Időszak: {from_date_str} - {to_date_str}")
                except Exception as e:
                    logger.error(f"Error formatting dates in filter indicator: {e}")
                    active_filters.append("Időszak: Egyedi szűrés")
    except Exception as e:
        logger.error(f"Error rendering active filters: {e}")
        # If there's an error, just add a generic filter indicator
        active_filters = ["Aktív szűrők vannak beállítva"]
    
    # Ha vannak aktív szűrők, megjelenítsük őket
    if active_filters:
        html_content = """
        <style>
        .active-filters {
            margin: 10px 0;
            padding: 5px 0;
        }
        .filter-tag {
            display: inline-block;
            background-color: #f0f2f6;
            border-radius: 15px;
            padding: 5px 10px;
            margin: 2px 5px;
            font-size: 0.8em;
        }
        </style>
        <div class="active-filters">
            <strong>Aktív szűrők:</strong> 
        """
        
        for filter_text in active_filters:
            html_content += f'<span class="filter-tag">{filter_text}</span>'
        
        html_content += '</div>'
        
        st.markdown(html_content, unsafe_allow_html=True)

def render_pagination_controls(current_page, total_pages, on_change):
    """
    Lapozási vezérlők megjelenítése.
    
    Args:
        current_page (int): Az aktuális oldal száma
        total_pages (int): Az összes oldal száma
        on_change (callable): Függvény, amely az oldalszám változásakor fut le
    """
    st.markdown("---")
    
    # Lapozás vezérlők
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        if st.button("⬅️ Előző oldal", disabled=current_page <= 1):
            on_change(current_page - 1)
    
    with col2:
        st.markdown(f"<div style='text-align: center;'>{current_page} / {total_pages} oldal</div>", unsafe_allow_html=True)
    
    with col3:
        if st.button("Következő oldal ➡️", disabled=current_page >= total_pages):
            on_change(current_page + 1)
    
    # Oldalszám közvetlen kiválasztása
    st.slider(
        "Oldal kiválasztása",
        min_value=1,
        max_value=max(1, total_pages),
        value=current_page,
        key="page_slider",
        on_change=lambda: on_change(st.session_state.page_slider)
    )

def render_tooltip(tooltip_text, icon="ℹ️", placement="top", key=None):
    """
    Tooltip megjelenítése ikon mellett.
    
    Args:
        tooltip_text (str): A megjelenítendő tooltip szövege
        icon (str, optional): Az ikon, ami mellett megjelenik a tooltip. Defaults to "ℹ️".
        placement (str, optional): A tooltip elhelyezése (top, bottom, left, right). Defaults to "top".
        key (str, optional): Egyedi kulcs a tooltip-hez. Ha None, akkor generálunk egyet. Defaults to None.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"tooltip_{str(uuid.uuid4())[:8]}"
    
    # CSS stílus a tooltip-hez
    tooltip_css = f"""
    <style>
    .tooltip-{key} {{
        position: relative;
        display: inline-block;
        cursor: help;
        margin-left: 5px;
    }}
    
    .tooltip-text-{key} {{
        visibility: hidden;
        width: 200px;
        background-color: #333;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 0.8em;
    }}
    
    .tooltip-{key}:hover .tooltip-text-{key} {{
        visibility: visible;
        opacity: 0.9;
    }}
    
    /* Different placements */
    .tooltip-text-{key}.top {{
        bottom: 125%;
        left: 50%;
        margin-left: -100px;
    }}
    
    .tooltip-text-{key}.bottom {{
        top: 125%;
        left: 50%;
        margin-left: -100px;
    }}
    
    .tooltip-text-{key}.left {{
        top: -5px;
        right: 110%;
    }}
    
    .tooltip-text-{key}.right {{
        top: -5px;
        left: 110%;
    }}
    </style>
    """
    
    # HTML a tooltip-hez
    tooltip_html = f"""
    <span class="tooltip-{key}">
        {icon}
        <span class="tooltip-text-{key} {placement}">{tooltip_text}</span>
    </span>
    """
    
    # Megjelenítjük a tooltip-et
    st.markdown(f"{tooltip_css}{tooltip_html}", unsafe_allow_html=True)

def render_help_text(component_name, help_text, icon="❓"):
    """
    Segítő szöveg megjelenítése egy komponenshez magyarázattal.
    
    Args:
        component_name (str): A komponens neve, amihez a segítség tartozik
        help_text (str): A megjelenítendő segítő szöveg
        icon (str, optional): A segítség ikon. Defaults to "❓".
    """
    st.markdown(f"**{component_name}** {render_tooltip(help_text, icon=icon)}", unsafe_allow_html=True)

def display_status_legend():
    """
    Státusz magyarázatok megjelenítése egy kompakt, vizuálisan vonzó formában.
    FONTOS: Használj inkább status_visualization.display_enhanced_status_legend() függvényt!
    Ez a függvény csak visszafelé kompatibilitási okokból maradt meg.
    """
    # Import az új, továbbfejlesztett státusz legenda komponens
    try:
        from .status_visualization import display_enhanced_status_legend
        # Ha sikerült importálni, használjuk az új komponenst
        display_enhanced_status_legend()
        return
    except ImportError:
        # Ha nem sikerült importálni, használjuk a régi implementációt
        pass
    
    st.markdown("### Státusz magyarázat")
    
    # Státusz kategóriák és magyarázatok - csak a hivatalos API státuszok
    statuses = {
        "Kezdeti státuszok": {
            "CREATED": "Az ajánlat létrehozva, de még nem került megerősítésre",
            "CONFIRMED_BY_COMPANY": "Az ajánlat megerősítve a vállalat által, várakozás a termelő visszajelzésére"
        },
        "Folyamatban lévő státuszok": {
            "ACCEPTED_BY_USER": "A termelő elfogadta az ajánlatot"
        },
        "Végső státuszok": {
            "FINALIZED": "Az ajánlat véglegesítve, a folyamat lezárult",
            "REJECTED_BY_USER": "A termelő elutasította az ajánlatot"
        }
    }
    
    # Státusz színek a jobb vizuális elkülönítéshez - csak a hivatalos API státuszok
    status_colors = {
        "CREATED": "#FFA07A",        # LightSalmon
        "CONFIRMED_BY_COMPANY": "#FFD700",  # Gold
        "ACCEPTED_BY_USER": "#98FB98",  # PaleGreen
        "REJECTED_BY_USER": "#FF6347",  # Tomato
        "FINALIZED": "#20B2AA"       # LightSeaGreen
    }
    
    # Megjelenítjük a státuszokat kategóriánként
    for category, category_statuses in statuses.items():
        st.markdown(f"#### {category}")
        
        # Kategórián belüli státuszok
        for status, description in category_statuses.items():
            color = status_colors.get(status, "#777777")
            
            # HTML a stílusos megjelenítéshez
            st.markdown(f"""
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 15px; height: 15px; background-color: {color}; 
                            border-radius: 50%; margin-right: 10px;"></div>
                <div style="min-width: 200px; font-weight: bold;">{format_status(status)}</div>
                <div>{description}</div>
            </div>
            """, unsafe_allow_html=True)

def render_section_card(title, content, color="#3584e4", icon=None, is_mobile=False, key=None, expanded=True):
    """
    Kártyaként megjelenített tartalom szekció.
    
    Args:
        title (str): A kártya címe
        content (callable): Függvény, amely a kártya tartalmát rendereli
        color (str, optional): A kártya fejlécének színe. Defaults to "#3584e4".
        icon (str, optional): Ikon a kártya fejlécéhez. Defaults to None.
        is_mobile (bool, optional): Mobilnézet-e. Defaults to False.
        key (str, optional): Egyedi kulcs a kártyához. Defaults to None.
        expanded (bool, optional): Alapértelmezetten kinyitott-e. Ha None, akkor nem összecsukható. Defaults to True.
    """
    # Egyedi kulcs generálása
    if key is None:
        key = f"card_{str(uuid.uuid4())[:8]}"
    
    # Állapot inicializálása a session state-ben
    if expanded is not None and f"card_expanded_{key}" not in st.session_state:
        st.session_state[f"card_expanded_{key}"] = expanded
    
    # Mobilbarát méretezés
    card_width = "100%" if is_mobile else "95%"
    
    # CSS stílus a kártyához
    card_css = f"""
    <style>
    .card-{key} {{
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        width: {card_width};
        overflow: hidden;
    }}
    
    .card-header-{key} {{
        background-color: {color};
        color: white;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        cursor: {f"pointer" if expanded is not None else "default"};
    }}
    
    .card-icon-{key} {{
        margin-right: 10px;
        font-size: 1.2em;
    }}
    
    .card-title-{key} {{
        font-weight: bold;
        font-size: 1.1em;
        flex-grow: 1;
    }}
    
    .card-toggle-{key} {{
        font-size: 1.2em;
    }}
    
    .card-content-{key} {{
        padding: 15px;
        background-color: white;
    }}
    </style>
    """
    
    # JavaScript a kártya összecsukásához/kinyitásához
    toggle_js = f"""
    <script>
    // Kártya összecsukás/kinyitás JS
    document.addEventListener('DOMContentLoaded', function() {{
        const cardHeader = document.querySelector('.card-header-{key}');
        const cardContent = document.querySelector('.card-content-{key}');
        const cardToggle = document.querySelector('.card-toggle-{key}');
        
        if (cardHeader && cardContent && cardToggle) {{
            cardHeader.addEventListener('click', function() {{
                // Váltjuk a láthatóságot
                if (cardContent.style.display === 'none') {{
                    cardContent.style.display = 'block';
                    cardToggle.textContent = '▼';
                    // Állapot frissítése a hidden input-on keresztül
                    document.getElementById('card_state_{key}').value = 'expanded';
                    // Streamlit rerun trigger
                    document.getElementById('card_state_{key}').dispatchEvent(new Event('change'));
                }} else {{
                    cardContent.style.display = 'none';
                    cardToggle.textContent = '▶';
                    // Állapot frissítése a hidden input-on keresztül
                    document.getElementById('card_state_{key}').value = 'collapsed';
                    // Streamlit rerun trigger
                    document.getElementById('card_state_{key}').dispatchEvent(new Event('change'));
                }}
            }});
        }}
    }});
    </script>
    """
    
    # A kártyafejléc HTML
    toggle_button = f'<div class="card-toggle-{key}">{"▼" if st.session_state.get(f"card_expanded_{key}", expanded) else "▶"}</div>' if expanded is not None else ''
    
    header_html = f"""
    <div class="card-header-{key}">
        <div class="card-icon-{key}">{icon or ""}</div>
        <div class="card-title-{key}">{title}</div>
        {toggle_button}
    </div>
    """
    
    # A teljes kártya HTML (fejléc + tartalom container)
    card_html = f"""
    {card_css}
    <div class="card-{key}">
        {header_html}
        <div class="card-content-{key}" style="display: {'block' if st.session_state.get(f'card_expanded_{key}', expanded) else 'none'};">
            <!-- A tartalom itt lesz dinamikusan beillesztve Streamlit által -->
        </div>
    </div>
    {toggle_js if expanded is not None else ""}
    """
    
    # A kártya renderelése
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Ha a kártya ki van nyitva, akkor rendereljük a tartalmát
    if expanded is None or st.session_state.get(f"card_expanded_{key}", expanded):
        # Létrehozunk egy kontextust a tartalom rendereléshez
        content_placeholder = st.container()
        
        # A tartalmat a létrehozott konténer kontextusában rendereljük
        with content_placeholder:
            content()  # Meghívjuk a tartalmat renderelő függvényt
    
    # Rejtett input a kártya állapotának követéséhez (csak ha szükséges)
    if expanded is not None:
        value = "expanded" if st.session_state.get(f"card_expanded_{key}", expanded) else "collapsed"
        st.text_input(f"Card State {key}", value=value, key=f"card_state_{key}", label_visibility="collapsed")

def render_quantity_input(label, key, value=None, min_value=0.1, max_value=10000.0, step=0.1, help=None):
    """
    Mennyiség beviteli mező renderelése, amely támogat különböző formátumokat
    és speciális validációt biztosít.
    
    Args:
        label (str): A beviteli mező címkéje
        key (str): Egyedi kulcs a beviteli mezőhöz
        value (float/str, optional): Kezdeti érték. Defaults to None.
        min_value (float, optional): Minimum érték. Defaults to 0.1.
        max_value (float, optional): Maximum érték. Defaults to 10000.0.
        step (float, optional): Lépésköz. Defaults to 0.1.
        help (str, optional): Segítő szöveg. Defaults to None.
        
    Returns:
        float: A bevitt mennyiség értéke
    """
    from .utils import show_inline_error
    
    # Kezdeti érték formázása
    if value is not None:
        try:
            # Próbáljuk számmá konvertálni, ha string
            if isinstance(value, str):
                value = value.replace(',', '.')
                value = float(value)
            
            # Formázzuk 1 tizedesjegyre
            value_str = f"{value:.1f}"
        except (ValueError, TypeError):
            value_str = ""
    else:
        value_str = ""
    
    # Speciális beviteli mező, amely string-et ad vissza, de csak számokat fogad el
    input_value = st.text_input(
        label,
        value=value_str,
        key=key,
        help=help
    )
    
    # Érték konvertálása és validálása
    if input_value:
        try:
            # Vessző cseréje pontra
            input_value = input_value.replace(',', '.')
            
            # Konvertálás float-ra
            quantity = float(input_value)
            
            # Validálás
            if quantity < min_value:
                show_inline_error(f"A megadott érték nem lehet kisebb, mint {min_value}")
                return min_value
                
            if quantity > max_value:
                show_inline_error(f"A megadott érték nem lehet nagyobb, mint {max_value}")
                return max_value
                
            return quantity
            
        except ValueError:
            show_inline_error("Kérjük, adjon meg egy érvényes számot.")
            return None
    
    return None

def render_confirmation_dialog(title, message, confirm_text="Igen", cancel_text="Nem", icon="⚠️", key=None):
    """
    Megerősítő párbeszédablak renderelése kritikus műveletekhez.
    
    Ez a függvény egy megerősítő párbeszédablakot jelenít meg, amely
    a felhasználó explicit megerősítését kéri a kritikus műveletek
    (pl. törlés, státuszváltások) előtt.
    
    Args:
        title (str): A párbeszédablak címe.
        message (str): A megerősítő üzenet.
        confirm_text (str, optional): A megerősítő gomb szövege. Defaults to "Igen".
        cancel_text (str, optional): A mégsem gomb szövege. Defaults to "Nem".
        icon (str, optional): Az ikon. Defaults to "⚠️".
        key (str, optional): Egyedi kulcs a komponenshez. Defaults to None.
        
    Returns:
        bool: True ha a felhasználó megerősíti, False egyébként.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"confirm_dialog_{str(uuid.uuid4())[:8]}"
    
    # Ellenőrizzük, hogy a dialógus állapotát tároltuk-e már
    if f"{key}_open" not in st.session_state:
        st.session_state[f"{key}_open"] = False
        st.session_state[f"{key}_confirmed"] = False
    
    # Ha a dialógus nem nyitott, visszatérünk
    if not st.session_state[f"{key}_open"]:
        return False
    
    # A dialógus megjelenítése
    dialog_container = st.container()
    with dialog_container:
        # Stílus a dialógushoz
        st.markdown(f"""
        <style>
        .dialog-overlay-{key} {{
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        
        .dialog-container-{key} {{
            background-color: var(--background-color, white);
            border: 1px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            animation: dialog-fade-in-{key} 0.3s ease;
        }}
        
        @keyframes dialog-fade-in-{key} {{
            from {{ opacity: 0; transform: translateY(-20px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        .dialog-title-{key} {{
            background-color: #f0f2f6;
            padding: 15px;
            font-weight: bold;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }}
        
        .dialog-title-icon-{key} {{
            font-size: 1.4em;
            margin-right: 10px;
        }}
        
        .dialog-content-{key} {{
            padding: 20px 15px;
            font-size: 1.05em;
            line-height: 1.4;
        }}
        
        .dialog-buttons-{key} {{
            display: flex;
            justify-content: flex-end;
            padding: 10px 15px 20px;
            gap: 15px;
        }}
        
        .dialog-button-{key} {{
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
        }}
        
        .dialog-button-{key}:hover {{
            opacity: 0.9;
            transform: translateY(-2px);
        }}
        
        .dialog-confirm-{key} {{
            background-color: #ff4b4b;
            color: white;
        }}
        
        .dialog-cancel-{key} {{
            background-color: #f0f2f6;
            color: #333;
        }}
        </style>
        
        <div class="dialog-overlay-{key}">
            <div class="dialog-container-{key}">
                <div class="dialog-title-{key}">
                    <span class="dialog-title-icon-{key}">{icon}</span>
                    <span>{title}</span>
                </div>
                <div class="dialog-content-{key}">
                    {message}
                </div>
                <div class="dialog-buttons-{key}">
                    <button class="dialog-button-{key} dialog-cancel-{key}" id="dialog_cancel_btn_{key}">{cancel_text}</button>
                    <button class="dialog-button-{key} dialog-confirm-{key}" id="dialog_confirm_btn_{key}">{confirm_text}</button>
                </div>
            </div>
        </div>
        
        <script>
            // Gomb eseménykezelők hozzáadása
            document.addEventListener('DOMContentLoaded', function() {{
                const confirmBtn = document.getElementById('dialog_confirm_btn_{key}');
                const cancelBtn = document.getElementById('dialog_cancel_btn_{key}');
                const resultInput = document.getElementById('{key}_result');
                
                if (confirmBtn && resultInput) {{
                    confirmBtn.addEventListener('click', function() {{
                        resultInput.value = 'true';
                        resultInput.dispatchEvent(new Event('change'));
                    }});
                }}
                
                if (cancelBtn && resultInput) {{
                    cancelBtn.addEventListener('click', function() {{
                        resultInput.value = 'false';
                        resultInput.dispatchEvent(new Event('change'));
                    }});
                }}
                
                // ESC gomb kezelése a dialógushoz
                document.addEventListener('keydown', function(e) {{
                    if (e.key === 'Escape' && resultInput) {{
                        resultInput.value = 'false';
                        resultInput.dispatchEvent(new Event('change'));
                    }}
                }});
            }});
        </script>
        """, unsafe_allow_html=True)
        
        # Gomb állapot és eredmény követése
        result = st.text_input(f"Dialog result", value="waiting", key=f"{key}_result", label_visibility="collapsed")
        
        if result == "true":
            st.session_state[f"{key}_confirmed"] = True
            st.session_state[f"{key}_open"] = False
            return True
        elif result == "false":
            st.session_state[f"{key}_confirmed"] = False
            st.session_state[f"{key}_open"] = False
            return False
    
    return False

# Segédfüggvény a dialógus megnyitásához
def open_confirmation_dialog(title, message, confirm_text="Igen", cancel_text="Nem", icon="⚠️", key=None):
    """
    Megnyit egy megerősítő dialógust egy adott kulccsal.
    
    Ez a segédfüggvény beállítja a session state-et, hogy egy megerősítő 
    dialógus megjelenjen a következő újbóli futtatáskor.
    
    Args:
        title (str): A dialógus címe
        message (str): A megerősítendő üzenet
        confirm_text (str, optional): A megerősítő gomb szövege. Defaults to "Igen".
        cancel_text (str, optional): A mégsem gomb szövege. Defaults to "Nem".
        icon (str, optional): A dialógus ikonja. Defaults to "⚠️".
        key (str, optional): Egyedi kulcs a dialógushoz. Defaults to None.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"confirm_dialog_{str(uuid.uuid4())[:8]}"
    
    # Beállítjuk a dialógus állapotát
    dialog_key = f"{key}_open"
    st.session_state[dialog_key] = True
    
    # Tároljuk a dialógus paramétereit a session state-ben
    st.session_state[f"{key}_title"] = title
    st.session_state[f"{key}_message"] = message
    st.session_state[f"{key}_confirm_text"] = confirm_text
    st.session_state[f"{key}_cancel_text"] = cancel_text
    st.session_state[f"{key}_icon"] = icon
    
    # Újratelenítjük az oldalt, hogy a dialógus megjelenjen
    st.rerun()

def show_inline_error(error_message):
    """
    Inline hibaüzenet megjelenítése közvetlenül a hiba után.
    
    Args:
        error_message (str): A megjelenítendő hibaüzenet.
    """
    if error_message:
        st.markdown(f"""
        <div style="color: #721c24; background-color: #f8d7da; 
                    border: 1px solid #f5c6cb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #dc3545;">
            <strong>⚠️ Hiba:</strong> {error_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_warning(warning_message):
    """
    Inline figyelmeztető üzenet megjelenítése közvetlenül a mező után.
    
    Args:
        warning_message (str): A megjelenítendő figyelmeztetés.
    """
    if warning_message:
        st.markdown(f"""
        <div style="color: #856404; background-color: #fff3cd; 
                    border: 1px solid #ffeeba; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #ffc107;">
            <strong>⚠️ Figyelmeztetés:</strong> {warning_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_success(success_message):
    """
    Inline sikeres művelet üzenet megjelenítése.
    
    Args:
        success_message (str): A megjelenítendő sikeres művelet üzenete.
    """
    if success_message:
        st.markdown(f"""
        <div style="color: #155724; background-color: #d4edda; 
                    border: 1px solid #c3e6cb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #28a745;">
            <strong>✅ Sikeres:</strong> {success_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_info(info_message):
    """
    Inline információs üzenet megjelenítése.
    
    Args:
        info_message (str): A megjelenítendő információs üzenet.
    """
    if info_message:
        st.markdown(f"""
        <div style="color: #0c5460; background-color: #d1ecf1; 
                    border: 1px solid #bee5eb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;
                    border-left: 4px solid #17a2b8;">
            <strong>ℹ️ Információ:</strong> {info_message}
        </div>
        """, unsafe_allow_html=True)

# Az inject_keyboard_shortcuts és inject_screen_detection függvényeket 
# áthelyeztük a responsive_ui.py fájlba.

# A display_mobile_offer_card függvényt áthelyeztük a responsive_ui.py fájlba

def display_offer_table_with_actions(offers, pagination=True):
    """
    Ajánlatok táblázatos megjelenítése műveletgombokkal.
    
    Args:
        offers (list): Az ajánlatok listája
        pagination (bool, optional): Legyen-e lapozás. Defaults to True.
    """
    from components.data_display import display_offer_table
    
    if not offers:
        st.info("Nincsenek találatok a keresési feltételekkel.")
        return
    
    # Oldaltöréses adatok
    total_offers = len(offers)
    page_size = 10
    
    # Aktuális oldal állapot kezelése
    if "current_page" not in st.session_state:
        st.session_state.current_page = 1
    
    # Oldalak száma
    total_pages = (total_offers + page_size - 1) // page_size
    
    # Jelenlegi oldal adatai
    start_idx = (st.session_state.current_page - 1) * page_size
    end_idx = min(start_idx + page_size, total_offers)
    paged_offers = offers[start_idx:end_idx]
    
    # Táblázat mező definíciók - olvasható magyar címkékkel
    offer_fields = {
        "id": "id",
        "státusz": "status",
        "termék": "product_type.name",
        "termelő": "user.contact_name",
        "mennyiség": "quantity_in_kg",
        "beszállítási_dátum": "delivery_date"
    }
    
    # Adattáblázat megjelenítése
    display_data = prepare_offer_display_data(paged_offers)
    
    # Táblázatos kijelzés a data_display komponens segítségével
    display_offer_table(display_data)
    
    # Műveletek a kiválasztott ajánlaton
    if paged_offers:
        st.write("### Műveletek")
        
        # Ajánlat azonosítók listája a kiválasztáshoz
        offer_ids = [offer.get("id") for offer in paged_offers]
        offer_names = [f"#{offer.get('id')} - {offer.get('product_type', {}).get('name', 'Ismeretlen termék')}" for offer in paged_offers]
        
        # Kiválasztó widget
        selected_index = st.selectbox(
            "Válasszon ajánlatot a műveletekhez:",
            range(len(offer_ids)),
            format_func=lambda i: offer_names[i]
        )
        
        # Kiválasztott ajánlat és státusz lekérése
        selected_offer = paged_offers[selected_index]
        selected_id = selected_offer.get("id")
        status = selected_offer.get("status", "")
        
        # Gombokat oszlopokban jelenítjük meg
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("Részletek", key=f"view_{selected_id}", use_container_width=True):
                st.session_state.selected_offer_id = selected_id
                st.rerun()
        
        with col2:
            disabled = status not in ["CREATED", "CONFIRMED_BY_COMPANY", "pending", "approved"]
            if st.button("Szerkesztés", key=f"edit_{selected_id}", use_container_width=True, disabled=disabled):
                st.session_state.selected_offer_id = selected_id
                st.session_state.edit_mode = True
                st.rerun()
        
        with col3:
            disabled = status not in ["CONFIRMED_BY_COMPANY", "approved"]
            if st.button("Elfogadás", key=f"accept_{selected_id}", use_container_width=True, disabled=disabled):
                from .api_client import update_offer_status
                
                success, result = update_offer_status(selected_id, "ACCEPTED_BY_USER")
                if success:
                    st.success("Ajánlat elfogadva!")
                    st.rerun()
                else:
                    st.error(f"Hiba: {result}")
        
        with col4:
            if st.button("Törlés", key=f"delete_{selected_id}", use_container_width=True, type="secondary"):
                if st.checkbox(f"Biztosan törli ezt az ajánlatot: #{selected_id}?", key=f"confirm_delete_{selected_id}"):
                    from .api_client import update_offer_status
                    
                    success, result = update_offer_status(selected_id, "canceled")
                    if success:
                        st.success("Ajánlat törölve!")
                        st.rerun()
                    else:
                        st.error(f"Hiba: {result}")
    
    # Lapozási kontrollok
    if pagination and total_pages > 1:
        render_pagination_controls(
            st.session_state.current_page, 
            total_pages,
            lambda page: setattr(st.session_state, 'current_page', page) or st.rerun()
        )

def prepare_offer_display_data(offers):
    """
    Ajánlatok adatainak előkészítése megjelenítéshez.
    
    Args:
        offers (list): Az ajánlatok listája
        
    Returns:
        list: Megjelenítésre előkészített ajánlatok
    """
    display_data = []
    
    for offer in offers:
        # Alap adatok
        display_offer = {
            "id": offer.get("id", "N/A"),
            "status": format_status(offer.get("status", "N/A")),
            "created_at": format_datetime(offer.get("created_at")),
            "updated_at": format_datetime(offer.get("updated_at"))
        }
        
        # Termék adatok
        product_type = offer.get("product_type", {})
        if product_type:
            display_offer["product_type"] = {
                "id": product_type.get("id"),
                "name": product_type.get("name", "Ismeretlen termék")
            }
        else:
            display_offer["product_type"] = {"name": "Ismeretlen termék"}
        
        # Termelő adatok
        user = offer.get("user", {})
        if user:
            display_offer["user"] = {
                "id": user.get("id"),
                "contact_name": user.get("contact_name", "Ismeretlen termelő")
            }
        else:
            display_offer["user"] = {"contact_name": "Ismeretlen termelő"}
        
        # Mennyiségi adatok
        display_offer["quantity_in_kg"] = format_quantity(offer.get("quantity_in_kg", 0))
        
        # Dátum adatok
        display_offer["delivery_date"] = format_date(offer.get("delivery_date", ""))
        
        display_data.append(display_offer)
    
    return display_data