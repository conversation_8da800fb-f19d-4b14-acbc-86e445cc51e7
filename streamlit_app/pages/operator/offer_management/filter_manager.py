"""
Central filter management system
Coordinates between UI filters, API parameters, and state persistence
"""

import streamlit as st
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class FilterManager:
    """Central coordinator for all filtering operations"""
    
    def __init__(self):
        self.initialize_filter_state()
    
    def safe_date_comparison(self, from_date, to_date):
        """
        Biztonságos dátum összehasonlítás None értékek kezelésével
        
        Returns:
            tuple: (is_valid, error_message)
        """
        # None check
        if from_date is None or to_date is None:
            return True, None
        
        # Type check
        if not (hasattr(from_date, '__gt__') and hasattr(to_date, '__gt__')):
            return False, f"Invalid date types: {type(from_date)}, {type(to_date)}"
        
        try:
            # Actual comparison
            if from_date > to_date:
                return False, f"From date ({from_date}) cannot be after to date ({to_date})"
            return True, None
        except (TypeError, ValueError) as e:
            return False, f"Date comparison failed: {e}"
    
    def initialize_filter_state(self):
        """Initialize all filter-related session state"""
        if 'filter_manager_state' not in st.session_state:
            st.session_state.filter_manager_state = {
                'last_filters': {},
                'cache_timestamp': None,
                'filter_cache': {},
                'validation_errors': []
            }
        
        logger.info("FilterManager state initialized")
    
    def get_ui_filters(self) -> Dict[str, Any]:
        """Extract current UI filter values from session state"""
        ui_filters = {}
        
        # Extract from modern UI components
        filter_keys = [
            'modern_producer_filter',
            'modern_status_filter', 
            'modern_from_date',
            'modern_to_date',
            'modern_product_filter',
            'modern_search'
        ]
        
        for key in filter_keys:
            if key in st.session_state:
                value = st.session_state[key]
                # Map session state keys to filter names
                filter_name = self._map_session_key_to_filter(key)
                if filter_name and value is not None:
                    ui_filters[filter_name] = value
        
        logger.debug(f"Extracted UI filters: {ui_filters}")
        return ui_filters
    
    def _map_session_key_to_filter(self, session_key: str) -> Optional[str]:
        """Map session state key to filter name"""
        mapping = {
            'modern_producer_filter': 'producer_filter',
            'modern_status_filter': 'status',
            'modern_from_date': 'from_date',
            'modern_to_date': 'to_date', 
            'modern_product_filter': 'product_name',
            'modern_search': 'search_term'
        }
        return mapping.get(session_key)
    
    def validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize filter values"""
        validated_filters = {}
        errors = []
        
        # 🚨 CRITICAL FIX: Enhanced producer filter validation with full traceability
        logger.critical(f"🔍 FILTER MANAGER: Validating producer filter from UI filters: {filters.get('producer_filter')}")
        
        if 'producer_filter' in filters:
            producer_data = filters['producer_filter']
            logger.critical(f"🔍 PRODUCER VALIDATION: Raw data: {producer_data} (type: {type(producer_data)})")
            
            # STRICT VALIDATION: Must be tuple with valid ID
            if isinstance(producer_data, tuple) and len(producer_data) == 2:
                display_name, producer_id = producer_data
                logger.critical(f"🔍 FILTER MANAGER: Extracted from tuple - Name: '{display_name}', ID: {producer_id}")
                
                # CRITICAL: Ensure producer_id is valid integer
                try:
                    producer_id_int = int(producer_id)
                    logger.critical(f"✅ PRODUCER VALIDATION: Valid ID extracted: {producer_id_int}")
                    
                    # 🚨 CRITICAL FIX: Keep original producer_filter format AND add individual fields
                    # This ensures compatibility with both old and new API parameter converter
                    validated_filters['producer_filter'] = producer_data  # Keep original format
                    validated_filters['producer_id'] = producer_id_int     # For legacy compatibility
                    validated_filters['producer_display_name'] = display_name  # For legacy compatibility
                    
                    # STORE validated producer data with tracking metadata
                    validated_filters['_producer_validation'] = {
                        'original_data': producer_data,
                        'extracted_id': producer_id_int,
                        'extracted_name': display_name,
                        'validation_timestamp': datetime.now().isoformat(),
                        'validation_source': 'FilterManager.validate_filters'
                    }
                    
                    logger.critical(f"✅ FILTER MANAGER: Successfully validated producer - ID: {producer_id_int}, Name: '{display_name}'")
                    
                except (ValueError, TypeError) as e:
                    logger.error(f"🚨 PRODUCER VALIDATION FAILED: Invalid ID {producer_id}: {e}")
                    errors.append(f"Producer ID conversion failed: {producer_id} - {str(e)}")
                    
            elif producer_data and producer_data != "Minden termelő":
                logger.error(f"🚨 PRODUCER VALIDATION FAILED: Invalid format {producer_data}")
                errors.append(f"Producer filter invalid format: {producer_data}")
            else:
                logger.info(f"ℹ️ FILTER MANAGER: Producer filter is empty or 'Minden termelő', skipping")
        else:
            logger.info(f"ℹ️ FILTER MANAGER: No 'producer_filter' key found in filters")
        
        # Validate status filter
        if 'status' in filters:
            status_list = filters['status']
            if isinstance(status_list, list):
                valid_statuses = ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']
                validated_statuses = [s for s in status_list if s in valid_statuses]
                if validated_statuses:
                    validated_filters['status'] = validated_statuses
                else:
                    errors.append("No valid statuses selected")
            elif status_list:
                errors.append(f"Status filter must be a list, got: {type(status_list)}")
        
        # Validate date filters
        if 'from_date' in filters:
            from_date = filters['from_date']
            if isinstance(from_date, (datetime, type(from_date))):
                validated_filters['from_date'] = from_date
            else:
                errors.append(f"Invalid from_date format: {from_date}")
                
        if 'to_date' in filters:
            to_date = filters['to_date']
            if isinstance(to_date, (datetime, type(to_date))):
                validated_filters['to_date'] = to_date
            else:
                errors.append(f"Invalid to_date format: {to_date}")
        
        # Validate date range - NULL-safe comparison
        if ('from_date' in validated_filters and 'to_date' in validated_filters and
            validated_filters['from_date'] is not None and validated_filters['to_date'] is not None):
            try:
                if validated_filters['from_date'] > validated_filters['to_date']:
                    errors.append("From date cannot be after to date")
                    # Swap dates to fix the issue
                    validated_filters['from_date'], validated_filters['to_date'] = \
                        validated_filters['to_date'], validated_filters['from_date']
            except TypeError as e:
                errors.append(f"Date comparison error: {e}")
                logger.error(f"Date comparison error in filter validation: {e}")
        
        # Validate product filter
        if 'product_name' in filters:
            product = filters['product_name']
            if product and product != "Minden termék":
                validated_filters['product_name'] = str(product).strip()
        
        # Validate search term
        if 'search_term' in filters:
            search = filters['search_term']
            if search and isinstance(search, str):
                cleaned_search = search.strip()
                if cleaned_search:
                    validated_filters['search_term'] = cleaned_search
        
        # Store validation errors
        st.session_state.filter_manager_state['validation_errors'] = errors
        
        if errors:
            logger.warning(f"Filter validation errors: {errors}")
        
        logger.info(f"Validated filters: {validated_filters}")
        return validated_filters
    
    def convert_to_api_params(self, ui_filters: Dict[str, Any]) -> Dict[str, Any]:
        """Convert UI filters to API parameters"""
        # This will be handled by APIParameterConverter
        # For now, return as-is with basic conversion
        api_params = {}
        
        if 'producer_id' in ui_filters:
            # JAVÍTÁS: Backend user_id paramétert vár producer szűréshez
            api_params['user_id'] = ui_filters['producer_id']
        
        if 'status' in ui_filters:
            if isinstance(ui_filters['status'], list):
                api_params['status'] = ','.join(ui_filters['status'])
            else:
                api_params['status'] = ui_filters['status']
        
        if 'from_date' in ui_filters:
            date_obj = ui_filters['from_date']
            if hasattr(date_obj, 'strftime'):
                api_params['date_from'] = date_obj.strftime('%Y-%m-%d')
        
        if 'to_date' in ui_filters:
            date_obj = ui_filters['to_date']
            if hasattr(date_obj, 'strftime'):
                api_params['date_to'] = date_obj.strftime('%Y-%m-%d')
        
        if 'product_name' in ui_filters:
            api_params['product_name'] = ui_filters['product_name']
        
        if 'search_term' in ui_filters:
            api_params['search'] = ui_filters['search_term']
        
        # Remove empty values
        cleaned_params = {k: v for k, v in api_params.items() 
                         if v is not None and v != '' and v != []}
        
        logger.info(f"Converted to API params: {cleaned_params}")
        return cleaned_params
    
    def should_refresh_data(self, current_filters: Dict[str, Any]) -> bool:
        """Determine if data needs to be refreshed"""
        state = st.session_state.filter_manager_state
        
        # Check cache timestamp (5 minute TTL)
        if state['cache_timestamp']:
            cache_age = datetime.now() - state['cache_timestamp']
            if cache_age.total_seconds() > 300:  # 5 minutes
                logger.info("Cache expired, refresh needed")
                return True
        else:
            logger.info("No cache timestamp, refresh needed")
            return True
        
        # Check if filters changed
        last_filters = state['last_filters']
        if current_filters != last_filters:
            logger.info(f"Filters changed: {last_filters} -> {current_filters}")
            return True
        
        logger.debug("No refresh needed")
        return False
    
    def update_filter_cache(self, filters: Dict[str, Any], timestamp: datetime):
        """Update filter cache with new values"""
        state = st.session_state.filter_manager_state
        state['last_filters'] = filters.copy()
        state['cache_timestamp'] = timestamp
        
        logger.info(f"Filter cache updated at {timestamp}")
    
    def clear_cache(self):
        """Clear all filter cache"""
        state = st.session_state.filter_manager_state
        state['last_filters'] = {}
        state['cache_timestamp'] = None
        state['filter_cache'] = {}
        state['validation_errors'] = []
        
        logger.info("Filter cache cleared")
    
    def get_validation_errors(self) -> list:
        """Get current validation errors"""
        return st.session_state.filter_manager_state.get('validation_errors', [])
    
    def has_active_filters(self, filters: Dict[str, Any]) -> bool:
        """Check if there are any active filters"""
        return bool(filters and any(
            v for v in filters.values() 
            if v is not None and v != [] and v != ''
        ))