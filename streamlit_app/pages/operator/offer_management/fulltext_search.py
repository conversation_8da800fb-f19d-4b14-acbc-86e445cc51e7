"""
Full-text search functionality for the offer management page.
Provides comprehensive search capabilities across all offer fields.
"""
import streamlit as st
import pandas as pd
import logging
import uuid
import re
from datetime import datetime

# Try multiple import paths with fallbacks
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    from streamlit_app.pages.operator.offer_management.enhanced_ui_components import (
        show_inline_error,
        show_inline_warning,
        show_inline_success,
        show_inline_info
    )
except ImportError:
    try:
        # Try module import with full path
        from pages.operator.offer_management.enhanced_ui_components import (
            show_inline_error,
            show_inline_warning,
            show_inline_success,
            show_inline_info
        )
        # Try regular app-relative import
        from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
    except ImportError:
        try:
            # Try direct local import
            from enhanced_ui_components import (
                show_inline_error,
                show_inline_warning,
                show_inline_success,
                show_inline_info
            )
            # Fallback formatting functions
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
        except ImportError:
            # Minimal implementations if all imports fail
            logging.warning("Could not import necessary modules in fulltext_search.py, using minimal implementations")
            
            def show_inline_error(message):
                st.error(message)
                
            def show_inline_warning(message):
                st.warning(message)
                
            def show_inline_success(message):
                st.success(message)
                
            def show_inline_info(message):
                st.info(message)
                
            format_status = lambda x: x
            format_datetime = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d %H:%M") if x else "-"
            format_date = lambda x: x if isinstance(x, str) else x.strftime("%Y-%m-%d") if x else "-"
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"

# Logger setup
logger = logging.getLogger(__name__)

# Define searchable fields with metadata
SEARCHABLE_FIELDS = {
    "id": {
        "display_name": "Azonosító",
        "field": "id",
        "weight": 5,  # Higher weight for direct ID matches
        "formatter": str
    },
    "status": {
        "display_name": "Státusz",
        "field": "status",
        "weight": 3,
        "formatter": format_status
    },
    "delivery_date": {
        "display_name": "Beszállítás dátuma",
        "field": "delivery_date",
        "weight": 2,
        "formatter": format_date
    },
    "product_name": {
        "display_name": "Termék",
        "field": "product_type.name",
        "weight": 4,
        "formatter": str
    },
    "quantity_in_kg": {
        "display_name": "Mennyiség",
        "field": "quantity_in_kg",
        "weight": 2,
        "formatter": format_quantity
    },
    "price": {
        "display_name": "Ár",
        "field": "price",
        "weight": 2,
        "formatter": format_price
    },
    "created_at": {
        "display_name": "Létrehozva",
        "field": "created_at",
        "weight": 1,
        "formatter": format_datetime
    },
    "producer_name": {
        "display_name": "Termelő",
        "field": "user.contact_name",
        "weight": 4,
        "formatter": str
    },
    "producer_email": {
        "display_name": "Termelő email",
        "field": "user.email",
        "weight": 3,
        "formatter": str
    },
    "description": {
        "display_name": "Megjegyzés",
        "field": "description",
        "weight": 4,
        "formatter": str
    }
}

def generate_unique_key(base_name, suffix=None):
    """
    Generate a unique key for Streamlit widgets.
    
    Args:
        base_name (str): Base name for the key
        suffix (str, optional): Optional suffix to add. Defaults to None.
        
    Returns:
        str: Unique key
    """
    if suffix:
        return f"{base_name}_{suffix}_{str(uuid.uuid4())[:8]}"
    return f"{base_name}_{str(uuid.uuid4())[:8]}"

def get_nested_value(obj, path):
    """
    Get a value from a nested object using a dot-separated path.
    
    Args:
        obj (dict): Object to extract value from
        path (str): Dot-separated path to the value
        
    Returns:
        any: The extracted value or None if not found
    """
    try:
        parts = path.split('.')
        value = obj
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            else:
                return None
        return value
    except (AttributeError, KeyError, TypeError):
        return None

def extract_all_searchable_text(offer):
    """
    Extract all searchable text from an offer into a single string.
    
    Args:
        offer (dict): Offer data
        
    Returns:
        str: Concatenated searchable text from the offer
    """
    texts = []
    
    # Extract values from all searchable fields
    for field_key, field_info in SEARCHABLE_FIELDS.items():
        field_path = field_info["field"]
        formatter = field_info["formatter"]
        
        # Get raw value
        value = get_nested_value(offer, field_path)
        
        # Format and add to texts if not None
        if value is not None:
            try:
                formatted_value = formatter(value)
                if formatted_value:
                    texts.append(str(formatted_value))
            except Exception as e:
                logger.warning(f"Error formatting field {field_path}: {e}")
    
    # Combine all texts
    return " ".join(texts)

def search_offers(offers, query, threshold=0.5):
    """
    Search offers using full-text search.
    
    Args:
        offers (list): List of offer dictionaries
        query (str): Search query
        threshold (float, optional): Matching score threshold. Defaults to 0.5.
        
    Returns:
        list: List of matching offers with scores
    """
    if not query or not offers:
        return offers
    
    # Normalize query
    query = query.lower().strip()
    query_terms = query.split()
    
    # Calculate scores for each offer
    results = []
    for offer in offers:
        # Extract searchable text for the offer
        offer_text = extract_all_searchable_text(offer).lower()
        
        # Calculate basic score
        score = 0
        matches = []
        
        # Field-specific matching with weights
        for field_key, field_info in SEARCHABLE_FIELDS.items():
            field_path = field_info["field"]
            weight = field_info["weight"]
            formatter = field_info["formatter"]
            
            # Get raw value
            value = get_nested_value(offer, field_path)
            
            # Skip if no value
            if value is None:
                continue
                
            # Format the value
            try:
                formatted_value = str(formatter(value)).lower()
            except Exception:
                formatted_value = str(value).lower()
            
            # Add score based on each query term match
            for term in query_terms:
                # Direct match on entire field (exact match)
                if term == formatted_value:
                    score += weight * 2.0
                    matches.append(f"Pontos egyezés: {field_info['display_name']}")
                
                # Substring match
                elif term in formatted_value:
                    term_position = formatted_value.find(term)
                    
                    # Word boundary match (better than substring)
                    if (term_position == 0 or not formatted_value[term_position-1].isalnum()) and \
                       (term_position + len(term) == len(formatted_value) or not formatted_value[term_position + len(term)].isalnum()):
                        score += weight * 1.5
                        matches.append(f"Szóegyezés: {field_info['display_name']}")
                    else:
                        score += weight * 1.0
                        matches.append(f"Részegyezés: {field_info['display_name']}")
                
                # Word starts with match
                elif any(word.startswith(term) for word in formatted_value.split()):
                    score += weight * 0.75
                    matches.append(f"Szókezdet: {field_info['display_name']}")
        
        # Normalize score based on number of query terms
        if query_terms:
            score = score / len(query_terms) / 10.0  # Normalize to 0-1 range
        
        # Add to results if score is above threshold
        if score >= threshold:
            # Store score and match info in the offer object
            offer_copy = offer.copy()
            offer_copy["_search_score"] = score
            offer_copy["_search_matches"] = matches
            results.append(offer_copy)
    
    # Sort by score (descending)
    results.sort(key=lambda x: x.get("_search_score", 0), reverse=True)
    
    return results

def highlight_matches(text, query):
    """
    Highlight query matches in text with HTML formatting.
    
    Args:
        text (str): Text to highlight
        query (str): Query to highlight
        
    Returns:
        str: Text with highlighted matches
    """
    if not query or not text:
        return text
    
    # Escape HTML special characters
    def escape_html(s):
        return s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
    
    escaped_text = escape_html(str(text))
    query_terms = query.lower().split()
    
    # Replace matches with highlighted versions
    for term in query_terms:
        pattern = re.compile(f"({re.escape(term)})", re.IGNORECASE)
        escaped_text = pattern.sub(r'<span style="background-color: #FFF9C4; font-weight: bold;">\1</span>', escaped_text)
    
    return escaped_text

def render_search_ui(on_search=None):
    """
    Render the search input UI.
    
    Args:
        on_search (function, optional): Callback when search is triggered. Defaults to None.
        
    Returns:
        str: Search query
    """
    # Create search UI
    search_col, button_col = st.columns([5, 1])
    
    with search_col:
        # Get previous search query from session state if it exists
        previous_query = st.session_state.get("fulltext_search_query", "")
        
        # Search input
        query = st.text_input(
            "Keresés az ajánlatokban",
            value=previous_query,
            placeholder="Írja be a keresett szöveget...",
            key=generate_unique_key("search_input")
        )
    
    with button_col:
        # Vertical alignment for the button
        st.write("")  # Empty space for alignment
        search_button = st.button(
            "🔍 Keresés",
            key=generate_unique_key("search_button")
        )
    
    # Only trigger search on button click or Enter key
    if search_button and query:
        # Store query in session state
        st.session_state["fulltext_search_query"] = query
        
        # Call callback if provided
        if on_search:
            on_search(query)
    
    return query

def render_search_results_info(results, query, total_count):
    """
    Render information about search results.
    
    Args:
        results (list): Search results
        query (str): Search query
        total_count (int): Total number of items before search
    """
    if not query:
        return
    
    results_count = len(results)
    
    if results_count == 0:
        show_inline_warning(f"Nincs találat a keresett kifejezésre: '{query}'")
    else:
        percentage = int(results_count / total_count * 100) if total_count > 0 else 0
        st.markdown(
            f"""
            <div style="margin: 10px 0; padding: 8px 12px; background-color: #E3F2FD; border-radius: 4px; 
                        border-left: 4px solid #1976D2;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <span style="font-weight: 500;">Keresési eredmények:</span> 
                        <span>'{query}'</span>
                    </div>
                    <div>
                        <span style="font-weight: 500;">{results_count} találat</span>
                        <span style="color: #666; margin-left: 5px;">({percentage}%)</span>
                    </div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

def render_match_indicators(offer, query):
    """
    Render visual indicators for search matches in an offer.
    
    Args:
        offer (dict): Offer with search matches
        query (str): Search query
        
    Returns:
        str: HTML for match indicators
    """
    search_score = offer.get("_search_score", 0)
    matches = offer.get("_search_matches", [])
    
    if not search_score or not matches:
        return ""
    
    # Create score indicator (0-5 dots)
    score_dots = int(min(search_score * 10, 5))
    score_html = '<span style="color: #1976D2;">' + "●" * score_dots + "○" * (5 - score_dots) + '</span>'
    
    # Create matches list (max 3 unique matches)
    unique_matches = []
    for match in matches:
        if match not in unique_matches:
            unique_matches.append(match)
        if len(unique_matches) >= 3:
            break
    
    matches_html = ", ".join(unique_matches[:3])
    if len(matches) > 3:
        matches_html += f" és még {len(matches) - 3} találat"
    
    # Combine score and matches
    return f"""
    <div style="font-size: 0.85em; margin: 5px 0;">
        <div>{score_html} Relevancia: {int(search_score * 100)}%</div>
        <div style="color: #666;">{matches_html}</div>
    </div>
    """

def apply_fulltext_search(offers):
    """
    Apply full-text search to offers and render UI.
    
    Args:
        offers (list): List of offer dictionaries
        
    Returns:
        list: Filtered offers matching the search query
    """
    # Store original offers count
    total_count = len(offers)
    
    # Create styled search UI
    st.markdown("""
    <style>
    .search-container {
        margin: 15px 0;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 8px;
        border: 1px solid #eee;
    }
    .fulltext-search-title {
        font-weight: 500;
        margin-bottom: 10px;
        color: #333;
    }
    </style>
    <div class="search-container">
        <div class="fulltext-search-title">Teljes szöveges keresés</div>
    """, unsafe_allow_html=True)
    
    # Render search input
    query = render_search_ui()
    
    # Close the container div
    st.markdown("</div>", unsafe_allow_html=True)
    
    # Apply search if query exists
    results = offers
    if query:
        # Perform search
        results = search_offers(offers, query)
        
        # Show search results info
        render_search_results_info(results, query, total_count)
    
    return results

# Test function
if __name__ == "__main__":
    st.set_page_config(page_title="Full-text Search Test", layout="wide")
    
    st.title("Full-text Search Test")
    
    # Create sample data
    sample_offers = [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-05-01",
            "created_at": "2025-04-01T10:00:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 500,
            "price": 350,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Bio minőségű alma a saját kertünkből."
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-05-10",
            "created_at": "2025-04-05T14:30:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 300,
            "price": 450,
            "user": {"contact_name": "Mezőgazda Márton", "email": "<EMAIL>"},
            "description": "Kiváló minőségű, zamatos körte."
        },
        {
            "id": 3,
            "status": "ACCEPTED_BY_USER",
            "delivery_date": "2025-05-05",
            "created_at": "2025-04-10T09:15:00",
            "product_type": {"name": "Alma"},
            "quantity_in_kg": 800,
            "price": 320,
            "user": {"contact_name": "Termelő Tamás", "email": "<EMAIL>"},
            "description": "Nagy mennyiségű étkezési alma, hosszú tárolhatósággal."
        },
        {
            "id": 4,
            "status": "FINALIZED",
            "delivery_date": "2025-04-20",
            "created_at": "2025-03-15T11:45:00",
            "product_type": {"name": "Szilva"},
            "quantity_in_kg": 250,
            "price": 550,
            "user": {"contact_name": "Gyümölcsös Gábor", "email": "<EMAIL>"},
            "description": "Befőzésre alkalmas szilva, magas cukortartalommal."
        },
        {
            "id": 5,
            "status": "CREATED",
            "delivery_date": "2025-06-01",
            "created_at": "2025-04-25T16:20:00",
            "product_type": {"name": "Körte"},
            "quantity_in_kg": 400,
            "price": 420,
            "user": {"contact_name": "Almás Anna", "email": "<EMAIL>"},
            "description": "Friss nyári körte, egyenesen a fáról."
        }
    ]
    
    # Apply search
    results = apply_fulltext_search(sample_offers)
    
    # Display results
    st.markdown("### Keresési eredmények")
    
    # Check if search results contain relevance info
    has_search_results = any("_search_score" in offer for offer in results)
    
    # Convert to DataFrame for display
    df_data = []
    for offer in results:
        row = {
            "ID": offer["id"],
            "Státusz": format_status(offer["status"]),
            "Beszállítás": format_date(offer["delivery_date"]),
            "Termék": offer["product_type"]["name"],
            "Mennyiség": f"{offer['quantity_in_kg']} kg",
            "Ár": f"{offer['price']} Ft/kg",
            "Termelő": offer["user"]["contact_name"],
            "Leírás": offer.get("description", "")
        }
        
        # Add relevance info if available
        if has_search_results and "_search_score" in offer:
            query = st.session_state.get("fulltext_search_query", "")
            row["Relevancia"] = render_match_indicators(offer, query)
        
        df_data.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(df_data)
    
    # Render table using Streamlit's dataframe
    st.write(df)