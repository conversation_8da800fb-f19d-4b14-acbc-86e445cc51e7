#!/usr/bin/env python3
"""
Refaktorált Ajánlatkezelő Alkalmazás
Modern UI valós API adatokkal - egyszerűsített architektúra
"""
import streamlit as st
import asyncio
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any, Optional

from .data_models import Offer, FilterParams, OfferStatistics
from .api_service import get_api_service
from .modern_ui_components import (
    ModernFilterPanel,
    StatisticsCard,
    OfferTable,
    MobileOfferCards
)

logger = logging.getLogger(__name__)

class OfferManagementApp:
    """
    Fő ajánlatkezelő alkalmazás osztály
    Egyszerűsített, tiszta architektúra valós API integrációval
    """
    
    def __init__(self):
        """Alkalmazás inicializálása"""
        self.api_service = get_api_service()
        self.filter_panel = ModernFilterPanel()
        self.stats_card = StatisticsCard()
        self.offer_table = OfferTable()
        self.mobile_cards = MobileOfferCards()
        
        # Állapot inicializálása
        self._init_session_state()
    
    def _init_session_state(self):
        """Session state inicializálása"""
        defaults = {
            'filter_panel_expanded': True,
            'device_type': 'desktop',
            'current_offers': [],
            'loading_offers': False,
            'last_filter_update': None
        }
        
        for key, value in defaults.items():
            if key not in st.session_state:
                st.session_state[key] = value
    
    def run(self):
        """
        Alkalmazás futtatása
        Fő belépési pont
        """
        try:
            # Autentikáció ellenőrzése
            if not self._check_authentication():
                return
            
            # Modern UI inicializálása
            self._inject_global_styles()
            self._render_page_header()
            
            # Eszköz típus meghatározása
            device_type = self._detect_device_type()
            st.session_state['device_type'] = device_type
            
            # Szűrők renderelése és aktív szűrők lekérése
            with st.spinner("Szűrők betöltése..."):
                filters = self.filter_panel.render()
            
            # Ajánlatok betöltése
            offers = self._load_offers_safe(filters)
            
            # Eredmények megjelenítése
            if offers:
                # Statisztikák
                self.stats_card.render(offers)
                
                # Eredmények számának megjelenítése
                self._render_results_header(len(offers))
                
                # Ajánlatok megjelenítése eszköz típus szerint
                if device_type == 'mobile':
                    self.mobile_cards.render(offers)
                else:
                    self.offer_table.render(offers)
            else:
                self._render_no_results()
            
        except Exception as e:
            logger.error(f"Error in main app run: {e}")
            st.error("Hiba történt az alkalmazás futtatásakor. Kérjük, frissítse az oldalt.")
            
            # Debug információ fejlesztői módban
            if st.session_state.get('dev_mode', False):
                st.exception(e)
    
    def _check_authentication(self) -> bool:
        """
        Autentikáció ellenőrzése
        
        Returns:
            bool: True ha autentikált, False egyébként
        """
        try:
            # Meglévő autentikációs függvények importálása
            from streamlit_app.utils.session import is_authenticated, get_current_user
            
            if not is_authenticated():
                st.warning("🔒 Kérjük, jelentkezzen be a rendszerbe az ajánlatok kezeléséhez!")
                st.info("Átirányítás a bejelentkezési oldalra...")
                st.stop()
                return False
            
            # Felhasználó jogosultságainak ellenőrzése
            current_user = get_current_user()
            if current_user.get('role') not in ['operator', 'admin']:
                st.error("❌ Nincs jogosultsága az ajánlatok kezeléséhez!")
                st.stop()
                return False
                
            return True
            
        except ImportError:
            # Fejlesztői mód - skip authentication
            logger.warning("Authentication modules not available - development mode")
            return True
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            st.error("Hiba az autentikáció során.")
            return False
    
    def _inject_global_styles(self):
        """Globális CSS stílusok injektálása"""
        st.markdown("""
        <style>
        /* Globális stílusok */
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .results-header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            margin: 1rem 0;
            overflow: hidden;
        }
        
        .results-header-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem 2rem;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px dashed #dee2e6;
            margin: 2rem 0;
        }
        
        /* Responsive adaptáció */
        @media (max-width: 767px) {
            .main-header {
                padding: 1.5rem 1rem;
                margin-bottom: 1rem;
            }
            
            .main-header h1 {
                font-size: 2rem !important;
            }
        }
        
        /* Animációk */
        @keyframes slideInFromTop {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def _render_page_header(self):
        """Oldal fejléc renderelése"""
        st.markdown("""
        <div class="main-header">
            <h1 style="margin: 0; font-size: 2.5rem; font-weight: 600;">🌾 Ajánlatok Kezelése</h1>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">Modern felület valós adatokkal</p>
        </div>
        """, unsafe_allow_html=True)
    
    def _detect_device_type(self) -> str:
        """
        Eszköz típus meghatározása
        
        Returns:
            str: 'mobile', 'tablet', vagy 'desktop'
        """
        # Egyszerű eszköz detektálás Streamlit viewport alapján
        # A valós implementáció JavaScript-et használna
        
        # Session state-ből vagy alapértelmezett
        screen_width = st.session_state.get('screen_width', 1200)
        
        if screen_width < 768:
            return 'mobile'
        elif screen_width < 992:
            return 'tablet'
        else:
            return 'desktop'
    
    def _load_offers_safe(self, filters: FilterParams) -> List[Offer]:
        """
        Biztonságos ajánlat betöltés hibakezeléssel
        
        Args:
            filters: Szűrési paraméterek
            
        Returns:
            List[Offer]: Betöltött ajánlatok
        """
        try:
            with st.spinner("Ajánlatok betöltése..."):
                # API paraméterek előkészítése
                api_params = filters.to_api_params()
                logger.info(f"Loading offers with params: {api_params}")
                
                # API hívás
                offers_data = self.api_service.get_offers_with_filters(api_params)
                
                # Adatok konvertálása Offer objektumokká
                offers = []
                for offer_data in offers_data:
                    try:
                        offer = Offer.from_api_response(offer_data)
                        offers.append(offer)
                    except Exception as e:
                        logger.warning(f"Could not parse offer data: {e}")
                        continue
                
                logger.info(f"Successfully loaded {len(offers)} offers")
                
                # Session state frissítése
                st.session_state['current_offers'] = offers
                st.session_state['last_filter_update'] = datetime.now()
                
                return offers
                
        except Exception as e:
            logger.error(f"Error loading offers: {e}")
            st.error("Hiba történt az ajánlatok betöltésekor. Kérjük, próbálja újra később.")
            
            # Fallback a session state-ből
            return st.session_state.get('current_offers', [])
    
    def _render_results_header(self, count: int):
        """
        Eredmények header renderelése
        
        Args:
            count: Ajánlatok száma
        """
        st.markdown(f"""
        <div class="results-header fade-in">
            <div class="results-header-content">
                <span>📋 Ajánlatok listája</span>
                <span>Találatok: {count} ajánlat</span>
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    def _render_no_results(self):
        """Üres eredmény megjelenítése"""
        st.markdown("""
        <div class="no-results fade-in">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
            <h3 style="color: #6c757d; margin-bottom: 0.5rem;">Nincsenek ajánlatok</h3>
            <p style="color: #868e96; margin: 0;">A megadott szűrési feltételekkel nem találhatók ajánlatok.</p>
            <p style="color: #868e96; margin-top: 0.5rem; font-size: 0.9rem;">
                Próbálja meg módosítani a szűrési feltételeket vagy törölje az összes szűrőt.
            </p>
        </div>
        """, unsafe_allow_html=True)

# Loading state decorator
def with_loading_state(message: str = "Betöltés..."):
    """
    Loading state decorator
    
    Args:
        message: Loading üzenet
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with st.spinner(message):
                return func(*args, **kwargs)
        return wrapper
    return decorator

# Főbb függvények a kompatibilitáshoz
def run_refactored_offer_management():
    """
    Refaktorált ajánlatkezelő futtatása
    Külső interfész a meglévő kódhoz
    """
    try:
        app = OfferManagementApp()
        app.run()
        return True
    except Exception as e:
        logger.error(f"Error running refactored app: {e}")
        st.error("Hiba a refaktorált alkalmazásban. Visszaváltás a klasszikus nézetre.")
        return False

def init_offer_management_state():
    """
    Ajánlatkezelés session state inicializálása
    Kompatibilitási függvény
    """
    defaults = {
        'filter_panel_expanded': True,
        'device_type': 'desktop',
        'current_offers': [],
        'producer_filter_om': None,
        'product_filter_om': None,
        'status_filter_om': None,
        'from_date_filter_om': None,
        'to_date_filter_om': None,
        'search_filter_om': '',
        'use_refactored_ui': True
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

# Hibakezelés és hibakeresés
def debug_offer_management_state():
    """Debug információk a fejlesztéshez"""
    if st.session_state.get('dev_mode', False):
        with st.expander("🐛 Debug információk", expanded=False):
            st.write("**Session State:**")
            debug_state = {k: v for k, v in st.session_state.items() if 'filter' in k or 'offer' in k}
            st.json(debug_state)
            
            st.write("**API Service Status:**")
            api_service = get_api_service()
            st.write(f"API Available: {api_service.api_available}")
            
            if st.button("🧹 Session State tisztítása"):
                keys_to_clear = [k for k in st.session_state.keys() if 'filter' in k or 'offer' in k]
                for key in keys_to_clear:
                    del st.session_state[key]
                st.success("Session state megtisztítva!")
                st.rerun()

# Tesztelési segédfüggvények
def test_api_integration():
    """API integráció tesztelése"""
    st.markdown("### 🧪 API Integráció Teszt")
    
    api_service = get_api_service()
    
    if st.button("🔄 API kapcsolat tesztelése"):
        with st.spinner("API tesztelése..."):
            try:
                # Producers teszt
                producers = api_service.get_producers_for_filter()
                st.success(f"✅ Producers: {len(producers)} betöltve")
                
                # Products teszt
                products = api_service.get_products_for_filter()
                st.success(f"✅ Products: {len(products)} betöltve")
                
                # Offers teszt
                offers = api_service.get_offers_with_filters({})
                st.success(f"✅ Offers: {len(offers)} betöltve")
                
                st.balloons()
                
            except Exception as e:
                st.error(f"❌ API hiba: {e}")
                logger.error(f"API test error: {e}")

if __name__ == "__main__":
    # Standalone futtatás
    st.set_page_config(
        page_title="Ajánlatok Kezelése - Refaktorált",
        page_icon="🌾",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # Development mode toggle
    if st.sidebar.checkbox("🔧 Fejlesztői mód"):
        st.session_state['dev_mode'] = True
        debug_offer_management_state()
        
        if st.sidebar.button("🧪 API Teszt"):
            test_api_integration()
    
    # Fő alkalmazás futtatása
    run_refactored_offer_management()