"""
Resizable Panels Component - CodePen inspired draggable panel system
"""
import streamlit as st
import streamlit.components.v1 as components
import uuid

def render_resizable_panel_system():
    """
    Render a complete resizable panel system with JavaScript
    """
    panel_id = f"resizable_panel_{str(uuid.uuid4())[:8]}"
    
    panel_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            /* Resizable Panel Container */
            .panel-container {{
                display: flex;
                gap: 1rem;
                height: 600px;
                width: 100%;
                position: relative;
            }}
            
            /* Individual Panel */
            .resizable-panel {{
                background: white;
                border-radius: 16px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                padding: 1.5rem;
                position: relative;
                overflow: auto;
                min-width: 200px;
                flex: 1;
                transition: box-shadow 0.3s ease;
            }}
            
            .resizable-panel:hover {{
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }}
            
            /* Resize Handle */
            .resize-handle {{
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 12px;
                cursor: col-resize;
                background: transparent;
                transition: background 0.2s ease;
                z-index: 10;
            }}
            
            .resize-handle:hover {{
                background: linear-gradient(90deg, transparent 0%, rgba(99, 102, 241, 0.1) 50%, rgba(99, 102, 241, 0.2) 100%);
            }}
            
            .resize-handle::before {{
                content: '⋮⋮';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #9CA3AF;
                font-size: 0.75rem;
                opacity: 0;
                transition: opacity 0.2s ease;
            }}
            
            .resize-handle:hover::before {{
                opacity: 1;
            }}
            
            /* Panel Header */
            .panel-header {{
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 1rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid #E5E7EB;
            }}
            
            .panel-icon {{
                font-size: 1.5rem;
            }}
            
            .panel-title {{
                font-size: 1.125rem;
                font-weight: 600;
                color: #1F2937;
            }}
            
            /* Panel Content */
            .panel-content {{
                color: #4B5563;
            }}
            
            /* Resize Indicator */
            .resize-indicator {{
                position: fixed;
                background: rgba(99, 102, 241, 0.1);
                border: 2px dashed #6366F1;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
                z-index: 1000;
            }}
            
            .resize-indicator.active {{
                opacity: 1;
            }}
            
            /* Panel States */
            .resizable-panel.resizing {{
                user-select: none;
                cursor: col-resize;
            }}
            
            .resizable-panel.collapsed {{
                min-width: 60px;
                max-width: 60px;
            }}
            
            .resizable-panel.collapsed .panel-content {{
                display: none;
            }}
            
            .resizable-panel.collapsed .panel-title {{
                display: none;
            }}
            
            /* Responsive */
            @media (max-width: 768px) {{
                .panel-container {{
                    flex-direction: column;
                    height: auto;
                }}
                
                .resizable-panel {{
                    min-height: 200px;
                    width: 100% !important;
                }}
                
                .resize-handle {{
                    display: none;
                }}
            }}
        </style>
    </head>
    <body>
        <div id="{panel_id}" class="panel-container">
            <!-- Panel 1: Offer Summary -->
            <div class="resizable-panel" data-panel-id="1">
                <div class="resize-handle"></div>
                <div class="panel-header">
                    <span class="panel-icon">📋</span>
                    <span class="panel-title">Ajánlat Összefoglaló</span>
                </div>
                <div class="panel-content">
                    <p>Húzd a jobb szélét a panel átméretezéséhez!</p>
                    <ul>
                        <li>Mennyiség: 100 kg</li>
                        <li>Ár: 850 Ft/kg</li>
                        <li>Státusz: Visszaigazolva</li>
                    </ul>
                </div>
            </div>
            
            <!-- Panel 2: Timeline -->
            <div class="resizable-panel" data-panel-id="2">
                <div class="resize-handle"></div>
                <div class="panel-header">
                    <span class="panel-icon">🕒</span>
                    <span class="panel-title">Idősor</span>
                </div>
                <div class="panel-content">
                    <div style="position: relative; padding-left: 2rem;">
                        <div style="position: absolute; left: 0; top: 0; width: 12px; height: 12px; background: #10B981; border-radius: 50%;"></div>
                        <div style="margin-bottom: 1rem;">
                            <strong>Létrehozva</strong><br>
                            <small>2024. 04. 27.</small>
                        </div>
                        <div style="position: absolute; left: 0; top: 2rem; width: 12px; height: 12px; background: #F59E0B; border-radius: 50%;"></div>
                        <div style="margin-top: 1rem;">
                            <strong>Visszaigazolva</strong><br>
                            <small>2024. 04. 28.</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Panel 3: Producer Info -->
            <div class="resizable-panel" data-panel-id="3">
                <div class="resize-handle"></div>
                <div class="panel-header">
                    <span class="panel-icon">👤</span>
                    <span class="panel-title">Termelő Adatok</span>
                </div>
                <div class="panel-content">
                    <p><strong>Nagy János</strong></p>
                    <p>BioFarm Kft.</p>
                    <p>📧 <EMAIL></p>
                    <p>📱 +36 30 123 4567</p>
                </div>
            </div>
        </div>
        
        <!-- Resize Indicator -->
        <div class="resize-indicator"></div>
        
        <script>
            class ResizablePanelSystem {{
                constructor(containerId) {{
                    this.container = document.getElementById(containerId);
                    this.panels = this.container.querySelectorAll('.resizable-panel');
                    this.resizeIndicator = document.querySelector('.resize-indicator');
                    this.activePanel = null;
                    this.startX = 0;
                    this.startWidth = 0;
                    this.minWidth = 200;
                    this.maxWidth = 800;
                    
                    this.init();
                }}
                
                init() {{
                    this.panels.forEach(panel => {{
                        const handle = panel.querySelector('.resize-handle');
                        if (handle) {{
                            handle.addEventListener('mousedown', (e) => this.startResize(e, panel));
                        }}
                        
                        // Double click to collapse/expand
                        const header = panel.querySelector('.panel-header');
                        header.addEventListener('dblclick', () => this.toggleCollapse(panel));
                    }});
                    
                    // Global mouse events
                    document.addEventListener('mousemove', (e) => this.resize(e));
                    document.addEventListener('mouseup', () => this.stopResize());
                    
                    // Prevent text selection during resize
                    document.addEventListener('selectstart', (e) => {{
                        if (this.activePanel) e.preventDefault();
                    }});
                }}
                
                startResize(e, panel) {{
                    e.preventDefault();
                    this.activePanel = panel;
                    this.startX = e.clientX;
                    this.startWidth = panel.offsetWidth;
                    
                    panel.classList.add('resizing');
                    document.body.style.cursor = 'col-resize';
                    
                    // Show resize indicator
                    const rect = panel.getBoundingClientRect();
                    this.resizeIndicator.style.left = rect.left + 'px';
                    this.resizeIndicator.style.top = rect.top + 'px';
                    this.resizeIndicator.style.width = rect.width + 'px';
                    this.resizeIndicator.style.height = rect.height + 'px';
                    this.resizeIndicator.classList.add('active');
                }}
                
                resize(e) {{
                    if (!this.activePanel) return;
                    
                    const deltaX = e.clientX - this.startX;
                    let newWidth = this.startWidth + deltaX;
                    
                    // Apply constraints
                    newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, newWidth));
                    
                    // Update panel width
                    this.activePanel.style.width = newWidth + 'px';
                    this.activePanel.style.flex = 'none';
                    
                    // Update resize indicator
                    this.resizeIndicator.style.width = newWidth + 'px';
                }}
                
                stopResize() {{
                    if (!this.activePanel) return;
                    
                    this.activePanel.classList.remove('resizing');
                    this.resizeIndicator.classList.remove('active');
                    document.body.style.cursor = '';
                    this.activePanel = null;
                }}
                
                toggleCollapse(panel) {{
                    panel.classList.toggle('collapsed');
                    
                    if (panel.classList.contains('collapsed')) {{
                        panel.style.width = '60px';
                        panel.style.flex = 'none';
                    }} else {{
                        panel.style.width = '';
                        panel.style.flex = '1';
                    }}
                }}
                
                // Save panel state to localStorage
                savePanelState() {{
                    const state = {{}};
                    this.panels.forEach(panel => {{
                        const id = panel.dataset.panelId;
                        state[id] = {{
                            width: panel.style.width || 'auto',
                            collapsed: panel.classList.contains('collapsed')
                        }};
                    }});
                    localStorage.setItem('resizablePanelState', JSON.stringify(state));
                }}
                
                // Restore panel state from localStorage
                restorePanelState() {{
                    const savedState = localStorage.getItem('resizablePanelState');
                    if (!savedState) return;
                    
                    try {{
                        const state = JSON.parse(savedState);
                        this.panels.forEach(panel => {{
                            const id = panel.dataset.panelId;
                            if (state[id]) {{
                                if (state[id].width !== 'auto') {{
                                    panel.style.width = state[id].width;
                                    panel.style.flex = 'none';
                                }}
                                if (state[id].collapsed) {{
                                    panel.classList.add('collapsed');
                                }}
                            }}
                        }});
                    }} catch (e) {{
                        console.error('Failed to restore panel state:', e);
                    }}
                }}
            }}
            
            // Initialize the resizable panel system
            const panelSystem = new ResizablePanelSystem('{panel_id}');
            
            // Restore saved state
            panelSystem.restorePanelState();
            
            // Save state on window unload
            window.addEventListener('beforeunload', () => {{
                panelSystem.savePanelState();
            }});
            
            // Add some animation on load
            setTimeout(() => {{
                document.querySelectorAll('.resizable-panel').forEach((panel, index) => {{
                    panel.style.opacity = '0';
                    panel.style.transform = 'translateY(20px)';
                    panel.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {{
                        panel.style.opacity = '1';
                        panel.style.transform = 'translateY(0)';
                    }}, index * 100);
                }});
            }}, 100);
        </script>
    </body>
    </html>
    """
    
    components.html(panel_html, height=650, scrolling=False)


def create_resizable_panel(title, icon, content, panel_id=None):
    """
    Create a single resizable panel
    
    Args:
        title (str): Panel title
        icon (str): Emoji icon
        content (str): HTML content for the panel
        panel_id (str): Unique panel identifier
    
    Returns:
        str: HTML for the panel
    """
    if not panel_id:
        panel_id = str(uuid.uuid4())[:8]
    
    return f"""
    <div class="resizable-panel" data-panel-id="{panel_id}">
        <div class="resize-handle"></div>
        <div class="panel-header">
            <span class="panel-icon">{icon}</span>
            <span class="panel-title">{title}</span>
        </div>
        <div class="panel-content">
            {content}
        </div>
    </div>
    """