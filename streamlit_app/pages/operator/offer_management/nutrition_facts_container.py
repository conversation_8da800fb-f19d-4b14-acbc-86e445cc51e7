"""
Nutrition Facts stílusú DetailContainer komponens
A CodePen nutrition facts design alapján adaptált ajánlat megjelenítéshez
"""
import streamlit as st
import streamlit.components.v1 as components
import uuid
import logging
import html

# Import our universal HTML renderer
try:
    from .html_renderer import render_html, render_css
except ImportError:
    # Fallback to direct implementation
    def render_html(html_content, height=None, key=None, scrolling=True):
        """Fallback HTML renderer"""
        try:
            if hasattr(st, 'html'):
                st.html(html_content)
                return True
        except:
            pass
        
        if height is None:
            height = 700
        components.html(html_content, height=height, scrolling=scrolling)
        return False

logger = logging.getLogger(__name__)

class NutritionFactsContainer:
    """
    Nutrition Facts stílusú konténer komponens
    
    A nutrition facts label design-ját adaptálja ajánlat adatok megjelenítéséhez.
    Tiszta, táblázatos, professzionális megjelen<PERSON>.
    """
    
    def __init__(self, title="<PERSON>j<PERSON>lat Részletek", subtitle="", icon="📋", key=None):
        """
        Inicializálja a nutrition facts stílusú konténert.
        
        Args:
            title (str): Főcím (pl. "Ajánlat Részletek")
            subtitle (str): Alcím (pl. "Ajánlat #9")
            icon (str): Emoji ikon
            key (str, optional): Egyedi azonosító
        """
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.key = key or f"nutrition_facts_{str(uuid.uuid4())[:8]}"
        
        # HTML escape a biztonság kedvéért
        self.safe_title = html.escape(str(title))
        self.safe_subtitle = html.escape(str(subtitle))
        self.safe_icon = html.escape(str(icon))
    
    def render(self, offer_data, debug_mode=False):
        """
        Nutrition facts stílusú ajánlat megjelenítés
        
        Args:
            offer_data (dict): Az ajánlat adatai
            debug_mode (bool): Debug mód engedélyezése
        """
        try:
            if debug_mode:
                st.checkbox(f"🔍 Debug {self.title}", key=f"debug_{self.key}", value=True)
                with st.expander("Debug Info", expanded=False):
                    st.write("**Offer Data Keys:**", list(offer_data.keys()) if offer_data else [])
                    st.write("**Container Key:**", self.key)
                    st.write("**Raw Offer Data:**", offer_data)
            
            # CSS injektálás
            self._inject_nutrition_facts_css()
            
            # Nutrition facts stílusú HTML generálás
            self._render_nutrition_facts_html(offer_data)
            
        except Exception as e:
            logger.error(f"Hiba a NutritionFactsContainer rendereléskor: {str(e)}")
            st.error(f"❌ Hiba a konténer megjelenítésekor: {str(e)}")
            if debug_mode:
                st.exception(e)
    
    def _inject_nutrition_facts_css(self):
        """Nutrition facts stílusú CSS injektálás"""
        try:
            container_id = self.key.replace('-', '_')
            
            css = f"""
            <style>
            .offer-facts-{container_id} {{
                border: 2px solid #000;
                margin: 20px auto;
                width: 100%;
                max-width: 400px;
                padding: 0.75rem;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 0.9rem;
                line-height: 1.4;
                background: white;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }}
            
            .offer-facts-{container_id} table {{
                border-collapse: collapse;
                width: 100%;
            }}
            
            .offer-facts__title-{container_id} {{
                font-weight: bold;
                font-size: 1.8rem;
                margin: 0 0 0.25rem 0;
                text-align: left;
                display: flex;
                align-items: center;
            }}
            
            .offer-facts__title-{container_id} .icon {{
                margin-right: 10px;
                font-size: 1.5rem;
            }}
            
            .offer-facts__header-{container_id} {{
                border-bottom: 8px solid #000;
                padding: 0 0 0.5rem 0;
                margin: 0 0 0.75rem 0;
            }}
            
            .offer-facts__header-{container_id} p {{
                margin: 0.2rem 0;
                font-size: 0.85rem;
                color: #333;
            }}
            
            .offer-facts__table-{container_id} {{
                width: 100%;
            }}
            
            .offer-facts__table-{container_id} thead tr th,
            .offer-facts__table-{container_id} thead tr td {{
                border: 0;
            }}
            
            .offer-facts__table-{container_id} th,
            .offer-facts__table-{container_id} td {{
                font-weight: normal;
                text-align: left;
                padding: 0.4rem 0;
                border-top: 1px solid #000;
                white-space: nowrap;
            }}
            
            .offer-facts__table-{container_id} td:last-child {{
                text-align: right;
                font-weight: bold;
            }}
            
            .offer-facts__table-{container_id} .blank-cell {{
                width: 1rem;
                border-top: 0;
            }}
            
            .offer-facts__table-{container_id} .thick-row th,
            .offer-facts__table-{container_id} .thick-row td {{
                border-top-width: 4px;
                border-top-color: #000;
            }}
            
            .offer-facts__table-{container_id} .thick-end {{
                border-bottom: 8px solid #000;
            }}
            
            .offer-facts__table-{container_id} .thin-end {{
                border-bottom: 1px solid #000;
            }}
            
            .small-info-{container_id} {{
                font-size: 0.75rem;
                color: #666;
            }}
            
            .status-badge-{container_id} {{
                display: inline-block;
                padding: 0.3rem 0.6rem;
                border-radius: 15px;
                font-size: 0.8rem;
                font-weight: bold;
                margin: 0.5rem 0;
            }}
            
            .status-created {{
                background-color: #E3F2FD;
                color: #1976D2;
                border: 1px solid #BBDEFB;
            }}
            
            .status-confirmed {{
                background-color: #FFF3E0;
                color: #F57C00;
                border: 1px solid #FFE0B2;
            }}
            
            .status-accepted {{
                background-color: #E8F5E8;
                color: #2E7D32;
                border: 1px solid #C8E6C9;
            }}
            
            .status-rejected {{
                background-color: #FFEBEE;
                color: #D32F2F;
                border: 1px solid #FFCDD2;
            }}
            
            .status-finalized {{
                background-color: #E8F5E8;
                color: #2E7D32;
                border: 1px solid #C8E6C9;
            }}
            
            /* Mobil optimalizálás */
            @media (max-width: 768px) {{
                .offer-facts-{container_id} {{
                    max-width: 100%;
                    margin: 10px;
                    padding: 0.5rem;
                    font-size: 0.8rem;
                }}
                
                .offer-facts__title-{container_id} {{
                    font-size: 1.4rem;
                }}
                
                .offer-facts__table-{container_id} th,
                .offer-facts__table-{container_id} td {{
                    padding: 0.3rem 0;
                }}
            }}
            </style>
            """
            
            # CSS injection - always use markdown for better compatibility
            st.markdown(css, unsafe_allow_html=True)
            
        except Exception as e:
            logger.warning(f"CSS injektálás hiba: {str(e)}")
    
    def _render_nutrition_facts_html(self, offer_data):
        """Nutrition facts stílusú HTML renderelés"""
        try:
            container_id = self.key.replace('-', '_')
            
            # Adatok kinyerése és biztonságos escape-elés
            offer_id = offer_data.get('id', 'N/A')
            status = offer_data.get('status', 'UNKNOWN')
            status_display = offer_data.get('status_display', status)
            
            # Termelő adatok
            producer = offer_data.get('user', {})
            producer_name = html.escape(str(producer.get('contact_name', 'N/A')))
            company_name = html.escape(str(producer.get('company_name', 'N/A')))
            
            # Termék adatok
            product = offer_data.get('product_type', {})
            product_name = html.escape(str(product.get('name', 'N/A')))
            
            # Mennyiség és ár
            quantity = offer_data.get('quantity_in_kg', 0)
            price = offer_data.get('price', 0)
            confirmed_quantity = offer_data.get('confirmed_quantity', '')
            confirmed_price = offer_data.get('confirmed_price', '')
            
            # Dátumok
            created_at = self._format_date(offer_data.get('created_at', ''))
            delivery_date = self._format_date(offer_data.get('delivery_date', ''))
            
            # Státusz badge osztály
            status_class = self._get_status_class(status)
            
            # HTML generálás
            html_content = f"""
            <section class="offer-facts-{container_id}">
                <header class="offer-facts__header-{container_id}">
                    <h1 class="offer-facts__title-{container_id}">
                        <span class="icon">{self.safe_icon}</span>
                        {self.safe_title}
                    </h1>
                    <p><strong>Ajánlat #{offer_id}</strong></p>
                    <p>{company_name}</p>
                    <div class="status-badge-{container_id} {status_class}">
                        {status_display}
                    </div>
                </header>
                
                <table class="offer-facts__table-{container_id}">
                    <thead>
                        <tr>
                            <th colspan="3" class="small-info-{container_id}">
                                Ajánlat információk
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th colspan="2">
                                <b>Termék</b>
                            </th>
                            <td>
                                {product_name}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2">
                                <b>Termelő</b>
                            </th>
                            <td>
                                {producer_name}
                            </td>
                        </tr>
                        <tr class="thick-row">
                            <td colspan="3" class="small-info-{container_id}">
                                <b>Mennyiség és árazás</b>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2">
                                <b>Mennyiség</b>
                            </th>
                            <td>
                                <b>{self._format_quantity(quantity)} kg</b>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2">
                                <b>Egységár</b>
                            </th>
                            <td>
                                <b>{self._format_price(price)}/kg</b>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2">
                                <b>Összérték</b>
                            </th>
                            <td>
                                <b>{self._format_price(quantity * price if quantity and price else 0)}</b>
                            </td>
                        </tr>
            """
            
            # Visszaigazolt adatok, ha vannak
            if confirmed_quantity or confirmed_price:
                html_content += f"""
                        <tr class="thick-row">
                            <td colspan="3" class="small-info-{container_id}">
                                <b>Visszaigazolt adatok</b>
                            </td>
                        </tr>
                """
                
                if confirmed_quantity:
                    html_content += f"""
                        <tr>
                            <td class="blank-cell"></td>
                            <th>Visszaig. mennyiség</th>
                            <td><b>{self._format_quantity(confirmed_quantity)} kg</b></td>
                        </tr>
                    """
                
                if confirmed_price:
                    html_content += f"""
                        <tr>
                            <td class="blank-cell"></td>
                            <th>Visszaig. ár</th>
                            <td><b>{self._format_price(confirmed_price)}/kg</b></td>
                        </tr>
                    """
                    
                    # Visszaigazolt összérték
                    if confirmed_quantity and confirmed_price:
                        confirmed_total = float(confirmed_quantity) * float(confirmed_price)
                        html_content += f"""
                        <tr>
                            <td class="blank-cell"></td>
                            <th>Visszaig. összérték</th>
                            <td><b>{self._format_price(confirmed_total)}</b></td>
                        </tr>
                        """
            
            # Dátumok szekció
            html_content += f"""
                        <tr class="thick-row">
                            <td colspan="3" class="small-info-{container_id}">
                                <b>Fontos dátumok</b>
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2">
                                <b>Létrehozás</b>
                            </th>
                            <td>
                                {created_at}
                            </td>
                        </tr>
                        <tr class="thin-end">
                            <th colspan="2">
                                <b>Beszállítás</b>
                            </th>
                            <td>
                                {delivery_date}
                            </td>
                        </tr>
                    </tbody>
                </table>
            """
            
            # Kapcsolattartó információk
            phone = producer.get('phone_number') or producer.get('phone')  # Mindkét mezőt ellenőrizzük
            if producer.get('email') or phone:
                html_content += f"""
                <table class="offer-facts__table-{container_id}">
                    <tbody>
                        <tr class="thick-row">
                            <td colspan="3" class="small-info-{container_id}">
                                <b>Kapcsolattartó</b>
                            </td>
                        </tr>
                """
                
                if producer.get('email'):
                    email = producer.get('email', '')
                    html_content += f"""
                        <tr>
                            <th colspan="2">Email</th>
                            <td>
                                <a href="mailto:{email}" 
                                   style="color: #1976D2; text-decoration: none;">
                                    {email}
                                </a>
                            </td>
                        </tr>
                    """
                
                if phone:
                    html_content += f"""
                        <tr class="thin-end">
                            <th colspan="2">Telefon</th>
                            <td>
                                <a href="tel:{phone}"
                                   style="color: #1976D2; text-decoration: none;">
                                    {phone}
                                </a>
                            </td>
                        </tr>
                    """
                
                html_content += """
                    </tbody>
                </table>
                """
            
            # Megjegyzés, ha van
            if offer_data.get('note'):
                note_text = html.escape(str(offer_data.get('note', '')))
                html_content += f"""
                <p class="small-info-{container_id}" style="margin-top: 1rem; padding: 0.5rem; background: #f5f5f5; border-radius: 4px;">
                    <strong>Megjegyzés:</strong> {note_text}
                </p>
                """
            
            html_content += """
            </section>
            """
            
            # HTML renderelés
            # Use universal renderer
            render_html(html_content, height=600)
            
        except Exception as e:
            logger.error(f"HTML renderelés hiba: {str(e)}")
            st.error(f"❌ HTML renderelés sikertelen: {str(e)}")
    
    def _get_status_class(self, status):
        """Státusz CSS osztály meghatározása"""
        status_map = {
            'CREATED': 'status-created',
            'CONFIRMED_BY_COMPANY': 'status-confirmed',
            'ACCEPTED_BY_USER': 'status-accepted',
            'REJECTED_BY_USER': 'status-rejected',
            'FINALIZED': 'status-finalized'
        }
        return status_map.get(status, 'status-created')
    
    def _format_quantity(self, value):
        """Mennyiség formázása"""
        try:
            if not value:
                return "0"
            return f"{float(value):,.2f}"
        except (ValueError, TypeError):
            return str(value) if value else "0"
    
    def _format_price(self, value):
        """Ár formázása"""
        try:
            if not value:
                return "0 Ft"
            return f"{float(value):,.0f} Ft"
        except (ValueError, TypeError):
            return f"{value} Ft" if value else "0 Ft"
    
    def _format_date(self, value):
        """Dátum formázása"""
        try:
            if not value:
                return "-"
            if isinstance(value, str):
                from datetime import datetime
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d")
            return value.strftime("%Y-%m-%d")
        except (ValueError, AttributeError):
            return str(value) if value else "-"


# Használati példa és wrapper függvények
def create_offer_facts_panel(offer_data, title="Ajánlat Részletek", icon="📋", debug_mode=False):
    """
    Egyszerű wrapper az ajánlat facts panel létrehozásához
    
    Args:
        offer_data (dict): Az ajánlat adatai
        title (str): Panel címe
        icon (str): Emoji ikon
        debug_mode (bool): Debug mód
    """
    # Fallback egyszerű renderelés, ha HTML problémák vannak
    use_simple = st.sidebar.checkbox("Use Simple Facts Panel (No HTML)", value=True, key="simple_facts_fallback")  # Alapértelmezetten bekapcsolva
    
    if use_simple:
        _render_simple_facts_panel(offer_data, title, icon)
    else:
        # HTML verziót csak akkor használjuk, ha kifejezetten kérik
        st.warning("⚠️ HTML verzió - Ha HTML kódokat látsz, kapcsold be a 'Simple Facts Panel' opciót!")
        container = NutritionFactsContainer(title=title, icon=icon)
        container.render(offer_data, debug_mode=debug_mode)


def _render_simple_facts_panel(offer_data, title="Ajánlat Részletek", icon="📋"):
    """Egyszerű facts panel HTML nélkül - Nutrition Facts stílusban"""
    
    # Nutrition Facts stílusú fejléc
    st.markdown(f"## {icon} {title}")
    
    # Ajánlat azonosító és cég
    offer_id = offer_data.get('id', 'N/A')
    producer = offer_data.get('user', {})
    company_name = producer.get('company_name', 'N/A')
    
    st.markdown(f"**Ajánlat #{offer_id}**")
    st.markdown(f"{company_name}")
    
    # Státusz badge-jellegű megjelenítés
    status_display = offer_data.get('status_display', offer_data.get('status', 'N/A'))
    
    if status_display == 'Létrehozva':
        st.info(f"🔵 {status_display}")
    elif status_display == 'Cég által visszaigazolva':
        st.warning(f"🟡 {status_display}")
    elif status_display == 'Elfogadva':
        st.success(f"🟢 {status_display}")
    elif status_display == 'Elutasítva':
        st.error(f"🔴 {status_display}")
    elif status_display == 'Véglegesítve':
        st.success(f"✅ {status_display}")
    else:
        st.info(f"⚪ {status_display}")
    
    st.markdown("---")  # Elválasztó vonal
    
    # Alapadatok táblázatos megjelenítés
    with st.container():
        st.markdown("**Ajánlat információk**")
        
        # Termék és termelő információ
        product = offer_data.get('product_type', {})
        st.write(f"**Termék**: {product.get('name', 'N/A')}")
        st.write(f"**Termelő**: {producer.get('contact_name', 'N/A')}")
        
        st.markdown("---")
        st.markdown("**Mennyiség és árazás**")
        
        # Mennyiség és ár táblázatos megjelenítés
        quantity = offer_data.get('quantity_in_kg', 0)
        price = offer_data.get('price', 0)
        
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write("**Mennyiség**")
        with col2:
            st.write(f"**{quantity} kg**")
        
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write("**Egységár**")
        with col2:
            st.write(f"**{price:,.0f} Ft/kg**")
        
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write("**Összérték**")
        with col2:
            if quantity and price:
                total = quantity * price
                st.write(f"**{total:,.0f} Ft**")
            else:
                st.write("**0 Ft**")
        
        # Visszaigazolt adatok
        if offer_data.get('confirmed_quantity') or offer_data.get('confirmed_price'):
            st.markdown("---")
            st.markdown("**Visszaigazolt adatok**")
            
            if offer_data.get('confirmed_quantity'):
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.write("Visszaig. mennyiség")
                with col2:
                    st.write(f"**{offer_data.get('confirmed_quantity')} kg**")
            
            if offer_data.get('confirmed_price'):
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.write("Visszaig. ár")
                with col2:
                    st.write(f"**{offer_data.get('confirmed_price'):,.0f} Ft/kg**")
            
            # Visszaigazolt összérték
            if offer_data.get('confirmed_quantity') and offer_data.get('confirmed_price'):
                confirmed_total = float(offer_data.get('confirmed_quantity')) * float(offer_data.get('confirmed_price'))
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.write("Visszaig. összérték")
                with col2:
                    st.write(f"**{confirmed_total:,.0f} Ft**")
        
        # Dátumok
        st.markdown("---")
        st.markdown("**Fontos dátumok**")
        
        # Létrehozás dátuma
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write("**Létrehozás**")
        with col2:
            created_at = offer_data.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime("%Y-%m-%d")
                    st.write(formatted_date)
                except:
                    st.write(created_at)
            else:
                st.write("-")
        
        # Beszállítás dátuma
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write("**Beszállítás**")
        with col2:
            delivery_date = offer_data.get('delivery_date', '')
            st.write(delivery_date if delivery_date else '-')
        
        # Kapcsolattartó
        phone = producer.get('phone_number') or producer.get('phone')
        if producer.get('email') or phone:
            st.markdown("---")
            st.markdown("**Kapcsolattartó**")
            
            if producer.get('email'):
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.write("Email")
                with col2:
                    st.markdown(f"[{producer.get('email')}](mailto:{producer.get('email')})")
            
            if phone:
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.write("Telefon")
                with col2:
                    st.markdown(f"[{phone}](tel:{phone})")
        
        # Megjegyzés
        if offer_data.get('note'):
            st.markdown("---")
            st.markdown("**Megjegyzés**")
            st.info(offer_data.get('note'))


def create_producer_facts_panel(offer_data):
    """Termelő információk facts panel"""
    producer_data = offer_data.get('user', {})
    if not producer_data:
        st.info("Nincs termelő adat")
        return
    
    # Átalakítjuk a termelő adatokat nutrition facts formátumra
    facts_data = {
        'id': f"Termelő #{producer_data.get('id', 'N/A')}",
        'status': 'ACTIVE' if producer_data.get('is_active') else 'INACTIVE',
        'status_display': 'Aktív' if producer_data.get('is_active') else 'Inaktív',
        'user': {
            'contact_name': producer_data.get('contact_name', 'N/A'),
            'company_name': producer_data.get('company_name', 'N/A'),
            'email': producer_data.get('email', ''),
            'phone_number': producer_data.get('phone_number', '')
        },
        'product_type': {'name': 'Termelő profil'},
        'quantity_in_kg': 0,
        'price': 0,
        'created_at': producer_data.get('created_at', ''),
        'delivery_date': ''
    }
    
    container = NutritionFactsContainer(title="Termelő Adatok", icon="👨‍🌾")
    container.render(facts_data)


def test_nutrition_facts_container():
    """Tesztelő függvény"""
    st.markdown("## 🧪 Nutrition Facts Container Teszt")
    
    # Teszt adatok
    test_offer_data = {
        'id': 9,
        'status': 'ACCEPTED_BY_USER',
        'status_display': 'Elfogadva',
        'quantity_in_kg': 10.0,
        'price': 665.0,
        'confirmed_quantity': 10.0,
        'confirmed_price': 665.0,
        'created_at': '2025-04-27T05:30:39.602911',
        'delivery_date': '2025-04-28',
        'note': 'Kiváló minőségű paprika, természetes termesztésből.',
        'user': {
            'contact_name': 'Kiss Péter',
            'company_name': 'Termelői Gazdaság Kft.',
            'email': '<EMAIL>',
            'phone_number': '+36209876543'
        },
        'product_type': {
            'name': 'Pritamin paprika'
        }
    }
    
    # Teszt renderelés
    create_offer_facts_panel(test_offer_data)


if __name__ == "__main__":
    test_nutrition_facts_container()