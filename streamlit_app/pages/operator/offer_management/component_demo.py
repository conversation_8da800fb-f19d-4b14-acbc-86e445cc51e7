"""
Demo page showcasing the new React-like components.
This serves as both a test and documentation for the component system.
"""
import streamlit as st
from datetime import date, timedelta
import logging

# Configure page
st.set_page_config(
    page_title="React-like Components Demo",
    page_icon="🔥",
    layout="wide"
)

# Import components
try:
    from react_like_components import (
        BaseComponent,
        create_component,
        with_props,
        ButtonComponent,
        StatCard
    )
    from modern_components import (
        ModernFilterPanel,
        OfferStatsCard,
        OfferCard,
        LoadingSpinner,
        ModernButton
    )
    components_available = True
except ImportError as e:
    st.error(f"Could not import components: {e}")
    components_available = False

if components_available:
    st.title("🔥 React-like Components Demo")
    st.markdown("---")
    
    # Functional Component Demo
    st.header("1. Functional Components")
    
    # Counter example
    @create_component
    def Counter(ctx):
        """Simple counter component demonstrating useState."""
        initial_count = ctx['props'].get('initial', 0)
        count, set_count = ctx['use_state'](initial_count)
        
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col1:
            if st.button("➖", key=f"{ctx['component_id']}_minus"):
                set_count(count - 1)
                
        with col2:
            st.markdown(f"<h2 style='text-align: center;'>{count}</h2>", unsafe_allow_html=True)
            
        with col3:
            if st.button("➕", key=f"{ctx['component_id']}_plus"):
                set_count(count + 1)
    
    # Use the counter
    st.subheader("Counter Component")
    counter = Counter()
    counter(initial=5)
    
    # Todo List Example
    @create_component
    def TodoList(ctx):
        """Todo list component demonstrating complex state management."""
        todos, set_todos = ctx['use_state']([])
        new_todo_text, set_new_todo_text = ctx['use_state']("")
        
        # Add todo function
        def add_todo():
            if new_todo_text.strip():
                new_todos = todos + [{"id": len(todos), "text": new_todo_text, "done": False}]
                set_todos(new_todos)
                set_new_todo_text("")
        
        # Toggle todo function  
        def toggle_todo(todo_id):
            new_todos = []
            for todo in todos:
                if todo["id"] == todo_id:
                    new_todos.append({**todo, "done": not todo["done"]})
                else:
                    new_todos.append(todo)
            set_todos(new_todos)
        
        # UI
        st.subheader("Todo List Component")
        
        # Add new todo
        col1, col2 = st.columns([3, 1])
        with col1:
            new_text = st.text_input(
                "New todo", 
                value=new_todo_text,
                key=f"{ctx['component_id']}_input",
                placeholder="Add a new todo..."
            )
            if new_text != new_todo_text:
                set_new_todo_text(new_text)
                
        with col2:
            if st.button("Add", key=f"{ctx['component_id']}_add"):
                add_todo()
        
        # Display todos
        for todo in todos:
            col1, col2 = st.columns([1, 10])
            with col1:
                if st.checkbox(
                    "", 
                    value=todo["done"],
                    key=f"{ctx['component_id']}_todo_{todo['id']}"
                ):
                    if not todo["done"]:
                        toggle_todo(todo["id"])
                else:
                    if todo["done"]:
                        toggle_todo(todo["id"])
                        
            with col2:
                text_style = "text-decoration: line-through; color: #999;" if todo["done"] else ""
                st.markdown(f"<span style='{text_style}'>{todo['text']}</span>", unsafe_allow_html=True)
        
        if not todos:
            st.info("No todos yet. Add one above!")
    
    # Use the todo list
    todo_list = TodoList()
    todo_list()
    
    st.markdown("---")
    
    # Modern Components Demo
    st.header("2. Modern Offer Management Components")
    
    # Stats Card Demo
    st.subheader("Statistics Card")
    sample_stats = {
        'total_count': 42,
        'total_quantity': 1250,
        'total_value': 287500,
        'avg_price': 230
    }
    
    stats_card = OfferStatsCard()
    stats_card(stats=sample_stats, title="📊 Ajánlatok statisztikái")
    
    # Filter Panel Demo
    st.subheader("Modern Filter Panel")
    
    def handle_demo_filters(filters):
        st.success(f"Filters changed: {filters}")
    
    demo_filters = {
        'producer': 'Kovács János',
        'statuses': ['CREATED', 'CONFIRMED_BY_COMPANY'],
        'from_date': date.today() - timedelta(days=30),
        'to_date': date.today(),
        'search': 'alma'
    }
    
    filter_panel = ModernFilterPanel(component_id="demo_filter_panel")
    filter_panel.render(
        title="🔍 Demo Szűrőpanel",
        filters=demo_filters,
        on_filter_change=handle_demo_filters
    )
    
    # Offer Cards Demo
    st.subheader("Offer Cards")
    
    sample_offers = [
        {
            'id': '1001',
            'user': {'name': 'Kovács János'},
            'product_type': {'name': 'Alma'},
            'quantity_in_kg': 500,
            'price': 220,
            'status': 'CREATED',
            'created_at': '2023-05-12'
        },
        {
            'id': '1002',
            'user': {'name': 'Szabó Péter'},
            'product_type': {'name': 'Szőlő'},
            'quantity_in_kg': 200,
            'price': 350,
            'status': 'CONFIRMED_BY_COMPANY',
            'created_at': '2023-05-13'
        },
        {
            'id': '1003',
            'user': {'name': 'Nagy István'},
            'product_type': {'name': 'Burgonya'},
            'quantity_in_kg': 550,
            'price': 180,
            'status': 'ACCEPTED_BY_USER',
            'created_at': '2023-05-14'
        }
    ]
    
    for idx, offer in enumerate(sample_offers):
        offer_card = OfferCard()
        offer_card(
            offer=offer,
            index=idx,
            on_click=lambda: st.balloons()
        )
    
    # Modern Buttons Demo
    st.subheader("Modern Buttons")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        primary_btn = ModernButton()
        primary_btn(
            label="Primary Button",
            variant="primary",
            icon="🚀",
            on_click=lambda: st.success("Primary clicked!")
        )
        
    with col2:
        secondary_btn = ModernButton()
        secondary_btn(
            label="Secondary Button", 
            variant="secondary",
            icon="⚙️",
            on_click=lambda: st.info("Secondary clicked!")
        )
        
    with col3:
        outline_btn = ModernButton()
        outline_btn(
            label="Outline Button",
            variant="outline", 
            icon="📝",
            on_click=lambda: st.warning("Outline clicked!")
        )
    
    # Loading Spinner Demo
    st.subheader("Loading Spinner")
    
    if st.button("Show Loading Spinner"):
        spinner = LoadingSpinner()
        spinner(message="Adatok betöltése...", size="large")
        import time
        time.sleep(2)
        st.success("Loading complete!")
    
    st.markdown("---")
    
    # Class-based Component Demo
    st.header("3. Class-based Components")
    
    # Custom class component
    class TemperatureConverter(BaseComponent):
        """Temperature converter component demonstrating class-based approach."""
        
        def render(self):
            celsius, set_celsius = self.use_state(0, 'celsius')
            
            st.subheader("Temperature Converter")
            
            # Celsius input
            new_celsius = st.number_input(
                "Celsius",
                value=float(celsius),
                key=f"{self.component_id}_celsius"
            )
            
            if new_celsius != celsius:
                set_celsius(new_celsius)
            
            # Conversions
            fahrenheit = (celsius * 9/5) + 32
            kelvin = celsius + 273.15
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Fahrenheit", f"{fahrenheit:.1f}°F")
            with col2:
                st.metric("Kelvin", f"{kelvin:.1f}K")
    
    # Use the temperature converter
    temp_converter = TemperatureConverter(component_id="temp_converter")
    temp_converter.render()
    
    st.markdown("---")
    
    # Component Lifecycle Demo
    st.header("4. Component Lifecycle & Hooks")
    
    @create_component
    def LifecycleDemo(ctx):
        """Demonstrates useEffect and component lifecycle."""
        count, set_count = ctx['use_state'](0)
        message, set_message = ctx['use_state']("Component mounted!")
        
        # Effect that runs on every render
        ctx['use_effect'](
            lambda: set_message(f"Rendered {count} times"),
            [count],  # Dependency array
            'count_effect'
        )
        
        # Effect that runs only once (like componentDidMount)
        ctx['use_effect'](
            lambda: st.info("Component mounted (this only shows once)"),
            [],  # Empty dependency array
            'mount_effect'
        )
        
        st.subheader("Lifecycle Demo")
        st.write(f"Count: {count}")
        st.write(f"Message: {message}")
        
        if st.button("Increment", key=f"{ctx['component_id']}_increment"):
            set_count(count + 1)
    
    # Use lifecycle demo
    lifecycle_demo = LifecycleDemo()
    lifecycle_demo()
    
    st.markdown("---")
    st.success("🎉 All React-like components are working correctly!")
    
    # Component source code
    with st.expander("📋 Component Source Code Examples"):
        st.markdown("### Counter Component")
        st.code('''
@create_component
def Counter(ctx):
    initial_count = ctx['props'].get('initial', 0)
    count, set_count = ctx['use_state'](initial_count)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col1:
        if st.button("➖", key=f"{ctx['component_id']}_minus"):
            set_count(count - 1)
            
    with col2:
        st.markdown(f"<h2 style='text-align: center;'>{count}</h2>", unsafe_allow_html=True)
        
    with col3:
        if st.button("➕", key=f"{ctx['component_id']}_plus"):
            set_count(count + 1)
        ''', language='python')
        
        st.markdown("### Class-based Component")
        st.code('''
class TemperatureConverter(BaseComponent):
    def render(self):
        celsius, set_celsius = self.use_state(0, 'celsius')
        
        new_celsius = st.number_input(
            "Celsius",
            value=float(celsius),
            key=f"{self.component_id}_celsius"
        )
        
        if new_celsius != celsius:
            set_celsius(new_celsius)
        
        fahrenheit = (celsius * 9/5) + 32
        kelvin = celsius + 273.15
        
        st.metric("Fahrenheit", f"{fahrenheit:.1f}°F")
        st.metric("Kelvin", f"{kelvin:.1f}K")
        ''', language='python')

else:
    st.error("Components are not available. Please check the imports.")

# Footer
st.markdown("---")
st.markdown("**React-like Components for Streamlit** - Developed for the Agricultural Product Management System")