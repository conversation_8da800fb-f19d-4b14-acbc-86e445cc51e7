"""
Tesztmodul a Modern Search Panel működésének demonstrálására.
Ez a fájl közvetlenül futtatható Streamlit-tel a komponens teszteléséhez.
"""
import streamlit as st
from datetime import datetime, timedelta
import logging

try:
    from streamlit_app.pages.operator.offer_management.modern_search_panel import (
        render_modern_search_panel_complete,
        inject_modern_search_styles
    )
except ImportError:
    try:
        from pages.operator.offer_management.modern_search_panel import (
            render_modern_search_panel_complete,
            inject_modern_search_styles
        )
    except ImportError:
        try:
            from modern_search_panel import (
                render_modern_search_panel_complete,
                inject_modern_search_styles
            )
        except ImportError:
            st.error("Nem sikerült importálni a modern_search_panel modult!")
            st.stop()

# Konfiguráljuk a naplózást
logging.basicConfig(level=logging.INFO)

# Oldal konfigurálása
st.set_page_config(
    page_title="Modern Search Panel Demo",
    page_icon="🔍",
    layout="wide"
)

# Demó fejléc
st.title("Modern Kártyás Keresőpanel Demó")
st.markdown("""
Ez a demó oldal a modern kártyás keresőpanel működését mutatja be, amely a TASK-1.1 feladat részeként készült.
A komponens a custom_css_framework-re épül, és a következő jellemzőket tartalmazza:

- ✅ Árnyékolt kártya dizájn
- ✅ Összecsukható panel funkció
- ✅ Vizuálisan megkülönböztetett címkék és szűrők
- ✅ Reszponzív elrendezés
- ✅ Előre definiált szűrők
- ✅ Gyorsszűrő opciók
- ✅ Aktív szűrők vizualizálása

Próbálja ki a különböző szűrési lehetőségeket és figyelje meg az interaktív viselkedést!
""")

# Szűrési eredmények megjelenítése
if "search_results" not in st.session_state:
    st.session_state.search_results = None

# Keresés kezelése
def handle_search(params):
    """
    Keresés eseménykezelő
    
    Args:
        params: Keresési paraméterek
    """
    # A valós alkalmazásban itt hívnánk API-t, ebben a demóban csak megjelenítjük a paramétereket
    st.session_state.search_results = params
    
    # Sikeres keresés üzenet
    st.success("Keresés sikeresen végrehajtva!")

# A modern keresőpanel renderelése
search_params = render_modern_search_panel_complete(
    title="Ajánlatok keresése",
    icon="🔍",
    collapsible=True,
    expanded=True,
    on_search=handle_search
)

# Elválasztó
st.markdown("---")

# Keresési eredmények megjelenítése
if st.session_state.search_results:
    st.subheader("Keresési eredmények")
    
    # Az eredmények formázása a demonstrációhoz
    formatted_results = {}
    for key, value in st.session_state.search_results.items():
        if value is not None and value != "":
            if isinstance(value, (datetime, )):
                formatted_value = value.strftime("%Y-%m-%d")
            else:
                formatted_value = str(value)
            
            formatted_results[key] = formatted_value
    
    # Ha vannak találatok
    if formatted_results:
        st.json(formatted_results)
        
        # Demonstrációs táblázat
        st.subheader("Találatok demó megjelenítése")
        
        # Példa találatok generálása a demóhoz
        demo_results = []
        for i in range(5):
            demo_results.append({
                "id": f"{1000 + i}",
                "producer": "Demo Producer Ltd.",
                "product": "Termék típus XYZ",
                "status": "CONFIRMED_BY_COMPANY",
                "quantity": f"{(i+1) * 100} kg",
                "created_at": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            })
        
        # Táblázat megjelenítése
        st.table(demo_results)
    else:
        st.info("Nincs találat a megadott szűrési feltételekkel.")
else:
    st.info("Használja a keresőpanelt a szűrések beállításához és az eredmények megjelenítéséhez.")

# Fejlesztői információk
with st.expander("Fejlesztői információk"):
    st.markdown("""
    **Modul:** modern_search_panel.py
    
    **Fő funkciók:**
    - `render_modern_search_panel_complete`: Teljes keresőpanel renderelése
    - `render_search_form`: Keresőűrlap renderelése
    - `render_status_filter`: Többszörös kiválasztást támogató státusz szűrő
    - `render_date_range_filter`: Fejlett dátum tartomány szűrő
    - `render_active_filter_badges`: Aktív szűrők megjelenítése címkékkel
    - `render_predefined_filters`: Előre definiált szűrőbeállítások
    
    **Függőségek:**
    - custom_css_framework.py: CSS komponensek és stílusok
    
    A komponens teljes mértékben moduláris és újrafelhasználható különböző kontextusokban is.
    """)
    
    # Aktuális szűrési paraméterek
    st.subheader("Aktuális szűrési paraméterek")
    st.write(search_params)