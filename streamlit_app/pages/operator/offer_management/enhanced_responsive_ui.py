"""
Továbbfejlesztett reszponzív design modul.

Ez a modul fejlett reszponzív UI funkciókat biztosít, amelyek automatikusan
alkalmazkodnak a különböző képernyőméretekhez és eszköztípusokhoz, valamint
gesztusvezérlési támogatást nyújtanak.
"""
import streamlit as st
import logging
from typing import Dict, Any, Optional, Callable, Tuple, List, Union
import re
import uuid
import json

# Az egyedi CSS keretrendszer importálása
try:
    from streamlit_app.pages.operator.offer_management.custom_css_framework import (
        inject_base_styles,
        BREAKPOINTS
    )
except ImportError:
    try:
        from pages.operator.offer_management.custom_css_framework import (
            inject_base_styles,
            BREAKPOINTS
        )
    except ImportError:
        try:
            from offer_management.custom_css_framework import (
                inject_base_styles,
                BREAKPOINTS
            )
        except ImportError:
            try:
                from custom_css_framework import (
                    inject_base_styles,
                    BREAKPOINTS
                )
            except ImportError:
                logging.error("Nem sikerült importálni a CSS keretrendszert")
                # <PERSON><PERSON><PERSON><PERSON> fallback
                def inject_base_styles():
                    pass
                BREAKPOINTS = {
                    "xs": "0px",      # Extra kicsi - mobilok
                    "sm": "576px",    # Kicsi - nagyobb mobilok
                    "md": "768px",    # Közepes - tabletek
                    "lg": "992px",    # Nagy - kis laptopok
                    "xl": "1200px",   # Extra nagy - asztali 
                    "2xl": "1400px",  # Extra extra nagy - nagy monitorok
                }

# Naplózás beállítása
logger = logging.getLogger(__name__)

# A breakpointok számmá konvertálása
BREAKPOINT_VALUES = {
    key: int(re.sub(r'[^0-9]', '', value))
    for key, value in BREAKPOINTS.items()
}

def inject_responsive_styles():
    """
    Reszponzív UI stílusok injektálása.
    
    Ezek a stílusok a különböző képernyőméretekhez igazodó elrendezést biztosítanak.
    """
    # Alap stílusok betöltése
    inject_base_styles()
    
    # Reszponzív UI stílusok
    css = """
    <style>
    /* ===== RESZPONZÍV UI STÍLUSOK ===== */
    
    /* Alapvető grid rendszer */
    .responsive-grid {
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: var(--spacing-md);
        width: 100%;
    }
    
    /* Adaptív konténerek */
    .responsive-container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0 auto;
        transition: padding var(--animation-normal);
    }
    
    /* Grid elemek különböző méretekhez */
    .col-xs-12 { grid-column: span 12; }
    .col-xs-11 { grid-column: span 11; }
    .col-xs-10 { grid-column: span 10; }
    .col-xs-9 { grid-column: span 9; }
    .col-xs-8 { grid-column: span 8; }
    .col-xs-7 { grid-column: span 7; }
    .col-xs-6 { grid-column: span 6; }
    .col-xs-5 { grid-column: span 5; }
    .col-xs-4 { grid-column: span 4; }
    .col-xs-3 { grid-column: span 3; }
    .col-xs-2 { grid-column: span 2; }
    .col-xs-1 { grid-column: span 1; }
    
    /* Kisebb mobilok (sm) */
    @media (min-width: 576px) {
        .responsive-container {
            padding: 0 var(--spacing-md);
        }
        
        .col-sm-12 { grid-column: span 12; }
        .col-sm-11 { grid-column: span 11; }
        .col-sm-10 { grid-column: span 10; }
        .col-sm-9 { grid-column: span 9; }
        .col-sm-8 { grid-column: span 8; }
        .col-sm-7 { grid-column: span 7; }
        .col-sm-6 { grid-column: span 6; }
        .col-sm-5 { grid-column: span 5; }
        .col-sm-4 { grid-column: span 4; }
        .col-sm-3 { grid-column: span 3; }
        .col-sm-2 { grid-column: span 2; }
        .col-sm-1 { grid-column: span 1; }
    }
    
    /* Tabletek (md) */
    @media (min-width: 768px) {
        .responsive-container {
            padding: 0 var(--spacing-lg);
        }
        
        .col-md-12 { grid-column: span 12; }
        .col-md-11 { grid-column: span 11; }
        .col-md-10 { grid-column: span 10; }
        .col-md-9 { grid-column: span 9; }
        .col-md-8 { grid-column: span 8; }
        .col-md-7 { grid-column: span 7; }
        .col-md-6 { grid-column: span 6; }
        .col-md-5 { grid-column: span 5; }
        .col-md-4 { grid-column: span 4; }
        .col-md-3 { grid-column: span 3; }
        .col-md-2 { grid-column: span 2; }
        .col-md-1 { grid-column: span 1; }
    }
    
    /* Kis laptopok (lg) */
    @media (min-width: 992px) {
        .responsive-container {
            padding: 0 var(--spacing-xl);
        }
        
        .col-lg-12 { grid-column: span 12; }
        .col-lg-11 { grid-column: span 11; }
        .col-lg-10 { grid-column: span 10; }
        .col-lg-9 { grid-column: span 9; }
        .col-lg-8 { grid-column: span 8; }
        .col-lg-7 { grid-column: span 7; }
        .col-lg-6 { grid-column: span 6; }
        .col-lg-5 { grid-column: span 5; }
        .col-lg-4 { grid-column: span 4; }
        .col-lg-3 { grid-column: span 3; }
        .col-lg-2 { grid-column: span 2; }
        .col-lg-1 { grid-column: span 1; }
    }
    
    /* Nagy képernyők (xl) */
    @media (min-width: 1200px) {
        .responsive-container {
            max-width: 1140px;
            padding: 0;
        }
        
        .col-xl-12 { grid-column: span 12; }
        .col-xl-11 { grid-column: span 11; }
        .col-xl-10 { grid-column: span 10; }
        .col-xl-9 { grid-column: span 9; }
        .col-xl-8 { grid-column: span 8; }
        .col-xl-7 { grid-column: span 7; }
        .col-xl-6 { grid-column: span 6; }
        .col-xl-5 { grid-column: span 5; }
        .col-xl-4 { grid-column: span 4; }
        .col-xl-3 { grid-column: span 3; }
        .col-xl-2 { grid-column: span 2; }
        .col-xl-1 { grid-column: span 1; }
    }
    
    /* Nagyon nagy képernyők (2xl) */
    @media (min-width: 1400px) {
        .responsive-container {
            max-width: 1320px;
        }
    }
    
    /* Láthatatlan elemek különböző képernyőméreteken */
    .hidden-xs {
        display: none !important;
    }
    
    @media (min-width: 576px) {
        .hidden-xs {
            display: initial !important;
        }
        .hidden-sm {
            display: none !important;
        }
    }
    
    @media (min-width: 768px) {
        .hidden-sm {
            display: initial !important;
        }
        .hidden-md {
            display: none !important;
        }
    }
    
    @media (min-width: 992px) {
        .hidden-md {
            display: initial !important;
        }
        .hidden-lg {
            display: none !important;
        }
    }
    
    @media (min-width: 1200px) {
        .hidden-lg {
            display: initial !important;
        }
        .hidden-xl {
            display: none !important;
        }
    }
    
    /* Csak bizonyos képernyőméreteken látható elemek */
    .visible-xs-only {
        display: initial !important;
    }
    .visible-sm-only,
    .visible-md-only,
    .visible-lg-only,
    .visible-xl-only {
        display: none !important;
    }
    
    @media (min-width: 576px) {
        .visible-xs-only {
            display: none !important;
        }
        .visible-sm-only {
            display: initial !important;
        }
    }
    
    @media (min-width: 768px) {
        .visible-sm-only {
            display: none !important;
        }
        .visible-md-only {
            display: initial !important;
        }
    }
    
    @media (min-width: 992px) {
        .visible-md-only {
            display: none !important;
        }
        .visible-lg-only {
            display: initial !important;
        }
    }
    
    @media (min-width: 1200px) {
        .visible-lg-only {
            display: none !important;
        }
        .visible-xl-only {
            display: initial !important;
        }
    }
    
    /* Érintés-optimalizált komponensek */
    .touch-target {
        min-height: 44px;  /* Apple ajánlása érintésterületekre */
        min-width: 44px;
    }
    
    .touch-list-item {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        border-radius: var(--radius-md);
        transition: background-color var(--animation-fast);
    }
    
    .touch-list-item:active {
        background-color: var(--color-midgray);
    }
    
    /* Gesztus támogatás */
    .gesture-container {
        position: relative;
        overflow: hidden;
        touch-action: pan-y; /* függőleges görgetés engedélyezése */
    }
    
    .swipeable-item {
        position: relative;
        transition: transform var(--animation-normal);
        touch-action: pan-x; /* vízszintes swipe engedélyezése */
    }
    
    .swipeable-item-content {
        position: relative;
        z-index: 1;
        background-color: var(--color-white);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-small);
    }
    
    .swipeable-item-actions {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        display: flex;
        align-items: center;
        z-index: 0;
    }
    
    .swipeable-item-action {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 var(--spacing-md);
        color: white;
        font-weight: 500;
    }
    
    .swipe-action-delete {
        background-color: var(--color-danger);
    }
    
    .swipe-action-edit {
        background-color: var(--color-info);
    }
    
    /* Streamlit UI módosítások különböző képernyőméretekhez */
    @media (max-width: 768px) {
        /* Kisebb margók és padding mobil nézetben */
        .stApp {
            margin: 0;
            padding: 0;
        }
        
        /* Kisebb betűméret */
        .stMarkdown p {
            font-size: var(--font-size-sm);
        }
        
        /* Kisebb címek */
        .stMarkdown h1 {
            font-size: var(--font-size-xl);
        }
        
        .stMarkdown h2 {
            font-size: var(--font-size-lg);
        }
        
        .stMarkdown h3 {
            font-size: var(--font-size-md);
        }
        
        /* Gomb optimalizálás */
        .stButton button {
            width: 100%;
            min-height: 44px;
        }
    }
    </style>
    """
    
    try:
        st.markdown(css, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a reszponzív stílusok injektálásakor: {str(e)}")


def inject_touch_gesture_support():
    """
    Érintés- és gesztusvezérlés támogatás injektálása.
    
    Ez a JavaScript kód swipe és egyéb érintés vezérlést tesz lehetővé a mobilon.
    """
    js_code = """
    <script>
    // Reszponzív design és touch vezérlés
    document.addEventListener('DOMContentLoaded', function() {
        // Képernyőméret detektálás
        function detectScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            // Eszköz osztályok hozzáadása a body-hoz
            document.body.classList.remove('device-mobile', 'device-tablet', 'device-desktop');
            document.body.classList.remove('orientation-portrait', 'orientation-landscape');
            
            // Képernyőméret osztály
            if (width < 576) {
                document.body.classList.add('device-mobile');
            } else if (width < 992) {
                document.body.classList.add('device-tablet');
            } else {
                document.body.classList.add('device-desktop');
            }
            
            // Tájolás osztály
            if (width < height) {
                document.body.classList.add('orientation-portrait');
            } else {
                document.body.classList.add('orientation-landscape');
            }
            
            // Érintőképernyő osztály
            if (isTouch) {
                document.body.classList.add('touch-device');
            } else {
                document.body.classList.remove('touch-device');
            }
            
            // Adatok elküldése Streamlit-nek
            const device_info = {
                width: width,
                height: height,
                isTouch: isTouch,
                deviceType: width < 576 ? 'mobile' : width < 992 ? 'tablet' : 'desktop',
                orientation: width < height ? 'portrait' : 'landscape'
            };
            
            // Rejtett input mezőbe tárolás
            const deviceInfoInput = document.getElementById('device_info_data');
            if (deviceInfoInput) {
                deviceInfoInput.value = JSON.stringify(device_info);
                deviceInfoInput.dispatchEvent(new Event('change'));
            }
        }
        
        // Képernyőméret változás figyelése
        window.addEventListener('resize', detectScreenSize);
        
        // Kezdeti detektálás
        detectScreenSize();
        
        // Swipe gesztus kezelés
        class SwipeHandler {
            constructor(element, options = {}) {
                this.element = element;
                this.options = Object.assign({
                    threshold: 50,         // A swipe aktiválásához szükséges minimális távolság
                    restraint: 100,        // A swipe iránytól való maximális eltérés
                    allowedTime: 300,      // A swipe maximális ideje ms-ben
                    onSwipeLeft: null,     // Callback a balra swipe-ra
                    onSwipeRight: null,    // Callback a jobbra swipe-ra
                    onSwipeUp: null,       // Callback a felfelé swipe-ra
                    onSwipeDown: null      // Callback a lefelé swipe-ra
                }, options);
                
                this.startX = 0;
                this.startY = 0;
                this.startTime = 0;
                this.handleTouchStart = this.handleTouchStart.bind(this);
                this.handleTouchMove = this.handleTouchMove.bind(this);
                this.handleTouchEnd = this.handleTouchEnd.bind(this);
                
                this.element.addEventListener('touchstart', this.handleTouchStart, false);
                this.element.addEventListener('touchmove', this.handleTouchMove, false);
                this.element.addEventListener('touchend', this.handleTouchEnd, false);
            }
            
            handleTouchStart(e) {
                const firstTouch = e.touches[0];
                this.startX = firstTouch.clientX;
                this.startY = firstTouch.clientY;
                this.startTime = new Date().getTime();
            }
            
            handleTouchMove(e) {
                // Streamlit nem blokkolja a görgetést
                if (e.touches.length > 1) {
                    return; // Ha több érintés van, ignoráljuk
                }
            }
            
            handleTouchEnd(e) {
                const touchEndTime = new Date().getTime();
                const touchTime = touchEndTime - this.startTime;
                
                if (touchTime > this.options.allowedTime) {
                    return; // Ha túl hosszú időbe telt, nem swipe
                }
                
                const endTouch = e.changedTouches[0];
                const distX = endTouch.clientX - this.startX;
                const distY = endTouch.clientY - this.startY;
                
                // Irány meghatározása
                if (Math.abs(distX) >= this.options.threshold && Math.abs(distY) <= this.options.restraint) {
                    // Vízszintes swipe
                    if (distX > 0 && this.options.onSwipeRight) {
                        this.options.onSwipeRight(e, distX);
                    } else if (distX < 0 && this.options.onSwipeLeft) {
                        this.options.onSwipeLeft(e, -distX);
                    }
                } else if (Math.abs(distY) >= this.options.threshold && Math.abs(distX) <= this.options.restraint) {
                    // Függőleges swipe
                    if (distY > 0 && this.options.onSwipeDown) {
                        this.options.onSwipeDown(e, distY);
                    } else if (distY < 0 && this.options.onSwipeUp) {
                        this.options.onSwipeUp(e, -distY);
                    }
                }
            }
            
            destroy() {
                this.element.removeEventListener('touchstart', this.handleTouchStart);
                this.element.removeEventListener('touchmove', this.handleTouchMove);
                this.element.removeEventListener('touchend', this.handleTouchEnd);
            }
        }
        
        // SwipeHandler globális elérhetővé tétele
        window.SwipeHandler = SwipeHandler;
        
        // Swipeable elemek inicializálása
        function initSwipeableItems() {
            const swipeableItems = document.querySelectorAll('.swipeable-item');
            
            swipeableItems.forEach(item => {
                const itemId = item.getAttribute('data-item-id');
                const deleteBtn = item.querySelector('.swipe-action-delete');
                const editBtn = item.querySelector('.swipe-action-edit');
                const content = item.querySelector('.swipeable-item-content');
                
                // Swipe kezelő létrehozása
                new SwipeHandler(content, {
                    onSwipeLeft: (e, distance) => {
                        // Balra swipe: akciók megjelenítése
                        content.style.transform = `translateX(-80px)`;
                        
                        // Állítsuk mega az esemény buborékolását
                        e.stopPropagation();
                    },
                    onSwipeRight: (e, distance) => {
                        // Jobbra swipe: akciók elrejtése
                        content.style.transform = 'translateX(0)';
                        
                        // Állítsuk mega az esemény buborékolását
                        e.stopPropagation();
                    }
                });
                
                // Kattintás eseménykezelők
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        // Törlés gomb triggerlése Streamlit-ben
                        const triggerBtn = document.getElementById(`delete_item_${itemId}`);
                        if (triggerBtn) triggerBtn.click();
                    });
                }
                
                if (editBtn) {
                    editBtn.addEventListener('click', () => {
                        // Szerkesztés gomb triggerlése Streamlit-ben
                        const triggerBtn = document.getElementById(`edit_item_${itemId}`);
                        if (triggerBtn) triggerBtn.click();
                    });
                }
                
                // Kattintás a konténeren kívül visszaállítja az elemet
                document.addEventListener('click', (e) => {
                    if (!item.contains(e.target)) {
                        content.style.transform = 'translateX(0)';
                    }
                });
            });
        }
        
        // Swipeable elemek inicializálása
        // MutationObserver használata, hogy új elemeket is kezeljen a DOM-ban
        const observer = new MutationObserver((mutations) => {
            let hasNewSwipeableItems = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1 && (
                            node.classList?.contains('swipeable-item') || 
                            node.querySelector?.('.swipeable-item')
                        )) {
                            hasNewSwipeableItems = true;
                        }
                    });
                }
            });
            
            if (hasNewSwipeableItems) {
                initSwipeableItems();
            }
        });
        
        // A teljes dokumentum figyelése
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Kezdeti inicializálás
        initSwipeableItems();
    });
    </script>
    """
    
    try:
        st.markdown(js_code, unsafe_allow_html=True)
    except Exception as e:
        logger.error(f"Hiba a gesztusvezérlés támogatás injektálásakor: {str(e)}")


def detect_device_info():
    """
    Eszköz információk detektálása JavaScript segítségével.
    
    Frissíti a session state-ben tárolt információkat az eszköz típusáról,
    képernyőméretről, és érintőképernyő támogatásról.
    
    Returns:
        dict: Az eszköz információi
    """
    # Először ellenőrizzük, hogy már inicializáltuk-e ezeket az értékeket
    if all(key in st.session_state for key in [
        'device_type', 'device_width', 'device_height', 
        'is_touch_device', 'device_orientation'
    ]):
        # Már van információnk
        return {
            'deviceType': st.session_state.device_type,
            'width': st.session_state.device_width,
            'height': st.session_state.device_height,
            'isTouch': st.session_state.is_touch_device,
            'orientation': st.session_state.device_orientation
        }
    
    # Alapértelmezett értékek beállítása
    st.session_state.device_type = 'desktop'
    st.session_state.device_width = 1200
    st.session_state.device_height = 800
    st.session_state.is_touch_device = False
    st.session_state.device_orientation = 'landscape'
    
    # Rejtett input renderelése JavaScript-hez
    st.markdown("""
    <input type="hidden" id="device_info_data" value="{}" style="display:none;">
    """, unsafe_allow_html=True)
    
    # Rejtett input, amit a JavaScript frissít
    device_info_json = st.text_input('Device Info:', 
                                      value='{}', 
                                      key='device_info_input',
                                      label_visibility="collapsed")
    
    # Az értékek frissítése a JSON adatból
    try:
        if device_info_json and device_info_json != '{}':
            device_info = json.loads(device_info_json)
            
            # Frissítsük a session state-et
            st.session_state.device_type = device_info.get('deviceType', 'desktop')
            st.session_state.device_width = device_info.get('width', 1200)
            st.session_state.device_height = device_info.get('height', 800)
            st.session_state.is_touch_device = device_info.get('isTouch', False)
            st.session_state.device_orientation = device_info.get('orientation', 'landscape')
            
            return device_info
    except Exception as e:
        logger.error(f"Hiba az eszköz információk feldolgozásakor: {str(e)}")
        
    # Alapértelmezett adatok visszaadása
    return {
        'deviceType': st.session_state.device_type,
        'width': st.session_state.device_width,
        'height': st.session_state.device_height,
        'isTouch': st.session_state.is_touch_device,
        'orientation': st.session_state.device_orientation
    }


def inject_responsive_enhancements():
    """
    Az összes reszponzív fejlesztés injektálása.
    
    Ez a függvény betölti az összes reszponzív stílust és JavaScript funkciót.
    """
    # Stílusok betöltése
    inject_responsive_styles()
    
    # Gesztusvezérlés támogatás
    inject_touch_gesture_support()
    
    # Eszköz információk detektálása
    detect_device_info()


def create_responsive_container(
    content_func: Callable,
    mobile_content_func: Optional[Callable] = None,
    tablet_content_func: Optional[Callable] = None,
    desktop_content_func: Optional[Callable] = None
):
    """
    Reszponzív konténer létrehozása eszköztípus szerinti tartalommal.
    
    Ez a függvény különböző tartalmat jelenít meg az eszköz típusától függően.
    
    Args:
        content_func: Alapértelmezett tartalom renderelő függvény
        mobile_content_func: Mobil specifikus tartalom (ha nem adott, content_func lesz használva)
        tablet_content_func: Tablet specifikus tartalom (ha nem adott, content_func lesz használva)
        desktop_content_func: Desktop specifikus tartalom (ha nem adott, content_func lesz használva)
    """
    # Eszköz információk lekérése
    device_info = detect_device_info()
    device_type = device_info['deviceType']
    
    # HTML konténer nyitása
    st.markdown('<div class="responsive-container">', unsafe_allow_html=True)
    
    # Megfelelő tartalom renderelése
    if device_type == 'mobile' and mobile_content_func:
        mobile_content_func()
    elif device_type == 'tablet' and tablet_content_func:
        tablet_content_func()
    elif device_type == 'desktop' and desktop_content_func:
        desktop_content_func()
    else:
        # Alapértelmezett tartalom
        content_func()
    
    # HTML konténer zárása
    st.markdown('</div>', unsafe_allow_html=True)


def create_swipeable_item(
    content_func: Callable,
    item_id: str,
    show_delete: bool = True,
    show_edit: bool = True,
    on_delete: Optional[Callable[[str], None]] = None,
    on_edit: Optional[Callable[[str], None]] = None
):
    """
    Swipe gesztusokkal kezelhető elem létrehozása.
    
    Ez a komponens mobilon swipe gesztusokkal elérhetővé teszi a szerkesztés és törlés akciókat.
    
    Args:
        content_func: Tartalom renderelő függvény
        item_id: Elem egyedi azonosítója
        show_delete: Mutassa-e a törlés gombot
        show_edit: Mutassa-e a szerkesztés gombot
        on_delete: Törlés eseménykezelő függvény (param: item_id)
        on_edit: Szerkesztés eseménykezelő függvény (param: item_id)
    """
    # Eszköz információk ellenőrzése
    device_info = detect_device_info()
    is_touch = device_info['isTouch']
    
    # HTML struktúra
    st.markdown(f"""
    <div class="swipeable-item" data-item-id="{item_id}">
        <div class="swipeable-item-content">
            <div class="swipeable-item-inner">
    """, unsafe_allow_html=True)
    
    # Tartalom renderelése
    content_func()
    
    # Tartalom lezárása
    st.markdown("""
            </div>
        </div>
    """, unsafe_allow_html=True)
    
    # Swipe akciók
    actions_html = '<div class="swipeable-item-actions">'
    
    if show_edit:
        actions_html += '<div class="swipeable-item-action swipe-action-edit">✎</div>'
    
    if show_delete:
        actions_html += '<div class="swipeable-item-action swipe-action-delete">🗑️</div>'
    
    actions_html += '</div>'
    
    st.markdown(actions_html, unsafe_allow_html=True)
    
    # Elem lezárása
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Eseménykezelők rejtett gombok
    if show_edit and on_edit:
        if st.button("Edit", key=f"edit_item_{item_id}", label_visibility="collapsed"):
            on_edit(item_id)
    
    if show_delete and on_delete:
        if st.button("Delete", key=f"delete_item_{item_id}", label_visibility="collapsed"):
            on_delete(item_id)
    
    # Swipe segítségnyújtás mobil eszközökön első használatkor
    if is_touch and "swipe_tip_shown" not in st.session_state:
        st.info("💡 Tipp: Húzza el az elemeket jobbról balra (swipe) további műveletek megjelenítéséhez.")
        st.session_state.swipe_tip_shown = True


def create_adaptive_grid(
    column_content_funcs: List[Callable],
    mobile_columns: List[int] = None,
    tablet_columns: List[int] = None,
    desktop_columns: List[int] = None
):
    """
    Adaptív grid elrendezés létrehozása különböző eszköztípusokhoz.
    
    Args:
        column_content_funcs: Oszlopok tartalmát renderelő függvények listája
        mobile_columns: Oszlopok szélessége mobilon (12-es grid rendszerben)
        tablet_columns: Oszlopok szélessége tableten (12-es grid rendszerben)
        desktop_columns: Oszlopok szélessége asztali gépen (12-es grid rendszerben)
    """
    # Eszköz információk lekérése
    device_info = detect_device_info()
    device_type = device_info['deviceType']
    
    # Alapértelmezett oszlop szélességek
    if mobile_columns is None:
        mobile_columns = [12] * len(column_content_funcs)
    
    if tablet_columns is None:
        # Alapértelmezetten 2 oszlopos elrendezés tableten
        tablet_columns = [6] * len(column_content_funcs)
        # Ha páratlan számú oszlop van, az utolsó teljes szélességű
        if len(column_content_funcs) % 2 == 1:
            tablet_columns[-1] = 12
    
    if desktop_columns is None:
        # Egyenlő szélességű oszlopok desktopra
        col_width = 12 // len(column_content_funcs)
        desktop_columns = [col_width] * len(column_content_funcs)
        # Fennmaradó szélesség hozzáadása az utolsó oszlophoz
        remainder = 12 - (col_width * len(column_content_funcs))
        if remainder > 0:
            desktop_columns[-1] += remainder
    
    # Grid konténer nyitása
    st.markdown('<div class="responsive-grid">', unsafe_allow_html=True)
    
    # Az aktuális eszköztípusnak megfelelő oszlopszélességek kiválasztása
    if device_type == 'mobile':
        column_widths = mobile_columns
    elif device_type == 'tablet':
        column_widths = tablet_columns
    else:  # desktop
        column_widths = desktop_columns
    
    # Oszlopok renderelése
    for idx, content_func in enumerate(column_content_funcs):
        col_width = column_widths[idx] if idx < len(column_widths) else 12
        
        # Oszlop nyitása a megfelelő grid-column szélességgel
        st.markdown(f'<div class="col-xs-12 col-sm-{col_width} col-md-{col_width} col-lg-{col_width}">', unsafe_allow_html=True)
        
        # Tartalom renderelése
        content_func()
        
        # Oszlop zárása
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Grid konténer zárása
    st.markdown('</div>', unsafe_allow_html=True)


def render_adaptive_filter_panel(
    filter_components_func: Callable,
    title: str = "Szűrők",
    icon: str = "🔍",
    collapsible: bool = True,
    expanded: bool = True,
    key: Optional[str] = None
):
    """
    Adaptív szűrőpanel renderelése, amely alkalmazkodik a képernyőmérethez.
    
    Args:
        filter_components_func: Szűrő komponenseket renderelő függvény
        title: A panel címe
        icon: A panel ikonja
        collapsible: Legyen-e összecsukható
        expanded: Alapértelmezetten kinyitott-e
        key: Egyedi kulcs a komponenshez
    """
    # Eszköz információk lekérése
    device_info = detect_device_info()
    device_type = device_info['deviceType']
    
    # Egyedi komponens azonosító
    panel_id = key or f"filter_panel_{str(uuid.uuid4())[:8]}"
    
    # Állapot kezelése a session state-ben
    panel_state_key = f"{panel_id}_expanded"
    if panel_state_key not in st.session_state:
        st.session_state[panel_state_key] = expanded
    
    # Külön logika különböző eszköztípusokhoz
    if device_type == 'mobile':
        # Mobilon alapértelmezetten összecsukott és lebegő gombbal aktiválható
        floating_button_key = f"{panel_id}_mobile_toggle"
        
        # Lebegő szűrőgomb
        st.markdown(f"""
        <div style="position: fixed; bottom: 20px; right: 20px; z-index: 9999;">
            <button id="{floating_button_key}" style="width: 56px; height: 56px; border-radius: 50%; 
                    background-color: var(--color-primary); color: white; border: none;
                    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
                    font-size: 24px; display: flex; align-items: center; justify-content: center;">
                {icon}
            </button>
        </div>
        
        <script>
            document.addEventListener('DOMContentLoaded', function() {{
                const floatingBtn = document.getElementById('{floating_button_key}');
                if (floatingBtn) {{
                    floatingBtn.addEventListener('click', function() {{
                        // Streamlit rejtett gomb aktiválása
                        document.getElementById('mobile_filter_toggle_{panel_id}').click();
                    }});
                }}
            }});
        </script>
        """, unsafe_allow_html=True)
        
        # Rejtett gomb a Streamlit állapot frissítéséhez
        if st.button("Toggle Filters", key=f"mobile_filter_toggle_{panel_id}", label_visibility="collapsed"):
            st.session_state[panel_state_key] = not st.session_state[panel_state_key]
            st.rerun()
        
        # Teljes képernyős szűrő drawer
        if st.session_state[panel_state_key]:
            st.markdown(f"""
            <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; 
                    background-color: white; z-index: 9998; padding: 20px;
                    animation: slide-in-right 0.3s ease;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>{title}</h2>
                    <button id="close_filter_drawer_{panel_id}" style="background: none; border: none; 
                            font-size: 24px; cursor: pointer;">✕</button>
                </div>
                <div id="mobile_filter_content_{panel_id}">
                    <!-- Szűrő komponensek ide kerülnek -->
                </div>
            </div>
            
            <style>
            @keyframes slide-in-right {{
                from {{ transform: translateX(100%); }}
                to {{ transform: translateX(0); }}
            }}
            </style>
            
            <script>
                document.addEventListener('DOMContentLoaded', function() {{
                    const closeBtn = document.getElementById('close_filter_drawer_{panel_id}');
                    if (closeBtn) {{
                        closeBtn.addEventListener('click', function() {{
                            document.getElementById('mobile_filter_toggle_{panel_id}').click();
                        }});
                    }}
                }});
            </script>
            """, unsafe_allow_html=True)
            
            # Szűrő komponensek renderelése
            filter_components_func()
            
    elif device_type == 'tablet':
        # Tablet nézeten oldalsáv szűrőpanel
        col1, col2 = st.columns([1, 3])
        
        with col1:
            if collapsible:
                expanded_state = st.checkbox(f"{title}", value=st.session_state[panel_state_key], key=f"{panel_id}_tablet_toggle")
                st.session_state[panel_state_key] = expanded_state
            else:
                st.subheader(title)
            
            if st.session_state[panel_state_key]:
                filter_components_func()
        
        with col2:
            # Tartalom helyét biztosítjuk (üres konténer)
            st.empty()
    
    else:  # desktop
        # Asztali nézet: standard összecsukható kártya
        st.markdown(f"""
        <div class="filter-panel" id="{panel_id}">
            <div class="filter-panel-header" id="filter-header-{panel_id}">
                <div class="filter-panel-title">{icon} {title}</div>
                {f'<div class="filter-panel-toggle">{("▼" if st.session_state[panel_state_key] else "▶")}</div>' if collapsible else ''}
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # JavaScript az összecsukáshoz/kinyitáshoz
        if collapsible:
            js_code = f"""
            <script>
            document.addEventListener('DOMContentLoaded', () => {{
                const header = document.getElementById('filter-header-{panel_id}');
                
                if (header) {{
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => {{
                        document.getElementById('desktop_filter_toggle_{panel_id}').click();
                    }});
                }}
            }});
            </script>
            """
            st.markdown(js_code, unsafe_allow_html=True)
            
            # Rejtett gomb a Streamlit állapot frissítéséhez
            if st.button("Toggle", key=f"desktop_filter_toggle_{panel_id}", label_visibility="collapsed"):
                st.session_state[panel_state_key] = not st.session_state[panel_state_key]
                st.rerun()
        
        # Panel tartalma, ha kinyitva
        if st.session_state[panel_state_key]:
            filter_components_func()


# Példa használat, ha ezt a modult közvetlenül futtatják
if __name__ == "__main__":
    st.set_page_config(page_title="Reszponzív UI Demo", layout="wide")
    
    st.title("Továbbfejlesztett Reszponzív Design Demó")
    st.markdown("""
    Ez a demó oldal a továbbfejlesztett reszponzív designt és gesztusvezérlést mutatja be,
    amely a TASK-1.4 feladat részeként készült.
    
    **Főbb tulajdonságok:**
    - Automatikus eszköztípus-felismerés
    - Adaptív elrendezés különböző képernyőméretekhez
    - Gesztusvezérlés támogatás érintőképernyőkön
    - Swipe műveletek mobilon
    """)
    
    # Reszponzív fejlesztések betöltése
    inject_responsive_enhancements()
    
    # Eszköz információk kijelzése
    device_info = detect_device_info()
    with st.expander("Eszköz információk"):
        st.write(f"**Eszköz típus:** {device_info['deviceType']}")
        st.write(f"**Képernyő méret:** {device_info['width']}x{device_info['height']} px")
        st.write(f"**Érintőképernyő:** {'Igen' if device_info['isTouch'] else 'Nem'}")
        st.write(f"**Képernyő tájolás:** {device_info['orientation']}")
    
    # Adaptív UI példák
    st.header("1. Adaptív Komponensek")
    
    # Példa tartalom függvények
    def mobile_content():
        st.write("Ez a tartalom mobilra van optimalizálva")
        st.button("Mobil Gomb", use_container_width=True)
    
    def tablet_content():
        st.write("Ez a tartalom tabletre van optimalizálva")
        col1, col2 = st.columns(2)
        with col1:
            st.button("Tablet Gomb 1")
        with col2:
            st.button("Tablet Gomb 2")
    
    def desktop_content():
        st.write("Ez a tartalom asztali gépre van optimalizálva")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.button("Asztali Gomb 1")
        with col2:
            st.button("Asztali Gomb 2") 
        with col3:
            st.button("Asztali Gomb 3")
    
    # Adaptív konténer
    create_responsive_container(
        content_func=lambda: st.write("Alapértelmezett tartalom"),
        mobile_content_func=mobile_content,
        tablet_content_func=tablet_content,
        desktop_content_func=desktop_content
    )
    
    # Adaptív grid példa
    st.header("2. Adaptív Grid Rendszer")
    
    create_adaptive_grid(
        column_content_funcs=[
            lambda: st.card("Panel 1", body="Ez az első panel tartalma"),
            lambda: st.card("Panel 2", body="Ez a második panel tartalma"),
            lambda: st.card("Panel 3", body="Ez a harmadik panel tartalma"),
        ],
        mobile_columns=[12, 12, 12],  # Mobilon egymás alatt
        tablet_columns=[6, 6, 12],    # Tableten 2+1 elrendezés
        desktop_columns=[4, 4, 4]     # Desktopra 3 egyenlő oszlop
    )
    
    # Swipe gesztus példa
    st.header("3. Swipe Gesztus Támogatás")
    
    # Példa elemek
    sample_items = [
        {"id": "item1", "title": "Első elem", "description": "Ez az első elem leírása"},
        {"id": "item2", "title": "Második elem", "description": "Ez a második elem leírása"},
        {"id": "item3", "title": "Harmadik elem", "description": "Ez a harmadik elem leírása"}
    ]
    
    # Swipe műveletek kezelése
    def handle_item_delete(item_id):
        st.session_state.deleted_item = item_id
        st.success(f"Elem törlése: {item_id}")
    
    def handle_item_edit(item_id):
        st.session_state.edited_item = item_id
        st.info(f"Elem szerkesztése: {item_id}")
    
    # Swipeable elemek renderelése
    for item in sample_items:
        create_swipeable_item(
            content_func=lambda i=item: st.write(f"**{i['title']}**\n\n{i['description']}"),
            item_id=item["id"],
            on_delete=handle_item_delete,
            on_edit=handle_item_edit
        )
    
    # Adaptív szűrőpanel példa
    st.header("4. Adaptív Szűrőpanel")
    
    # Példa szűrő komponensek
    def render_sample_filters():
        st.selectbox("Státusz:", ["Összes", "Aktív", "Inaktív"])
        st.date_input("Dátum:")
        st.slider("Ártartomány:", 0, 100, (25, 75))
        st.multiselect("Kategóriák:", ["Kategória 1", "Kategória 2", "Kategória 3"])
        st.button("Szűrés", use_container_width=True)
    
    # Reszponzív szűrőpanel
    render_adaptive_filter_panel(
        filter_components_func=render_sample_filters,
        title="Szűrők",
        icon="🔍",
        collapsible=True,
        expanded=True,
        key="sample_filter"
    )
    
    # Fejlesztői információk
    with st.expander("Fejlesztői információk"):
        st.markdown("""
        **Modul:** enhanced_responsive_ui.py
        
        **Fő funkciók:**
        - `inject_responsive_enhancements`: Betölti az összes reszponzív funkciót
        - `detect_device_info`: Eszköz információk detektálása
        - `create_responsive_container`: Eszköztípusnak megfelelő tartalmat jelenít meg
        - `create_adaptive_grid`: Reszponzív grid elrendezés
        - `create_swipeable_item`: Swipe gesztusokkal kezelhető elem
        - `render_adaptive_filter_panel`: Képernyőmérethez alkalmazkodó szűrőpanel
        
        **Használt technológiák:**
        - CSS Grid és Media Queries a reszponzív elrendezéshez
        - JavaScript a gesztusvezérlés és eszközdetektálás megvalósításához
        - Streamlit session state a komponensek állapotának követéséhez
        """)