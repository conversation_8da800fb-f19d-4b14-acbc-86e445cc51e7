"""
Modern state management system for the offer management module.
Implements centralized state management with Redux-like patterns for Streamlit.
"""
import streamlit as st
import logging
from typing import Dict, Any, Callable, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, date
import json
import uuid
from abc import ABC, abstractmethod
from copy import deepcopy

logger = logging.getLogger(__name__)

@dataclass
class StateUpdate:
    """Represents a state update action."""
    type: str
    payload: Any = None
    timestamp: datetime = field(default_factory=datetime.now)
    component_id: Optional[str] = None

class StateSubscriber:
    """Manages subscriptions to state changes."""
    def __init__(self, callback: Callable, selector: Optional[Callable] = None):
        self.callback = callback
        self.selector = selector or (lambda state: state)
        self.last_value = None
        
    def notify(self, state: Dict[str, Any]) -> None:
        """Notify subscriber if selected state has changed."""
        try:
            selected_value = self.selector(state)
            if selected_value != self.last_value:
                self.last_value = deepcopy(selected_value)
                self.callback(selected_value)
        except Exception as e:
            logger.error(f"Error in state subscriber: {e}")

class Reducer(ABC):
    """Abstract base class for state reducers."""
    
    @abstractmethod
    def reduce(self, state: Dict[str, Any], action: StateUpdate) -> Dict[str, Any]:
        """Apply action to state and return new state."""
        pass

class OfferFilterReducer(Reducer):
    """Handles offer filter state changes."""
    
    def reduce(self, state: Dict[str, Any], action: StateUpdate) -> Dict[str, Any]:
        new_state = deepcopy(state)
        
        if action.type == "SET_PRODUCER_FILTER":
            new_state["filters"]["producer"] = action.payload
            
        elif action.type == "SET_STATUS_FILTER":
            new_state["filters"]["status"] = action.payload
            
        elif action.type == "SET_DATE_RANGE_FILTER":
            new_state["filters"]["date_range"] = action.payload
            
        elif action.type == "SET_SEARCH_FILTER":
            new_state["filters"]["search"] = action.payload
            
        elif action.type == "CLEAR_ALL_FILTERS":
            new_state["filters"] = self._get_initial_filters()
            
        elif action.type == "APPLY_SAVED_FILTER":
            saved_filter = action.payload
            if saved_filter:
                new_state["filters"].update(saved_filter.get("filter_data", {}))
                
        return new_state
    
    def _get_initial_filters(self) -> Dict[str, Any]:
        """Get initial filter state."""
        return {
            "producer": None,
            "status": None,
            "date_range": {
                "from_date": (date.today() - pd.Timedelta(days=30)).date() if 'pd' in globals() else None,
                "to_date": date.today()
            },
            "search": "",
            "product_type": None
        }

class OfferDataReducer(Reducer):
    """Handles offer data state changes."""
    
    def reduce(self, state: Dict[str, Any], action: StateUpdate) -> Dict[str, Any]:
        new_state = deepcopy(state)
        
        if action.type == "LOAD_OFFERS_START":
            new_state["ui"]["loading"] = True
            new_state["ui"]["error"] = None
            
        elif action.type == "LOAD_OFFERS_SUCCESS":
            new_state["offers"]["data"] = action.payload
            new_state["offers"]["last_updated"] = datetime.now()
            new_state["ui"]["loading"] = False
            new_state["ui"]["error"] = None
            
        elif action.type == "LOAD_OFFERS_ERROR":
            new_state["ui"]["loading"] = False
            new_state["ui"]["error"] = action.payload
            
        elif action.type == "SELECT_OFFER":
            new_state["offers"]["selected_id"] = action.payload
            
        elif action.type == "UPDATE_OFFER":
            offers = new_state["offers"]["data"]
            for i, offer in enumerate(offers):
                if offer.get("id") == action.payload["id"]:
                    offers[i] = {**offer, **action.payload}
                    break
            new_state["offers"]["last_updated"] = datetime.now()
            
        elif action.type == "DELETE_OFFER":
            offers = new_state["offers"]["data"]
            new_state["offers"]["data"] = [
                offer for offer in offers 
                if offer.get("id") != action.payload
            ]
            
        return new_state

class UIStateReducer(Reducer):
    """Handles UI state changes."""
    
    def reduce(self, state: Dict[str, Any], action: StateUpdate) -> Dict[str, Any]:
        new_state = deepcopy(state)
        
        if action.type == "SET_VIEW_MODE":
            new_state["ui"]["view_mode"] = action.payload
            
        elif action.type == "SET_MOBILE_MODE":
            new_state["ui"]["is_mobile"] = action.payload
            
        elif action.type == "TOGGLE_FILTER_PANEL":
            new_state["ui"]["filter_panel_expanded"] = not new_state["ui"]["filter_panel_expanded"]
            
        elif action.type == "SET_NOTIFICATION":
            if "notifications" not in new_state["ui"]:
                new_state["ui"]["notifications"] = []
            new_state["ui"]["notifications"].append({
                "id": str(uuid.uuid4()),
                "message": action.payload.get("message"),
                "type": action.payload.get("type", "info"),
                "timestamp": datetime.now()
            })
            
        elif action.type == "CLEAR_NOTIFICATION":
            notifications = new_state["ui"].get("notifications", [])
            new_state["ui"]["notifications"] = [
                n for n in notifications if n["id"] != action.payload
            ]
            
        elif action.type == "SET_THEME":
            new_state["ui"]["theme"] = action.payload
            
        return new_state

class StateManager:
    """
    Centralized state management system with Redux-like patterns.
    """
    
    def __init__(self):
        self._state_key = "global_offer_management_state"
        self._reducers: Dict[str, Reducer] = {}
        self._subscribers: List[StateSubscriber] = []
        self._middleware: List[Callable] = []
        self._action_history: List[StateUpdate] = []
        self._max_history = 50
        
        # Register default reducers
        self.register_reducer("filters", OfferFilterReducer())
        self.register_reducer("offers", OfferDataReducer())
        self.register_reducer("ui", UIStateReducer())
        
        # Initialize state if not exists
        if self._state_key not in st.session_state:
            st.session_state[self._state_key] = self._get_initial_state()
    
    def _get_initial_state(self) -> Dict[str, Any]:
        """Get the initial application state."""
        return {
            "filters": {
                "producer": None,
                "status": None,
                "date_range": {
                    "from_date": None,
                    "to_date": date.today()
                },
                "search": "",
                "product_type": None
            },
            "offers": {
                "data": [],
                "selected_id": None,
                "last_updated": None,
                "cache": {}
            },
            "ui": {
                "loading": False,
                "error": None,
                "view_mode": "modern",
                "is_mobile": False,
                "filter_panel_expanded": True,
                "notifications": [],
                "theme": "light"
            },
            "saved_filters": {
                "available": [],
                "current": None
            },
            "statistics": {
                "total_count": 0,
                "total_quantity": 0,
                "total_value": 0,
                "avg_price": 0
            }
        }
    
    def register_reducer(self, domain: str, reducer: Reducer) -> None:
        """Register a reducer for a specific state domain."""
        self._reducers[domain] = reducer
        logger.debug(f"Registered reducer for domain: {domain}")
    
    def add_middleware(self, middleware: Callable) -> None:
        """Add middleware function that processes actions."""
        self._middleware.append(middleware)
    
    def subscribe(self, callback: Callable, selector: Optional[Callable] = None) -> str:
        """
        Subscribe to state changes.
        
        Args:
            callback: Function to call when state changes
            selector: Optional function to select specific state slice
            
        Returns:
            Subscription ID for unsubscribing
        """
        subscriber = StateSubscriber(callback, selector)
        self._subscribers.append(subscriber)
        subscription_id = str(uuid.uuid4())
        
        # Store subscription ID for cleanup
        if not hasattr(subscriber, 'id'):
            subscriber.id = subscription_id
            
        return subscription_id
    
    def unsubscribe(self, subscription_id: str) -> None:
        """Unsubscribe from state changes."""
        self._subscribers = [
            sub for sub in self._subscribers 
            if getattr(sub, 'id', None) != subscription_id
        ]
    
    def get_state(self) -> Dict[str, Any]:
        """Get the current application state."""
        return deepcopy(st.session_state[self._state_key])
    
    def get_state_slice(self, path: str) -> Any:
        """
        Get a specific slice of state using dot notation.
        
        Example: get_state_slice("offers.selected_id")
        """
        state = self.get_state()
        keys = path.split(".")
        
        current = state
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def dispatch(self, action_type: str, payload: Any = None, component_id: Optional[str] = None) -> None:
        """
        Dispatch an action to update state.
        
        Args:
            action_type: Type of action to dispatch
            payload: Data associated with the action
            component_id: ID of component dispatching the action
        """
        action = StateUpdate(
            type=action_type,
            payload=payload,
            component_id=component_id
        )
        
        # Apply middleware
        for middleware_func in self._middleware:
            try:
                action = middleware_func(action, self.get_state()) or action
            except Exception as e:
                logger.error(f"Error in middleware: {e}")
        
        # Apply reducers
        current_state = self.get_state()
        new_state = current_state
        
        for domain, reducer in self._reducers.items():
            try:
                new_state = reducer.reduce(new_state, action)
            except Exception as e:
                logger.error(f"Error in reducer {domain}: {e}")
        
        # Update state
        st.session_state[self._state_key] = new_state
        
        # Add to history
        self._action_history.append(action)
        if len(self._action_history) > self._max_history:
            self._action_history.pop(0)
        
        # Notify subscribers
        self._notify_subscribers(new_state)
        
        logger.debug(f"Dispatched action: {action_type}")
    
    def _notify_subscribers(self, state: Dict[str, Any]) -> None:
        """Notify all subscribers of state changes."""
        for subscriber in self._subscribers:
            subscriber.notify(state)
    
    def get_action_history(self) -> List[StateUpdate]:
        """Get the history of dispatched actions."""
        return self._action_history.copy()
    
    def clear_history(self) -> None:
        """Clear the action history."""
        self._action_history.clear()
    
    def reset_state(self) -> None:
        """Reset state to initial values."""
        st.session_state[self._state_key] = self._get_initial_state()
        self._action_history.clear()
        logger.info("State reset to initial values")

# Action creators for common operations
class ActionCreators:
    """Factory class for creating common actions."""
    
    @staticmethod
    def set_producer_filter(producer_id: Optional[str]) -> tuple:
        return ("SET_PRODUCER_FILTER", producer_id)
    
    @staticmethod
    def set_status_filter(status: Optional[str]) -> tuple:
        return ("SET_STATUS_FILTER", status)
    
    @staticmethod
    def set_date_range_filter(from_date: date, to_date: date) -> tuple:
        return ("SET_DATE_RANGE_FILTER", {"from_date": from_date, "to_date": to_date})
    
    @staticmethod
    def set_search_filter(search_term: str) -> tuple:
        return ("SET_SEARCH_FILTER", search_term)
    
    @staticmethod
    def clear_all_filters() -> tuple:
        return ("CLEAR_ALL_FILTERS", None)
    
    @staticmethod
    def load_offers_start() -> tuple:
        return ("LOAD_OFFERS_START", None)
    
    @staticmethod
    def load_offers_success(offers: List[Dict[str, Any]]) -> tuple:
        return ("LOAD_OFFERS_SUCCESS", offers)
    
    @staticmethod
    def load_offers_error(error: str) -> tuple:
        return ("LOAD_OFFERS_ERROR", error)
    
    @staticmethod
    def select_offer(offer_id: str) -> tuple:
        return ("SELECT_OFFER", offer_id)
    
    @staticmethod
    def set_view_mode(mode: str) -> tuple:
        return ("SET_VIEW_MODE", mode)
    
    @staticmethod
    def set_mobile_mode(is_mobile: bool) -> tuple:
        return ("SET_MOBILE_MODE", is_mobile)
    
    @staticmethod
    def show_notification(message: str, notification_type: str = "info") -> tuple:
        return ("SET_NOTIFICATION", {"message": message, "type": notification_type})
    
    @staticmethod
    def set_theme(theme: str) -> tuple:
        return ("SET_THEME", theme)

# Middleware functions
def logging_middleware(action: StateUpdate, state: Dict[str, Any]) -> StateUpdate:
    """Middleware that logs all actions."""
    logger.debug(f"Action dispatched: {action.type} from {action.component_id}")
    return action

def validation_middleware(action: StateUpdate, state: Dict[str, Any]) -> StateUpdate:
    """Middleware that validates action payloads."""
    # Add validation logic here
    if action.type == "SET_DATE_RANGE_FILTER":
        payload = action.payload
        if payload and isinstance(payload, dict):
            from_date = payload.get("from_date")
            to_date = payload.get("to_date")
            if (from_date is not None and to_date is not None and 
                hasattr(from_date, '__gt__') and hasattr(to_date, '__gt__') and
                from_date > to_date):
                logger.warning("Invalid date range: from_date > to_date")
                # Swap dates
                action.payload = {"from_date": to_date, "to_date": from_date}
    
    return action

def persistence_middleware(action: StateUpdate, state: Dict[str, Any]) -> StateUpdate:
    """Middleware that persists certain state changes."""
    # Persist user preferences
    if action.type in ["SET_VIEW_MODE", "SET_THEME"]:
        preferences_key = "user_preferences_offer_management"
        if preferences_key not in st.session_state:
            st.session_state[preferences_key] = {}
        
        if action.type == "SET_VIEW_MODE":
            st.session_state[preferences_key]["view_mode"] = action.payload
        elif action.type == "SET_THEME":
            st.session_state[preferences_key]["theme"] = action.payload
    
    return action

# State selectors (helper functions for accessing state)
class StateSelectors:
    """Helper functions for selecting specific state slices."""
    
    @staticmethod
    def get_filters(state: Dict[str, Any]) -> Dict[str, Any]:
        return state.get("filters", {})
    
    @staticmethod
    def get_offers(state: Dict[str, Any]) -> List[Dict[str, Any]]:
        return state.get("offers", {}).get("data", [])
    
    @staticmethod
    def get_selected_offer_id(state: Dict[str, Any]) -> Optional[str]:
        return state.get("offers", {}).get("selected_id")
    
    @staticmethod
    def get_selected_offer(state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        offers = StateSelectors.get_offers(state)
        selected_id = StateSelectors.get_selected_offer_id(state)
        if selected_id:
            return next((offer for offer in offers if offer.get("id") == selected_id), None)
        return None
    
    @staticmethod
    def is_loading(state: Dict[str, Any]) -> bool:
        return state.get("ui", {}).get("loading", False)
    
    @staticmethod
    def get_error(state: Dict[str, Any]) -> Optional[str]:
        return state.get("ui", {}).get("error")
    
    @staticmethod
    def get_view_mode(state: Dict[str, Any]) -> str:
        return state.get("ui", {}).get("view_mode", "modern")
    
    @staticmethod
    def is_mobile(state: Dict[str, Any]) -> bool:
        return state.get("ui", {}).get("is_mobile", False)
    
    @staticmethod
    def get_notifications(state: Dict[str, Any]) -> List[Dict[str, Any]]:
        return state.get("ui", {}).get("notifications", [])
    
    @staticmethod
    def get_theme(state: Dict[str, Any]) -> str:
        return state.get("ui", {}).get("theme", "light")

# Global state manager instance
state_manager = StateManager()

# Add default middleware
state_manager.add_middleware(logging_middleware)
state_manager.add_middleware(validation_middleware)
state_manager.add_middleware(persistence_middleware)

# Export commonly used items
__all__ = [
    'StateManager',
    'state_manager',
    'ActionCreators', 
    'StateSelectors',
    'StateUpdate',
    'Reducer'
]