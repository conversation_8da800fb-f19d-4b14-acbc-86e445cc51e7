"""
Quick Action Bar Component - Modern Sticky Header Style
Gyors műveletek sáv komponens - Modern sticky header stílusban
"""
import streamlit as st
import streamlit.components.v1 as components
from typing import Dict, Any, Optional
import json

def render_quick_action_bar(offer: Dict[str, Any], offer_id: int) -> None:
    """
    Modern gyors műveletek sáv megjelenítése sticky header-ként.
    
    Args:
        offer: Az ajánlat adatai
        offer_id: <PERSON>z ajánlat azonosítója
    """
    # Get user info from session state
    user = st.session_state.get("user", {})
    user_role = user.get("role", "")
    user_id = user.get("id", None)
    
    status = offer.get("status", "")
    offer_user_id = offer.get("user", {}).get("id") if isinstance(offer.get("user"), dict) else offer.get("user_id")
    
    # Jogosultságok ellenőrzése
    is_owner = user_id and offer_user_id == user_id
    is_operator = user_role in ["admin", "operator", "ügyintéző"]
    is_admin = user_role == "admin"
    
    # Determine available actions based on status and permissions
    can_edit = is_owner and status not in ["FINALIZED", "REJECTED_BY_USER"]
    can_confirm = is_operator and status in ["CREATED", "MODIFIED"]
    can_accept = is_owner and status == "CONFIRMED_BY_COMPANY"
    can_reject = is_owner and status == "CONFIRMED_BY_COMPANY"
    can_finalize = is_operator and status == "ACCEPTED_BY_USER"
    can_delete = is_admin and status != "FINALIZED"
    
    # CSS stílusok
    action_bar_css = """
    <style>
    .quick-action-bar {
        position: sticky;
        top: 0;
        z-index: 999;
        background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
        padding: 12px 20px;
        margin: -1rem -1rem 2rem -1rem;
        border-bottom: 1px solid #333;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    
    .quick-action-group {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .quick-action-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        color: white;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
    }
    
    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .quick-action-btn.primary {
        background: linear-gradient(135deg, #0099e0, #0077b5);
        border-color: #0099e0;
    }
    
    .quick-action-btn.primary:hover {
        background: linear-gradient(135deg, #0077b5, #005a8c);
        border-color: #0077b5;
    }
    
    .quick-action-btn.success {
        background: linear-gradient(135deg, #10dc60, #0ea449);
        border-color: #10dc60;
    }
    
    .quick-action-btn.success:hover {
        background: linear-gradient(135deg, #0ea449, #0c8a3a);
        border-color: #0ea449;
    }
    
    .quick-action-btn.warning {
        background: linear-gradient(135deg, #ff8c1a, #e67e17);
        border-color: #ff8c1a;
    }
    
    .quick-action-btn.warning:hover {
        background: linear-gradient(135deg, #e67e17, #cc7014);
        border-color: #e67e17;
    }
    
    .quick-action-btn.danger {
        background: linear-gradient(135deg, #ff5045, #dc3545);
        border-color: #ff5045;
    }
    
    .quick-action-btn.danger:hover {
        background: linear-gradient(135deg, #dc3545, #b02a37);
        border-color: #dc3545;
    }
    
    .quick-action-btn.secondary {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.15);
    }
    
    .quick-action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }
    
    .action-separator {
        width: 1px;
        height: 32px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 8px;
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 600;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 8px;
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        min-width: 180px;
        display: none;
        z-index: 1000;
    }
    
    .dropdown-menu.show {
        display: block;
    }
    
    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        color: #e0e0e0;
        text-decoration: none;
        transition: all 0.2s ease;
        cursor: pointer;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .dropdown-item:last-child {
        border-bottom: none;
    }
    
    .dropdown-item:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
    }
    
    .dropdown-item.danger {
        color: #ff5045;
    }
    
    .dropdown-item.danger:hover {
        background: rgba(255, 80, 69, 0.2);
    }
    
    @media (max-width: 768px) {
        .quick-action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
            padding: 16px;
        }
        
        .quick-action-group {
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .quick-action-btn {
            flex: 1;
            justify-content: center;
            min-width: 120px;
        }
        
        .action-separator {
            width: 100%;
            height: 1px;
            margin: 4px 0;
        }
    }
    </style>
    """
    
    # Status badge config
    status_config = {
        "CREATED": {"color": "#ffce00", "icon": "🆕", "text": "Létrehozva"},
        "CONFIRMED_BY_COMPANY": {"color": "#ff8c1a", "icon": "✅", "text": "Visszaigazolva"},
        "ACCEPTED_BY_USER": {"color": "#10dc60", "icon": "✔️", "text": "Elfogadva"},
        "REJECTED_BY_USER": {"color": "#ff5045", "icon": "❌", "text": "Elutasítva"},
        "FINALIZED": {"color": "#0099e0", "icon": "🔒", "text": "Véglegesítve"}
    }
    
    current_status = status_config.get(status, {"color": "#6c757d", "icon": "❓", "text": status})
    
    # HTML struktúra
    action_bar_html = f"""
    {action_bar_css}
    <div class="quick-action-bar">
        <div class="quick-action-group">
            <div class="quick-action-btn secondary" onclick="goBack()">
                <span>←</span>
                <span>Vissza</span>
            </div>
            <span style="color: #999; font-size: 14px;">Ajánlat #{offer_id}</span>
            <div class="status-badge" style="background: {current_status['color']}20; border-color: {current_status['color']}40;">
                <span>{current_status['icon']}</span>
                <span style="color: {current_status['color']}">{current_status['text']}</span>
            </div>
        </div>
        
        <div class="action-separator"></div>
        
        <div class="quick-action-group">
    """
    
    # Státusz specifikus gombok hozzáadása
    if can_confirm:
        action_bar_html += """
            <div class="quick-action-btn primary" onclick="handleAction('confirm')">
                <span>✓</span>
                <span>Visszaigazolás</span>
            </div>
        """
    
    if can_accept:
        action_bar_html += """
            <div class="quick-action-btn success" onclick="handleAction('accept')">
                <span>✔️</span>
                <span>Elfogadás</span>
            </div>
        """
    
    if can_reject:
        action_bar_html += """
            <div class="quick-action-btn warning" onclick="handleAction('reject')">
                <span>✗</span>
                <span>Elutasítás</span>
            </div>
        """
    
    if can_finalize:
        action_bar_html += """
            <div class="quick-action-btn primary" onclick="handleAction('finalize')">
                <span>🔒</span>
                <span>Véglegesítés</span>
            </div>
        """
    
    if can_edit:
        action_bar_html += """
            <div class="quick-action-btn secondary" onclick="handleAction('edit')">
                <span>✏️</span>
                <span>Szerkesztés</span>
            </div>
        """
    
    # Always available actions
    action_bar_html += """
            <div class="quick-action-btn secondary" onclick="handleAction('export')">
                <span>📊</span>
                <span>Export</span>
            </div>
            <div class="quick-action-btn secondary" onclick="handleAction('print')">
                <span>🖨️</span>
                <span>Nyomtatás</span>
            </div>
        </div>
        
        <div class="action-separator"></div>
        
        <div class="quick-action-group" style="margin-left: auto; position: relative;">
            <div class="quick-action-btn secondary" onclick="toggleDropdown()" id="more-btn">
                <span>⋮</span>
            </div>
            <div class="dropdown-menu" id="dropdown-menu">
                <div class="dropdown-item" onclick="handleAction('copy')">
                    <span>📋</span>
                    <span>Másolás</span>
                </div>
                <div class="dropdown-item" onclick="handleAction('history')">
                    <span>📜</span>
                    <span>Előzmények</span>
                </div>
    """
    
    if can_delete:
        action_bar_html += """
                <div class="dropdown-item danger" onclick="handleAction('delete')">
                    <span>🗑️</span>
                    <span>Törlés</span>
                </div>
        """
    
    action_bar_html += """
            </div>
        </div>
    </div>
    """
    
    # JavaScript eseménykezelők
    action_bar_js = f"""
    <script>
    // Action handlers
    function handleAction(action) {{
        const actionData = {{
            action: action,
            offerId: {offer_id},
            timestamp: new Date().toISOString()
        }};
        
        // Store action in session state via hidden div
        const actionDiv = document.getElementById('action-trigger');
        if (actionDiv) {{
            actionDiv.setAttribute('data-action', JSON.stringify(actionData));
            actionDiv.click();
        }}
        
        // Close dropdown if open
        const dropdown = document.getElementById('dropdown-menu');
        if (dropdown) {{
            dropdown.classList.remove('show');
        }}
    }}
    
    // Back navigation
    function goBack() {{
        handleAction('back');
    }}
    
    // Dropdown toggle
    function toggleDropdown() {{
        const dropdown = document.getElementById('dropdown-menu');
        if (dropdown) {{
            dropdown.classList.toggle('show');
        }}
    }}
    
    // Close dropdown on outside click
    document.addEventListener('click', function(event) {{
        const moreBtn = document.getElementById('more-btn');
        const dropdown = document.getElementById('dropdown-menu');
        
        if (!moreBtn.contains(event.target) && !dropdown.contains(event.target)) {{
            dropdown.classList.remove('show');
        }}
    }});
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {{
        if (e.altKey) {{
            switch(e.key) {{
                case 'b':
                case 'ArrowLeft':
                    goBack();
                    break;
                case 'e':
                    if ({str(can_edit).lower()}) handleAction('edit');
                    break;
                case 'p':
                    handleAction('print');
                    break;
                case 'x':
                    handleAction('export');
                    break;
            }}
        }}
    }});
    </script>
    """
    
    # Render the action bar
    full_html = action_bar_html + action_bar_js
    components.html(full_html, height=80)
    
    # Hidden button to trigger Streamlit actions
    if st.button("", key=f"action-trigger_{offer_id}", help="Hidden action trigger"):
        pass

def handle_quick_action(offer_id: int) -> Optional[str]:
    """
    Handle quick action bar events.
    Returns the action type if an action was triggered.
    """
    # Check if action was triggered
    if st.session_state.get("action-trigger"):
        # This would need JavaScript-to-Streamlit communication
        # For now, we'll use button states
        return None
    
    return None

# Simpler version using native Streamlit buttons
def render_quick_action_bar_native(offer: Dict[str, Any], offer_id: int, key_prefix: str = "") -> Optional[str]:
    """
    Native Streamlit implementation of quick action bar.
    Returns the selected action.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        key_prefix: Optional prefix for widget keys to ensure uniqueness
    """
    # Create unique key suffix
    if not key_prefix:
        import hashlib
        import time
        # Use offer_id and timestamp to create unique keys
        key_prefix = hashlib.md5(f"{offer_id}_{time.time()}".encode()).hexdigest()[:8]
    
    # Get user info
    user = st.session_state.get("user", {})
    user_role = user.get("role", "")
    user_id = user.get("id", None)
    
    status = offer.get("status", "")
    offer_user_id = offer.get("user", {}).get("id") if isinstance(offer.get("user"), dict) else offer.get("user_id")
    
    # Permissions
    is_owner = user_id and offer_user_id == user_id
    is_operator = user_role in ["admin", "operator", "ügyintéző"]
    is_admin = user_role == "admin"
    
    can_edit = is_owner and status not in ["FINALIZED", "REJECTED_BY_USER"]
    can_confirm = is_operator and status in ["CREATED", "MODIFIED"]
    can_accept = is_owner and status == "CONFIRMED_BY_COMPANY"
    can_reject = is_owner and status == "CONFIRMED_BY_COMPANY"
    can_finalize = is_operator and status == "ACCEPTED_BY_USER"
    can_delete = is_admin and status != "FINALIZED"
    
    # Create action bar container
    action_container = st.container()
    
    with action_container:
        # Style injection
        st.markdown("""
        <style>
        div[data-testid="stHorizontalBlock"] {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            gap: 0.5rem;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Action buttons
        cols = st.columns([1, 1, 1, 1, 1, 1, 1, 1])
        action = None
        
        with cols[0]:
            if st.button("← Vissza", key=f"quick_back_{key_prefix}_{offer_id}", use_container_width=True):
                action = "back"
        
        col_idx = 1
        
        if can_confirm and col_idx < len(cols):
            with cols[col_idx]:
                if st.button("✓ Visszaigazolás", key=f"quick_confirm_{key_prefix}_{offer_id}", type="primary", use_container_width=True):
                    action = "confirm"
            col_idx += 1
        
        if can_accept and col_idx < len(cols):
            with cols[col_idx]:
                if st.button("✔️ Elfogadás", key=f"quick_accept_{key_prefix}_{offer_id}", type="primary", use_container_width=True):
                    action = "accept"
            col_idx += 1
        
        if can_reject and col_idx < len(cols):
            with cols[col_idx]:
                if st.button("✗ Elutasítás", key=f"quick_reject_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "reject"
            col_idx += 1
        
        if can_finalize and col_idx < len(cols):
            with cols[col_idx]:
                if st.button("🔒 Véglegesítés", key=f"quick_finalize_{key_prefix}_{offer_id}", type="primary", use_container_width=True):
                    action = "finalize"
            col_idx += 1
        
        if can_edit and col_idx < len(cols):
            with cols[col_idx]:
                if st.button("✏️ Szerkesztés", key=f"quick_edit_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "edit"
            col_idx += 1
        
        if col_idx < len(cols):
            with cols[col_idx]:
                if st.button("📊 Export", key=f"quick_export_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "export"
            col_idx += 1
        
        if col_idx < len(cols):
            with cols[col_idx]:
                if st.button("🖨️ Nyomtatás", key=f"quick_print_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "print"
        
        # More actions in expander
        with st.expander("⋮ További műveletek", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📋 Másolás", key=f"quick_copy_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "copy"
                if st.button("📜 Előzmények", key=f"quick_history_{key_prefix}_{offer_id}", use_container_width=True):
                    action = "history"
            
            with col2:
                if can_delete:
                    if st.button("🗑️ Törlés", key=f"quick_delete_{key_prefix}_{offer_id}", type="secondary", use_container_width=True):
                        action = "delete"
    
    return action