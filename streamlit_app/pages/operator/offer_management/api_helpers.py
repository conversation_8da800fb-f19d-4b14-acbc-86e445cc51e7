"""
API hívások és hibakezelési segédfunkciók az ajánlatkezeléshez.
Ezek a funkciók a jobb felhasználói élményt és a robusztusabb hibake<PERSON><PERSON><PERSON>,
v<PERSON><PERSON><PERSON> t<PERSON><PERSON> a reszponzív működést különböző eszközökön.
"""
import logging
import streamlit as st
import time
import json

# Try different import paths for formatting
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status
    except ImportError:
        # Fallback formatting function if import fails
        logging.warning("Could not import format_status function, using fallback")
        format_status = lambda x: x if x else "Ismeretlen"

logger = logging.getLogger(__name__)

def handle_api_error(error, operation_type):
    """
    API hibák egységes kezelése részletes hibaüzenetekkel és felhasználói visszajelzéssel.
    
    Args:
        error (str): A hibaüzenet.
        operation_type (str): A művelet típusa (pl. "ajánlatok betöltése").
    """
    error_message = str(error)
    
    # Hibaüzenet naplózása
    logger.error(f"API error during {operation_type}: {error_message}")
    
    # Import for showing error messages
    try:
        from streamlit_app.pages.operator.offer_management.ui_components import show_inline_error
    except ImportError:
        try:
            from pages.operator.offer_management.ui_components import show_inline_error
        except ImportError:
            try:
                from ui_components import show_inline_error
            except ImportError:
                # Fallback error display if imports fail
                def show_inline_error(message):
                    st.error(message)
    
    # 401/403 hibakódok kezelése (hitelesítési hibák)
    if "401" in error_message or "Unauthorized" in error_message or "403" in error_message:
        show_inline_error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        # Clear auth token
        if 'auth_token' in st.session_state:
            st.session_state.auth_token = None
        st.rerun()
    
    # 404 hibakódok kezelése (nem található erőforrás)
    elif "404" in error_message or "Not Found" in error_message:
        show_inline_error(f"A kért erőforrás nem található. ({operation_type})")
    
    # Kapcsolódási problémák kezelése
    elif "Connection" in error_message or "timeout" in error_message.lower():
        show_inline_error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
    
    # Egyéb hibák kezelése
    else:
        show_inline_error(f"Váratlan hiba történt: {error_message} ({operation_type})")

def fetch_offer_data_with_progress(query_params=None, page=1, page_size=20):
    """
    Ajánlatok betöltése vizuális folyamatjelzővel.
    
    Ez a függvény egy Streamlit progress bar-t jelenít meg, miközben
    betölti az ajánlatokat az API-ból, majd visszaadja az eredményt.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek az API híváshoz.
        page (int, optional): Az aktuális oldal száma a lapozáshoz. Defaults to 1.
        page_size (int, optional): Oldalméret a lapozáshoz. Defaults to 20.
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    try:
        from pages.operator.offer_management.data_processing import load_offers_with_pagination
    except ImportError:
        from data_processing import load_offers_with_pagination
    
    # Folyamatjelző megjelenítése
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Hálózati kérés előtti szöveg
    status_text.text("Ajánlatok betöltése...")
    
    try:
        # Adatok betöltése a lazy_load_cache segítségével
        try:
            from pages.operator.offer_management.state_management import lazy_load_cache
        except ImportError:
            from state_management import lazy_load_cache
                
        # Cache kulcs generálása a lekérdezési paraméterekből
        cache_params = query_params.copy() if query_params else {}
        cache_params.update({"page": page, "page_size": page_size})
        import json
        cache_key = f"offers_{json.dumps(cache_params, sort_keys=True)}"
        
        success, result = lazy_load_cache(
            cache_key=cache_key,
            data_loader_func=lambda: load_offers_with_pagination(query_params, page, page_size),
            cache_ttl=120  # 2 perces cache az ajánlatokhoz (gyakran változhat)
        )
        
        # Folyamatjelző frissítése
        progress_bar.progress(50)
        
        if success:
            # Ajánlatok ellenőrzése és feldolgozása
            if isinstance(result, dict) and "items" in result:
                offers = result["items"]
                total = result.get("total", 0)
                
                # Folyamatjelző frissítése
                progress_bar.progress(100)
                status_text.text(f"{len(offers)} ajánlat betöltve ({total} találatból)")
            else:
                # Ha az eredmény nem a várt formátumú, de sikeres
                progress_bar.progress(100)
                status_text.text("Adatok betöltve")
        else:
            # Hiba esetén
            progress_bar.progress(100)
            status_text.text(f"Hiba: {result}")
            # Hiba kezelése
            handle_api_error(result, "ajánlatok betöltése")
        
        # Várakozás, hogy a felhasználó lássa az eredményt
        import time
        time.sleep(0.5)
        
        # Folyamatjelző és státusz törlése
        progress_bar.empty()
        status_text.empty()
        
        return success, result
        
    except Exception as e:
        # Folyamatjelző és státusz törlése hiba esetén
        progress_bar.empty()
        status_text.empty()
        
        # Hiba kezelése
        handle_api_error(e, "ajánlatok betöltése")
        
        # Hiba visszaadása
        logger.error(f"Error in fetch_offer_data_with_progress: {str(e)}")
        return False, str(e)

def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """
    Biztonságos API hívás végrehajtása egységes hibakezeléssel.
    
    Ez a függvény egy API hívást próbál végrehajtani, és egységesen kezeli a lehetséges hibákat.
    Hasznos az ismétlődő try-except blokkok elkerülésére az alkalmazásban.
    
    Args:
        api_function (callable): Az API funkció, amelyet meg kell hívni.
        error_operation_name (str): A művelet neve hibaüzenetekhez.
        *args: További pozíciós argumentumok az API funkcióhoz.
        **kwargs: További kulcsszó argumentumok az API funkcióhoz.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az API hívás eredményét vagy a hibaüzenetet tartalmazza.
    """
    try:
        logger.info(f"Calling API function: {api_function.__name__} with args: {args}, kwargs: {kwargs}")
        success, result = api_function(*args, **kwargs)
        
        if not success:
            logger.error(f"API returned error for {error_operation_name}: {result}")
            handle_api_error(result, error_operation_name)
        
        return success, result
    except Exception as e:
        logger.error(f"Exception in {error_operation_name}: {str(e)}")
        handle_api_error(e, error_operation_name)
        return False, str(e)

def extract_status_from_offer(offer, status_key="status"):
    """
    Biztonságosan kinyeri az ajánlat státuszát és formázza azt.
    
    Args:
        offer (dict): Az ajánlat adatai.
        status_key (str, optional): A kulcs, amely alatt a státusz található. Defaults to "status".
        
    Returns:
        tuple: (status_code, formatted_status), ahol status_code az eredeti kód és formatted_status a formázott státusz.
    """
    if not offer:
        return None, "Ismeretlen"
    
    status_code = offer.get(status_key)
    formatted_status = format_status(status_code)
    return status_code, formatted_status

def get_paginated_data(api_func, params, page_size=None, mobile_page_size=None, tablet_page_size=None):
    """
    Eszközmérethez igazított lapozási segédfüggvény API hívásokhoz.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        page_size (int, optional): Alapértelmezett lapozási méret. Defaults to None.
        mobile_page_size (int, optional): Mobil eszközökön használt lapozási méret. Defaults to None.
        tablet_page_size (int, optional): Tablet eszközökön használt lapozási méret. Defaults to None.
        
    Returns:
        tuple: (success, data, total_items)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Eszközmérethez igazított lapozási méret
    if is_mobile and mobile_page_size is not None:
        effective_page_size = mobile_page_size
    elif is_tablet and tablet_page_size is not None:
        effective_page_size = tablet_page_size
    elif page_size is not None:
        effective_page_size = page_size
    else:
        # Alapértelmezett értékek eszköz szerint
        effective_page_size = 5 if is_mobile else (10 if is_tablet else 20)
    
    # Aktuális oldal lekérése a session state-ből
    current_page = st.session_state.get("current_page", 1)
    
    # Lapozási paraméterek hozzáadása
    pagination_params = {
        "offset": (current_page - 1) * effective_page_size,
        "limit": effective_page_size
    }
    
    # Paraméterek összeolvasztása
    merged_params = {**params, **pagination_params}
    
    # API hívás
    try:
        success, result = api_func(merged_params)
        
        if success:
            # Ellenőrizzük, hogy a válasz tartalmaz-e meta információkat a teljes elemszámról
            if isinstance(result, dict) and "meta" in result and "total" in result["meta"]:
                total_items = result["meta"]["total"]
                data = result.get("data", [])
                return success, data, total_items
            
            # Ha a válasz csak egy lista, akkor a teljes elemszámot nem ismerjük
            if isinstance(result, list):
                return success, result, len(result)
            
            # Egyéb esetekben próbáljunk a result-ból adatokat kinyerni
            if isinstance(result, dict):
                data = result.get("data", result)
                total = result.get("total", len(data) if isinstance(data, list) else 0)
                return success, data, total
            
            # Visszatérünk az eredeti eredménnyel
            return success, result, 0
            
        else:
            logger.error(f"API hívás sikertelen: {result}")
            return False, result, 0
            
    except Exception as e:
        logger.error(f"Hiba az API hívás során: {str(e)}")
        return False, str(e), 0

def get_responsive_data(api_func, params, simplify_for_mobile=True):
    """
    Reszponzív adatlekérés, amely eszközmérethez igazított adatszerkezetet ad vissza.
    Mobil eszközökön egyszerűsíti az adatszerkezetet a gyorsabb betöltés érdekében.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        simplify_for_mobile (bool, optional): Mobilon egyszerűsítse-e az adatszerkezetet. Defaults to True.
        
    Returns:
        tuple: (success, data)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # API hívás
    try:
        success, result = api_func(params)
        
        if not success:
            return False, result
        
        # Mobil esetén egyszerűsítés, ha a flag engedélyezi
        if (is_mobile or is_tablet) and simplify_for_mobile:
            # Adatszerkezet egyszerűsítése eszköz szerint
            if isinstance(result, list):
                simplified_result = []
                for item in result:
                    if isinstance(item, dict):
                        # Csak a fontosabb mezőket tartjuk meg
                        simplified_item = {}
                        
                        # Kulcsok kiválasztása az eszköz alapján
                        important_keys = get_important_keys(is_mobile, is_tablet)
                        
                        # Fontos mezők megőrzése
                        for key in important_keys:
                            if key in item:
                                simplified_item[key] = item[key]
                        
                        # Beágyazott objektumok egyszerűsítése
                        for key, value in item.items():
                            if isinstance(value, dict) and not key in simplified_item:
                                # A beágyazott objektumból csak az id és name mezőket tartjuk meg
                                if "id" in value:
                                    simplified_item[f"{key}_id"] = value["id"]
                                if "name" in value:
                                    simplified_item[f"{key}_name"] = value["name"]
                        
                        simplified_result.append(simplified_item)
                    else:
                        simplified_result.append(item)
                
                return True, simplified_result
                
            elif isinstance(result, dict):
                simplified_result = {}
                
                # Ha van data kulcs, akkor azon alkalmazzuk az egyszerűsítést
                if "data" in result and isinstance(result["data"], list):
                    simplified_result["data"] = get_responsive_data(lambda _: (True, result["data"]), None, True)[1]
                    
                    # Meta adatok megtartása
                    if "meta" in result:
                        simplified_result["meta"] = result["meta"]
                        
                    return True, simplified_result
                
                # Fontos mezők megőrzése
                important_keys = get_important_keys(is_mobile, is_tablet)
                for key in important_keys:
                    if key in result:
                        simplified_result[key] = result[key]
                
                return True, simplified_result
        
        # Ha nincs egyszerűsítés, vagy nem mobil/tablet, visszaadjuk az eredeti adatokat
        return True, result
    
    except Exception as e:
        logger.error(f"Hiba a reszponzív adatlekérés során: {str(e)}")
        return False, str(e)

def get_important_keys(is_mobile=False, is_tablet=False):
    """
    Fontosabb mezők listája az eszköz mérete alapján.
    
    Args:
        is_mobile (bool, optional): Mobil eszköz esetén. Defaults to False.
        is_tablet (bool, optional): Tablet eszköz esetén. Defaults to False.
        
    Returns:
        list: Fontosabb mezők listája
    """
    # Minimális mezők mobilon
    if is_mobile:
        return [
            "id", "name", "title", "status", "created_at", "quantity", "quantity_in_kg", 
            "price", "confirmed_price", "delivery_date", "product_type_id", "user_id"
        ]
    
    # Bővebb mezők tableten
    if is_tablet:
        return [
            "id", "name", "title", "description", "status", "created_at", "updated_at",
            "quantity", "quantity_in_kg", "price", "confirmed_price", "delivery_date", 
            "product_type_id", "user_id", "note", "tags"
        ]
    
    # Minden mező asztali eszközökön
    return None  # None esetén nem szűrünk mezőket

def fetch_responsive_offer_data(query_params=None):
    """
    Reszponzív ajánlat adatok lekérése az eszköz méretéhez igazítva.
    Ez a függvény a fetch_offer_data_with_progress kiterjesztése reszponzív funkcionalitással.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek. Defaults to None.
        
    Returns:
        tuple: (success, result) formában
    """
    # Képernyőméret információk lekérése és lapozási méretek meghatározása
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Eszközmérethez igazított lapméret
    page_size = 5 if is_mobile else (10 if is_tablet else 20)
    
    # Aktuális oldal lekérése a session state-ből
    page = st.session_state.get("current_page", 1)
    
    # Folyamatjelző mobilon és tabeten kompaktabb
    if is_mobile or is_tablet:
        # Egyszerűbb folyamatjelző használata
        with st.spinner("Adatok betöltése..."):
            return fetch_offer_data_with_progress(
                query_params=query_params,
                page=page,
                page_size=page_size
            )
    else:
        # Teljes folyamatjelző használata asztali nézetben
        return fetch_offer_data_with_progress(
            query_params=query_params,
            page=page,
            page_size=page_size
        )