"""
Reszponzív UI segédfüggvények az ajánlatok kezelése oldalhoz.
Ezek a függvények az általános utils.responsive_ui.py modul specifikus alkalmazásai.
"""
import streamlit as st
import logging

# Try multiple import paths with fallbacks for responsive UI utilities
try:
    # Try absolute import first
    from streamlit_app.utils.responsive_ui import inject_screen_detection as base_inject_screen_detection
except ImportError:
    try:
        # Try regular app-relative import
        from utils.responsive_ui import inject_screen_detection as base_inject_screen_detection
    except ImportError:
        # Fallback implementation if import fails
        logging.warning("Could not import responsive_ui, using local implementation")
        
        # Define a basic version of the function locally
        def base_inject_screen_detection():
            """Basic screen detection fallback implementation"""
            # Set default values if not already in session state
            if "is_mobile" not in st.session_state:
                st.session_state.is_mobile = False
            if "is_tablet" not in st.session_state:
                st.session_state.is_tablet = False
            if "screen_width" not in st.session_state:
                st.session_state.screen_width = 1200
            
            # Inject JavaScript for screen detection
            st.markdown("""
            <script>
                const reportScreenWidth = () => {
                    // Report screen width to Streamlit
                    const width = window.innerWidth;
                    const isMobile = width < 768;
                    const isTablet = width >= 768 && width < 992;
                    
                    // Send screen info to Streamlit
                    window.parent.postMessage({
                        type: "streamlit:setComponentValue",
                        value: {
                            width: width,
                            is_mobile: isMobile,
                            is_tablet: isTablet
                        }
                    }, "*");
                };
                
                // Call immediately and on resize
                reportScreenWidth();
                window.addEventListener('resize', reportScreenWidth);
            </script>
            """, unsafe_allow_html=True)

def inject_screen_detection():
    """
    Képernyőméret érzékelés a session state-be.
    Ez a függvény az offer_management oldalon használt képernyőméret detektáláshoz.
    
    Továbbfejlesztett verzió, ami az alapvető képernyőméret érzékelés mellett
    további oldalspecifikus logikát is tartalmaz.
    """
    # Alapvető képernyőméret érzékelés
    base_inject_screen_detection()
    
    # Fejlesztői mód ellenőrzése és tárolása a session state-ben
    if "dev_mode" not in st.session_state:
        st.session_state.dev_mode = False
    
    # Ha már van ismert képernyőméret, logoljuk
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    screen_width = st.session_state.get('screen_width', 1200)
    
    # Debug célokra
    if st.session_state.get('dev_mode', False):
        print(f"Screen detection: mobile={is_mobile}, tablet={is_tablet}, width={screen_width}px")

def inject_keyboard_shortcuts():
    """
    JavaScript alapú billentyűzetkombinációk befecskendezése a streamlit alkalmazásba.
    
    Ez a függvény a következő billentyűparancsokat teszi lehetővé:
    - J/K: Le/Fel navigálás az ajánlatok között
    - N: Új oldal (következő oldal lapozáshoz)
    - P: Előző oldal
    - F: Fókusz a szűrőkre
    - R: Adatok újratöltése
    - Escape: Vissza a listanézethez (részletes nézetből)
    - 1-5: Gyors státuszváltás (ha részletes nézetben vagyunk)
    - Alt+S: Mentés (szerkesztési mód)
    - Alt+E: Szerkesztés mód váltása
    """
    # Billentyűzetkombinációk session state inicializálása
    if "keyboard_shortcuts_initialized" not in st.session_state:
        st.session_state.keyboard_shortcuts_initialized = False
    
    # Ha már inicializálva van, ne csináljunk semmit
    if st.session_state.keyboard_shortcuts_initialized:
        return
    
    # A JavaScript kód, ami a billentyűparancsokat kezeli
    js_code = """
    <script>
    // Az oldal betöltésekor aktiváljuk a billentyűzetes vezérlést
    document.addEventListener('DOMContentLoaded', function() {
        // Megváltozott e már a jelenlegi key handler
        let handlerActive = false;
        
        // Ellenőrizzük minden 100ms-ban, hogy található-e már a DOM-ban gomb/táblázat
        const checkInterval = setInterval(function() {
            if (!handlerActive && 
                (document.querySelector('table') || 
                document.querySelector('button[kind="primary"]'))) {
                
                // A DOM már betöltődött, inicializáljuk a billentyűzetkezelőt
                initKeyboardHandler();
                handlerActive = true;
                clearInterval(checkInterval);
            }
        }, 100);
        
        function initKeyboardHandler() {
            document.addEventListener('keydown', function(e) {
                // Ne kezeljük a billentyűeseményeket, ha input mezőben vagyunk
                if (document.activeElement.tagName === 'INPUT' || 
                    document.activeElement.tagName === 'TEXTAREA') {
                    return;
                }
                
                // Aktuális URL ellenőrzése, hogy részletes nézetben vagyunk-e
                const isDetailView = window.location.search.includes('offer_id=');
                
                // A billentyűkombinációk kezelése
                switch(e.key) {
                    case 'j': // Le (következő elem)
                        navigateList(1);
                        break;
                    case 'k': // Fel (előző elem)
                        navigateList(-1);
                        break;
                    case 'n': // Következő oldal
                        clickButton('Következő oldal');
                        break;
                    case 'p': // Előző oldal
                        clickButton('Előző oldal');
                        break;
                    case 'f': // Fókusz a szűrőkre
                        focusOnElement('input[aria-label="Keresés"]');
                        break;
                    case 'r': // Adatok újratöltése
                        clickButton('🔍 Keresés');
                        break;
                    case 'Escape': // Visszatérés a listához
                        if (isDetailView) {
                            clickButton('⬅️ Vissza a listához');
                        }
                        
                        // Streamlit session state módosítása
                        window.parent.postMessage({
                            type: "streamlit:setSessionState",
                            data: { clear_selected_offer: true }
                        }, "*");
                        
                        // Oldal újratöltése után visszafutunk a listanézetbe
                        setTimeout(() => {
                            const runButton = document.querySelector('button[kind="primaryFormSubmit"]');
                            if (runButton) {
                                runButton.click();
                            }
                        }, 100);
                        break;
                    case '1': // Gyors státuszváltás
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                        if (isDetailView) {
                            const status_idx = parseInt(e.key) - 1;
                            const status_buttons = document.querySelectorAll('button[aria-label*="Státusz"]');
                            if (status_buttons.length > status_idx) {
                                status_buttons[status_idx].click();
                            }
                        }
                        break;
                }
                
                // Alt billentyű kombinációk
                if (e.altKey) {
                    switch(e.key) {
                        case 's': // Alt+S - Mentés (szerkesztési mód)
                            const saveButton = document.querySelector('button:contains("Mentés"), button:contains("💾")');
                            if (saveButton) {
                                saveButton.click();
                                e.preventDefault();
                            }
                            break;
                        case 'e': // Alt+E - Szerkesztés mód váltása
                            const editButton = document.querySelector('button:contains("Szerkesztés"), button:contains("✏️")');
                            if (editButton) {
                                editButton.click();
                                e.preventDefault();
                            }
                            break;
                        case 'r': // Alt+R - Oldal frissítése
                            const runButton = document.querySelector('button[kind="primaryFormSubmit"]');
                            if (runButton) {
                                runButton.click();
                                e.preventDefault();
                            }
                            break;
                    }
                }
            });
        }
        
        // Segédfüggvények
        function navigateList(direction) {
            const rows = document.querySelectorAll('table tbody tr');
            if (rows.length === 0) return;
            
            // Megkeressük, hogy melyik sor van kijelölve
            let activeIndex = -1;
            rows.forEach((row, index) => {
                if (row.classList.contains('selected') || row.hasAttribute('data-selected')) {
                    activeIndex = index;
                }
            });
            
            // Kiválasztjuk a következő/előző sort
            let newIndex = activeIndex + direction;
            if (newIndex < 0) newIndex = 0;
            if (newIndex >= rows.length) newIndex = rows.length - 1;
            
            // Csak ha tényleg változott az index, akkor kattintunk
            if (newIndex !== activeIndex) {
                const targetRow = rows[newIndex];
                // Megkeressük a sorban lévő kattintható elemet
                const clickable = targetRow.querySelector('a, button');
                if (clickable) clickable.click();
            }
        }
        
        function clickButton(textContent) {
            // Megkeressük a gombot szöveg alapján
            const buttons = Array.from(document.querySelectorAll('button'));
            const button = buttons.find(btn => 
                btn.textContent.trim().includes(textContent));
            
            if (button) button.click();
        }
        
        function focusOnElement(selector) {
            const element = document.querySelector(selector);
            if (element) element.focus();
        }
    });
    
    // A document.querySelector(':contains') polyfill
    jQuery.expr[':'].contains = function(a, i, m) {
        return jQuery(a).text().toUpperCase().indexOf(m[3].toUpperCase()) >= 0;
    };
    </script>
    """
    
    # Beillesztjük a JavaScript kódot
    st.markdown(js_code, unsafe_allow_html=True)
    
    # Beállítjuk, hogy már inicializáltuk
    st.session_state.keyboard_shortcuts_initialized = True
    
    # Segítő tippek megjelenítése (ha fejlesztői módban vagyunk)
    if st.session_state.get('dev_mode', False):
        with st.expander("⌨️ Billentyűparancsok", expanded=False):
            st.markdown("""
            - **J/K**: Le/Fel navigálás az ajánlatok között
            - **N/P**: Következő/Előző oldal
            - **F**: Fókusz a szűrőkre
            - **R**: Adatok újratöltése
            - **Escape**: Vissza a listához (részletes nézetből)
            - **1-5**: Gyors státuszváltás (részletes nézetben)
            - **Alt+S**: Mentés (szerkesztési mód)
            - **Alt+E**: Szerkesztés mód váltása
            - **Alt+R**: Oldal frissítése
            """)

def display_mobile_offer_card(offer, index, on_click_func=None):
    """
    Ajánlat kártya megjelenítése mobilnézetben.
    
    Args:
        offer (dict): Az ajánlat adatai
        index (int): Az ajánlat indexe a listában
        on_click_func (function, optional): Kattintásra meghívandó függvény
    """
    # Try multiple import paths with fallbacks for formatting utilities
    try:
        # Try absolute import first
        from streamlit_app.utils.formatting import format_price, format_quantity
    except ImportError:
        try:
            # Try regular app-relative import
            from utils.formatting import format_price, format_quantity
        except ImportError:
            # Fallback formatting functions if import fails
            logging.warning("Could not import formatting functions, using fallbacks")
            format_price = lambda x: f"{x:,.0f} Ft" if x else "-"
            format_quantity = lambda x: f"{x:,.2f}" if x else "-"
    
    # Define fallback functions for responsive UI utilities
    def get_theme_colors():
        """Fallback theme colors"""
        return {
            "primary": "#1E88E5",
            "secondary": "#26A69A",
            "background": "#FFFFFF",
            "text": "#212121",
            "accent": "#FF4081"
        }
    
    def display_card(title, content, icon=None):
        """Fallback card display function"""
        st.markdown(f"### {icon} {title}" if icon else f"### {title}")
        st.markdown(content, unsafe_allow_html=True)
        
    # Try to import the real responsive_ui utilities
    try:
        # Try absolute import first
        from streamlit_app.utils.responsive_ui import get_theme_colors as real_get_theme_colors
        from streamlit_app.utils.responsive_ui import display_card as real_display_card
        # Override with real functions if import succeeds
        get_theme_colors = real_get_theme_colors
        display_card = real_display_card
    except ImportError:
        try:
            # Try regular app-relative import
            from utils.responsive_ui import get_theme_colors as real_get_theme_colors
            from utils.responsive_ui import display_card as real_display_card
            # Override with real functions if import succeeds
            get_theme_colors = real_get_theme_colors
            display_card = real_display_card
        except ImportError:
            # We'll use our fallback implementations
            logging.warning("Could not import responsive_ui utilities, using fallbacks")
    
    colors = get_theme_colors()
    
    # Státusz színek
    status_colors = {
        'CREATED': '#90CAF9',
        'CONFIRMED_BY_COMPANY': '#66BB6A',
        'ACCEPTED_BY_USER': '#4CAF50',
        'REJECTED_BY_USER': '#EF5350',
        'FINALIZED': '#9C27B0',
        'CANCELLED': '#9E9E9E'
    }
    
    # Magyar fordítás a státuszokhoz
    status_translations = {
        'CREATED': 'Létrehozva',
        'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
        'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
        'REJECTED_BY_USER': 'Felhasználó által elutasítva',
        'FINALIZED': 'Véglegesítve',
        'CANCELLED': 'Törölve'
    }
    
    status = offer.get('status', '')
    status_color = status_colors.get(status, '#9E9E9E')
    status_text = status_translations.get(status, status)
    
    # Ajánlat részletes adatai
    offer_id = offer.get('id', 'N/A')
    product_name = offer.get('product_name', offer.get('product_type_id', 'Ismeretlen termék'))
    quantity = offer.get('quantity_in_kg', offer.get('quantity', 0))
    price = offer.get('confirmed_price', offer.get('price', 0))
    user_name = offer.get('user_name', 'Ismeretlen felhasználó')
    delivery_date = offer.get('delivery_date', 'Ismeretlen dátum')
    
    # Szállítási dátum formázása
    if hasattr(delivery_date, 'strftime'):
        delivery_date_str = delivery_date.strftime('%Y-%m-%d')
    else:
        delivery_date_str = str(delivery_date)
    
    # Kártya tartalom összeállítása
    content = f"""
    <div style="margin-bottom: 10px;">
        <span style="display: inline-block; background-color: {status_color}; color: white; 
               padding: 3px 8px; border-radius: 10px; font-size: 0.8em; font-weight: bold;">
            {status_text}
        </span>
    </div>
    
    <div style="display: flex; flex-direction: column; gap: 5px;">
        <div><strong>Termék:</strong> {product_name}</div>
        <div><strong>Mennyiség:</strong> {format_quantity(quantity)}</div>
        <div><strong>Ár:</strong> {format_price(price) if price > 0 else 'Nem beállított'}</div>
        <div><strong>Termelő:</strong> {user_name}</div>
        <div><strong>Szállítás:</strong> {delivery_date_str}</div>
    </div>
    """
    
    # Kártya címe az ID-val
    card_title = f"Ajánlat #{offer_id}"
    
    # Kártya megjelenítése
    with st.container():
        display_card(
            title=card_title,
            content=content,
            icon="📦"
        )
        
        # Részletek gomb
        if st.button(f"Részletek", key=f"offer_details_{index}_{offer_id}"):
            if on_click_func:
                on_click_func()

def render_responsive_filters(filter_components, key_prefix="filter"):
    """
    Reszponzív szűrők megjelenítése a képernyőméret alapján.
    Mobilon egy oszlopban, nagyobb képernyőn több oszlopban.
    
    Args:
        filter_components (list): Szűrő komponensek listája, mindegyik egy dict:
                     {"label": "Címke", "component": render_func, "key": "egyedi_kulcs"}
        key_prefix (str): Egyedi prefix a session state kulcsokhoz
                          
    Returns:
        dict: A szűrők értékeit adja vissza
    """
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    
    # Eredmények tárolása
    filter_results = {}
    
    # Megjelenítés az eszköz típusa alapján
    if is_mobile:
        # Mobilon egy oszlopban jelenítjük meg a szűrőket
        with st.expander("📱 Szűrők", expanded=False):
            for filter_item in filter_components:
                label = filter_item.get("label", "")
                component_func = filter_item.get("component")
                filter_key = filter_item.get("key", f"{key_prefix}_{label}")
                
                # Címke megjelenítése
                st.markdown(f"**{label}:**")
                
                # Komponens renderelése
                filter_results[filter_key] = component_func()
    
    elif is_tablet:
        # Tableten két oszlopban jelenítjük meg a szűrőket
        with st.expander("📊 Szűrők", expanded=False):
            for i in range(0, len(filter_components), 2):
                col1, col2 = st.columns(2)
                
                with col1:
                    if i < len(filter_components):
                        filter_item = filter_components[i]
                        label = filter_item.get("label", "")
                        component_func = filter_item.get("component")
                        filter_key = filter_item.get("key", f"{key_prefix}_{label}")
                        
                        # Címke megjelenítése
                        st.markdown(f"**{label}:**")
                        
                        # Komponens renderelése
                        filter_results[filter_key] = component_func()
                
                with col2:
                    if i + 1 < len(filter_components):
                        filter_item = filter_components[i + 1]
                        label = filter_item.get("label", "")
                        component_func = filter_item.get("component")
                        filter_key = filter_item.get("key", f"{key_prefix}_{label}")
                        
                        # Címke megjelenítése
                        st.markdown(f"**{label}:**")
                        
                        # Komponens renderelése
                        filter_results[filter_key] = component_func()
    
    else:
        # Asztali nézeten megfelelő számú oszlopban jelenítjük meg a szűrőket
        num_cols = min(len(filter_components), 4)  # Maximum 4 oszlopot használunk
        cols = st.columns(num_cols)
        
        for i, filter_item in enumerate(filter_components):
            with cols[i % num_cols]:
                label = filter_item.get("label", "")
                component_func = filter_item.get("component")
                filter_key = filter_item.get("key", f"{key_prefix}_{label}")
                
                # Címke megjelenítése
                st.markdown(f"**{label}:**")
                
                # Komponens renderelése
                filter_results[filter_key] = component_func()
    
    return filter_results