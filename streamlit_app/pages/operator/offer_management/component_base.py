"""
Component base classes for the offer management UI system.

This module defines the foundation for a modular, reusable component system
that standardizes how UI components are initialized, rendered, and managed.
Components follow a React-like pattern with props, state, and lifecycle methods.
"""
import streamlit as st
import uuid
import inspect
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable, Union, Tuple, TypeVar

# Setup logging
logger = logging.getLogger(__name__)

# Type definitions
T = TypeVar('T')
ComponentId = str  # Type alias for component IDs
StateDict = Dict[str, Any]  # Type alias for state dictionaries
PropDict = Dict[str, Any]  # Type alias for property dictionaries


class ComponentBase(ABC):
    """
    Base class for all UI components in the offer management system.

    This abstract class defines the common interface and behavior for all UI components.
    Components are initialized with props (read-only input values) and maintain internal
    state that can change during the component lifecycle.

    Usage:
        class MyComponent(ComponentBase):
            def __init__(self, label: str, value: int = 0):
                props = {'label': label, 'value': value}
                super().__init__(props=props)
                
            def render(self) -> None:
                st.write(f"{self.props['label']}: {self.state.get('value', self.props['value'])}")
                
                if st.button(f"Increment {self.component_id}"):
                    self.set_state({'value': self.state.get('value', self.props['value']) + 1})
    """

    def __init__(self, props: Optional[PropDict] = None, component_id: Optional[ComponentId] = None):
        """
        Initialize the component with its properties and a unique identifier.

        Args:
            props: Read-only input values for the component
            component_id: Optional unique identifier for the component.
                       If not provided, a unique ID is generated.
        """
        self.props = props or {}
        self.component_id = component_id or self._generate_component_id()
        self._initialize_state()
        self.is_mounted = False
        self._did_render = False

    def _initialize_state(self) -> None:
        """Initialize the component's state in the session state."""
        if not hasattr(st.session_state, 'component_states'):
            st.session_state.component_states = {}
            
        if self.component_id not in st.session_state.component_states:
            st.session_state.component_states[self.component_id] = {}

    def _generate_component_id(self) -> ComponentId:
        """Generate a unique component ID based on the class name and a UUID."""
        class_name = self.__class__.__name__
        unique_id = str(uuid.uuid4())[:8]
        return f"{class_name}_{unique_id}"

    @property
    def state(self) -> StateDict:
        """Get the current state of the component from the session state."""
        return st.session_state.component_states.get(self.component_id, {})

    def set_state(self, new_state: StateDict) -> None:
        """
        Update the component's state.

        Args:
            new_state: Dictionary containing state updates
        """
        if not hasattr(st.session_state, 'component_states'):
            st.session_state.component_states = {}
            
        if self.component_id not in st.session_state.component_states:
            st.session_state.component_states[self.component_id] = {}
            
        # Update state with new values
        st.session_state.component_states[self.component_id].update(new_state)
        
        # Trigger on_state_change if the component has already rendered
        if self._did_render:
            self.on_state_change(new_state)

    def get_state_value(self, key: str, default: T = None) -> T:
        """
        Get a value from the component's state with an optional default value.

        Args:
            key: The state key to retrieve
            default: Default value if the key doesn't exist

        Returns:
            The state value or the default value
        """
        return self.state.get(key, default)

    def mount(self) -> None:
        """Mount the component and call the component_did_mount lifecycle method."""
        if not self.is_mounted:
            self.component_did_mount()
            self.is_mounted = True

    def unmount(self) -> None:
        """Unmount the component and call the component_will_unmount lifecycle method."""
        if self.is_mounted:
            self.component_will_unmount()
            self.is_mounted = False

    def component_did_mount(self) -> None:
        """
        Lifecycle method called when the component is first mounted.
        Override this method to perform initialization tasks.
        """
        pass

    def component_will_unmount(self) -> None:
        """
        Lifecycle method called just before the component is unmounted.
        Override this method to perform cleanup tasks.
        """
        pass

    def on_state_change(self, changed_state: StateDict) -> None:
        """
        Lifecycle method called whenever the component's state changes.
        Override this method to react to state changes.

        Args:
            changed_state: Dictionary containing the state keys that were changed
        """
        pass

    def should_update(self, new_props: PropDict) -> bool:
        """
        Determine if the component should update when new props are received.
        Override this method to optimize rendering performance.

        Args:
            new_props: New properties that would be assigned to the component

        Returns:
            True if the component should re-render, False otherwise
        """
        # Default implementation always updates when props change
        return new_props != self.props

    @abstractmethod
    def render(self) -> Any:
        """
        Render the component UI.
        This method must be implemented by all component subclasses.

        Returns:
            Any values that should be returned from the component
        """
        pass

    def __call__(self) -> Any:
        """
        Make the component callable, which mounts and renders it.
        This allows using component instances directly as functions.

        Returns:
            The return value of the render method
        """
        self.mount()
        self._did_render = True
        return self.render()


class CompositeComponent(ComponentBase):
    """
    A component that can contain and manage child components.
    
    This class provides methods for managing a collection of child components,
    including adding, removing, and rendering them.
    """

    def __init__(self, props: Optional[PropDict] = None, component_id: Optional[ComponentId] = None):
        """Initialize the composite component."""
        super().__init__(props, component_id)
        self._children = {}  # Dictionary of child components by ID

    def add_child(self, child: ComponentBase, child_id: Optional[str] = None) -> ComponentId:
        """
        Add a child component to this composite component.
        
        Args:
            child: The child component to add
            child_id: Optional ID for the child. If not provided, the child's existing ID is used.
            
        Returns:
            The ID of the added child
        """
        if child_id is not None:
            child.component_id = child_id
            
        self._children[child.component_id] = child
        return child.component_id

    def remove_child(self, child_id: ComponentId) -> None:
        """
        Remove a child component by its ID.
        
        Args:
            child_id: The ID of the child component to remove
        """
        if child_id in self._children:
            self._children[child_id].unmount()
            del self._children[child_id]

    def get_child(self, child_id: ComponentId) -> Optional[ComponentBase]:
        """
        Get a child component by its ID.
        
        Args:
            child_id: The ID of the child component to retrieve
            
        Returns:
            The child component if found, None otherwise
        """
        return self._children.get(child_id)

    def render_children(self) -> Dict[ComponentId, Any]:
        """
        Render all child components.
        
        Returns:
            Dictionary mapping child IDs to their render results
        """
        results = {}
        for child_id, child in self._children.items():
            results[child_id] = child()
        return results

    def component_will_unmount(self) -> None:
        """Unmount all child components when this component is unmounted."""
        for child in self._children.values():
            child.unmount()
        super().component_will_unmount()


class FormComponent(ComponentBase):
    """
    Base class for form input components.
    
    This class provides common functionality for form fields, including
    validation, error handling, and value management.
    """

    def __init__(
        self,
        label: str,
        value: Any = None,
        required: bool = False,
        validator: Optional[Callable[[Any], Tuple[bool, str]]] = None,
        component_id: Optional[ComponentId] = None
    ):
        """
        Initialize a form component.
        
        Args:
            label: The form field label
            value: Initial value for the field
            required: Whether the field is required
            validator: Optional validation function that returns (is_valid, error_message)
            component_id: Optional unique ID for the component
        """
        props = {
            'label': label,
            'initial_value': value,
            'required': required
        }
        super().__init__(props, component_id)
        self._validator = validator
        
        # Initialize state with value and validation status
        self.set_state({
            'value': value,
            'touched': False,
            'error': None,
            'is_valid': True
        })

    def set_value(self, value: Any) -> None:
        """
        Set the value of the form field and validate it.
        
        Args:
            value: The new value for the field
        """
        # Update the value in state
        self.set_state({'value': value, 'touched': True})
        
        # Validate the new value
        self.validate()

    def validate(self) -> bool:
        """
        Validate the current value of the field.
        
        Returns:
            True if the field value is valid, False otherwise
        """
        value = self.get_state_value('value')
        error = None
        is_valid = True
        
        # Check required fields
        if self.props.get('required', False) and (value is None or value == ''):
            error = f"A(z) {self.props['label']} mező kitöltése kötelező"
            is_valid = False
        # If we have a custom validator, use it
        elif self._validator is not None:
            is_valid, error = self._validator(value)
        
        # Update validation state
        self.set_state({
            'error': error,
            'is_valid': is_valid
        })
        
        return is_valid

    def reset(self) -> None:
        """Reset the form field to its initial state."""
        self.set_state({
            'value': self.props.get('initial_value'),
            'touched': False,
            'error': None,
            'is_valid': True
        })

    def get_value(self) -> Any:
        """Get the current value of the form field."""
        return self.get_state_value('value')

    def has_error(self) -> bool:
        """Check if the field has a validation error."""
        return not self.get_state_value('is_valid', True)

    def get_error(self) -> Optional[str]:
        """Get the current validation error message, if any."""
        return self.get_state_value('error')


class FormGroup(CompositeComponent):
    """
    A composite component that groups and manages multiple form fields.
    
    This class handles validation across multiple fields and provides methods
    for managing form submission and resetting.
    """

    def __init__(
        self,
        fields: Optional[Dict[str, FormComponent]] = None,
        on_submit: Optional[Callable[[Dict[str, Any]], None]] = None,
        component_id: Optional[ComponentId] = None
    ):
        """
        Initialize a form group.
        
        Args:
            fields: Dictionary mapping field names to FormComponent instances
            on_submit: Callback function called when the form is submitted with valid data
            component_id: Optional unique ID for the component
        """
        props = {
            'on_submit': on_submit
        }
        super().__init__(props, component_id)
        
        # Initialize state
        self.set_state({
            'is_submitting': False,
            'was_submitted': False,
            'is_valid': True,
            'submit_error': None
        })
        
        # Add fields as children
        if fields:
            for field_name, field_component in fields.items():
                self.add_child(field_component, f"{self.component_id}_{field_name}")
    
    def add_field(self, field_name: str, field_component: FormComponent) -> None:
        """
        Add a form field to the group.
        
        Args:
            field_name: Name of the field
            field_component: The FormComponent instance
        """
        self.add_child(field_component, f"{self.component_id}_{field_name}")
    
    def validate_all(self) -> bool:
        """
        Validate all form fields.
        
        Returns:
            True if all fields are valid, False otherwise
        """
        all_valid = True
        
        for child in self._children.values():
            if isinstance(child, FormComponent):
                field_valid = child.validate()
                all_valid = all_valid and field_valid
        
        self.set_state({'is_valid': all_valid})
        return all_valid
    
    def get_values(self) -> Dict[str, Any]:
        """
        Get the values of all form fields.
        
        Returns:
            Dictionary mapping field names to their current values
        """
        values = {}
        
        for child_id, child in self._children.items():
            if isinstance(child, FormComponent):
                # Extract field name from child ID (removing the form group prefix)
                field_name = child_id.replace(f"{self.component_id}_", "")
                values[field_name] = child.get_value()
        
        return values
    
    def reset_all(self) -> None:
        """Reset all form fields to their initial values."""
        for child in self._children.values():
            if isinstance(child, FormComponent):
                child.reset()
        
        self.set_state({
            'is_submitting': False,
            'was_submitted': False,
            'is_valid': True,
            'submit_error': None
        })
    
    def submit(self) -> bool:
        """
        Submit the form if all fields are valid.
        
        Returns:
            True if submission was successful, False otherwise
        """
        self.set_state({'is_submitting': True})
        
        if self.validate_all():
            values = self.get_values()
            on_submit = self.props.get('on_submit')
            
            if on_submit:
                try:
                    on_submit(values)
                    self.set_state({
                        'is_submitting': False,
                        'was_submitted': True,
                        'submit_error': None
                    })
                    return True
                except Exception as e:
                    logger.error(f"Error in form submission: {e}")
                    self.set_state({
                        'is_submitting': False,
                        'submit_error': str(e)
                    })
                    return False
            else:
                self.set_state({
                    'is_submitting': False,
                    'was_submitted': True
                })
                return True
        else:
            self.set_state({'is_submitting': False})
            return False


# Helper functions for working with components

def create_component(component_class: type, **kwargs) -> ComponentBase:
    """
    Factory function to create and initialize a component instance.
    
    Args:
        component_class: The component class to instantiate
        **kwargs: Arguments to pass to the component constructor
        
    Returns:
        An initialized component instance
    """
    return component_class(**kwargs)


def render_component(component: Union[ComponentBase, Callable]) -> Any:
    """
    Render a component or component function.
    
    Args:
        component: A component instance or a function that returns a component
        
    Returns:
        The result of rendering the component
    """
    if callable(component) and not isinstance(component, ComponentBase):
        component_instance = component()
        if isinstance(component_instance, ComponentBase):
            return component_instance()
        return component_instance
    elif isinstance(component, ComponentBase):
        return component()
    else:
        raise TypeError("Expected a ComponentBase instance or a function that returns a component")


def with_props(component_class: type, **fixed_props):
    """
    Higher-order function that returns a factory function for creating components with fixed props.
    
    Args:
        component_class: The component class to create
        **fixed_props: Fixed props to apply to all created components
        
    Returns:
        A factory function that creates components with the fixed props
    """
    def create_with_props(**variable_props):
        # Merge fixed and variable props
        props = {**fixed_props, **variable_props}
        return component_class(**props)
    
    return create_with_props