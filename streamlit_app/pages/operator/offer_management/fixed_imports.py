"""
Authentication function imports for the offer management module.
Provides centralized authentication logic to avoid circular imports.
"""
import streamlit as st
import logging
from typing import Dict, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Import config for session variables
try:
    import app_config as config
    logger.info("Successfully imported app_config in fixed_imports")
except ImportError:
    logger.warning("Could not import app_config in fixed_imports")
    config = None

# Default session variables if config is not available
DEFAULT_SESSION_VARS = {
    "user": "user",
    "token": "auth_token",
    "authenticated": "authenticated",
}

def is_authenticated() -> bool:
    """
    Check if a user is authenticated by looking at session state.
    
    Returns:
        bool: True if user is authenticated, False otherwise
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS) if config else DEFAULT_SESSION_VARS
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if we have authentication data in session state
    auth_token = st.session_state.get(token_key)
    authenticated = st.session_state.get(auth_key, False)
    
# Debug output removed for production
    
    # If both token and authenticated flag are present, user is authenticated
    if auth_token and authenticated:
        return True
    
    # For development, fallback to returning authenticated status
    return authenticated

def get_current_user() -> Dict[str, Any]:
    """
    Get the current authenticated user data.
    
    Returns:
        Dict[str, Any]: User data dictionary or None if not authenticated
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS) if config else DEFAULT_SESSION_VARS
    user_key = session_vars.get("user", "user")
    
    # Get user data from session state
    user_data = st.session_state.get(user_key)
    
# Debug output removed for production
    
    # Return user data if it exists
    if user_data:
        return user_data
        
    # For development, return a default user
    logger.info("No user data in session state, returning default operator user")
    return {
        "id": 1,
        "username": "operator",
        "role": "ügyintéző",
        "name": "Test Operator",
        "permissions": ["view_offers", "edit_offers", "change_status"]
    }