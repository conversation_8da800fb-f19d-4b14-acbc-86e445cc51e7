#!/usr/bin/env python3
"""
Enhanced Offer Management UI Example
Based on UI_IMPLEMENTATION_PLAN.md specifications

This example demonstrates the modern, responsive UI components with:
- Filter presets functionality
- Keyboard navigation support
- Responsive design for all screen sizes
- Smooth animations and transitions
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date, timedelta
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the enhanced components
try:
    from offer_management.modern_components import (
        ModernFilterPanel,
        EnhancedOfferStatsCard,
        EnhancedOfferCard
    )
    from offer_management.react_like_components import create_component
    components_available = True
except ImportError:
    logger.warning("Enhanced components not available, using fallback UI")
    components_available = False

def main():
    """Main application entry point."""
    st.set_page_config(
        page_title="Ajánlatok Kezelése - Enhanced UI",
        page_icon="🌾",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # Inject screen detection for responsive design
    inject_responsive_detection()
    
    # Initialize session state
    init_session_state()
    
    # Render the enhanced UI
    render_enhanced_offer_management()

def inject_responsive_detection():
    """Inject JavaScript for responsive screen detection."""
    st.markdown("""
    <script>
    function detectScreenSize() {
        const width = window.innerWidth;
        const isMobile = width < 768;
        const isTablet = width >= 768 && width < 992;
        
        // Store in session storage for Streamlit access
        sessionStorage.setItem('screen_width', width);
        sessionStorage.setItem('is_mobile', isMobile);
        sessionStorage.setItem('is_tablet', isTablet);
    }
    
    // Detect screen size on load and resize
    detectScreenSize();
    window.addEventListener('resize', detectScreenSize);
    </script>
    """, unsafe_allow_html=True)

def init_session_state():
    """Initialize session state with default values."""
    defaults = {
        'filters': {
            'producer': None,
            'statuses': ['CREATED', 'CONFIRMED_BY_COMPANY'],
            'from_date': date.today() - timedelta(days=30),
            'to_date': date.today(),
            'product_type': None,
            'search': ''
        },
        'view_mode': 'list',
        'offers_data': generate_sample_offers(),
        'stats_data': None,
        'is_mobile': False,
        'is_tablet': False,
        'screen_width': 1200
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

def generate_sample_offers():
    """Generate sample offer data for demonstration."""
    sample_data = [
        {
            'id': 1001,
            'date': '2023-05-12',
            'producer': 'Kovács János',
            'product': 'Alma',
            'quantity': 500,
            'price': 220,
            'status': 'CREATED',
            'status_display': 'Létrehozva',
            'status_color': '🟢'
        },
        {
            'id': 1002,
            'date': '2023-05-13',
            'producer': 'Szabó Béla',
            'product': 'Szőlő',
            'quantity': 200,
            'price': 350,
            'status': 'CONFIRMED_BY_COMPANY',
            'status_display': 'Megerősítve',
            'status_color': '🟡'
        },
        {
            'id': 1003,
            'date': '2023-05-14',
            'producer': 'Nagy István',
            'product': 'Burgonya',
            'quantity': 550,
            'price': 180,
            'status': 'REJECTED_BY_USER',
            'status_display': 'Elutasítva',
            'status_color': '🔴'
        },
        {
            'id': 1004,
            'date': '2023-05-15',
            'producer': 'Tóth Mária',
            'product': 'Paradicsom',
            'quantity': 300,
            'price': 450,
            'status': 'ACCEPTED_BY_USER',
            'status_display': 'Elfogadva',
            'status_color': '🟢'
        },
        {
            'id': 1005,
            'date': '2023-05-16',
            'producer': 'Horváth Anna',
            'product': 'Paprika',
            'quantity': 150,
            'price': 600,
            'status': 'FINALIZED',
            'status_display': 'Véglegesítve',
            'status_color': '✅'
        }
    ]
    return sample_data

def calculate_stats(offers_data: List[Dict], filters: Dict) -> Dict:
    """Calculate statistics based on filtered offers."""
    # Filter offers based on current filters
    filtered_offers = filter_offers(offers_data, filters)
    
    if not filtered_offers:
        return {
            'total_quantity': 0,
            'average_price': 0,
            'total_value': 0,
            'status_distribution': {},
            'product_distribution': {}
        }
    
    # Calculate metrics
    total_quantity = sum(offer.get('quantity', 0) for offer in filtered_offers)
    total_value = sum(offer.get('quantity', 0) * offer.get('price', 0) for offer in filtered_offers)
    average_price = total_value / total_quantity if total_quantity > 0 else 0
    
    # Status distribution
    status_counts = {}
    for offer in filtered_offers:
        status = offer.get('status_display', 'Unknown')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Product distribution
    product_counts = {}
    for offer in filtered_offers:
        product = offer.get('product', 'Unknown')
        product_counts[product] = product_counts.get(product, 0) + 1
    
    return {
        'total_quantity': total_quantity,
        'average_price': average_price,
        'total_value': total_value,
        'status_distribution': status_counts,
        'product_distribution': product_counts
    }

def filter_offers(offers_data: List[Dict], filters: Dict) -> List[Dict]:
    """Filter offers based on current filter criteria."""
    filtered = offers_data.copy()
    
    # Producer filter
    if filters.get('producer'):
        filtered = [o for o in filtered if o.get('producer') == filters['producer']]
    
    # Status filter
    if filters.get('statuses'):
        filtered = [o for o in filtered if o.get('status') in filters['statuses']]
    
    # Search filter
    if filters.get('search'):
        search_term = filters['search'].lower()
        filtered = [o for o in filtered if 
                   search_term in o.get('producer', '').lower() or
                   search_term in o.get('product', '').lower()]
    
    return filtered

def render_enhanced_offer_management():
    """Render the enhanced offer management interface."""
    
    # Page header
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
    ">
        <h1 style="margin: 0; font-size: 2.5rem;">🌾 Ajánlatok Kezelése</h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">Enhanced UI with Modern Components</p>
    </div>
    """, unsafe_allow_html=True)
    
    if components_available:
        render_with_enhanced_components()
    else:
        render_fallback_ui()

def render_with_enhanced_components():
    """Render using the enhanced modern components."""
    
    # Get available producers and products for filter options
    available_producers = list(set(offer.get('producer', '') for offer in st.session_state.offers_data))
    available_products = list(set(offer.get('product', '') for offer in st.session_state.offers_data))
    
    # Filter change handler
    def handle_filter_change(new_filters):
        st.session_state.filters.update(new_filters)
        # Recalculate stats when filters change
        st.session_state.stats_data = calculate_stats(st.session_state.offers_data, st.session_state.filters)
        st.rerun()
    
    # Create and render the modern filter panel
    filter_panel = create_component(ModernFilterPanel, {
        'title': '🔍 Ajánlatok szűrése',
        'filters': st.session_state.filters,
        'on_filter_change': handle_filter_change,
        'available_producers': available_producers,
        'available_products': available_products,
        'saved_presets': []  # This would come from the API in real implementation
    })
    filter_panel.render()
    
    # Calculate current stats
    current_stats = calculate_stats(st.session_state.offers_data, st.session_state.filters)
    
    # Create and render the statistics card
    stats_card = create_component(EnhancedOfferStatsCard, {
        'stats_data': current_stats,
        'title': '📊 Statisztika',
        'loading': False
    })
    stats_card.render()
    
    # Offer list section
    st.markdown("""
    <div style="
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
        margin: 1rem 0;
        overflow: hidden;
    ">
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        ">
            <span>📋 Ajánlatok listája</span>
            <span>Találatok: {} ajánlat</span>
        </div>
    </div>
    """.format(len(filter_offers(st.session_state.offers_data, st.session_state.filters))), unsafe_allow_html=True)
    
    # Render filtered offers
    filtered_offers = filter_offers(st.session_state.offers_data, st.session_state.filters)
    render_offer_list(filtered_offers)

def render_fallback_ui():
    """Render fallback UI when enhanced components are not available."""
    
    st.markdown("### 🔍 Szűrők")
    
    # Basic filter controls
    col1, col2 = st.columns(2)
    
    with col1:
        producers = list(set(offer.get('producer', '') for offer in st.session_state.offers_data))
        selected_producer = st.selectbox(
            "Termelő:",
            [""] + producers,
            key="producer_select"
        )
        
        if selected_producer != st.session_state.filters.get('producer', ''):
            st.session_state.filters['producer'] = selected_producer if selected_producer else None
            st.rerun()
    
    with col2:
        search_term = st.text_input(
            "Keresés:",
            value=st.session_state.filters.get('search', ''),
            key="search_input"
        )
        
        if search_term != st.session_state.filters.get('search', ''):
            st.session_state.filters['search'] = search_term
            st.rerun()
    
    # Status checkboxes
    st.markdown("**Státusz:**")
    status_options = {
        'CREATED': 'Létrehozva',
        'CONFIRMED_BY_COMPANY': 'Megerősítve',
        'ACCEPTED_BY_USER': 'Elfogadva',
        'REJECTED_BY_USER': 'Elutasítva',
        'FINALIZED': 'Véglegesítve'
    }
    
    selected_statuses = []
    cols = st.columns(len(status_options))
    
    for i, (status_key, status_label) in enumerate(status_options.items()):
        with cols[i]:
            if st.checkbox(
                status_label,
                value=status_key in st.session_state.filters.get('statuses', []),
                key=f"status_{status_key}"
            ):
                selected_statuses.append(status_key)
    
    if selected_statuses != st.session_state.filters.get('statuses', []):
        st.session_state.filters['statuses'] = selected_statuses
        st.rerun()
    
    # Statistics
    current_stats = calculate_stats(st.session_state.offers_data, st.session_state.filters)
    render_statistics_fallback(current_stats)
    
    # Offer list
    filtered_offers = filter_offers(st.session_state.offers_data, st.session_state.filters)
    render_offer_list(filtered_offers)

def render_statistics_fallback(stats_data: Dict):
    """Render statistics section with fallback design."""
    
    st.markdown("### 📊 Statisztika")
    
    # Metrics row
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Összes mennyiség",
            f"{stats_data['total_quantity']:,.0f} kg"
        )
    
    with col2:
        st.metric(
            "Átlagár",
            f"{stats_data['average_price']:,.0f} Ft/kg"
        )
    
    with col3:
        st.metric(
            "Összérték",
            f"{stats_data['total_value']:,.0f} Ft"
        )
    
    # Charts
    if stats_data['status_distribution']:
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Státuszok megoszlása**")
            fig_status = px.pie(
                values=list(stats_data['status_distribution'].values()),
                names=list(stats_data['status_distribution'].keys()),
                title="Státuszok"
            )
            st.plotly_chart(fig_status, use_container_width=True)
        
        with col2:
            st.markdown("**Terméktípusok**")
            fig_products = px.bar(
                x=list(stats_data['product_distribution'].values()),
                y=list(stats_data['product_distribution'].keys()),
                orientation='h',
                title="Termékek"
            )
            st.plotly_chart(fig_products, use_container_width=True)

def render_offer_list(offers: List[Dict]):
    """Render the list of offers with responsive design."""
    
    if not offers:
        st.info("Nincsenek ajánlatok a megadott szűrési feltételekkel.")
        return
    
    # Check if mobile view
    is_mobile = st.session_state.get('is_mobile', False)
    
    if is_mobile or st.session_state.get('view_mode') == 'card':
        render_card_view(offers)
    else:
        render_table_view(offers)

def render_card_view(offers: List[Dict]):
    """Render offers in card view (mobile-friendly)."""
    
    st.markdown("### 📱 Kártya nézet")
    
    for offer in offers:
        with st.container():
            st.markdown(f"""
            <div style="
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border: 1px solid #e1e5e9;
                padding: 1rem;
                margin: 0.5rem 0;
                transition: transform 0.2s ease;
            ">
                <div style="font-weight: 600; margin-bottom: 0.5rem;">
                    #{offer['id']} - {offer['producer']}
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 0.25rem;">
                    <span style="margin-right: 0.5rem;">🍎</span>
                    <span>{offer['product']}</span>
                </div>
                <div style="margin-bottom: 0.25rem;">
                    📦 {offer['quantity']} kg @ {offer['price']} Ft/kg
                </div>
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 0.5rem;">{offer['status_color']}</span>
                    <span>{offer['status_display']}</span>
                </div>
            </div>
            """, unsafe_allow_html=True)

def render_table_view(offers: List[Dict]):
    """Render offers in table view (desktop)."""
    
    # Convert to DataFrame for better display
    df = pd.DataFrame(offers)
    
    # Format the DataFrame for display
    df_display = df.copy()
    df_display['Státusz'] = df['status_color'] + ' ' + df['status_display']
    df_display = df_display[['id', 'date', 'producer', 'product', 'quantity', 'price', 'Státusz']]
    df_display.columns = ['ID', 'Dátum', 'Termelő', 'Termék', 'Mennyiség (kg)', 'Ár (Ft/kg)', 'Státusz']
    
    # Display with custom styling
    st.markdown("### 📊 Táblázat nézet")
    
    # Add custom CSS for the table
    st.markdown("""
    <style>
    .dataframe {
        border-radius: 8px;
        overflow: hidden;
    }
    .dataframe th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
    }
    .dataframe td {
        border-bottom: 1px solid #e1e5e9;
    }
    .dataframe tr:hover {
        background-color: #f8f9fa;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.dataframe(
        df_display,
        use_container_width=True,
        hide_index=True
    )

if __name__ == "__main__":
    main()