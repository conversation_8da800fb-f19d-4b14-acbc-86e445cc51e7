"""
Kompatibilitási modul az Offer Management funkcionalitás eléréséhez.
Ez a modul segít meg<PERSON>ülni a relatív import problémákat.
"""
import sys
import os
import importlib
import logging

logger = logging.getLogger(__name__)

# Adjuk hozzá az offer_management könyvtárat a sys.path-hoz
current_dir = os.path.dirname(os.path.abspath(__file__))
offer_management_dir = os.path.join(current_dir, "offer_management")
if offer_management_dir not in sys.path:
    sys.path.append(offer_management_dir)
    logger.info(f"Added {offer_management_dir} to sys.path")

# Relatív import hiba üzenet
RELATIVE_IMPORT_ERROR_MESSAGE = """
FIGYELEM: Relatív import hiba történt az offer_management modulban.
Az offer_detail.py fájl a `.api_client` importot prób<PERSON><PERSON> has<PERSON>nálni, ami relatív import,
de a Python nem tudja meghatározni a szülő csomagot.

A probléma megoldási lehetőségei:
1. Használj abszolút importot a relatív helyett: 
   Módosítsd az `from .api_client import ...` sort erre:
   `from streamlit_app.pages.operator.offer_management.api_client import ...`

2. Használd ezt a kompatibilitási modult
   A fő offer_management.py fájlban importáld így:
   `from offer_management_compatibility import show_offer_detail`
"""

# Importáljuk a szükséges modulokat
try:
    # Most már direkt importálhatjuk az offer_management almodulokat
    from offer_management.offer_detail import show_offer_detail
    from offer_management.api_client import (
        get_offer_details,
        get_offer_logs,
        update_offer_status,
        get_offer_attachments,
        get_related_offers
    )
    
    logger.info("Successfully imported offer_management modules")
    
    # Tegyük elérhetővé a külvilág számára ezeket a függvényeket
    __all__ = [
        'show_offer_detail',
        'get_offer_details',
        'get_offer_logs',
        'update_offer_status',
        'get_offer_attachments',
        'get_related_offers'
    ]
    
except ImportError as e:
    logger.error(f"Failed to import offer_management modules: {str(e)}")
    
    # Ellenőrizzük, hogy a relatív import hiba történt-e
    if "attempted relative import with no known parent package" in str(e):
        logger.error(RELATIVE_IMPORT_ERROR_MESSAGE)
        print(RELATIVE_IMPORT_ERROR_MESSAGE)
    
    # Fallback: dinamikus importálás
    try:
        # Próbáljuk meg dynamically importálni
        offer_detail = importlib.import_module("offer_management.offer_detail")
        api_client = importlib.import_module("offer_management.api_client")
        
        # Hozzáférhetővé tesszük a függvényeket
        show_offer_detail = offer_detail.show_offer_detail
        get_offer_details = api_client.get_offer_details
        get_offer_logs = api_client.get_offer_logs
        update_offer_status = api_client.update_offer_status
        get_offer_attachments = api_client.get_offer_attachments
        get_related_offers = api_client.get_related_offers
        
        logger.info("Successfully imported offer_management modules using dynamic import")
    except Exception as e2:
        logger.error(f"All import methods failed: {str(e2)}")
        raise ImportError(f"Cannot import offer_management modules: {str(e)}, followed by: {str(e2)}") 