# Riportok és statisztikák
"""
Ügyintézői jelentések és riportok oldal.
"""
import streamlit as st
import datetime
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import io
from api import offers as offers_api
from api import users as users_api
from api import products as products_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.formatting import format_quantity, format_price, format_status
import app_config as config

def show_operator_reports():
    """
    Ügyintézői jelentések és riportok oldal megjelenítése.
    """
    st.title("Összegzés és riportok")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # <PERSON>lhaszná<PERSON>ó adatainak lekérése
    user = get_current_user()
    
    # <PERSON>őrizzük, hogy ügyintéző vagy admin
    if user.get("role") not in ["ügyintéző", "admin"]:
        show_error("Az oldal megtekintéséhez ügyintézői jogosultság szükséges.")
        return
    
    # Jelentéstípus kiválasztása
    report_types = [
        "Áttekintő riport",
        "Pénzügyi riport",
        "Termelői riport", 
        "Termék riport", 
        "Időszaki összehasonlítás"
    ]
    
    selected_report = st.selectbox("Válasszon jelentéstípust", options=report_types)
    
    # Időszak választása
    st.subheader("Időszak kiválasztása")
    
    col1, col2 = st.columns(2)
    
    with col1:
        from_date = st.date_input("Kezdő dátum", value=datetime.date.today() - datetime.timedelta(days=30))
    
    with col2:
        to_date = st.date_input("Záró dátum", value=datetime.date.today())
    
    # További szűrők kollapsz szekcióban
    with st.expander("További szűrési feltételek"):
        # Termelő szűrő
        success_users, users = users_api.get_all_farmers()
        if success_users and users:
            user_options = [(0, "Összes termelő")] + [(u["id"], u.get("contact_name", "Névtelen")) for u in users]
            selected_user_id = st.selectbox(
                "Termelő",
                options=[id for id, _ in user_options],
                format_func=lambda x: next((name for id, name in user_options if id == x), "Ismeretlen")
            )
        else:
            st.warning("Nem sikerült lekérni a termelők listáját")
            selected_user_id = 0
        
        # Termék típus szűrő
        success_products, products = products_api.get_product_types()
        if success_products and products:
            product_options = [(0, "Összes termék")] + [(p["id"], p["name"]) for p in products]
            selected_product_id = st.selectbox(
                "Termék típus",
                options=[id for id, _ in product_options],
                format_func=lambda x: next((name for id, name in product_options if id == x), "Ismeretlen")
            )
        else:
            st.warning("Nem sikerült lekérni a termékek listáját")
            selected_product_id = 0
    
    # Statisztikák lekérése
    params = {
        "date_from": from_date.strftime("%Y-%m-%d"),
        "date_to": to_date.strftime("%Y-%m-%d")
    }
    
    # Hozzáadjuk az opcionális szűrőket
    if selected_user_id > 0:
        params["user_id"] = selected_user_id
    
    if selected_product_id > 0:
        params["product_type_id"] = selected_product_id
    
    # Debug: print request parameters
    print("==== REPORT API REQUEST ====")
    print(f"Report type: {selected_report}")
    print(f"Request params: {params}")
    print("===========================")
    
    success, stats = offers_api.get_statistics(params=params)
    
    # Ha van termelő vagy termék szűrés, adjuk hozzá a statisztikákhoz az azonosítókat
    if selected_user_id > 0:
        stats["user_id"] = selected_user_id
    
    if selected_product_id > 0:
        stats["product_type_id"] = selected_product_id
    
    # Debug: print API response details
    print("==== REPORT API RESPONSE ====")
    print(f"Success: {success}")
    print(f"Stats type: {type(stats)}")
    if isinstance(stats, dict):
        print(f"Stats keys: {stats.keys()}")
        
        # Debug key sections
        print("--- Top-level data ---")
        print(f"total_offers: {stats.get('total_offers')}")
        print(f"accepted_offers: {stats.get('accepted_offers')}")
        print(f"total_quantity: {stats.get('total_quantity')}")
        print(f"total_value: {stats.get('total_value')}")
        
        # Debug specific sections based on report type
        if selected_report == "Termék riport":
            print("--- Product data ---")
            product_data = stats.get("product_summary", [])
            print(f"product_summary count: {len(product_data)}")
            if product_data:
                print(f"First product: {product_data[0]}")
        
        if selected_report == "Termelői riport":
            print("--- Producer data ---")
            producer_data = stats.get("producer_summary", [])
            print(f"producer_summary count: {len(producer_data)}")
            if producer_data:
                print(f"First producer: {producer_data[0]}")
    else:
        print(f"Stats: {stats}")
    print("============================")
    
    if not success:
        show_error(f"Hiba a statisztikák lekérésekor: {stats}")
        return
    
    # A kiválasztott jelentés megjelenítése
    if selected_report == "Áttekintő riport":
        show_overview_report(stats, from_date, to_date)
    elif selected_report == "Pénzügyi riport":
        show_financial_report(stats, from_date, to_date)
    elif selected_report == "Termelői riport":
        show_producer_report(stats, from_date, to_date)
    elif selected_report == "Termék riport":
        show_product_report(stats, from_date, to_date)
    elif selected_report == "Időszaki összehasonlítás":
        show_time_comparison_report(from_date, to_date)

def show_overview_report(stats, from_date, to_date):
    """
    Áttekintő riport megjelenítése.
    
    Args:
        stats (dict): Statisztikai adatok
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Záró dátum
    """
    st.header(f"Áttekintő riport ({from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')})")
    
    # Alapvető statisztikák
    total_offers = stats.get("total_offers", 0)
    status_counts = stats.get("status_counts", {})
    accepted_offers = status_counts.get("ACCEPTED_BY_USER", 0) + status_counts.get("FINALIZED", 0)
    total_quantity = float(stats.get("total_quantity", 0))
    total_value = float(stats.get("total_value", 0))
    
    # Metrikai kártyák
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(label="Összes ajánlat", value=total_offers)
    
    with col2:
        if total_offers > 0:
            st.metric(label="Elfogadási arány", value=f"{(accepted_offers / total_offers * 100):.1f}%")
        else:
            st.metric(label="Elfogadási arány", value="0%")
    
    with col3:
        st.metric(label="Összes mennyiség", value=format_quantity(total_quantity))
    
    with col4:
        st.metric(label="Összes érték", value=format_price(total_value))
    
    # Státusz szerinti megoszlás
    st.subheader("Ajánlatok státusz szerinti megoszlása")
    
    # Státusz adatok
    status_data = stats.get("status_summary", [])
    
    if status_data:
        # DataFrame létrehozása
        status_df = pd.DataFrame(status_data)
        
        # Státuszok formázása
        status_df["status_name"] = status_df["status"].apply(lambda s: format_status(s))
        
        # Kördiagram
        fig = px.pie(
            status_df, 
            values="count", 
            names="status_name", 
            title="Ajánlatok státusz szerinti megoszlása",
            hole=0.3
        )
        
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("Nincs elérhető adat a státusz szerinti megoszláshoz.")
    
    # Időbeli eloszlás
    st.subheader("Ajánlatok időbeli eloszlása")
    
    # Napi bontás
    daily_data = stats.get("daily_summary", [])
    
    if daily_data:
        # DataFrame létrehozása
        daily_df = pd.DataFrame(daily_data)
        
        # Dátum rendezése
        daily_df = daily_df.sort_values("date")
        
        # Vonaldiagram - mennyiség
        fig = px.line(
            daily_df, 
            x="date", 
            y="quantity", 
            title="Napi beszállítások mennyisége",
            labels={"date": "Dátum", "quantity": "Mennyiség (kg)"}
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Vonaldiagram - érték
        fig2 = px.line(
            daily_df, 
            x="date", 
            y="value", 
            title="Napi beszállítások értéke",
            labels={"date": "Dátum", "value": "Érték (Ft)"}
        )
        
        fig2.update_traces(line_color='green')
        st.plotly_chart(fig2, use_container_width=True)
        
        # Ajánlatok számának és mennyiségének együttes megjelenítése
        fig3 = go.Figure()
        
        # Ajánlatok száma (bal tengely)
        fig3.add_trace(go.Bar(
            x=daily_df["date"],
            y=daily_df["count"],
            name="Ajánlatok száma",
            marker_color='rgb(55, 83, 109)'
        ))
        
        # Mennyiség (jobb tengely)
        fig3.add_trace(go.Scatter(
            x=daily_df["date"],
            y=daily_df["quantity"],
            name="Mennyiség (kg)",
            marker_color='rgb(26, 118, 255)',
            mode='lines+markers',
            yaxis='y2'
        ))
        
        # Két tengely beállítása
        fig3.update_layout(
            title="Ajánlatok száma és mennyisége naponta",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Ajánlatok száma"),
            yaxis2=dict(title="Mennyiség (kg)", overlaying="y", side="right"),
            legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)")
        )
        
        st.plotly_chart(fig3, use_container_width=True)
    else:
        st.info("Nincs elérhető adat az időbeli eloszláshoz.")
    
    # Exportálási lehetőség
    st.subheader("Riport exportálása")
    
    if st.button("Exportálás CSV formátumban"):
        # CSV adatok előkészítése
        export_data = {
            "Időszak": f"{from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')}",
            "Összes ajánlat": total_offers,
            "Elfogadott ajánlatok": accepted_offers,
            "Elfogadási arány": f"{(accepted_offers / total_offers * 100) if total_offers > 0 else 0:.1f}%",
            "Összes mennyiség (kg)": float(total_quantity),
            "Összes érték (Ft)": float(total_value)
        }
        
        # DataFrame létrehozása
        export_df = pd.DataFrame([export_data])
        
        # Napi adatok exportálása
        if daily_data:
            daily_export = pd.DataFrame(daily_data)
            
            # CSV letöltés - összesített adatok és napi bontás
            st.download_button(
                label="Összesített adatok CSV letöltése",
                data=export_df.to_csv(index=False).encode('utf-8'),
                file_name=f"attekinto_osszegzett_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv",
            )
            
            st.download_button(
                label="Napi bontás CSV letöltése",
                data=daily_export.to_csv(index=False).encode('utf-8'),
                file_name=f"attekinto_napi_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv",
            )
        else:
            # Csak összesített adatok
            st.download_button(
                label="CSV letöltése",
                data=export_df.to_csv(index=False).encode('utf-8'),
                file_name=f"attekinto_riport_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv",
            )

def show_financial_report(stats, from_date, to_date):
    """
    Pénzügyi riport megjelenítése.
    
    Args:
        stats (dict): Statisztikai adatok
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Záró dátum
    """
    st.header(f"Pénzügyi riport ({from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')})")
    
    # Pénzügyi statisztikák
    total_value = float(stats.get("total_value", 0))
    avg_price = float(stats.get("average_price", 0))
    total_offers = stats.get("total_offers", 0)
    total_quantity = float(stats.get("total_quantity", 0))
    
    # Státusz számok
    status_counts = stats.get("status_counts", {})
    accepted_offers = status_counts.get("ACCEPTED_BY_USER", 0) + status_counts.get("FINALIZED", 0)
    
    # Alap metrikai kártyák
    st.subheader("Pénzügyi mutatók")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="Összes érték", 
            value=format_price(total_value),
            help="A véglegesített és elfogadott ajánlatok teljes értéke"
        )
    
    with col2:
        st.metric(
            label="Átlagos egységár", 
            value=format_price(avg_price),
            help="A véglegesített és elfogadott ajánlatok átlagos egységára"
        )
        
    with col3:
        st.metric(
            label="Összes mennyiség", 
            value=format_quantity(total_quantity),
            help="A beszállítások teljes mennyisége"
        )
        
    with col4:
        active_rate = (accepted_offers / total_offers * 100) if total_offers > 0 else 0
        st.metric(
            label="Aktiválási arány", 
            value=f"{active_rate:.1f}%",
            help="Az elfogadott és véglegesített ajánlatok aránya az összes ajánlathoz képest"
        )
    
    # Státusz összefoglalás
    status_data = stats.get("status_summary", [])
    if status_data:
        # DataFrame létrehozása
        status_df = pd.DataFrame(status_data)
        
        # Státuszok formázása
        status_df["status_name"] = status_df["status"].apply(lambda s: format_status(s))
        
        # Fontos pénzügyi státuszok kiemelése
        st.subheader("Üzleti státusz összefoglaló")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Kördiagram a státusz megoszlásról
            fig_status_pie = px.pie(
                status_df,
                values="count",
                names="status_name",
                title="Ajánlatok státusz szerinti megoszlása",
                hole=0.4,
                color_discrete_sequence=px.colors.qualitative.Bold
            )
            st.plotly_chart(fig_status_pie, use_container_width=True)
        
        with col2:
            # Státusz táblázat pénzügyi adatokkal
            if "value" in status_df.columns and "quantity" in status_df.columns:
                st.caption("Státuszok pénzügyi hatása")
                st.dataframe(
                    status_df,
                    column_config={
                        "status_name": "Státusz",
                        "count": "Ajánlatok száma",
                        "quantity": st.column_config.NumberColumn("Mennyiség (kg)", format="%.2f kg"),
                        "value": st.column_config.NumberColumn("Érték (Ft)", format="%.2f Ft")
                    },
                    hide_index=True,
                    use_container_width=True
                )
            else:
                # Alap státusz összesítés, ha nincs érték és mennyiség
                st.caption("Státusz szerinti megoszlás")
                st.dataframe(
                    status_df,
                    column_config={
                        "status_name": "Státusz",
                        "count": "Ajánlatok száma"
                    },
                    hide_index=True,
                    use_container_width=True
                )
    
    # Időbeli trendek szekció
    st.subheader("Pénzügyi trendek időbeli bontásban")
    daily_data = stats.get("daily_summary", [])
    
    if not daily_data:
        st.info("Nincs elérhető adat az időbeli trendek megjelenítéséhez. Próbáljon meg nagyobb időszakot választani vagy másik szűrést alkalmazni.")
        return
    
    # DataFrame létrehozása
    daily_df = pd.DataFrame(daily_data)
    
    # Dátum rendezése és konverzió
    daily_df = daily_df.sort_values("date")
    daily_df["date"] = pd.to_datetime(daily_df["date"])
    
    # Heti és havi összegzések létrehozása
    daily_df["week"] = daily_df["date"].dt.isocalendar().week
    daily_df["month"] = daily_df["date"].dt.month
    daily_df["month_name"] = daily_df["date"].dt.strftime("%Y-%m")
    daily_df["year"] = daily_df["date"].dt.year
    
    # Heti átlag egységár számítása
    daily_df["avg_price"] = daily_df["value"] / daily_df["quantity"].where(daily_df["quantity"] > 0, 1)
    
    # Kumulatív értékek számítása
    daily_df["cumulative_value"] = daily_df["value"].cumsum()
    daily_df["cumulative_quantity"] = daily_df["quantity"].cumsum()
    
    # Trend mutatása
    tabs = st.tabs(["Napi bontás", "Heti összesítés", "Havi összesítés", "Kumulatív trendek", "Forgalom előrejelzés"])
    
    with tabs[0]:  # Napi bontás
        st.caption("Napi pénzügyi adatok alakulása")
        
        # Napi bevétel és mennyiség összehasonlító diagramja
        fig = go.Figure()
        
        # Bevétel (bal tengely)
        fig.add_trace(go.Bar(
            x=daily_df["date"],
            y=daily_df["value"],
            name="Bevétel (Ft)",
            marker_color='rgba(58, 71, 80, 0.7)'
        ))
        
        # Mennyiség (jobb tengely)
        fig.add_trace(go.Scatter(
            x=daily_df["date"],
            y=daily_df["quantity"],
            name="Mennyiség (kg)",
            marker_color='rgba(246, 78, 139, 0.7)',
            mode='lines+markers',
            yaxis='y2'
        ))
        
        # Két tengely beállítása
        fig.update_layout(
            title="Napi bevétel és beszállított mennyiség",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Bevétel (Ft)"),
            yaxis2=dict(title="Mennyiség (kg)", overlaying="y", side="right"),
            legend=dict(x=0.01, y=0.99)
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Átlagár változás
        fig2 = px.line(
            daily_df,
            x="date",
            y="avg_price",
            title="Napi átlagárak alakulása",
            labels={"date": "Dátum", "avg_price": "Átlagár (Ft/kg)"}
        )
        fig2.update_traces(line=dict(color='green'))
        
        st.plotly_chart(fig2, use_container_width=True)

        # Napi adatok táblázatos formában exportálási lehetőséggel
        st.caption("Napi adatok részletezése")
        
        # Táblázat megjelenítése
        st.dataframe(
            daily_df[["date", "count", "quantity", "value", "avg_price"]],
            column_config={
                "date": "Dátum",
                "count": "Ajánlatok száma",
                "quantity": st.column_config.NumberColumn("Mennyiség (kg)", format="%.2f kg"),
                "value": st.column_config.NumberColumn("Érték (Ft)", format="%.2f Ft"),
                "avg_price": st.column_config.NumberColumn("Átlagár (Ft/kg)", format="%.2f Ft/kg")
            },
            hide_index=True,
            use_container_width=True
        )
        
        # Napi adatok exportálása
        st.download_button(
            label="Napi adatok exportálása CSV-be",
            data=daily_df[["date", "count", "quantity", "value", "avg_price"]].to_csv(index=False).encode('utf-8'),
            file_name=f"penzugy_napi_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
    
    with tabs[1]:  # Heti összesítés
        st.caption("Heti összesített pénzügyi adatok")
        
        # Heti aggregált adatok
        weekly_data = daily_df.groupby(["year", "week"]).agg({
            "value": "sum",
            "quantity": "sum",
            "count": "sum",
            "date": "first"
        }).reset_index()
        
        weekly_data["avg_price"] = weekly_data["value"] / weekly_data["quantity"].where(weekly_data["quantity"] > 0, 1)
        weekly_data["week_label"] = weekly_data["date"].dt.strftime("%Y-%W")
        
        # Heti bevétel trend
        fig_weekly = px.bar(
            weekly_data,
            x="week_label",
            y="value",
            title="Heti bevétel alakulása",
            labels={"week_label": "Hét", "value": "Bevétel (Ft)"}
        )
        
        st.plotly_chart(fig_weekly, use_container_width=True)
        
        # Heti átlagárak
        fig_weekly_avg = px.line(
            weekly_data,
            x="week_label",
            y="avg_price",
            title="Heti átlagárak alakulása",
            labels={"week_label": "Hét", "avg_price": "Átlagár (Ft/kg)"}
        )
        
        st.plotly_chart(fig_weekly_avg, use_container_width=True)
        
        # Heti adatok exportálása
        st.download_button(
            label="Heti adatok exportálása CSV-be",
            data=weekly_data.to_csv(index=False).encode('utf-8'),
            file_name=f"penzugy_heti_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
    
    with tabs[2]:  # Havi összesítés
        st.caption("Havi összesített pénzügyi adatok")
        
        # Havi aggregált adatok
        monthly_data = daily_df.groupby("month_name").agg({
            "value": "sum",
            "quantity": "sum",
            "count": "sum"
        }).reset_index()
        
        monthly_data["avg_price"] = monthly_data["value"] / monthly_data["quantity"].where(monthly_data["quantity"] > 0, 1)
        monthly_data = monthly_data.sort_values("month_name")
        
        # Havi összehasonlító diagram
        fig_monthly = go.Figure()
        
        fig_monthly.add_trace(go.Bar(
            x=monthly_data["month_name"],
            y=monthly_data["value"],
            name="Havi bevétel (Ft)",
            marker_color='rgba(58, 71, 80, 0.7)'
        ))
        
        fig_monthly.add_trace(go.Scatter(
            x=monthly_data["month_name"],
            y=monthly_data["avg_price"],
            name="Havi átlagár (Ft/kg)",
            mode='lines+markers',
            line=dict(color='green'),
            yaxis='y2'
        ))
        
        fig_monthly.update_layout(
            title="Havi bevétel és átlagár alakulása",
            xaxis=dict(title="Hónap"),
            yaxis=dict(title="Bevétel (Ft)"),
            yaxis2=dict(title="Átlagár (Ft/kg)", overlaying="y", side="right"),
            legend=dict(x=0.01, y=0.99)
        )
        
        st.plotly_chart(fig_monthly, use_container_width=True)
        
        # Havi adatok táblázatos formában
        st.dataframe(
            monthly_data,
            column_config={
                "month_name": "Hónap",
                "value": st.column_config.NumberColumn("Bevétel (Ft)", format="%.2f Ft"),
                "quantity": st.column_config.NumberColumn("Mennyiség (kg)", format="%.2f kg"),
                "count": "Ajánlatok száma",
                "avg_price": st.column_config.NumberColumn("Átlagár (Ft/kg)", format="%.2f Ft/kg")
            },
            hide_index=True,
            use_container_width=True
        )
        
        # Havi adatok exportálása
        st.download_button(
            label="Havi adatok exportálása CSV-be",
            data=monthly_data.to_csv(index=False).encode('utf-8'),
            file_name=f"penzugy_havi_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )
    
    with tabs[3]:  # Kumulatív trendek
        st.caption("Kumulatív pénzügyi trendek az időszak alatt")
        
        # Kumulatív bevétel trend
        fig_cumulative = go.Figure()
        
        fig_cumulative.add_trace(go.Scatter(
            x=daily_df["date"],
            y=daily_df["cumulative_value"],
            name="Kumulatív bevétel (Ft)",
            mode='lines',
            fill='tozeroy',
            line=dict(color='blue')
        ))
        
        fig_cumulative.update_layout(
            title="Kumulatív bevétel alakulása",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Kumulatív bevétel (Ft)")
        )
        
        st.plotly_chart(fig_cumulative, use_container_width=True)
        
        # Kumulatív mennyiség trend
        fig_cumulative_qty = go.Figure()
        
        fig_cumulative_qty.add_trace(go.Scatter(
            x=daily_df["date"],
            y=daily_df["cumulative_quantity"],
            name="Kumulatív mennyiség (kg)",
            mode='lines',
            fill='tozeroy',
            line=dict(color='green')
        ))
        
        fig_cumulative_qty.update_layout(
            title="Kumulatív beszállított mennyiség alakulása",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Kumulatív mennyiség (kg)")
        )
        
        st.plotly_chart(fig_cumulative_qty, use_container_width=True)
    
    with tabs[4]:  # Forgalom előrejelzés
        st.caption("Várható forgalom előrejelzés")
        
        if len(daily_df) > 7:
            # Egyszerű lineáris trend számítás saját implementációval, sklearn nélkül
            # Napok számozása
            days = np.array(range(len(daily_df)))
            values = daily_df["value"].values
            
            # Saját lineáris regresszió
            def linear_regression(x, y):
                n = len(x)
                mean_x = np.mean(x)
                mean_y = np.mean(y)
                
                # Regressziós együtthatók számítása
                numerator = 0
                denominator = 0
                
                for i in range(n):
                    numerator += (x[i] - mean_x) * (y[i] - mean_y)
                    denominator += (x[i] - mean_x) ** 2
                
                slope = numerator / denominator
                intercept = mean_y - slope * mean_x
                
                return slope, intercept
            
            # Regressziós együtthatók számítása
            slope, intercept = linear_regression(days, values)
            
            # Következő 7 nap előrejelzése
            future_days = np.array(range(len(daily_df), len(daily_df) + 7))
            predicted_values = intercept + slope * future_days
            
            # Dátumok a jövőre
            last_date = daily_df["date"].iloc[-1]
            future_dates = [last_date + datetime.timedelta(days=i+1) for i in range(7)]
            
            # Előrejelzési adatok táblázatba
            forecast_df = pd.DataFrame({
                "date": future_dates,
                "predicted_value": predicted_values
            })
            
            # Összevont ábra a múltbeli és előrejelzett adatokkal
            fig_forecast = go.Figure()
            
            # Múltbeli adatok
            fig_forecast.add_trace(go.Scatter(
                x=daily_df["date"],
                y=daily_df["value"],
                mode='lines+markers',
                name='Tényleges bevétel',
                line=dict(color='blue')
            ))
            
            # Előrejelzett adatok
            fig_forecast.add_trace(go.Scatter(
                x=forecast_df["date"],
                y=forecast_df["predicted_value"],
                mode='lines+markers',
                name='Előrejelzett bevétel',
                line=dict(color='red', dash='dot')
            ))
            
            fig_forecast.update_layout(
                title="Bevétel előrejelzés a következő 7 napra",
                xaxis=dict(title="Dátum"),
                yaxis=dict(title="Bevétel (Ft)"),
                legend=dict(x=0.01, y=0.99)
            )
            
            st.plotly_chart(fig_forecast, use_container_width=True)
            
            # Összesített előrejelzés
            total_predicted = forecast_df["predicted_value"].sum()
            
            st.metric(
                label="Előrejelzett bevétel a következő 7 napra", 
                value=format_price(total_predicted)
            )
            
            # Táblázat az előrejelzett értékekkel
            st.dataframe(
                forecast_df,
                column_config={
                    "date": "Dátum",
                    "predicted_value": st.column_config.NumberColumn("Előrejelzett bevétel (Ft)", format="%.2f Ft")
                },
                hide_index=True,
                use_container_width=True
            )
            
            # Előrejelzés exportálása
            st.download_button(
                label="Előrejelzés exportálása CSV-be",
                data=forecast_df.to_csv(index=False).encode('utf-8'),
                file_name=f"penzugy_elorejelzes_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
        else:
            st.info("Az előrejelzéshez legalább 7 nap adatai szükségesek.")
    
    # Termékenkénti pénzügyi elemzés
    st.subheader("Termékenkénti pénzügyi elemzés")
    
    # Termék árak és termék statisztikák
    product_prices = stats.get("product_price_summary", [])
    product_stats = stats.get("product_summary", [])
    
    if product_prices or product_stats:
        if product_prices:
            # DataFrame létrehozása
            price_df = pd.DataFrame(product_prices)
            
            # Oszlopok típusának konvertálása
            for col in ['average_price', 'min_price', 'max_price', 'total_value']:
                if col in price_df.columns:
                    price_df[col] = pd.to_numeric(price_df[col], errors='coerce')
            
            # Termékenkénti ár összehasonlítás
            fig_prices = px.bar(
                price_df, 
                x="product_name", 
                y="average_price", 
                title="Termékenkénti átlagárak",
                labels={"product_name": "Termék", "average_price": "Átlagár (Ft/kg)"},
                error_y=[p-m for p, m in zip(price_df["max_price"], price_df["average_price"])] if "max_price" in price_df.columns else None
            )
            
            st.plotly_chart(fig_prices, use_container_width=True)
            
            # Táblázat megjelenítése
            st.caption("Termékenkénti árstatisztikák")
            st.dataframe(
                price_df,
                column_config={
                    "product_name": "Termék",
                    "average_price": st.column_config.NumberColumn("Átlagár (Ft/kg)", format="%.2f Ft"),
                    "min_price": st.column_config.NumberColumn("Minimum ár (Ft/kg)", format="%.2f Ft"),
                    "max_price": st.column_config.NumberColumn("Maximum ár (Ft/kg)", format="%.2f Ft"),
                    "total_value": st.column_config.NumberColumn("Összes érték (Ft)", format="%.2f Ft"),
                    "offer_count": "Ajánlatok száma"
                },
                hide_index=True,
                use_container_width=True
            )
        
        if product_stats:
            # DataFrame létrehozása
            product_df = pd.DataFrame(product_stats)
            
            # Oszlopok típusának konvertálása
            for col in ['total_quantity', 'total_value', 'average_price']:
                if col in product_df.columns:
                    product_df[col] = pd.to_numeric(product_df[col], errors='coerce')
            
            # Termékenkénti bevétel megoszlás
            st.caption("Bevétel megoszlása termékek között")
            fig_revenue = px.pie(
                product_df,
                values="total_value",
                names="product_name",
                title="Bevétel megoszlása termékek szerint",
                hole=0.4
            )
            
            st.plotly_chart(fig_revenue, use_container_width=True)
            
            # Top 5 legjövedelmezőbb termék
            top_revenue_products = product_df.sort_values("total_value", ascending=False).head(5)
            
            st.caption("Top 5 legjövedelmezőbb termék")
            fig_top = px.bar(
                top_revenue_products,
                x="product_name",
                y="total_value",
                title="Top 5 legjövedelmezőbb termék",
                labels={"product_name": "Termék", "total_value": "Összes bevétel (Ft)"}
            )
            
            st.plotly_chart(fig_top, use_container_width=True)
            
            # Kategória szerinti csoportosítás
            if "category_name" in product_df.columns:
                category_stats = product_df.groupby("category_name").agg({
                    "total_value": "sum",
                    "total_quantity": "sum",
                    "offer_count": "sum"
                }).reset_index()
                
                category_stats["average_price"] = category_stats["total_value"] / category_stats["total_quantity"].where(category_stats["total_quantity"] > 0, 1)
                
                st.caption("Bevétel megoszlása kategóriák szerint")
                fig_category = px.pie(
                    category_stats,
                    values="total_value",
                    names="category_name",
                    title="Bevétel megoszlása kategóriák szerint",
                    hole=0.4
                )
                
                st.plotly_chart(fig_category, use_container_width=True)
                
                # Kategória táblázat
                st.dataframe(
                    category_stats,
                    column_config={
                        "category_name": "Kategória",
                        "total_value": st.column_config.NumberColumn("Összes érték (Ft)", format="%.2f Ft"),
                        "total_quantity": st.column_config.NumberColumn("Összes mennyiség (kg)", format="%.2f kg"),
                        "average_price": st.column_config.NumberColumn("Átlagár (Ft/kg)", format="%.2f Ft"),
                        "offer_count": "Ajánlatok száma"
                    },
                    hide_index=True,
                    use_container_width=True
                )
        
        # Termékadatok exportálása
        st.subheader("Termékadatok exportálása")
        
        if product_prices:
            st.download_button(
                label="Ár statisztikák exportálása",
                data=price_df.to_csv(index=False).encode('utf-8'),
                file_name=f"termek_arak_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
        
        if product_stats:
            st.download_button(
                label="Termék statisztikák exportálása",
                data=product_df.to_csv(index=False).encode('utf-8'),
                file_name=f"termek_statisztikak_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
    else:
        st.info("Nincs elérhető adat a termék statisztikákhoz.")
    
    # Összesített pénzügyi adatok exportálása
    st.subheader("Teljes pénzügyi riport exportálása")
    
    # Export formátum választása
    export_format = st.radio("Válassza ki az exportálás formátumát:", ["CSV", "Excel"], horizontal=True)
    
    # Excel exportálás (több munkalappal)
    if export_format == "Excel":
        # Excel fájl előkészítése több munkalappal
        # Excel buffer létrehozása
        excel_buffer = io.BytesIO()
        
        # ExcelWriter létrehozása
        with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
            # Összesítő lap
            summary_data = {
                "Mutató": ["Időszak", "Összes ajánlat", "Elfogadott ajánlatok", "Elfogadási arány", 
                            "Összes mennyiség (kg)", "Összes érték (Ft)", "Átlagár (Ft/kg)"],
                "Érték": [f"{from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')}",
                          total_offers,
                          accepted_offers,
                          f"{(accepted_offers / total_offers * 100) if total_offers > 0 else 0:.1f}%",
                          total_quantity,
                          total_value,
                          avg_price]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Összesítés', index=False)
            
            # Napi adatok lap
            if daily_data:
                daily_export = pd.DataFrame(daily_data)
                daily_export.to_excel(writer, sheet_name='Napi adatok', index=False)
            
            # Státusz adatok lap
            if status_data:
                status_export = pd.DataFrame(status_data)
                status_export["status_name"] = status_export["status"].apply(lambda s: format_status(s))
                status_export.to_excel(writer, sheet_name='Státuszok', index=False)
            
            # Termék adatok lap
            product_stats = stats.get("product_summary", [])
            if product_stats:
                product_export = pd.DataFrame(product_stats)
                product_export.to_excel(writer, sheet_name='Termékek', index=False)
        
        # Excel fájl letöltése
        st.download_button(
            label="Teljes riport letöltése Excel formátumban",
            data=excel_buffer.getvalue(),
            file_name=f"penzugyi_riport_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.xlsx",
            mime="application/vnd.ms-excel"
        )
    else:  # CSV exportálás
        # Összesített adatok 
        summary_data = {
            "Időszak": f"{from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')}",
            "Összes ajánlat": total_offers,
            "Elfogadott ajánlatok": accepted_offers,
            "Elfogadási arány": f"{(accepted_offers / total_offers * 100) if total_offers > 0 else 0:.1f}%",
            "Összes mennyiség (kg)": total_quantity,
            "Összes érték (Ft)": total_value,
            "Átlagár (Ft/kg)": avg_price
        }
        
        # DataFrame létrehozása
        summary_df = pd.DataFrame([summary_data])
        
        st.download_button(
            label="Összesített pénzügyi adatok exportálása CSV formátumban",
            data=summary_df.to_csv(index=False).encode('utf-8'),
            file_name=f"penzugyi_osszegzes_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

def show_producer_report(stats, from_date, to_date):
    """
    Termelői riport megjelenítése.
    
    Args:
        stats (dict): Statisztikai adatok
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Záró dátum
    """
    st.header(f"Termelői riport ({from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')})")
    
    # Ellenőrizzük a szükséges adatok meglétét
    if "user_id" not in stats:
        st.warning("A termelői részletes adatok nem érhetők el. Kérjük, válasszon ki egy termelőt a szűrési feltételeknél.")
        
        # Termelők listája
        success_users, users = users_api.get_all_farmers()
        if success_users and users:
            st.subheader("Válasszon termelőt a részletes adatok megtekintéséhez")
            
            # Rendezzük a termelőket név szerint
            users = sorted(users, key=lambda x: x.get("contact_name", ""))
            
            # Táblázat a termelőkről
            user_data = []
            for user in users:
                user_data.append({
                    "id": user.get("id"),
                    "contact_name": user.get("contact_name", "Névtelen"),
                    "company_name": user.get("company_name", "-"),
                    "email": user.get("email", "-"),
                    "phone_number": user.get("phone_number", "-")
                })
            
            user_df = pd.DataFrame(user_data)
            st.dataframe(
                user_df,
                column_config={
                    "id": "Azonosító",
                    "contact_name": "Kapcsolattartó neve",
                    "company_name": "Cég neve",
                    "email": "Email",
                    "phone_number": "Telefonszám"
                },
                hide_index=True,
                use_container_width=True
            )
        else:
            st.error("Nem sikerült lekérni a termelők listáját.")
        
        # Alap statisztikák
        total_offers = stats.get("total_offers", 0)
        total_quantity = float(stats.get("total_quantity", 0))
        total_value = float(stats.get("total_value", 0))
        
        st.info(f"Kiválasztott időszakban {total_offers} ajánlat, {total_quantity:.2f} kg mennyiség, {total_value:.2f} Ft érték")
        
        # Státusz szerinti megoszlás
        status_data = stats.get("status_summary", [])
        if status_data:
            # DataFrame létrehozása
            status_df = pd.DataFrame(status_data)
            
            # Státuszok formázása
            status_df["status_name"] = status_df["status"].apply(lambda s: format_status(s))
            
            # Kördiagram
            fig = px.pie(
                status_df, 
                values="count", 
                names="status_name", 
                title="Ajánlatok státusz szerinti megoszlása",
                hole=0.3
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        return
    
    # Termelő adatainak lekérdezése
    if "user_id" in stats:
        success_user, user = users_api.get_user(stats["user_id"])
        if success_user and user:
            st.subheader(f"Termelő: {user.get('contact_name', 'Ismeretlen')}")
            st.write(f"Cég: {user.get('company_name', '-')}")
            st.write(f"Telefon: {user.get('phone_number', '-')}")
            st.write(f"Email: {user.get('email', '-')}")
    
    # Termelői alap statisztikák
    total_offers = stats.get("total_offers", 0)
    total_quantity = float(stats.get("total_quantity", 0))
    total_value = float(stats.get("total_value", 0))
    avg_price = float(stats.get("average_price", 0))
    
    # Metrikai kártyák
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(label="Összes ajánlat", value=total_offers)
    with col2:
        st.metric(label="Összes mennyiség", value=format_quantity(total_quantity))
    with col3:
        st.metric(label="Összes érték", value=format_price(total_value))
    with col4:
        st.metric(label="Átlagos egységár", value=format_price(avg_price))
    
    # Időbeli eloszlás
    st.subheader("Termelői aktivitás időbeli eloszlása")
    
    # Napi bontás
    daily_data = stats.get("daily_summary", [])
    
    if daily_data:
        # DataFrame létrehozása
        daily_df = pd.DataFrame(daily_data)
        
        # Dátum rendezése
        daily_df = daily_df.sort_values("date")
        
        # Vonaldiagram - mennyiség
        fig = px.line(
            daily_df, 
            x="date", 
            y="quantity", 
            title="Napi beszállítások mennyisége",
            labels={"date": "Dátum", "quantity": "Mennyiség (kg)"}
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Területi diagram - érték napi bontásban
        fig2 = px.area(
            daily_df,
            x="date",
            y="value",
            title="Napi beszállítások értéke",
            labels={"date": "Dátum", "value": "Érték (Ft)"}
        )
        
        st.plotly_chart(fig2, use_container_width=True)
    else:
        st.info("Nincs elérhető adat az időbeli eloszláshoz.")
    
    # Státusz szerinti megoszlás
    st.subheader("Termelői ajánlatok státusz szerint")
    
    # Státusz adatok
    status_data = stats.get("status_summary", [])
    
    if status_data:
        # DataFrame létrehozása
        status_df = pd.DataFrame(status_data)
        
        # Státuszok formázása
        status_df["status_name"] = status_df["status"].apply(lambda s: format_status(s))
        
        # Oszlopdiagram
        fig = px.bar(
            status_df,
            x="status_name",
            y="count",
            title="Ajánlatok száma státusz szerint",
            labels={"status_name": "Státusz", "count": "Ajánlatok száma"}
        )
        
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("Nincs elérhető adat a státusz szerinti megoszláshoz.")
    
    # Exportálási lehetőség
    st.subheader("Termelői adatok exportálása")
    
    if daily_data:
        # DataFrame létrehozása exportáláshoz
        daily_export = pd.DataFrame(daily_data)
        
        # CSV letöltés
        st.download_button(
            label="Napi bontás exportálása",
            data=daily_export.to_csv(index=False).encode('utf-8'),
            file_name=f"termelo_riport_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

def show_product_report(stats, from_date, to_date):
    """
    Termék riport megjelenítése.
    
    Args:
        stats (dict): Statisztikai adatok
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Záró dátum
    """
    st.header(f"Termék riport ({from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')})")
    
    # Ellenőrizzük a szükséges adatok meglétét
    if "product_type_id" not in stats:
        st.warning("A termék részletes adatok nem érhetők el. Kérjük, válasszon ki egy terméktípust a szűrési feltételeknél.")
        
        # Alap statisztikák
        total_offers = stats.get("total_offers", 0)
        total_quantity = float(stats.get("total_quantity", 0))
        total_value = float(stats.get("total_value", 0))
        
        st.info(f"Kiválasztott időszakban {total_offers} ajánlat, {total_quantity:.2f} kg mennyiség, {total_value:.2f} Ft érték")
        return
    
    # Termék adatainak lekérdezése
    if "product_type_id" in stats:
        success_product, product = products_api.get_product_type(stats["product_type_id"])
        if success_product and product:
            st.subheader(f"Termék: {product.get('name', 'Ismeretlen')}")
            st.write(f"Kategória: {product.get('category_name', '-')}")
            if product.get("has_quality_grades", False):
                st.write("A termékhez minőségi besorolások tartoznak.")
    
    # Termék alap statisztikák
    total_offers = stats.get("total_offers", 0)
    total_quantity = float(stats.get("total_quantity", 0))
    total_value = float(stats.get("total_value", 0))
    avg_price = float(stats.get("average_price", 0))
    
    # Metrikai kártyák
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(label="Összes ajánlat", value=total_offers)
    with col2:
        st.metric(label="Összes mennyiség", value=format_quantity(total_quantity))
    with col3:
        st.metric(label="Összes érték", value=format_price(total_value))
    with col4:
        st.metric(label="Átlagos egységár", value=format_price(avg_price))
    
    # Időbeli eloszlás
    st.subheader("Termék beszállítások időbeli eloszlása")
    
    # Napi bontás
    daily_data = stats.get("daily_summary", [])
    
    if daily_data:
        # DataFrame létrehozása
        daily_df = pd.DataFrame(daily_data)
        
        # Dátum rendezése
        daily_df = daily_df.sort_values("date")
        
        # Kombinált diagram - mennyiség és érték
        fig = go.Figure()
        
        # Mennyiség (bal tengely)
        fig.add_trace(go.Bar(
            x=daily_df["date"],
            y=daily_df["quantity"],
            name="Mennyiség (kg)",
            marker_color='rgba(58, 71, 80, 0.6)'
        ))
        
        # Érték (jobb tengely)
        fig.add_trace(go.Scatter(
            x=daily_df["date"],
            y=daily_df["value"],
            name="Érték (Ft)",
            marker_color='rgb(246, 78, 139)',
            mode='lines+markers',
            yaxis='y2'
        ))
        
        # Két tengely beállítása
        fig.update_layout(
            title="Napi beszállítások mennyisége és értéke",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Mennyiség (kg)"),
            yaxis2=dict(title="Érték (Ft)", overlaying="y", side="right"),
            barmode='group',
            legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)")
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Átlagár változás időben
        daily_df["avg_price"] = daily_df.apply(lambda row: row["value"] / row["quantity"] if row["quantity"] > 0 else 0, axis=1)
        
        fig2 = px.line(
            daily_df,
            x="date",
            y="avg_price",
            title="Átlagár alakulása a kiválasztott időszakban",
            labels={"date": "Dátum", "avg_price": "Átlagár (Ft/kg)"}
        )
        
        st.plotly_chart(fig2, use_container_width=True)
    else:
        st.info("Nincs elérhető adat az időbeli eloszláshoz.")
    
    # Státusz szerinti megoszlás
    st.subheader("Termék ajánlatok státusz szerint")
    
    # Státusz adatok
    status_data = stats.get("status_summary", [])
    
    if status_data:
        # DataFrame létrehozása
        status_df = pd.DataFrame(status_data)
        
        # Státuszok formázása
        status_df["status_name"] = status_df["status"].apply(lambda s: format_status(s))
        
        # Kördiagram
        fig = px.pie(
            status_df,
            values="count",
            names="status_name",
            title="Ajánlatok státusz szerinti megoszlása",
            color_discrete_sequence=px.colors.sequential.RdBu
        )
        
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("Nincs elérhető adat a státusz szerinti megoszláshoz.")
    
    # Exportálási lehetőség
    st.subheader("Termék adatok exportálása")
    
    if daily_data:
        # DataFrame létrehozása exportáláshoz
        daily_export = pd.DataFrame(daily_data)
        daily_export["avg_price"] = daily_export.apply(lambda row: row["value"] / row["quantity"] if row["quantity"] > 0 else 0, axis=1)
        
        # CSV letöltés
        st.download_button(
            label="Napi bontás exportálása",
            data=daily_export.to_csv(index=False).encode('utf-8'),
            file_name=f"termek_riport_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

def show_time_comparison_report(from_date, to_date):
    """
    Időszaki összehasonlítás riport megjelenítése.
    
    Args:
        from_date (datetime.date): Kezdő dátum
        to_date (datetime.date): Záró dátum
    """
    st.header(f"Időszaki összehasonlítás riport")
    
    # A két időszak hossza
    days_diff = (to_date - from_date).days
    
    # Előző időszak dátumai
    prev_to_date = from_date - datetime.timedelta(days=1)
    prev_from_date = prev_to_date - datetime.timedelta(days=days_diff)
    
    st.write(f"**Aktuális időszak:** {from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')} ({days_diff+1} nap)")
    st.write(f"**Összehasonlító időszak:** {prev_from_date.strftime('%Y-%m-%d')} - {prev_to_date.strftime('%Y-%m-%d')} ({days_diff+1} nap)")
    
    # Statisztikák lekérése mindkét időszakra
    params1 = {
        "date_from": from_date.strftime("%Y-%m-%d"),
        "date_to": to_date.strftime("%Y-%m-%d")
    }
    
    params2 = {
        "date_from": prev_from_date.strftime("%Y-%m-%d"),
        "date_to": prev_to_date.strftime("%Y-%m-%d")
    }
    
    success1, stats1 = offers_api.get_statistics(params=params1)
    success2, stats2 = offers_api.get_statistics(params=params2)
    
    if not success1:
        show_error(f"Hiba az aktuális időszak statisztikáinak lekérésekor: {stats1}")
        return
    
    if not success2:
        show_error(f"Hiba az összehasonlító időszak statisztikáinak lekérésekor: {stats2}")
        return
    
    # Alapvető statisztikák
    total_offers1 = stats1.get("total_offers", 0)
    total_offers2 = stats2.get("total_offers", 0)
    
    accepted_offers1 = stats1.get("accepted_offers", 0)
    accepted_offers2 = stats2.get("accepted_offers", 0)
    
    # Convert any string values to proper numeric types
    total_quantity1 = float(stats1.get("total_quantity", 0))
    total_quantity2 = float(stats2.get("total_quantity", 0))
    
    total_value1 = float(stats1.get("total_value", 0))
    total_value2 = float(stats2.get("total_value", 0))
    
    # Változások kiszámítása
    offers_delta = ((total_offers1 - total_offers2) / total_offers2 * 100) if total_offers2 > 0 else 0
    accepted_delta = ((accepted_offers1 - accepted_offers2) / accepted_offers2 * 100) if accepted_offers2 > 0 else 0
    quantity_delta = ((total_quantity1 - total_quantity2) / total_quantity2 * 100) if total_quantity2 > 0 else 0
    value_delta = ((total_value1 - total_value2) / total_value2 * 100) if total_value2 > 0 else 0

    # Összehasonlító metrikai kártyák
    st.subheader("Fő mutatók összehasonlítása")
    
    col1, col2 = st.columns(2)
    with col1:
        st.metric(
            label="Összes ajánlat",
            value=total_offers1,
            delta=f"{offers_delta:.1f}%",
            delta_color="normal"
        )
        
        st.metric(
            label="Elfogadott ajánlatok",
            value=accepted_offers1,
            delta=f"{accepted_delta:.1f}%",
            delta_color="normal"
        )
    
    with col2:
        st.metric(
            label="Összes mennyiség (kg)",
            value=f"{total_quantity1:.2f}",
            delta=f"{quantity_delta:.1f}%",
            delta_color="normal"
        )
        
        st.metric(
            label="Összes érték (Ft)",
            value=f"{total_value1:.2f}",
            delta=f"{value_delta:.1f}%",
            delta_color="normal"
        )
    
    # Összehasonlító táblázat
    st.subheader("Időszakok összehasonlítása")
    
    comparison_data = {
        "Mutató": ["Ajánlatok száma", "Elfogadott ajánlatok", "Összes mennyiség (kg)", "Összes érték (Ft)"],
        "Aktuális időszak": [total_offers1, accepted_offers1, f"{total_quantity1:.2f}", f"{total_value1:.2f}"],
        "Előző időszak": [total_offers2, accepted_offers2, f"{total_quantity2:.2f}", f"{total_value2:.2f}"],
        "Változás (%)": [
            f"{offers_delta:.1f}%", 
            f"{accepted_delta:.1f}%", 
            f"{quantity_delta:.1f}%", 
            f"{value_delta:.1f}%"
        ]
    }
    
    comparison_df = pd.DataFrame(comparison_data)
    st.dataframe(comparison_df, hide_index=True, use_container_width=True)
    
    # Napi összehasonlítás
    st.subheader("Napi adatok összehasonlítása")
    
    daily_data1 = stats1.get("daily_summary", [])
    daily_data2 = stats2.get("daily_summary", [])
    
    if daily_data1 and daily_data2:
        # DataFrame létrehozása
        daily_df1 = pd.DataFrame(daily_data1)
        daily_df2 = pd.DataFrame(daily_data2)
        
        # Dátumok rendezése
        daily_df1 = daily_df1.sort_values("date")
        daily_df2 = daily_df2.sort_values("date")
        
        # Dátumok átcímkézése az aktuális időszakban az összehasonlíthatóság érdekében
        # Az előző időszak első napját a jelenlegi időszak első napjára képezzük le
        day_shift = (datetime.datetime.strptime(daily_df1["date"].iloc[0], "%Y-%m-%d") - 
                     datetime.datetime.strptime(daily_df2["date"].iloc[0], "%Y-%m-%d")).days
        
        daily_df2["adjusted_date"] = daily_df2["date"].apply(
            lambda x: (datetime.datetime.strptime(x, "%Y-%m-%d") + 
                     datetime.timedelta(days=day_shift)).strftime("%Y-%m-%d")
        )
        
        # Mennyiségek összehasonlítása
        fig = go.Figure()
        
        # Aktuális időszak
        fig.add_trace(go.Bar(
            x=daily_df1["date"],
            y=daily_df1["quantity"],
            name="Aktuális időszak",
            marker_color='rgba(58, 71, 80, 0.6)'
        ))
        
        # Előző időszak (igazított dátumokkal)
        fig.add_trace(go.Bar(
            x=daily_df2["adjusted_date"],
            y=daily_df2["quantity"],
            name="Előző időszak",
            marker_color='rgba(246, 78, 139, 0.6)'
        ))
        
        fig.update_layout(
            title="Napi beszállítások mennyisége időszakonként",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Mennyiség (kg)"),
            barmode='group',
            legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)")
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Értékek összehasonlítása
        fig2 = go.Figure()
        
        # Aktuális időszak
        fig2.add_trace(go.Line(
            x=daily_df1["date"],
            y=daily_df1["value"],
            name="Aktuális időszak",
            marker_color='rgb(58, 71, 80)'
        ))
        
        # Előző időszak (igazított dátumokkal)
        fig2.add_trace(go.Line(
            x=daily_df2["adjusted_date"],
            y=daily_df2["value"],
            name="Előző időszak",
            marker_color='rgb(246, 78, 139)'
        ))
        
        fig2.update_layout(
            title="Napi beszállítások értéke időszakonként",
            xaxis=dict(title="Dátum"),
            yaxis=dict(title="Érték (Ft)"),
            legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)")
        )
        
        st.plotly_chart(fig2, use_container_width=True)
    else:
        st.info("Nincs elegendő adat az időszakok összehasonlításához.")
        
    # Exportálási lehetőség
    st.subheader("Összehasonlító adatok exportálása")
        
    comparison_export = comparison_df.copy()
    
    st.download_button(
        label="Összehasonlító adatok exportálása",
        data=comparison_export.to_csv(index=False).encode('utf-8'),
        file_name=f"idoszaki_osszehasonlitas_{from_date.strftime('%Y%m%d')}-{to_date.strftime('%Y%m%d')}.csv",
        mime="text/csv"
    )

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Összegzés és riportok - {config.APP_NAME}",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a jelentések és riportok oldalt
    show_operator_reports()
