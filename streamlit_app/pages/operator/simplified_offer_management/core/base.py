"""
Base Classes and Interfaces
Alap osztályok és interfészek az egyszerűsített rendszerhez
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class ComponentResult:
    """Komponens műveletek eredménye"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class BaseComponent(ABC):
    """Alap komponens osztály"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self._initialized = False
    
    def initialize(self) -> ComponentResult:
        """Komponens inicializálása"""
        try:
            self._initialize()
            self._initialized = True
            self.logger.info(f"{self.name} component initialized successfully")
            return ComponentResult(success=True, data="Initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.name}: {e}")
            return ComponentResult(success=False, error=str(e))
    
    @abstractmethod
    def _initialize(self):
        """Komponens specifikus inicializálás"""
        pass
    
    def is_initialized(self) -> bool:
        """Ellenőrzi, hogy a komponens inicializálva van-e"""
        return self._initialized

class BaseManager(BaseComponent):
    """Alap manager osztály"""
    
    def __init__(self, name: str):
        super().__init__(name)
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
    
    def _get_cache_key(self, *args, **kwargs) -> str:
        """Cache kulcs generálása"""
        key_parts = [str(arg) for arg in args]
        key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        return "|".join(key_parts)
    
    def _is_cache_valid(self, key: str, ttl_seconds: int = 300) -> bool:
        """Cache érvényességének ellenőrzése"""
        if key not in self._cache_timestamps:
            return False
        
        age = (datetime.now() - self._cache_timestamps[key]).total_seconds()
        return age < ttl_seconds
    
    def _set_cache(self, key: str, value: Any):
        """Érték cache-elése"""
        self._cache[key] = value
        self._cache_timestamps[key] = datetime.now()
    
    def _get_cache(self, key: str) -> Optional[Any]:
        """Érték lekérése cache-ből"""
        return self._cache.get(key)
    
    def clear_cache(self):
        """Cache törlése"""
        self._cache.clear()
        self._cache_timestamps.clear()
        self.logger.debug(f"{self.name} cache cleared")

class ErrorHandler:
    """Központi hibakezelő"""
    
    @staticmethod
    def handle_api_error(error: Exception, operation: str) -> ComponentResult:
        """API hiba kezelése"""
        error_msg = f"API hiba {operation} során: {str(error)}"
        logger.error(error_msg)
        return ComponentResult(success=False, error=error_msg)
    
    @staticmethod
    def handle_validation_error(error: Exception, field: str) -> ComponentResult:
        """Validációs hiba kezelése"""
        error_msg = f"Validációs hiba {field} mezőben: {str(error)}"
        logger.warning(error_msg)
        return ComponentResult(success=False, error=error_msg)
    
    @staticmethod
    def handle_system_error(error: Exception, component: str) -> ComponentResult:
        """Rendszerhiba kezelése"""
        error_msg = f"Rendszerhiba {component} komponensben: {str(error)}"
        logger.error(error_msg, exc_info=True)
        return ComponentResult(success=False, error=error_msg)

class ConfigManager:
    """Konfigurációs manager"""
    
    # Alapértelmezett beállítások
    DEFAULT_CONFIG = {
        'page_size': 50,
        'cache_ttl': 300,  # 5 perc
        'api_timeout': 30,
        'max_retries': 3,
        'debounce_delay': 500,  # ms
        'mobile_breakpoint': 768,
    }
    
    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        """Konfiguráció lekérése"""
        return cls.DEFAULT_CONFIG.get(key, default)
    
    @classmethod
    def get_all(cls) -> Dict[str, Any]:
        """Összes konfiguráció lekérése"""
        return cls.DEFAULT_CONFIG.copy()

# Utility függvények
def safe_execute(func, *args, **kwargs) -> ComponentResult:
    """Biztonságos függvény végrehajtás"""
    try:
        result = func(*args, **kwargs)
        return ComponentResult(success=True, data=result)
    except Exception as e:
        return ErrorHandler.handle_system_error(e, func.__name__)

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> ComponentResult:
    """Kötelező mezők validálása"""
    missing_fields = [field for field in required_fields if not data.get(field)]
    
    if missing_fields:
        error_msg = f"Hiányzó kötelező mezők: {', '.join(missing_fields)}"
        return ComponentResult(success=False, error=error_msg)
    
    return ComponentResult(success=True, data="Validation passed")

def format_error_message(error: str, context: str = "") -> str:
    """Felhasználóbarát hibaüzenet formázása"""
    if context:
        return f"{context}: {error}"
    return error