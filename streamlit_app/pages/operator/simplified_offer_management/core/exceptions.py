"""
Custom Exceptions
Egyedi kivételek az egyszerűsített rendszerhez
"""

class SimplifiedOfferManagementError(Exception):
    """Alap kivétel osztály"""
    
    def __init__(self, message: str, component: str = "", details: str = ""):
        self.message = message
        self.component = component
        self.details = details
        super().__init__(self.message)
    
    def __str__(self):
        if self.component:
            return f"[{self.component}] {self.message}"
        return self.message

class APIError(SimplifiedOfferManagementError):
    """API kapcsolati hibák"""
    
    def __init__(self, message: str, status_code: int = None, response_data: str = ""):
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(message, "API", response_data)

class ValidationError(SimplifiedOfferManagementError):
    """Validációs hibák"""
    
    def __init__(self, message: str, field: str = "", value: str = ""):
        self.field = field
        self.value = value
        details = f"Field: {field}, Value: {value}" if field else ""
        super().__init__(message, "Validation", details)

class DataError(SimplifiedOfferManagementError):
    """Adatkezelési hibák"""
    
    def __init__(self, message: str, operation: str = "", data_type: str = ""):
        self.operation = operation
        self.data_type = data_type
        details = f"Operation: {operation}, Type: {data_type}" if operation else ""
        super().__init__(message, "Data", details)

class ComponentError(SimplifiedOfferManagementError):
    """Komponens hibák"""
    
    def __init__(self, message: str, component_name: str = "", state: str = ""):
        self.component_name = component_name
        self.state = state
        details = f"State: {state}" if state else ""
        super().__init__(message, component_name, details)

class ConfigurationError(SimplifiedOfferManagementError):
    """Konfigurációs hibák"""
    
    def __init__(self, message: str, config_key: str = "", config_value: str = ""):
        self.config_key = config_key
        self.config_value = config_value
        details = f"Key: {config_key}, Value: {config_value}" if config_key else ""
        super().__init__(message, "Configuration", details)

# Utility függvények a hibakezeléshez
def handle_exception(func):
    """Decorator a kivételek kezelésére"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SimplifiedOfferManagementError:
            # Saját kivételeket továbbítjuk
            raise
        except Exception as e:
            # Egyéb kivételeket becsomagoljuk
            raise SimplifiedOfferManagementError(
                message=f"Unexpected error in {func.__name__}: {str(e)}",
                component=func.__module__,
                details=str(e)
            )
    return wrapper