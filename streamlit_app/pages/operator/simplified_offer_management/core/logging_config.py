"""
Logging Configuration
Naplózási konfiguráció az egyszerűsített rendszerhez
"""
import logging
import sys
from typing import Optional
from datetime import datetime

class SimplifiedFormatter(logging.Formatter):
    """Egyszerűsített log formázó"""
    
    def format(self, record):
        # Színkódok
        colors = {
            'DEBUG': '\033[36m',    # Cyan
            'INFO': '\033[32m',     # Green  
            'WARNING': '\033[33m',  # Yellow
            'ERROR': '\033[31m',    # Red
            'CRITICAL': '\033[35m', # Magenta
            'RESET': '\033[0m'      # Reset
        }
        
        # Időbélyeg
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # Komponens név kinyerése
        component = record.name.split('.')[-1] if '.' in record.name else record.name
        
        # Színes formázás
        color = colors.get(record.levelname, colors['RESET'])
        reset = colors['RESET']
        
        # Formá<PERSON>tt üzenet
        formatted = f"{color}[{timestamp}] {record.levelname:<8} {component:<15} {record.getMessage()}{reset}"
        
        # Kivétel információ hozzáadása
        if record.exc_info:
            formatted += f"\n{self.formatException(record.exc_info)}"
        
        return formatted

def setup_logging(level: str = "INFO", component_name: Optional[str] = None) -> logging.Logger:
    """
    Naplózás beállítása
    
    Args:
        level: Log szint (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        component_name: Komponens neve (opcionális)
    
    Returns:
        Logger objektum
    """
    # Log szint beállítása
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Logger létrehozása
    logger_name = f"simplified_offer_management.{component_name}" if component_name else "simplified_offer_management"
    logger = logging.getLogger(logger_name)
    
    # Ha már be van állítva, ne állítsuk be újra
    if logger.handlers:
        return logger
    
    logger.setLevel(log_level)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(SimplifiedFormatter())
    
    logger.addHandler(console_handler)
    
    # Propagation kikapcsolása a duplikált üzenetek elkerülésére
    logger.propagate = False
    
    return logger

def get_logger(component_name: str) -> logging.Logger:
    """
    Logger lekérése komponens névvel
    
    Args:
        component_name: Komponens neve
    
    Returns:
        Logger objektum
    """
    return setup_logging(component_name=component_name)

# Alapértelmezett logger
default_logger = setup_logging()