"""
Filter Panel UI Component
Sz<PERSON>rő panel UI komponens Streamlit alapon
"""
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta
import logging

from ..core.base import BaseComponent, ComponentResult
from ..core.exceptions import ComponentError, handle_exception
from ..models.data_models import FilterState
from .filter_manager import get_filter_manager
from ..services.api_client import get_api_client

logger = logging.getLogger(__name__)

class FilterPanel(BaseComponent):
    """Szűrő panel UI komponens"""
    
    def __init__(self):
        super().__init__("FilterPanel")
        self.filter_manager = get_filter_manager()
        self.api_client = get_api_client()
        self._debounce_timer = None
        
    def _initialize(self):
        """Filter panel inicializálása"""
        # Session state inicializálása UI állapotokhoz
        if 'simplified_filter_panel' not in st.session_state:
            st.session_state.simplified_filter_panel = {
                'expanded_sections': {
                    'basic': True,
                    'advanced': False,
                    'saved': False
                },
                'mobile_mode': False,
                'last_render': None,
                'producers_cache': None,
                'product_types_cache': None
            }
        
        self.logger.info("Filter panel initialized")
    
    @handle_exception
    def render(self) -> FilterState:
        """
        Szűrő panel renderelése
        
        Returns:
            Aktuális FilterState
        """
        if not self.is_initialized():
            self.initialize()
        
        # Mobil mód detektálása
        self._detect_mobile_mode()
        
        # Debounced keresés ellenőrzése
        self._check_debounced_search()
        
        # Főcím és gyors műveletek
        self._render_header()
        
        # Alapvető szűrők
        self._render_basic_filters()
        
        # Haladó szűrők (összecsukható)
        self._render_advanced_filters()
        
        # Mentett szűrők (összecsukható)
        self._render_saved_filters()
        
        # Szűrő összefoglaló és műveletek
        self._render_filter_summary()
        
        # Aktuális szűrők visszaadása
        return self.filter_manager.get_current_filters()
    
    def _check_debounced_search(self):
        """Ellenőrzi és alkalmazza a debounced keresést"""
        panel_state = st.session_state.simplified_filter_panel
        
        # Minden debounced filter ellenőrzése
        for filter_type in ['search_debounce', 'producer_debounce', 'category_debounce']:
            debounce_info = panel_state.get(filter_type)
            
            if debounce_info and self._should_apply_debounced_filter(filter_type):
                # Alkalmazzuk a szűrőt
                cleaned_term = debounce_info['term'].strip() if debounce_info['term'] else None
                filter_key = debounce_info['filter_key']
                
                self.filter_manager.update_filter(filter_key, cleaned_term)
                
                # Töröljük a debounce információt
                del panel_state[filter_type]
                
                # Naplózás
                self.logger.debug(f"Applied debounced filter: {filter_key} = {cleaned_term}")
    
    def _detect_mobile_mode(self):
        """Mobil mód detektálása"""
        # Egyszerű heurisztika - valós implementációban JavaScript-tel lehetne pontosabb
        panel_state = st.session_state.simplified_filter_panel
        
        # Streamlit container width alapján (hozzávetőleges)
        try:
            # Képernyő szélesség becslése
            # Streamlit-ben nincs közvetlen hozzáférés a képernyő mérethez,
            # ezért közvetett módszereket használunk
            
            # Elrendezés tesztelése
            col1, col2, col3, col4, col5 = st.columns(5)
            
            # Ha ez a kód lefut, akkor valószínűleg desktop módban vagyunk
            panel_state['mobile_mode'] = False
            
            # Töröljük a teszteléshez használt üres oszlopokat
            st.empty()
        except Exception:
            # Ha hiba történik, akkor valószínűleg mobil módban vagyunk
            panel_state['mobile_mode'] = True
            
        # Mentjük az utolsó detektálás idejét
        import time
        panel_state['last_mobile_detection'] = time.time()
    
    def _handle_debounced_filter(self, value: str, filter_key: str, debounce_key: str):
        """
        Általános debounced szűrő kezelése 500ms késleltetéssel
        
        Args:
            value: Szűrési érték
            filter_key: Szűrő kulcs a FilterState-ben
            debounce_key: Session state kulcs a debounce információhoz
        """
        import time
        
        panel_state = st.session_state.simplified_filter_panel
        current_time = time.time()
        
        # Debounce timer beállítása
        panel_state[debounce_key] = {
            'term': value,
            'timestamp': current_time,
            'filter_key': filter_key
        }
        
        # Streamlit-ben nincs valós timer, ezért session state alapú megoldás
        # A következő render ciklusban ellenőrizzük az időt
        if self._should_apply_debounced_filter(debounce_key):
            cleaned_value = value.strip() if value else None
            self.filter_manager.update_filter(filter_key, cleaned_value)
    
    def _handle_debounced_search(self, search_term: str):
        """
        Debounced keresés kezelése 500ms késleltetéssel
        
        Args:
            search_term: Keresési kifejezés
        """
        self._handle_debounced_filter(search_term, 'search_term', 'search_debounce')
    
    def _should_apply_debounced_filter(self, debounce_key: str) -> bool:
        """
        Ellenőrzi, hogy alkalmazni kell-e a debounced szűrőt
        
        Args:
            debounce_key: Session state kulcs a debounce információhoz
            
        Returns:
            True ha alkalmazni kell
        """
        import time
        
        panel_state = st.session_state.simplified_filter_panel
        debounce_info = panel_state.get(debounce_key)
        
        if not debounce_info:
            return False
        
        # 500ms eltelt-e
        elapsed = (time.time() - debounce_info['timestamp']) * 1000
        return elapsed >= self.debounce_delay
    
    def _render_header(self):
        """Fejléc és gyors műveletek renderelése"""
        panel_state = st.session_state.simplified_filter_panel
        
        if panel_state['mobile_mode']:
            # Mobil layout - egymás alatt
            st.subheader("🔍 Szűrők")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 Frissítés", help="Szűrők alkalmazása", use_container_width=True):
                    self.filter_manager.mark_clean()
                    st.rerun()
            
            with col2:
                if st.button("❌ Törlés", help="Összes szűrő törlése", use_container_width=True):
                    self.filter_manager.reset_filters()
                    st.rerun()
        else:
            # Desktop layout - egy sorban
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                st.subheader("🔍 Szűrők")
            
            with col2:
                if st.button("🔄 Frissítés", help="Szűrők alkalmazása", use_container_width=True):
                    self.filter_manager.mark_clean()
                    st.rerun()
            
            with col3:
                if st.button("❌ Törlés", help="Összes szűrő törlése", use_container_width=True):
                    self.filter_manager.reset_filters()
                    st.rerun()
        
        # Gyors szűrők
        self._render_quick_filters()
    
    def _render_quick_filters(self):
        """Gyors szűrők renderelése"""
        panel_state = st.session_state.simplified_filter_panel
        
        st.write("**Gyors szűrők:**")
        
        if panel_state['mobile_mode']:
            # Mobil layout - 2x2 grid
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📅 Ma", help="Mai ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('today')
                    st.rerun()
                
                if st.button("🆕 Új", help="Létrehozott ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('created')
                    st.rerun()
            
            with col2:
                if st.button("📅 Hét", help="Heti ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('week')
                    st.rerun()
                
                if st.button("✅ Megerősített", help="Megerősített ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('confirmed')
                    st.rerun()
        else:
            # Desktop layout - egy sorban
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                if st.button("📅 Ma", help="Mai ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('today')
                    st.rerun()
            
            with col2:
                if st.button("📅 Hét", help="Heti ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('week')
                    st.rerun()
            
            with col3:
                if st.button("🆕 Új", help="Létrehozott ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('created')
                    st.rerun()
            
            with col4:
                if st.button("✅ Megerősített", help="Megerősített ajánlatok", use_container_width=True):
                    self.filter_manager.apply_quick_filter('confirmed')
                    st.rerun()
    
    def _render_basic_filters(self):
        """Alapvető szűrők renderelése"""
        panel_state = st.session_state.simplified_filter_panel
        
        with st.expander("🔧 Alapvető szűrők", expanded=panel_state['expanded_sections']['basic']):
            # Mobil és desktop layout
            if panel_state['mobile_mode']:
                self._render_basic_filters_mobile()
            else:
                self._render_basic_filters_desktop()
    
    def _render_basic_filters_desktop(self):
        """Alapvető szűrők desktop layout"""
        current_filters = self.filter_manager.get_current_filters()
        
        # Első sor: Termelő és keresés
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_producer_filter(current_filters)
        
        with col2:
            self._render_search_filter(current_filters)
        
        # Második sor: Dátumok
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_date_from_filter(current_filters)
        
        with col2:
            self._render_date_to_filter(current_filters)
        
        # Harmadik sor: Státusz
        self._render_status_filter(current_filters)
    
    def _render_basic_filters_mobile(self):
        """Alapvető szűrők mobil layout"""
        current_filters = self.filter_manager.get_current_filters()
        
        # Mobil módban egymás alatt
        self._render_producer_filter(current_filters)
        self._render_search_filter(current_filters)
        self._render_date_from_filter(current_filters)
        self._render_date_to_filter(current_filters)
        self._render_status_filter(current_filters)
    
    def _render_producer_filter(self, current_filters: FilterState):
        """Termelő szűrő renderelése keresési funkcióval"""
        producers = self._get_producers()
        
        if producers:
            # Keresőmező a termelőkhöz
            search_term = st.text_input(
                "🔍 Termelő keresés",
                value="",
                placeholder="Keresés termelő nevében...",
                key="producer_search",
                help="Írjon be egy nevet a termelők szűréséhez"
            )
            
            # Termelők szűrése a keresési kifejezés alapján
            filtered_producers = producers
            if search_term:
                search_term_lower = search_term.lower()
                filtered_producers = [p for p in producers if search_term_lower in p["name"].lower()]
            
            # Opciók előkészítése
            producer_options = [{"id": None, "name": "Minden termelő"}] + filtered_producers
            producer_names = [p["name"] for p in producer_options]
            
            # Jelenlegi érték megkeresése
            current_index = 0
            if current_filters.producer_id:
                for i, producer in enumerate(producer_options):
                    if producer["id"] == current_filters.producer_id:
                        current_index = i
                        break
                # Ha a keresés miatt a jelenlegi termelő nincs a listában
                if current_index >= len(producer_options):
                    current_index = 0
            
            # Ha nincs találat a keresésre
            if not filtered_producers and search_term:
                st.info(f"Nincs találat a keresésre: '{search_term}'")
            
            # Termelő választó
            selected_index = st.selectbox(
                "👤 Termelő",
                range(len(producer_names)),
                format_func=lambda x: producer_names[x],
                index=min(current_index, len(producer_names)-1),
                key="filter_producer",
                help="Válasszon termelőt a szűréshez. Használja a keresőt a gyorsabb megtaláláshoz."
            )
            
            selected_producer = producer_options[selected_index]
            new_producer_id = selected_producer["id"]
            
            # Találatok számának megjelenítése
            if search_term and len(filtered_producers) > 0:
                st.caption(f"{len(filtered_producers)} termelő található")
            
            # Frissítés ha változott
            if new_producer_id != current_filters.producer_id:
                self.filter_manager.update_filter('producer_id', new_producer_id)
        else:
            with st.spinner("Termelők betöltése..."):
                st.info("Termelők betöltése...")
    
    def _render_search_filter(self, current_filters: FilterState):
        """Keresési szűrő renderelése debounced frissítéssel"""
        search_term = st.text_input(
            "🔍 Keresés",
            value=current_filters.search_term or "",
            placeholder="Keresés termék vagy termelő nevében...",
            help="Keresés a termék vagy termelő nevében (500ms késleltetéssel frissül)",
            key="filter_search"
        )
        
        # Debounced frissítés implementálása
        if search_term != (current_filters.search_term or ""):
            self._handle_debounced_search(search_term)
            
            # Vizuális visszajelzés a debounce állapotról
            panel_state = st.session_state.simplified_filter_panel
            if panel_state.get('search_debounce'):
                st.caption("⏱️ Keresés folyamatban...")
                
        # Ha van aktív keresés, mutassuk
        if current_filters.search_term:
            st.caption(f"🔍 Aktív keresés: '{current_filters.search_term}'")
            
            # Keresés törlése gomb
            if st.button("❌ Keresés törlése", key="clear_search"):
                self.filter_manager.update_filter('search_term', None)
                st.rerun()
    
    def _render_date_from_filter(self, current_filters: FilterState):
        """Kezdő dátum szűrő renderelése"""
        date_from = st.date_input(
            "📅 Kezdő dátum",
            value=current_filters.date_from,
            help="Szállítási dátum kezdete. A záró dátum automatikusan frissül ha szükséges.",
            key="filter_date_from"
        )
        
        if date_from != current_filters.date_from:
            # Validáció: nem lehet jövőbeli dátum túl messze
            from datetime import date, timedelta
            max_future_date = date.today() + timedelta(days=365)
            
            if date_from and date_from > max_future_date:
                st.warning("⚠️ A kezdő dátum túl messze van a jövőben")
            else:
                self.filter_manager.update_filter('date_from', date_from)
    
    def _render_date_to_filter(self, current_filters: FilterState):
        """Záró dátum szűrő renderelése"""
        date_to = st.date_input(
            "📅 Záró dátum",
            value=current_filters.date_to,
            help="Szállítási dátum vége. A kezdő dátum automatikusan frissül ha szükséges.",
            key="filter_date_to"
        )
        
        if date_to != current_filters.date_to:
            # Validáció: nem lehet jövőbeli dátum túl messze
            from datetime import date, timedelta
            max_future_date = date.today() + timedelta(days=365)
            
            if date_to and date_to > max_future_date:
                st.warning("⚠️ A záró dátum túl messze van a jövőben")
            elif date_to and current_filters.date_from and date_to < current_filters.date_from:
                st.warning("⚠️ A záró dátum nem lehet korábbi a kezdő dátumnál")
            else:
                self.filter_manager.update_filter('date_to', date_to)
    
    def _render_status_filter(self, current_filters: FilterState):
        """Státusz szűrő renderelése"""
        status_options = [
            {"value": "CREATED", "label": "🆕 Létrehozva"},
            {"value": "CONFIRMED_BY_COMPANY", "label": "✅ Megerősítve"},
            {"value": "ACCEPTED_BY_USER", "label": "👍 Elfogadva"},
            {"value": "REJECTED_BY_USER", "label": "👎 Elutasítva"},
            {"value": "FINALIZED", "label": "🏁 Véglegesítve"}
        ]
        
        status_labels = [opt["label"] for opt in status_options]
        
        # Jelenlegi kiválasztások megkeresése
        current_indices = []
        for i, option in enumerate(status_options):
            if option["value"] in current_filters.status_filters:
                current_indices.append(i)
        
        selected_indices = st.multiselect(
            "📊 Státusz",
            range(len(status_labels)),
            default=current_indices,
            format_func=lambda x: status_labels[x],
            help="Válasszon egy vagy több státuszt",
            key="filter_status"
        )
        
        # Új státusz lista
        new_status_filters = [status_options[i]["value"] for i in selected_indices]
        
        # Frissítés ha változott
        if set(new_status_filters) != set(current_filters.status_filters):
            self.filter_manager.update_filter('status_filters', new_status_filters)
    
    def _render_advanced_filters(self):
        """Haladó szűrők renderelése"""
        panel_state = st.session_state.simplified_filter_panel
        
        with st.expander("⚙️ Haladó szűrők", expanded=panel_state['expanded_sections']['advanced']):
            current_filters = self.filter_manager.get_current_filters()
            
            # Termék kategória
            self._render_product_category_filter(current_filters)
            
            # Rendezési beállítások
            col1, col2 = st.columns(2)
            
            with col1:
                self._render_sort_column_filter(current_filters)
            
            with col2:
                self._render_sort_direction_filter(current_filters)
            
            # Lapozási beállítások
            col1, col2 = st.columns(2)
            
            with col1:
                self._render_page_size_filter(current_filters)
            
            with col2:
                st.metric("Jelenlegi oldal", current_filters.page)
    
    def _render_product_category_filter(self, current_filters: FilterState):
        """Termék kategória hierarchikus szűrő renderelése"""
        product_types = self._get_product_types()
        
        if product_types:
            # Kategóriák hierarchikus rendezése
            categories_by_parent = self._organize_categories_hierarchically(product_types)
            
            # Fő kategória választó
            st.write("🏷️ **Termék kategória**")
            
            # Minden kategória opció
            all_selected = st.checkbox(
                "Minden kategória",
                value=current_filters.product_category_id is None,
                key="all_categories"
            )
            
            if all_selected:
                # Ha "Minden kategória" van kiválasztva
                if current_filters.product_category_id is not None:
                    self.filter_manager.update_filter('product_category_id', None)
            else:
                # Hierarchikus kategória választó
                selected_category_id = current_filters.product_category_id
                
                # Fő kategóriák (parent_id = None)
                root_categories = categories_by_parent.get(None, [])
                
                if root_categories:
                    # Fő kategória kiválasztása
                    root_category_names = [c["name"] for c in root_categories]
                    
                    # Jelenlegi fő kategória meghatározása
                    current_root_index = 0
                    current_root_id = None
                    
                    # Ha van kiválasztott kategória, megkeressük a szülőjét
                    if selected_category_id:
                        for cat in product_types:
                            if cat["id"] == selected_category_id:
                                # Ha van parent_id, akkor az a fő kategória
                                if cat.get("parent_id") is not None:
                                    current_root_id = cat["parent_id"]
                                else:
                                    # Ha nincs parent_id, akkor ez maga egy fő kategória
                                    current_root_id = cat["id"]
                                break
                    
                    # Fő kategória index meghatározása
                    if current_root_id:
                        for i, cat in enumerate(root_categories):
                            if cat["id"] == current_root_id:
                                current_root_index = i
                                break
                    
                    # Fő kategória választó
                    selected_root_index = st.selectbox(
                        "Fő kategória",
                        range(len(root_category_names)),
                        format_func=lambda x: root_category_names[x],
                        index=current_root_index,
                        key="root_category"
                    )
                    
                    selected_root = root_categories[selected_root_index]
                    
                    # Alkategóriák az adott fő kategóriához
                    subcategories = categories_by_parent.get(selected_root["id"], [])
                    
                    if subcategories:
                        # Van-e kiválasztva alkategória a jelenlegi fő kategória alatt
                        is_subcategory_selected = False
                        current_sub_index = 0
                        
                        if selected_category_id:
                            for i, subcat in enumerate(subcategories):
                                if subcat["id"] == selected_category_id:
                                    current_sub_index = i
                                    is_subcategory_selected = True
                                    break
                        
                        # Alkategória választó
                        subcategory_names = ["[Összes a kategóriában]"] + [c["name"] for c in subcategories]
                        
                        selected_sub_index = st.selectbox(
                            "Alkategória",
                            range(len(subcategory_names)),
                            format_func=lambda x: subcategory_names[x],
                            index=0 if not is_subcategory_selected else current_sub_index + 1,
                            key="subcategory"
                        )
                        
                        # Kategória ID meghatározása
                        if selected_sub_index == 0:
                            # Fő kategória kiválasztva, alkategória nélkül
                            new_category_id = selected_root["id"]
                        else:
                            # Alkategória kiválasztva
                            new_category_id = subcategories[selected_sub_index - 1]["id"]
                    else:
                        # Nincs alkategória, csak a fő kategória
                        new_category_id = selected_root["id"]
                        
                        # Információ megjelenítése
                        st.caption("Nincsenek alkategóriák ebben a kategóriában")
                    
                    # Frissítés ha változott
                    if new_category_id != current_filters.product_category_id:
                        self.filter_manager.update_filter('product_category_id', new_category_id)
                else:
                    st.info("Nincsenek kategóriák definiálva")
        else:
            with st.spinner("Termék kategóriák betöltése..."):
                st.info("Termék kategóriák betöltése...")
    
    def _organize_categories_hierarchically(self, product_types):
        """
        Kategóriák hierarchikus rendezése
        
        Args:
            product_types: Termék típusok listája
            
        Returns:
            Dict[Optional[int], List[Dict]]: Kategóriák szülő ID szerint rendezve
        """
        categories_by_parent = {}
        
        for category in product_types:
            parent_id = category.get("parent_id")
            
            if parent_id not in categories_by_parent:
                categories_by_parent[parent_id] = []
                
            categories_by_parent[parent_id].append(category)
        
        # Rendezés név szerint minden kategória csoportban
        for parent_id, categories in categories_by_parent.items():
            categories_by_parent[parent_id] = sorted(categories, key=lambda x: x["name"])
            
        return categories_by_parent
    
    def _render_sort_column_filter(self, current_filters: FilterState):
        """Rendezési oszlop szűrő renderelése"""
        sort_options = [
            {"value": "created_at", "label": "Létrehozás dátuma"},
            {"value": "updated_at", "label": "Frissítés dátuma"},
            {"value": "delivery_date", "label": "Szállítási dátum"},
            {"value": "producer_name", "label": "Termelő neve"},
            {"value": "product_name", "label": "Termék neve"},
            {"value": "quantity_value", "label": "Mennyiség"},
            {"value": "status", "label": "Státusz"}
        ]
        
        sort_labels = [opt["label"] for opt in sort_options]
        
        # Jelenlegi érték megkeresése
        current_index = 0
        for i, option in enumerate(sort_options):
            if option["value"] == current_filters.sort_column:
                current_index = i
                break
        
        selected_index = st.selectbox(
            "📊 Rendezés",
            range(len(sort_labels)),
            format_func=lambda x: sort_labels[x],
            index=current_index,
            key="filter_sort_column"
        )
        
        new_sort_column = sort_options[selected_index]["value"]
        
        # Frissítés ha változott
        if new_sort_column != current_filters.sort_column:
            self.filter_manager.update_filter('sort_column', new_sort_column)
    
    def _render_sort_direction_filter(self, current_filters: FilterState):
        """Rendezési irány szűrő renderelése"""
        direction_options = [
            {"value": "desc", "label": "🔽 Csökkenő"},
            {"value": "asc", "label": "🔼 Növekvő"}
        ]
        
        direction_labels = [opt["label"] for opt in direction_options]
        
        # Jelenlegi érték megkeresése
        current_index = 0
        for i, option in enumerate(direction_options):
            if option["value"] == current_filters.sort_direction:
                current_index = i
                break
        
        selected_index = st.selectbox(
            "↕️ Irány",
            range(len(direction_labels)),
            format_func=lambda x: direction_labels[x],
            index=current_index,
            key="filter_sort_direction"
        )
        
        new_sort_direction = direction_options[selected_index]["value"]
        
        # Frissítés ha változott
        if new_sort_direction != current_filters.sort_direction:
            self.filter_manager.update_filter('sort_direction', new_sort_direction)
    
    def _render_page_size_filter(self, current_filters: FilterState):
        """Oldal méret szűrő renderelése"""
        page_size_options = [10, 25, 50, 100, 200]
        
        # Jelenlegi érték megkeresése
        current_index = 2  # 50 alapértelmezett
        if current_filters.page_size in page_size_options:
            current_index = page_size_options.index(current_filters.page_size)
        
        selected_page_size = st.selectbox(
            "📄 Oldal méret",
            page_size_options,
            index=current_index,
            key="filter_page_size"
        )
        
        # Frissítés ha változott
        if selected_page_size != current_filters.page_size:
            self.filter_manager.update_filter('page_size', selected_page_size)
    
    def _render_saved_filters(self):
        """Mentett szűrők renderelése"""
        panel_state = st.session_state.simplified_filter_panel
        
        with st.expander("💾 Mentett szűrők", expanded=panel_state['expanded_sections']['saved']):
            saved_filters = self.filter_manager.get_saved_filters()
            
            if saved_filters:
                # Mentett szűrők listája
                st.write("**Mentett szűrők:**")
                
                for name, filter_data in saved_filters.items():
                    col1, col2, col3 = st.columns([3, 1, 1])
                    
                    with col1:
                        st.write(f"📁 **{name}**")
                        if filter_data.get('description'):
                            st.caption(filter_data['description'])
                    
                    with col2:
                        if st.button("📂", key=f"load_{name}", help=f"Betöltés: {name}"):
                            self.filter_manager.load_saved_filter(name)
                            st.rerun()
                    
                    with col3:
                        if st.button("🗑️", key=f"delete_{name}", help=f"Törlés: {name}"):
                            # TODO: Implement delete saved filter
                            st.warning("Törlés funkció még nem implementált")
            
            # Új szűrő mentése
            st.divider()
            st.write("**Jelenlegi szűrők mentése:**")
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                filter_name = st.text_input(
                    "Szűrő neve",
                    placeholder="Adjon nevet a szűrőnek...",
                    key="save_filter_name"
                )
            
            with col2:
                if st.button("💾 Mentés", disabled=not filter_name):
                    result = self.filter_manager.save_filter(filter_name)
                    if result.success:
                        st.success(f"Szűrő mentve: {filter_name}")
                        st.rerun()
                    else:
                        st.error(f"Mentés sikertelen: {result.error}")
    
    def _render_filter_summary(self):
        """Szűrő összefoglaló renderelése"""
        # Validációs hibák megjelenítése
        validation_errors = self.filter_manager.get_validation_errors()
        if validation_errors:
            for error in validation_errors:
                st.error(f"⚠️ {error}")
        
        # Szűrő összefoglaló
        summary = self.filter_manager.get_filter_summary()
        if summary != "Nincs aktív szűrő":
            st.info(f"📋 Aktív szűrők: {summary}")
        
        # Dirty state jelzése
        if self.filter_manager.is_dirty():
            st.warning("⚠️ Vannak nem alkalmazott szűrő változások. Kattintson a 'Frissítés' gombra!")
        
        # Teljesítmény információk
        self._render_performance_info()
    
    def _render_performance_info(self):
        """Teljesítmény információk megjelenítése"""
        try:
            # Cache információk
            cache_info = self.filter_manager.cache_manager.get_cache_info()
            if cache_info:
                with st.expander("🔧 Teljesítmény információk", expanded=False):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Cache találatok", cache_info.get('hits', 0))
                    
                    with col2:
                        st.metric("Cache hibák", cache_info.get('misses', 0))
                    
                    with col3:
                        hit_rate = cache_info.get('hit_rate', 0)
                        st.metric("Találati arány", f"{hit_rate:.1%}")
        
        except Exception as e:
            self.logger.debug(f"Could not render performance info: {e}")
    
    def _get_producers(self) -> List[Dict[str, Any]]:
        """Termelők lekérése cache-eléssel"""
        panel_state = st.session_state.simplified_filter_panel
        
        # Cache ellenőrzése
        if panel_state['producers_cache'] is not None:
            return panel_state['producers_cache']
        
        try:
            result = self.api_client.get_producers()
            if result.success:
                panel_state['producers_cache'] = result.data
                return result.data
            else:
                self.logger.warning(f"Failed to load producers: {result.error}")
                # Docker környezetben hálózati hibák gyakoribbak lehetnek
                if "connection" in str(result.error).lower():
                    st.warning("🌐 Hálózati kapcsolat ellenőrzése...")
                return []
        except Exception as e:
            self.logger.error(f"Error loading producers: {e}")
            # Docker-specifikus hibaüzenet
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                st.error("🚫 API kapcsolat hiba. Ellenőrizze a backend szolgáltatást.")
            return []
    
    def _get_product_types(self) -> List[Dict[str, Any]]:
        """Terméktípusok lekérése cache-eléssel"""
        panel_state = st.session_state.simplified_filter_panel
        
        # Cache ellenőrzése
        if panel_state['product_types_cache'] is not None:
            return panel_state['product_types_cache']
        
        try:
            result = self.api_client.get_product_types()
            if result.success:
                panel_state['product_types_cache'] = result.data
                return result.data
            else:
                self.logger.warning(f"Failed to load product types: {result.error}")
                return []
        except Exception as e:
            self.logger.error(f"Error loading product types: {e}")
            return []

# Singleton instance
_filter_panel_instance = None

def get_filter_panel() -> FilterPanel:
    """
    Filter panel singleton lekérése
    
    Returns:
        FilterPanel instance
    """
    global _filter_panel_instance
    
    if _filter_panel_instance is None:
        _filter_panel_instance = FilterPanel()
        try:
            _filter_panel_instance.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize filter panel: {e}")
    
    return _filter_panel_instance