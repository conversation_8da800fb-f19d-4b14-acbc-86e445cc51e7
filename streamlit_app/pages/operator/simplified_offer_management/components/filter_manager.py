"""
Filter Manager
Kö<PERSON><PERSON><PERSON> sz<PERSON><PERSON><PERSON> kezel<PERSON> komponens session state alapon
"""
import streamlit as st
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, date, timedelta
import logging

from ..core.base import BaseManager, ComponentResult
from ..core.exceptions import ComponentError, handle_exception
from ..models.data_models import FilterState
from ..models.validation import FilterStateValidator, validate_filter_params
from ..services.cache_manager import get_cache_manager

logger = logging.getLogger(__name__)

class FilterManager(BaseManager):
    """Központi szűrő kezelő komponens"""
    
    def __init__(self):
        super().__init__("FilterManager")
        self.cache_manager = get_cache_manager()
        self.debounce_delay = 500  # ms
        
    def _initialize(self):
        """Filter manager inicializálása"""
        if 'simplified_filter_manager' not in st.session_state:
            st.session_state.simplified_filter_manager = {
                'current_filters': self._get_default_filters(),
                'saved_filters': {},
                'filter_history': [],
                'last_applied': None,
                'validation_errors': [],
                'dirty_state': False
            }
        
        self.logger.info("Filter manager initialized")
    
    def _get_default_filters(self) -> Dict[str, Any]:
        """Alapértelmezett szűrők lekérése"""
        return {
            'producer_id': None,
            'date_from': None,
            'date_to': None,
            'status_filters': [],
            'product_category_id': None,
            'search_term': None,
            'sort_column': 'created_at',
            'sort_direction': 'desc',
            'page': 1,
            'page_size': 50
        }
    
    @handle_exception
    def get_current_filters(self) -> FilterState:
        """
        Jelenlegi szűrők lekérése FilterState objektumként
        
        Returns:
            FilterState objektum
        """
        if not self.is_initialized():
            self.initialize()
        
        current_filters = st.session_state.simplified_filter_manager['current_filters']
        
        try:
            return FilterState(**current_filters)
        except Exception as e:
            self.logger.warning(f"Invalid filter state, using defaults: {e}")
            default_filters = self._get_default_filters()
            st.session_state.simplified_filter_manager['current_filters'] = default_filters
            return FilterState(**default_filters)
    
    @handle_exception
    def update_filter(self, key: str, value: Any, validate: bool = True) -> ComponentResult:
        """
        Egyedi szűrő frissítése
        
        Args:
            key: Szűrő kulcs
            value: Új érték
            validate: Validáció futtatása
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            current_filters = st.session_state.simplified_filter_manager['current_filters']
            old_value = current_filters.get(key)
            
            # Érték beállítása
            current_filters[key] = value
            
            # Dirty state beállítása
            st.session_state.simplified_filter_manager['dirty_state'] = True
            
            # Validáció futtatása ha kért
            if validate:
                validation_result = self._validate_current_filters()
                if not validation_result.success:
                    # Visszaállítás hiba esetén
                    current_filters[key] = old_value
                    return validation_result
            
            # Kapcsolódó szűrők frissítése
            self._handle_filter_dependencies(key, value)
            
            # Cache invalidálása
            self.cache_manager.invalidate("offers")
            
            self.logger.debug(f"Filter updated: {key} = {value}")
            return ComponentResult(success=True, data=f"Filter {key} updated")
            
        except Exception as e:
            error_msg = f"Failed to update filter {key}: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def update_filters(self, filters: Dict[str, Any], validate: bool = True) -> ComponentResult:
        """
        Több szűrő egyszerre frissítése
        
        Args:
            filters: Szűrők dictionary
            validate: Validáció futtatása
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            current_filters = st.session_state.simplified_filter_manager['current_filters']
            old_filters = current_filters.copy()
            
            # Szűrők frissítése
            current_filters.update(filters)
            
            # Dirty state beállítása
            st.session_state.simplified_filter_manager['dirty_state'] = True
            
            # Validáció futtatása ha kért
            if validate:
                validation_result = self._validate_current_filters()
                if not validation_result.success:
                    # Visszaállítás hiba esetén
                    st.session_state.simplified_filter_manager['current_filters'] = old_filters
                    return validation_result
            
            # Cache invalidálása
            self.cache_manager.invalidate("offers")
            
            self.logger.debug(f"Multiple filters updated: {list(filters.keys())}")
            return ComponentResult(success=True, data=f"{len(filters)} filters updated")
            
        except Exception as e:
            error_msg = f"Failed to update filters: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def _handle_filter_dependencies(self, key: str, value: Any):
        """
        Szűrő függőségek kezelése
        
        Args:
            key: Frissített szűrő kulcs
            value: Új érték
        """
        current_filters = st.session_state.simplified_filter_manager['current_filters']
        
        # Dátum függőségek
        if key == 'date_from' and value and current_filters.get('date_to'):
            if value > current_filters['date_to']:
                current_filters['date_to'] = value
        
        elif key == 'date_to' and value and current_filters.get('date_from'):
            if value < current_filters['date_from']:
                current_filters['date_from'] = value
        
        # Lapozás visszaállítása szűrő változáskor
        if key != 'page' and key != 'page_size':
            current_filters['page'] = 1
        
        # Keresési kifejezés tisztítása
        if key == 'search_term' and isinstance(value, str):
            current_filters['search_term'] = value.strip() if value else None
    
    def _validate_current_filters(self) -> ComponentResult:
        """
        Jelenlegi szűrők validálása
        
        Returns:
            ComponentResult
        """
        try:
            current_filters = st.session_state.simplified_filter_manager['current_filters']
            
            # Pydantic validáció
            validator = FilterStateValidator(**current_filters)
            
            # Validációs hibák törlése
            st.session_state.simplified_filter_manager['validation_errors'] = []
            
            return ComponentResult(success=True, data="Validation passed")
            
        except Exception as e:
            error_msg = f"Filter validation failed: {e}"
            
            # Validációs hibák tárolása
            st.session_state.simplified_filter_manager['validation_errors'] = [error_msg]
            
            self.logger.warning(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def reset_filters(self, keep_pagination: bool = False) -> ComponentResult:
        """
        Szűrők visszaállítása alapértelmezettre
        
        Args:
            keep_pagination: Lapozási beállítások megtartása
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            current_filters = st.session_state.simplified_filter_manager['current_filters']
            default_filters = self._get_default_filters()
            
            if keep_pagination:
                # Lapozási beállítások megtartása
                default_filters['page'] = current_filters.get('page', 1)
                default_filters['page_size'] = current_filters.get('page_size', 50)
                default_filters['sort_column'] = current_filters.get('sort_column', 'created_at')
                default_filters['sort_direction'] = current_filters.get('sort_direction', 'desc')
            
            st.session_state.simplified_filter_manager['current_filters'] = default_filters
            st.session_state.simplified_filter_manager['dirty_state'] = True
            st.session_state.simplified_filter_manager['validation_errors'] = []
            
            # Cache invalidálása
            self.cache_manager.invalidate("offers")
            
            self.logger.info("Filters reset to defaults")
            return ComponentResult(success=True, data="Filters reset")
            
        except Exception as e:
            error_msg = f"Failed to reset filters: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def apply_quick_filter(self, filter_type: str, value: Any = None) -> ComponentResult:
        """
        Gyors szűrő alkalmazása
        
        Args:
            filter_type: Szűrő típus ('today', 'week', 'month', 'created', 'confirmed')
            value: Opcionális érték
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            today = date.today()
            filters_to_update = {}
            
            # Dátum alapú gyors szűrők
            if filter_type == 'today':
                filters_to_update = {
                    'date_from': today,
                    'date_to': today
                }
            elif filter_type == 'week':
                week_start = today - timedelta(days=today.weekday())
                filters_to_update = {
                    'date_from': week_start,
                    'date_to': week_start + timedelta(days=6)
                }
            elif filter_type == 'month':
                month_start = today.replace(day=1)
                next_month = month_start.replace(month=month_start.month + 1) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1)
                month_end = next_month - timedelta(days=1)
                filters_to_update = {
                    'date_from': month_start,
                    'date_to': month_end
                }
            
            # Státusz alapú gyors szűrők
            elif filter_type == 'created':
                filters_to_update = {'status_filters': ['CREATED']}
            elif filter_type == 'confirmed':
                filters_to_update = {'status_filters': ['CONFIRMED_BY_COMPANY']}
            elif filter_type == 'pending':
                filters_to_update = {'status_filters': ['CREATED', 'CONFIRMED_BY_COMPANY']}
            
            # Egyéb gyors szűrők
            elif filter_type == 'clear_search':
                filters_to_update = {'search_term': None}
            elif filter_type == 'clear_dates':
                filters_to_update = {'date_from': None, 'date_to': None}
            elif filter_type == 'clear_status':
                filters_to_update = {'status_filters': []}
            
            # Custom érték
            elif filter_type == 'custom' and value:
                filters_to_update = value
            
            else:
                return ComponentResult(success=False, error=f"Unknown quick filter type: {filter_type}")
            
            # Szűrők alkalmazása
            result = self.update_filters(filters_to_update)
            
            if result.success:
                self.logger.info(f"Quick filter applied: {filter_type}")
            
            return result
            
        except Exception as e:
            error_msg = f"Failed to apply quick filter {filter_type}: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def get_filter_summary(self) -> str:
        """
        Szűrők összefoglalása szöveges formában
        
        Returns:
            Szűrők összefoglalása
        """
        if not self.is_initialized():
            return "Nincs aktív szűrő"
        
        try:
            filter_state = self.get_current_filters()
            return filter_state.get_summary()
        except Exception as e:
            self.logger.warning(f"Failed to get filter summary: {e}")
            return "Szűrő összefoglaló nem elérhető"
    
    def is_dirty(self) -> bool:
        """
        Ellenőrzi, hogy vannak-e nem alkalmazott szűrő változások
        
        Returns:
            True ha vannak változások
        """
        if not self.is_initialized():
            return False
        
        return st.session_state.simplified_filter_manager.get('dirty_state', False)
    
    def mark_clean(self):
        """Dirty state törlése (szűrők alkalmazva)"""
        if not self.is_initialized():
            self.initialize()
        
        st.session_state.simplified_filter_manager['dirty_state'] = False
        st.session_state.simplified_filter_manager['last_applied'] = datetime.now()
    
    def get_validation_errors(self) -> List[str]:
        """
        Validációs hibák lekérése
        
        Returns:
            Hibák listája
        """
        if not self.is_initialized():
            return []
        
        return st.session_state.simplified_filter_manager.get('validation_errors', [])
    
    def has_active_filters(self) -> bool:
        """
        Ellenőrzi, hogy vannak-e aktív szűrők
        
        Returns:
            True ha vannak aktív szűrők
        """
        try:
            filter_state = self.get_current_filters()
            return not filter_state.is_empty()
        except Exception:
            return False
    
    @handle_exception
    def save_filter(self, name: str, description: str = "") -> ComponentResult:
        """
        Jelenlegi szűrők mentése
        
        Args:
            name: Szűrő neve
            description: Leírás
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            current_filters = st.session_state.simplified_filter_manager['current_filters']
            saved_filters = st.session_state.simplified_filter_manager['saved_filters']
            
            saved_filter = {
                'name': name,
                'description': description,
                'filters': current_filters.copy(),
                'created_at': datetime.now(),
                'used_count': 0
            }
            
            saved_filters[name] = saved_filter
            
            self.logger.info(f"Filter saved: {name}")
            return ComponentResult(success=True, data=f"Filter '{name}' saved")
            
        except Exception as e:
            error_msg = f"Failed to save filter: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def load_saved_filter(self, name: str) -> ComponentResult:
        """
        Mentett szűrő betöltése
        
        Args:
            name: Szűrő neve
            
        Returns:
            ComponentResult
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            saved_filters = st.session_state.simplified_filter_manager['saved_filters']
            
            if name not in saved_filters:
                return ComponentResult(success=False, error=f"Saved filter '{name}' not found")
            
            saved_filter = saved_filters[name]
            
            # Szűrők betöltése
            result = self.update_filters(saved_filter['filters'])
            
            if result.success:
                # Használat számláló növelése
                saved_filter['used_count'] += 1
                saved_filter['last_used'] = datetime.now()
                
                self.logger.info(f"Saved filter loaded: {name}")
            
            return result
            
        except Exception as e:
            error_msg = f"Failed to load saved filter: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def get_saved_filters(self) -> Dict[str, Dict[str, Any]]:
        """
        Mentett szűrők lekérése
        
        Returns:
            Mentett szűrők dictionary
        """
        if not self.is_initialized():
            return {}
        
        return st.session_state.simplified_filter_manager.get('saved_filters', {})
    
    def get_filter_history(self) -> List[Dict[str, Any]]:
        """
        Szűrő történet lekérése
        
        Returns:
            Szűrő történet listája
        """
        if not self.is_initialized():
            return []
        
        return st.session_state.simplified_filter_manager.get('filter_history', [])

# Singleton instance
_filter_manager_instance = None

def get_filter_manager() -> FilterManager:
    """
    Filter manager singleton lekérése
    
    Returns:
        FilterManager instance
    """
    global _filter_manager_instance
    
    if _filter_manager_instance is None:
        _filter_manager_instance = FilterManager()
        try:
            _filter_manager_instance.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize filter manager: {e}")
    
    return _filter_manager_instance