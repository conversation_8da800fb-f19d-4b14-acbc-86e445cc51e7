"""
Validation Tests
Validációs tesztek a Pydantic modellek számára
"""
import pytest
from datetime import date, datetime
from decimal import Decimal
from pydantic import ValidationError

from ..models.validation import (
    FilterStateValidator,
    OfferValidator,
    ActionResultValidator,
    validate_api_response_data,
    validate_filter_params,
    validate_offer_data,
    validate_offers_batch,
    ValidationResult
)

class TestFilterStateValidator:
    """FilterStateValidator tesztek"""
    
    def test_valid_filter_state(self):
        """Érvényes szűrési állapot tesztelése"""
        data = {
            'producer_id': 1,
            'date_from': date(2024, 1, 1),
            'date_to': date(2024, 12, 31),
            'status_filters': ['CREATED', 'CONFIRMED_BY_COMPANY'],
            'search_term': 'test search',
            'page': 1,
            'page_size': 50
        }
        
        validator = FilterStateValidator(**data)
        assert validator.producer_id == 1
        assert validator.status_filters == ['CREATED', 'CONFIRMED_BY_COMPANY']
        assert validator.search_term == 'test search'
    
    def test_invalid_producer_id(self):
        """Érvénytelen termelő ID tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            FilterStateValidator(producer_id=0)
        
        assert 'greater than 0' in str(exc_info.value)
    
    def test_invalid_date_range(self):
        """Érvénytelen dátum tartomány tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            FilterStateValidator(
                date_from=date(2024, 12, 31),
                date_to=date(2024, 1, 1)
            )
        
        assert 'záró dátum nem lehet korábbi' in str(exc_info.value)
    
    def test_invalid_status_filter(self):
        """Érvénytelen státusz szűrő tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            FilterStateValidator(status_filters=['INVALID_STATUS'])
        
        assert 'Érvénytelen státusz' in str(exc_info.value)
    
    def test_search_term_cleaning(self):
        """Keresési kifejezés tisztítása tesztelése"""
        validator = FilterStateValidator(search_term='  test search  ')
        assert validator.search_term == 'test search'
        
        # Üres string esetén None-t kell visszaadni
        validator = FilterStateValidator(search_term='   ')
        assert validator.search_term is None
    
    def test_invalid_sort_column(self):
        """Érvénytelen rendezési oszlop tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            FilterStateValidator(sort_column='invalid_column')
        
        assert 'Érvénytelen rendezési oszlop' in str(exc_info.value)
    
    def test_invalid_sort_direction(self):
        """Érvénytelen rendezési irány tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            FilterStateValidator(sort_direction='invalid')
        
        assert 'asc" vagy "desc"' in str(exc_info.value)

class TestOfferValidator:
    """OfferValidator tesztek"""
    
    def test_valid_offer(self):
        """Érvényes ajánlat tesztelése"""
        data = {
            'id': 1,
            'producer_id': 1,
            'producer_name': 'Test Producer',
            'product_name': 'Test Product',
            'product_category': 'Test Category',
            'quantity_value': Decimal('100.50'),
            'quantity_unit': 'kg',
            'delivery_date': date(2024, 6, 15),
            'status': 'CREATED',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        validator = OfferValidator(**data)
        assert validator.id == 1
        assert validator.producer_name == 'Test Producer'
        assert validator.quantity_value == Decimal('100.50')
        assert validator.quantity_unit == 'kg'
    
    def test_invalid_quantity_unit(self):
        """Érvénytelen mennyiségi egység tesztelése"""
        data = self._get_valid_offer_data()
        data['quantity_unit'] = 'invalid_unit'
        
        with pytest.raises(ValidationError) as exc_info:
            OfferValidator(**data)
        
        assert 'Érvénytelen mennyiségi egység' in str(exc_info.value)
    
    def test_invalid_status(self):
        """Érvénytelen státusz tesztelése"""
        data = self._get_valid_offer_data()
        data['status'] = 'INVALID_STATUS'
        
        with pytest.raises(ValidationError) as exc_info:
            OfferValidator(**data)
        
        assert 'Érvénytelen státusz' in str(exc_info.value)
    
    def test_invalid_delivery_date(self):
        """Érvénytelen szállítási dátum tesztelése"""
        data = self._get_valid_offer_data()
        data['delivery_date'] = date(2023, 1, 1)  # 2024 előtti
        
        with pytest.raises(ValidationError) as exc_info:
            OfferValidator(**data)
        
        assert '2024 előtti' in str(exc_info.value)
    
    def test_name_cleaning(self):
        """Nevek tisztítása tesztelése"""
        data = self._get_valid_offer_data()
        data['producer_name'] = '  Test Producer  '
        data['product_name'] = '  Test Product  '
        
        validator = OfferValidator(**data)
        assert validator.producer_name == 'Test Producer'
        assert validator.product_name == 'Test Product'
    
    def test_empty_name_validation(self):
        """Üres név validáció tesztelése"""
        data = self._get_valid_offer_data()
        data['producer_name'] = '   '
        
        with pytest.raises(ValidationError) as exc_info:
            OfferValidator(**data)
        
        assert 'nem lehet üres' in str(exc_info.value)
    
    def _get_valid_offer_data(self):
        """Érvényes ajánlat adat helper"""
        return {
            'id': 1,
            'producer_id': 1,
            'producer_name': 'Test Producer',
            'product_name': 'Test Product',
            'product_category': 'Test Category',
            'quantity_value': Decimal('100.50'),
            'quantity_unit': 'kg',
            'delivery_date': date(2024, 6, 15),
            'status': 'CREATED',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

class TestActionResultValidator:
    """ActionResultValidator tesztek"""
    
    def test_valid_action_result(self):
        """Érvényes művelet eredmény tesztelése"""
        data = {
            'success': True,
            'message': 'Operation successful',
            'updated_offers': [],
            'errors': []
        }
        
        validator = ActionResultValidator(**data)
        assert validator.success is True
        assert validator.message == 'Operation successful'
    
    def test_message_cleaning(self):
        """Üzenet tisztítása tesztelése"""
        validator = ActionResultValidator(
            success=True,
            message='  Test message  '
        )
        assert validator.message == 'Test message'
    
    def test_empty_message_validation(self):
        """Üres üzenet validáció tesztelése"""
        with pytest.raises(ValidationError) as exc_info:
            ActionResultValidator(success=True, message='   ')
        
        assert 'nem lehet üres' in str(exc_info.value)
    
    def test_errors_cleaning(self):
        """Hibaüzenetek tisztítása tesztelése"""
        validator = ActionResultValidator(
            success=False,
            message='Failed',
            errors=['  Error 1  ', '', '  Error 2  ', '   ']
        )
        
        # Üres stringek kiszűrve, whitespace eltávolítva
        assert validator.errors == ['Error 1', 'Error 2']

class TestUtilityFunctions:
    """Utility függvények tesztelése"""
    
    def test_validate_api_response_data(self):
        """API válasz validáció tesztelése"""
        data = {
            'id': '1',
            'producer_name': 'Test Producer',
            'product_name': 'Test Product',
            'status': 'CREATED',
            'user_id': '2',
            'quantity_in_kg': 100.5,
            'created_at': '2024-01-01T12:00:00Z'
        }
        
        result = validate_api_response_data(data)
        
        assert result['id'] == 1
        assert result['producer_id'] == 2
        assert result['quantity_value'] == 100.5
        assert result['quantity_unit'] == 'kg'
        assert isinstance(result['created_at'], datetime)
    
    def test_validate_api_response_missing_fields(self):
        """Hiányzó mezők validáció tesztelése"""
        data = {'id': 1}  # Hiányzó kötelező mezők
        
        with pytest.raises(ValueError) as exc_info:
            validate_api_response_data(data)
        
        assert 'Hiányzó kötelező mezők' in str(exc_info.value)
    
    def test_validate_filter_params(self):
        """Szűrési paraméterek validáció tesztelése"""
        params = {
            'producer_id': 1,
            'page': 1,
            'page_size': 50
        }
        
        result = validate_filter_params(params)
        assert result['producer_id'] == 1
        assert result['page'] == 1
    
    def test_validate_offer_data(self):
        """Ajánlat adatok validáció tesztelése"""
        data = {
            'id': 1,
            'producer_name': 'Test Producer',
            'product_name': 'Test Product',
            'status': 'CREATED',
            'quantity_value': 100,
            'quantity_unit': 'kg',
            'delivery_date': '2024-06-15',
            'created_at': '2024-01-01T12:00:00Z',
            'updated_at': '2024-01-01T12:00:00Z',
            'producer_id': 1,
            'product_category': 'Test Category'
        }
        
        result = validate_offer_data(data)
        assert result['id'] == 1
        assert result['producer_name'] == 'Test Producer'
    
    def test_validate_offers_batch(self):
        """Batch validáció tesztelése"""
        offers_data = [
            {
                'id': 1,
                'producer_name': 'Producer 1',
                'product_name': 'Product 1',
                'status': 'CREATED',
                'quantity_value': 100,
                'quantity_unit': 'kg',
                'delivery_date': '2024-06-15',
                'created_at': '2024-01-01T12:00:00Z',
                'updated_at': '2024-01-01T12:00:00Z',
                'producer_id': 1,
                'product_category': 'Category 1'
            },
            {
                'id': 2,
                # Hiányzó mezők - ezt ki kell szűrni
            }
        ]
        
        result = validate_offers_batch(offers_data)
        
        # Csak az érvényes ajánlat marad
        assert len(result) == 1
        assert result[0]['id'] == 1

class TestValidationResult:
    """ValidationResult tesztelése"""
    
    def test_success_result(self):
        """Sikeres eredmény tesztelése"""
        result = ValidationResult.success_result(
            data={'test': 'data'},
            warnings=['Warning message']
        )
        
        assert result.success is True
        assert result.data == {'test': 'data'}
        assert result.warnings == ['Warning message']
        assert result.errors == []
    
    def test_error_result(self):
        """Hibás eredmény tesztelése"""
        result = ValidationResult.error_result(
            errors=['Error 1', 'Error 2'],
            data={'partial': 'data'}
        )
        
        assert result.success is False
        assert result.errors == ['Error 1', 'Error 2']
        assert result.data == {'partial': 'data'}
        assert result.warnings == []

# Pytest fixtures
@pytest.fixture
def valid_filter_data():
    """Érvényes szűrési adatok fixture"""
    return {
        'producer_id': 1,
        'date_from': date(2024, 1, 1),
        'date_to': date(2024, 12, 31),
        'status_filters': ['CREATED'],
        'page': 1,
        'page_size': 50
    }

@pytest.fixture
def valid_offer_data():
    """Érvényes ajánlat adatok fixture"""
    return {
        'id': 1,
        'producer_id': 1,
        'producer_name': 'Test Producer',
        'product_name': 'Test Product',
        'product_category': 'Test Category',
        'quantity_value': Decimal('100.50'),
        'quantity_unit': 'kg',
        'delivery_date': date(2024, 6, 15),
        'status': 'CREATED',
        'created_at': datetime.now(),
        'updated_at': datetime.now()
    }

@pytest.fixture
def valid_api_response_data():
    """Érvényes API válasz adatok fixture"""
    return {
        'id': 1,
        'producer_name': 'Test Producer',
        'product_name': 'Test Product',
        'status': 'CREATED',
        'user_id': 2,
        'quantity_in_kg': 100.5,
        'created_at': '2024-01-01T12:00:00Z',
        'updated_at': '2024-01-01T12:00:00Z',
        'delivery_date': '2024-06-15',
        'product_category': 'Test Category'
    }