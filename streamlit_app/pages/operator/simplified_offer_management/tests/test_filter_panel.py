"""
Test Filter Panel Component
<PERSON><PERSON><PERSON><PERSON><PERSON> panel komponens tesztelése
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime
import sys
import os
import time

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.data_models import FilterState
from core.base import ComponentResult

class TestFilterPanel(unittest.TestCase):
    """Filter Panel tesztek"""
    
    def setUp(self):
        """Test setup"""
        # Mock streamlit session state
        self.mock_session_state = {
            'simplified_filter_panel': {
                'expanded_sections': {
                    'basic': True,
                    'advanced': False,
                    'saved': False
                },
                'mobile_mode': False,
                'last_render': None,
                'producers_cache': None,
                'product_types_cache': None
            }
        }
    
    @patch('streamlit.session_state', new_callable=lambda: MagicMock())
    def test_filter_panel_initialization(self, mock_st):
        """Test filter panel initialization"""
        # Mock session state
        mock_st.__contains__ = lambda key: key in self.mock_session_state
        mock_st.__getitem__ = lambda key: self.mock_session_state[key]
        mock_st.__setitem__ = lambda key, value: self.mock_session_state.update({key: value})
        
        # This would normally import and test FilterPanel
        # But since we can't import streamlit in tests, we'll test the logic
        
        # Test default filter state
        default_filters = {
            'producer_id': None,
            'date_from': None,
            'date_to': None,
            'status_filters': [],
            'product_category_id': None,
            'search_term': None,
            'sort_column': 'created_at',
            'sort_direction': 'desc',
            'page': 1,
            'page_size': 50
        }
        
        filter_state = FilterState(**default_filters)
        
        # Verify default state
        self.assertIsNone(filter_state.producer_id)
        self.assertIsNone(filter_state.date_from)
        self.assertIsNone(filter_state.date_to)
        self.assertEqual(filter_state.status_filters, [])
        self.assertEqual(filter_state.sort_column, 'created_at')
        self.assertEqual(filter_state.sort_direction, 'desc')
        self.assertEqual(filter_state.page, 1)
        self.assertEqual(filter_state.page_size, 50)
    
    def test_filter_state_validation(self):
        """Test filter state validation"""
        # Test valid filter state
        valid_filters = FilterState(
            producer_id=1,
            date_from=date.today(),
            date_to=date.today(),
            status_filters=['CREATED'],
            search_term='test'
        )
        
        self.assertEqual(valid_filters.producer_id, 1)
        self.assertEqual(valid_filters.status_filters, ['CREATED'])
        self.assertEqual(valid_filters.search_term, 'test')
    
    def test_filter_api_params_conversion(self):
        """Test conversion to API parameters"""
        filter_state = FilterState(
            producer_id=123,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 1, 31),
            status_filters=['CREATED', 'CONFIRMED_BY_COMPANY'],
            product_category_id=456,
            search_term='apple',
            page=2,
            page_size=25,
            sort_column='delivery_date',
            sort_direction='asc'
        )
        
        api_params = filter_state.to_api_params()
        
        # Verify API parameter conversion
        self.assertEqual(api_params['user_id'], 123)
        self.assertEqual(api_params['date_from'], '2024-01-01')
        self.assertEqual(api_params['date_to'], '2024-01-31')
        self.assertEqual(api_params['statuses'], ['CREATED', 'CONFIRMED_BY_COMPANY'])
        self.assertEqual(api_params['product_type_id'], 456)
        self.assertEqual(api_params['search'], 'apple')
        self.assertEqual(api_params['page'], 2)
        self.assertEqual(api_params['limit'], 25)
        self.assertEqual(api_params['sort_by'], 'delivery_date')
        self.assertEqual(api_params['sort_order'], 'asc')
    
    def test_filter_state_empty_check(self):
        """Test empty filter state detection"""
        # Empty filter state
        empty_filters = FilterState()
        self.assertTrue(empty_filters.is_empty())
        
        # Non-empty filter state
        non_empty_filters = FilterState(producer_id=1)
        self.assertFalse(non_empty_filters.is_empty())
        
        # Filter with only whitespace search term should be empty
        whitespace_filters = FilterState(search_term="   ")
        self.assertTrue(whitespace_filters.is_empty())
    
    def test_filter_summary(self):
        """Test filter summary generation"""
        # Empty filters
        empty_filters = FilterState()
        self.assertEqual(empty_filters.get_summary(), "Nincs aktív szűrő")
        
        # Filters with values
        filters_with_values = FilterState(
            producer_id=123,
            status_filters=['CREATED'],
            date_from=date(2024, 1, 1),
            date_to=date(2024, 1, 31),
            search_term='apple'
        )
        
        summary = filters_with_values.get_summary()
        self.assertIn("Termelő: 123", summary)
        self.assertIn("Státusz: CREATED", summary)
        self.assertIn("Időszak: 2024-01-01 - 2024-01-31", summary)
        self.assertIn("Keresés: apple", summary)
    
    def test_debounce_logic(self):
        """Test debounce timing logic"""
        import time
        
        # Simulate debounce timing
        current_time = time.time()
        
        # Recent timestamp - should not apply
        recent_debounce = {
            'term': 'test',
            'timestamp': current_time - 0.1  # 100ms ago
        }
        
        elapsed = (current_time - recent_debounce['timestamp']) * 1000
        should_apply_recent = elapsed >= 500
        self.assertFalse(should_apply_recent)
        
        # Old timestamp - should apply
        old_debounce = {
            'term': 'test',
            'timestamp': current_time - 0.6  # 600ms ago
        }
        
        elapsed = (current_time - old_debounce['timestamp']) * 1000
        should_apply_old = elapsed >= 500
        self.assertTrue(should_apply_old)
    
    def test_mobile_mode_detection(self):
        """Test mobile mode detection logic"""
        # This is a simple test for the mobile detection logic
        # In a real implementation, this would test viewport width detection
        
        panel_state = {
            'mobile_mode': False
        }
        
        # Simulate desktop mode
        panel_state['mobile_mode'] = False
        self.assertFalse(panel_state['mobile_mode'])
        
        # Simulate mobile mode
        panel_state['mobile_mode'] = True
        self.assertTrue(panel_state['mobile_mode'])
    
    def test_hierarchical_category_organization(self):
        """Test hierarchical category organization"""
        # Mock product types with parent-child relationships
        product_types = [
            {"id": 1, "name": "Zöldség", "parent_id": None},
            {"id": 2, "name": "Gyümölcs", "parent_id": None},
            {"id": 3, "name": "Paradicsom", "parent_id": 1},
            {"id": 4, "name": "Paprika", "parent_id": 1},
            {"id": 5, "name": "Alma", "parent_id": 2},
            {"id": 6, "name": "Körte", "parent_id": 2},
            {"id": 7, "name": "Koktélparadicsom", "parent_id": 3}
        ]
        
        # Create a mock FilterPanel instance with _organize_categories_hierarchically method
        class MockFilterPanel:
            def _organize_categories_hierarchically(self, product_types):
                categories_by_parent = {}
                
                for category in product_types:
                    parent_id = category.get("parent_id")
                    
                    if parent_id not in categories_by_parent:
                        categories_by_parent[parent_id] = []
                        
                    categories_by_parent[parent_id].append(category)
                
                # Rendezés név szerint minden kategória csoportban
                for parent_id, categories in categories_by_parent.items():
                    categories_by_parent[parent_id] = sorted(categories, key=lambda x: x["name"])
                    
                return categories_by_parent
        
        mock_panel = MockFilterPanel()
        result = mock_panel._organize_categories_hierarchically(product_types)
        
        # Verify root categories
        self.assertEqual(len(result[None]), 2)
        self.assertEqual(result[None][0]["name"], "Gyümölcs")
        self.assertEqual(result[None][1]["name"], "Zöldség")
        
        # Verify subcategories
        self.assertEqual(len(result[1]), 2)
        self.assertEqual(result[1][0]["name"], "Paprika")
        self.assertEqual(result[1][1]["name"], "Paradicsom")
        
        self.assertEqual(len(result[2]), 2)
        self.assertEqual(result[2][0]["name"], "Alma")
        self.assertEqual(result[2][1]["name"], "Körte")
        
        # Verify nested subcategories
        self.assertEqual(len(result[3]), 1)
        self.assertEqual(result[3][0]["name"], "Koktélparadicsom")
    
    def test_debounced_filtering(self):
        """Test debounced filtering logic"""
        # Mock panel state
        panel_state = {
            'search_debounce': {
                'term': 'test search',
                'timestamp': time.time() - 0.1,  # 100ms ago
                'filter_key': 'search_term'
            }
        }
        
        # Create a mock FilterPanel instance with _should_apply_debounced_filter method
        class MockFilterPanel:
            def __init__(self):
                self.debounce_delay = 500
                
            def _should_apply_debounced_filter(self, debounce_key):
                debounce_info = panel_state.get(debounce_key)
                
                if not debounce_info:
                    return False
                
                # 500ms eltelt-e
                elapsed = (time.time() - debounce_info['timestamp']) * 1000
                return elapsed >= self.debounce_delay
        
        mock_panel = MockFilterPanel()
        
        # Test with recent timestamp (should not apply)
        self.assertFalse(mock_panel._should_apply_debounced_filter('search_debounce'))
        
        # Update timestamp to be older than debounce delay
        panel_state['search_debounce']['timestamp'] = time.time() - 0.6  # 600ms ago
        
        # Test with old timestamp (should apply)
        self.assertTrue(mock_panel._should_apply_debounced_filter('search_debounce'))

if __name__ == '__main__':
    unittest.main()