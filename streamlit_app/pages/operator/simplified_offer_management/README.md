# Egyszerűsített Ajánlatkezelő Rendszer

Ez a projekt egy egyszerűsített ajánlatkezelő rendszert implementál Streamlit alapon, amely lehetővé teszi a mezőgazdasági ajánlatok hatékony kezelését.

## Komponensek

- **Filter Panel**: Szűrési felület az ajánlatok szűréséhez
- **Filter Manager**: Szűrési állapot kezelése
- **Data Manager**: Adatok betöltése és kezelése
- **API Client**: Backend API kommunikáció
- **Cache Manager**: Intelligens cache-elés

## Demó Futtatása

### Lokális <PERSON>

A szűrőpanel demó alkalmazás futtatásához:

```bash
cd streamlit_app
streamlit run pages/operator/simplified_offer_management/demo_filter_panel.py
```

### Docker Környezetben

Docker és Docker Compose használatával:

```bash
cd streamlit_app
docker-compose up
```

Ezután nyisd meg a böngészőben: http://localhost:8501

## Főbb Funkciók

- Termelő szerinti szűrés (keresési funkcióval)
- Dátum szerinti szűrés
- Státusz szerinti szűrés
- Termék kategória hierarchikus szűrés
- Rendezési és lapozási beállítások
- Mentett szűrők kezelése

## Fejlesztés

A projekt moduláris felépítésű, tiszta architektúrával. A komponensek egymástól függetlenül fejleszthetők és tesztelhetők.

### Tesztek Futtatása

```bash
cd streamlit_app
python -m unittest discover pages/operator/simplified_offer_management/tests
```

### Docker Build

Egyedi Docker image építése:

```bash
cd streamlit_app
docker build -t simplified-offer-management:latest .
```

## Követelmények

- Python 3.8+
- Streamlit 1.10+
- Pydantic 2.0+

### Docker Környezethez
- Docker
- Docker Compose