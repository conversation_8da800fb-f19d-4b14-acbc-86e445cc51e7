<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sz<PERSON>rőpanel Komponens Demó</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
        }
        .demo-image {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .feature-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
        .code-block {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: monospace;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Szűrőpanel Komponens Demó</h1>
        
        <p>
            Ez az oldal bemutatja az egyszerűsített ajánlatkezelő rendszer szűrőpanel komponensét.
            A szűrőpanel lehetővé teszi a felhasználók számára, hogy különböző kritériumok alapján szűrjék az ajánlatokat.
        </p>

        <div class="note">
            <strong>Megjegyzés:</strong> Ez egy statikus HTML demó. A valós komponens a Streamlit alkalmazásban fut.
        </div>

        <h2>Főbb funkciók</h2>
        <ul class="feature-list">
            <li><strong>Termelő szerinti szűrés</strong> - Keresési funkcióval ellátott dropdown</li>
            <li><strong>Dátum szerinti szűrés</strong> - Kezdő és záró dátum választó</li>
            <li><strong>Státusz szerinti szűrés</strong> - Többszörös választás lehetőségével</li>
            <li><strong>Termék kategória hierarchikus szűrés</strong> - Fő- és alkategóriák</li>
            <li><strong>Rendezési és lapozási beállítások</strong> - Különböző oszlopok szerint</li>
            <li><strong>Mentett szűrők kezelése</strong> - Gyakran használt szűrők mentése és betöltése</li>
            <li><strong>500ms debounced keresés</strong> - Valós idejű keresés késleltetéssel</li>
            <li><strong>Mobil-optimalizált elrendezés</strong> - Reszponzív design</li>
        </ul>

        <h2>Komponens szerkezet</h2>
        <div class="code-block">
            <pre>
FilterPanel
├── _render_header()
│   └── _render_quick_filters()
├── _render_basic_filters()
│   ├── _render_basic_filters_desktop()
│   │   ├── _render_producer_filter()
│   │   ├── _render_search_filter()
│   │   ├── _render_date_from_filter()
│   │   ├── _render_date_to_filter()
│   │   └── _render_status_filter()
│   └── _render_basic_filters_mobile()
├── _render_advanced_filters()
│   ├── _render_product_category_filter()
│   │   └── _organize_categories_hierarchically()
│   ├── _render_sort_column_filter()
│   ├── _render_sort_direction_filter()
│   └── _render_page_size_filter()
├── _render_saved_filters()
└── _render_filter_summary()
    └── _render_performance_info()
            </pre>
        </div>

        <h2>Használati példa</h2>
        <div class="code-block">
            <pre>
# Szűrőpanel komponens használata
filter_panel = get_filter_panel()
current_filters = filter_panel.render()

# Szűrési feltételek lekérése
producer_id = current_filters.producer_id
date_from = current_filters.date_from
date_to = current_filters.date_to
status_filters = current_filters.status_filters
            </pre>
        </div>

        <h2>Implementációs részletek</h2>
        <p>
            A szűrőpanel komponens a következő technológiákat használja:
        </p>
        <ul>
            <li><strong>Streamlit</strong> - UI komponensek és interaktivitás</li>
            <li><strong>Pydantic</strong> - Adatvalidáció</li>
            <li><strong>Session State</strong> - Állapotkezelés</li>
            <li><strong>Debounce</strong> - Keresési optimalizáció</li>
        </ul>

        <div class="note">
            <p><strong>Hogyan próbáld ki:</strong></p>
            <ol>
                <li>Nyisd meg a Streamlit alkalmazást: <code>http://10.0.0.121:8501/pages/operator/simplified_offer_management/app</code></li>
                <li>Vagy indítsd újra a Streamlit szervert: <code>streamlit run streamlit_app/pages/operator/simplified_offer_management/app.py</code></li>
            </ol>
        </div>
    </div>
</body>
</html>