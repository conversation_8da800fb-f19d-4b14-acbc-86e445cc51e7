"""
Data Manager
<PERSON><PERSON><PERSON><PERSON><PERSON> adatke<PERSON>ő komponens cache-<PERSON><PERSON><PERSON> és optimistic updates-szel
"""
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

from ..core.base import BaseManager, ComponentResult
from ..core.exceptions import DataError, handle_exception
from ..models.data_models import FilterState, Offer, ActionResult, offers_from_api_response
from ..models.validation import validate_filter_params, ValidationResult
from .api_client import get_api_client
from .cache_manager import get_cache_manager

logger = logging.getLogger(__name__)

class DataManager(BaseManager):
    """Központi adatkezelő komponens"""
    
    def __init__(self):
        super().__init__("DataManager")
        self.api_client = get_api_client()
        self.cache_manager = get_cache_manager()
        self._loading_states = {}
        
    def _initialize(self):
        """Data manager inicializálása"""
        # Session state inicializálása
        if 'simplified_data_manager' not in st.session_state:
            st.session_state.simplified_data_manager = {
                'loading_states': {},
                'error_messages': [],
                'last_update': datetime.now(),
                'performance_metrics': {
                    'api_calls': 0,
                    'cache_hits': 0,
                    'total_load_time': 0.0
                }
            }
        
        self.logger.info("Data manager initialized")
    
    def _set_loading_state(self, operation: str, loading: bool):
        """Loading állapot beállítása"""
        if not self.is_initialized():
            self.initialize()
        
        st.session_state.simplified_data_manager['loading_states'][operation] = loading
        self._loading_states[operation] = loading
    
    def _get_loading_state(self, operation: str) -> bool:
        """Loading állapot lekérése"""
        if not self.is_initialized():
            return False
        
        return st.session_state.simplified_data_manager['loading_states'].get(operation, False)
    
    def _add_error_message(self, error: str):
        """Hibaüzenet hozzáadása"""
        if not self.is_initialized():
            self.initialize()
        
        errors = st.session_state.simplified_data_manager['error_messages']
        errors.append({
            'message': error,
            'timestamp': datetime.now()
        })
        
        # Csak az utolsó 10 hibát tartjuk meg
        if len(errors) > 10:
            st.session_state.simplified_data_manager['error_messages'] = errors[-10:]
    
    def _update_performance_metrics(self, operation: str, duration: float, cache_hit: bool = False):
        """Performance metrikák frissítése"""
        if not self.is_initialized():
            return
        
        metrics = st.session_state.simplified_data_manager['performance_metrics']
        
        if cache_hit:
            metrics['cache_hits'] += 1
        else:
            metrics['api_calls'] += 1
        
        metrics['total_load_time'] += duration
    
    @handle_exception
    def load_offers(self, filters: FilterState) -> List[Offer]:
        """
        Ajánlatok betöltése szűrőkkel
        
        Args:
            filters: Szűrési paraméterek
            
        Returns:
            Ajánlatok listája
            
        Raises:
            DataError: Adatbetöltési hiba esetén
        """
        if not self.is_initialized():
            self.initialize()
        
        operation = "load_offers"
        self._set_loading_state(operation, True)
        
        try:
            start_time = datetime.now()
            
            # Szűrők validálása
            try:
                api_params = filters.to_api_params()
                validated_params = validate_filter_params(api_params)
            except Exception as e:
                raise DataError(f"Invalid filter parameters: {e}", "load_offers", "FilterState")
            
            # API hívás
            result = self.api_client.get_offers(validated_params)
            
            if not result.success:
                error_msg = f"Failed to load offers: {result.error}"
                self._add_error_message(error_msg)
                raise DataError(error_msg, "load_offers", "API")
            
            # Adatok konvertálása Offer objektumokká
            try:
                offers = offers_from_api_response(result.data)
                
                duration = (datetime.now() - start_time).total_seconds()
                self._update_performance_metrics(operation, duration)
                
                self.logger.info(f"Successfully loaded {len(offers)} offers in {duration:.3f}s")
                return offers
                
            except Exception as e:
                error_msg = f"Failed to convert API response to offers: {e}"
                self._add_error_message(error_msg)
                raise DataError(error_msg, "load_offers", "Conversion")
        
        except DataError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error loading offers: {e}"
            self._add_error_message(error_msg)
            raise DataError(error_msg, "load_offers", "System")
        
        finally:
            self._set_loading_state(operation, False)
    
    @handle_exception
    def get_offer_details(self, offer_id: int) -> Offer:
        """
        Ajánlat részletek lekérése
        
        Args:
            offer_id: Ajánlat azonosító
            
        Returns:
            Offer objektum
            
        Raises:
            DataError: Adatbetöltési hiba esetén
        """
        if not self.is_initialized():
            self.initialize()
        
        operation = f"get_offer_{offer_id}"
        self._set_loading_state(operation, True)
        
        try:
            start_time = datetime.now()
            
            # API hívás
            result = self.api_client.get_offer(offer_id)
            
            if not result.success:
                error_msg = f"Failed to load offer {offer_id}: {result.error}"
                self._add_error_message(error_msg)
                raise DataError(error_msg, "get_offer_details", "API")
            
            # Offer objektum létrehozása
            try:
                offer = Offer.from_api_response(result.data)
                
                duration = (datetime.now() - start_time).total_seconds()
                self._update_performance_metrics(operation, duration)
                
                self.logger.info(f"Successfully loaded offer {offer_id} in {duration:.3f}s")
                return offer
                
            except Exception as e:
                error_msg = f"Failed to convert API response to offer: {e}"
                self._add_error_message(error_msg)
                raise DataError(error_msg, "get_offer_details", "Conversion")
        
        except DataError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error loading offer {offer_id}: {e}"
            self._add_error_message(error_msg)
            raise DataError(error_msg, "get_offer_details", "System")
        
        finally:
            self._set_loading_state(operation, False)
    
    @handle_exception
    def update_offer(self, offer_id: int, updates: Dict[str, Any]) -> ActionResult:
        """
        Ajánlat frissítése optimistic updates-szel
        
        Args:
            offer_id: Ajánlat azonosító
            updates: Frissítendő mezők
            
        Returns:
            ActionResult
        """
        if not self.is_initialized():
            self.initialize()
        
        operation = f"update_offer_{offer_id}"
        self._set_loading_state(operation, True)
        
        try:
            start_time = datetime.now()
            
            # Optimistic update - cache frissítése azonnal
            self._apply_optimistic_update(offer_id, updates)
            
            # API hívás
            result = self.api_client.update_offer(offer_id, updates)
            
            if result.success:
                # Sikeres frissítés - cache invalidálása és újratöltése
                self.cache_manager.invalidate("offers")
                
                duration = (datetime.now() - start_time).total_seconds()
                self._update_performance_metrics(operation, duration)
                
                self.logger.info(f"Successfully updated offer {offer_id} in {duration:.3f}s")
                
                return ActionResult.success_result(
                    message=f"Ajánlat {offer_id} sikeresen frissítve",
                    updated_offers=[Offer.from_api_response(result.data)] if result.data else []
                )
            else:
                # Sikertelen frissítés - optimistic update visszavonása
                self._revert_optimistic_update(offer_id)
                
                error_msg = f"Failed to update offer {offer_id}: {result.error}"
                self._add_error_message(error_msg)
                
                return ActionResult.error_result(
                    message=f"Ajánlat {offer_id} frissítése sikertelen",
                    errors=[error_msg]
                )
        
        except Exception as e:
            # Hiba esetén optimistic update visszavonása
            self._revert_optimistic_update(offer_id)
            
            error_msg = f"Unexpected error updating offer {offer_id}: {e}"
            self._add_error_message(error_msg)
            
            return ActionResult.error_result(
                message=f"Ajánlat {offer_id} frissítése sikertelen",
                errors=[error_msg]
            )
        
        finally:
            self._set_loading_state(operation, False)
    
    @handle_exception
    def update_offer_status(self, offer_id: int, status: str, 
                           confirmation_data: Dict[str, Any] = None) -> ActionResult:
        """
        Ajánlat státusz frissítése
        
        Args:
            offer_id: Ajánlat azonosító
            status: Új státusz
            confirmation_data: Visszaigazolási adatok
            
        Returns:
            ActionResult
        """
        if not self.is_initialized():
            self.initialize()
        
        operation = f"update_status_{offer_id}"
        self._set_loading_state(operation, True)
        
        try:
            start_time = datetime.now()
            
            # API hívás
            result = self.api_client.update_offer_status(offer_id, status, confirmation_data)
            
            if result.success:
                # Cache invalidálása
                self.cache_manager.invalidate("offers")
                self.cache_manager.invalidate("offer", str(offer_id))
                
                duration = (datetime.now() - start_time).total_seconds()
                self._update_performance_metrics(operation, duration)
                
                self.logger.info(f"Successfully updated offer {offer_id} status to {status} in {duration:.3f}s")
                
                return ActionResult.success_result(
                    message=f"Ajánlat {offer_id} státusza frissítve: {status}",
                    updated_offers=[Offer.from_api_response(result.data)] if result.data else []
                )
            else:
                error_msg = f"Failed to update offer {offer_id} status: {result.error}"
                self._add_error_message(error_msg)
                
                return ActionResult.error_result(
                    message=f"Ajánlat {offer_id} státusz frissítése sikertelen",
                    errors=[error_msg]
                )
        
        except Exception as e:
            error_msg = f"Unexpected error updating offer {offer_id} status: {e}"
            self._add_error_message(error_msg)
            
            return ActionResult.error_result(
                message=f"Ajánlat {offer_id} státusz frissítése sikertelen",
                errors=[error_msg]
            )
        
        finally:
            self._set_loading_state(operation, False)
    
    def _apply_optimistic_update(self, offer_id: int, updates: Dict[str, Any]):
        """
        Optimistic update alkalmazása cache-ben
        
        Args:
            offer_id: Ajánlat azonosító
            updates: Frissítések
        """
        try:
            # Jelenlegi ajánlat lekérése cache-ből
            cached_offer = self.cache_manager.get("offer", str(offer_id))
            
            if cached_offer:
                # Frissítések alkalmazása
                updated_offer = cached_offer.copy()
                updated_offer.update(updates)
                updated_offer['updated_at'] = datetime.now().isoformat()
                
                # Cache frissítése
                self.cache_manager.set("offer", str(offer_id), updated_offer, ttl_seconds=60)
                
                self.logger.debug(f"Applied optimistic update to offer {offer_id}")
        
        except Exception as e:
            self.logger.warning(f"Failed to apply optimistic update: {e}")
    
    def _revert_optimistic_update(self, offer_id: int):
        """
        Optimistic update visszavonása
        
        Args:
            offer_id: Ajánlat azonosító
        """
        try:
            # Cache invalidálása - eredeti adat újratöltése
            self.cache_manager.invalidate("offer", str(offer_id))
            self.logger.debug(f"Reverted optimistic update for offer {offer_id}")
        
        except Exception as e:
            self.logger.warning(f"Failed to revert optimistic update: {e}")
    
    def get_loading_state(self, operation: str = None) -> Dict[str, bool]:
        """
        Loading állapotok lekérése
        
        Args:
            operation: Specifikus művelet (None = összes)
            
        Returns:
            Loading állapotok dictionary
        """
        if not self.is_initialized():
            return {}
        
        loading_states = st.session_state.simplified_data_manager['loading_states']
        
        if operation:
            return {operation: loading_states.get(operation, False)}
        
        return loading_states.copy()
    
    def get_error_messages(self) -> List[Dict[str, Any]]:
        """
        Hibaüzenetek lekérése
        
        Returns:
            Hibaüzenetek listája
        """
        if not self.is_initialized():
            return []
        
        return st.session_state.simplified_data_manager['error_messages'].copy()
    
    def clear_error_messages(self):
        """Hibaüzenetek törlése"""
        if not self.is_initialized():
            self.initialize()
        
        st.session_state.simplified_data_manager['error_messages'] = []
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Performance metrikák lekérése
        
        Returns:
            Performance metrikák
        """
        if not self.is_initialized():
            return {
                'api_calls': 0,
                'cache_hits': 0,
                'total_load_time': 0.0,
                'average_load_time': 0.0
            }
        
        metrics = st.session_state.simplified_data_manager['performance_metrics'].copy()
        
        # Átlagos betöltési idő számítása
        total_calls = metrics['api_calls'] + metrics['cache_hits']
        if total_calls > 0:
            metrics['average_load_time'] = metrics['total_load_time'] / total_calls
        else:
            metrics['average_load_time'] = 0.0
        
        return metrics
    
    def invalidate_cache(self, namespace: str = None) -> ComponentResult:
        """
        Cache invalidálása
        
        Args:
            namespace: Cache névtér (None = összes)
            
        Returns:
            ComponentResult
        """
        return self.api_client.invalidate_cache(namespace)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Cache statisztikák lekérése
        
        Returns:
            Cache statisztikák
        """
        return self.api_client.get_cache_stats()

# Singleton instance
_data_manager_instance = None

def get_data_manager() -> DataManager:
    """
    Data manager singleton lekérése
    
    Returns:
        DataManager instance
    """
    global _data_manager_instance
    
    if _data_manager_instance is None:
        _data_manager_instance = DataManager()
        try:
            _data_manager_instance.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize data manager: {e}")
    
    return _data_manager_instance

# Convenience functions
def load_offers_with_filters(filters: FilterState) -> List[Offer]:
    """
    Convenience function ajánlatok betöltéshez
    
    Args:
        filters: Szűrési paraméterek
        
    Returns:
        Ajánlatok listája
        
    Raises:
        DataError: Betöltési hiba esetén
    """
    return get_data_manager().load_offers(filters)

def get_offer_by_id(offer_id: int) -> Offer:
    """
    Convenience function ajánlat lekéréshez
    
    Args:
        offer_id: Ajánlat azonosító
        
    Returns:
        Offer objektum
        
    Raises:
        DataError: Betöltési hiba esetén
    """
    return get_data_manager().get_offer_details(offer_id)

def update_offer_data(offer_id: int, updates: Dict[str, Any]) -> ActionResult:
    """
    Convenience function ajánlat frissítéshez
    
    Args:
        offer_id: Ajánlat azonosító
        updates: Frissítések
        
    Returns:
        ActionResult
    """
    return get_data_manager().update_offer(offer_id, updates)