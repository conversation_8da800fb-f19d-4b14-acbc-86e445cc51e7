"""
API Client
Egyszerűsített API kliens cache-eléssel és hibakezel<PERSON>
"""
import time
from typing import Dict, List, Any, Optional, Tuple, Union
import logging
from datetime import datetime

from ..core.base import BaseManager, ComponentResult
from ..core.exceptions import APIError, handle_exception
from ..models.validation import validate_api_response_data, validate_offers_batch
from .cache_manager import get_cache_manager

# Meglévő API client importálása - rugalmas import
try:
    from pages.operator.offer_management.api_client import (
        get_offers as legacy_get_offers,
        get_offer as legacy_get_offer,
        update_offer as legacy_update_offer,
        update_offer_status as legacy_update_offer_status,
        get_producers as legacy_get_producers,
        get_product_types as legacy_get_product_types
    )
    LEGACY_API_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("Legacy API client imported successfully")
except ImportError as e:
    LEGACY_API_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"Legacy API client not available: {e}")

class APIClient(BaseManager):
    """Egyszerűsített API kliens cache-eléssel"""
    
    def __init__(self):
        super().__init__("APIClient")
        self.cache_manager = get_cache_manager()
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        self.timeout = 30  # seconds
        
    def _initialize(self):
        """API kliens inicializálása"""
        if not LEGACY_API_AVAILABLE:
            raise APIError("Legacy API client not available", component="APIClient")
        
        self.logger.info("API client initialized")
    
    @handle_exception
    def get_offers(self, filters: Dict[str, Any] = None) -> ComponentResult:
        """
        Ajánlatok lekérése cache-eléssel
        
        Args:
            filters: Szűrési paraméterek
            
        Returns:
            ComponentResult ajánlatok listájával
        """
        if not self.is_initialized():
            self.initialize()
        
        # Cache kulcs generálása
        cache_key = "offers"
        cache_params = filters or {}
        
        # Cache ellenőrzése
        cached_offers = self.cache_manager.get("offers", cache_key, cache_params, ttl_seconds=300)
        if cached_offers is not None:
            self.logger.debug(f"Cache hit for offers with filters: {filters}")
            return ComponentResult(success=True, data=cached_offers)
        
        # API hívás retry logikával
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"API call attempt {attempt + 1} for offers with filters: {filters}")
                start_time = time.time()
                
                # Legacy API hívás
                success, result = legacy_get_offers(filters)
                
                call_duration = time.time() - start_time
                self.logger.info(f"API call completed in {call_duration:.3f}s, success: {success}")
                
                if success and isinstance(result, list):
                    # Validáció és cache-elés
                    try:
                        validated_offers = validate_offers_batch(result)
                        
                        # Cache-elés
                        self.cache_manager.set("offers", cache_key, validated_offers, cache_params)
                        
                        self.logger.info(f"Successfully loaded {len(validated_offers)} offers")
                        return ComponentResult(success=True, data=validated_offers)
                        
                    except Exception as validation_error:
                        self.logger.warning(f"Validation failed, using raw data: {validation_error}")
                        # Cache-eljük a nyers adatokat is
                        self.cache_manager.set("offers", cache_key, result, cache_params)
                        return ComponentResult(success=True, data=result)
                
                elif not success:
                    error_msg = f"API error: {result}"
                    self.logger.error(error_msg)
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay * (attempt + 1))
                        continue
                    
                    return ComponentResult(success=False, error=error_msg)
                
                else:
                    error_msg = f"Unexpected API response type: {type(result)}"
                    self.logger.error(error_msg)
                    return ComponentResult(success=False, error=error_msg)
                    
            except Exception as e:
                error_msg = f"API call failed: {str(e)}"
                self.logger.error(error_msg)
                
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                
                return ComponentResult(success=False, error=error_msg)
        
        return ComponentResult(success=False, error="Max retries exceeded")
    
    @handle_exception
    def get_offer(self, offer_id: int) -> ComponentResult:
        """
        Egyedi ajánlat lekérése
        
        Args:
            offer_id: Ajánlat azonosító
            
        Returns:
            ComponentResult ajánlat adatokkal
        """
        if not self.is_initialized():
            self.initialize()
        
        # Cache ellenőrzése
        cached_offer = self.cache_manager.get("offer", str(offer_id), ttl_seconds=180)
        if cached_offer is not None:
            self.logger.debug(f"Cache hit for offer: {offer_id}")
            return ComponentResult(success=True, data=cached_offer)
        
        try:
            self.logger.info(f"Loading offer: {offer_id}")
            success, result = legacy_get_offer(offer_id)
            
            if success:
                # Validáció
                try:
                    validated_offer = validate_api_response_data(result)
                    # Cache-elés
                    self.cache_manager.set("offer", str(offer_id), validated_offer)
                    return ComponentResult(success=True, data=validated_offer)
                except Exception as validation_error:
                    self.logger.warning(f"Offer validation failed: {validation_error}")
                    return ComponentResult(success=True, data=result)
            else:
                return ComponentResult(success=False, error=f"Failed to load offer: {result}")
                
        except Exception as e:
            error_msg = f"Error loading offer {offer_id}: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def update_offer(self, offer_id: int, data: Dict[str, Any]) -> ComponentResult:
        """
        Ajánlat frissítése
        
        Args:
            offer_id: Ajánlat azonosító
            data: Frissítendő adatok
            
        Returns:
            ComponentResult frissített ajánlattal
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            self.logger.info(f"Updating offer {offer_id} with data: {data}")
            success, result = legacy_update_offer(offer_id, data)
            
            if success:
                # Cache invalidálása
                self.cache_manager.invalidate("offer", str(offer_id))
                self.cache_manager.invalidate("offers")  # Teljes offers cache invalidálása
                
                return ComponentResult(success=True, data=result)
            else:
                return ComponentResult(success=False, error=f"Failed to update offer: {result}")
                
        except Exception as e:
            error_msg = f"Error updating offer {offer_id}: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def update_offer_status(self, offer_id: int, status: str, 
                           confirmation_data: Dict[str, Any] = None) -> ComponentResult:
        """
        Ajánlat státusz frissítése
        
        Args:
            offer_id: Ajánlat azonosító
            status: Új státusz
            confirmation_data: Visszaigazolási adatok
            
        Returns:
            ComponentResult frissített ajánlattal
        """
        if not self.is_initialized():
            self.initialize()
        
        try:
            self.logger.info(f"Updating offer {offer_id} status to: {status}")
            success, result = legacy_update_offer_status(offer_id, status, confirmation_data)
            
            if success:
                # Cache invalidálása
                self.cache_manager.invalidate("offer", str(offer_id))
                self.cache_manager.invalidate("offers")
                
                return ComponentResult(success=True, data=result)
            else:
                return ComponentResult(success=False, error=f"Failed to update offer status: {result}")
                
        except Exception as e:
            error_msg = f"Error updating offer status {offer_id}: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def get_producers(self) -> ComponentResult:
        """
        Termelők lekérése cache-eléssel
        
        Returns:
            ComponentResult termelők listájával
        """
        if not self.is_initialized():
            self.initialize()
        
        # Cache ellenőrzése - hosszabb TTL termelőknek
        cached_producers = self.cache_manager.get("producers", "all", ttl_seconds=600)
        if cached_producers is not None:
            self.logger.debug("Cache hit for producers")
            return ComponentResult(success=True, data=cached_producers)
        
        try:
            self.logger.info("Loading producers")
            success, result = legacy_get_producers()
            
            if success and isinstance(result, list):
                # Cache-elés
                self.cache_manager.set("producers", "all", result)
                return ComponentResult(success=True, data=result)
            else:
                return ComponentResult(success=False, error=f"Failed to load producers: {result}")
                
        except Exception as e:
            error_msg = f"Error loading producers: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    @handle_exception
    def get_product_types(self) -> ComponentResult:
        """
        Terméktípusok lekérése cache-eléssel
        
        Returns:
            ComponentResult terméktípusok listájával
        """
        if not self.is_initialized():
            self.initialize()
        
        # Cache ellenőrzése - hosszabb TTL terméktípusoknak
        cached_products = self.cache_manager.get("product_types", "all", ttl_seconds=600)
        if cached_products is not None:
            self.logger.debug("Cache hit for product types")
            return ComponentResult(success=True, data=cached_products)
        
        try:
            self.logger.info("Loading product types")
            success, result = legacy_get_product_types()
            
            if success and isinstance(result, list):
                # Cache-elés
                self.cache_manager.set("product_types", "all", result)
                return ComponentResult(success=True, data=result)
            else:
                return ComponentResult(success=False, error=f"Failed to load product types: {result}")
                
        except Exception as e:
            error_msg = f"Error loading product types: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def invalidate_cache(self, namespace: str = None) -> ComponentResult:
        """
        Cache invalidálása
        
        Args:
            namespace: Specifikus névtér (None = összes)
            
        Returns:
            ComponentResult
        """
        try:
            if namespace:
                result = self.cache_manager.invalidate(namespace)
            else:
                result = self.cache_manager.clear_all()
            
            self.logger.info(f"Cache invalidated: {namespace or 'all'}")
            return result
            
        except Exception as e:
            error_msg = f"Error invalidating cache: {str(e)}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Cache statisztikák lekérése
        
        Returns:
            Cache statisztikák
        """
        return self.cache_manager.get_stats()

# Singleton instance
_api_client_instance = None

def get_api_client() -> APIClient:
    """
    API client singleton lekérése
    
    Returns:
        APIClient instance
    """
    global _api_client_instance
    
    if _api_client_instance is None:
        _api_client_instance = APIClient()
        try:
            _api_client_instance.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize API client: {e}")
    
    return _api_client_instance

# Convenience functions
def api_get_offers(filters: Dict[str, Any] = None) -> Tuple[bool, Union[List[Dict], str]]:
    """
    Convenience function ajánlatok lekéréshez
    
    Returns:
        (success, data_or_error) tuple
    """
    result = get_api_client().get_offers(filters)
    return result.success, result.data if result.success else result.error

def api_get_offer(offer_id: int) -> Tuple[bool, Union[Dict, str]]:
    """
    Convenience function ajánlat lekéréshez
    
    Returns:
        (success, data_or_error) tuple
    """
    result = get_api_client().get_offer(offer_id)
    return result.success, result.data if result.success else result.error

def api_update_offer(offer_id: int, data: Dict[str, Any]) -> Tuple[bool, Union[Dict, str]]:
    """
    Convenience function ajánlat frissítéshez
    
    Returns:
        (success, data_or_error) tuple
    """
    result = get_api_client().update_offer(offer_id, data)
    return result.success, result.data if result.success else result.error

def api_get_producers() -> Tuple[bool, Union[List[Dict], str]]:
    """
    Convenience function termelők lekéréshez
    
    Returns:
        (success, data_or_error) tuple
    """
    result = get_api_client().get_producers()
    return result.success, result.data if result.success else result.error