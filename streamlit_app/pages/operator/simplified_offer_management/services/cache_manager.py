"""
Cache Manager
Intelligens cache kezelés session state alapon
"""
import streamlit as st
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import hashlib
import json
import logging
from ..core.base import BaseManager, ComponentResult
from ..core.exceptions import ComponentError

logger = logging.getLogger(__name__)

class CacheManager(BaseManager):
    """Session-level cache manager Streamlit session state alapon"""
    
    def __init__(self):
        super().__init__("CacheManager")
        self.default_ttl = 300  # 5 perc
        self.max_cache_size = 100  # Maximum cache entries
        
    def _initialize(self):
        """Cache inicializálása session state-ben"""
        if 'simplified_cache' not in st.session_state:
            st.session_state.simplified_cache = {
                'data': {},
                'timestamps': {},
                'access_count': {},
                'created_at': datetime.now()
            }
        
        self.logger.info("Cache manager initialized")
    
    def _get_cache_key(self, namespace: str, key: str, params: Dict[str, Any] = None) -> str:
        """
        Cache kulcs generálása
        
        Args:
            namespace: <PERSON><PERSON> n<PERSON> (pl. 'offers', 'producers')
            key: <PERSON>ap kulcs
            params: Paraméterek hash-eléshez
            
        Returns:
            Egyedi cache kulcs
        """
        key_parts = [namespace, key]
        
        if params:
            # Paraméterek determinisztikus hash-elése
            params_str = json.dumps(params, sort_keys=True, default=str)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            key_parts.append(params_hash)
        
        return "|".join(key_parts)
    
    def get(self, namespace: str, key: str, params: Dict[str, Any] = None, 
            ttl_seconds: int = None) -> Optional[Any]:
        """
        Érték lekérése cache-ből
        
        Args:
            namespace: Cache névtér
            key: Cache kulcs
            params: Paraméterek
            ttl_seconds: TTL másodpercekben (None = default)
            
        Returns:
            Cache-elt érték vagy None
        """
        if not self.is_initialized():
            self.initialize()
        
        cache_key = self._get_cache_key(namespace, key, params)
        cache = st.session_state.simplified_cache
        
        # Ellenőrizzük, hogy létezik-e
        if cache_key not in cache['data']:
            return None
        
        # TTL ellenőrzése
        ttl = ttl_seconds or self.default_ttl
        timestamp = cache['timestamps'].get(cache_key)
        
        if timestamp:
            age = (datetime.now() - timestamp).total_seconds()
            if age > ttl:
                # Lejárt, töröljük
                self._remove_from_cache(cache_key)
                self.logger.debug(f"Cache expired for key: {cache_key}")
                return None
        
        # Access count növelése
        cache['access_count'][cache_key] = cache['access_count'].get(cache_key, 0) + 1
        
        value = cache['data'][cache_key]
        self.logger.debug(f"Cache hit for key: {cache_key}")
        return value
    
    def set(self, namespace: str, key: str, value: Any, params: Dict[str, Any] = None,
            ttl_seconds: int = None) -> ComponentResult:
        """
        Érték beállítása cache-ben
        
        Args:
            namespace: Cache névtér
            key: Cache kulcs
            value: Érték
            params: Paraméterek
            ttl_seconds: TTL másodpercekben
            
        Returns:
            ComponentResult
        """
        try:
            if not self.is_initialized():
                self.initialize()
            
            cache_key = self._get_cache_key(namespace, key, params)
            cache = st.session_state.simplified_cache
            
            # Cache méret ellenőrzése
            if len(cache['data']) >= self.max_cache_size:
                self._evict_oldest_entries()
            
            # Érték beállítása
            cache['data'][cache_key] = value
            cache['timestamps'][cache_key] = datetime.now()
            cache['access_count'][cache_key] = 1
            
            self.logger.debug(f"Cache set for key: {cache_key}")
            return ComponentResult(success=True, data=f"Cached: {cache_key}")
            
        except Exception as e:
            error_msg = f"Failed to set cache: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def invalidate(self, namespace: str, key: str = None, params: Dict[str, Any] = None) -> ComponentResult:
        """
        Cache invalidálása
        
        Args:
            namespace: Cache névtér
            key: Specifikus kulcs (None = teljes névtér)
            params: Paraméterek
            
        Returns:
            ComponentResult
        """
        try:
            if not self.is_initialized():
                return ComponentResult(success=True, data="Cache not initialized")
            
            cache = st.session_state.simplified_cache
            
            if key:
                # Specifikus kulcs törlése
                cache_key = self._get_cache_key(namespace, key, params)
                self._remove_from_cache(cache_key)
                self.logger.debug(f"Cache invalidated for key: {cache_key}")
            else:
                # Teljes névtér törlése
                keys_to_remove = [k for k in cache['data'].keys() if k.startswith(f"{namespace}|")]
                for cache_key in keys_to_remove:
                    self._remove_from_cache(cache_key)
                self.logger.debug(f"Cache invalidated for namespace: {namespace}")
            
            return ComponentResult(success=True, data="Cache invalidated")
            
        except Exception as e:
            error_msg = f"Failed to invalidate cache: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def clear_all(self) -> ComponentResult:
        """
        Teljes cache törlése
        
        Returns:
            ComponentResult
        """
        try:
            if 'simplified_cache' in st.session_state:
                st.session_state.simplified_cache = {
                    'data': {},
                    'timestamps': {},
                    'access_count': {},
                    'created_at': datetime.now()
                }
            
            self.logger.info("All cache cleared")
            return ComponentResult(success=True, data="All cache cleared")
            
        except Exception as e:
            error_msg = f"Failed to clear cache: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Cache statisztikák lekérése
        
        Returns:
            Statisztikák dictionary
        """
        if not self.is_initialized() or 'simplified_cache' not in st.session_state:
            return {
                'total_entries': 0,
                'total_hits': 0,
                'cache_age_seconds': 0,
                'namespaces': {}
            }
        
        cache = st.session_state.simplified_cache
        
        # Névterek szerinti csoportosítás
        namespaces = {}
        for cache_key in cache['data'].keys():
            namespace = cache_key.split('|')[0]
            if namespace not in namespaces:
                namespaces[namespace] = {
                    'entries': 0,
                    'total_hits': 0
                }
            
            namespaces[namespace]['entries'] += 1
            namespaces[namespace]['total_hits'] += cache['access_count'].get(cache_key, 0)
        
        # Cache életkor
        cache_age = (datetime.now() - cache['created_at']).total_seconds()
        
        return {
            'total_entries': len(cache['data']),
            'total_hits': sum(cache['access_count'].values()),
            'cache_age_seconds': cache_age,
            'namespaces': namespaces
        }
    
    def _remove_from_cache(self, cache_key: str):
        """Cache bejegyzés eltávolítása"""
        cache = st.session_state.simplified_cache
        
        cache['data'].pop(cache_key, None)
        cache['timestamps'].pop(cache_key, None)
        cache['access_count'].pop(cache_key, None)
    
    def _evict_oldest_entries(self, count: int = 10):
        """
        Legrégebbi bejegyzések eltávolítása
        
        Args:
            count: Eltávolítandó bejegyzések száma
        """
        cache = st.session_state.simplified_cache
        
        # Timestamp szerint rendezés
        sorted_entries = sorted(
            cache['timestamps'].items(),
            key=lambda x: x[1]
        )
        
        # Legrégebbi bejegyzések törlése
        for cache_key, _ in sorted_entries[:count]:
            self._remove_from_cache(cache_key)
        
        self.logger.debug(f"Evicted {count} oldest cache entries")
    
    def cleanup_expired(self) -> ComponentResult:
        """
        Lejárt cache bejegyzések törlése
        
        Returns:
            ComponentResult
        """
        try:
            if not self.is_initialized():
                return ComponentResult(success=True, data="Cache not initialized")
            
            cache = st.session_state.simplified_cache
            current_time = datetime.now()
            expired_keys = []
            
            for cache_key, timestamp in cache['timestamps'].items():
                age = (current_time - timestamp).total_seconds()
                if age > self.default_ttl:
                    expired_keys.append(cache_key)
            
            for cache_key in expired_keys:
                self._remove_from_cache(cache_key)
            
            self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
            return ComponentResult(
                success=True, 
                data=f"Cleaned up {len(expired_keys)} expired entries"
            )
            
        except Exception as e:
            error_msg = f"Failed to cleanup expired cache: {e}"
            self.logger.error(error_msg)
            return ComponentResult(success=False, error=error_msg)

# Singleton instance
_cache_manager_instance = None

def get_cache_manager() -> CacheManager:
    """
    Cache manager singleton lekérése
    
    Returns:
        CacheManager instance
    """
    global _cache_manager_instance
    
    if _cache_manager_instance is None:
        _cache_manager_instance = CacheManager()
        _cache_manager_instance.initialize()
    
    return _cache_manager_instance

# Convenience functions
def cache_get(namespace: str, key: str, params: Dict[str, Any] = None, 
              ttl_seconds: int = None) -> Optional[Any]:
    """Convenience function cache lekéréshez"""
    return get_cache_manager().get(namespace, key, params, ttl_seconds)

def cache_set(namespace: str, key: str, value: Any, params: Dict[str, Any] = None,
              ttl_seconds: int = None) -> bool:
    """Convenience function cache beállításhoz"""
    result = get_cache_manager().set(namespace, key, value, params, ttl_seconds)
    return result.success

def cache_invalidate(namespace: str, key: str = None, params: Dict[str, Any] = None) -> bool:
    """Convenience function cache invalidáláshoz"""
    result = get_cache_manager().invalidate(namespace, key, params)
    return result.success