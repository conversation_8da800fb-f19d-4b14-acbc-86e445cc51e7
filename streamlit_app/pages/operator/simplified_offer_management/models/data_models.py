"""
Core Data Models
Alapvető adatmodellek a CONTEXT7 elemzés és meglévő kódbázi<PERSON> al<PERSON>
"""
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

@dataclass
class FilterState:
    """Szűrési állapot - a design document alapján"""
    producer_id: Optional[int] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    status_filters: List[str] = field(default_factory=list)
    product_category_id: Optional[int] = None
    search_term: Optional[str] = None
    sort_column: str = "created_at"
    sort_direction: str = "desc"
    page: int = 1
    page_size: int = 50
    
    def to_api_params(self) -> Dict[str, Any]:
        """Konvertálás API paraméterekké - meglévő API client alapján"""
        params = {}
        
        # Producer szűrő - a meglévő API client user_id-t vár
        if self.producer_id:
            params['user_id'] = self.producer_id
        
        # Dátum szűrők - API client date_from/date_to formátumot vár
        if self.date_from:
            params['date_from'] = self.date_from.isoformat()
        if self.date_to:
            params['date_to'] = self.date_to.isoformat()
        
        # Státusz szűrők
        if self.status_filters:
            if len(self.status_filters) == 1:
                params['status'] = self.status_filters[0]
            else:
                params['statuses'] = self.status_filters
        
        # Termék kategória
        if self.product_category_id:
            params['product_type_id'] = self.product_category_id
        
        # Keresési kifejezés
        if self.search_term:
            params['search'] = self.search_term
        
        # Lapozás
        params['page'] = self.page
        params['limit'] = self.page_size
        
        # Rendezés
        params['sort_by'] = self.sort_column
        params['sort_order'] = self.sort_direction
        
        return params
    
    def is_empty(self) -> bool:
        """Ellenőrzi, hogy vannak-e aktív szűrők"""
        return not any([
            self.producer_id,
            self.date_from,
            self.date_to,
            self.status_filters,
            self.product_category_id,
            self.search_term and self.search_term.strip()
        ])
    
    def get_summary(self) -> str:
        """Szűrők összefoglalása"""
        if self.is_empty():
            return "Nincs aktív szűrő"
        
        parts = []
        if self.producer_id:
            parts.append(f"Termelő: {self.producer_id}")
        if self.status_filters:
            parts.append(f"Státusz: {', '.join(self.status_filters)}")
        if self.date_from and self.date_to:
            parts.append(f"Időszak: {self.date_from} - {self.date_to}")
        if self.search_term:
            parts.append(f"Keresés: {self.search_term}")
        
        return "; ".join(parts)

@dataclass
class Offer:
    """Ajánlat modell - meglévő API válasz struktúra alapján"""
    id: int
    producer_name: str
    producer_id: int
    product_name: str
    product_category: str
    quantity_value: Decimal
    quantity_unit: str
    delivery_date: date
    status: str
    confirmed_price: Optional[Decimal]
    confirmed_quantity: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'Offer':
        """API válasz alapján Offer objektum létrehozása - meglévő pattern alapján"""
        try:
            # Dátumok kezelése - meglévő kód alapján
            created_at = datetime.now()
            updated_at = datetime.now()
            
            if data.get('created_at'):
                try:
                    if isinstance(data['created_at'], str):
                        created_at = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
                    elif isinstance(data['created_at'], datetime):
                        created_at = data['created_at']
                except Exception as e:
                    logger.warning(f"Could not parse created_at: {e}")
            
            if data.get('updated_at'):
                try:
                    if isinstance(data['updated_at'], str):
                        updated_at = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
                    elif isinstance(data['updated_at'], datetime):
                        updated_at = data['updated_at']
                except Exception as e:
                    logger.warning(f"Could not parse updated_at: {e}")
            
            # Delivery date kezelése
            delivery_date = date.today()
            if data.get('delivery_date'):
                try:
                    if isinstance(data['delivery_date'], str):
                        delivery_date = date.fromisoformat(data['delivery_date'])
                    elif isinstance(data['delivery_date'], date):
                        delivery_date = data['delivery_date']
                except Exception as e:
                    logger.warning(f"Could not parse delivery_date: {e}")
            
            # Mennyiség kezelése - meglévő API struktúra alapján
            quantity_value = Decimal(str(data.get('quantity_value', data.get('quantity_in_kg', 0))))
            quantity_unit = data.get('quantity_unit', 'kg')
            
            # Producer információk - API client validáció alapján
            producer_id = data.get('producer_id', data.get('user_id', 0))
            producer_name = data.get('producer_name', data.get('user', {}).get('contact_name', 'Ismeretlen termelő'))
            if isinstance(data.get('user'), dict):
                producer_name = data['user'].get('contact_name', producer_name)
            
            return cls(
                id=int(data.get('id', 0)),
                producer_id=int(producer_id),
                producer_name=str(producer_name),
                product_name=str(data.get('product_name', data.get('product_type', {}).get('name', 'Ismeretlen termék'))),
                product_category=str(data.get('product_category', data.get('product_type', {}).get('category', {}).get('name', 'Ismeretlen kategória'))),
                quantity_value=quantity_value,
                quantity_unit=str(quantity_unit),
                delivery_date=delivery_date,
                status=str(data.get('status', 'CREATED')),
                confirmed_price=Decimal(str(data['confirmed_price'])) if data.get('confirmed_price') else None,
                confirmed_quantity=Decimal(str(data['confirmed_quantity'])) if data.get('confirmed_quantity') else None,
                created_at=created_at,
                updated_at=updated_at
            )
            
        except Exception as e:
            logger.error(f"Error creating Offer from API data: {e}")
            raise ValueError(f"Invalid API data for Offer: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Dictionary formátumba konvertálás"""
        return {
            'id': self.id,
            'producer_id': self.producer_id,
            'producer_name': self.producer_name,
            'product_name': self.product_name,
            'product_category': self.product_category,
            'quantity_value': float(self.quantity_value),
            'quantity_unit': self.quantity_unit,
            'delivery_date': self.delivery_date.isoformat(),
            'status': self.status,
            'confirmed_price': float(self.confirmed_price) if self.confirmed_price else None,
            'confirmed_quantity': float(self.confirmed_quantity) if self.confirmed_quantity else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @property
    def formatted_quantity(self) -> str:
        """Formázott mennyiség megjelenítés"""
        if self.quantity_unit == "db":
            return f"{self.quantity_value:,.0f} db"
        elif self.quantity_unit == "tonna":
            return f"{self.quantity_value:,.2f} tonna"
        else:
            return f"{self.quantity_value:,.2f} {self.quantity_unit}"
    
    @property
    def status_display(self) -> str:
        """Státusz megjelenítési neve"""
        status_map = {
            'CREATED': 'Létrehozva',
            'CONFIRMED_BY_COMPANY': 'Megerősítve',
            'ACCEPTED_BY_USER': 'Elfogadva',
            'REJECTED_BY_USER': 'Elutasítva',
            'FINALIZED': 'Véglegesítve'
        }
        return status_map.get(self.status, self.status)

@dataclass
class ActionResult:
    """Műveletek eredménye"""
    success: bool
    message: str
    updated_offers: List[Offer] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    @classmethod
    def success_result(cls, message: str, updated_offers: List[Offer] = None) -> 'ActionResult':
        """Sikeres művelet eredménye"""
        return cls(
            success=True,
            message=message,
            updated_offers=updated_offers or []
        )
    
    @classmethod
    def error_result(cls, message: str, errors: List[str] = None) -> 'ActionResult':
        """Sikertelen művelet eredménye"""
        return cls(
            success=False,
            message=message,
            errors=errors or [message]
        )

@dataclass
class AppState:
    """Alkalmazás állapot - session state kezeléshez"""
    current_filters: FilterState = field(default_factory=FilterState)
    selected_offers: List[int] = field(default_factory=list)
    current_view: str = "list"  # "list" | "detail"
    selected_offer_id: Optional[int] = None
    loading_states: Dict[str, bool] = field(default_factory=dict)
    error_messages: List[str] = field(default_factory=list)
    cache: Dict[str, Any] = field(default_factory=dict)
    
    def set_loading(self, operation: str, loading: bool = True):
        """Loading állapot beállítása"""
        self.loading_states[operation] = loading
    
    def is_loading(self, operation: str) -> bool:
        """Loading állapot ellenőrzése"""
        return self.loading_states.get(operation, False)
    
    def add_error(self, error: str):
        """Hibaüzenet hozzáadása"""
        self.error_messages.append(error)
        # Csak az utolsó 10 hibát tartjuk meg
        if len(self.error_messages) > 10:
            self.error_messages = self.error_messages[-10:]
    
    def clear_errors(self):
        """Hibaüzenetek törlése"""
        self.error_messages.clear()
    
    def set_cache(self, key: str, value: Any):
        """Cache érték beállítása"""
        self.cache[key] = {
            'value': value,
            'timestamp': datetime.now()
        }
    
    def get_cache(self, key: str, max_age_seconds: int = 300) -> Optional[Any]:
        """Cache érték lekérése"""
        if key not in self.cache:
            return None
        
        cached = self.cache[key]
        age = (datetime.now() - cached['timestamp']).total_seconds()
        
        if age > max_age_seconds:
            del self.cache[key]
            return None
        
        return cached['value']
    
    def clear_cache(self):
        """Cache törlése"""
        self.cache.clear()

# Utility függvények
def offers_from_api_response(api_response: List[Dict[str, Any]]) -> List[Offer]:
    """API válasz listából Offer objektumok listája"""
    offers = []
    for item in api_response:
        try:
            offer = Offer.from_api_response(item)
            offers.append(offer)
        except Exception as e:
            logger.warning(f"Could not create Offer from API item: {e}")
            continue
    
    return offers