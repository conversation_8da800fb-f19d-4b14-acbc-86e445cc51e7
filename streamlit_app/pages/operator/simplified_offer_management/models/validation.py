"""
Data Validation Module
Pydantic alapú validációs modellek a CONTEXT7 elemzés alapján
"""
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic.dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

# Pydantic konfiguráció
class BaseConfig:
    """Alap Pydantic konfiguráció"""
    model_config = ConfigDict(
        validate_assignment=True,
        str_strip_whitespace=True,
        validate_default=True,
        extra='forbid'
    )

class FilterStateValidator(BaseModel, BaseConfig):
    """Szűrési állapot validátor - Pydantic alapú"""
    producer_id: Optional[int] = Field(None, gt=0, description="Termelő azonosító")
    date_from: Optional[date] = Field(None, description="Kezdő dátum")
    date_to: Optional[date] = Field(None, description="Záró dátum")
    status_filters: List[str] = Field(default_factory=list, description="Státusz szűrők")
    product_category_id: Optional[int] = Field(None, gt=0, description="Termék kategória azonosító")
    search_term: Optional[str] = Field(None, min_length=1, max_length=100, description="Keresési kifejezés")
    sort_column: str = Field("created_at", description="Rendezési oszlop")
    sort_direction: str = Field("desc", description="Rendezési irány")
    page: int = Field(1, gt=0, description="Oldal száma")
    page_size: int = Field(50, gt=0, le=200, description="Oldal mérete")
    
    @field_validator('date_to', mode='after')
    @classmethod
    def validate_date_range(cls, v: Optional[date], info) -> Optional[date]:
        """Dátum tartomány validálása"""
        if v is not None and 'date_from' in info.data:
            date_from = info.data['date_from']
            if date_from and v < date_from:
                raise ValueError('A záró dátum nem lehet korábbi a kezdő dátumnál')
        return v
    
    @field_validator('status_filters', mode='after')
    @classmethod
    def validate_status_filters(cls, v: List[str]) -> List[str]:
        """Státusz szűrők validálása"""
        valid_statuses = {
            'CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 
            'REJECTED_BY_USER', 'FINALIZED'
        }
        
        for status in v:
            if status not in valid_statuses:
                raise ValueError(f'Érvénytelen státusz: {status}')
        
        return v
    
    @field_validator('sort_column', mode='after')
    @classmethod
    def validate_sort_column(cls, v: str) -> str:
        """Rendezési oszlop validálása"""
        valid_columns = {
            'created_at', 'updated_at', 'producer_name', 'product_name',
            'quantity_value', 'delivery_date', 'status'
        }
        
        if v not in valid_columns:
            raise ValueError(f'Érvénytelen rendezési oszlop: {v}')
        
        return v
    
    @field_validator('sort_direction', mode='after')
    @classmethod
    def validate_sort_direction(cls, v: str) -> str:
        """Rendezési irány validálása"""
        if v not in ['asc', 'desc']:
            raise ValueError('A rendezési irány csak "asc" vagy "desc" lehet')
        
        return v
    
    @field_validator('search_term', mode='before')
    @classmethod
    def clean_search_term(cls, v: Optional[str]) -> Optional[str]:
        """Keresési kifejezés tisztítása"""
        if v is None:
            return None
        
        # Whitespace eltávolítása és üres string kezelése
        cleaned = v.strip()
        return cleaned if cleaned else None

class OfferValidator(BaseModel, BaseConfig):
    """Ajánlat validátor - API kompatibilis"""
    id: int = Field(..., gt=0, description="Ajánlat azonosító")
    producer_id: int = Field(..., gt=0, description="Termelő azonosító")
    producer_name: str = Field(..., min_length=1, max_length=255, description="Termelő neve")
    product_name: str = Field(..., min_length=1, max_length=255, description="Termék neve")
    product_category: str = Field(..., min_length=1, max_length=100, description="Termék kategória")
    quantity_value: Decimal = Field(..., gt=0, description="Mennyiség értéke")
    quantity_unit: str = Field(..., description="Mennyiségi egység")
    delivery_date: date = Field(..., description="Szállítási dátum")
    status: str = Field(..., description="Ajánlat státusza")
    confirmed_price: Optional[Decimal] = Field(None, gt=0, description="Megerősített ár")
    confirmed_quantity: Optional[Decimal] = Field(None, gt=0, description="Megerősített mennyiség")
    created_at: datetime = Field(..., description="Létrehozás időpontja")
    updated_at: datetime = Field(..., description="Frissítés időpontja")
    
    @field_validator('quantity_unit', mode='after')
    @classmethod
    def validate_quantity_unit(cls, v: str) -> str:
        """Mennyiségi egység validálása"""
        valid_units = {'kg', 'tonna', 'db'}
        if v not in valid_units:
            raise ValueError(f'Érvénytelen mennyiségi egység: {v}. Engedélyezett: {", ".join(valid_units)}')
        return v
    
    @field_validator('status', mode='after')
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Státusz validálása"""
        valid_statuses = {
            'CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER',
            'REJECTED_BY_USER', 'FINALIZED'
        }
        if v not in valid_statuses:
            raise ValueError(f'Érvénytelen státusz: {v}')
        return v
    
    @field_validator('delivery_date', mode='after')
    @classmethod
    def validate_delivery_date(cls, v: date) -> date:
        """Szállítási dátum validálása"""
        # Csak a múltbeli dátumokat tiltjuk (2024 előtti)
        if v.year < 2024:
            raise ValueError(f'A szállítási dátum nem lehet 2024 előtti: {v}')
        return v
    
    @field_validator('producer_name', 'product_name', mode='before')
    @classmethod
    def clean_names(cls, v: str) -> str:
        """Nevek tisztítása"""
        if not v or not v.strip():
            raise ValueError('A név nem lehet üres')
        return v.strip()

class ActionResultValidator(BaseModel, BaseConfig):
    """Művelet eredmény validátor"""
    success: bool = Field(..., description="Sikeres volt-e a művelet")
    message: str = Field(..., min_length=1, description="Eredmény üzenet")
    updated_offers: List[OfferValidator] = Field(default_factory=list, description="Frissített ajánlatok")
    errors: List[str] = Field(default_factory=list, description="Hibaüzenetek")
    
    @field_validator('message', mode='before')
    @classmethod
    def clean_message(cls, v: str) -> str:
        """Üzenet tisztítása"""
        if not v or not v.strip():
            raise ValueError('Az üzenet nem lehet üres')
        return v.strip()
    
    @field_validator('errors', mode='after')
    @classmethod
    def validate_errors(cls, v: List[str]) -> List[str]:
        """Hibaüzenetek validálása"""
        # Üres stringek kiszűrése
        return [error.strip() for error in v if error and error.strip()]

# Pydantic dataclass validátorok
@dataclass(config=ConfigDict(validate_assignment=True))
class ValidatedFilterState:
    """Validált szűrési állapot dataclass"""
    producer_id: Optional[int] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    status_filters: List[str] = None
    product_category_id: Optional[int] = None
    search_term: Optional[str] = None
    sort_column: str = "created_at"
    sort_direction: str = "desc"
    page: int = 1
    page_size: int = 50
    
    def __post_init__(self):
        """Post-init validáció"""
        if self.status_filters is None:
            self.status_filters = []
        
        # Dátum tartomány ellenőrzése
        if (self.date_from and self.date_to and 
            self.date_to < self.date_from):
            raise ValueError('A záró dátum nem lehet korábbi a kezdő dátumnál')
    
    @field_validator('producer_id', mode='before')
    @classmethod
    def validate_producer_id(cls, v):
        """Termelő ID validálása"""
        if v is not None and v <= 0:
            raise ValueError('A termelő azonosító pozitív szám kell legyen')
        return v

# Utility validátor függvények
def validate_api_response_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    API válasz adatok validálása
    
    Args:
        data: API válasz dictionary
        
    Returns:
        Validált adatok
        
    Raises:
        ValueError: Validációs hiba esetén
    """
    try:
        # Kötelező mezők ellenőrzése
        required_fields = ['id', 'producer_name', 'product_name', 'status']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            raise ValueError(f'Hiányzó kötelező mezők: {", ".join(missing_fields)}')
        
        # Típus konverziók
        if 'id' in data:
            data['id'] = int(data['id'])
        
        if 'producer_id' in data:
            data['producer_id'] = int(data['producer_id'])
        elif 'user_id' in data:
            data['producer_id'] = int(data['user_id'])
        
        # Mennyiség kezelése
        if 'quantity_value' not in data and 'quantity_in_kg' in data:
            data['quantity_value'] = data['quantity_in_kg']
        
        if 'quantity_unit' not in data:
            data['quantity_unit'] = 'kg'
        
        # Dátumok kezelése
        for date_field in ['created_at', 'updated_at']:
            if data.get(date_field) and isinstance(data[date_field], str):
                try:
                    data[date_field] = datetime.fromisoformat(data[date_field].replace('Z', '+00:00'))
                except ValueError:
                    logger.warning(f"Could not parse {date_field}: {data[date_field]}")
                    data[date_field] = datetime.now()
        
        if data.get('delivery_date') and isinstance(data['delivery_date'], str):
            try:
                data['delivery_date'] = date.fromisoformat(data['delivery_date'])
            except ValueError:
                logger.warning(f"Could not parse delivery_date: {data['delivery_date']}")
                data['delivery_date'] = date.today()
        
        return data
        
    except Exception as e:
        logger.error(f"API response validation error: {e}")
        raise ValueError(f"Érvénytelen API válasz: {e}")

def validate_filter_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Szűrési paraméterek validálása
    
    Args:
        params: Szűrési paraméterek
        
    Returns:
        Validált paraméterek
    """
    try:
        validator = FilterStateValidator(**params)
        return validator.model_dump(exclude_none=True)
    except Exception as e:
        logger.error(f"Filter validation error: {e}")
        raise ValueError(f"Érvénytelen szűrési paraméterek: {e}")

def validate_offer_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ajánlat adatok validálása
    
    Args:
        data: Ajánlat adatok
        
    Returns:
        Validált adatok
    """
    try:
        # Előfeldolgozás
        validated_data = validate_api_response_data(data)
        
        # Pydantic validáció
        validator = OfferValidator(**validated_data)
        return validator.model_dump()
        
    except Exception as e:
        logger.error(f"Offer validation error: {e}")
        raise ValueError(f"Érvénytelen ajánlat adatok: {e}")

# Batch validáció
def validate_offers_batch(offers_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Ajánlatok batch validálása
    
    Args:
        offers_data: Ajánlatok listája
        
    Returns:
        Validált ajánlatok listája
    """
    validated_offers = []
    errors = []
    
    for i, offer_data in enumerate(offers_data):
        try:
            validated_offer = validate_offer_data(offer_data)
            validated_offers.append(validated_offer)
        except ValueError as e:
            errors.append(f"Ajánlat #{i}: {e}")
            logger.warning(f"Skipping invalid offer #{i}: {e}")
    
    if errors:
        logger.warning(f"Batch validation completed with {len(errors)} errors")
    
    return validated_offers

# Validációs eredmény wrapper
class ValidationResult(BaseModel):
    """Validációs eredmény"""
    success: bool
    data: Optional[Any] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    @classmethod
    def success_result(cls, data: Any, warnings: List[str] = None) -> 'ValidationResult':
        """Sikeres validáció eredménye"""
        return cls(
            success=True,
            data=data,
            warnings=warnings or []
        )
    
    @classmethod
    def error_result(cls, errors: List[str], data: Any = None) -> 'ValidationResult':
        """Sikertelen validáció eredménye"""
        return cls(
            success=False,
            data=data,
            errors=errors
        )