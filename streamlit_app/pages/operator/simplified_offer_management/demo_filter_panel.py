"""
Demo Filter Panel
Szűrőpanel komponens demó alkalmazás
"""
import streamlit as st
import logging
import sys
import os
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from decimal import Decimal

# Konfiguráljuk a loggolást
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Importálási útvonal beállítása
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Importáljuk a szükséges komponenseket
try:
    from pages.operator.simplified_offer_management.components.filter_panel import get_filter_panel
    from pages.operator.simplified_offer_management.components.filter_manager import get_filter_manager
    from pages.operator.simplified_offer_management.models.data_models import FilterState, Offer
except ImportError as e:
    st.error(f"Importálási hiba: {e}")
    st.error("<PERSON><PERSON><PERSON><PERSON>, hogy a szükséges modulok elérhetőek-e.")
    
    # Próbáljuk meg relatív importálással
    try:
        from .components.filter_panel import get_filter_panel
        from .components.filter_manager import get_filter_manager
        from .models.data_models import FilterState, Offer
        st.success("Relatív importálás sikeres!")
    except ImportError as e2:
        st.error(f"Relatív importálási hiba: {e2}")
        
        # Utolsó próbálkozás
        try:
            sys.path.append(current_dir)
            from components.filter_panel import get_filter_panel
            from components.filter_manager import get_filter_manager
            from models.data_models import FilterState, Offer
            st.success("Lokális importálás sikeres!")
        except ImportError as e3:
            st.error(f"Lokális importálási hiba: {e3}")
            st.error("A demó nem tud betöltődni az importálási hibák miatt.")

def main():
    """Fő alkalmazás"""
    # Oldal beállítások
    st.set_page_config(
        page_title="Egyszerűsített Ajánlatkezelő - Szűrőpanel Demó",
        page_icon="🔍",
        layout="wide"
    )
    
    # Cím
    st.title("🔍 Egyszerűsített Ajánlatkezelő - Szűrőpanel Demó")
    
    # Rövid leírás
    st.markdown("""
    Ez egy demó alkalmazás, amely bemutatja az egyszerűsített ajánlatkezelő rendszer szűrőpanel komponensét.
    A szűrőpanel lehetővé teszi a felhasználók számára, hogy különböző kritériumok alapján szűrjék az ajánlatokat.
    
    **Főbb funkciók:**
    - Termelő szerinti szűrés (keresési funkcióval)
    - Dátum szerinti szűrés
    - Státusz szerinti szűrés
    - Termék kategória hierarchikus szűrés
    - Rendezési és lapozási beállítások
    - Mentett szűrők kezelése
    """)
    
    # Elválasztó
    st.divider()
    
    # Két oszlopos elrendezés
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("Szűrőpanel")
        
        # Szűrőpanel komponens renderelése
        filter_panel = get_filter_panel()
        current_filters = filter_panel.render()
        
        # Szűrő állapot megjelenítése (debug)
        with st.expander("🔧 Szűrő állapot (Debug)", expanded=False):
            st.json({
                'producer_id': current_filters.producer_id,
                'date_from': current_filters.date_from.isoformat() if current_filters.date_from else None,
                'date_to': current_filters.date_to.isoformat() if current_filters.date_to else None,
                'status_filters': current_filters.status_filters,
                'product_category_id': current_filters.product_category_id,
                'search_term': current_filters.search_term,
                'sort_column': current_filters.sort_column,
                'sort_direction': current_filters.sort_direction,
                'page': current_filters.page,
                'page_size': current_filters.page_size
            })
    
    with col2:
        st.subheader("Ajánlatok")
        
        # Ajánlatok betöltése (demó adatok)
        offers = _get_demo_offers(current_filters)
        
        # Ajánlatok megjelenítése
        if offers:
            # Táblázat oszlopok
            columns = ["ID", "Termelő", "Termék", "Mennyiség", "Szállítási dátum", "Státusz"]
            
            # Adatok előkészítése a táblázathoz
            data = []
            for offer in offers:
                data.append([
                    offer.id,
                    offer.producer_name,
                    offer.product_name,
                    offer.formatted_quantity,
                    offer.delivery_date.strftime("%Y-%m-%d"),
                    offer.status_display
                ])
            
            # Táblázat megjelenítése
            st.dataframe(
                data,
                column_config={
                    0: "ID",
                    1: "Termelő",
                    2: "Termék",
                    3: "Mennyiség",
                    4: "Szállítási dátum",
                    5: "Státusz"
                },
                hide_index=True,
                use_container_width=True
            )
            
            # Lapozás
            st.caption(f"Összesen {len(offers)} ajánlat | Oldal: {current_filters.page} | Oldal méret: {current_filters.page_size}")
        else:
            st.info("Nincs megjeleníthető ajánlat a jelenlegi szűrési feltételekkel.")
            
            # Demó adatok generálása gomb
            if st.button("🔄 Demó adatok generálása", use_container_width=True):
                st.session_state['demo_offers'] = _generate_demo_offers()
                st.rerun()

def _get_demo_offers(filters: FilterState) -> List[Offer]:
    """
    Demó ajánlatok lekérése a szűrési feltételek alapján
    
    Args:
        filters: Szűrési feltételek
        
    Returns:
        Szűrt ajánlatok listája
    """
    # Demó ajánlatok lekérése vagy generálása
    if 'demo_offers' not in st.session_state:
        st.session_state['demo_offers'] = _generate_demo_offers()
    
    all_offers = st.session_state['demo_offers']
    
    # Szűrés
    filtered_offers = []
    for offer in all_offers:
        # Termelő szűrés
        if filters.producer_id and offer.producer_id != filters.producer_id:
            continue
        
        # Dátum szűrés
        if filters.date_from and offer.delivery_date < filters.date_from:
            continue
        if filters.date_to and offer.delivery_date > filters.date_to:
            continue
        
        # Státusz szűrés
        if filters.status_filters and offer.status not in filters.status_filters:
            continue
        
        # Termék kategória szűrés (egyszerűsített)
        if filters.product_category_id:
            # Demó esetén csak az ID utolsó számjegyét használjuk kategóriaként
            category_id = offer.id % 10
            if category_id != filters.product_category_id:
                continue
        
        # Keresés
        if filters.search_term:
            search_term = filters.search_term.lower()
            if (search_term not in offer.producer_name.lower() and 
                search_term not in offer.product_name.lower()):
                continue
        
        filtered_offers.append(offer)
    
    # Rendezés
    if filters.sort_column == 'producer_name':
        filtered_offers.sort(key=lambda o: o.producer_name)
    elif filters.sort_column == 'product_name':
        filtered_offers.sort(key=lambda o: o.product_name)
    elif filters.sort_column == 'delivery_date':
        filtered_offers.sort(key=lambda o: o.delivery_date)
    elif filters.sort_column == 'status':
        filtered_offers.sort(key=lambda o: o.status)
    else:  # default: created_at
        filtered_offers.sort(key=lambda o: o.created_at)
    
    # Rendezési irány
    if filters.sort_direction == 'desc':
        filtered_offers.reverse()
    
    # Lapozás
    start_idx = (filters.page - 1) * filters.page_size
    end_idx = start_idx + filters.page_size
    
    return filtered_offers[start_idx:end_idx]

def _generate_demo_offers() -> List[Offer]:
    """
    Demó ajánlatok generálása
    
    Returns:
        Generált ajánlatok listája
    """
    from decimal import Decimal
    import random
    
    # Termelők
    producers = [
        {"id": 1, "name": "Nagy Gazda Kft."},
        {"id": 2, "name": "Zöld Mező Szövetkezet"},
        {"id": 3, "name": "Friss Termény Bt."},
        {"id": 4, "name": "Napfény Farm"},
        {"id": 5, "name": "Termő Föld Kft."},
        {"id": 6, "name": "Arany Kalász Zrt."},
        {"id": 7, "name": "Bio Kert Egyesület"},
        {"id": 8, "name": "Hazai Termék Kft."},
        {"id": 9, "name": "Termesztő Kft."},
        {"id": 10, "name": "Agrár Centrum Zrt."}
    ]
    
    # Termékek
    products = [
        {"name": "Alma", "category": "Gyümölcs"},
        {"name": "Körte", "category": "Gyümölcs"},
        {"name": "Szilva", "category": "Gyümölcs"},
        {"name": "Paradicsom", "category": "Zöldség"},
        {"name": "Paprika", "category": "Zöldség"},
        {"name": "Uborka", "category": "Zöldség"},
        {"name": "Burgonya", "category": "Zöldség"},
        {"name": "Hagyma", "category": "Zöldség"},
        {"name": "Búza", "category": "Gabona"},
        {"name": "Kukorica", "category": "Gabona"}
    ]
    
    # Státuszok
    statuses = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
    
    # Mennyiségi egységek
    units = ["kg", "tonna", "db"]
    
    # Ajánlatok generálása
    offers = []
    today = date.today()
    
    for i in range(1, 101):  # 100 ajánlat
        # Véletlenszerű termelő
        producer = random.choice(producers)
        
        # Véletlenszerű termék
        product = random.choice(products)
        
        # Véletlenszerű státusz
        status = random.choice(statuses)
        
        # Véletlenszerű mennyiség
        quantity_value = Decimal(str(random.randint(10, 1000)))
        quantity_unit = random.choice(units)
        
        # Véletlenszerű dátumok
        days_offset = random.randint(-30, 60)
        delivery_date = today + timedelta(days=days_offset)
        
        created_days_ago = random.randint(1, 90)
        created_at = datetime.now() - timedelta(days=created_days_ago)
        
        updated_days_ago = random.randint(0, created_days_ago)
        updated_at = datetime.now() - timedelta(days=updated_days_ago)
        
        # Megerősített ár és mennyiség (csak bizonyos státuszokhoz)
        confirmed_price = None
        confirmed_quantity = None
        
        if status in ["CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "FINALIZED"]:
            confirmed_price = Decimal(str(random.randint(100, 5000)))
            confirmed_quantity = quantity_value
        
        # Ajánlat létrehozása
        offer = Offer(
            id=i,
            producer_name=producer["name"],
            producer_id=producer["id"],
            product_name=product["name"],
            product_category=product["category"],
            quantity_value=quantity_value,
            quantity_unit=quantity_unit,
            delivery_date=delivery_date,
            status=status,
            confirmed_price=confirmed_price,
            confirmed_quantity=confirmed_quantity,
            created_at=created_at,
            updated_at=updated_at
        )
        
        offers.append(offer)
    
    return offers

if __name__ == "__main__":
    main()