# Ügyintézői irányítópult
"""
Ügyintézői irányítópult oldal.
"""
import streamlit as st
import datetime
from api import offers as offers_api
from api import users as users_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from components.data_display import display_offer_table, display_status_chart
from components.activity_feed import render_dashboard_widgets
from utils.session import is_authenticated, get_current_user, init_session_state
from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
from utils.config import get_page_title
import uuid

def load_settings_to_session_state():
    """
    Beállítások betöltése a session state-be a backend API-ból.
    Ez biztosítja, hogy a beállítások elérhetőek legyenek akkor is, ha a session state elveszne.
    """
    # Lekérjük a beállításokat a backendről
    success, settings = users_api.get_user_default_settings()
    
    # Mindig írjuk ki a konzolra a teljes API választ a diagnosztikához
    print("==== API BEÁLLÍTÁSOK VÁLASZ ====")
    print(f"API hívás sikeres: {success}")
    print(f"API válasz: {settings}")
    print("==== API VÁLASZ RÉSZLETEK ====")
    if success and isinstance(settings, dict):
        for key, value in settings.items():
            print(f"{key}: {value}")
    print("==== API VÁLASZ VÉGE ====")
    
    # Csak akkor töltünk be adatokat, ha a settings ténylegesen nem üres
    if success and isinstance(settings, dict) and settings:
        # Létrehozzuk a user_settings objektumot a session state-ben
        st.session_state.user_settings = {
            "default_product_type": {
                "id": settings.get("default_product_type_id"),
                "name": settings.get("default_product_type_name", ""),
                "has_quality_grades": settings.get("has_quality_grades", False)
            },
            "default_quality_grade": {
                "id": settings.get("default_quality_grade_id"),
                "name": settings.get("default_quality_grade_name", "")
            },
            "default_quantity_unit": settings.get("default_quantity_unit", "kg"),
            "category": {
                "id": settings.get("default_category_id"),
                "name": settings.get("default_category_name", "")
            }
        }
        print("Beállítások sikeresen betöltve a session state-be a backendről")
        return True
    else:
        print("Nem sikerült betölteni a beállításokat - hiányos vagy üres API válasz")
        return False

def show_operator_dashboard():
    """
    Ügyintézői irányítópult megjelenítése.
    """
    # Biztosítjuk, hogy a page_uuid inicializálva van
    init_session_state()
    
    # Debug: Kiírjuk a session state tartalmát
    print(f"Operator dashboard: page_uuid={st.session_state.get('page_uuid', 'nem létezik')}")
    
    # Check if selected_offer_id is in session state and redirect if needed
    if "selected_offer_id" in st.session_state:
        offer_id = st.session_state["selected_offer_id"]
        print(f"Redirecting to offer management with offer ID: {offer_id}")
        st.switch_page("pages/operator/offer_management.py")
        return
    
    # Beállítjuk a debug módot az oldal tetejére
    debug_mode = False
    
    st.title("Ügyintézői Irányítópult")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Betöltjük a beállításokat a session state-be - mindig megpróbáljuk frissíteni
    if "user_settings" in st.session_state:
        del st.session_state.user_settings
    load_success = load_settings_to_session_state()
    
    # Debug információ megjelenítése az oldalon, ha engedélyezett
    if debug_mode:
        with st.expander("Debug Információ", expanded=False):
            st.write("### API Válasz Debug")
            success, settings = users_api.get_user_default_settings()
            st.write(f"API hívás sikeres: {success}")
            st.write(f"API válasz: {settings}")
            st.write("### Session State Debug")
            if "user_settings" in st.session_state:
                st.write("Session state tartalmaz beállításokat:")
                st.write(st.session_state.user_settings)
            else:
                st.write("Session state NEM tartalmaz beállításokat!")
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Üdvözlő üzenet
    st.write(f"### Üdvözöljük, {user.get('contact_name')}!")
    
    # Mai dátum és idő
    now = datetime.datetime.now()
    st.write(f"Dátum: {now.strftime('%Y. %m. %d.')} - {now.strftime('%H:%M')}")
    
    # Új tevékenységi widget megjelenítése
    render_dashboard_widgets()
    
    st.write("---")  # Elválasztó vonal
    
    # Irányítópult főbb elemei
    col1, col2 = st.columns(2)
    
    with col1:
        # Gyors áttekintés keret
        with st.container(border=True):
            st.subheader("Gyors áttekintés")
            
            # Ajánlatok lekérése a backenddől
            # Az ügyintéző az összes ajánlatot látja, nem csak a sajátjait
            success, result = offers_api.get_offers()
            
            if success:
                offers = result
                
                # Különböző státuszú ajánlatok számának kiszámítása
                total_offers = len(offers)
                new_offers = sum(1 for o in offers if o.get("status") == "CREATED")
                pending_approval = sum(1 for o in offers if o.get("status") == "ACCEPTED_BY_USER")
                finalized = sum(1 for o in offers if o.get("status") == "FINALIZED")
                
                # Mai/holnapi beszállítások
                today = datetime.date.today()
                tomorrow = today + datetime.timedelta(days=1)
                
                today_str = today.strftime("%Y-%m-%d")
                tomorrow_str = tomorrow.strftime("%Y-%m-%d")
                
                today_deliveries = sum(1 for o in offers if o.get("delivery_date") == today_str and o.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"])
                tomorrow_deliveries = sum(1 for o in offers if o.get("delivery_date") == tomorrow_str and o.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"])
                
                # Debug information
                print(f"Today: {today_str}, Tomorrow: {tomorrow_str}")
                print(f"Delivery dates in offers: {[o.get('delivery_date') for o in offers[:5]]}")
                
                # Megjelenítés metrika kártyákon
                metric_col1, metric_col2 = st.columns(2)
                with metric_col1:
                    st.metric(label="Új ajánlatok", value=new_offers)
                    st.metric(label="Mai beszállítások", value=today_deliveries)
                
                with metric_col2:
                    st.metric(label="Elfogadott, véglegesítésre vár", value=pending_approval)
                    st.metric(label="Holnapi beszállítások", value=tomorrow_deliveries)
                
                # Gyors műveletek
                st.write("### Gyors műveletek")
                button_col1, button_col2 = st.columns(2)
                
                with button_col1:
                    if st.button("Ajánlatok kezelése", type="primary", use_container_width=True):
                        st.switch_page("pages/operator_offer_management.py")
                
                with button_col2:
                    if st.button("Naptári nézet", use_container_width=True):
                        st.switch_page("pages/operator_calendar.py")
            else:
                show_error(f"Hiba az ajánlatok lekérésekor: {result}")
    
    with col2:
        # Sürgős ajánlatok keret
        with st.container(border=True):
            st.subheader("Sürgős ajánlatok")
            
            # Lekérjük a közelgő beszállításokat és a nem feldolgozott ajánlatokat
            # Két naptól egy héten belüli beszállítások, amelyek még nincsenek véglegesítve
            tomorrow = datetime.date.today() + datetime.timedelta(days=1)
            next_week = datetime.date.today() + datetime.timedelta(days=7)
            
            urgent_params = {
                "from_date": tomorrow.strftime("%Y-%m-%d"),
                "to_date": next_week.strftime("%Y-%m-%d"),
                "status": "CREATED"  # Csak a még nem visszaigazolt ajánlatok
            }
            
            success, result = offers_api.get_offers(params=urgent_params)
            
            if success:
                urgent_offers = result
                
                if urgent_offers:
                    # Rendezzük a sürgősségi sorrendben (a közelgő beszállítások előre)
                    urgent_offers.sort(key=lambda x: x.get("delivery_date", ""))
                    
                    # Táblázat készítése az ajánlatokról
                    display_offer_table(urgent_offers, with_actions=True, pagination=False)
                    
                    # Link a teljes ajánlatkezeléshez
                    if st.button("Összes ajánlat kezelése", type="primary", use_container_width=True):
                        st.switch_page("pages/operator_offer_management.py")
                else:
                    st.info("Nincs sürgős, feldolgozásra váró ajánlat a következő 7 napra.")
            else:
                show_error(f"Hiba az ajánlatok lekérésekor: {result}")
    
    # Mai és holnapi beszállítások
    st.write("## Mai és holnapi beszállítások")
    
    today = datetime.date.today()
    tomorrow = today + datetime.timedelta(days=1)
    
    # Debug info
    print("==== DELIVERIES DEBUG ====")
    print(f"Today: {today}, Tomorrow: {tomorrow}")
    
    # Mai beszállítások lekérése
    today_params = {
        "from_date": today.strftime("%Y-%m-%d"),
        "to_date": today.strftime("%Y-%m-%d"),
        "status": "FINALIZED,ACCEPTED_BY_USER"  # Csak az elfogadott vagy véglegesített ajánlatok
    }
    
    print(f"Today's delivery params: {today_params}")
    
    success, today_result = offers_api.get_offers(params=today_params)
    
    # Debug output
    print(f"Today's API call success: {success}")
    print(f"Today's result type: {type(today_result)}")
    if success:
        if isinstance(today_result, list):
            print(f"Today's deliveries count: {len(today_result)}")
            if len(today_result) > 0:
                print(f"First item delivery date: {today_result[0].get('delivery_date')}")
                print(f"First item keys: {today_result[0].keys() if isinstance(today_result[0], dict) else 'Not a dict'}")
                print(f"First item status: {today_result[0].get('status')}")
                print(f"First item: {today_result[0]}")
            else:
                print("Today's result is empty list")
        else:
            print(f"Today's result is not a list: {today_result}")
    
    # Holnapi beszállítások lekérése
    tomorrow_params = {
        "from_date": tomorrow.strftime("%Y-%m-%d"),
        "to_date": tomorrow.strftime("%Y-%m-%d"),
        "status": "FINALIZED,ACCEPTED_BY_USER"  # Csak az elfogadott vagy véglegesített ajánlatok
    }
    
    print(f"Tomorrow's delivery params: {tomorrow_params}")
    
    success_tomorrow, tomorrow_result = offers_api.get_offers(params=tomorrow_params)
    
    # Debug output
    print(f"Tomorrow's API call success: {success_tomorrow}")
    print(f"Tomorrow's result type: {type(tomorrow_result)}")
    if success_tomorrow:
        if isinstance(tomorrow_result, list):
            print(f"Tomorrow's deliveries count: {len(tomorrow_result)}")
            if len(tomorrow_result) > 0:
                print(f"First item delivery date: {tomorrow_result[0].get('delivery_date')}")
                print(f"First item keys: {tomorrow_result[0].keys() if isinstance(tomorrow_result[0], dict) else 'Not a dict'}")
                print(f"First item status: {tomorrow_result[0].get('status')}")
                print(f"First item: {tomorrow_result[0]}")
            else:
                print("Tomorrow's result is empty list")
        else:
            print(f"Tomorrow's result is not a list: {tomorrow_result}")
    
    print("=========================")
    
    # Két fül a mai és holnapi beszállításokhoz
    tab1, tab2 = st.tabs(["Mai beszállítások", "Holnapi beszállítások"])
    
    with tab1:
        if success:
            if today_result:
                # Összesítés
                total_quantity = sum(offer.get("confirmed_quantity", 0) for offer in today_result)
                total_value = sum(offer.get("confirmed_quantity", 0) * offer.get("confirmed_price", 0) for offer in today_result)
                
                st.write(f"### Összesen: {format_quantity(total_quantity)} - {format_price(total_value)}")
                
                # Táblázat készítése az ajánlatokról
                display_offer_table(today_result, pagination=False)
            else:
                # Show date info with no deliveries message
                st.info(f"Nincs mai beszállítás ({today.strftime('%Y. %m. %d.')})")
                
                # Check if there are any scheduled deliveries in near future
                next_days = 5  # Check next 5 days
                future_date = today + datetime.timedelta(days=next_days)
                
                future_params = {
                    "from_date": (today + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                    "to_date": future_date.strftime("%Y-%m-%d"),
                    "status": "FINALIZED,ACCEPTED_BY_USER"
                }
                
                success_future, future_result = offers_api.get_offers(params=future_params)
                
                if success_future and future_result:
                    # Group by date
                    future_by_date = {}
                    for offer in future_result:
                        del_date = offer.get("delivery_date", "")
                        if del_date:
                            if del_date not in future_by_date:
                                future_by_date[del_date] = []
                            future_by_date[del_date].append(offer)
                    
                    # Show upcoming deliveries by date
                    st.write("#### Közelgő beszállítások:")
                    for date, offers in sorted(future_by_date.items()):
                        try:
                            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d").date()
                            display_date = date_obj.strftime("%Y. %m. %d.")
                            day_diff = (date_obj - today).days
                            day_text = f"{day_diff} nap múlva" if day_diff > 1 else "Holnap"
                            
                            st.write(f"**{display_date}** ({day_text}): {len(offers)} beszállítás")
                        except:
                            st.write(f"**{date}**: {len(offers)} beszállítás")
        else:
            show_error(f"Hiba a beszállítások lekérésekor: {today_result}")
    
    with tab2:
        if success_tomorrow:
            if tomorrow_result:
                # Összesítés
                total_quantity = sum(offer.get("confirmed_quantity", 0) for offer in tomorrow_result)
                total_value = sum(offer.get("confirmed_quantity", 0) * offer.get("confirmed_price", 0) for offer in tomorrow_result)
                
                st.write(f"### Összesen: {format_quantity(total_quantity)} - {format_price(total_value)}")
                
                # Táblázat készítése az ajánlatokról
                display_offer_table(tomorrow_result, pagination=False)
            else:
                # Show date info with no deliveries message
                st.info(f"Nincs holnapi beszállítás ({tomorrow.strftime('%Y. %m. %d.')})")
                
                # Check if there are any scheduled deliveries in near future
                next_days = 7  # Check next 7 days after tomorrow
                future_date = tomorrow + datetime.timedelta(days=next_days)
                
                future_params = {
                    "from_date": (tomorrow + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                    "to_date": future_date.strftime("%Y-%m-%d"),
                    "status": "FINALIZED,ACCEPTED_BY_USER"
                }
                
                success_future, future_result = offers_api.get_offers(params=future_params)
                
                if success_future and future_result:
                    # Group by date
                    future_by_date = {}
                    for offer in future_result:
                        del_date = offer.get("delivery_date", "")
                        if del_date:
                            if del_date not in future_by_date:
                                future_by_date[del_date] = []
                            future_by_date[del_date].append(offer)
                    
                    # Show upcoming deliveries by date
                    st.write("#### Közelgő beszállítások:")
                    for date, offers in sorted(future_by_date.items()):
                        try:
                            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d").date()
                            display_date = date_obj.strftime("%Y. %m. %d.")
                            day_diff = (date_obj - today).days
                            day_text = f"{day_diff} nap múlva"
                            
                            st.write(f"**{display_date}** ({day_text}): {len(offers)} beszállítás")
                        except:
                            st.write(f"**{date}**: {len(offers)} beszállítás")
        else:
            show_error(f"Hiba a beszállítások lekérésekor: {tomorrow_result}")
    
    # Footer
    st.write("---")
    st.write("További funkciók eléréséhez használja az oldalsávon található menüt.")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=get_page_title("Ügyintézői Irányítópult"),
        page_icon="🏠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük az ügyintézői irányítópultot
    show_operator_dashboard()
