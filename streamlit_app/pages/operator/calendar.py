"""
Ügyintézői naptári né<PERSON>t oldal.
"""
import streamlit as st
import datetime
from datetime import datetime as dt, timedelta
import pandas as pd
import plotly.graph_objects as go
import io
import base64
import time
from api import offers as offers_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.formatting import format_quantity, format_price
import app_config as config
import plotly.express as px
from components.data_display import fetch_quality_grades
from components.calendar_component import render_css_calendar, render_interactive_calendar
from utils.responsive_ui import (
    create_responsive_columns, 
    detect_mobile, 
    get_theme_colors, 
    setup_responsive_ui, 
    render_responsive_calendar,
    render_responsive_tabs,
    show_responsive_dataframe,
    show_toast,
    render_section_card
)
from components.calendar_views import CalendarView, DailyView, WeeklyView, MonthlyView, CustomView

# Oldal beállítások
st.set_page_config(
    page_title=f"Naptári nézet - {config.APP_NAME}",
    page_icon="📅",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Reszponzív UI beállítása
setup_responsive_ui()

# Oldalsáv megjelenítése
render_sidebar()

# Gyorsítótárazási segédfüggvények
def get_cached_data(cache_key, max_age_minutes=30):
    """
    Adatok lekérése a gyorsítótárból
    
    Args:
        cache_key (str): Gyorsítótár kulcs
        max_age_minutes (int): Maximális életkor percben
        
    Returns:
        object: A gyorsítótárazott adat vagy None, ha nincs érvényes adat
    """
    if cache_key in st.session_state:
        cache_data = st.session_state[cache_key]
        if time.time() - cache_data['timestamp'] < max_age_minutes * 60:
            return cache_data['data']
    return None

def cache_data(cache_key, data):
    """
    Adatok mentése a gyorsítótárba
    
    Args:
        cache_key (str): Gyorsítótár kulcs
        data (object): Mentendő adat
    """
    st.session_state[cache_key] = {
        'data': data,
        'timestamp': time.time()
    }

# Dátumkezelési segédfüggvények
def parse_date(date_str):
    """
    Dátum string konvertálása datetime objektummá
    
    Args:
        date_str (str): Dátum string YYYY-MM-DD formátumban
        
    Returns:
        datetime.date: Dátum objektum vagy None, ha érvénytelen
    """
    try:
        return dt.strptime(date_str, "%Y-%m-%d").date()
    except (ValueError, TypeError):
        return None

def format_date(date_obj):
    """
    Dátum objektum formázása stringként
    
    Args:
        date_obj (datetime.date): Dátum objektum
        
    Returns:
        str: Formázott dátum string
    """
    if isinstance(date_obj, (datetime.date, dt)):
        return date_obj.strftime("%Y-%m-%d")
    return str(date_obj)

def format_quantity(quantity):
    """
    Mennyiség formázása.
    
    Args:
        quantity (float): A formázandó mennyiség
        
    Returns:
        str: A formázott mennyiség
    """
    return f"{quantity:.2f} kg"

def format_price(price):
    """
    Ár formázása.
    
    Args:
        price (float): A formázandó ár
        
    Returns:
        str: A formázott ár
    """
    return f"{price:,.0f} Ft".replace(",", " ")

def get_week_start(date):
    """
    Hét kezdő dátumának meghatározása.
    
    Args:
        date (datetime.date): A dátum
        
    Returns:
        datetime.date: A hét kezdő dátuma
    """
    return date - timedelta(days=date.weekday())

def get_week_end(date):
    """
    Hét záró dátumának meghatározása.
    
    Args:
        date (datetime.date): A dátum
        
    Returns:
        datetime.date: A hét záró dátuma
    """
    return date + timedelta(days=6-date.weekday())

def get_month_start(date):
    """
    Hónap kezdő dátumának meghatározása.
    
    Args:
        date (datetime.date): A dátum
        
    Returns:
        datetime.date: A hónap kezdő dátuma
    """
    return datetime.date(date.year, date.month, 1)

def get_month_end(date):
    """
    Hónap záró dátumának meghatározása.
    
    Args:
        date (datetime.date): A dátum
        
    Returns:
        datetime.date: A hónap záró dátuma
    """
    if date.month == 12:
        return datetime.date(date.year + 1, 1, 1) - timedelta(days=1)
    else:
        return datetime.date(date.year, date.month + 1, 1) - timedelta(days=1)

# Adatfeldolgozási segédfüggvények
def process_calendar_events(response):
    """
    Naptári események feldolgozása.
    
    Args:
        response (tuple): A naptári események API válasza (success, data)
        
    Returns:
        list: A feldolgozott események listája
    """
    # Ha nincs válasz
    if not response or not isinstance(response, tuple):
        print("Üres vagy érvénytelen válasz érkezett az API-tól")
        return []
    
    success, data = response
    if not success or not data:
        print("Sikertelen API válasz vagy üres adat")
        return []
    
    # Debug információk
    print(f"API válasz típusa: {type(data)}")
    if isinstance(data, dict):
        print(f"API válasz kulcsai: {list(data.keys())}")
    elif isinstance(data, list) and data:
        print(f"API válasz elemek száma: {len(data)}")
        if len(data) > 0 and isinstance(data[0], dict):
            print(f"Első elem kulcsai: {list(data[0].keys())}")
            # Ellenőrizzük a dátummezőket az első elemben
            for date_field in ['delivery_date', 'date', 'created_at', 'beszallitas', 'szallitasi_datum']:
                if date_field in data[0]:
                    print(f"Talált dátummező: {date_field} = {data[0][date_field]}")
    
    processed_events = []
    
    # Ha a válasz egy szótár (dátum -> ajánlatok)
    if isinstance(data, dict):
        for date, offers in data.items():
            if isinstance(offers, list):
                for offer in offers:
                    try:
                        # Kötelező mezők ellenőrzése
                        required_fields = ['id', 'product_type_id', 'quantity_in_kg']
                        if not all(field in offer for field in required_fields):
                            print(f"Hiányzó kötelező mezők: {[f for f in required_fields if f not in offer]}")
                            continue
                        
                        # Mennyiség konvertálása
                        quantity = float(offer['quantity_in_kg'])
                        if quantity <= 0:
                            print(f"Érvénytelen mennyiség: {quantity}")
                            continue
                        
                        # Ár konvertálása
                        price = float(offer.get('confirmed_price', 0))
                        if price < 0:
                            price = 0
                        
                        # Dátum konvertálása
                        try:
                            # Próbáljuk a date kulcsot, ami szöveges formátumban lehet
                            if isinstance(date, str):
                                delivery_date = pd.to_datetime(date).date()
                            else:
                                delivery_date = pd.to_datetime(date).date()
                        except Exception as date_error:
                            print(f"Hiba a dátum konvertálása során: {str(date_error)}")
                            # Próbáljuk meg más dátummezőkkel
                            try:
                                if 'delivery_date' in offer:
                                    delivery_date = pd.to_datetime(offer['delivery_date']).date()
                                elif 'date' in offer:
                                    delivery_date = pd.to_datetime(offer['date']).date()
                                else:
                                    print("Nem található érvényes dátum mező")
                                    continue
                            except Exception as e:
                                print(f"Nem sikerült a dátum konvertálása: {str(e)}")
                                continue
                        
                        # Esemény feldolgozása
                        processed_event = {
                            'id': offer['id'],
                            'delivery_date': delivery_date,
                            'product_type_id': offer['product_type_id'],
                            'product_name': offer.get('product_name', offer['product_type_id']),
                            'quality_grade_id': offer.get('quality_grade_id'),
                            'quantity_in_kg': quantity,
                            'confirmed_price': price,
                            'note': offer.get('note', ''),
                            'status': offer.get('status', ''),
                            'user_id': offer.get('user_id'),
                            'user_name': offer.get('user_name', 'Ismeretlen felhasználó'),
                            'created_by_user_id': offer.get('created_by_user_id')
                        }
                        
                        processed_events.append(processed_event)
                    except Exception as e:
                        print(f"Hiba az esemény feldolgozása során: {str(e)}")
                        continue
    
    # Ha a válasz egy lista
    elif isinstance(data, list):
        for offer in data:
            try:
                if not isinstance(offer, dict):
                    print(f"Érvénytelen elem a listában, nem szótár: {type(offer)}")
                    continue
                    
                # Kötelező mezők ellenőrzése - rugalmasabb kezelés
                required_count = 0
                for field in ['id', 'product_type_id', 'quantity_in_kg']:
                    if field in offer:
                        required_count += 1
                
                # Legalább az id és egy mennyiség vagy termék mező legyen meg
                if required_count < 2:
                    print(f"Túl kevés kötelező mező: {offer.keys()}")
                    continue
                
                # Dátum konvertálása - próbálunk többféle kulcsot
                delivery_date = None
                date_fields = ['delivery_date', 'date', 'created_at', 'beszallitas', 'szallitasi_datum']
                
                for date_field in date_fields:
                    if date_field in offer and offer[date_field]:
                        try:
                            delivery_date = pd.to_datetime(offer[date_field]).date()
                            print(f"Használt dátummező: {date_field} = {offer[date_field]} → {delivery_date}")
                            break
                        except Exception as e:
                            print(f"Hiba a {date_field} mező konvertálása során: {str(e)}")
                
                if delivery_date is None:
                    print(f"Nem található érvényes dátum mező: {list(offer.keys())}")
                    continue
                
                # Mennyiség konvertálása
                quantity = 0
                try:
                    if 'quantity_in_kg' in offer:
                        quantity = float(offer['quantity_in_kg'])
                    elif 'quantity' in offer:
                        quantity = float(offer['quantity'])
                except Exception as e:
                    print(f"Hiba a mennyiség konvertálása során: {str(e)}")
                
                # Product mező rugalmas kezelése
                product_type = ""
                if 'product_type_id' in offer:
                    product_type = offer['product_type_id']
                elif 'product_type' in offer and isinstance(offer['product_type'], dict):
                    product_type = offer['product_type'].get('name', '')
                
                # Ár konvertálása
                price = 0
                try:
                    if 'confirmed_price' in offer:
                        price = float(offer['confirmed_price'])
                    elif 'price' in offer:
                        price = float(offer['price'])
                except Exception as e:
                    print(f"Hiba az ár konvertálása során: {str(e)}")
                
                if price < 0:
                    price = 0
                
                # Esemény feldolgozása
                processed_event = {
                    'id': offer.get('id', ''),
                    'delivery_date': delivery_date,
                    'product_type_id': product_type,
                    'product_name': offer.get('product_name', product_type),
                    'quality_grade_id': offer.get('quality_grade_id', ''),
                    'quantity_in_kg': quantity,
                    'confirmed_price': price,
                    'note': offer.get('note', ''),
                    'status': offer.get('status', ''),
                    'user_id': offer.get('user_id', ''),
                    'user_name': offer.get('user_name', 'Ismeretlen felhasználó'),
                    'created_by_user_id': offer.get('created_by_user_id', '')
                }
                
                processed_events.append(processed_event)
            except Exception as e:
                print(f"Hiba az esemény feldolgozása során: {str(e)}")
                continue
    
    print(f"Feldolgozott események száma: {len(processed_events)}")
    if processed_events:
        # Debug: írjuk ki az első pár feldolgozott esemény adatait
        for i, event in enumerate(processed_events[:3]):
            print(f"Esemény {i+1}: id={event['id']}, date={event['delivery_date']}, type={event['product_type_id']}")
    
    return processed_events

# Biztonságos API hívás kezelése
def safe_api_call(api_func, params, fallback_func=None):
    """
    Biztonságos API hívás kezelése fallback opcióval
    
    Args:
        api_func (function): API függvény
        params (dict): API paraméterek
        fallback_func (function, optional): Fallback API függvény
        
    Returns:
        tuple: (success, result)
    """
    try:
        success, result = api_func(params)
        if success and result:
            return True, result
    except Exception as e:
        print(f"API hívás hiba: {e}")
    
    if fallback_func:
        try:
            fallback_success, fallback_result = fallback_func(params)
            if fallback_success and fallback_result:
                return True, fallback_result
        except Exception as e:
            print(f"Fallback API hívás hiba: {e}")
    
    return False, []

# Function to generate a download link for a DataFrame
def get_download_link(df, filename, link_text, file_format="csv"):
    """
    Letöltési link generálása DataFrame-hez.
    
    Args:
        df (pd.DataFrame): A letöltendő DataFrame
        filename (str): A fájl neve
        link_text (str): A link szövege
        file_format (str): A fájl formátuma (csv vagy xlsx)
        
    Returns:
        str: A letöltési link HTML kódja
    """
    if file_format == "csv":
        csv = df.to_csv(index=False)
        b64 = base64.b64encode(csv.encode()).decode()
        mime_type = "text/csv"
        file_ext = "csv"
    else:  # xlsx
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False)
        b64 = base64.b64encode(output.getvalue()).decode()
        mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        file_ext = "xlsx"
    
    href = f'<a href="data:{mime_type};base64,{b64}" download="{filename}.{file_ext}">{link_text}</a>'
    return href

def show_export_options(df, date_label):
    """
    Exportálási opciók megjelenítése.
    
    Args:
        df (pd.DataFrame): Az exportálandó DataFrame
        date_label (str): A dátum címke
    """
    st.write("### Exportálás")
    
    # Reszponzív elrendezés a letöltési gombokhoz
    export_cols = create_responsive_columns([1, 1])
    
    with export_cols[0]:
        st.markdown(
            get_download_link(
                df,
                f"beszallitasok_{date_label}",
                "📥 Letöltés CSV formátumban",
                "csv"
            ),
            unsafe_allow_html=True
        )
    
    with export_cols[1]:
        st.markdown(
            get_download_link(
                df,
                f"beszallitasok_{date_label}",
                "📥 Letöltés Excel formátumban",
                "xlsx"
            ),
            unsafe_allow_html=True
        )

def show_operator_calendar():
    """
    Ügyintézői naptári nézet megjelenítése.
    """
    st.header("📅 Naptári nézet")
    
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    screen_width = st.session_state.get("screen_width", 1200)
    
    # Mai dátum
    today = datetime.date.today()
    
    # Session state inicializálása
    if 'selected_days' not in st.session_state:
        st.session_state.selected_days = []
    
    # Interaktív naptár beállítása alapértelmezetten (nem mobil eszközön)
    if 'use_interactive_calendar' not in st.session_state:
        st.session_state.use_interactive_calendar = not (is_mobile or is_tablet)
        
    # Kártyaalapú nézet beállítása alapértelmezetten (mobil eszközön)
    if 'use_card_view' not in st.session_state:
        st.session_state.use_card_view = is_mobile or is_tablet
        
    # Szűrési beállítások inicializálása
    if 'filter_product_types' not in st.session_state:
        st.session_state.filter_product_types = []
    if 'filter_statuses' not in st.session_state:
        st.session_state.filter_statuses = []
    
    # Felső szűrők és vezérlők - reszponzív elrendezés
    filter_cols = create_responsive_columns([3, 1])
    
    with filter_cols[0]:
        # Nézet választó
        view_options = ["Napi", "Heti", "Havi", "Egyedi"]
        
        # Mobilon radio gombok helyett selectbox
        if is_mobile:
            view_type = st.selectbox(
                "Nézet típusa",
                options=view_options,
                index=2  # Havi nézet az alapértelmezett
            )
        else:
            view_type = st.radio(
                "Nézet típusa",
                options=view_options,
                horizontal=True,
                index=2  # Havi nézet az alapértelmezett
            )
    
    with filter_cols[1]:
        # Interaktív naptár kapcsoló (csak nem mobil eszközön)
        if not is_mobile and not is_tablet:
            use_interactive = st.checkbox("Interaktív naptár", value=st.session_state.use_interactive_calendar, key="use_interactive_calendar")
            use_card_view = st.checkbox("Kártyák", value=st.session_state.use_card_view, key="use_card_view")
        else:
            st.session_state.use_interactive_calendar = False
            st.session_state.use_card_view = True
            use_interactive = False
            use_card_view = True
            # Csak infóként jelezzük
            st.info("Mobilbarát nézet")

    # Dátum választó az aktuális nézetnek megfelelően
    today = dt.now().date()
    
    if view_type == "Napi":
        date_col1, date_col2 = create_responsive_columns([1, 1])
        with date_col1:
            start_date = st.date_input("Dátum", today)
            end_date = start_date
            
            # Alapértelmezetten a kiválasztott napot adjuk a selected_days-hez
            if not st.session_state.selected_days or len(st.session_state.selected_days) != 1 or st.session_state.selected_days[0] != start_date:
                st.session_state.selected_days = [start_date]
            
    elif view_type == "Heti":
        # Aktuális hét kezdete és vége
        current_week_start = get_week_start(today)
        current_week_end = get_week_end(today)
        
        # Hetek listája
        weeks = []
        for i in range(-4, 5):  # 4 hét előre és 4 hét vissza
            week_start = current_week_start + timedelta(weeks=i)
            week_end = get_week_end(week_start)
            week_label = f"{week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}"
            weeks.append((week_start, week_end, week_label))
        
        # Hét választó
        with st.container():
            selected_week = st.selectbox(
                "Válasszon hetet",
                options=weeks,
                format_func=lambda x: x[2],
                index=4  # Az aktuális hét az alapértelmezett
            )
            
            start_date = selected_week[0]
            end_date = selected_week[1]
            
            # Napok inicializálása a hétre
            week_dates = []
            current = start_date
            while current <= end_date:
                week_dates.append(current)
                current += timedelta(days=1)
            
            # Alapértelmezetten a teljes hetet adjuk a selected_days-hez
            if (not st.session_state.selected_days or 
                len(st.session_state.selected_days) != len(week_dates) or 
                min(st.session_state.selected_days) != start_date or 
                max(st.session_state.selected_days) != end_date):
                st.session_state.selected_days = week_dates
            
    elif view_type == "Havi":
        # Aktuális hónap kezdete és vége
        current_month_start = get_month_start(today)
        current_month_end = get_month_end(today)
        
        # Hónapok listája
        months = []
        for i in range(-6, 7):  # 6 hónap előre és 6 hónap vissza
            # Hónap és év számolása
            year = current_month_start.year
            month = current_month_start.month + i
            
            # Hónap átcsordulás kezelése
            if month > 12:
                year += month // 12
                month = month % 12
                if month == 0:
                    month = 12
                    year -= 1
            elif month < 1:
                year += (month - 1) // 12
                month = ((month - 1) % 12) + 1
            
            month_start = datetime.date(year, month, 1)
            month_end = get_month_end(month_start)
            month_label = month_start.strftime("%Y-%m")
            months.append((month_start, month_end, month_label))
        
        # Hónap választó
        with st.container():
            selected_month = st.selectbox(
                "Válasszon hónapot",
                options=months,
                format_func=lambda x: x[2],
                index=6  # Az aktuális hónap az alapértelmezett
            )
            
            start_date = selected_month[0]
            end_date = selected_month[1]
            
            # Napok inicializálása a hónapra
            month_dates = []
            current = start_date
            while current <= end_date:
                month_dates.append(current)
                current += timedelta(days=1)
            
            # Alapértelmezetten a teljes hónapot adjuk a selected_days-hez
            if (not st.session_state.selected_days or 
                len(st.session_state.selected_days) != len(month_dates) or 
                min(st.session_state.selected_days) != start_date or 
                max(st.session_state.selected_days) != end_date):
                st.session_state.selected_days = month_dates
            
    else:  # Egyedi
        date_cols = create_responsive_columns([1, 1])
        with date_cols[0]:
            start_date = st.date_input("Kezdő dátum", today)
        with date_cols[1]:
            end_date = st.date_input("Befejező dátum", today + timedelta(days=7))
        
        # Napok inicializálása az egyedi tartományra
        custom_dates = []
        current = start_date
        while current <= end_date:
            custom_dates.append(current)
            current += timedelta(days=1)
        
        # Alapértelmezetten az összes napot kiválasztjuk az egyedi tartományban
        if (not st.session_state.selected_days or 
            len(st.session_state.selected_days) != len(custom_dates) or 
            min(st.session_state.selected_days) != start_date or 
            max(st.session_state.selected_days) != end_date):
            st.session_state.selected_days = custom_dates
    
    # API hívás paramétereinek mentése a session state-be debug célokból
    api_params = {
        "date_from": start_date.strftime("%Y-%m-%d"),
        "date_to": end_date.strftime("%Y-%m-%d")
    }
    st.session_state.last_api_params = api_params
    
    # Naptári események betöltése a kiválasztott időszakra
    with st.spinner("Adatok betöltése..."):
        success, data = load_calendar_events(start_date, end_date)
    
    # Az eredeti API válasz mentése a debug módhoz
    if 'api_response' not in st.session_state:
        st.session_state.api_response = None
    
    # Debug mód kapcsoló
    debug_expander = st.sidebar.expander("Debug lehetőségek", expanded=False)
    with debug_expander:
        debug_mode = st.checkbox("Debug mód", value=False)
    
    if debug_mode:
        def debug_content():
            st.write(f"Lekérdezés sikeressége: {success}")
            st.write(f"Kezdő dátum: {start_date}, Befejező dátum: {end_date}")
            st.write(f"API paraméterek: {api_params}")
            st.write(f"Nézet típusa: {view_type}")
            st.write(f"Interaktív naptár használata: {st.session_state.get('use_interactive_calendar', False)}")
            st.write(f"Mobileszköz: {is_mobile}")
            st.write(f"Tablet: {is_tablet}")
            st.write(f"Képernyő szélesség: {screen_width}px")
            st.write(f"Kiválasztott napok száma: {len(st.session_state.selected_days)}")
            if st.session_state.selected_days:
                st.write(f"Kiválasztott napok: {', '.join([d.strftime('%Y-%m-%d') for d in st.session_state.selected_days[:5]])}{', ...' if len(st.session_state.selected_days) > 5 else ''}")
                
            # API adatok debuggolása
            if isinstance(data, pd.DataFrame) and not data.empty:
                st.write("### API adatok elemzése")
                st.write(f"DataFrame oszlopok: {data.columns.tolist()}")
                st.write("Első sor adatai:")
                first_row = data.iloc[0].to_dict()
                for col, val in first_row.items():
                    st.write(f"- {col}: {val}")
        
        # Debug információk megjelenítése kártyaként
        render_section_card(
            title="Debug információk",
            content=debug_content,
            color="#F44336",  # Piros
            icon="🛠️",
            key="debug_card",
            expanded=True
        )
    
    # Hiba esetén hibaüzenet megjelenítése
    if not success:
        show_error(f"Hiba történt az adatok betöltésekor: {data}")
        show_toast(f"Hiba az adatok betöltésekor: {data}", type="error")
        return
    
    # Ha a DataFrame üres, jelezzük
    if isinstance(data, pd.DataFrame) and data.empty:
        show_info("Nincs esemény a kiválasztott időszakban.")
        show_toast("Nincs esemény a kiválasztott időszakban.", type="info")
    
    # Szűrők hozzáadása - szekcióként, hogy ne foglaljon túl sok helyet
    def filter_content():
        
        # Oszlopok a szűrőkhöz - reszponzív
        filter_cols = create_responsive_columns([1, 1])
        
        with filter_cols[0]:
            # Termék típus szűrő
            if isinstance(data, pd.DataFrame) and not data.empty and 'product_type_id' in data.columns:
                available_product_types = data['product_type_id'].unique().tolist()
                selected_product_types = st.multiselect(
                    "Termék típusok",
                    options=available_product_types,
                    default=st.session_state.filter_product_types
                )
                st.session_state.filter_product_types = selected_product_types
            else:
                selected_product_types = []
                
        with filter_cols[1]:
            # Állapot szűrő
            if isinstance(data, pd.DataFrame) and not data.empty and 'status' in data.columns:
                available_statuses = data['status'].unique().tolist()
                selected_statuses = st.multiselect(
                    "Állapotok",
                    options=available_statuses,
                    default=st.session_state.filter_statuses
                )
                st.session_state.filter_statuses = selected_statuses
            else:
                selected_statuses = []
        
        # Szűrés gombjai - reszponzív módon
        button_cols = create_responsive_columns([1, 1])
        
        with button_cols[0]:
            # Szűrés alkalmazása gomb
            apply_filter = st.button("Szűrés alkalmazása", use_container_width=True, type="primary")
        
        with button_cols[1]:
            # Szűrés törlése gomb
            clear_filter = st.button("Szűrés törlése", use_container_width=True)
            if clear_filter:
                st.session_state.filter_product_types = []
                st.session_state.filter_statuses = []
                selected_product_types = []
                selected_statuses = []
                st.rerun()
    
    # Szűrők szekció
    render_section_card(
        title="Szűrési lehetőségek",
        content=filter_content,
        color="#2196F3",  # Kék
        icon="🔍",
        key="filters_card",
        expanded=False
    )
    
    # Adatok szűrése a kiválasztott feltételek alapján
    filtered_data = data
    
    if isinstance(filtered_data, pd.DataFrame) and not filtered_data.empty:
        # Termék típus szűrés
        if st.session_state.filter_product_types:
            filtered_data = filtered_data[filtered_data['product_type_id'].isin(st.session_state.filter_product_types)]
        
        # Állapot szűrés
        if st.session_state.filter_statuses:
            filtered_data = filtered_data[filtered_data['status'].isin(st.session_state.filter_statuses)]
    
    # Ha a szűrt adatok üresek, jelezzük
    if isinstance(filtered_data, pd.DataFrame) and filtered_data.empty:
        show_warning("A szűrési feltételeknek megfelelő ajánlat nem található.")
        show_toast("A szűrési feltételeknek megfelelő ajánlat nem található.", type="warning")
    
    # Aktív szűrők kijelzése
    if st.session_state.filter_product_types or st.session_state.filter_statuses:
        filters = []
        if st.session_state.filter_product_types:
            filters.append(f"Termék típusok: {', '.join(st.session_state.filter_product_types)}")
        if st.session_state.filter_statuses:
            filters.append(f"Állapotok: {', '.join(st.session_state.filter_statuses)}")
        
        # Aktív szűrők megjelenítése
        st.info(f"Aktív szűrők: {'; '.join(filters)}")
    
    # Megfelelő naptár nézet használata a view_type alapján
    use_interactive = st.session_state.get("use_interactive_calendar", False)
    use_card_view = st.session_state.get("use_card_view", is_mobile or is_tablet)
    
    if view_type == "Napi":
        calendar_view = DailyView(
            filtered_data,
            (start_date, start_date),
            use_interactive=use_interactive,
            card_only=use_card_view
        )
        calendar_view.show()
        
    elif view_type == "Heti":
        calendar_view = WeeklyView(
            filtered_data,
            (start_date, end_date),
            use_interactive=use_interactive,
            card_only=use_card_view
        )
        calendar_view.show()
        
    elif view_type == "Havi":
        calendar_view = MonthlyView(
            filtered_data,
            (start_date, end_date),
            use_interactive=use_interactive,
            card_only=use_card_view
        )
        calendar_view.show()
        
    else:  # Egyedi
        calendar_view = CustomView(
            filtered_data,
            (start_date, end_date),
            use_interactive=use_interactive,
            card_only=use_card_view
        )
        calendar_view.show()

def load_calendar_events(start_date, end_date):
    """
    Naptári események betöltése az API-tól.
    
    Args:
        start_date (datetime.date): A kezdő dátum
        end_date (datetime.date): A záró dátum
        
    Returns:
        tuple: (success, result) ahol success egy boolean és result vagy egy DataFrame vagy egy hibaüzenet
    """
    # Debug információk
    print(f"Dátum paraméterek: start_date={start_date}, end_date={end_date}")
    
    # Ellenőrizzük a bemenő dátumokat
    if not isinstance(start_date, (datetime.date, datetime.datetime)):
        print(f"Érvénytelen kezdő dátum típus: {type(start_date)}")
        try:
            # Próbáljuk konvertálni, ha stringként jött
            if isinstance(start_date, str):
                start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
            else:
                return False, "Érvénytelen kezdő dátum formátum"
        except Exception as e:
            print(f"Hiba a kezdő dátum konvertálásakor: {str(e)}")
            return False, "Érvénytelen kezdő dátum formátum"
    
    if not isinstance(end_date, (datetime.date, datetime.datetime)):
        print(f"Érvénytelen befejező dátum típus: {type(end_date)}")
        try:
            # Próbáljuk konvertálni, ha stringként jött
            if isinstance(end_date, str):
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
            else:
                return False, "Érvénytelen befejező dátum formátum"
        except Exception as e:
            print(f"Hiba a befejező dátum konvertálásakor: {str(e)}")
            return False, "Érvénytelen befejező dátum formátum"
    
    # Biztosítsuk, hogy datetime.date objektumok legyenek
    if isinstance(start_date, datetime.datetime):
        start_date = start_date.date()
    if isinstance(end_date, datetime.datetime):
        end_date = end_date.date()
    
    # API paraméterek előkészítése (az API date_from és date_to neveket vár)
    params = {
        "date_from": start_date.strftime("%Y-%m-%d"),
        "date_to": end_date.strftime("%Y-%m-%d")
    }
    
    # Gyorsítótárazott adatok ellenőrzése
    cache_key = f"calendar_data_{params['date_from']}_{params['date_to']}"
    cached_data = get_cached_data(cache_key, max_age_minutes=5)  # 5 perces gyorsítótárazás
    
    if cached_data is not None:
        print(f"Gyorsítótárazott adatok használata: {cache_key}")
        return True, cached_data
    
    print(f"API paraméterek: {params}")
    
    # API hívás
    success, response = offers_api.get_offers(params)  # Használjuk a get_offers API-t
    
    # Mentsük el a nyers API választ a debug-hoz
    st.session_state.api_response = response
    
    print(f"API válasz sikeressége: {success}")
    print(f"API válasz típusa: {type(response)}")
    
    if not success:
        print(f"API hiba: {response}")
        return False, response
    
    # Üres válasz kezelése
    if isinstance(response, list) and not response:
        print("Üres lista érkezett a szerverről")
        return True, pd.DataFrame()
    
    # Ha sikeres a válasz, átalakítjuk DataFrame-mé
    try:
        processed_events = process_calendar_events((success, response))
        if processed_events:
            df = pd.DataFrame(processed_events)
            
            # Ellenőrizzük a delivery_date oszlopot
            if 'delivery_date' in df.columns:
                # Győződjünk meg róla, hogy a delivery_date datetime típusú
                if not pd.api.types.is_datetime64_any_dtype(df['delivery_date']):
                    df['delivery_date'] = pd.to_datetime(df['delivery_date'])
                
                # A delivery_date típusa és értékei (debug)
                print(f"delivery_date oszlop típusa: {df['delivery_date'].dtype}")
                print(f"delivery_date első pár értéke: {df['delivery_date'].head()}")
            
            # Adatok mentése a gyorsítótárba
            cache_data(cache_key, df)
            
            return True, df  # Minden eseményt visszaadunk, a szűrést majd a nézetek végzik
        else:
            print("Nem sikerült feldolgozni az eseményeket")
            return True, pd.DataFrame()
    except Exception as e:
        print(f"Hiba az adatok feldolgozása során: {str(e)}")
        return False, f"Hiba az adatok feldolgozása során: {str(e)}"

def main():
    """
    Fő függvény.
    """
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        show_toast("Bejelentkezés szükséges", type="error")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Ellenőrizzük, hogy ügyintéző vagy admin
    if user.get("role") not in ["ügyintéző", "admin"]:
        show_error("Az oldal megtekintéséhez ügyintézői jogosultság szükséges.")
        show_toast("Jogosultság szükséges", type="error")
        return
    
    # Oldal megjelenítése
    show_operator_calendar()

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    main()