"""
Aj<PERSON>latkezelés operátor oldalon - frissített verzió a hiányzó funkcionalitással együtt
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
import traceback
import json
import app_config as config
from api import offers as offers_api
from api import products as products_api
from api import users as users_api
from utils.session import get_current_user
from components.notification import show_info, show_error, show_success
from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date

# Logging beállítása
logger = logging.getLogger(__name__)

# Segédfüggvények a hiányzó funkcionalitáshoz

def display_offer_products(offer):
    """
    Ajánlathoz tartozó termékek táblázatos megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    st.subheader("Termékek")
    
    # Debug info
    print(f"==== LOADING PRODUCT DETAILS ====")
    print(f"Offer data: {offer}")
    
    # Get product details
    products = []
    product_type_id = offer.get('product_type_id')
    quality_grade_id = offer.get('quality_grade_id')
    
    print(f"Product type ID: {product_type_id}")
    print(f"Quality grade ID: {quality_grade_id}")
    
    if product_type_id:
        try:
            # Get product type details
            print("Fetching product type details...")
            success, product_type = products_api.get_product_type(product_type_id)
            if success:
                print(f"Product type details: {product_type}")
                
                # Kategória név kinyerése
                category_name = "N/A"
                if "category" in product_type and product_type["category"]:
                    category_name = product_type["category"].get("name", "N/A")
                
                # Get quality grade details if available
                quality_grade_name = "N/A"
                quality_grade = None
                
                # 1. Próba: API hívás, ha implementálva van
                if quality_grade_id and hasattr(products_api, 'get_quality_grade'):
                    try:
                        print("Fetching quality grade details...")
                        success, quality_grade = products_api.get_quality_grade(quality_grade_id)
                        if success:
                            quality_grade_name = quality_grade.get('name', 'N/A')
                            print(f"Quality grade details: {quality_grade}")
                    except Exception as e:
                        print(f"Error fetching quality grade: {str(e)}")
                
                # 2. Alternatíva: Minőségi osztály adatok kinyerése az ajánlatból
                if quality_grade_name == "N/A" and "quality_grade" in offer and offer["quality_grade"]:
                    quality_grade = offer["quality_grade"]
                    quality_grade_name = quality_grade.get("name", "N/A")
                    print(f"Using quality grade from offer: {quality_grade_name}")
                
                # Get quantity and price
                quantity = offer.get('quantity_in_kg', 0)
                price = offer.get('confirmed_price') or offer.get('price') or 0
                
                print(f"Quantity: {quantity}")
                print(f"Price: {price}")
                
                # Create product entry
                product = {
                    'name': product_type.get('name', 'N/A'),
                    'category': category_name,
                    'quality_grade': quality_grade_name,
                    'quantity': quantity,
                    'unit': 'kg',
                    'unit_price': price,
                    'total_price': float(quantity) * float(price) if price else 0
                }
                products.append(product)
                
                # Debug info
                print(f"Created product entry: {product}")
            else:
                print(f"Failed to get product type details: {product_type}")
        except Exception as e:
            print(f"Error fetching product details: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    if products:
        # Termékek DataFrame létrehozása
        df_products = pd.DataFrame(products)
        
        # Oszlopok átnevezése
        rename_dict = {
            "name": "Termék neve",
            "category": "Kategória",
            "quality_grade": "Minőségi osztály",
            "unit_price": "Egységár",
            "quantity": "Mennyiség",
            "unit": "Me.",
            "total_price": "Összérték"
        }
        
        available_columns = [col for col in rename_dict.keys() if col in df_products.columns]
        rename_available = {k: v for k, v in rename_dict.items() if k in available_columns}
        
        df_products.rename(columns=rename_available, inplace=True)
        
        # Formázások alkalmazása
        if "Egységár" in df_products.columns:
            df_products["Egységár"] = df_products["Egységár"].apply(lambda x: format_price(x) if x else "Nincs ár")
        
        if "Mennyiség" in df_products.columns:
            df_products["Mennyiség"] = df_products["Mennyiség"].apply(format_quantity)
        
        if "Összérték" in df_products.columns:
            df_products["Összérték"] = df_products["Összérték"].apply(lambda x: format_price(x) if x else "Nincs ár")
        
        # Táblázat megjelenítése
        display_columns = [col for col in ["Termék neve", "Kategória", "Minőségi osztály", "Mennyiség", "Me.", 
                                         "Egységár", "Összérték"] 
                         if col in df_products.columns]
        
        st.dataframe(df_products[display_columns], use_container_width=True)
        
        # Összesítés
        total_price = sum([p.get("total_price", 0) for p in products])
        if total_price > 0:
            st.markdown(f"**Ajánlat összértéke**: {format_price(total_price)}")
        else:
            st.markdown("**Ajánlat összértéke**: Nincs ár")
    else:
        st.info("Az ajánlat nem tartalmaz termékeket.")

def render_offer_edit_form(offer, offer_id):
    """
    Ajánlat szerkesztési űrlap megjelenítése.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    st.subheader("Ajánlat szerkesztése")
    
    # Szerkesztési űrlap
    with st.form(key=f"edit_offer_form_{offer_id}"):
        # Termék adatok szerkesztése
        st.subheader("Termék adatok")
        
        # Termék típus kiválasztása
        success, product_types = products_api.get_product_types()
        if success:
            product_type_options = {pt.get("id"): pt.get("name") for pt in product_types}
            selected_product_type = st.selectbox(
                "Termék típusa",
                options=list(product_type_options.keys()),
                format_func=lambda x: product_type_options.get(x, ""),
                index=list(product_type_options.keys()).index(offer.get("product_type_id")) if offer.get("product_type_id") in product_type_options else 0
            )
        
        # Mennyiség szerkesztése
        quantity = st.number_input(
            "Mennyiség (kg)",
            min_value=0.0,
            value=float(offer.get("quantity_in_kg", 0)),
            step=0.1
        )
        
        # Ár szerkesztése
        price = st.number_input(
            "Ár (Ft/kg)",
            min_value=0,
            value=int(offer.get("price", 0)),
            step=100
        )
        
        # Szállítási dátum szerkesztése
        delivery_date = st.date_input(
            "Szállítási dátum",
            value=datetime.strptime(offer.get("delivery_date"), "%Y-%m-%d").date() if offer.get("delivery_date") else None
        )
        
        # Megjegyzés szerkesztése
        note = st.text_area(
            "Megjegyzés",
            value=offer.get("note", "")
        )
        
        # Mentés gomb
        if st.form_submit_button("Mentés"):
            # Adatok összeállítása
            updated_data = {
                "product_type_id": selected_product_type,
                "quantity_in_kg": quantity,
                "price": price,
                "delivery_date": delivery_date.strftime("%Y-%m-%d"),
                "note": note
            }
            
            # API hívás a frissítéshez
            success, result = offers_api.update_offer(offer_id, updated_data)
            
            if success:
                show_success("Az ajánlat sikeresen frissítve!")
                # Cache frissítése
                st.session_state.offer_cache[offer_id] = result
                # Szerkesztési mód kikapcsolása
                del st.session_state["edit_mode"]
                st.rerun()
            else:
                show_error(f"Hiba az ajánlat frissítésekor: {result}")

def handle_status_transitions(offer, offer_id):
    """
    Ajánlat státusz átmenetek és műveletek kezelése.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    # Státusz kezelés és műveletek
    st.subheader("Műveletek")
    
    # Elérhető státusz átmenetek az aktuális státusz alapján
    available_transitions = {
        "CREATED": ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"],
        "CONFIRMED_BY_COMPANY": ["ACCEPTED_BY_USER", "REJECTED_BY_USER"],
        "ACCEPTED_BY_USER": ["FINALIZED"],
        "REJECTED_BY_USER": ["CREATED"],
        "FINALIZED": []
    }
    
    current_status = offer.get("status")
    possible_transitions = available_transitions.get(current_status, [])
    
    if possible_transitions:
        col1, col2 = st.columns([1, 3])
        
        with col1:
            selected_status = st.selectbox(
                "Új státusz",
                options=possible_transitions,
                format_func=lambda x: config.OFFER_STATUSES.get(x, {}).get("name", x)
            )
        
        with col2:
            # Ha CONFIRMED_BY_COMPANY státuszra váltunk, bekérjük a visszaigazolási adatokat
            confirmation_data = None
            if selected_status == "CONFIRMED_BY_COMPANY":
                st.write("Visszaigazolási adatok:")
                conf_col1, conf_col2 = st.columns(2)
                with conf_col1:
                    confirmed_quantity = st.number_input(
                        "Visszaigazolt mennyiség (kg)",
                        min_value=0.0,
                        value=float(offer.get("quantity_in_kg", 0)),
                        step=0.1
                    )
                with conf_col2:
                    confirmed_price = st.number_input(
                        "Visszaigazolt ár (Ft/kg)",
                        min_value=0,
                        value=int(offer.get("price", 0)),
                        step=100
                    )
                confirmation_data = {
                    "confirmed_quantity": confirmed_quantity,
                    "confirmed_price": confirmed_price
                }
            
            if st.button("Státusz frissítése"):
                try:
                    # Státusz frissítése
                    success, result = offers_api.update_offer_status(
                        offer_id, 
                        selected_status,
                        confirmation_data=confirmation_data if selected_status == "CONFIRMED_BY_COMPANY" else None
                    )
                    
                    if success:
                        show_success("Az ajánlat státusza sikeresen frissítve!")
                        # Cache frissítése
                        if offer_id in st.session_state.offer_cache:
                            st.session_state.offer_cache[offer_id]["status"] = selected_status
                            if confirmation_data:
                                st.session_state.offer_cache[offer_id].update(confirmation_data)
                        st.rerun()
                    else:
                        show_error(f"Hiba a státusz frissítésekor: {result}")
                except Exception as e:
                    logger.error(f"Error updating offer status: {str(e)}")
                    show_error(f"Hiba a státusz frissítésekor: {str(e)}")
    else:
        if current_status == "FINALIZED":
            st.info("Ez az ajánlat már véglegesítve van, további státuszváltás nem lehetséges.")
        else:
            st.warning("Az aktuális státuszból nincs elérhető státuszváltás.")

def handle_additional_actions(offer, offer_id):
    """
    További ajánlatkezelési műveletek implementálása.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    # További műveletek
    st.subheader("További műveletek")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Ajánlat részletes nyomtatása"):
            # TODO: Nyomtatási funkció
            show_info("A nyomtatási funkció jelenleg fejlesztés alatt áll.")
    
    with col2:
        current_status = offer.get("status")
        if current_status not in ["ACCEPTED_BY_USER", "FINALIZED"]:
            if st.button("Ajánlat törlése", type="primary", use_container_width=True):
                # Törlés megerősítése
                confirm = st.checkbox("Erősítse meg a törlést!")
                
                if confirm:
                    try:
                        success, result = offers_api.delete_offer(offer_id)
                        
                        if success:
                            show_success("Az ajánlat sikeresen törölve!")
                            # Cache frissítése
                            if offer_id in st.session_state.offer_cache:
                                del st.session_state.offer_cache[offer_id]
                            # Visszalépés a listához
                            if "selected_offer_id" in st.session_state:
                                del st.session_state["selected_offer_id"]
                            st.rerun()
                        else:
                            show_error(f"Hiba az ajánlat törlésekor: {result}")
                    except Exception as e:
                        logger.error(f"Error deleting offer: {str(e)}")
                        show_error(f"Hiba az ajánlat törlésekor: {str(e)}")


# Főfüggvény a hiányzó funkcionalitás integrálásával
def show_offer_detail(offer_id):
    """
    Ajánlat részletes megjelenítése és kezelése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Visszalépési lehetőség
    if st.button("← Vissza a listához"):
        if "selected_offer_id" in st.session_state:
            del st.session_state["selected_offer_id"]
        if "edit_mode" in st.session_state:
            del st.session_state["edit_mode"]
        st.rerun()
    
    # Debug info
    print(f"==== LOADING OFFER DETAILS ====")
    print(f"Offer ID: {offer_id}")
    
    # Ajánlat részletes adatainak lekérése (ha nincs a cache-ben)
    if "offer_cache" not in st.session_state:
        st.session_state.offer_cache = {}
        
    if offer_id not in st.session_state.offer_cache:
        print("Offer not in cache, fetching from API...")
        success, result = offers_api.get_offer(offer_id)
        if success:
            print(f"API response: {result}")
            st.session_state.offer_cache[offer_id] = result
        else:
            st.error(f"Hiba az ajánlat részleteinek betöltésekor: {result}")
            return
    
    offer = st.session_state.offer_cache[offer_id]
    print(f"Offer data: {offer}")
    
    # Ajánlat címe és azonosítója
    st.header(f"Ajánlat (#{offer_id})")
    
    # Státusz megjelenítése
    status = offer.get("status")
    status_details = config.OFFER_STATUSES.get(status, {})
    status_color = status_details.get("color", "#808080")
    status_name = status_details.get("name", status)
    
    st.markdown(f"""
    <div style="
        background-color: {status_color}; 
        color: white; 
        padding: 10px; 
        border-radius: 5px; 
        margin-bottom: 20px;
        display: inline-block;
        font-weight: bold;">
        {status_name}
    </div>
    """, unsafe_allow_html=True)
    
    # Szerkesztési mód ellenőrzése
    is_edit_mode = st.session_state.get("edit_mode", False)
    
    if is_edit_mode:
        # Szerkesztési űrlap megjelenítése
        render_offer_edit_form(offer, offer_id)
    else:
        # Alapinformációk megjelenítése
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Alap információk")
            
            # Get producer details if available
            producer_id = offer.get('user_id')
            producer_details = {}
            if producer_id:
                try:
                    success, producer = users_api.get_user(producer_id)
                    if success:
                        producer_details = producer
                except Exception as e:
                    print(f"Error fetching producer details: {str(e)}")
            
            st.markdown(f"""
            - **Termelő**: {producer_details.get('contact_name', offer.get('contact_name', 'N/A'))}
            - **Email**: {producer_details.get('email', offer.get('user_email', 'N/A'))}
            - **Telefonszám**: {producer_details.get('phone_number', offer.get('contact_phone', 'N/A'))}
            - **Létrehozva**: {format_datetime(offer.get('created_at'))}
            - **Frissítve**: {format_datetime(offer.get('updated_at'))}
            """)
        
        with col2:
            st.subheader("Szállítási részletek")
            st.markdown(f"""
            - **Szállítási dátum**: {format_date(offer.get('delivery_date'))}
            - **Szállítási cím**: {offer.get('delivery_address', 'N/A')}
            - **Megjegyzés**: {offer.get('note', 'Nincs megjegyzés')}
            """)
    
        # Szerkesztés gomb
        if st.button("Ajánlat szerkesztése"):
            st.session_state.edit_mode = True
            st.rerun()
    
    # Termékek táblázata
    display_offer_products(offer)
    
    # Státuszváltás és műveletek
    handle_status_transitions(offer, offer_id)
    
    # További műveletek
    handle_additional_actions(offer, offer_id)
    
    # Csatolmányok megjelenítése, ha elérhetőek
    try:
        # Ellenőrizzük, hogy a get_offer_attachments funkció elérhető-e
        if hasattr(offers_api, 'get_offer_attachments'):
            with st.spinner("Csatolmányok betöltése..."):
                success, attachments = offers_api.get_offer_attachments(offer_id)
                if success:
                    display_offer_attachments(attachments)
                else:
                    # Ha hiba történt az API hívás során
                    display_offer_attachments([])  # Üres lista = nincsenek csatolmányok
        else:
            # Ha az API funkció nem elérhető
            display_offer_attachments(None)  # None = API nem elérhető
    except Exception as e:
        # Váratlan hiba esetén
        logger.error(f"Error fetching attachments: {str(e)}")
        display_offer_attachments(None)

def display_offer_attachments(attachments_data=None):
    """
    Ajánlat csatolmányainak megjelenítése.
    
    Args:
        attachments_data (list/None): Csatolmányok adatai vagy None, ha nincs elérhető csatolmány
    """
    st.subheader("Csatolmányok")
    
    # Ha nincs elérhető API, akkor tájékoztatás
    if attachments_data is None:
        st.info("A csatolmányok kezelése jelenleg fejlesztés alatt áll.")
        st.warning("A csatolmányok API végpont még nem érhető el a rendszerben.")
        return
    
    # Ha nincs csatolmány
    if not attachments_data:
        st.info("Az ajánlathoz még nem tartoznak csatolmányok.")
        return
    
    # Csatolmányok listázása
    for attachment in attachments_data:
        col1, col2 = st.columns([3, 1])
        with col1:
            st.markdown(f"**{attachment.get('filename', 'Ismeretlen fájl')}**")
            st.markdown(f"Feltöltve: {format_datetime(attachment.get('created_at', ''))}")
        with col2:
            if st.button("Letöltés", key=f"download_{attachment.get('id', 'unknown')}"):
                st.info("A letöltési funkció implementálása folyamatban...")