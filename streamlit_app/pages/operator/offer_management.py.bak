#!/usr/bin/env python3
"""
Ajánlatok részletes kezelése ügyintézők számára.
"""
import streamlit as st
import uuid
import time
import logging
from datetime import datetime, timedelta
import json

# App imports
import app_config as config
from api import offers as offers_api
from api import products as products_api
from api import users as users_api
from utils.session import is_authenticated, get_current_user
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date
from components.data_display import display_offer_table, fetch_users, fetch_products, fetch_categories
from components.display_data_components import (
    display_offer_status_card, 
    display_offer_detail_cards,
    display_offer_timeline,
    display_offer_actions,
    display_status_history,
    display_quantity_indicator,
    display_offer_attachments
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def format_date_for_api(date_obj, add_day=False):
    """
    Konzisztens dátumformázás az API hívásokhoz.
    
    Args:
        date_obj (date/datetime): A formázandó dátum.
        add_day (bool): Hozzáadjon-e egy napot (záró dátumhoz hasznos).
    
    Returns:
        str: YYYY-MM-DD formátumú dátum string.
    """
    if date_obj is None:
        return None
        
    # Sztring konvertálása dátummá, ha szükséges
    if isinstance(date_obj, str):
        date_obj = datetime.strptime(date_obj, "%Y-%m-%d").date()
    
    # Egy nap hozzáadása, ha kérték
    if add_day:
        date_obj = date_obj + timedelta(days=1)
        
    # Formázás a szükséges formátumba
    return date_obj.strftime("%Y-%m-%d")

def prepare_filter_params(selected_producer_id, selected_status, from_date, to_date):
    """
    Szűrési paraméterek előkészítése API hívásokhoz.
    
    Args:
        selected_producer_id (str/int): A kiválasztott termelő azonosítója.
        selected_status (str/list): A kiválasztott státusz(ok).
        from_date (date/str): Kezdő dátum.
        to_date (date/str): Záró dátum.
    
    Returns:
        dict: Az API híváshoz használható paraméterek szótára.
    """
    query_params = {}
    
    # Termelő szűrés
    if selected_producer_id:
        query_params["user_id"] = selected_producer_id
    
    # Státusz szűrés
    if selected_status:
        if isinstance(selected_status, list):
            query_params["status"] = ",".join(selected_status)
        else:
            query_params["status"] = selected_status
    
    # Dátum szűrés
    if from_date and to_date:
        query_params["date_from"] = format_date_for_api(from_date)
        query_params["date_to"] = format_date_for_api(to_date, add_day=True)
    
    return query_params

def init_page_state():
    """Initialize page-specific session state variables if they don't exist"""
    # Session state változók listája, amit inicializálni kell
    state_vars = {
        "page_uuid": str(uuid.uuid4()),
        "needs_rerun": False,
        "offer_cache": {},
        "producers_cache": None,
        "producers_last_fetch": 0,
        "is_mobile": False,
        "is_tablet": False,
        "screen_width": 1200,  # Alapértelmezett, desktop méret
        "current_page": 1,
        "page_size": 20,
        "display_data_cache": {},
        "display_data_timestamp": time.time(),
        "selected_offer_ids": [],
        "selected_offer_index": None,
        "saved_filters": {},
        "lazy_loaded_data": {}  # Lazy loading adatok tárolása
    }
    
    # Ellenőrizzük és beállítjuk a hiányzó változókat
    for var_name, default_value in state_vars.items():
        if var_name not in st.session_state:
            st.session_state[var_name] = default_value

def get_producers():
    """
    Termelők betöltése cache-eléssel a jobb teljesítmény érdekében.
    
    Ez a függvény a lazy_load_cache segítségével tölti be a termelők adatait,
    elkerülve a felesleges API hívásokat, ha az adatok még elérhetők a gyorsítótárban.
    
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    return lazy_load_cache(
        cache_key="producers_list",
        data_loader_func=lambda: users_api.get_users(params={"role": "termelő"}),
        cache_ttl=300  # 5 perc
    )

def render_producer_filter(page_id):
    """
    Termelő szűrő komponens renderelése.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        str/int/None: A kiválasztott termelő azonosítója vagy None.
    """
    success_users, result_users = get_producers()
    
    if success_users:
        producers = result_users
        producer_options = [{"id": None, "contact_name": "Minden termelő"}] + producers
        
        selected_producer = st.selectbox(
            "Termelő", 
            options=producer_options,
            format_func=lambda x: x.get("contact_name") if x else "Minden termelő",
            key=f"producer_filter_{page_id}"
        )
        
        return selected_producer.get("id") if selected_producer else None
    else:
        st.error("Nem sikerült betölteni a termelőket.")
        return None

def render_status_filter(page_id):
    """
    Státusz szűrő komponens renderelése.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        str/None: A kiválasztott státusz vagy None.
    """
    status_options = [None] + list(config.OFFER_STATUSES.keys())
    
    selected_status = st.selectbox(
        "Státusz", 
        options=status_options,
        format_func=lambda x: "Minden státusz" if x is None else format_status(x),
        key=f"status_filter_{page_id}"
    )
    
    return selected_status

def render_date_filter(page_id):
    """
    Dátum szűrő komponens renderelése.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        tuple: (from_date, to_date) a kiválasztott dátumtartomány.
    """
    date_filter_options = [
        "Mind", 
        "Mai nap", 
        "Holnap", 
        "Következő 7 nap", 
        "Következő 30 nap", 
        "Múlt 7 nap", 
        "Múlt 30 nap",
        "Egyedi időszak"
    ]
    
    selected_date_filter = st.selectbox(
        "Időszak", 
        options=date_filter_options, 
        key=f"date_filter_{page_id}"
    )
    
    today = datetime.now().date()
    from_date = None
    to_date = None
    
    if selected_date_filter == "Egyedi időszak":
        date_cols = st.columns(2)
        
        with date_cols[0]:
            from_date = st.date_input(
                "Kezdő dátum", 
                today - timedelta(days=7), 
                key=f"from_date_{page_id}"
            )
        
        with date_cols[1]:
            to_date = st.date_input(
                "Záró dátum", 
                today + timedelta(days=7), 
                key=f"to_date_{page_id}"
            )
    else:
        # Előre definiált dátum tartományok
        date_ranges = {
            "Mai nap": (today, today),
            "Holnap": (today + timedelta(days=1), today + timedelta(days=1)),
            "Következő 7 nap": (today, today + timedelta(days=7)),
            "Következő 30 nap": (today, today + timedelta(days=30)),
            "Múlt 7 nap": (today - timedelta(days=7), today),
            "Múlt 30 nap": (today - timedelta(days=30), today)
        }
        
        if selected_date_filter in date_ranges:
            from_date, to_date = date_ranges[selected_date_filter]
    
    return from_date, to_date

def render_offer_filters(page_id):
    """
    Az összes szűrő komponens renderelése és visszaadása.
    
    Args:
        page_id (str): Az oldal egyedi azonosítója a session key-ek egyediségéhez.
    
    Returns:
        tuple: (selected_producer_id, selected_status, from_date, to_date) a kiválasztott szűrők.
    """
    # Expander nélkül renderjük a szűrőket, mivel a függvényt már egy expanderen belül hívjuk
    filter_cols = st.columns(3)
    
    # Producer filter
    with filter_cols[0]:
        selected_producer_id = render_producer_filter(page_id)
    
    # Status filter
    with filter_cols[1]:
        selected_status = render_status_filter(page_id)
    
    # Date filter
    with filter_cols[2]:
        from_date, to_date = render_date_filter(page_id)
        
    return selected_producer_id, selected_status, from_date, to_date

def load_offers_with_pagination(query_params=None, page=1, page_size=20):
    """
    Ajánlatok betöltése lapozással a backend API-ból.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek. Defaults to None.
        page (int): Az aktuális oldal száma. Defaults to 1.
        page_size (int): Oldalankénti elemszám. Defaults to 20.
        
    Returns:
        tuple: (sikeres, eredmény) ahol eredmény szótár lapozási adatokkal vagy hibaüzenet
    """
    try:
        success, result = offers_api.get_offers_paginated(
            page=page, 
            page_size=page_size,
            params=query_params
        )
            
        if success:
            # Sikeres lekérdezés esetén a result egy szótár lapozási adatokkal
            return True, result
        
        # Hiba esetén a result egy hibaüzenet
        return False, result
    
    except Exception as e:
        return False, f"Hiba az ajánlatok betöltésekor: {str(e)}"

def fetch_offer_data_with_progress(query_params=None, page=1, page_size=20):
    """
    Ajánlatok betöltése vizuális folyamatjelzővel.
    
    Ez a függvény egy Streamlit progress bar-t jelenít meg, miközben
    betölti az ajánlatokat az API-ból, majd visszaadja az eredményt.
    
    Args:
        query_params (dict, optional): Szűrési paraméterek az API híváshoz.
        page (int, optional): Az aktuális oldal száma a lapozáshoz. Defaults to 1.
        page_size (int, optional): Oldalméret a lapozáshoz. Defaults to 20.
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    # Folyamatjelző megjelenítése
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Hálózati kérés előtti szöveg
    status_text.text("Ajánlatok betöltése...")
    
    try:
        # Cache kulcs generálása a lekérdezési paraméterekből
        cache_params = query_params.copy() if query_params else {}
        cache_params.update({"page": page, "page_size": page_size})
        cache_key = f"offers_{json.dumps(cache_params, sort_keys=True)}"
        
        # Adatok betöltése a lazy_load_cache segítségével
        success, result = lazy_load_cache(
            cache_key=cache_key,
            data_loader_func=lambda: load_offers_with_pagination(query_params, page, page_size),
            cache_ttl=120  # 2 perces cache az ajánlatokhoz (gyakran változhat)
        )
        
        # Folyamatjelző frissítése
        progress_bar.progress(50)
        
        if success:
            # További adatok előtöltése, ha szükséges
            if isinstance(result, dict) and "items" in result:
                offers = result["items"]
                
                # Kapcsolódó adatok előtöltése a háttérben (termelők, termékek)
                try:
                    producers_cache_key = "producers_list"
                    products_cache_key = "products_list"
                    
                    # Termelők betöltése a háttérben
                    lazy_load_cache(
                        cache_key=producers_cache_key,
                        data_loader_func=lambda: users_api.get_users(params={"role": "termelő"}),
                        cache_ttl=300  # 5 perc
                    )
                    
                    # Termékek betöltése a háttérben
                    lazy_load_cache(
                        cache_key=products_cache_key,
                        data_loader_func=lambda: products_api.get_product_types(),
                        cache_ttl=300  # 5 perc
                    )
                except Exception as e:
                    # Itt csak logolja a hibát, de nem szakítja meg a fő adatbetöltést
                    logger.error(f"Error preloading related data: {str(e)}")
                
                # Folyamatjelző frissítése
                progress_bar.progress(100)
                status_text.text(f"{len(offers)} ajánlat betöltve")
            else:
                progress_bar.progress(100)
                status_text.text("Adatok betöltve")
        else:
            progress_bar.progress(100)
            status_text.text(f"Hiba: {result}")
        
        # Várakozás, hogy a felhasználó lássa az eredményt
        time.sleep(0.5)
        
        # Folyamatjelző és státusz törlése
        progress_bar.empty()
        status_text.empty()
        
        return success, result
        
    except Exception as e:
        # Folyamatjelző és státusz törlése hiba esetén
        progress_bar.empty()
        status_text.empty()
        
        # Hiba visszaadása
        logger.error(f"Error in fetch_offer_data_with_progress: {str(e)}")
        return False, str(e)

def render_pagination_controls(current_page, total_pages, on_change):
    """
    Lapozó vezérlők renderelése.
    
    Args:
        current_page (int): Aktuális oldal száma
        total_pages (int): Összes oldalak száma
        on_change (callable): Függvény, amely meghívódik oldal váltásakor
    """
    cols = st.columns([1, 3, 1])
    
    with cols[0]:
        if current_page > 1:
            if st.button("⬅️ Előző", key="prev_page_btn"):
                on_change(current_page - 1)
    
    with cols[1]:
        page_numbers = []
        
        # Logika a megjelenítendő oldalszámokhoz
        if total_pages <= 7:
            page_numbers = list(range(1, total_pages + 1))
        else:
            # Mindig mutassuk az első, utolsó és aktuális körüli oldalakat
            if current_page <= 4:
                page_numbers = list(range(1, 6)) + ["..."] + [total_pages]
            elif current_page >= total_pages - 3:
                page_numbers = [1, "..."] + list(range(total_pages - 4, total_pages + 1))
            else:
                page_numbers = [1, "..."] + list(range(current_page - 1, current_page + 2)) + ["..."] + [total_pages]
        
        # Oldalszámok megjelenítése
        col_count = min(len(page_numbers), 9)  # Maximum 9 gombot jelenítünk meg
        page_cols = st.columns(col_count)
        
        for i, page_num in enumerate(page_numbers):
            with page_cols[i % col_count]:
                if page_num == "...":
                    st.markdown("...")
                else:
                    button_style = "primary" if page_num == current_page else "secondary"
                    if st.button(f"{page_num}", key=f"page_{page_num}", type=button_style):
                        on_change(page_num)
    
    with cols[2]:
        if current_page < total_pages:
            if st.button("Következő ➡️", key="next_page_btn"):
                on_change(current_page + 1)

def load_offers_with_filters(query_params):
    """
    Ajánlatok betöltése a megadott szűrőkkel.
    
    Args:
        query_params (dict): Az API híváshoz használandó paraméterek.
    
    Returns:
        tuple: (success, result) A sikert és az eredményt tartalmazza.
    """
    try:
        logger.info(f"Fetching offers with params: {query_params}")
        return offers_api.get_offers(params=query_params)
    except Exception as e:
        logger.error(f"Error loading offers: {str(e)}")
        return False, str(e)

def handle_api_error(error, operation_type):
    """
    API hibák egységes kezelése részletes hibaüzenetekkel és felhasználói visszajelzéssel.
    
    Args:
        error (str): A hibaüzenet.
        operation_type (str): A művelet típusa (pl. "ajánlatok betöltése").
    """
    if "401" in str(error) or "Unauthorized" in str(error):
        show_error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        # Clear auth token
        st.session_state.auth_token = None
        st.experimental_rerun()
    elif "404" in str(error):
        show_error(f"A kért erőforrás nem található. ({operation_type})")
    elif "Connection" in str(error):
        show_error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
    else:
        show_error(f"Váratlan hiba történt: {str(error)} ({operation_type})")
    
    # Log the error
    logger.error(f"API error during {operation_type}: {str(error)}")

def inject_keyboard_shortcuts():
    """
    JavaScript alapú billentyűzetkombinációk befecskendezése a streamlit alkalmazásba.
    
    Ez a függvény a következő billentyűparancsokat teszi lehetővé:
    - J/K: Le/Fel navigálás az ajánlatok között
    - N: Új oldal (következő oldal lapozáshoz)
    - P: Előző oldal
    - F: Fókusz a szűrőkre
    - R: Adatok újratöltése
    - Escape: Vissza a listanézethez (részletes nézetből)
    - 1-5: Gyors státuszváltás (ha részletes nézetben vagyunk)
    """
    # A JavaScript kód, ami a billentyűparancsokat kezeli
    js_code = """
    <script>
    // Az oldal betöltésekor aktiváljuk a billentyűzetes vezérlést
    document.addEventListener('DOMContentLoaded', function() {
        // Megváltozott e már a jelenlegi key handler
        let handlerActive = false;
        
        // Ellenőrizzük minden 100ms-ban, hogy található-e már a DOM-ban gomb/táblázat
        const checkInterval = setInterval(function() {
            if (!handlerActive && 
                (document.querySelector('table') || 
                document.querySelector('button[kind="primary"]'))) {
                
                // A DOM már betöltődött, inicializáljuk a billentyűzetkezelőt
                initKeyboardHandler();
                handlerActive = true;
                clearInterval(checkInterval);
            }
        }, 100);
        
        function initKeyboardHandler() {
            document.addEventListener('keydown', function(e) {
                // Ne kezeljük a billentyűeseményeket, ha input mezőben vagyunk
                if (document.activeElement.tagName === 'INPUT' || 
                    document.activeElement.tagName === 'TEXTAREA') {
                    return;
                }
                
                // Aktuális URL ellenőrzése, hogy részletes nézetben vagyunk-e
                const isDetailView = window.location.search.includes('offer_id=');
                
                // A billentyűkombinációk kezelése
                switch(e.key) {
                    case 'j': // Le (következő elem)
                        navigateList(1);
                        break;
                    case 'k': // Fel (előző elem)
                        navigateList(-1);
                        break;
                    case 'n': // Következő oldal
                        clickButton('▶️');
                        break;
                    case 'p': // Előző oldal
                        clickButton('◀️');
                        break;
                    case 'f': // Fókusz a szűrőkre
                        focusOnElement('input[aria-label="Keresés"]');
                        break;
                    case 'r': // Adatok újratöltése
                        clickButton('Frissítés');
                        break;
                    case 'Escape': // Visszatérés a listához
                        if (isDetailView) {
                            clickButton('⬅️ Vissza a listához');
                        }
                        break;
                    case '1': // Gyors státuszváltás
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                        if (isDetailView) {
                            const status_idx = parseInt(e.key) - 1;
                            const status_buttons = document.querySelectorAll('button[aria-label*="Státusz"]');
                            if (status_buttons.length > status_idx) {
                                status_buttons[status_idx].click();
                            }
                        }
                        break;
                }
            });
        }
        
        // Segédfüggvények
        function navigateList(direction) {
            const rows = document.querySelectorAll('table tbody tr');
            if (rows.length === 0) return;
            
            // Megkeressük, hogy melyik sor van kijelölve
            let activeIndex = -1;
            rows.forEach((row, index) => {
                if (row.classList.contains('selected') || row.hasAttribute('data-selected')) {
                    activeIndex = index;
                }
            });
            
            // Kiválasztjuk a következő/előző sort
            let newIndex = activeIndex + direction;
            if (newIndex < 0) newIndex = 0;
            if (newIndex >= rows.length) newIndex = rows.length - 1;
            
            // Csak ha tényleg változott az index, akkor kattintunk
            if (newIndex !== activeIndex) {
                const targetRow = rows[newIndex];
                // Megkeressük a sorban lévő kattintható elemet
                const clickable = targetRow.querySelector('a, button');
                if (clickable) clickable.click();
            }
        }
        
        function clickButton(textContent) {
            // Megkeressük a gombot szöveg alapján
            const buttons = Array.from(document.querySelectorAll('button'));
            const button = buttons.find(btn => 
                btn.textContent.trim().includes(textContent));
            
            if (button) button.click();
        }
        
        function focusOnElement(selector) {
            const element = document.querySelector(selector);
            if (element) element.focus();
        }
    });
    </script>
    """
    
    # Beillesztjük a JavaScript kódot
    st.markdown(js_code, unsafe_allow_html=True)
    
    # Segítő tippek megjelenítése (ha fejlesztői módban vagyunk)
    if st.session_state.get('dev_mode', False):
        with st.expander("⌨️ Billentyűparancsok", expanded=False):
            st.write("""
            - **J/K**: Le/Fel navigálás az ajánlatok között
            - **N/P**: Következő/Előző oldal
            - **F**: Fókusz a szűrőkre
            - **R**: Adatok újratöltése
            - **Escape**: Vissza a listához (részletes nézetből)
            - **1-5**: Gyors státuszváltás (részletes nézetben)
            """)

def render_active_filters_indicator():
    """
    Megjeleníti az aktív szűrőket vizuálisan a felhasználó számára.
    
    Ez a függvény ellenőrzi mely szűrők aktívak jelenleg, és egy vizuális jelzést 
    ad erről a felhasználónak, ami segíti a keresési feltételek átlátását.
    """
    active_filters = []
    
    # Ellenőrizzük a különböző szűrőket
    if st.session_state.get('selected_producer_id'):
        # Termelő szűrő
        producer_id = st.session_state.get('selected_producer_id')
        producer_name = "Ismeretlen termelő"
        
        # Termelő név lekérése
        if producer_id and st.session_state.get('producers_cache'):
            for p in st.session_state.producers_cache:
                if p.get('id') == producer_id:
                    producer_name = p.get('company_name') or p.get('contact_name') or "Ismeretlen termelő"
                    break
        
        active_filters.append(f"Termelő: {producer_name}")
    
    # Státusz szűrő
    if st.session_state.get('selected_status'):
        status = st.session_state.get('selected_status')
        active_filters.append(f"Státusz: {format_status(status)}")
    
    # Dátum szűrők
    from_date = st.session_state.get('from_date')
    to_date = st.session_state.get('to_date')
    
    if from_date and to_date:
        active_filters.append(f"Dátum: {from_date.strftime('%Y-%m-%d')} - {to_date.strftime('%Y-%m-%d')}")
    elif from_date:
        active_filters.append(f"Dátum: {from_date.strftime('%Y-%m-%d')}-től")
    elif to_date:
        active_filters.append(f"Dátum: {to_date.strftime('%Y-%m-%d')}-ig")
    
    # Ha nincsenek aktív szűrők
    if not active_filters:
        return
    
    # Megjelenítjük az aktív szűrőket
    with st.container():
        # Stílus az aktív szűrőkhöz
        st.markdown("""
        <style>
        .active-filters {
            padding: 10px;
            border-radius: 5px;
            background-color: var(--background-color, #f0f2f6);
            border-left: 5px solid #1f77b4;
            margin-bottom: 1rem;
        }
        .filter-tag {
            display: inline-block;
            background-color: #1f77b4;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Az aktív szűrők HTML megjelenítése
        html_content = """
        <div class="active-filters">
            <p><strong>Aktív szűrők:</strong></p>
            <div>
        """
        
        for filter_text in active_filters:
            html_content += f'<span class="filter-tag">{filter_text}</span>'
        
        html_content += """
            </div>
        </div>
        """
        
        st.markdown(html_content, unsafe_allow_html=True)
        
        # Szűrők törlése gomb
        if st.button("Szűrők törlése", key="reset_filters_btn"):
            # Töröljük a szűrési paramétereket
            if 'selected_producer_id' in st.session_state:
                st.session_state.selected_producer_id = None
            if 'selected_status' in st.session_state:
                st.session_state.selected_status = None
            if 'from_date' in st.session_state:
                st.session_state.from_date = None
            if 'to_date' in st.session_state:
                st.session_state.to_date = None
                
            # Újratöltjük az oldalt
            st.experimental_rerun()

def render_tooltip(tooltip_text, icon="ℹ️", placement="top", key=None):
    """
    Tooltip komponens renderelése, amely segítséget nyújt a felhasználóknak.
    
    Ez a függvény egy kattintásra megjelenő tooltip komponenst renderel,
    amely segítséget nyújt a felhasználónak a komplex funkciók megértésében.
    
    Args:
        tooltip_text (str): A tooltip szövege, ami megjelenik.
        icon (str, optional): Az ikon. Defaults to "ℹ️".
        placement (str, optional): A tooltip pozíciója. Defaults to "top".
        key (str, optional): Egyedi kulcs a komponenshez. Defaults to None.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"tooltip_{str(uuid.uuid4())[:8]}"
    
    # JavaScript és CSS a tooltiphez
    tooltip_style = f"""
    <style>
    .tooltip-{key} {{
        position: relative;
        display: inline-block;
        cursor: pointer;
        color: #3584e4;
        margin-left: 5px;
    }}
    
    .tooltip-{key} .tooltip-text-{key} {{
        visibility: hidden;
        width: 250px;
        background-color: #333;
        color: #fff;
        text-align: left;
        border-radius: 5px;
        padding: 10px;
        position: absolute;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s;
        font-size: 0.9em;
        line-height: 1.4;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }}
    
    /* Pozícionálás a placement paraméter alapján */
    .tooltip-{key} .tooltip-text-{key}.top {{
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
    }}
    
    .tooltip-{key} .tooltip-text-{key}.bottom {{
        top: 125%;
        left: 50%;
        transform: translateX(-50%);
    }}
    
    .tooltip-{key} .tooltip-text-{key}.left {{
        top: -5px;
        right: 105%;
    }}
    
    .tooltip-{key} .tooltip-text-{key}.right {{
        top: -5px;
        left: 105%;
    }}
    
    /* Nyilacska a tooltiphez */
    .tooltip-{key} .tooltip-text-{key}::after {{
        content: "";
        position: absolute;
        border-width: 5px;
        border-style: solid;
    }}
    
    .tooltip-{key} .tooltip-text-{key}.top::after {{
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-color: #333 transparent transparent transparent;
    }}
    
    .tooltip-{key} .tooltip-text-{key}.bottom::after {{
        bottom: 100%;
        left: 50%;
        margin-left: -5px;
        border-color: transparent transparent #333 transparent;
    }}
    
    .tooltip-{key} .tooltip-text-{key}.left::after {{
        top: 50%;
        left: 100%;
        margin-top: -5px;
        border-color: transparent transparent transparent #333;
    }}
    
    .tooltip-{key} .tooltip-text-{key}.right::after {{
        top: 50%;
        right: 100%;
        margin-top: -5px;
        border-color: transparent #333 transparent transparent;
    }}
    
    .tooltip-{key}:hover .tooltip-text-{key} {{
        visibility: visible;
        opacity: 1;
    }}
    </style>
    """
    
    # HTML a tooltiphez
    tooltip_html = f"""
    <span class="tooltip-{key}">
        {icon}
        <span class="tooltip-text-{key} {placement}">{tooltip_text}</span>
    </span>
    """
    
    # Tooltip renderelése
    st.markdown(tooltip_style, unsafe_allow_html=True)
    st.markdown(tooltip_html, unsafe_allow_html=True)

def render_help_text(component_name, help_text, icon="❓"):
    """
    Súgószöveg komponens renderelése a funkció neve mellé.
    
    Args:
        component_name (str): A komponens neve, amihez a súgó tartozik
        help_text (str): A magyarázó szöveg
        icon (str, optional): Ikon a súgószöveghez. Defaults to "❓".
    """
    help_container = st.container()
    with help_container:
        cols = st.columns([3, 1])
        with cols[0]:
            st.markdown(f"**{component_name}**")
        with cols[1]:
            render_tooltip(help_text, icon=icon)

def display_status_legend():
    """
    Státusz jelmagyarázat megjelenítése.
    
    Ez a függvény megjeleníti az ajánlatok különböző státuszaihoz tartozó
    színjelöléseket és magyarázatokat egy kompakt, vizuális formában.
    """
    # Státusz információk
    statuses = config.OFFER_STATUSES
    
    st.markdown("#### Státusz jelmagyarázat")
    
    # Státuszok csoportosítása
    status_groups = {
        "Folyamatban lévő": ["CREATED", "MODIFIED", "CONFIRMED_BY_COMPANY"],
        "Lezárt": ["ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED", "CANCELED"],
    }
    
    # Jelmagyarázat tárolódoboz
    with st.container():
        # Státuszok megjelenítése csoportonként
        for group_name, group_statuses in status_groups.items():
            st.markdown(f"**{group_name} státuszok:**")
            
            # Státuszok oszlopokban megjelenítése
            cols = st.columns(len(group_statuses))
            
            for i, status_code in enumerate(group_statuses):
                if status_code in statuses:
                    status_info = statuses[status_code]
                    status_name = status_info.get("name", status_code)
                    status_color = status_info.get("color", "#808080")
                    
                    # Státusz színjelölés és név megjelenítése
                    with cols[i]:
                        st.markdown(
                            f"""
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <div style="background-color: {status_color}; width: 16px; height: 16px; 
                                            border-radius: 4px; margin-right: 8px;"></div>
                                <div>{status_name}</div>
                            </div>
                            """,
                            unsafe_allow_html=True
                        )
    
    st.markdown("<hr style='margin: 10px 0px;'>", unsafe_allow_html=True)

def bulk_status_update(offer_ids, new_status):
    """
    Több ajánlat státuszának egyszerre történő frissítése.
    
    Args:
        offer_ids (list): Az ajánlatok azonosítóinak listája
        new_status (str): Az új státusz kód
        
    Returns:
        dict: Eredmények szótára sikeres és sikertelen műveletekkel
    """
    results = {
        "success": [],
        "failed": []
    }
    
    for offer_id in offer_ids:
        try:
            success, result = offers_api.update_offer_status(offer_id, new_status)
            if success:
                results["success"].append(offer_id)
            else:
                results["failed"].append({"id": offer_id, "error": result})
        except Exception as e:
            results["failed"].append({"id": offer_id, "error": str(e)})
    
    return results

def add_bulk_operation_buttons(offers):
    """
    Tömeges műveleti gombok megjelenítése több kiválasztott ajánlathoz.
    
    Args:
        offers (list): Az ajánlatok listája
    """
    if not offers:
        return
    
    st.markdown("### Tömeges műveletek")
    
    # Ajánlatok többszörös kiválasztása
    offer_options = [f"#{o.get('id')} - {o.get('product_type', {}).get('name', 'Ismeretlen')} ({format_date(o.get('delivery_date', ''))})" for o in offers]
    
    # Kiválasztó widget - multi=True a többszörös kiválasztáshoz
    selected_indices = st.multiselect(
        "Válasszon ajánlatokat:",
        options=range(len(offer_options)),
        format_func=lambda i: offer_options[i],
        key="selected_offers_for_bulk_actions"
    )
    
    # Ha nincsenek kiválasztott ajánlatok, nem jelenítünk meg gombokat
    if not selected_indices:
        st.info("Válasszon ki legalább egy ajánlatot a tömeges műveletekhez.")
        return
    
    # Kiválasztott ajánlatok és azok azonosítói
    selected_offers = [offers[i] for i in selected_indices]
    selected_ids = [offer.get("id") for offer in selected_offers]
    
    # Státuszok ellenőrzése a lehetséges műveletek meghatározásához
    statuses = [offer.get("status") for offer in selected_offers]
    
    # Tömeges műveleti gombok oszlopokban
    st.write(f"**{len(selected_ids)} ajánlat kiválasztva**")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Tömeges elfogadás gomb - csak olyan ajánlatokra, amelyek CONFIRMED_BY_COMPANY státuszban vannak
        can_accept = all(status == "CONFIRMED_BY_COMPANY" for status in statuses)
        if st.button("Tömeges elfogadás", disabled=not can_accept, use_container_width=True):
            if can_accept:
                with st.spinner("Ajánlatok elfogadása folyamatban..."):
                    results = bulk_status_update(selected_ids, "ACCEPTED_BY_USER")
                    
                    # Eredmények megjelenítése
                    if results["success"]:
                        st.success(f"{len(results['success'])} ajánlat sikeresen elfogadva!")
                    
                    if results["failed"]:
                        st.error(f"{len(results['failed'])} ajánlat elfogadása sikertelen volt.")
                        for failed in results["failed"]:
                            st.error(f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                    
                    # Oldal újratöltése 1 másodperc késleltetéssel
                    time.sleep(1)
                    st.experimental_rerun()
            else:
                st.warning("Csak CONFIRMED_BY_COMPANY státuszú ajánlatokat fogadhat el tömegesen.")
    
    with col2:
        # Tömeges elutasítás gomb - csak olyan ajánlatokra, amelyek CONFIRMED_BY_COMPANY státuszban vannak
        can_reject = all(status == "CONFIRMED_BY_COMPANY" for status in statuses)
        if st.button("Tömeges elutasítás", disabled=not can_reject, use_container_width=True):
            if can_reject:
                # Megerősítés kérése a több elem elutasításához
                if st.checkbox(f"Biztosan elutasítja mind a {len(selected_ids)} kiválasztott ajánlatot?", key="confirm_bulk_reject"):
                    with st.spinner("Ajánlatok elutasítása folyamatban..."):
                        results = bulk_status_update(selected_ids, "REJECTED_BY_USER")
                        
                        # Eredmények megjelenítése
                        if results["success"]:
                            st.success(f"{len(results['success'])} ajánlat sikeresen elutasítva!")
                        
                        if results["failed"]:
                            st.error(f"{len(results['failed'])} ajánlat elutasítása sikertelen volt.")
                            for failed in results["failed"]:
                                st.error(f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                        
                        # Oldal újratöltése 1 másodperc késleltetéssel
                        time.sleep(1)
                        st.experimental_rerun()
            else:
                st.warning("Csak CONFIRMED_BY_COMPANY státuszú ajánlatokat utasíthat el tömegesen.")
    
    with col3:
        # Tömeges törlés gomb - minden ajánlatra, de megerősítést igényel
        if st.button("Tömeges törlés", type="secondary", use_container_width=True):
            # Többszörös törléshez kötelező megerősítés
            if st.checkbox(f"Biztosan törli mind a {len(selected_ids)} kiválasztott ajánlatot? A művelet nem vonható vissza!", key="confirm_bulk_delete"):
                with st.spinner("Ajánlatok törlése folyamatban..."):
                    results = bulk_status_update(selected_ids, "CANCELED")
                    
                    # Eredmények megjelenítése
                    if results["success"]:
                        st.success(f"{len(results['success'])} ajánlat sikeresen törölve!")
                    
                    if results["failed"]:
                        st.error(f"{len(results['failed'])} ajánlat törlése sikertelen volt.")
                        for failed in results["failed"]:
                            st.error(f"Hiba az ajánlatnál #{failed['id']}: {failed['error']}")
                    
                    # Oldal újratöltése 1 másodperc késleltetéssel
                    time.sleep(1)
                    st.experimental_rerun()

def show_offer_list():
    """
    Ajánlatok listázása és kezelése
    """
    # Session state inicializálása
    init_page_state()
    
    # Billentyűzetkombinációk injektálása
    inject_keyboard_shortcuts()
    
    # Oldal azonosító a session state-hez
    page_id = st.session_state.page_uuid
    
    # Lapozási állapot inicializálása
    if "current_page" not in st.session_state:
        st.session_state.current_page = 1
    if "page_size" not in st.session_state:
        st.session_state.page_size = 20
    
    # Callback az oldalváltáshoz
    def change_page(new_page):
        st.session_state.current_page = new_page
        st.experimental_rerun()
    
    # Az oldal oszlopai
    page_columns = st.columns([8, 2])
    
    with page_columns[0]:
        st.title("Ajánlatok kezelése")
        
        # Súgó a billentyűparancsokhoz
        keyboard_help = """
        **Billentyűparancsok:**
        - J/K: Le/Fel navigálás az ajánlatok között
        - N/P: Következő/Előző oldal
        - F: Fókusz a szűrőkre
        - R: Adatok újratöltése
        - ESC: Vissza a listához (részletes nézetből)
        - 1-5: Gyors státuszváltás (részletes nézetben)
        """
        render_tooltip(keyboard_help, icon="⌨️", placement="right", key="keyboard_help")
    
    with page_columns[1]:
        st.markdown("<div style='margin-top: 25px;'></div>", unsafe_allow_html=True)
        if st.button("➕ Új ajánlat", key="create_new_offer", type="primary"):
            # Use JavaScript redirection instead of st.switch_page
            st.markdown("""
            <script>
                window.parent.location.href = "/operator_create_offer";
            </script>
            """, unsafe_allow_html=True)
    
    # Szűrők megjelenítése
    st.markdown("### Szűrési feltételek " + 
               """<span style="font-size:0.8em;">(A szűréshez válassza ki a kívánt feltételeket)</span>""", 
               unsafe_allow_html=True)
    
    filters_help = """
    **Szűrési segédlet:**
    - **Termelő:** Válasszon egy termelőt a listából.
    - **Státusz:** Szűrjön státusz alapján (pl. függőben, elfogadva).
    - **Időszak:** Válasszon előre definiált időszakot vagy egyedi dátumokat.
    
    A szűrők kombinálhatók, és a "Szűrők törlése" gombbal alaphelyzetbe állíthatók.
    """
    render_tooltip(filters_help, icon="🔍", placement="right", key="filters_help")
    
    # Szűrők renderelése
    selected_producer_id, selected_status, from_date, to_date = render_offer_filters(page_id)
    
    # Aktív szűrők megjelenítése
    render_active_filters_indicator()
    
    # JavaScript befecskendezése a képernyőméret érzékeléséhez
    inject_screen_detection()
    
    # Szűrési paraméterek előkészítése
    query_params = prepare_filter_params(selected_producer_id, selected_status, from_date, to_date)
    
    # Frissítés gomb hozzáadása
    refresh_col1, refresh_col2, refresh_col3 = st.columns([6, 1, 1])
    with refresh_col2:
        if st.button("🔄 Frissítés", key="refresh_data"):
            # Cache törlése
            clear_cache()
            st.success("Adatok frissítése...")
            time.sleep(0.5)
            st.experimental_rerun()
    
    # Ajánlatok betöltése vizuális folyamatjelzővel
    success, result = fetch_offer_data_with_progress(
        query_params=query_params,
        page=st.session_state.current_page,
        page_size=st.session_state.page_size
    )
    
    # Státusz jelmagyarázat megjelenítése
    display_status_legend()
    
    # Funkciógombok az ajánlatok felett
    button_cols = st.columns([1, 1, 1])
    with button_cols[0]:
        # Exportálási gombok csak akkor jelennek meg, ha vannak ajánlatok
        if success and isinstance(result, dict) and result.get("total", 0) > 0:
            export_options = ["Exportálás...", "CSV", "Excel"]
            export_option = st.selectbox("", options=export_options, key="export_select")
            
            # Súgó az exportálási opcióhoz
            export_help = """
            **Exportálási lehetőségek:**
            - **CSV**: Vessző-elválasztott értékek (Excel, Google Sheets)
            - **Excel**: Microsoft Excel formátum (.xlsx)
            
            Az exportált fájl tartalmazza az összes megjelenített ajánlatot
            a jelenleg alkalmazott szűrési feltételek alapján.
            """
            render_tooltip(export_help, icon="📤", placement="right", key="export_help")
            
            if export_option == "CSV":
                # CSV exportálás
                csv_href = export_offers_to_csv(result.get("items", []))
                st.markdown(csv_href, unsafe_allow_html=True)
            elif export_option == "Excel":
                # Excel exportálás
                excel_href = export_offers_to_excel(result.get("items", []))
                st.markdown(excel_href, unsafe_allow_html=True)

    # Megjelenítés előkészítése
    if success:
        if isinstance(result, dict):
            offers = result.get("items", [])
            total_offers = result.get("total", 0)
            total_pages = result.get("pages", 1)
            
            if not offers:
                st.info("Nem található ajánlat a megadott szűrési feltételek alapján.")
            else:
                # Mobilbarát vagy asztali megjelenítés
                st.write(f"Összesen {total_offers} ajánlat található.")
                
                # Az ajánlatok megjelenítése mobilnézettől függően
                if st.session_state.is_mobile:
                    # Mobilnézet: kártyák
                    for i, offer in enumerate(offers):
                        display_mobile_offer_card(offer, i, on_click_func=lambda o=offer: st.session_state.update({"selected_offer_id": o.get("id"), "needs_rerun": True}))
                else:
                    # Asztali nézet: új függvény használata különálló műveletgombokkal
                    display_offer_table_with_actions(offers)
                
                # Tömeges műveletek hozzáadása
                add_bulk_operation_buttons(offers)
                
                # Lapozó vezérlők megjelenítése
                render_pagination_controls(
                    current_page=st.session_state.current_page,
                    total_pages=total_pages,
                    on_change=change_page
                )
        else:
            st.error(f"Hiba történt az ajánlatok betöltésekor: {result}")
    else:
        st.error(f"Adatbetöltési hiba: {result}")
    
    # Ha egy ajánlatot kiválasztottak, átirányítunk a részletek nézetre
    if "needs_rerun" in st.session_state and st.session_state.needs_rerun:
        st.session_state.needs_rerun = False
        st.experimental_rerun()
    
    # Ellenőrizzük, hogy van-e ajánlat azonosító a session-ben
    if "selected_offer_id" in st.session_state:
        offer_id = st.session_state.selected_offer_id
        
        # Töröljük a session state-ből, hogy elkerüljük a rekurzív újratöltést
        del st.session_state["selected_offer_id"]
        
        # Ajánlat azonosító validálása - támogatja a string és integer típusú azonosítókat is
        try:
            # Ha string, akkor próbáljuk konvertálni int-té
            if isinstance(offer_id, str) and offer_id.isdigit():
                offer_id = int(offer_id)
            # Ha nem string vagy nem konvertálható int-té, akkor dobjunk hibát
            elif not isinstance(offer_id, int):
                raise ValueError(f"Nem számszerű ajánlat azonosító: {offer_id}")
                
            # Ha idáig eljutottunk, akkor az offer_id egy érvényes szám
            show_offer_detail(offer_id)
        except Exception as e:
            logger.error(f"Error with offer ID validation: {str(e)}")
            st.error(f"Érvénytelen ajánlat azonosító: {offer_id}")

def lazy_load_related_data(offer_id, data_type):
    """
    Kapcsolódó adatok késleltetett betöltése az ajánlathoz.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        data_type (str): Az adattípus (logs, attachments, stb.)
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    # Biztosítjuk, hogy az offer_id integer legyen a konzisztens használathoz
    try:
        if isinstance(offer_id, str) and offer_id.isdigit():
            offer_id = int(offer_id)
    except (ValueError, TypeError):
        logger.error(f"Invalid offer ID in lazy_load_related_data: {offer_id}")
    
    # Inicializáljuk a lazy loaded adatokat, ha még nem létezik
    if "lazy_loaded_data" not in st.session_state:
        st.session_state.lazy_loaded_data = {}
    
    # Ajánlat specifikus cache inicializálása
    if offer_id not in st.session_state.lazy_loaded_data:
        st.session_state.lazy_loaded_data[offer_id] = {}
    
    # Ha már van cached adat, akkor azt adjuk vissza
    if data_type in st.session_state.lazy_loaded_data[offer_id]:
        cached_data = st.session_state.lazy_loaded_data[offer_id][data_type]
        # Ellenőrizzük, hogy a cached adat nem túl régi-e (15 perces időkorlát)
        if time.time() - cached_data.get("timestamp", 0) < 900:  # 15 perc = 900 másodperc
            return cached_data.get("success", False), cached_data.get("data")
    
    # Különböző típusú adatok lekérése
    try:
        if data_type == "logs":
            success, result = offers_api.get_offer_logs(offer_id)
        elif data_type == "attachments":
            success, result = offers_api.get_offer_attachments(offer_id)
        elif data_type == "related_offers":
            # Kapcsolódó ajánlatok lekérése, pl. ugyanattól a termelőtől
            success, offer_details = offers_api.get_offer(offer_id)
            if success and offer_details:
                user_id = offer_details.get("user", {}).get("id")
                if user_id:
                    success, result = offers_api.get_offers(params={"user_id": user_id, "limit": 5})
                else:
                    success, result = False, "Nem található felhasználó azonosító az ajánlathoz"
            else:
                success, result = False, "Nem található ajánlat a megadott azonosítóval"
        else:
            success, result = False, f"Ismeretlen adattípus: {data_type}"
        
        # Cache-eljük az eredményt
        st.session_state.lazy_loaded_data[offer_id][data_type] = {
            "success": success,
            "data": result,
            "timestamp": time.time()
        }
        
        return success, result
    except Exception as e:
        logger.error(f"Error in lazy_load_related_data for {data_type}: {str(e)}")
        return False, str(e)

def lazy_load_cache(cache_key, data_loader_func, cache_ttl=300):
    """
    Általános lazy loading függvény cache-eléssel.
    
    Ez a függvény lehetővé teszi adatok késleltetett betöltését és cache-elését
    a session state-ben, ezáltal csökkentve a felesleges API hívások számát.
    
    Args:
        cache_key (str): A cache kulcs neve a session state-ben
        data_loader_func (callable): Az adatok betöltéséért felelős függvény
        cache_ttl (int, optional): Cache élettartama másodpercekben. Defaults to 300 (5 perc).
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    # Cache inicializálása, ha még nem létezik
    if "lazy_cache" not in st.session_state:
        st.session_state.lazy_cache = {}
    
    # Ha már van cached adat és nem túl régi, akkor azt adjuk vissza
    if cache_key in st.session_state.lazy_cache:
        cached_data = st.session_state.lazy_cache[cache_key]
        if time.time() - cached_data.get("timestamp", 0) < cache_ttl:
            return cached_data.get("success", False), cached_data.get("data")
    
    # Adatok betöltése
    try:
        success, result = data_loader_func()
        
        # Cache-eljük az eredményt
        st.session_state.lazy_cache[cache_key] = {
            "success": success,
            "data": result,
            "timestamp": time.time()
        }
        
        return success, result
    except Exception as e:
        logger.error(f"Error in lazy_load_cache for {cache_key}: {str(e)}")
        return False, str(e)

def clear_cache(keys=None):
    """
    Cache törlése a session state-ből.
    
    Args:
        keys (list, optional): A törlendő cache kulcsok listája. 
            Ha None, akkor minden cache törlődik.
    """
    # Lazy cache törlése
    if "lazy_cache" in st.session_state:
        if keys is None:
            # Minden cache törlése
            st.session_state.lazy_cache = {}
        else:
            # Csak a megadott kulcsok törlése
            for key in keys:
                if key in st.session_state.lazy_cache:
                    del st.session_state.lazy_cache[key]
    
    # Ajánlat specifikus cache törlése
    if "lazy_loaded_data" in st.session_state and keys is not None:
        for key in keys:
            # Ha a kulcs egy ajánlat azonosító
            if key.isdigit():
                offer_id = int(key)
                if offer_id in st.session_state.lazy_loaded_data:
                    del st.session_state.lazy_loaded_data[offer_id]
            # Ha a kulcs egy ajánlat típus (pl. "logs")
            else:
                for offer_id in list(st.session_state.lazy_loaded_data.keys()):
                    if key in st.session_state.lazy_loaded_data[offer_id]:
                        del st.session_state.lazy_loaded_data[offer_id][key]

@st.cache_data(ttl=300)
def get_producer_details(producer_id):
    """
    Termelő adatainak lekérése cache-eléssel.
    
    Args:
        producer_id (int): A termelő azonosítója
        
    Returns:
        dict: A termelő részletes adatai vagy üres szótár hiba esetén
    """
    try:
        success, result = users_api.get_user(producer_id)
        if success:
            return result
        return {}
    except Exception as e:
        logger.error(f"Error fetching producer details for ID {producer_id}: {str(e)}")
        return {}

@st.cache_data(ttl=300)
def get_product_details(product_id):
    """
    Termék részletes adatainak lekérése gyorsítótárral.
    
    Args:
        product_id (int): A termék azonosítója
        
    Returns:
        dict: A termék részletes adatai vagy üres szótár hiba esetén
    """
    try:
        success, result = products_api.get_product_type(product_id)
        if success:
            return result
        return {}
    except Exception as e:
        logger.error(f"Error fetching product details for ID {product_id}: {str(e)}")
        return {}

def render_section_card(title, content, color="#3584e4", icon=None, is_mobile=False, key=None, expanded=True):
    """
    Egységes megjelenésű kártyacomponens renderelése különböző szekciókhoz.
    
    Ez a függvény egy vizuálisan attraktív, egységes stílusú kártyát jelenít meg
    a Streamlit alkalmazásban. A kártya színe, ikonja és tartalma testre szabható.
    
    Args:
        title (str): A kártya címe/fejléce.
        content (callable): Függvény, amely a kártya tartalmát rendereli a with context-ben.
        color (str, optional): A kártya színkódja (hex, rgb vagy név). Defaults: "#3584e4" (kék).
        icon (str, optional): Szöveges ikon a cím mellett (emoji vagy Unicode karakter).
        is_mobile (bool, optional): Mobilbarát elrendezés használata. Defaults: False.
        key (str, optional): Egyedi kulcs a komponenshez. Defaults: None.
        expanded (bool, optional): Kezdeti állapot (kinyitva/becsukva). Defaults: True.
    
    Returns:
        None
    """
    # Ha nincs egyedi kulcs, generálunk egyet a cím alapján
    if key is None:
        key = f"card_{title.lower().replace(' ', '_')}_{str(uuid.uuid4())[:8]}"
    
    # Alapértelmezett ikon, ha nincs megadva
    if icon is None:
        icon = "📋"
    
    # Kártya stílus generálása
    card_style = f"""
    <style>
    .card-{key} {{
        border-radius: 10px;
        border-left: 5px solid {color};
        background-color: var(--background-color, white);
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }}
    .card-header-{key} {{
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: {18 if is_mobile else 20}px;
        font-weight: bold;
        color: var(--text-color, #333);
    }}
    .card-icon-{key} {{
        margin-right: 0.5rem;
        font-size: {20 if is_mobile else 24}px;
    }}
    .card-title-{key} {{
        flex-grow: 1;
    }}
    .card-content-{key} {{
        padding: 0.5rem 0;
    }}
    </style>
    """
    
    # Kártya HTML struktúra
    card_html = f"""
    <div class="card-{key}">
        <div class="card-header-{key}">
            <div class="card-icon-{key}">{icon}</div>
            <div class="card-title-{key}">{title}</div>
        </div>
    </div>
    """
    
    # Kártya stílus és fejléc renderelése
    st.markdown(card_style, unsafe_allow_html=True)
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Tartalom renderelése - egy container-be csomagoljuk
    with st.container():
        # Ha kinyitható/becsukható kártyát szeretnénk
        if expanded is not None:
            with st.expander("", expanded=expanded):
                content()
        else:
            # Egyszerű tartalom, mindig látható
            content()

def show_offer_detail(offer_id):
    """
    Ajánlat részletes megtekintése és kezelése
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
    """
    # Session state inicializálása
    init_page_state()
    
    # Billentyűzetkombinációk injektálása
    inject_keyboard_shortcuts()
    
    # Biztosítjuk, hogy az offer_id megfelelő típusú legyen
    try:
        # Ha string, akkor megpróbáljuk konvertálni int-té
        if isinstance(offer_id, str):
            if offer_id.isdigit():
                offer_id = int(offer_id)
            else:
                logger.error(f"Non-numeric offer ID string: {offer_id}")
                st.error(f"Érvénytelen ajánlat azonosító formátum: {offer_id}")
                return
        # Ha nem integer vagy nem tudtuk konvertálni
        elif not isinstance(offer_id, int):
            logger.error(f"Invalid offer ID type: {type(offer_id)}")
            st.error(f"Érvénytelen ajánlat azonosító típus: {offer_id}")
            return
            
        logger.info(f"Processing offer ID: {offer_id} (type: {type(offer_id)})")
    except Exception as e:
        logger.error(f"Exception during offer ID validation: {str(e)}")
        st.error(f"Hiba az ajánlat azonosító feldolgozásakor: {offer_id}")
        return
    
    # Ajánlat adatainak betöltése
    success = False
    
    # Először a gyorsítótárból próbáljuk betölteni
    if "offer_cache" in st.session_state and offer_id in st.session_state.offer_cache:
        logger.info(f"Loading offer {offer_id} from cache")
        offer = st.session_state.offer_cache[offer_id]
        success = True
    else:
        # Ha nincs a gyorsítótárban, akkor API hívással töltjük be
        try:
            logger.info(f"Loading offer {offer_id} from API")
            success, offer_result = offers_api.get_offer(offer_id)
            
            if success:
                offer = offer_result
                # Frissítjük a gyorsítótárat
                if "offer_cache" not in st.session_state:
                    st.session_state.offer_cache = {}
                st.session_state.offer_cache[offer_id] = offer
                logger.info(f"Successfully loaded offer {offer_id} from API")
            else:
                error_msg = f"Hiba az ajánlat betöltésekor: {offer_result}"
                logger.error(f"API error loading offer {offer_id}: {offer_result}")
                st.error(error_msg)
                return
        except Exception as e:
            error_msg = f"Váratlan hiba az ajánlat betöltésekor: {str(e)}"
            logger.error(f"Exception loading offer {offer_id}: {str(e)}")
            st.error(error_msg)
            return
    
    if not success:
        st.error("Az ajánlat nem található vagy nem hozzáférhető.")
        return
    
    # Nézet beállítása a képernyőméret alapján
    is_mobile = st.session_state.get('is_mobile', False)
    
    # Visszagomb
    if st.button("⬅️ Vissza a listához", key="back_button"):
        # Remove offer_id from session state and trigger rerun
        if "selected_offer_id" in st.session_state:
            del st.session_state.selected_offer_id
        st.experimental_rerun()
    
    # Cím és alapinformációk
    st.title(f"Ajánlat részletei #{offer_id}")
    
    # Alapadatok megjelenítése
    if is_mobile:
        # Mobilbarát elrendezés
        # Állapot kártya
        display_offer_status_card(offer)
        
        # Alapvető információk kártyán
        display_offer_detail_cards(offer, is_mobile=True)
        
        # Mennyiség indikátor
        display_quantity_indicator(offer)
    else:
        # Asztali elrendezés
        # Két oszlop az alapadatokhoz és állapot kártyához
        col1, col2 = st.columns([3, 1])
        
        with col1:
            # Alapvető információk kártyán
            display_offer_detail_cards(offer)
        
        with col2:
            # Állapot kártya
            display_offer_status_card(offer)
            
            # Mennyiség indikátor
            display_quantity_indicator(offer)
    
    # Idősor megjelenítése (azonos mindkét nézeten)
    def timeline_content():
        # Idősor HTML kód direkt megjelenítése Streamlit-en belül, nem component-ből
        events = [
            {"date": offer.get("created_at"), "label": "Létrehozva"},
            {"date": offer.get("updated_at"), "label": "Módosítva"},
            {"date": offer.get("delivery_date"), "label": "Beszállítás"}
        ]
        events = [e for e in events if e["date"]]
        events.sort(key=lambda x: x["date"])
        
        if not events:
            st.info("Nincs idővonal adat.")
            return
        
        # Saját Streamlit elemekkel rajzoljuk meg a timeline-t
        st.markdown("### Idősor", unsafe_allow_html=True)
        
        # Oszlopokat használunk az események megjelenítésére
        cols = st.columns(len(events))
        
        # Vonal a pont között (ezt CSS-sel nem tudjuk könnyen megijeleníteni)
        # Ehelyett egy vizuális folyamatjelzőt használunk a pontok között
        if len(events) > 1:
            st.markdown('<div style="margin: 10px 0;"></div>', unsafe_allow_html=True)
            progress_cols = st.columns([0.1, 0.8, 0.1])
            with progress_cols[1]:
                st.progress(100)
            st.markdown('<div style="margin: 10px 0;"></div>', unsafe_allow_html=True)
        
        # Az események megjelenítése az oszlopokban
        for i, (col, event) in enumerate(zip(cols, events)):
            with col:
                # Pont
                st.markdown(
                    f"""
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 24px; height: 24px; background-color: #3584e4; 
                                    border-radius: 50%; margin: 0 auto;"></div>
                        <p style="text-align: center; margin-top: 8px; font-size: 12px;">
                            {event['label']}<br>{format_datetime(event['date'])}
                        </p>
                    </div>
                    """, 
                    unsafe_allow_html=True
                )
    
    # Az idősor megjelenítése az új kártyarendszerrel
    render_section_card(
        title="Idősor", 
        content=timeline_content,
        color="#17a2b8",  # Világoskék szín 
        icon="⏱️",
        is_mobile=is_mobile,
        key="timeline_card",
        expanded=True
    )
    
    # Kapcsolódó adatok lazy loading-gal
    with st.spinner("Státusztörténet betöltése..."):
        logs_success, logs = lazy_load_related_data(offer_id, "logs")
        if logs_success:
            display_status_history(logs)
        else:
            st.error("Nem sikerült betölteni a státusztörténetet.")
    
    # Csatolmányok (ha vannak)
    with st.spinner("Csatolmányok betöltése..."):
        attachments_success, attachments = lazy_load_related_data(offer_id, "attachments")
        if attachments_success:
            display_offer_attachments(attachments)
        else:
            st.info("Nincsenek csatolmányok vagy nem sikerült betölteni őket.")
    
    # Kapcsolódó részletes adatok a termelőről és termékről lazy módon betöltve
    def related_data_content():
        tabs = st.tabs(["Termelő adatai", "Termék adatai"])
        
        with tabs[0]:
            with st.spinner("Termelő adatainak betöltése..."):
                producer_id = offer.get("user", {}).get("id")
                if producer_id:
                    producer = get_producer_details(producer_id)
                    if producer:
                        st.markdown(f"### {producer.get('company_name', 'Ismeretlen termelő')}")
                        st.markdown(f"**Kapcsolattartó:** {producer.get('contact_name', '-')}")
                        st.markdown(f"**Email:** {producer.get('email', '-')}")
                        st.markdown(f"**Telefon:** {producer.get('phone', '-')}")
                        st.markdown(f"**Cím:** {producer.get('address', '-')}")
                    else:
                        st.info("Nem sikerült betölteni a termelő részletes adatait.")
                else:
                    st.info("Nincs termelő hozzárendelve az ajánlathoz.")
        
        with tabs[1]:
            with st.spinner("Termék adatainak betöltése..."):
                product_id = offer.get("product_type", {}).get("id")
                if product_id:
                    product = get_product_details(product_id)
                    if product:
                        st.markdown(f"### {product.get('name', 'Ismeretlen termék')}")
                        st.markdown(f"**Kategória:** {product.get('category', {}).get('name', '-')}")
                        st.markdown(f"**Leírás:** {product.get('description', '-')}")
                        st.markdown(f"**Mértékegység:** {product.get('unit', 'kg')}")
                    else:
                        st.info("Nem sikerült betölteni a termék részletes adatait.")
                else:
                    st.info("Nincs termék hozzárendelve az ajánlathoz.")
    
    # Kapcsolódó adatok megjelenítése az új kártyarendszerrel
    render_section_card(
        title="Kapcsolódó adatok", 
        content=related_data_content,
        color="#6610f2",  # Lila szín
        icon="🔗",
        is_mobile=is_mobile,
        key="related_data_card",
        expanded=False
    )
    
    # Műveletek megjelenítése
    display_offer_actions(offer)

def display_status_history(logs_data):
    """
    Ajánlat státusztörténet megjelenítése.
    
    Args:
        logs_data (list): Státusztörténet adatok
    """
    def render_content():
        if not logs_data:
            st.info("Nincs elérhető státusztörténet ehhez az ajánlathoz.")
            return
        
        # Rendezés dátum szerint
        sorted_logs = sorted(logs_data, key=lambda x: x.get("created_at", ""), reverse=True)
        
        # Táblázat fejléc
        st.markdown("""
        | Dátum | Felhasználó | Státusz | Megjegyzés |
        | ----- | ----------- | ------- | ---------- |
        """)
        
        # Táblázat sorok
        for log in sorted_logs:
            created_at = format_datetime(log.get("created_at", ""))
            user_name = log.get("user", {}).get("contact_name", "Ismeretlen")
            status = format_status(log.get("status", ""))
            note = log.get("note", "")
            
            st.markdown(f"| {created_at} | {user_name} | {status} | {note} |")
    
    # Az új kártyarendszert használjuk
    is_mobile = st.session_state.get('is_mobile', False)
    render_section_card(
        title="Státusztörténet", 
        content=render_content,
        color="#6c757d",  # Szürke szín
        icon="📜",
        is_mobile=is_mobile,
        key="status_history_card",
        expanded=None  # Mindig kinyitva
    )

def display_offer_attachments(attachments_data):
    """
    Ajánlat csatolmányainak megjelenítése.
    
    Args:
        attachments_data (list): Csatolmányok adatai
    """
    def render_content():
        if not attachments_data:
            st.info("Nincsenek csatolmányok ehhez az ajánlathoz.")
            return
        
        # Csatolmányok listázása
        for attachment in attachments_data:
            col1, col2 = st.columns([3, 1])
            with col1:
                st.markdown(f"**{attachment.get('filename', 'Ismeretlen fájl')}**")
                st.markdown(f"Feltöltve: {format_datetime(attachment.get('created_at', ''))}")
            with col2:
                if st.button("Letöltés", key=f"download_{attachment.get('id')}"):
                    # Itt implementálható a letöltés funkció
                    st.info("Letöltés funkció implementálása folyamatban...")
    
    # Az új kártyarendszert használjuk
    is_mobile = st.session_state.get('is_mobile', False)
    render_section_card(
        title="Csatolmányok", 
        content=render_content,
        color="#28a745",  # Zöld szín
        icon="📎",
        is_mobile=is_mobile,
        key="attachments_card",
        expanded=None  # Mindig kinyitva
    )

def show_operation_feedback(operation_name, success, result_message=None, error_message=None, duration=3):
    """
    Vizuálisan attraktív visszajelzés megjelenítése műveletek elvégzéséről.
    
    Ez a függvény egy vizuálisan feltűnő, animált visszajelzést ad a felhasználónak
    a művelet sikerességéről vagy esetleges hibájáról.
    
    Args:
        operation_name (str): A művelet neve, ami megjelenik a visszajelzésben
        success (bool): Sikeres volt-e a művelet
        result_message (str, optional): Egyéni sikeres üzenet. Defaults to None.
        error_message (str, optional): Egyéni hibaüzenet. Defaults to None.
        duration (int, optional): Másodpercek száma, ameddig látható. Defaults to 3.
    """
    if "feedback_shown" not in st.session_state:
        st.session_state.feedback_shown = False
    
    # Ha már megjelenítettük a visszajelzést, nem mutatjuk újra
    if st.session_state.feedback_shown:
        return
    
    # Alapértelmezett üzenetek
    if not result_message:
        result_message = f"{operation_name} sikeresen végrehajtva!"
    if not error_message:
        error_message = f"Hiba történt a(z) {operation_name} során."
    
    # Stílus definíció
    feedback_style = """
    <style>
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-20px); }
        10% { opacity: 1; transform: translateY(0); }
        90% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-20px); }
    }
    .feedback-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        animation: fadeInOut 3s ease forwards;
    }
    .success-feedback {
        background-color: #28a745;
        color: white;
    }
    .error-feedback {
        background-color: #dc3545;
        color: white;
    }
    .feedback-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .feedback-icon {
        font-size: 20px;
        margin-right: 10px;
    }
    </style>
    """
    
    # HTML tartalom létrehozása
    if success:
        container_class = "success-feedback"
        icon = "✅"
        title = "Sikeres művelet"
        message = result_message
    else:
        container_class = "error-feedback"
        icon = "❌"
        title = "Hiba történt"
        message = error_message
    
    html_content = f"""
    {feedback_style}
    <div class="feedback-container {container_class}">
        <div class="feedback-title">
            <span class="feedback-icon">{icon}</span>
            {title}
        </div>
        <div class="feedback-message">{message}</div>
    </div>
    """
    
    # Megjelenítjük a visszajelzést
    st.markdown(html_content, unsafe_allow_html=True)
    
    # Beállítjuk, hogy megjelent a visszajelzés
    st.session_state.feedback_shown = True
    
    # Timer beállítása az újratöltéshez a megadott idő után
    # Ezt JavaScript-tel oldjuk meg
    js_timer = f"""
    <script>
        setTimeout(function() {{
            // Töröljük a visszajelzés-mutatót a sessionből
            window.sessionStorage.removeItem('feedback_shown');
            // Frissítés csak, ha még mindig ezen az oldalon vagyunk
            if (window.location.href.includes('operator_offer_management')) {{
                window.location.reload();
            }}
        }}, {duration * 1000});
    </script>
    """
    
    st.markdown(js_timer, unsafe_allow_html=True)

def display_status_change_feedback(old_status, new_status, success):
    """
    Vizuálisan informatív visszajelzés megjelenítése státuszváltozáskor.
    
    A függvény megjeleníti, hogy hogyan változott az ajánlat státusza,
    és szöveges magyarázatot ad a változás jelentéséről.
    
    Args:
        old_status (str): Az ajánlat korábbi státusza
        new_status (str): Az ajánlat új státusza
        success (bool): Sikeres volt-e a művelet
    """
    if not success:
        show_operation_feedback(
            "státuszváltoztatás", 
            False, 
            error_message=f"Nem sikerült a státuszt megváltoztatni erre: {format_status(new_status)}"
        )
        return
    
    # Státuszmagyarázatok szótára
    status_explanations = {
        "pending": "Az ajánlat feldolgozásra vár.",
        "approved": "Az ajánlat elfogadásra került, a termelő értesítést kap.",
        "rejected": "Az ajánlat elutasításra került, a termelő értesítést kap.",
        "in_progress": "Az ajánlat feldolgozás alatt áll.",
        "completed": "Az ajánlat teljesítve lett, a folyamat lezárult.",
        "canceled": "Az ajánlat törlésre került, a folyamat megszakadt."
    }
    
    # Alapértelmezett magyarázat, ha nem találunk specifikusat
    explanation = status_explanations.get(
        new_status, 
        f"Az ajánlat státusza megváltozott: {format_status(old_status)} → {format_status(new_status)}"
    )
    
    # Státuszváltás megjelenítése
    success_message = f"""
    Státusz sikeresen megváltoztatva: 
    <span style="color:#6c757d">{format_status(old_status)}</span> → 
    <span style="font-weight:bold;color:#28a745">{format_status(new_status)}</span>
    <br/><br/>
    <span style="font-size:0.9em">{explanation}</span>
    """
    
    show_operation_feedback(
        "státuszváltoztatás", 
        True, 
        result_message=success_message
    )

def display_offer_actions(offer):
    """
    Ajánlathoz kapcsolódó műveletek megjelenítése
    
    Args:
        offer (dict): Az ajánlat adatai
    """
    # Súgószöveg a műveletek szekcióhoz
    actions_help = """
    **Műveleti segédlet:**
    
    **Státuszváltások:**
    - **Jóváhagyás**: Az ajánlat elfogadása, a termelő értesítést kap.
    - **Elutasítás**: Az ajánlat elutasítása indoklással.
    - **Feldolgozás alatt**: Jelzi, hogy az ajánlat feldolgozás alatt áll.
    
    **Egyéb műveletek:**
    - **Teljesítve**: Az ajánlat lezárása sikeres teljesítéssel.
    - **Törlés**: Az ajánlat törlése a rendszerből (nem visszavonható).
    
    A státuszváltozások naplózásra kerülnek és nyomon követhetők a státusztörténetben.
    """
    
    # Gomb stílusok definíciója
    button_styles = {
        "approved": "success",
        "rejected": "danger",
        "in_progress": "primary",
        "pending": "warning",
        "completed": "info",
        "canceled": "dark"
    }
    
    st.markdown("### Műveletek")
    render_tooltip(actions_help, icon="🔧", placement="top", key="actions_help")
    
    # Az ajánlat aktuális státusza
    current_status = offer.get("status", "")
    
    # Státuszváltás gombok
    if current_status != "completed" and current_status != "canceled":
        st.markdown("#### Státuszváltás")
        
        # Státuszváltási gombok definíciója
        status_buttons = [
            {
                "label": "Jóváhagyás",
                "icon": "✅",
                "type": "primary" if current_status != "approved" else "secondary",
                "disabled": current_status == "approved",
                "key": "approve_btn",
                "tooltip": "Az ajánlat elfogadása. A termelő értesítést kap, és a termék beszállíthatóvá válik."
            },
            {
                "label": "Elutasítás",
                "icon": "❌",
                "type": "secondary",
                "disabled": current_status == "rejected",
                "key": "reject_btn",
                "tooltip": "Az ajánlat elutasítása. Kérjük, adja meg az elutasítás okát."
            },
            {
                "label": "Feldolgozás alatt",
                "icon": "🔄",
                "type": "primary" if current_status != "in_progress" else "secondary",
                "disabled": current_status == "in_progress",
                "key": "in_progress_btn",
                "tooltip": "Jelzi, hogy az ajánlat feldolgozás alatt áll, de még nem került végleges döntés."
            }
        ]
        
        # Gombok renderelése
        clicks = create_action_button_group(status_buttons, columns=3)
        
        # Gombkattintások kezelése
        if clicks[0]:  # Jóváhagyás
            # Megerősítő dialógus megjelenítése
            st.session_state[f"approve_dialog_{offer['id']}_open"] = True
            
        elif clicks[1]:  # Elutasítás
            # Megerősítő dialógus megjelenítése
            st.session_state[f"reject_dialog_{offer['id']}_open"] = True
            
        elif clicks[2]:  # Feldolgozás alatt
            # Megerősítő dialógus megjelenítése
            st.session_state[f"progress_dialog_{offer['id']}_open"] = True
        
        # Jóváhagyás megerősítő dialógus
        if f"approve_dialog_{offer['id']}_open" in st.session_state and st.session_state[f"approve_dialog_{offer['id']}_open"]:
            approve_confirmed = render_confirmation_dialog(
                title="Ajánlat jóváhagyása",
                message="Biztosan jóváhagyja ezt az ajánlatot? A termelő értesítést fog kapni.",
                confirm_text="Jóváhagyás",
                cancel_text="Mégsem",
                icon="✅",
                key=f"approve_dialog_{offer['id']}"
            )
            
            # Ha a felhasználó megerősítette, végrehajtsuk a műveletet
            if approve_confirmed:
                success, result = offers_api.update_offer_status(
                    offer_id=offer["id"],
                    status="approved",
                    note="Ajánlat jóváhagyva az ügyintéző által."
                )
                
                # Visszajelzés megjelenítése
                display_status_change_feedback(current_status, "approved", success)
                
                if success:
                    # Újratöltjük a gyorsítótárat
                    st.session_state.offer_cache[offer["id"]]["status"] = "approved"
        
        # Elutasítás megerősítő dialógus
        if f"reject_dialog_{offer['id']}_open" in st.session_state and st.session_state[f"reject_dialog_{offer['id']}_open"]:
            # Itt kérjünk megjegyzést
            note = st.text_input("Elutasítás indoklása:", key="reject_note")
            
            reject_confirmed = render_confirmation_dialog(
                title="Ajánlat elutasítása",
                message="Biztosan elutasítja ezt az ajánlatot? A termelő értesítést fog kapni az elutasításról.",
                confirm_text="Elutasítás",
                cancel_text="Mégsem",
                icon="❌",
                key=f"reject_dialog_{offer['id']}"
            )
            
            # Ha a felhasználó megerősítette, végrehajtsuk a műveletet
            if reject_confirmed:
                success, result = offers_api.update_offer_status(
                    offer_id=offer["id"],
                    status="rejected",
                    note=note if note else "Ajánlat elutasítva az ügyintéző által."
                )
                
                # Visszajelzés megjelenítése
                display_status_change_feedback(current_status, "rejected", success)
                
                if success:
                    # Újratöltjük a gyorsítótárat
                    st.session_state.offer_cache[offer["id"]]["status"] = "rejected"
        
        # Feldolgozás alatt megerősítő dialógus
        if f"progress_dialog_{offer['id']}_open" in st.session_state and st.session_state[f"progress_dialog_{offer['id']}_open"]:
            progress_confirmed = render_confirmation_dialog(
                title="Ajánlat feldolgozás alatt",
                message="Biztosan feldolgozás alá helyezi ezt az ajánlatot?",
                confirm_text="Feldolgozás alatt",
                cancel_text="Mégsem",
                icon="🔄",
                key=f"progress_dialog_{offer['id']}"
            )
            
            # Ha a felhasználó megerősítette, végrehajtsuk a műveletet
            if progress_confirmed:
                success, result = offers_api.update_offer_status(
                    offer_id=offer["id"],
                    status="in_progress",
                    note="Ajánlat feldolgozás alatt."
                )
                
                # Visszajelzés megjelenítése
                display_status_change_feedback(current_status, "in_progress", success)
                
                if success:
                    # Újratöltjük a gyorsítótárat
                    st.session_state.offer_cache[offer["id"]]["status"] = "in_progress"
    
    # Lezárás vagy törlés gombok
    st.markdown("#### Egyéb műveletek")
    
    # Egyéb műveleti gombok definíciója
    other_buttons = [
        {
            "label": "Teljesítve",
            "icon": "✅",
            "type": "primary" if current_status != "completed" else "secondary",
            "disabled": current_status == "completed",
            "key": "complete_btn",
            "tooltip": "Az ajánlat teljesítettként megjelölése, ami lezárja a folyamatot."
        },
        {
            "label": "Törlés",
            "icon": "🗑️",
            "type": "secondary",
            "disabled": current_status == "canceled",
            "key": "cancel_btn",
            "tooltip": "Az ajánlat törlése a rendszerből. Ez a művelet nem visszavonható!"
        }
    ]
    
    # Gombok renderelése
    other_clicks = create_action_button_group(other_buttons, columns=2)
    
    # Gombkattintások kezelése
    if other_clicks[0]:  # Teljesítve
        # Megerősítő dialógus megjelenítése
        st.session_state[f"complete_dialog_{offer['id']}_open"] = True
        
    elif other_clicks[1]:  # Törlés
        # Megerősítő dialógus megjelenítése
        st.session_state[f"cancel_dialog_{offer['id']}_open"] = True
    
    # Teljesítés megerősítő dialógus
    if f"complete_dialog_{offer['id']}_open" in st.session_state and st.session_state[f"complete_dialog_{offer['id']}_open"]:
        complete_confirmed = render_confirmation_dialog(
            title="Ajánlat teljesítése",
            message="Biztosan teljesítettként jelöli ezt az ajánlatot? Ez lezárja a folyamatot.",
            confirm_text="Teljesítés",
            cancel_text="Mégsem",
            icon="✅",
            key=f"complete_dialog_{offer['id']}"
        )
        
        # Ha a felhasználó megerősítette, végrehajtsuk a műveletet
        if complete_confirmed:
            success, result = offers_api.update_offer_status(
                offer_id=offer["id"],
                status="completed",
                note="Ajánlat teljesítve és lezárva."
            )
            
            # Visszajelzés megjelenítése
            display_status_change_feedback(current_status, "completed", success)
            
            if success:
                # Újratöltjük a gyorsítótárat
                st.session_state.offer_cache[offer["id"]]["status"] = "completed"
    
    # Törlés megerősítő dialógus
    if f"cancel_dialog_{offer['id']}_open" in st.session_state and st.session_state[f"cancel_dialog_{offer['id']}_open"]:
        # Itt kérjünk megjegyzést
        note = st.text_input("Törlés indoklása:", key="cancel_note")
        
        cancel_confirmed = render_confirmation_dialog(
            title="Ajánlat törlése",
            message="FIGYELEM! Biztosan törölni szeretné ezt az ajánlatot? Ez a művelet nem visszavonható!",
            confirm_text="Törlés",
            cancel_text="Mégsem",
            icon="⚠️",
            key=f"cancel_dialog_{offer['id']}"
        )
        
        # Ha a felhasználó megerősítette, végrehajtsuk a műveletet
        if cancel_confirmed:
            success, result = offers_api.update_offer_status(
                offer_id=offer["id"],
                status="canceled",
                note=note if note else "Ajánlat törölve az ügyintéző által."
            )
            
            # Visszajelzés megjelenítése
            display_status_change_feedback(current_status, "canceled", success)
            
            if success:
                # Újratöltjük a gyorsítótárat
                st.session_state.offer_cache[offer["id"]]["status"] = "canceled"
                
                # Visszatérünk a listához
                st.markdown("""
                <script>
                    setTimeout(function() {
                        window.location.href = '?';
                    }, 3000);
                </script>
                """, unsafe_allow_html=True)

def create_styled_button(label, icon=None, on_click=None, key=None, type="primary", disabled=False, size="md", help=None, tooltip=None):
    """
    Konzisztens, stílusos gombok létrehozása az alkalmazásban.
    
    Ez a függvény egy egységes megjelenésű és viselkedésű gombot hoz létre,
    amely követi a stílusútmutatónkat és opcionálisan tooltipet is tartalmaz.
    
    Args:
        label (str): A gomb címkéje/szövege
        icon (str, optional): Ikon a gomb előtt. Defaults to None.
        on_click (callable, optional): Eseménykezelő a gombkattintáshoz. Defaults to None.
        key (str, optional): Egyedi kulcs a gombhoz. Defaults to None.
        type (str, optional): Gomb típusa: primary, secondary, success, danger, warning, info. Defaults to "primary".
        disabled (bool, optional): Le van-e tiltva a gomb. Defaults to False.
        size (str, optional): Gomb mérete: sm, md, lg. Defaults to "md".
        help (str, optional): Segítő szöveg a gombhoz. Defaults to None.
        tooltip (str, optional): Tooltip szöveg a gombhoz. Defaults to None.
        
    Returns:
        bool: True ha a gombra kattintottak, False ha nem
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"btn_{label.lower().replace(' ', '_')}_{str(uuid.uuid4())[:8]}"
    
    # Ikon hozzáadása a címkéhez, ha van
    display_label = f"{icon} {label}" if icon else label
    
    # Biztosítjuk, hogy csak 'primary' vagy 'secondary' típust használjunk
    # Az egyéb típusokat konvertáljuk ezekre
    button_type = "primary"
    if type in ["secondary", "warning", "danger", "dark"]:
        button_type = "secondary"
    
    # Gomb létrehozása a megfelelő mérettel
    if size == "sm":
        container = st.container()
        with container:
            st.markdown(f"""<div style="transform: scale(0.8); transform-origin: left top;">""", unsafe_allow_html=True)
            clicked = st.button(
                display_label,
                key=key,
                type=button_type,
                disabled=disabled,
                help=help,
                on_click=on_click if on_click else None
            )
            st.markdown("</div>", unsafe_allow_html=True)
    elif size == "lg":
        # Nagy méretű gomb CSS stílussal
        st.markdown(f"""
        <style>
        .lg-button-{key} {{
            font-size: 1.2em;
            font-weight: bold;
        }}
        </style>
        """, unsafe_allow_html=True)
        clicked = st.button(
            display_label,
            key=key,
            type=button_type,
            disabled=disabled,
            help=help,
            on_click=on_click if on_click else None
        )
        # Utólag adjuk hozzá a CSS osztályt JavaScript segítségével
        st.markdown(f"""
        <script>
        document.addEventListener('DOMContentLoaded', function() {{
            var btn = document.querySelector('button[data-testid="{key}"]');
            if (btn) {{
                btn.classList.add('lg-button-{key}');
            }}
        }});
        </script>
        """, unsafe_allow_html=True)
    else:  # md
        clicked = st.button(
            display_label,
            key=key,
            type=button_type,
            disabled=disabled,
            help=help,
            on_click=on_click if on_click else None
        )
    
    # Tooltip hozzáadása, ha van
    if tooltip and clicked is False:  # Csak akkor jelenítjük meg, ha nem kattintottak rá
        tooltip_id = f"{key}_tooltip"
        
        # A tooltip HTML és stílus létrehozása
        tooltip_style = f"""
        <style>
        .btn-tooltip-{tooltip_id} {{
            position: relative;
            display: inline-block;
        }}
        
        .btn-tooltip-{tooltip_id} .tooltiptext {{
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8em;
        }}
        
        .btn-tooltip-{tooltip_id}:hover .tooltiptext {{
            visibility: visible;
            opacity: 1;
        }}
        </style>
        <script>
        // Tooltip elhelyezése a gomb fölé
        document.addEventListener('DOMContentLoaded', function() {{
            var btn = document.querySelector('button[aria-describedby="{key}"]');
            if (btn) {{
                var tooltipDiv = document.createElement('div');
                tooltipDiv.className = 'btn-tooltip-{tooltip_id}';
                var tooltipText = document.createElement('span');
                tooltipText.className = 'tooltiptext';
                tooltipText.innerText = '{tooltip}';
                tooltipDiv.appendChild(tooltipText);
                btn.parentNode.insertBefore(tooltipDiv, btn);
                tooltipDiv.appendChild(btn);
            }}
        }});
        </script>
        """
        
        st.markdown(tooltip_style, unsafe_allow_html=True)
    
    return clicked

def create_action_button_group(buttons, columns=None):
    """
    Gombok csoportjának létrehozása konzisztens elrendezéssel.
    
    Args:
        buttons (list): Gombok definíciója, minden elem egy dict a következő kulcsokkal:
            - label: gomb címkéje
            - icon: ikon a gombra (opcionális)
            - on_click: kattintási eseménykezelő (opcionális)
            - key: egyedi kulcs (opcionális)
            - type: gomb típusa (opcionális, alapértelmezett: "primary")
            - disabled: le van-e tiltva (opcionális, alapértelmezett: False)
            - size: méret (opcionális, alapértelmezett: "md")
            - help: segítő szöveg (opcionális)
            - tooltip: tooltip szöveg (opcionális)
        columns (int, optional): Oszlopok száma. Defaults to None (egyenlő a gombok számával).
    
    Returns:
        list: A gombkattintások listája (True/False minden gombra)
    """
    # Ha nincs megadva oszlopszám, használjuk a gombok számát
    if columns is None:
        columns = len(buttons)
    
    # Létrehozzuk az oszlopokat
    cols = st.columns(columns)
    
    # Egyenlően osztjuk el a gombokat az oszlopokban
    clicks = []
    for i, button in enumerate(buttons):
        with cols[i % columns]:
            click = create_styled_button(
                label=button["label"],
                icon=button.get("icon"),
                on_click=button.get("on_click"),
                key=button.get("key"),
                type=button.get("type", "primary"),
                disabled=button.get("disabled", False),
                size=button.get("size", "md"),
                help=button.get("help"),
                tooltip=button.get("tooltip")
            )
            clicks.append(click)
    
    return clicks

def validate_offer_data(offer_data, is_new=True):
    """
    Ajánlat adatok kliens oldali validálása beküldés előtt.
    
    Ez a függvény ellenőrzi az ajánlat adatainak érvényességét mielőtt
    elküldenénk azokat az API-nak. Visszaadja a hibákat és figyelmeztetéseket.
    
    Args:
        offer_data (dict): Az ellenőrizendő ajánlat adatok.
        is_new (bool, optional): Új ajánlat létrehozása vagy meglévő módosítása. Defaults to True.
    
    Returns:
        tuple: (érvényes, hibák, figyelmeztetések) ahol:
            - érvényes (bool): True ha az adatok érvényesek, False egyébként
            - hibák (dict): Mező név és hibaüzenetek szótára
            - figyelmeztetések (dict): Mező név és figyelmeztető üzenetek szótára
    """
    errors = {}
    warnings = {}
    
    # Kötelező mezők ellenőrzése
    required_fields = {
        "product_type_id": "Termék típus",
        "quantity": "Mennyiség",
        "delivery_date": "Beszállítási dátum",
    }
    
    if is_new:
        required_fields["user_id"] = "Termelő"
    
    # Kötelező mezők meglétének ellenőrzése
    for field, field_name in required_fields.items():
        if field not in offer_data or not offer_data[field]:
            errors[field] = f"A(z) {field_name} mező kitöltése kötelező."
    
    # Mennyiség validálása
    if "quantity" in offer_data and offer_data.get("quantity"):
        try:
            qty = float(offer_data["quantity"])
            if qty <= 0:
                errors["quantity"] = "A mennyiségnek pozitív számnak kell lennie."
            elif qty < 1:
                warnings["quantity"] = "1 kg-nál kevesebb mennyiség nem tipikus. Biztos ebben?"
            elif qty > 10000:
                warnings["quantity"] = "Szokatlanul nagy mennyiség. Biztos helyesen adta meg?"
        except ValueError:
            errors["quantity"] = "A mennyiségnek számnak kell lennie."
    
    # Ár validálása (ha meg lett adva)
    if "price" in offer_data and offer_data.get("price"):
        try:
            price = float(offer_data["price"])
            if price < 0:
                errors["price"] = "Az ár nem lehet negatív."
            elif price == 0:
                warnings["price"] = "Ingyenes ajánlatot ad meg. Biztos ebben?"
            elif price > 100000:
                warnings["price"] = "Szokatlanul magas ár. Biztos helyesen adta meg?"
        except ValueError:
            errors["price"] = "Az árnak számnak kell lennie."
    
    # Dátum validálása
    if "delivery_date" in offer_data and offer_data.get("delivery_date"):
        try:
            if isinstance(offer_data["delivery_date"], str):
                delivery_date = datetime.strptime(offer_data["delivery_date"], "%Y-%m-%d").date()
            else:
                delivery_date = offer_data["delivery_date"]
                
            today = datetime.now().date()
            
            if delivery_date < today:
                errors["delivery_date"] = "A beszállítási dátum nem lehet múltbeli."
            elif delivery_date == today:
                warnings["delivery_date"] = "A beszállítás a mai napra van ütemezve. Biztos benne?"
            elif delivery_date > today + timedelta(days=365):
                warnings["delivery_date"] = "A beszállítási dátum több mint egy év múlva van."
        except ValueError:
            errors["delivery_date"] = "Érvénytelen dátumformátum. Használja az ÉÉÉÉ-HH-NN formátumot."
    
    # Termelő ellenőrzése új ajánlat esetén
    if is_new and "user_id" in offer_data and offer_data.get("user_id"):
        # Ellenőrizzük, hogy létezik-e a termelő
        success, producers = get_producers()
        if success:
            producer_ids = [p.get("id") for p in producers]
            if offer_data["user_id"] not in producer_ids:
                errors["user_id"] = "A kiválasztott termelő nem található."
    
    # Termék típus ellenőrzése
    if "product_type_id" in offer_data and offer_data.get("product_type_id"):
        # Itt ellenőrizhetjük, hogy létezik-e a termék, esetleg API hívással
        # A példa kedvéért most csak egy egyszerű ellenőrzést végzünk
        if not isinstance(offer_data["product_type_id"], (int, str)) or not str(offer_data["product_type_id"]).isdigit():
            errors["product_type_id"] = "Érvénytelen termék típus azonosító."
    
    # Megjegyzés hosszának ellenőrzése
    if "note" in offer_data and offer_data.get("note"):
        if len(str(offer_data["note"])) > 1000:
            errors["note"] = "A megjegyzés maximum 1000 karakter hosszú lehet."
    
    # Érvényesség meghatározása
    is_valid = len(errors) == 0
    
    return is_valid, errors, warnings

def render_quantity_input(label, key, value=None, min_value=0.1, max_value=10000.0, step=0.1, help=None):
    """
    Mennyiség beviteli mező renderelése beépített validációval.
    
    Ez a függvény egy mennyiség beviteli mezőt jelenít meg valós idejű validációval
    és vizuális visszajelzéssel a felhasználó számára.
    
    Args:
        label (str): A mező címkéje.
        key (str): Egyedi kulcs a komponenshez.
        value (float, optional): Alapértelmezett érték. Defaults to None.
        min_value (float, optional): Minimum megengedett érték. Defaults to 0.1.
        max_value (float, optional): Maximum megengedett érték. Defaults to 10000.0.
        step (float, optional): Lépésköz. Defaults to 0.1.
        help (str, optional): Segítő szöveg. Defaults to None.
        
    Returns:
        float/None: A megadott mennyiség vagy None, ha érvénytelen.
    """
    # Ha nincs megadva value, üres stringet használunk kezdőértéknek
    if value is None:
        value = ""
    
    # Előző érték nyilvántartása a session state-ben
    if f"{key}_prev" not in st.session_state:
        st.session_state[f"{key}_prev"] = value
    
    # Hiba állapot nyilvántartása
    if f"{key}_error" not in st.session_state:
        st.session_state[f"{key}_error"] = ""
    
    # A beviteli mező megjelenítése
    input_container = st.container()
    with input_container:
        input_col, error_col = st.columns([3, 2])
        
        # Beviteli mező saját column-ban
        with input_col:
            quantity_str = st.text_input(
                label=label,
                value=value,
                key=key,
                help=help or f"Adjon meg egy számot {min_value} és {max_value} között."
            )
        
        # Validáció
        quantity = None
        if quantity_str:
            try:
                quantity = float(quantity_str)
                
                # Érték tartomány ellenőrzése
                if quantity < min_value:
                    st.session_state[f"{key}_error"] = f"A minimum érték {min_value}."
                elif quantity > max_value:
                    st.session_state[f"{key}_error"] = f"A maximum érték {max_value}."
                else:
                    st.session_state[f"{key}_error"] = ""
                    st.session_state[f"{key}_prev"] = quantity
            except ValueError:
                st.session_state[f"{key}_error"] = "A mennyiségnek számnak kell lennie."
                quantity = None
        else:
            st.session_state[f"{key}_error"] = "Ez a mező kötelező."
            quantity = None
        
        # Hibaüzenet megjelenítése
        with error_col:
            if st.session_state[f"{key}_error"]:
                st.markdown(f'<div style="color: red; margin-top: 25px;">{st.session_state[f"{key}_error"]}</div>', unsafe_allow_html=True)
        
    return quantity

def render_confirmation_dialog(title, message, confirm_text="Igen", cancel_text="Nem", icon="⚠️", key=None):
    """
    Megerősítő párbeszédablak renderelése kritikus műveletekhez.
    
    Ez a függvény egy megerősítő párbeszédablakot jelenít meg, amely
    a felhasználó explicit megerősítését kéri a kritikus műveletek
    (pl. törlés, státuszváltások) előtt.
    
    Args:
        title (str): A párbeszédablak címe.
        message (str): A megerősítő üzenet.
        confirm_text (str, optional): A megerősítő gomb szövege. Defaults to "Igen".
        cancel_text (str, optional): A mégsem gomb szövege. Defaults to "Nem".
        icon (str, optional): Az ikon. Defaults to "⚠️".
        key (str, optional): Egyedi kulcs a komponenshez. Defaults to None.
        
    Returns:
        bool: True ha a felhasználó megerősíti, False egyébként.
    """
    # Egyedi kulcs generálása, ha nincs megadva
    if key is None:
        key = f"confirm_dialog_{str(uuid.uuid4())[:8]}"
    
    # Ellenőrizzük, hogy a dialógus állapotát tároltuk-e már
    if f"{key}_open" not in st.session_state:
        st.session_state[f"{key}_open"] = False
        st.session_state[f"{key}_confirmed"] = False
    
    # Ha a nyitó gombra kattintottak, nyissuk meg a dialógust
    if not st.session_state[f"{key}_open"]:
        return False
    
    # A dialógus megjelenítése
    dialog_container = st.container()
    with dialog_container:
        # Stílus a dialógushoz
        st.markdown(f"""
        <style>
        .dialog-container-{key} {{
            background-color: var(--background-color, white);
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .dialog-title-{key} {{
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }}
        .dialog-icon-{key} {{
            font-size: 1.4em;
            margin-right: 10px;
        }}
        .dialog-message-{key} {{
            margin-bottom: 15px;
        }}
        </style>
        """, unsafe_allow_html=True)
        
        # Dialógus struktúra
        st.markdown(f"""
        <div class="dialog-container-{key}">
            <div class="dialog-title-{key}">
                <span class="dialog-icon-{key}">{icon}</span>
                {title}
            </div>
            <div class="dialog-message-{key}">{message}</div>
        </div>
        """, unsafe_allow_html=True)
        
        # Gomb sor
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button(confirm_text, key=f"{key}_confirm", type="primary"):
                st.session_state[f"{key}_confirmed"] = True
                st.session_state[f"{key}_open"] = False
                return True
        
        with col2:
            if st.button(cancel_text, key=f"{key}_cancel"):
                st.session_state[f"{key}_open"] = False
                return False
    
    return False

def show_inline_error(error_message):
    """
    Inline hibaüzenet megjelenítése közvetlenül a hiba után.
    
    Args:
        error_message (str): A megjelenítendő hibaüzenet.
    """
    if error_message:
        st.markdown(f"""
        <div style="color: #721c24; background-color: #f8d7da; 
                    border: 1px solid #f5c6cb; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;">
            ⚠️ {error_message}
        </div>
        """, unsafe_allow_html=True)

def show_inline_warning(warning_message):
    """
    Inline figyelmeztető üzenet megjelenítése közvetlenül a mező után.
    
    Args:
        warning_message (str): A megjelenítendő figyelmeztetés.
    """
    if warning_message:
        st.markdown(f"""
        <div style="color: #856404; background-color: #fff3cd; 
                    border: 1px solid #ffeeba; padding: 8px 12px; 
                    border-radius: 4px; margin: 5px 0; font-size: 0.9em;">
            ⚠️ {warning_message}
        </div>
        """, unsafe_allow_html=True)

def inject_screen_detection():
    """
    JavaScript kód injektálása a képernyőméret érzékeléséhez.
    
    Ez a függvény JavaScript kódot injektál a Streamlit oldalba, amely
    érzékeli a felhasználó eszközének képernyőméretét, és beállítja a
    megfelelő változókat a session state-ben a reszponzív megjelenítéshez.
    """
    # JavaScript kód a képernyőméret érzékeléséhez
    js_code = """
    <script>
    // Képernyőméret érzékelése és tárolása a sessionState-ben
    function detectScreenSize() {
        const width = window.innerWidth;
        
        // Méret alapján kategorizálás
        const isMobile = width < 768;
        const isTablet = width >= 768 && width < 992;
        
        // Értékek tárolása a session state-ben
        if (window.parent.streamlitPyConnection) {
            window.parent.streamlitPyConnection.sendMessage({
                type: "streamlit:setSessionState",
                items: [
                    { key: "is_mobile", value: isMobile },
                    { key: "is_tablet", value: isTablet },
                    { key: "screen_width", value: width }
                ]
            });
            
            // Oldal újratöltése, ha változott a méret kategória
            const currentMobile = sessionStorage.getItem("is_mobile") === "true";
            if (currentMobile !== isMobile) {
                sessionStorage.setItem("is_mobile", isMobile);
                if (sessionStorage.getItem("screen_detection_initialized") === "true") {
                    window.location.reload();
                }
            }
            sessionStorage.setItem("screen_detection_initialized", "true");
        }
    }
    
    // Kezdeti érzékelés
    detectScreenSize();
    
    // Képernyőméret változás figyelése
    window.addEventListener('resize', detectScreenSize);
    </script>
    """
    
    # JavaScript kód injektálása
    st.markdown(js_code, unsafe_allow_html=True)
    
    # Alapértelmezett értékek, ha a JavaScript még nem futott le
    if "is_mobile" not in st.session_state:
        st.session_state.is_mobile = False
    if "is_tablet" not in st.session_state:
        st.session_state.is_tablet = False
    if "screen_width" not in st.session_state:
        st.session_state.screen_width = 1200  # Asztali alapértelmezett

@st.cache_data(ttl=300)
def prepare_offer_display_data(offers):
    """
    Előkészíti az ajánlatok adatait a megjelenítéshez, és gyorsítótárazza az eredményt.
    
    Ez a függvény átalakítja a nyers API válaszban lévő ajánlatok adatait könnyen
    megjeleníthető formátumba, beleértve a dátumok formázását, státusz információk
    szöveges megjelenítését, stb.
    
    Args:
        offers (list): A nyers ajánlatok listája az API-ból
    
    Returns:
        list: A megjelenítésre előkészített ajánlatok listája
    """
    display_data = []
    
    for offer in offers:
        # Alapadatok kinyerése
        offer_id = offer.get("id")
        status = offer.get("status")
        product_type = offer.get("product_type", {})
        user = offer.get("user", {})
        quantity = offer.get("quantity")
        delivery_date = offer.get("delivery_date")
        
        # Adatok formázása
        formatted_status = format_status(status)
        formatted_quantity = format_quantity(quantity, product_type.get("unit", "kg"))
        formatted_date = format_date(delivery_date)
        
        # Előkészített adatok szótára
        display_item = {
            "id": offer_id,
            "státusz": formatted_status,
            "termék": product_type.get("name", "Ismeretlen termék"),
            "termelő": user.get("company_name") or user.get("contact_name") or "Ismeretlen termelő",
            "mennyiség": formatted_quantity,
            "beszállítási_dátum": formatted_date,
            # Eredeti adatok megőrzése referenciaként
            "raw_data": offer
        }
        
        display_data.append(display_item)
    
    return display_data

def display_mobile_offer_card(offer, index, on_click_func=None):
    """
    Mobilbarát kártya megjelenítése az ajánlathoz.
    
    Ez a függvény mobileszközökre optimalizált, kompakt kártyaként jeleníti meg
    az ajánlat legfontosabb információit.
    
    Args:
        offer (dict): Az ajánlat adatai
        index (int): Az ajánlat sorszáma/indexe a listában
        on_click_func (callable, optional): Függvény, ami meghívódik a kártyára kattintáskor
    
    Returns:
        None
    """
    # Adatok kinyerése
    offer_id = offer.get("id")
    status = format_status(offer.get("status"))
    product_name = offer.get("product_type", {}).get("name", "Ismeretlen termék")
    producer_name = offer.get("user", {}).get("company_name") or offer.get("user", {}).get("contact_name") or "Ismeretlen termelő"
    quantity = format_quantity(offer.get("quantity"), offer.get("product_type", {}).get("unit", "kg"))
    delivery_date = format_date(offer.get("delivery_date"))
    
    # Státusz színkód
    status_colors = {
        "pending": "#FFC107",      # Sárga
        "approved": "#28A745",     # Zöld
        "rejected": "#DC3545",     # Piros
        "in_progress": "#17A2B8",  # Kék
        "completed": "#6C757D",    # Szürke
        "canceled": "#343A40"      # Sötétszürke
    }
    status_color = status_colors.get(offer.get("status"), "#FFC107")
    
    # Kártya HTML kód generálása
    card_html = f"""
    <div style="border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px; padding: 10px; 
                background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); position: relative;">
        <div style="position: absolute; top: 0; right: 0; height: 100%; width: 8px; 
                    background-color: {status_color}; border-top-right-radius: 8px; 
                    border-bottom-right-radius: 8px;"></div>
        <div style="font-weight: bold; font-size: 16px; margin-bottom: 5px;">
            {product_name} <span style="color: #6c757d; font-size: 12px;">#{offer_id}</span>
        </div>
        <div style="color: #555; margin-bottom: 5px;">Termelő: {producer_name}</div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <span>Mennyiség: {quantity}</span>
            <span>Beszállítás: {delivery_date}</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="background-color: {status_color}; color: white; padding: 3px 8px; 
                     border-radius: 12px; font-size: 12px;">
                {status}
            </div>
            <button onclick="streamlit.setComponentValue('view_offer_{offer_id}', true)" 
                    style="background-color: #3584e4; color: white; border: none; 
                           padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                Megtekintés
            </button>
        </div>
    </div>
    """
    
    # HTML megjelenítése
    st.markdown(card_html, unsafe_allow_html=True)
    
    # Eseménykezelő a kártyára kattintáshoz
    key = f"view_offer_{offer_id}"
    val = st.session_state.get(key, False)
    if val:
        st.session_state[key] = False
        if on_click_func:
            on_click_func()

def export_offers_to_csv(offers):
    """
    Ajánlatok exportálása CSV formátumba.
    
    Ez a függvény az ajánlatok listáját CSV formátumba konvertálja,
    és egy letölthető linket ad vissza.
    
    Args:
        offers (list): Az exportálandó ajánlatok listája
        
    Returns:
        str: HTML kód a letöltési linkkel
    """
    import pandas as pd
    import base64
    from io import StringIO
    
    # Ellenőrizzük, hogy van-e exportálandó adat
    if not offers:
        return "<p style='color:red'>Nincs exportálható adat</p>"
    
    try:
        # Adatok előkészítése a DataFrame-hez
        export_data = []
        for offer in offers:
            # Alapadatok kinyerése
            status = format_status(offer.get("status"))
            product_name = offer.get("product_type", {}).get("name", "Ismeretlen termék")
            producer_name = offer.get("user", {}).get("company_name") or offer.get("user", {}).get("contact_name") or "Ismeretlen termelő"
            quantity = offer.get("quantity")
            unit = offer.get("product_type", {}).get("unit", "kg")
            delivery_date = offer.get("delivery_date")
            created_at = offer.get("created_at")
            note = offer.get("note", "")
            
            # Adat hozzáadása
            export_data.append({
                "Azonosító": offer.get("id"),
                "Státusz": status,
                "Termék": product_name,
                "Termelő": producer_name,
                "Mennyiség": quantity,
                "Egység": unit,
                "Beszállítási dátum": delivery_date,
                "Létrehozva": created_at,
                "Megjegyzés": note
            })
        
        # DataFrame létrehozása
        df = pd.DataFrame(export_data)
        
        # CSV konvertálás
        csv = df.to_csv(index=False)
        
        # Base64 kódolás a letöltéshez
        b64 = base64.b64encode(csv.encode()).decode()
        
        # Fájlnév generálása
        import datetime
        now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ajanlatok_export_{now}.csv"
        
        # Letöltési link generálása
        href = f'<a href="data:file/csv;base64,{b64}" download="{filename}" target="_blank">CSV letöltése</a>'
        
        return href
    
    except Exception as e:
        return f"<p style='color:red'>Hiba az exportálás során: {str(e)}</p>"

def export_offers_to_excel(offers):
    """
    Ajánlatok exportálása Excel formátumba.
    
    Ez a függvény az ajánlatok listáját Excel (.xlsx) formátumba konvertálja,
    és egy letölthető linket ad vissza.
    
    Args:
        offers (list): Az exportálandó ajánlatok listája
        
    Returns:
        str: HTML kód a letöltési linkkel
    """
    import pandas as pd
    import base64
    from io import BytesIO
    
    # Ellenőrizzük, hogy van-e exportálandó adat
    if not offers:
        return "<p style='color:red'>Nincs exportálható adat</p>"
    
    try:
        # Adatok előkészítése a DataFrame-hez
        export_data = []
        for offer in offers:
            # Alapadatok kinyerése
            status = format_status(offer.get("status"))
            product_name = offer.get("product_type", {}).get("name", "Ismeretlen termék")
            producer_name = offer.get("user", {}).get("company_name") or offer.get("user", {}).get("contact_name") or "Ismeretlen termelő"
            quantity = offer.get("quantity")
            unit = offer.get("product_type", {}).get("unit", "kg")
            delivery_date = offer.get("delivery_date")
            created_at = offer.get("created_at")
            note = offer.get("note", "")
            
            # Adat hozzáadása
            export_data.append({
                "Azonosító": offer.get("id"),
                "Státusz": status,
                "Termék": product_name,
                "Termelő": producer_name,
                "Mennyiség": quantity,
                "Egység": unit,
                "Beszállítási dátum": delivery_date,
                "Létrehozva": created_at,
                "Megjegyzés": note
            })
        
        # DataFrame létrehozása
        df = pd.DataFrame(export_data)
        
        # Excel konvertálás
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Ajánlatok', index=False)
            # Automatikus oszlopszélesség beállítása
            worksheet = writer.sheets['Ajánlatok']
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        # Puffer pozícionálása az elejére
        output.seek(0)
        
        # Base64 kódolás a letöltéshez
        b64 = base64.b64encode(output.read()).decode()
        
        # Fájlnév generálása
        import datetime
        now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ajanlatok_export_{now}.xlsx"
        
        # Letöltési link generálása
        href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="{filename}" target="_blank">Excel letöltése</a>'
        
        return href
    
    except Exception as e:
        return f"<p style='color:red'>Hiba az exportálás során: {str(e)}</p>"

def display_offer_table_with_actions(offers, pagination=True):
    """
    Ajánlatok táblázatos megjelenítése különálló műveletgombokkal, amelyek a kiválasztott sorra vonatkoznak.
    
    Args:
        offers (list): Az ajánlatok listája
        pagination (bool, optional): Lapozás engedélyezése. Defaults to True.
        
    Returns:
        dataframe: A megjelenített dataframe
    """
    # Lapozási beállítások
    if pagination and len(offers) > 20:
        if "table_page" not in st.session_state:
            st.session_state.table_page = 0
        
        page_size = 20
        start_idx = st.session_state.table_page * page_size
        end_idx = min(start_idx + page_size, len(offers))
        
        # Lapozási információ megjelenítése
        st.write(f"**Megjelenítés: {start_idx+1}-{end_idx} / {len(offers)} ajánlat**")
        
        # Lapozó gombok
        col1, col2, col3 = st.columns([2, 4, 2])
        with col1:
            if st.button("◀ Előző oldal", disabled=st.session_state.table_page == 0, key="table_prev_page", use_container_width=True):
                st.session_state.table_page = max(0, st.session_state.table_page - 1)
                st.experimental_rerun()
        
        with col3:
            max_page = (len(offers) - 1) // page_size
            if st.button("Következő oldal ▶", disabled=st.session_state.table_page >= max_page, key="table_next_page", use_container_width=True):
                st.session_state.table_page = min(max_page, st.session_state.table_page + 1)
                st.experimental_rerun()
        
        # Oldalon megjelenített ajánlatok
        paged_offers = offers[start_idx:end_idx]
    else:
        paged_offers = offers
    
    # Előkészítjük az adatokat a megjelenítéshez (csak a lapozott adatok)
    display_data = prepare_offer_display_data(paged_offers)
    
    # Mezők az adattáblázathoz
    offer_fields = {
        "id": "id",
        "státusz": "státusz",
        "termék": "termék",
        "termelő": "termelő",
        "mennyiség": "mennyiség",
        "beszállítási_dátum": "beszállítási_dátum"
    }
    
    # Adattáblázat megjelenítése
    table_data = []
    for offer in display_data:
        row = {field: offer.get(value) for field, value in offer_fields.items()}
        table_data.append(row)
    
    # Státusz-színekkel bővített oszlop konfiguráció
    columns = {
        "id": st.column_config.NumberColumn(format="%d", width="small", label="ID"),
        "státusz": st.column_config.TextColumn(width="medium", label="Státusz"),
        "termék": st.column_config.TextColumn(width="medium", label="Termék"),
        "termelő": st.column_config.TextColumn(width="medium", label="Termelő"),
        "mennyiség": st.column_config.TextColumn(width="small", label="Mennyiség"),
        "beszállítási_dátum": st.column_config.TextColumn(width="medium", label="Beszállítás dátuma")
    }
    
    # Táblázat megjelenítése
    df = st.dataframe(
        table_data,
        column_config=columns,
        hide_index=True,
        use_container_width=True
    )
    
    # Műveleteket külön jelenítjük meg a táblázat alatt
    if len(paged_offers) > 0:
        st.write("### Műveletek")
        
        # Ajánlat azonosítók listája a kiválasztáshoz
        offer_ids = [offer.get("id") for offer in paged_offers]
        offer_names = [f"#{offer.get('id')} - {offer.get('product_type', {}).get('name', 'Ismeretlen termék')}" for offer in paged_offers]
        
        # Kiválasztó widget
        selected_index = st.selectbox(
            "Válasszon ajánlatot a műveletekhez:",
            range(len(offer_ids)),
            format_func=lambda i: offer_names[i]
        )
        
        # Kiválasztott ajánlat és státusz lekérése
        selected_offer = paged_offers[selected_index]
        selected_id = selected_offer.get("id")
        status = selected_offer.get("status", "")
        
        # Gombokat oszlopokban jelenítjük meg
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("Részletek", key=f"view_{selected_id}", use_container_width=True):
                st.session_state.selected_offer_id = selected_id
                st.experimental_rerun()  # Régebbi Streamlit verziókhoz kompatibilis
        
        with col2:
            disabled = status not in ["CREATED", "CONFIRMED_BY_COMPANY", "pending", "approved"]
            if st.button("Szerkesztés", key=f"edit_{selected_id}", use_container_width=True, disabled=disabled):
                st.session_state.selected_offer_id = selected_id
                st.session_state.edit_mode = True
                st.experimental_rerun()
        
        with col3:
            disabled = status not in ["CONFIRMED_BY_COMPANY", "approved"]
            if st.button("Elfogadás", key=f"accept_{selected_id}", use_container_width=True, disabled=disabled):
                success, result = offers_api.update_offer_status(selected_id, "ACCEPTED_BY_USER")
                if success:
                    st.success("Ajánlat elfogadva!")
                    st.experimental_rerun()
                else:
                    st.error(f"Hiba: {result}")
        
        with col4:
            if st.button("Törlés", key=f"delete_{selected_id}", use_container_width=True, type="secondary"):
                if st.checkbox(f"Biztosan törli ezt az ajánlatot: #{selected_id}?", key=f"confirm_delete_{selected_id}"):
                    success, result = offers_api.update_offer_status(selected_id, "canceled")
                    if success:
                        st.success("Ajánlat törölve!")
                        st.experimental_rerun()
                    else:
                        st.error(f"Hiba: {result}")
    
    return df

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Ajánlatok kezelése - {config.APP_NAME}",
        page_icon="🖱️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        st.warning("Kérjük, jelentkezzen be a rendszerbe az ajánlatok kezeléséhez!")
        st.info("Átirányítás a bejelentkezési oldalra...")
        
        # JavaScript átirányítás a bejelentkezési oldalra
        st.markdown("""
        <script>
            setTimeout(function() {
                window.parent.location.href = "/";
            }, 2000);
        </script>
        """, unsafe_allow_html=True)
        
        # Megállítjuk a kód további futását
        st.stop()
    
    # Ha be van jelentkezve, megjelenítjük az ajánlatkezelést
    show_offer_list()