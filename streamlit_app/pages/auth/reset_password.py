"""
Jelszó-visszaállítási oldal.
"""
import streamlit as st
import config
from components.auth_forms import password_reset_request_form, password_reset_form
from urllib.parse import parse_qs
from components.sidebar import render_sidebar

def show_reset_password():
    """
    Jelszó-visszaállítási oldal megjelenítése.
    """
    st.title("Jelszó-visszaállítás")
    
    # Ellenőrizzük, hogy van-e token a query paraméterekben
    query_params = st.query_params
    token = query_params.get("token", [None])[0]
    
    if token:
        # Ha van token, akkor jelszó-visszaállítási űrlapot jelenítünk meg
        success = password_reset_form(token)
        
        # Ha sikeres a jelszó-visszaállítás, átirányítjuk a bejelentkezési oldalra
        if success:
            st.switch_page("pages/auth_login.py")
    else:
        # Ha nincs token, akkor jels<PERSON>-visszaállítási kérelem űrlapot jelenítünk meg
        success = password_reset_request_form()
        
        if success:
            # Visszanavigálás a bejelentkezési oldalra opció
            if st.button("Vissza a bejelentkezéshez", type="secondary"):
                st.switch_page("pages/auth_login.py")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Jelszó-visszaállítás - {config.APP_NAME}",
        page_icon="🔑",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Az eredeti sidebar renderelése
    render_sidebar()
    
    # Megjelenítjük a jelszó-visszaállítási oldalt
    show_reset_password()