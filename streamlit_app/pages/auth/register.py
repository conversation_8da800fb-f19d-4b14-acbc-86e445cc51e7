# Regisztrációs oldal
"""
Regisztrációs oldal.
"""
import streamlit as st
from components.auth_forms import register_form
import config
from components.sidebar import render_sidebar

def show_register():
    """
    Regisztrációs oldal megjelenítése.
    """
    st.title("Regisztráció")

    # Két hasábos elrendezés
    col1, col2 = st.columns([2, 1])

    with col1:
        # Regisztrációs űrlap
        success = register_form()

        # Ha sikeres a regisztráció, megjelenítünk egy információs üzenetet
        if success:
            st.info("Sikeres regisztráció! Kérjük, ellenőrizze e-mail fiókját a megerősítő linkért.")

    with col2:
        # Információs panel
        st.info("""
        ### <PERSON><PERSON><PERSON> van fi<PERSON>?

        Jelentkezzen be meglévő fiókjával.
        """)

        # Átirányítás a bejelentkezési oldalra
        if st.button("Bejelentkezés", type="secondary", use_container_width=True):
            st.switch_page("pages/auth_login.py")

        # Segítség panel
        st.markdown("""
        ### Segítségre van szüksége?

        Ha problémába ütközik a regisztráció során, kérjük, vegye fel a kapcsolatot ügyfélszolgálatunkkal.

        Telefonszám: +36 30 737 0621 
        E-mail: <EMAIL>
        """)
# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Regisztráció - {config.APP_NAME}",
        page_icon="📝",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Az eredeti sidebar renderelése
    render_sidebar()

    # Megjelenítjük a regisztrációs oldalt
    show_register()