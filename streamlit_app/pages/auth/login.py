# Bejelentkezési oldal
"""
Bejelentkezési oldal.
"""
import streamlit as st
from components.auth_forms import login_form
from utils.config import get_page_title
from components.sidebar import render_sidebar
from utils.session import init_session_state, is_authenticated

def show_login():
    """
    Bejelentkezési oldal megjelenítése.
    """
    # Only initialize session state if not already authenticated
    if not st.session_state.get("authenticated", False):
        init_session_state()
    
    # Check if user just logged in successfully
    if st.session_state.get("just_logged_in", False):
        # Clear the flag
        st.session_state.just_logged_in = False
        show_welcome_dashboard()
        return
    
    st.title("Bejelentkezés")
    
    # Két hasábos elrendezés
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Bejelentkezési űrlap
        success = login_form()
        
        # Ha sikeres a bejelentkezés, j<PERSON><PERSON><PERSON>k és frissítjük az oldalt
        if success:
            st.session_state.just_logged_in = True
            st.rerun()
        
        # "Elfelejtett jelszó" link
        st.write("---")
        st.page_link("pages/auth_reset_password.py", label="Elfelejtett jelszó?")
    
    with col2:
        # Információs panel
        st.info("""
        ### Még nincs fiókja?
        
        Regisztráljon, hogy hozzáférjen a rendszerhez.
        """)
        
        # Átirányítás a regisztrációs oldalra
        if st.button("Regisztráció", type="secondary", use_container_width=True):
            st.switch_page("pages/auth_register.py")

def show_welcome_dashboard():
    """
    Üdvözlő irányítópult megjelenítése sikeres bejelentkezés után.
    """
    from utils.session import get_current_user
    
    user = get_current_user()
    
    # Check if user data is available
    if not user:
        st.error("Felhasználói adatok nem érhetők el. Kérjük, jelentkezzen be újra.")
        st.stop()
        return
    
    name = user.get("contact_name", "")
    role = user.get("role", "").lower()
    
    # Welcome header
    st.balloons()
    st.success("🎉 Sikeres bejelentkezés!")
    
    # Welcome message
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(f"""
        <div style="text-align: center; padding: 2rem; border-radius: 10px; background-color: #000000; color: #ffffff; border: 2px solid #4CAF50;">
            <h2 style="color: #ffffff; margin-bottom: 1rem;">👋 Üdvözöljük, {name}!</h2>
            <p style="color: #ffffff; font-size: 1.1rem;">Sikeresen bejelentkezett a rendszerbe.</p>
        </div>
        """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Role-specific navigation options
    role_emoji = "🌱" if role == "termelő" else "📊" if role == "ügyintéző" else "🔧"
    role_display = "Termelő" if role == "termelő" else "Ügyintéző" if role == "ügyintéző" else "Adminisztrátor"
    
    st.subheader(f"{role_emoji} {role_display} funkciók")
    st.info("Válassza ki, hogy mit szeretne csinálni:")
    
    # Navigation buttons based on role
    if role == "termelő":
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🏠 Irányítópult", use_container_width=True, type="primary", key="welcome_producer_dashboard"):
                st.switch_page("pages/producer_dashboard.py")
                
            if st.button("📦 Új ajánlat leadása", use_container_width=True, key="welcome_producer_create_offer"):
                st.switch_page("pages/producer_create_offer.py")
                
            if st.button("👤 Profil szerkesztése", use_container_width=True, key="welcome_producer_profile"):
                st.switch_page("pages/producer_profile.py")
        
        with col2:
            if st.button("📋 Ajánlataim", use_container_width=True, key="welcome_producer_offers"):
                st.switch_page("pages/producer_offers.py")
                
            if st.button("📊 Statisztikáim", use_container_width=True, key="welcome_producer_statistics"):
                st.switch_page("pages/producer_statistics.py")
    
    elif role == "ügyintéző":
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🏠 Irányítópult", use_container_width=True, type="primary", key="welcome_operator_dashboard"):
                st.switch_page("pages/operator_dashboard.py")
                
            if st.button("📋 Ajánlatok kezelése", use_container_width=True, key="welcome_operator_offers"):
                st.switch_page("pages/operator_offers.py")
                
            if st.button("📦 Ajánlat létrehozása", use_container_width=True, key="welcome_operator_create_offer"):
                st.switch_page("pages/operator_create_offer.py")
        
        with col2:
            if st.button("📅 Naptári nézet", use_container_width=True, key="welcome_operator_calendar"):
                st.switch_page("pages/operator_calendar.py")
                
            if st.button("📊 Összegzés és riportok", use_container_width=True, key="welcome_operator_reports"):
                st.switch_page("pages/operator_reports.py")
    
    elif role == "admin":
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🏠 Admin irányítópult", use_container_width=True, type="primary", key="welcome_admin_dashboard"):
                st.switch_page("pages/admin_dashboard.py")
                
            if st.button("👥 Felhasználók kezelése", use_container_width=True, key="welcome_admin_users"):
                st.switch_page("pages/admin_users.py")
                
            if st.button("🍅 Termékek kezelése", use_container_width=True, key="welcome_admin_products"):
                st.switch_page("pages/admin_products.py")
        
        with col2:
            if st.button("🧪 Tesztadat generátor", use_container_width=True, key="welcome_admin_data_generator"):
                st.switch_page("pages/admin_data_generator.py")
                
            if st.button("🌱 Termelői irányítópult", use_container_width=True, key="welcome_admin_producer_dashboard"):
                st.switch_page("pages/producer_dashboard.py")
    
    # Additional info
    st.markdown("---")
    st.info("💡 **Tipp:** A bal oldali menüben is megtalálja az összes elérhető funkciót szerepköre szerint rendezve.")
    
    # Quick logout option
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
        if st.button("🚪 Kijelentkezés", use_container_width=True, key="welcome_logout"):
            from utils.session import clear_session
            clear_session()
            st.rerun()

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=get_page_title("Bejelentkezés"),
        page_icon="🔑",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Az eredeti sidebar renderelése
    render_sidebar()
    
    # Check if user is already authenticated
    if is_authenticated():
        # User is already logged in, show welcome dashboard directly
        from utils.session import get_current_user
        user = get_current_user()
        
        # Double check that we actually have valid user data
        if user and user.get("email"):
            show_welcome_dashboard()
        else:
            # Authentication flag is set but no valid user data, clear session
            from utils.session import clear_session
            clear_session()
            show_login()
    else:
        # User not logged in, show login form
        show_login()
