"""
Je<PERSON><PERSON><PERSON> v<PERSON>llítási oldal
"""
import streamlit as st
import requests
from urllib.parse import parse_qs, urlparse
import time
import sys
import os

# Add the parent directory to sys.path to import app_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app_config import API_HOST

# Oldal konfiguráció
st.set_page_config(
    page_title="<PERSON><PERSON><PERSON><PERSON> vissza<PERSON>llítás - POM APP",
    page_icon="🔐",
    layout="centered"
)

# Fejléc
st.title("🔐 Je<PERSON><PERSON><PERSON> visszaállítás")
st.markdown("---")

# Token lekérése az URL-ből
def get_token_from_url():
    """Token kinyerése az URL query paraméterekből"""
    try:
        # Streamlit query params
        params = st.query_params
        if "token" in params:
            return params["token"]
    except:
        pass
    return None

# Token ellenőrz<PERSON>e
token = get_token_from_url()

if not token:
    st.error("❌ Érvénytelen vagy hiányzó token!")
    st.info("Kérjük, használja az e-mailben kapott linket a jelszó visszaállításához.")
    st.markdown("---")
    if st.button("↩️ Vissza a bejelentkezéshez"):
        st.switch_page("pages/auth_login.py")
else:
    # Jelszó visszaállítási űrlap
    st.info("ℹ️ Adja meg az új jelszavát")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        with st.form("reset_password_form"):
            new_password = st.text_input(
                "Új jelszó",
                type="password",
                placeholder="Minimum 8 karakter",
                help="A jelszónak legalább 8 karakter hosszúnak kell lennie"
            )
            
            confirm_password = st.text_input(
                "Új jelszó megerősítése",
                type="password",
                placeholder="Írja be újra az új jelszót"
            )
            
            submitted = st.form_submit_button("🔐 Jelszó visszaállítása", use_container_width=True)
            
            if submitted:
                # Validáció
                if not new_password:
                    st.error("❌ Kérjük, adja meg az új jelszót!")
                elif len(new_password) < 8:
                    st.error("❌ A jelszónak legalább 8 karakter hosszúnak kell lennie!")
                elif new_password != confirm_password:
                    st.error("❌ A két jelszó nem egyezik!")
                else:
                    # API hívás a jelszó visszaállításhoz
                    try:
                        backend_url = st.session_state.get('backend_url', API_HOST)
                        
                        with st.spinner("Jelszó visszaállítása..."):
                            response = requests.post(
                                f"{backend_url}/api/auth/password-reset-confirm",
                                json={
                                    "token": token,
                                    "new_password": new_password
                                },
                                timeout=10
                            )
                        
                        if response.status_code == 200:
                            st.success("✅ Jelszava sikeresen megváltozott!")
                            st.balloons()
                            
                            # Átirányítás a bejelentkezéshez
                            with st.spinner("Átirányítás a bejelentkezéshez..."):
                                time.sleep(2)
                            st.switch_page("pages/auth_login.py")
                            
                        elif response.status_code == 400:
                            try:
                                error_detail = response.json().get("detail", "Érvénytelen vagy lejárt token")
                            except:
                                error_detail = "Érvénytelen vagy lejárt token"
                            st.error(f"❌ {error_detail}")
                            
                            if "lejárt" in error_detail.lower() or "expired" in error_detail.lower():
                                st.info("ℹ️ Kérjen új jelszó-visszaállítási e-mailt.")
                                
                        else:
                            try:
                                error_msg = response.json().get("detail", "Ismeretlen hiba")
                            except:
                                error_msg = f"HTTP {response.status_code}"
                            st.error(f"❌ Hiba történt: {error_msg}")
                            st.error(f"Debug info: Status code: {response.status_code}")
                            
                    except requests.exceptions.ConnectionError:
                        st.error("❌ Nem sikerült kapcsolódni a szerverhez.")
                        st.error(f"Debug info: Backend URL: {backend_url}")
                        st.info("💡 Ellenőrizze, hogy a backend szerver fut-e.")
                    except requests.exceptions.Timeout:
                        st.error("❌ A kérés túllépte az időkorlátot.")
                    except Exception as e:
                        st.error(f"❌ Váratlan hiba történt: {str(e)}")
                        st.error(f"Debug info: {type(e).__name__}")
    
    # Vissza gomb
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
            st.switch_page("pages/auth_login.py")

# Stílus
st.markdown("""
<style>
    /* Középre igazítás */
    .block-container {
        max-width: 500px;
        padding-top: 3rem;
    }
    
    /* Gombok stílusa */
    .stButton > button {
        background-color: #4CAF50;
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
        border: none;
        transition: background-color 0.3s;
    }
    
    .stButton > button:hover {
        background-color: #45a049;
    }
    
    /* Form elemek */
    .stTextInput > div > div > input {
        border-radius: 5px;
    }
</style>
""", unsafe_allow_html=True)