"""
Admin felhasz<PERSON>zel<PERSON> oldal.
"""
import streamlit as st
import pandas as pd
from api import users as users_api
from api import auth as auth_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.auth_utils import validate_email
from utils.formatting import format_role
import app_config as config

def show_admin_users():
    """
    Admin felhasználókezelő oldal megjelenítése.
    """
    st.title("Felhasználók kezelése")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhaszná<PERSON>ó adatainak lekérése
    user = get_current_user()
    
    # Ellen<PERSON>rizzük, hogy admin-e
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Felhasználók lekérése
    success, result = users_api.get_users()
    
    if not success:
        show_error(f"Hiba a felhasználók lekérésekor: {result}")
        return
    
    users = result
    
    # Felhasználók szűrése
    st.subheader("Felhasználók szűrése")
    
    # Szűrési opciók
    col1, col2, col3 = st.columns(3)
    
    with col1:
        search_query = st.text_input("Keresés (név, e-mail, cégnév)", "")
    
    with col2:
        role_filter = st.selectbox(
            "Szerepkör szűrése", 
            options=["Összes"] + list(config.USER_ROLES.values())
        )
    
    with col3:
        sort_by = st.selectbox(
            "Rendezés", 
            options=["E-mail cím", "Név", "Regisztráció dátuma"]
        )
    
    # Szűrés és rendezés alkalmazása
    filtered_users = users
    
    # Szöveg keresés
    if search_query:
        filtered_users = []
        search_query = search_query.lower()
        
        for user in users:
            if (search_query in user.get("email", "").lower() or
                search_query in user.get("contact_name", "").lower() or
                search_query in (user.get("company_name", "") or "").lower()):
                filtered_users.append(user)
    
    # Szerepkör szűrés
    if role_filter != "Összes":
        # Visszakeresés a kódérték alapján
        role_code = None
        for code, name in config.USER_ROLES.items():
            if name == role_filter:
                role_code = code
                break
        
        if role_code:
            filtered_users = [u for u in filtered_users if u.get("role") == role_code]
    
    # Rendezés
    if sort_by == "E-mail cím":
        filtered_users.sort(key=lambda u: u.get("email", "").lower())
    elif sort_by == "Név":
        filtered_users.sort(key=lambda u: u.get("contact_name", "").lower())
    elif sort_by == "Regisztráció dátuma":
        filtered_users.sort(key=lambda u: u.get("created_at", ""), reverse=True)
    
    # Felhasználók megjelenítése
    st.subheader(f"Felhasználók listája ({len(filtered_users)} találat)")
    
    # Táblázat adatok előkészítése
    user_data = []
    for u in filtered_users:
        user_data.append({
            "id": u.get("id"),
            "email": u.get("email"),
            "contact_name": u.get("contact_name", ""),
            "company_name": u.get("company_name", ""),
            "role": format_role(u.get("role", "")),
            "is_active": "Aktív" if u.get("is_active") else "Inaktív",
            "created_at": u.get("created_at", "")
        })
    
    # DataFrame létrehozása
    df = pd.DataFrame(user_data)
    
    # Táblázat megjelenítése
    st.dataframe(
        df,
        column_config={
            "id": "Azonosító",
            "email": "E-mail cím",
            "contact_name": "Kapcsolattartó neve",
            "company_name": "Cégnév",
            "role": "Szerepkör",
            "is_active": "Státusz",
            "created_at": "Regisztráció dátuma"
        },
        hide_index=True,
        use_container_width=True
    )
    
    # Felhasználó kezelés
    st.subheader("Felhasználó kezelése")
    
    # Felhasználó kiválasztása
    selected_user_id = st.selectbox(
        "Válasszon felhasználót",
        options=[f"{u.get('id')} - {u.get('email')} ({u.get('contact_name')})" for u in filtered_users],
        format_func=lambda x: x
    )
    
    if selected_user_id:
        # Felhasználó azonosító kinyerése
        user_id = int(selected_user_id.split(" - ")[0])
        
        # Kiválasztott felhasználó megkeresése
        selected_user = next((u for u in filtered_users if u.get("id") == user_id), None)
        
        if selected_user:
            # Felhasználó adatainak megjelenítése
            st.write("### Felhasználó adatai")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**Azonosító:** {selected_user.get('id')}")
                st.write(f"**E-mail cím:** {selected_user.get('email')}")
                st.write(f"**Kapcsolattartó neve:** {selected_user.get('contact_name', '')}")
                st.write(f"**Telefonszám:** {selected_user.get('phone_number', '')}")
            
            with col2:
                st.write(f"**Szerepkör:** {format_role(selected_user.get('role', ''))}")
                st.write(f"**Státusz:** {'Aktív' if selected_user.get('is_active') else 'Inaktív'}")
                st.write(f"**Cégnév:** {selected_user.get('company_name', '-')}")
                st.write(f"**Adószám:** {selected_user.get('tax_id', '-')}")
            
            st.write(f"**Regisztráció dátuma:** {selected_user.get('created_at', '')}")
            st.write(f"**Utolsó módosítás:** {selected_user.get('updated_at', '')}")
            
            # Felhasználó műveletek
            st.write("### Műveletek")
            
            # Két hasábos elrendezés a műveletekhez
            action_col1, action_col2 = st.columns(2)
            
            with action_col1:
                # Szerepkör módosítása
                new_role = st.selectbox(
                    "Szerepkör módosítása",
                    options=list(config.USER_ROLES.values()),
                    index=list(config.USER_ROLES.values()).index(format_role(selected_user.get("role", "")))
                )
                
                # Szerepkör kód visszakeresése
                new_role_code = None
                for code, name in config.USER_ROLES.items():
                    if name == new_role:
                        new_role_code = code
                        break
                
                if st.button("Szerepkör módosítása", use_container_width=True):
                    if new_role_code and new_role_code != selected_user.get("role"):
                        # Szerepkör módosítása API hívással
                        success, result = users_api.set_user_role(user_id, new_role_code)
                        
                        if success:
                            show_success(f"Felhasználó szerepköre sikeresen módosítva: {new_role}")
                            # Frissítjük az oldalt
                            st.rerun()
                        else:
                            show_error(f"Hiba a szerepkör módosításakor: {result}")
                    else:
                        show_info("A kiválasztott szerepkör megegyezik a jelenlegi szerepkörrel.")
            
            with action_col2:
                # Jelszó-visszaállítási e-mail küldése
                if st.button("Jelszó-visszaállítási e-mail küldése", use_container_width=True):
                    # API hívás a jelszó-visszaállítási e-mail küldéséhez
                    success, result = auth_api.request_password_reset(selected_user.get("email"))
                    
                    if success:
                        show_success(f"Jelszó-visszaállítási e-mail sikeresen elküldve a következő címre: {selected_user.get('email')}")
                    else:
                        show_error(f"Hiba a jelszó-visszaállítási e-mail küldésekor: {result}")
            
            # Felhasználó törlése/deaktiválása
            st.write("### Felhasználó törlése / deaktiválása")
            
            # Ellenőrizzük, hogy a felhasználó saját magát akarja-e törölni/deaktiválni
            current_user = get_current_user()
            is_self = current_user.get("id") == selected_user.get("id")
            
            # Két hasábos elrendezés a műveletekhez
            state_col1, state_col2 = st.columns(2)
            
            with state_col1:
                # Aktiválás/Deaktiválás gomb
                if selected_user.get('is_active'):
                    # Ha aktív állapotban van, akkor deaktiválást ajánlunk fel
                    deactivate_btn = st.button(
                        "Felhasználó deaktiválása", 
                        disabled=is_self,
                        help="A felhasználó bejelentkezésének letiltása" if not is_self else "Nem deaktiválhatod saját felhasználói fiókodat",
                        type="secondary",
                        use_container_width=True
                    )
                    
                    if deactivate_btn:
                        # Deaktiválás megerősítése
                        if st.checkbox("Biztosan deaktiválni szeretnéd ezt a felhasználót?"):
                            # API hívás a deaktiváláshoz
                            success, result = users_api.deactivate_user(selected_user.get("id"))
                            
                            if success:
                                show_success("Felhasználó sikeresen deaktiválva!")
                                # Frissítjük az oldalt
                                st.rerun()
                            else:
                                show_error(f"Hiba a felhasználó deaktiválásakor: {result}")
                else:
                    # Ha inaktív állapotban van, akkor aktiválást ajánlunk fel
                    activate_btn = st.button(
                        "Felhasználó aktiválása", 
                        help="A felhasználó bejelentkezésének engedélyezése",
                        type="secondary",
                        use_container_width=True
                    )
                    
                    if activate_btn:
                        # API hívás az aktiváláshoz
                        success, result = users_api.activate_user(selected_user.get("id"))
                        
                        if success:
                            show_success("Felhasználó sikeresen aktiválva!")
                            # Frissítjük az oldalt
                            st.rerun()
                        else:
                            show_error(f"Hiba a felhasználó aktiválásakor: {result}")
            
            with state_col2:
                # Törlés gomb
                delete_btn = st.button(
                    "Felhasználó törlése", 
                    disabled=is_self,
                    help="A felhasználó és összes kapcsolódó adatának törlése" if not is_self else "Nem törölheted saját felhasználói fiókodat",
                    type="primary",
                    use_container_width=True
                )
                
                if delete_btn:
                    # Törlés megerősítése - dupla megerősítés
                    st.warning("Figyelem! A törlés művelete nem visszavonható. A felhasználóhoz tartozó összes adat véglegesen törlődik.")
                    
                    confirmation = st.checkbox("Megértettem, és biztosan törölni szeretném ezt a felhasználót.")
                    
                    if confirmation:
                        # Megerősítés után további megerősítés kérése
                        if st.button("Megerősítem a törlést", type="primary"):
                            # API hívás a törléshez
                            success, result = users_api.delete_user(selected_user.get("id"))
                            
                            if success:
                                show_success("Felhasználó sikeresen törölve!")
                                # Frissítjük az oldalt
                                st.rerun()
                            else:
                                show_error(f"Hiba a felhasználó törlésekor: {result}")
    
    # Új felhasználó létrehozása
    st.subheader("Új felhasználó létrehozása")
    
    with st.expander("Új felhasználó regisztrálása"):
        # Regisztrációs űrlap
        with st.form("new_user_form"):
            # Személyes adatok
            st.subheader("Személyes adatok")
            contact_name = st.text_input("Kapcsolattartó neve *", placeholder="Teljes név")
            email = st.text_input("E-mail cím *", placeholder="<EMAIL>")
            phone_number = st.text_input("Telefonszám *", placeholder="+36 12 345 6789")
            
            # Cégadatok
            st.subheader("Cégadatok")
            is_company = st.checkbox("Cég nevében regisztrál")
            company_name = st.text_input("Cégnév", placeholder="Cégnév", disabled=not is_company)
            tax_id = st.text_input("Adószám", placeholder="12345678-1-23", disabled=not is_company)
            
            # Szerepkör és jelszó
            st.subheader("Szerepkör és jelszó")
            role = st.selectbox("Szerepkör *", options=list(config.USER_ROLES.values()))
            
            password = st.text_input("Jelszó *", type="password", 
                                     help="A jelszónak legalább 8 karakter hosszúnak kell lennie, tartalmaznia kell betűt és számot is.")
            
            # Mentés gomb
            submit = st.form_submit_button("Felhasználó létrehozása", type="primary", use_container_width=True)
        
        if submit:
            # Adatok validálása
            validation_errors = []
            
            # Kötelező mezők ellenőrzése
            if not contact_name:
                validation_errors.append("Kapcsolattartó neve kötelező mező.")
            
            if not email:
                validation_errors.append("E-mail cím kötelező mező.")
            elif not validate_email(email):
                validation_errors.append("Érvénytelen e-mail cím formátum.")
            
            if not phone_number:
                validation_errors.append("Telefonszám kötelező mező.")
            
            if not password:
                validation_errors.append("Jelszó kötelező mező.")
            
            # Céges adatok ellenőrzése, ha céges
            if is_company:
                if not company_name:
                    validation_errors.append("Cégnév megadása kötelező céges regisztráció esetén.")
                if not tax_id:
                    validation_errors.append("Adószám megadása kötelező céges regisztráció esetén.")
            
            # Ha van validációs hiba, megjelenítjük
            if validation_errors:
                for error in validation_errors:
                    show_error(error)
            else:
                # Szerepkör kód visszakeresése
                role_code = None
                for code, name in config.USER_ROLES.items():
                    if name == role:
                        role_code = code
                        break
                
                # Regisztrációs adatok összeállítása
                user_data = {
                    "email": email,
                    "password": password,
                    "contact_name": contact_name,
                    "phone_number": phone_number,
                    "role": role_code
                }
                
                if is_company:
                    user_data["company_name"] = company_name
                    user_data["tax_id"] = tax_id
                
                # Regisztráció API hívás
                success, result = auth_api.register(user_data)
                
                if success:
                    show_success("Felhasználó sikeresen létrehozva!")
                    st.info("Az új felhasználó számára e-mail-ben elküldtük a megerősítő linket.")
                    # Frissítjük az oldalt
                    st.rerun()
                else:
                    show_error(f"Hiba a felhasználó létrehozásakor: {result}")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Felhasználók kezelése - {config.APP_NAME}",
        page_icon="👥",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a felhasználókezelő oldalt
    show_admin_users()
