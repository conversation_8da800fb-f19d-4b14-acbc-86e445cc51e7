import streamlit as st
from utils.session import is_authenticated, get_current_user
from components.notification import show_error
from components.sidebar import render_sidebar
import app_config as config

def show_admin_sitemap():
    """
    Oldaltérkép megjelenítése és navigációs teszt felület adminisztrátorok számára.
    """
    st.title("Oldaltérkép és Navigációs Teszt")
    
    # Ellenőrizzük, hogy a felhasz<PERSON>ló be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Ellenőrizzük, hogy admin
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return

    st.markdown("""
    ### Oldaltérkép és Navigációs Teszt

    Ez az oldal lehetővé teszi az összes oldal elérhetőségének tesztelését. Az oldalak két formátumban érhetők el:
    - `pages/oldal_neve.py` (eredeti hivatkozás)
    - `pages/oldal_neve.py` (lapos struktúra)
    
    Mindkét formátumot tesztelheti az alábbi listából.
    """)

    # Oldalak listája szerepkörök szerint
    pages = {
        "Autentikáció": {
            "Bejelentkezés": ["pages/auth_login.py", "pages/auth_login.py"],
            "Regisztráció": ["pages/auth_register.py", "pages/auth_register.py"],
            "Jelszó visszaállítás": ["pages/auth_reset_password.py", "pages/auth_reset_password.py"]
        },
        "Admin": {
            "Irányítópult": ["pages/admin_dashboard.py", "pages/admin_dashboard.py"],
            "Felhasználók": ["pages/admin_users.py", "pages/admin_users.py"],
            "Felhasználók kezelése": ["pages/admin_user_management.py", "pages/admin_user_management.py"],
            "Termékek": ["pages/admin_products.py", "pages/admin_products.py"],
            "Termékek kezelése": ["pages/admin_product_management.py", "pages/admin_product_management.py"]
        },
        "Ügyintéző": {
            "Irányítópult": ["pages/operator_dashboard.py", "pages/operator_dashboard.py"],
            "Ajánlatok": ["pages/operator_offers.py", "pages/operator_offers.py"],
            "Ajánlatok kezelése": ["pages/operator_offer_management.py", "pages/operator_offer_management.py"],
            "Új ajánlat": ["pages/operator_create_offer.py", "pages/operator_create_offer.py"],
            "Naptár": ["pages/operator_calendar.py", "pages/operator_calendar.py"],
            "Naptári nézet": ["pages/operator_calendar_view.py", "pages/operator_calendar_view.py"],
            "Riportok": ["pages/operator_reports.py", "pages/operator_reports.py"]
        },
        "Termelő": {
            "Irányítópult": ["pages/producer_dashboard.py", "pages/producer_dashboard.py"],
            "Ajánlatok": ["pages/producer_offers.py", "pages/producer_offers.py"],
            "Új ajánlat": ["pages/producer_create_offer.py", "pages/producer_create_offer.py"],
            "Profil": ["pages/producer_profile.py", "pages/producer_profile.py"],
            "Statisztikák": ["pages/producer_statistics.py", "pages/producer_statistics.py"]
        }
    }

    # Szerepkörönként megjelenítjük az oldalakat
    for role, role_pages in pages.items():
        st.header(role)
        
        # Két oszlopos elrendezés
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Eredeti hivatkozások")
            for page_name, page_paths in role_pages.items():
                if st.button(f"{page_name} (eredeti)", key=f"orig_{role}_{page_name}"):
                    st.switch_page(page_paths[0])
        
        with col2:
            st.subheader("Lapos struktúra")
            for page_name, page_paths in role_pages.items():
                if st.button(f"{page_name} (lapos)", key=f"flat_{role}_{page_name}"):
                    st.switch_page(page_paths[1])

    st.markdown("""
    ### Megjegyzések
    - A sikeres navigáció után használja a böngésző "Vissza" gombját vagy az oldalsáv menüt a visszatéréshez
    - Ha egy oldal nem elérhető, hibaüzenet jelenik meg
    - Az oldalsáv menü is tesztelhető a bal oldalon
    """)

if __name__ == "__main__":
    st.set_page_config(
        page_title=f"Oldaltérkép - {config.APP_NAME}",
        page_icon="🗺️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    render_sidebar()
    show_admin_sitemap() 