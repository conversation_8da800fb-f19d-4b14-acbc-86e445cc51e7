# Termékek kezelése
"""
Termékek részletes kezelése admin számára.
"""
import streamlit as st
import pandas as pd
from api import products as products_api
from utils.session import is_authenticated, get_current_user
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.formatting import format_datetime
import app_config as config

def show_product_management():
    """
    Termékek részletes kezelése admin számára.
    """
    st.title("Termékek Kezelése")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # <PERSON><PERSON><PERSON>zü<PERSON>, hogy admin-e
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Tabs a három fő termék entitásnak
    tab1, tab2, tab3 = st.tabs(["Termékkategóriák", "Terméktípusok", "Minőségi besorolások"])
    
    with tab1:
        show_category_management()
    
    with tab2:
        show_product_type_management()
        
    with tab3:
        show_quality_grade_management()

def show_category_management():
    """
    Termékkategóriák kezelése.
    """
    st.subheader("Termékkategóriák kezelése")
    
    # Kategóriák lekérdezése
    success, result = products_api.get_product_categories()
    
    # Kategóriák listázása
    if success:
        categories = result
        
        # Két oszlop: bal oldalt lista, jobb oldalt műveletek
        col1, col2 = st.columns([3, 2])
        
        with col1:
            # Kategóriák táblázata
            if categories:
                category_data = []
                for cat in categories:
                    category_data.append({
                        "ID": cat.get("id"),
                        "Név": cat.get("name", ""),
                        "Leírás": cat.get("description", ""),
                        "Létrehozva": format_datetime(cat.get("created_at", "")),
                        "Frissítve": format_datetime(cat.get("updated_at", ""))
                    })
                
                df = pd.DataFrame(category_data)
                
                st.dataframe(
                    df,
                    use_container_width=True,
                    column_config={
                        "ID": st.column_config.NumberColumn("ID", format="%d"),
                        "Név": st.column_config.TextColumn("Név"),
                        "Leírás": st.column_config.TextColumn("Leírás"),
                        "Létrehozva": st.column_config.TextColumn("Létrehozva"),
                        "Frissítve": st.column_config.TextColumn("Frissítve")
                    },
                    hide_index=True
                )
                
                # Kategória kiválasztása
                selected_category = st.selectbox(
                    "Kategória szerkesztése",
                    options=[cat.get("id") for cat in categories],
                    format_func=lambda x: next((cat.get("name", "") for cat in categories if cat.get("id") == x), "")
                )
            else:
                st.info("Nincs regisztrált termékkategória.")
                selected_category = None
        
        with col2:
            # Műveletek
            st.subheader("Kategória műveletek")
            
            # Új kategória létrehozása
            with st.form(key="new_category_form"):
                st.subheader("Új kategória létrehozása")
                
                new_cat_name = st.text_input("Kategória név", key="new_cat_name")
                new_cat_desc = st.text_area("Leírás", key="new_cat_desc")
                
                submit_button = st.form_submit_button("Létrehozás", type="primary")
                
                if submit_button:
                    if not new_cat_name:
                        show_error("A kategória név megadása kötelező.")
                    else:
                        success, result = products_api.create_product_category({
                            "name": new_cat_name,
                            "description": new_cat_desc
                        })
                        
                        if success:
                            show_success("Kategória sikeresen létrehozva!")
                            st.rerun()
                        else:
                            show_error(f"Hiba a kategória létrehozásakor: {result}")
            
            # Kategória szerkesztése/törlése (ha van kiválasztva)
            if selected_category:
                # Kiválasztott kategória adatai
                selected_cat_data = next((cat for cat in categories if cat.get("id") == selected_category), None)
                
                if selected_cat_data:
                    with st.form(key="edit_category_form"):
                        st.subheader("Kategória szerkesztése")
                        
                        edit_cat_name = st.text_input("Kategória név", value=selected_cat_data.get("name", ""))
                        edit_cat_desc = st.text_area("Leírás", value=selected_cat_data.get("description", ""))
                        
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            update_button = st.form_submit_button("Frissítés", type="primary")
                        
                        with col2:
                            delete_button = st.form_submit_button("Törlés", type="secondary")
                        
                        if update_button:
                            if not edit_cat_name:
                                show_error("A kategória név megadása kötelező.")
                            else:
                                success, result = products_api.update_product_category(
                                    selected_category,
                                    {
                                        "name": edit_cat_name,
                                        "description": edit_cat_desc
                                    }
                                )
                                
                                if success:
                                    show_success("Kategória sikeresen frissítve!")
                                    st.rerun()
                                else:
                                    show_error(f"Hiba a kategória frissítésekor: {result}")
                        
                        if delete_button:
                            success, result = products_api.delete_product_category(selected_category)
                            
                            if success:
                                show_success("Kategória sikeresen törölve!")
                                st.rerun()
                            else:
                                show_error(f"Hiba a kategória törlésekor: {result}")
    else:
        show_error(f"Hiba a termékkategóriák lekérésekor: {result}")

def show_product_type_management():
    """
    Terméktípusok kezelése.
    """
    st.subheader("Terméktípusok kezelése")
    
    # Kategóriák lekérdezése a szűréshez
    cat_success, cat_result = products_api.get_product_categories()
    
    if cat_success:
        categories = cat_result
        
        # Kategória szűrés
        selected_category_id = st.selectbox(
            "Kategória szűrés",
            options=[None] + [cat.get("id") for cat in categories],
            format_func=lambda x: "Összes kategória" if x is None else next((cat.get("name") for cat in categories if cat.get("id") == x), "")
        )
        
        # Terméktípusok lekérdezése
        success, result = products_api.get_product_types(category_id=selected_category_id)
        
        if success:
            product_types = result
            
            # Két oszlop: bal oldalt lista, jobb oldalt műveletek
            col1, col2 = st.columns([3, 2])
            
            with col1:
                # Terméktípusok táblázata
                if product_types:
                    type_data = []
                    for pt in product_types:
                        type_data.append({
                            "ID": pt.get("id"),
                            "Név": pt.get("name", ""),
                            "Kategória": pt.get("category", {}).get("name", ""),
                            "Leírás": pt.get("description", ""),
                            "Minőségi besorolások": "Igen" if pt.get("has_quality_grades") else "Nem",
                            "Létrehozva": format_datetime(pt.get("created_at", ""))
                        })
                    
                    df = pd.DataFrame(type_data)
                    
                    st.dataframe(
                        df,
                        use_container_width=True,
                        column_config={
                            "ID": st.column_config.NumberColumn("ID", format="%d"),
                            "Név": st.column_config.TextColumn("Név"),
                            "Kategória": st.column_config.TextColumn("Kategória"),
                            "Leírás": st.column_config.TextColumn("Leírás"),
                            "Minőségi besorolások": st.column_config.TextColumn("Minőségi besorolások"),
                            "Létrehozva": st.column_config.TextColumn("Létrehozva")
                        },
                        hide_index=True
                    )
                    
                    # Terméktípus kiválasztása
                    selected_type = st.selectbox(
                        "Terméktípus szerkesztése",
                        options=[pt.get("id") for pt in product_types],
                        format_func=lambda x: next((f"{pt.get('name')} ({pt.get('category', {}).get('name', '')})" for pt in product_types if pt.get("id") == x), "")
                    )
                else:
                    st.info("Nincs regisztrált terméktípus" + (" ehhez a kategóriához." if selected_category_id else "."))
                    selected_type = None
            
            with col2:
                # Műveletek
                st.subheader("Terméktípus műveletek")
                
                # Új terméktípus létrehozása
                with st.form(key="new_type_form"):
                    st.subheader("Új terméktípus létrehozása")
                    
                    new_type_category = st.selectbox(
                        "Kategória",
                        options=[cat.get("id") for cat in categories],
                        format_func=lambda x: next((cat.get("name") for cat in categories if cat.get("id") == x), "")
                    )
                    
                    new_type_name = st.text_input("Terméktípus név")
                    new_type_desc = st.text_area("Leírás")
                    new_type_has_grades = st.checkbox("Van minőségi besorolása", value=True)
                    
                    submit_button = st.form_submit_button("Létrehozás", type="primary")
                    
                    if submit_button:
                        if not new_type_name:
                            show_error("A terméktípus név megadása kötelező.")
                        else:
                            success, result = products_api.create_product_type({
                                "category_id": new_type_category,
                                "name": new_type_name,
                                "description": new_type_desc,
                                "has_quality_grades": new_type_has_grades
                            })
                            
                            if success:
                                show_success("Terméktípus sikeresen létrehozva!")
                                st.rerun()
                            else:
                                show_error(f"Hiba a terméktípus létrehozásakor: {result}")
                
                # Terméktípus szerkesztése/törlése (ha van kiválasztva)
                if selected_type:
                    # Kiválasztott terméktípus adatai
                    selected_type_data = next((pt for pt in product_types if pt.get("id") == selected_type), None)
                    
                    if selected_type_data:
                        with st.form(key="edit_type_form"):
                            st.subheader("Terméktípus szerkesztése")
                            
                            edit_type_category = st.selectbox(
                                "Kategória",
                                options=[cat.get("id") for cat in categories],
                                format_func=lambda x: next((cat.get("name") for cat in categories if cat.get("id") == x), ""),
                                index=[i for i, cat in enumerate(categories) if cat.get("id") == selected_type_data.get("category", {}).get("id")][0] if selected_type_data.get("category", {}).get("id") in [cat.get("id") for cat in categories] else 0
                            )
                            
                            edit_type_name = st.text_input("Terméktípus név", value=selected_type_data.get("name", ""))
                            edit_type_desc = st.text_area("Leírás", value=selected_type_data.get("description", ""))
                            edit_type_has_grades = st.checkbox("Van minőségi besorolása", value=selected_type_data.get("has_quality_grades", True))
                            
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                update_button = st.form_submit_button("Frissítés", type="primary")
                            
                            with col2:
                                delete_button = st.form_submit_button("Törlés", type="secondary")
                            
                            if update_button:
                                if not edit_type_name:
                                    show_error("A terméktípus név megadása kötelező.")
                                else:
                                    success, result = products_api.update_product_type(
                                        selected_type,
                                        {
                                            "category_id": edit_type_category,
                                            "name": edit_type_name,
                                            "description": edit_type_desc,
                                            "has_quality_grades": edit_type_has_grades
                                        }
                                    )
                                    
                                    if success:
                                        show_success("Terméktípus sikeresen frissítve!")
                                        st.rerun()
                                    else:
                                        show_error(f"Hiba a terméktípus frissítésekor: {result}")
                            
                            if delete_button:
                                success, result = products_api.delete_product_type(selected_type)
                                
                                if success:
                                    show_success("Terméktípus sikeresen törölve!")
                                    st.rerun()
                                else:
                                    show_error(f"Hiba a terméktípus törlésekor: {result}")
        else:
            show_error(f"Hiba a terméktípusok lekérésekor: {result}")
    else:
        show_error(f"Hiba a termékkategóriák lekérésekor: {cat_result}")

def show_quality_grade_management():
    """
    Minőségi besorolások kezelése.
    """
    st.subheader("Minőségi besorolások kezelése")
    
    # Terméktípusok lekérdezése a szűréshez
    type_success, type_result = products_api.get_product_types()
    
    if type_success:
        product_types = [pt for pt in type_result if pt.get("has_quality_grades")]
        
        if not product_types:
            st.info("Nincs olyan terméktípus, amelyhez minőségi besorolás tartozik.")
            return
        
        # Terméktípus szűrés
        selected_type_id = st.selectbox(
            "Terméktípus",
            options=[pt.get("id") for pt in product_types],
            format_func=lambda x: next((f"{pt.get('name')} ({pt.get('category', {}).get('name', '')})" for pt in product_types if pt.get("id") == x), "")
        )
        
        # Minőségi besorolások lekérdezése
        success, result = products_api.get_quality_grades(selected_type_id)
        
        if success:
            quality_grades = result
            
            # Két oszlop: bal oldalt lista, jobb oldalt műveletek
            col1, col2 = st.columns([3, 2])
            
            with col1:
                # Minőségi besorolások táblázata
                if quality_grades:
                    grade_data = []
                    for qg in quality_grades:
                        grade_data.append({
                            "ID": qg.get("id"),
                            "Név": qg.get("name", ""),
                            "Min. vállátmérő": qg.get("min_shoulder_diameter"),
                            "Max. vállátmérő": qg.get("max_shoulder_diameter"),
                            "Min. hossz": qg.get("min_length"),
                            "Max. hossz": qg.get("max_length"),
                            "Leírás": qg.get("description", "")
                        })
                    
                    df = pd.DataFrame(grade_data)
                    
                    st.dataframe(
                        df,
                        use_container_width=True,
                        column_config={
                            "ID": st.column_config.NumberColumn("ID", format="%d"),
                            "Név": st.column_config.TextColumn("Név"),
                            "Min. vállátmérő": st.column_config.NumberColumn("Min. váll (mm)", format="%.1f"),
                            "Max. vállátmérő": st.column_config.NumberColumn("Max. váll (mm)", format="%.1f"),
                            "Min. hossz": st.column_config.NumberColumn("Min. hossz (mm)", format="%.1f"),
                            "Max. hossz": st.column_config.NumberColumn("Max. hossz (mm)", format="%.1f"),
                            "Leírás": st.column_config.TextColumn("Leírás")
                        },
                        hide_index=True
                    )
                    
                    # Minőségi besorolás kiválasztása
                    selected_grade = st.selectbox(
                        "Minőségi besorolás szerkesztése",
                        options=[qg.get("id") for qg in quality_grades],
                        format_func=lambda x: next((qg.get("name", "") for qg in quality_grades if qg.get("id") == x), "")
                    )
                else:
                    st.info("Nincs regisztrált minőségi besorolás ehhez a terméktípushoz.")
                    selected_grade = None
            
            with col2:
                # Műveletek
                st.subheader("Minőségi besorolás műveletek")
                
                # Új minőségi besorolás létrehozása
                with st.form(key="new_grade_form"):
                    st.subheader("Új minőségi besorolás létrehozása")
                    
                    new_grade_name = st.text_input("Besorolás név")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        new_grade_min_shoulder = st.number_input("Min. vállátmérő (mm)", min_value=0.0, step=0.1)
                        new_grade_min_length = st.number_input("Min. hossz (mm)", min_value=0.0, step=0.1)
                    
                    with col2:
                        new_grade_max_shoulder = st.number_input("Max. vállátmérő (mm)", min_value=0.0, step=0.1)
                        new_grade_max_length = st.number_input("Max. hossz (mm)", min_value=0.0, step=0.1)
                    
                    new_grade_desc = st.text_area("Leírás")
                    
                    submit_button = st.form_submit_button("Létrehozás", type="primary")
                    
                    if submit_button:
                        if not new_grade_name:
                            show_error("A besorolás név megadása kötelező.")
                        else:
                            success, result = products_api.create_quality_grade({
                                "product_type_id": selected_type_id,
                                "name": new_grade_name,
                                "min_shoulder_diameter": new_grade_min_shoulder,
                                "max_shoulder_diameter": new_grade_max_shoulder,
                                "min_length": new_grade_min_length,
                                "max_length": new_grade_max_length,
                                "description": new_grade_desc
                            })
                            
                            if success:
                                show_success("Minőségi besorolás sikeresen létrehozva!")
                                st.rerun()
                            else:
                                show_error(f"Hiba a minőségi besorolás létrehozásakor: {result}")
                
                # Minőségi besorolás szerkesztése/törlése (ha van kiválasztva)
                if selected_grade:
                    # Kiválasztott minőségi besorolás adatai
                    selected_grade_data = next((qg for qg in quality_grades if qg.get("id") == selected_grade), None)
                    
                    if selected_grade_data:
                        with st.form(key="edit_grade_form"):
                            st.subheader("Minőségi besorolás szerkesztése")
                            
                            edit_grade_name = st.text_input("Besorolás név", value=selected_grade_data.get("name", ""))
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                edit_grade_min_shoulder = st.number_input(
                                    "Min. vállátmérő (mm)",
                                    min_value=0.0,
                                    step=0.1,
                                    value=float(selected_grade_data.get("min_shoulder_diameter", 0))
                                )
                                edit_grade_min_length = st.number_input(
                                    "Min. hossz (mm)",
                                    min_value=0.0,
                                    step=0.1,
                                    value=float(selected_grade_data.get("min_length", 0))
                                )
                            
                            with col2:
                                edit_grade_max_shoulder = st.number_input(
                                    "Max. vállátmérő (mm)",
                                    min_value=0.0,
                                    step=0.1,
                                    value=float(selected_grade_data.get("max_shoulder_diameter", 0))
                                )
                                edit_grade_max_length = st.number_input(
                                    "Max. hossz (mm)",
                                    min_value=0.0,
                                    step=0.1,
                                    value=float(selected_grade_data.get("max_length", 0))
                                )
                            
                            edit_grade_desc = st.text_area("Leírás", value=selected_grade_data.get("description", ""))
                            
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                update_button = st.form_submit_button("Frissítés", type="primary")
                            
                            with col2:
                                delete_button = st.form_submit_button("Törlés", type="secondary")
                            
                            if update_button:
                                if not edit_grade_name:
                                    show_error("A besorolás név megadása kötelező.")
                                else:
                                    success, result = products_api.update_quality_grade(
                                        selected_grade,
                                        {
                                            "product_type_id": selected_type_id,
                                            "name": edit_grade_name,
                                            "min_shoulder_diameter": edit_grade_min_shoulder,
                                            "max_shoulder_diameter": edit_grade_max_shoulder,
                                            "min_length": edit_grade_min_length,
                                            "max_length": edit_grade_max_length,
                                            "description": edit_grade_desc
                                        }
                                    )
                                    
                                    if success:
                                        show_success("Minőségi besorolás sikeresen frissítve!")
                                        st.rerun()
                                    else:
                                        show_error(f"Hiba a minőségi besorolás frissítésekor: {result}")
                            
                            if delete_button:
                                success, result = products_api.delete_quality_grade(selected_grade)
                                
                                if success:
                                    show_success("Minőségi besorolás sikeresen törölve!")
                                    st.rerun()
                                else:
                                    show_error(f"Hiba a minőségi besorolás törlésekor: {result}")
        else:
            show_error(f"Hiba a minőségi besorolások lekérésekor: {result}")
    else:
        show_error(f"Hiba a terméktípusok lekérésekor: {type_result}")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Termékek kezelése - {config.APP_NAME}",
        page_icon="🍅",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a termékkezelést
    show_product_management()
