# streamlit_app/pages/admin/data_generator.py

import streamlit as st
import pandas as pd
import numpy as np
import requests
import json
import random
from datetime import datetime, timedelta
import time
import uuid
import re
import os
from faker import Faker
import calendar
import matplotlib.pyplot as plt
import seaborn as sns
import io
import plotly.express as px
import plotly.graph_objects as go
from collections import defaultdict

# Relatív importok használata a megfelelő elérési utakkal
from app_config import API_BASE_URL, API_CLIENT_CONFIG
from utils.api_client import api_request
from utils.session import get_auth_token, is_authenticated, get_current_user
from utils.api_client import api_request
from components.notification import show_error, show_success, show_info
from components.sidebar import render_sidebar
from api import users as users_api, offers as offers_api, products as products_api
import app_config as config

# Faker inicializálása magyar lokalizációval
faker = Faker('hu_HU')
Faker.seed(42)  # Reprodukálható adatok

# Magyar mezőgazdasági cégnevek generálásához
MAGYAR_MEZOGAZDASAGI_CEGNEVEK_ELOTAG = [
    "Arany Kalász", "Zöld Mező", "Termő Föld", "Napfény", "Búzavirág", "Termés", "Földműves", 
    "Gyümölcskert", "Vetőmag", "Szorgos Gazda", "Bőség", "Határmenti", "Kalászos", "Aratás", 
    "Jó Termés", "Éltető Föld", "Hazai Termés", "Magyar Föld", "Paprikakert", "Almavirág",
    "Gazdakör", "Szántóföld", "Sárga Alma", "Zöldségfarm", "Napsugár", "Gabona", "Aranymező",
    "Jó Gazda", "Alma-Körte", "Zöldellő", "Friss Termés", "Termékeny Rög", "Piros Paprika"
]

MAGYAR_MEZOGAZDASAGI_CEGNEVEK_UTOTAG = [
    "Kft.", "Bt.", "Zrt.", "Kkt.", "Nyrt.", "Kistermelő", "Farm", "Gazdaság", "Szövetkezet", 
    "Egyéni Vállalkozás", "Családi Gazdaság", "és Társa", "és Fiai", "Agrár Kft.", "Termelői Csoport",
    "Mg. Szövetkezet", "Gazdálkodó", "Mezőgazdasági Kft.", "Birtok", "Tanya", "Kert"
]

# Jellemző magyar mezőgazdasági termékek és áraik
MEZOGAZDASAGI_TERMEKEK = {
    "Gabonafélék": {
        "Őszi búza": {"min_price": 65000, "max_price": 85000, "unit": "tonna", "szezon": [6, 7, 8]},
        "Kukorica": {"min_price": 55000, "max_price": 75000, "unit": "tonna", "szezon": [9, 10, 11]},
        "Árpa": {"min_price": 60000, "max_price": 75000, "unit": "tonna", "szezon": [6, 7]},
        "Zab": {"min_price": 60000, "max_price": 80000, "unit": "tonna", "szezon": [7, 8]},
        "Rozs": {"min_price": 55000, "max_price": 70000, "unit": "tonna", "szezon": [7, 8]}
    },
    "Zöldségfélék": {
        "Paradicsom": {"min_price": 300, "max_price": 800, "unit": "kg", "szezon": [6, 7, 8, 9]},
        "Paprika": {"min_price": 400, "max_price": 1200, "unit": "kg", "szezon": [6, 7, 8, 9, 10]},
        "Burgonya": {"min_price": 150, "max_price": 300, "unit": "kg", "szezon": [7, 8, 9, 10]},
        "Hagyma": {"min_price": 200, "max_price": 400, "unit": "kg", "szezon": [7, 8, 9, 10]},
        "Káposzta": {"min_price": 150, "max_price": 350, "unit": "kg", "szezon": [9, 10, 11]}
    },
    "Gyümölcsök": {
        "Alma": {"min_price": 150, "max_price": 400, "unit": "kg", "szezon": [8, 9, 10, 11]},
        "Körte": {"min_price": 300, "max_price": 700, "unit": "kg", "szezon": [8, 9, 10]},
        "Szilva": {"min_price": 250, "max_price": 600, "unit": "kg", "szezon": [8, 9]},
        "Meggy": {"min_price": 400, "max_price": 1000, "unit": "kg", "szezon": [6, 7]},
        "Szőlő": {"min_price": 300, "max_price": 800, "unit": "kg", "szezon": [8, 9, 10]}
    }
}

# Jellemző minőségi osztályok terméktípusonként
MINOSEGI_OSZTALYOK = {
    "Gabonafélék": {
        "Őszi búza": ["Étkezési", "Takarmány", "Euró minőség", "Malmi"],
        "Kukorica": ["Takarmány", "Étkezési", "Csemege"],
        "Árpa": ["Sörárpa", "Takarmány"],
        "Zab": ["Takarmány", "Étkezési"],
        "Rozs": ["Takarmány", "Malmi"]
    },
    "Zöldségfélék": {
        "Paradicsom": ["Extra", "I. osztály", "II. osztály", "Befőző"],
        "Paprika": ["Extra", "I. osztály", "II. osztály", "Ipari"],
        "Burgonya": ["A minőség", "B minőség", "Ipari"],
        "Hagyma": ["I. osztály", "II. osztály"],
        "Káposzta": ["I. osztály", "II. osztály"]
    },
    "Gyümölcsök": {
        "Alma": ["Extra", "I. osztály", "II. osztály", "Ipari"],
        "Körte": ["Extra", "I. osztály", "II. osztály"],
        "Szilva": ["Extra", "I. osztály", "II. osztály", "Befőző"],
        "Meggy": ["Étkezési", "Befőző", "Ipari"],
        "Szőlő": ["Csemege", "Borszőlő"]
    }
}

# Magyar régiók és megyék
MAGYAR_REGIOK = {
    "Észak-Magyarország": ["Borsod-Abaúj-Zemplén", "Heves", "Nógrád"],
    "Észak-Alföld": ["Hajdú-Bihar", "Jász-Nagykun-Szolnok", "Szabolcs-Szatmár-Bereg"],
    "Dél-Alföld": ["Bács-Kiskun", "Békés", "Csongrád-Csanád"],
    "Közép-Magyarország": ["Budapest", "Pest"],
    "Közép-Dunántúl": ["Komárom-Esztergom", "Fejér", "Veszprém"],
    "Nyugat-Dunántúl": ["Győr-Moson-Sopron", "Vas", "Zala"],
    "Dél-Dunántúl": ["Baranya", "Somogy", "Tolna"]
}

# Mezőgazdasági vállalkozások jellemző méretei
VALLALKOZAS_MERETEK = {
    "Kistermelő": {"alkalmazottak": (1, 5), "földterület": (1, 50)},
    "Középvállalkozás": {"alkalmazottak": (6, 50), "földterület": (51, 500)},
    "Nagyvállalat": {"alkalmazottak": (51, 200), "földterület": (501, 5000)}
}

# Segédfüggvények realisztikus adatok generálásához
def generate_magyar_cegnev():
    """Magyar mezőgazdasági cég nevének generálása"""
    elotag = random.choice(MAGYAR_MEZOGAZDASAGI_CEGNEVEK_ELOTAG)
    utotag = random.choice(MAGYAR_MEZOGAZDASAGI_CEGNEVEK_UTOTAG)
    return f"{elotag} {utotag}"

def generate_magyar_telefonszam():
    """Magyar telefonszám generálása"""
    prefixes = ['06-30', '06-20', '06-70', '+36-30', '+36-20', '+36-70']
    prefix = random.choice(prefixes)
    return f"{prefix}-{random.randint(100, 999)}-{random.randint(1000, 9999)}"

def get_szezonalis_termekek(honap):
    """Visszaadja az adott hónapban szezonális termékeket"""
    szezonalis = {}
    for kategoria, termekek in MEZOGAZDASAGI_TERMEKEK.items():
        kat_termekek = {}
        for termek, adatok in termekek.items():
            if honap in adatok["szezon"]:
                kat_termekek[termek] = adatok
        if kat_termekek:
            szezonalis[kategoria] = kat_termekek
    return szezonalis

def generate_realis_ajanlat(termek_nev, kategoria, min_date, max_date):
    """Generál egy realisztikus ajánlatot a megadott terméktípushoz"""
    termek_adatok = MEZOGAZDASAGI_TERMEKEK[kategoria][termek_nev]
    
    # Véletlen dátum generálása a szezonon belül
    szezon_honapok = termek_adatok["szezon"]
    if not szezon_honapok:
        delivery_date = faker.date_between(min_date, max_date)
    else:
        # Szezonhoz legközelebbi időpont kiválasztása
        now = datetime.now()
        current_year = now.year
        possible_dates = []
        
        # Idei és jövő évi szezonok figyelembevétele
        for ev in [current_year, current_year + 1]:
            for honap in szezon_honapok:
                # Az adott hónap utolsó napja
                utolso_nap = calendar.monthrange(ev, honap)[1]
                kezdo_datum = datetime(ev, honap, 1).date()
                veg_datum = datetime(ev, honap, utolso_nap).date()
                
                if min_date <= veg_datum and kezdo_datum <= max_date:
                    actual_start = max(min_date, kezdo_datum)
                    actual_end = min(max_date, veg_datum)
                    possible_dates.append((actual_start, actual_end))
        
        if possible_dates:
            start_date, end_date = random.choice(possible_dates)
            delivery_date = faker.date_between(start_date, end_date)
        else:
            delivery_date = faker.date_between(min_date, max_date)
    
    # Egység alapján reális mennyiség generálása
    if termek_adatok["unit"] == "tonna":
        min_qty = 1  # minimum 1 tonna
        max_qty = 25  # maximum 25 tonna
        quantity = round(random.uniform(min_qty, max_qty), 2)
        quantity_in_kg = quantity * 1000  # konvertálás kg-ra
    else:  # kg
        min_qty = 50  # minimum 50 kg
        max_qty = 2000  # maximum 2000 kg
        quantity_in_kg = round(random.uniform(min_qty, max_qty), 2)
    
    # Reális ár generálása
    price = round(random.uniform(termek_adatok["min_price"], termek_adatok["max_price"]), 0)
    
    return {
        "quantity_in_kg": quantity_in_kg,
        "delivery_date": delivery_date.isoformat(),
        "confirmed_price": price
    }

def generate_producer_profile():
    """Realisztikus termelői profil generálása"""
    # Válasszunk régiót és megyét
    regio = random.choice(list(MAGYAR_REGIOK.keys()))
    megye = random.choice(MAGYAR_REGIOK[regio])
    
    # Válasszunk vállalkozás méretet
    vallalkozas_tipus = random.choices(
        list(VALLALKOZAS_MERETEK.keys()), 
        weights=[0.7, 0.25, 0.05],  # 70% kistermelő, 25% középvállalkozás, 5% nagyvállalat
        k=1
    )[0]
    vallalkozas_adatok = VALLALKOZAS_MERETEK[vallalkozas_tipus]
    
    # Alkalmazottak száma és földterület
    alkalmazottak = random.randint(*vallalkozas_adatok["alkalmazottak"])
    foldterulet = random.randint(*vallalkozas_adatok["földterület"])
    
    # Specializáció - milyen termékekre koncentrál
    kategoria = random.choice(list(MEZOGAZDASAGI_TERMEKEK.keys()))
    specializacio = random.sample(list(MEZOGAZDASAGI_TERMEKEK[kategoria].keys()), k=min(3, len(MEZOGAZDASAGI_TERMEKEK[kategoria])))
    
    return {
        "company_name": generate_magyar_cegnev(),
        "phone_number": generate_magyar_telefonszam(),
        "region": regio,
        "county": megye,
        "company_size": vallalkozas_tipus,
        "employees": alkalmazottak,
        "land_area": foldterulet,
        "specialization_category": kategoria,
        "specialization_products": specializacio
    }

def show_data_generator():
    """
    Tesztadat generátor oldal megjelenítése.
    """
    # Oldal konfigurálása
    st.set_page_config(
        page_title="Tesztadat generátor - Termelo", 
        page_icon="🧪", 
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Sidebar megjelenítése
    render_sidebar()
    
    st.title("Tesztadat generátor")
    
    # Jogosultság ellenőrzése
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Admin jogosultság ellenőrzése
    user = get_current_user()
    if user.get("role") != "admin":
        show_error("Csak admin hozzáféréssel érhető el ez az oldal!")
        st.switch_page("pages/producer_dashboard.py")
        return
    
    # Session state inicializálása
    if "db_reset_success" not in st.session_state:
        st.session_state.db_reset_success = False
    if "db_reset_error" not in st.session_state:
        st.session_state.db_reset_error = ""
    
    # Sikeres vagy hibaüzenet megjelenítése, ha van
    if st.session_state.db_reset_success:
        show_success("Adatbázis sikeresen alaphelyzetbe állítva! Az alapértelmezett termékkategóriákat, terméktípusokat és demo felhasználókat sikeresen létrehoztuk.")
        st.session_state.db_reset_success = False
    
    if st.session_state.db_reset_error:
        show_error(f"Hiba történt: {st.session_state.db_reset_error}")
        st.session_state.db_reset_error = ""
    
    st.write("Ezen az oldalon generálhatsz tesztadatokat a rendszerhez.")
    
    # Adatbázis alaphelyzetbe állítása
    with st.expander("Adatbázis alaphelyzetbe állítása", expanded=False):
        st.header("Adatbázis alaphelyzetbe állítása")
        
        st.warning("⚠️ Figyelmeztetés: Ez a művelet törli az összes adatot az adatbázisból és visszaállítja az alapértelmezett termékkategóriákat, terméktípusokat és demo felhasználókat!")
        
        # Egyszerű gomb a reset művelethez
        if st.button("ADATBÁZIS ALAPHELYZETBE ÁLLÍTÁSA", type="primary"):
            st.write("---")
            st.subheader("⚙️ Reset folyamat végrehajtása")
            st.write("1. Reset művelet előkészítése...")
            
            token = get_auth_token()
            if token:
                st.write("2. Felhasználói token rendben ✅")
            else:
                st.error("Hiányzó token! ❌")
                st.stop()
                
            headers = {"Authorization": f"Bearer {token}"}
            endpoint = f"{API_BASE_URL}/admin/reset-database"
            
            st.write(f"3. API végpont: `{endpoint}`")
            
            st.write("4. Reset API hívás végrehajtása...")
            
            try:
                response = requests.post(endpoint, headers=headers)
                
                st.write(f"5. Válasz státusz: **{response.status_code}**")
                
                try:
                    response_data = response.json()
                    st.json(response_data)
                except:
                    st.text(f"Nem JSON válasz: {response.text}")
                
                if response.status_code == 200:
                    st.success("✅ SIKERES ADATBÁZIS RESET!")
                else:
                    st.error(f"❌ HIBA A RESET SORÁN! Státusz kód: {response.status_code}")
                    
                    if response.status_code == 404:
                        st.warning("Az API végpont nem létezik. Ellenőrizd, hogy a backend implementálja-e az `/admin/reset-database` végpontot!")
                        
            except Exception as e:
                st.error(f"❌ HIBA: {str(e)}")
                import traceback
                st.code(traceback.format_exc())
            
            st.write("---")
    
    # Adatbázis alaphelyzetbe állítása (felhasználók meghagyásával)
    with st.expander("Adatbázis alaphelyzetbe állítása (felhasználók meghagyásával)", expanded=False):
        st.header("Adatbázis alaphelyzetbe állítása (felhasználók meghagyásával)")
        
        st.warning("⚠️ Figyelmeztetés: Ez a művelet törli az ajánlatokat, termékadatokat és egyéb adatokat, de meghagyja a felhasználókat és azok beállításait!")
        
        # Egyszerű gomb a reset művelethez
        if st.button("ADATBÁZIS ALAPHELYZETBE ÁLLÍTÁSA (FELHASZNÁLÓK MEGHAGYÁSÁVAL)", type="primary", key="reset_keep_users"):
            st.write("---")
            st.subheader("⚙️ Reset folyamat végrehajtása (felhasználók meghagyásával)")
            st.write("1. Reset művelet előkészítése...")
            
            token = get_auth_token()
            if token:
                st.write("2. Felhasználói token rendben ✅")
            else:
                st.error("Hiányzó token! ❌")
                st.stop()
                
            headers = {"Authorization": f"Bearer {token}"}
            endpoint = f"{API_BASE_URL}/admin/reset-database-keep-users"
            
            st.write(f"3. API végpont: `{endpoint}`")
            
            st.write("4. Reset API hívás végrehajtása...")
            
            try:
                response = requests.post(endpoint, headers=headers)
                
                st.write(f"5. Válasz státusz: **{response.status_code}**")
                
                try:
                    response_data = response.json()
                    st.json(response_data)
                except:
                    st.text(f"Nem JSON válasz: {response.text}")
                
                if response.status_code == 200:
                    st.success("✅ SIKERES ADATBÁZIS RESET (FELHASZNÁLÓK MEGHAGYVA)!")
                else:
                    st.error(f"❌ HIBA A RESET SORÁN! Státusz kód: {response.status_code}")
                    
                    if response.status_code == 404:
                        st.warning("Az API végpont nem létezik. Ellenőrizd, hogy a backend implementálja-e az `/admin/reset-database-keep-users` végpontot!")
                        
            except Exception as e:
                st.error(f"❌ HIBA: {str(e)}")
                import traceback
                st.code(traceback.format_exc())
            
            st.write("---")
    
    st.markdown("---")
    
    # Fülek létrehozása
    tab1, tab2, tab3, tab4 = st.tabs(["Tömeges generálás", "Komplex szcenáriók", "Termelők generálása", "Ajánlatok generálása"])
    
    with tab1:
        show_bulk_generator()
    
    with tab2:
        show_complex_scenarios()
    
    with tab3:
        show_producer_generator()
        
    with tab4:
        show_offer_generator()

def show_bulk_generator():
    """
    Tömeges adatgenerálás felület megjelenítése
    """
    st.header("Tömeges adatgenerálás")
    st.write("Itt egyszerre több termelőt és ajánlatot generálhatsz.")
    
    # Felhasználók generálása
    st.subheader("Felhasználók generálása")
    user_count = st.slider("Generálandó felhasználók száma:", 5, 200, 50, key="bulk_user_count")
    
    # Termelő/ügyintéző arány
    producer_ratio = st.slider("Termelők aránya (%):", 0, 100, 80, key="bulk_producer_ratio")
    
    # Ajánlatok generálása
    st.subheader("Ajánlatok generálása")
    offer_count = st.slider("Generálandó ajánlatok száma:", 10, 1000, 200, key="bulk_offer_count")
    
    # Dátum tartomány
    st.subheader("Dátum tartomány")
    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input("Kezdő dátum:", datetime.now() - timedelta(days=30), key="bulk_start_date")
    with col2:
        end_date = st.date_input("Vég dátum:", datetime.now() + timedelta(days=30), key="bulk_end_date")
    
    # Státusz arányok
    st.subheader("Ajánlat státuszok aránya (%)")
    col1, col2 = st.columns(2)
    with col1:
        created_ratio = st.slider("CREATED:", 0, 100, 20, key="bulk_created_ratio")
        confirmed_ratio = st.slider("CONFIRMED_BY_COMPANY:", 0, 100, 30, key="bulk_confirmed_ratio")
    with col2:
        accepted_ratio = st.slider("ACCEPTED_BY_USER:", 0, 100, 20, key="bulk_accepted_ratio")
        rejected_ratio = st.slider("REJECTED_BY_USER:", 0, 100, 10, key="bulk_rejected_ratio")
    finalized_ratio = st.slider("FINALIZED:", 0, 100, 20, key="bulk_finalized_ratio")
    
    # Ellenőrizzük, hogy az arányok összege 100
    total_ratio = created_ratio + confirmed_ratio + accepted_ratio + rejected_ratio + finalized_ratio
    if total_ratio != 100:
        st.warning(f"A státusz arányok összege {total_ratio}%, de 100%-nak kellene lennie!")
    
    # Indítás gomb
    if st.button("Tömeges adatgenerálás indítása", type="primary"):
        with st.spinner("Adatok generálása folyamatban..."):
            try:
                token = get_auth_token()
                headers = {"Authorization": f"Bearer {token}"}
                
                # Generált felhasználók listája
                generated_users = []
                
                # Felhasználók generálása
                for i in range(user_count):
                    # Döntsük el, hogy termelő vagy ügyintéző legyen
                    is_producer = random.random() < (producer_ratio / 100)
                    role = "termelő" if is_producer else "ügyintéző"
                    
                    # Felhasználó adatok összeállítása
                    user_data = {
                        "email": f"test_{role}_{i+1}@test.com",
                        "contact_name": f"Test {role.title()} {i+1}",
                        "company_name": f"Test Company {i+1}",
                        "phone_number": f"+36{random.randint(10000000, 99999999)}",
                        "role": role,
                        "is_active": True,
                        "is_verified": True,
                        "password": "Password123"  # Alapértelmezett jelszó
                    }
                    
                    # API hívás a felhasználó létrehozásához
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/auth/register",
                            json=user_data,
                            headers=headers
                        )
                        
                        if response.status_code == 200 or response.status_code == 201:
                            # Sikeres létrehozás
                            user_result = response.json()
                            generated_users.append(user_result)
                            
                            # Ha termelő, akkor beállítunk neki random alapértelmezett értékeket
                            if is_producer:
                                # Random terméktípus és minőségi besorolás kiválasztása
                                success, categories = products_api.get_product_categories()
                                if success and categories:
                                    # Random kategória kiválasztása
                                    selected_category = random.choice(categories)
                                    
                                    # Terméktípusok lekérdezése a kiválasztott kategóriához
                                    success, product_types = products_api.get_product_types(category_id=selected_category["id"])
                                    if success and product_types:
                                        # Random terméktípus kiválasztása
                                        selected_type = random.choice(product_types)
                                        has_quality_grades = selected_type.get("has_quality_grades", False)
                                        
                                        # Random minőségi besorolás kiválasztása, ha szükséges
                                        selected_grade = None
                                        if has_quality_grades:
                                            success, quality_grades = products_api.get_quality_grades(product_type_id=selected_type["id"])
                                            if success and quality_grades:
                                                selected_grade = random.choice(quality_grades)
                                                
                                        # Random mértékegység kiválasztása
                                        quantity_unit = random.choice(["kg", "tonna"])
                                        
                                        # Beállítások mentése
                                        settings_data = {
                                            "default_product_type_id": selected_type["id"],
                                            "default_quality_grade_id": selected_grade["id"] if selected_grade else None,
                                            "default_quantity_unit": quantity_unit,
                                            "default_product_type_name": selected_type["name"],
                                            "default_quality_grade_name": selected_grade["name"] if selected_grade else "",
                                            "default_category_id": selected_category["id"],
                                            "default_category_name": selected_category["name"],
                                            "has_quality_grades": has_quality_grades
                                        }
                                        
                                        # API hívás a beállítások mentéséhez
                                        settings_response = requests.put(
                                            f"{API_BASE_URL}/admin/users/{user_result['id']}/settings",
                                            json=settings_data,
                                            headers=headers
                                        )
                                        
                                        if not (settings_response.status_code == 200 or settings_response.status_code == 201):
                                            show_error(f"Beállítások mentése sikertelen a(z) {user_data['email']} felhasználóhoz: {settings_response.text}")
                        else:
                            show_error(f"Hiba a(z) {user_data['email']} felhasználó létrehozásakor: {response.text}")
                    except Exception as e:
                        show_error(f"Hiba a(z) {user_data['email']} felhasználó létrehozásakor: {str(e)}")
                
                if not generated_users:
                    show_error("Nem sikerült egyetlen felhasználót sem létrehozni!")
                    return
                
                # Ajánlatok generálása
                generated_offers = []
                producer_users = [u for u in generated_users if u["role"] == "termelő"]
                
                if not producer_users:
                    show_error("Nincsenek termelők az ajánlatok generálásához!")
                    return
                
                # Terméktípusok lekérdezése
                success, all_categories = products_api.get_product_categories()
                all_product_types = []
                
                if success and all_categories:
                    for category in all_categories:
                        success, types = products_api.get_product_types(category_id=category["id"])
                        if success and types:
                            all_product_types.extend(types)
                
                if not all_product_types:
                    show_error("Nincsenek terméktípusok az ajánlatok generálásához!")
                    return
                
                for i in range(offer_count):
                    # Véletlenszerű termelő kiválasztása
                    producer = random.choice(producer_users)
                    
                    # Véletlenszerű terméktípus kiválasztása
                    selected_type = random.choice(all_product_types)
                    
                    # Véletlenszerű minőségi besorolás kiválasztása, ha szükséges
                    selected_grade_id = None
                    if selected_type.get("has_quality_grades", False):
                        success, grades = products_api.get_quality_grades(product_type_id=selected_type["id"])
                        if success and grades:
                            selected_grade = random.choice(grades)
                            selected_grade_id = selected_grade["id"]
                    
                    # Véletlenszerű státusz kiválasztása az arányok alapján
                    status_rand = random.random()
                    if status_rand < created_ratio / 100:
                        status = "CREATED"
                    elif status_rand < (created_ratio + confirmed_ratio) / 100:
                        status = "CONFIRMED_BY_COMPANY"
                    elif status_rand < (created_ratio + confirmed_ratio + accepted_ratio) / 100:
                        status = "ACCEPTED_BY_USER"
                    elif status_rand < (created_ratio + confirmed_ratio + accepted_ratio + rejected_ratio) / 100:
                        status = "REJECTED_BY_USER"
                    else:
                        status = "FINALIZED"
                    
                    # Szállítási dátum generálása
                    delivery_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
                    
                    # Ajánlat adatok összeállítása
                    offer_data = {
                        "user_id": producer["id"],
                        "product_type_id": selected_type["id"],
                        "quality_grade_id": selected_grade_id,
                        "quantity_in_kg": random.randint(100, 1000),
                        "delivery_date": delivery_date.isoformat(),
                        "note": f"Teszt ajánlat #{i+1} - {selected_type['name']}",
                        "status": status
                    }
                    
                    # API hívás az ajánlat létrehozásához
                    try:
                        # Az új create_offer_for_user függvényt használjuk a státusz beállításához
                        success, result = create_offer_for_user(offer_data, status)
                        
                        if success:
                            # Sikeres létrehozás
                            offer_result = result
                            generated_offers.append(offer_result)
                        else:
                            show_error(f"Hiba a(z) {i+1}. ajánlat létrehozásakor: {result}")
                    except Exception as e:
                        show_error(f"Hiba a(z) {i+1}. ajánlat létrehozásakor: {str(e)}")
                
                # Eredmény megjelenítése
                show_success(f"Sikeresen generálva: {len(generated_users)} felhasználó és {len(generated_offers)} ajánlat!")
                
                # Részletes eredmények
                with st.expander("Részletes eredmények"):
                    st.subheader("Létrehozott felhasználók")
                    for user in generated_users:
                        st.write(f"- **{user.get('contact_name')}** ({user.get('email')}) - **Szerepkör**: {user.get('role')}")
                    
                    st.subheader("Létrehozott ajánlatok")
                    for offer in generated_offers:
                        st.write(f"- **ID**: {offer.get('id')} - **Státusz**: {offer.get('status')} - **Mennyiség**: {offer.get('quantity_in_kg')} kg")
                
            except Exception as e:
                show_error(f"Hiba történt: {str(e)}")

def show_producer_generator():
    """
    Termelők generálásának felülete
    """
    st.header("Termelők generálása")
    st.write("Itt egyedi termelőket generálhatsz teszteléshez.")
    
    # Termelők adatainak beállítása
    st.subheader("Termelő adatai")
    
    # Oszlopok a jobb elrendezéshez
    col1, col2 = st.columns(2)
    
    with col1:
        # Generálás mód kiválasztása
        generation_mode = st.radio(
            "Generálás módja:", 
            ["Egyszerű generálás", "Részletes testreszabás", "Tömegesen generálás"],
            index=0
        )
        
        if generation_mode == "Tömegesen generálás":
            producer_count = st.number_input("Generálandó termelők száma:", min_value=1, max_value=50, value=5)
            use_realistic_data = st.checkbox("Realisztikus adatok használata", value=True)
            
            if not use_realistic_data:
                producer_name_prefix = st.text_input("Név előtag:", value="Test Producer")
                producer_email_domain = st.text_input("Email domain:", value="test.com")
        elif generation_mode == "Egyszerű generálás":
            producer_count = 1
            use_realistic_data = True
        else:  # Részletes testreszabás
            producer_count = 1
            use_realistic_data = True
            
            # Termelő profiljának generálása
            producer_profile = generate_producer_profile()
            
            # Alapadatok
            contact_name = st.text_input("Kapcsolattartó neve:", value=faker.name())
            email = st.text_input("Email cím:", value=f"{contact_name.lower().replace(' ', '.')}@termelok.hu")
            phone_number = st.text_input("Telefonszám:", value=generate_magyar_telefonszam())
    
    with col2:
        if generation_mode == "Részletes testreszabás":
            # Cégadatok
            company_name = st.text_input("Cégnév:", value=producer_profile["company_name"])
            tax_id = st.text_input("Adószám:", value=f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}")
            
            # Régió és megye kiválasztása
            region = st.selectbox("Régió:", options=list(MAGYAR_REGIOK.keys()), index=list(MAGYAR_REGIOK.keys()).index(producer_profile["region"]), key="producer_customization_region")
            county = st.selectbox("Megye:", options=MAGYAR_REGIOK[region], index=MAGYAR_REGIOK[region].index(producer_profile["county"]) if producer_profile["county"] in MAGYAR_REGIOK[region] else 0, key="producer_customization_county")
            
            # Vállalkozás mérete
            company_size = st.selectbox("Vállalkozás mérete:", options=list(VALLALKOZAS_MERETEK.keys()), index=list(VALLALKOZAS_MERETEK.keys()).index(producer_profile["company_size"]), key="producer_customization_company_size")
            
            # Alkalmazottak és földterület
            employees = st.number_input("Alkalmazottak száma:", min_value=1, max_value=200, value=producer_profile["employees"])
            land_area = st.number_input("Földterület mérete (hektár):", min_value=1, max_value=5000, value=producer_profile["land_area"])
        else:
            producer_active = st.checkbox("Aktív felhasználók", value=True)
            producer_verified = st.checkbox("Ellenőrzött email", value=True)
            generate_default_settings = st.checkbox("Alapértelmezett beállítások generálása", value=True)
            
            # Kategória preferencia
            if use_realistic_data:
                st.subheader("Termelő profilja")
                specialization_category = st.selectbox(
                    "Fő termékkategória:", 
                    options=list(MEZOGAZDASAGI_TERMEKEK.keys()),
                    index=0,
                    key="producer_generator_specialization"
                )
                
                # Régió és megye
                region = st.selectbox("Régió:", options=list(MAGYAR_REGIOK.keys()), key="producer_generator_region")
                county = st.selectbox("Megye:", options=MAGYAR_REGIOK[region], key="producer_generator_county")
    
    # Alapértelmezett beállítások generálása
    if (generation_mode != "Részletes testreszabás" and generate_default_settings) or generation_mode == "Részletes testreszabás":
        st.subheader("Alapértelmezett termék beállítások")
        
        # Kategóriák lekérdezése
        success, categories = products_api.get_product_categories()
        if success and categories:
            category_options = [(c["id"], c["name"]) for c in categories]
            selected_category_id = st.selectbox(
                "Alapértelmezett kategória:", 
                options=[c[0] for c in category_options],
                format_func=lambda x: next((c[1] for c in category_options if c[0] == x), ""),
                key="producer_generator_category"
            )
            
            # Terméktípusok lekérdezése
            if selected_category_id:
                success, product_types = products_api.get_product_types(category_id=selected_category_id)
                if success and product_types:
                    type_options = [(pt["id"], pt["name"]) for pt in product_types]
                    selected_type_id = st.selectbox(
                        "Alapértelmezett terméktípus:", 
                        options=[t[0] for t in type_options],
                        format_func=lambda x: next((t[1] for t in type_options if t[0] == x), ""),
                        key="producer_generator_type"
                    )
                    
                    # Terméktípus adatok lekérése
                    if selected_type_id:
                        selected_type = next((pt for pt in product_types if pt["id"] == selected_type_id), None)
                        has_quality_grades = selected_type and selected_type.get("has_quality_grades", False)
                        
                        # Minőségi besorolások lekérdezése
                        if has_quality_grades:
                            success, quality_grades = products_api.get_quality_grades(product_type_id=selected_type_id)
                            if success and quality_grades:
                                grade_options = [(g["id"], g["name"]) for g in quality_grades]
                                selected_grade_id = st.selectbox(
                                    "Alapértelmezett minőségi besorolás:", 
                                    options=[g[0] for g in grade_options],
                                    format_func=lambda x: next((g[1] for g in grade_options if g[0] == x), ""),
                                    key="producer_generator_grade"
                                )
                            else:
                                selected_grade_id = None
                        else:
                            selected_grade_id = None
                            st.info("A kiválasztott terméktípushoz nem tartozik minőségi besorolás.")
                    else:
                        selected_grade_id = None
                else:
                    st.warning("Nem sikerült lekérni a terméktípusokat.")
                    selected_type_id = None
                    selected_grade_id = None
        else:
            st.warning("Nem sikerült lekérni a kategóriákat.")
            selected_category_id = None
            selected_type_id = None
            selected_grade_id = None
        
        # Mértékegység
        quantity_unit = st.selectbox("Alapértelmezett mértékegység:", ["kg", "tonna", "db", "q"], key="producer_generator_quantity_unit")
    else:
        selected_category_id = None
        selected_type_id = None
        selected_grade_id = None
        quantity_unit = "kg"
    
    # Generálás gomb
    if st.button("Termelők generálása", type="primary"):
        with st.spinner("Termelők generálása folyamatban..."):
            # API hívás a backend felé - direktben hívjuk az API-t, mert ez egy admin funkció
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # Generált termelők listája
            generated_producers = []
            
            # Haladásjelző
            if producer_count > 1:
                progress_bar = st.progress(0)
                progress_text = st.empty()
            
            for i in range(producer_count):
                if producer_count > 1:
                    progress_bar.progress((i + 1) / producer_count)
                    progress_text.text(f"Termelő generálása: {i + 1}/{producer_count}")
                
                # Termelői profil generálása realisztikus adatokkal
                if generation_mode == "Részletes testreszabás":
                    # Már kitöltött adatokat használjuk
                    producer_data = {
                        "email": email,
                        "contact_name": contact_name,
                        "company_name": company_name,
                        "tax_id": tax_id,
                        "phone_number": phone_number,
                        "role": "termelő",
                        "is_active": True,
                        "is_verified": True,
                        "password": "Password123",
                        "region": region,
                        "county": county,
                        "company_size": company_size,
                        "employees": employees,
                        "land_area": land_area
                    }
                elif use_realistic_data:
                    # Generált realisztikus profilt használunk
                    producer_profile = generate_producer_profile()
                    
                    if generation_mode == "Tömegesen generálás":
                        # Több termelőnél sorszámozzuk őket
                        name_suffix = f" {i+1}" if producer_count > 1 else ""
                    else:
                        name_suffix = ""
                    
                    # Ha van megadott régió/kategória preferencia, azt használjuk
                    if 'region' in locals() and region:
                        producer_profile["region"] = region
                        producer_profile["county"] = county
                    
                    if 'specialization_category' in locals() and specialization_category:
                        producer_profile["specialization_category"] = specialization_category
                    
                    producer_data = {
                        "email": f"{faker.user_name().lower()}@termelok.hu",
                        "contact_name": faker.name() + name_suffix,
                        "company_name": producer_profile["company_name"],
                        "tax_id": f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}",
                        "phone_number": generate_magyar_telefonszam(),
                        "role": "termelő",
                        "is_active": producer_active if 'producer_active' in locals() else True,
                        "is_verified": producer_verified if 'producer_verified' in locals() else True,
                        "password": "Password123"  # Alapértelmezett jelszó
                    }
                else:
                    # Egyszerű tesztadatokat használunk
                    producer_data = {
                        "email": f"{producer_name_prefix.lower().replace(' ', '')}{i+1}@{producer_email_domain}",
                        "contact_name": f"{producer_name_prefix} {i+1}",
                        "company_name": f"{producer_name_prefix} Ltd. {i+1}",
                        "phone_number": f"+36{random.randint(10000000, 99999999)}",
                        "role": "termelő",
                        "is_active": producer_active,
                        "is_verified": producer_verified,
                        "password": "Password123"  # Alapértelmezett jelszó
                    }
                
                # API hívás a felhasználó létrehozásához
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/register",
                        json=producer_data,
                        headers=headers
                    )
                    
                    if response.status_code == 200 or response.status_code == 201:
                        # Sikeres létrehozás
                        user_result = response.json()
                        generated_producers.append(user_result)
                        
                        # Alapértelmezett beállítások mentése, ha szükséges
                        if ((generation_mode != "Részletes testreszabás" and generate_default_settings) or 
                            generation_mode == "Részletes testreszabás") and selected_type_id:
                            user_id = user_result.get("id")
                            
                            # Beállítások adatok összeállítása
                            settings_data = {
                                "default_product_type_id": selected_type_id,
                                "default_quality_grade_id": selected_grade_id,
                                "default_quantity_unit": quantity_unit,
                                "default_product_type_name": next((t[1] for t in type_options if t[0] == selected_type_id), ""),
                                "default_quality_grade_name": next((g[1] for g in grade_options if g[0] == selected_grade_id), "") if 'has_quality_grades' in locals() and has_quality_grades and selected_grade_id else "",
                                "default_category_id": selected_category_id,
                                "default_category_name": next((c[1] for c in category_options if c[0] == selected_category_id), ""),
                                "has_quality_grades": has_quality_grades if 'has_quality_grades' in locals() else False
                            }
                            
                            # API hívás a beállítások mentéséhez
                            settings_response = requests.put(
                                f"{API_BASE_URL}/admin/users/{user_id}/settings",
                                json=settings_data,
                                headers=headers
                            )
                            
                            if not (settings_response.status_code == 200 or settings_response.status_code == 201):
                                st.warning(f"Beállítások mentése sikertelen a(z) {producer_data['email']} felhasználóhoz: {settings_response.text}")
                    else:
                        st.error(f"Hiba a(z) {producer_data['email']} termelő létrehozásakor: {response.text}")
                except Exception as e:
                    st.error(f"Hiba a(z) {producer_data['email']} termelő létrehozásakor: {str(e)}")
            
            # Haladásjelző törlése
            if producer_count > 1:
                progress_bar.empty()
                progress_text.empty()
            
            # Eredmény megjelenítése
            if generated_producers:
                st.success(f"Sikeresen létrehozva {len(generated_producers)} termelő!")
                
                # Részletes adatok megjelenítése
                with st.expander("Létrehozott termelők adatai", expanded=True):
                    for producer in generated_producers:
                        st.markdown(f"""
                        #### {producer.get('contact_name')}
                        - **Email**: {producer.get('email')}
                        - **Cégnév**: {producer.get('company_name')}
                        - **Telefon**: {producer.get('phone_number')}
                        - **ID**: {producer.get('id')}
                        - **Jelszó**: Password123
                        """)
                
                # Ajánlat generálása az új termelőhöz
                if len(generated_producers) == 1:
                    if st.button("Ajánlatok generálása ehhez a termelőhöz"):
                        st.switch_page("pages/admin/data_generator.py#ajánlatok-generálása")
            else:
                st.error("Nem sikerült egyetlen termelőt sem létrehozni.")

def show_offer_generator():
    """
    Ajánlatok generálásának felülete
    """
    st.header("Ajánlatok generálása")
    st.write("Itt egyedi ajánlatokat generálhatsz különböző státuszokkal teszteléshez.")
    
    # Termelők lekérdezése
    success, producers = users_api.get_users(params={"role": "termelő", "is_active": True})
    
    if not (success and producers):
        st.error("Nem sikerült lekérni a termelőket, vagy nincsenek aktív termelők a rendszerben.")
        st.info("Kérjük, először hozz létre termelőket a 'Termelők generálása' fülön.")
        return
    
    # Ajánlat adatok beállítása
    st.subheader("Ajánlat adatai")
    
    # Oszlopok a jobb elrendezéshez
    col1, col2 = st.columns(2)
    
    with col1:
        # Termelő kiválasztása
        selected_producer_id = st.selectbox(
            "Válassz létező termelőt:", 
            options=[p["id"] for p in producers],
            format_func=lambda x: next((f"{p['contact_name']} ({p['email']})" for p in producers if p["id"] == x), ""),
            key="active_producer_existing_producer"
        )
        
        # Ajánlatok száma
        offer_count = st.number_input("Generálandó ajánlatok száma:", min_value=1, max_value=100, value=5)
        
        # Státusz kiválasztása
        offer_status = st.selectbox(
            "Ajánlat státusz:", 
            ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"],
            index=1,
            key="offer_generator_status"
        )
    
    with col2:
        # Dátum beállítások
        delivery_date = st.date_input(
            "Szállítási dátum:", 
            value=datetime.now() + timedelta(days=14),
            min_value=datetime.now()
        )
        
        # Mennyiség és ár
        min_quantity = st.number_input("Minimum mennyiség:", min_value=10, value=100)
        max_quantity = st.number_input("Maximum mennyiség:", min_value=min_quantity, value=min_quantity + 500)
        
        min_price = st.number_input("Minimum ár (Ft):", min_value=100, value=1000)
        max_price = st.number_input("Maximum ár (Ft):", min_value=min_price, value=min_price + 2000)
    
    # Termék kiválasztása
    st.subheader("Termék adatai")
    
    # Kategóriák lekérdezése
    success, categories = products_api.get_product_categories()
    if success and categories:
        category_options = [(c["id"], c["name"]) for c in categories]
        selected_category_id = st.selectbox(
            "Kategória:", 
            options=[c["id"] for c in categories],
            format_func=lambda x: next((c["name"] for c in categories if c["id"] == x), ""),
            key="offer_generator_category"
        )
        
        # Terméktípusok lekérdezése
        if selected_category_id:
            success, product_types = products_api.get_product_types(category_id=selected_category_id)
            if success and product_types:
                type_options = [(pt["id"], pt["name"]) for pt in product_types]
                selected_type_id = st.selectbox(
                    "Terméktípus:", 
                    options=[t[0] for t in type_options],
                    format_func=lambda x: next((t[1] for t in type_options if t[0] == x), ""),
                    key="offer_generator_type"
                )
                
                # Terméktípus adatok lekérése
                if selected_type_id:
                    selected_type = next((pt for pt in product_types if pt["id"] == selected_type_id), None)
                    has_quality_grades = selected_type and selected_type.get("has_quality_grades", False)
                    
                    # Minőségi besorolások lekérdezése
                    if has_quality_grades:
                        success, quality_grades = products_api.get_quality_grades(product_type_id=selected_type_id)
                        if success and quality_grades:
                            grade_options = [(g["id"], g["name"]) for g in quality_grades]
                            selected_grade_id = st.selectbox(
                                "Minőségi besorolás:", 
                                options=[g[0] for g in grade_options],
                                format_func=lambda x: next((g[1] for g in grade_options if g[0] == x), ""),
                                key="offer_generator_grade"
                            )
                        else:
                            selected_grade_id = None
                            st.warning("Nem sikerült lekérni a minőségi besorolásokat.")
                    else:
                        selected_grade_id = None
                        st.info("A kiválasztott terméktípushoz nem tartozik minőségi besorolás.")
                else:
                    selected_grade_id = None
            else:
                st.warning("Nem sikerült lekérni a terméktípusokat.")
                selected_type_id = None
                selected_grade_id = None
    else:
        st.warning("Nem sikerült lekérni a kategóriákat.")
        selected_category_id = None
        selected_type_id = None
        selected_grade_id = None
    
    # Mértékegység
    quantity_unit = st.selectbox("Mértékegység:", ["kg", "l", "db", "q"], key="offer_generator_quantity_unit")
    
    # Generálás gomb
    if st.button("Ajánlatok generálása", type="primary"):
        if not selected_producer_id or not selected_type_id:
            st.error("Termelő és terméktípus kiválasztása kötelező!")
            return
            
        with st.spinner("Ajánlatok generálása folyamatban..."):
            # API hívás a backend felé - direktben hívjuk az API-t, mert ez egy admin funkció
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # Generált ajánlatok listája
            generated_offers = []
            
            # Kiválasztott termék adatok
            product_type_name = next((t[1] for t in type_options if t[0] == selected_type_id), "")
            quality_grade_name = next((g[1] for g in grade_options if g[0] == selected_grade_id), "") if has_quality_grades and selected_grade_id else ""
            
            for i in range(offer_count):
                # Random mennyiség és ár generálása
                quantity = random.randint(min_quantity, max_quantity)
                price = random.randint(min_price, max_price)
                
                # Ajánlat adatok összeállítása
                offer_data = {
                    "user_id": selected_producer_id,
                    "product_type_id": selected_type_id,
                    "quality_grade_id": selected_grade_id,
                    "quantity_in_kg": quantity,
                    "delivery_date": delivery_date.isoformat(),
                    "note": f"Teszt ajánlat #{i+1} - {product_type_name} {quality_grade_name}",
                    "status": offer_status
                }
                
                # API hívás az ajánlat létrehozásához
                try:
                    # Az új create_offer_for_user függvényt használjuk a státusz beállításához
                    success, result = create_offer_for_user(offer_data, offer_status)
                    
                    if success:
                        # Sikeres létrehozás
                        offer_result = result
                        generated_offers.append(offer_result)
                    else:
                        st.error(f"Hiba a(z) {i+1}. ajánlat létrehozásakor: {result}")
                except Exception as e:
                    st.error(f"Hiba a(z) {i+1}. ajánlat létrehozásakor: {str(e)}")
            
            # Eredmény megjelenítése
            if generated_offers:
                st.success(f"Sikeresen létrehozva {len(generated_offers)} ajánlat!")
                with st.expander("Létrehozott ajánlatok"):
                    for offer in generated_offers:
                        st.write(f"- **ID**: {offer.get('id')} - **Termék**: {product_type_name} - **Mennyiség**: {offer.get('quantity_in_kg')} kg")
            else:
                st.error("Nem sikerült egyetlen ajánlatot sem létrehozni.")

def create_offer_for_user(offer_data, status=None):
    """
    Új ajánlat létrehozása más felhasználó nevében API hívás (admin/ügyintéző funkció)
    
    Args:
        offer_data (dict): Ajánlat adatai
        status (str): Opcionális státusz, amit be szeretnénk állítani (alapértelmezett: CREATED)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        # Eredeti admin token elmentése későbbi használatra
        admin_token = token
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Először létrehozzuk az ajánlatot
        response = requests.post(
            f"{API_BASE_URL}/offers/for-user",
            headers=headers,
            json=offer_data
        )
        
        if response.status_code == 201:
            offer_result = response.json()
            
            # Ha megadtunk státuszt és az nem CREATED, akkor külön beállítjuk
            if status and status != "CREATED":
                offer_id = offer_result["id"]
                producer_id = offer_data["user_id"]  # A termelő (tulajdonos) azonosítója
                
                # Mentsük el a termelő jelszavát az automatikus bejelentkezéshez
                producer_password = "Password123"  # Az alapértelmezett jelszó, amit használtunk a generálásnál
                
                # Státusz frissítése megfelelő végponton keresztül
                status_endpoint = None
                
                if status == "CONFIRMED_BY_COMPANY":
                    # Cég általi megerősítés - Ezt az admin tudja csinálni
                    status_endpoint = f"{API_BASE_URL}/offers/{offer_id}/confirm"
                    status_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    status_response = requests.post(status_endpoint, headers=headers, json=status_data)
                elif status == "ACCEPTED_BY_USER" or status == "REJECTED_BY_USER":
                    # Termelői művelet - először meg kell erősíteni admin-ként
                    confirm_endpoint = f"{API_BASE_URL}/offers/{offer_id}/confirm"
                    confirm_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    confirm_response = requests.post(confirm_endpoint, headers=headers, json=confirm_data)
                    
                    if confirm_response.status_code == 200:
                        # Először lekérjük a termelő email címét
                        producer_response = requests.get(f"{API_BASE_URL}/users/{producer_id}", headers=headers)
                        if producer_response.status_code != 200:
                            return False, f"Hiba a termelő adatainak lekérésekor: {producer_response.text}"
                        
                        producer_data = producer_response.json()
                        producer_email = producer_data.get("email")
                        
                        if not producer_email:
                            return False, "Nem sikerült lekérni a termelő email címét"
                        
                        # FONTOS: Aktiváljuk a felhasználót, ha még nincs aktiválva
                        if not producer_data.get("is_active", False):
                            activate_response = requests.put(
                                f"{API_BASE_URL}/users/{producer_id}/activate",
                                headers=headers
                            )
                            if activate_response.status_code != 200:
                                return False, f"Hiba a termelő aktiválásakor: {activate_response.text}"
                        
                        # Bejelentkezés a termelő nevében
                        login_data = {
                            "username": producer_email,
                            "password": producer_password
                        }
                        login_response = requests.post(
                            f"{API_BASE_URL}/auth/login", 
                            data=login_data,
                            headers={"Content-Type": "application/x-www-form-urlencoded"}
                        )
                        
                        if login_response.status_code != 200:
                            return False, f"Hiba a termelő nevében történő bejelentkezéskor: {login_response.text}"
                        
                        producer_token = login_response.json().get("access_token")
                        producer_headers = {"Authorization": f"Bearer {producer_token}"}
                        
                        # Termelői művelet végrehajtása
                        if status == "ACCEPTED_BY_USER":
                            status_endpoint = f"{API_BASE_URL}/offers/{offer_id}/accept"
                            status_response = requests.post(status_endpoint, headers=producer_headers)
                        else:  # REJECTED_BY_USER
                            status_endpoint = f"{API_BASE_URL}/offers/{offer_id}/reject"
                            status_response = requests.post(status_endpoint, headers=producer_headers)
                        
                        # Nincs szükség visszajelentkezésre, a következő kéréseket az eredeti admin tokennel tesszük meg
                    else:
                        return False, f"Hiba az ajánlat megerősítése során: {confirm_response.text}"
                elif status == "FINALIZED":
                    # Véglegesítés - először meg kell erősíteni admin-ként, aztán a termelő elfogadja, aztán az admin véglegesít
                    confirm_endpoint = f"{API_BASE_URL}/offers/{offer_id}/confirm"
                    confirm_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    confirm_response = requests.post(confirm_endpoint, headers=headers, json=confirm_data)
                    
                    if confirm_response.status_code == 200:
                        # Először lekérjük a termelő email címét
                        producer_response = requests.get(f"{API_BASE_URL}/users/{producer_id}", headers=headers)
                        if producer_response.status_code != 200:
                            return False, f"Hiba a termelő adatainak lekérésekor: {producer_response.text}"
                        
                        producer_data = producer_response.json()
                        producer_email = producer_data.get("email")
                        
                        if not producer_email:
                            return False, "Nem sikerült lekérni a termelő email címét"
                        
                        # FONTOS: Aktiváljuk a felhasználót, ha még nincs aktiválva
                        if not producer_data.get("is_active", False):
                            activate_response = requests.put(
                                f"{API_BASE_URL}/users/{producer_id}/activate",
                                headers=headers
                            )
                            if activate_response.status_code != 200:
                                return False, f"Hiba a termelő aktiválásakor: {activate_response.text}"
                        
                        # Bejelentkezés a termelő nevében
                        login_data = {
                            "username": producer_email,
                            "password": producer_password
                        }
                        login_response = requests.post(
                            f"{API_BASE_URL}/auth/login", 
                            data=login_data,
                            headers={"Content-Type": "application/x-www-form-urlencoded"}
                        )
                        
                        if login_response.status_code != 200:
                            return False, f"Hiba a termelő nevében történő bejelentkezéskor: {login_response.text}"
                        
                        producer_token = login_response.json().get("access_token")
                        producer_headers = {"Authorization": f"Bearer {producer_token}"}
                        
                        # Termelő elfogadja
                        accept_endpoint = f"{API_BASE_URL}/offers/{offer_id}/accept"
                        accept_response = requests.post(accept_endpoint, headers=producer_headers)
                        
                        if accept_response.status_code != 200:
                            return False, f"Hiba az ajánlat elfogadása során: {accept_response.text}"
                        
                        # Admin véglegesíti az eredeti tokennel
                        status_endpoint = f"{API_BASE_URL}/offers/{offer_id}/finalize"
                        status_response = requests.post(status_endpoint, headers={"Authorization": f"Bearer {admin_token}"})
                    else:
                        return False, f"Hiba az ajánlat megerősítése során: {confirm_response.text}"
                
                # Ellenőrizzük, hogy sikeres volt-e a státusz módosítás
                if status_endpoint and status_response.status_code != 200:
                    return False, f"Hiba az ajánlat {status} státuszra állítása során: {status_response.text}"
                
                # Frissített ajánlat adatok lekérése az admin tokennel
                get_offer_response = requests.get(
                    f"{API_BASE_URL}/offers/{offer_id}", 
                    headers={"Authorization": f"Bearer {admin_token}"}
                )
                if get_offer_response.status_code == 200:
                    return True, get_offer_response.json()
            
            return True, offer_result
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def show_complex_scenarios():
    """
    Komplex szcenáriók adatgenerálásához.
    Itt több összetett, valós helyzeteket szimuláló adatgenerálási lehetőség érhető el.
    """
    st.header("Komplex szcenáriók")
    st.write("""
    Ebben a szekcióban valós mezőgazdasági helyzeteket szimuláló komplex adatcsoportokat generálhatsz.
    Minden szcenárió több termelőt és hozzájuk tartozó sok ajánlatot hoz létre speciális jellemzőkkel.
    """)
    
    scenario = st.selectbox(
        "Válassz forgatókönyvet:", 
        ["Aktív termelő létrehozása", "Szezonális ajánlatok létrehozása", "Kategória statisztika", "Regionális termelők"],
        key="complex_scenario_selector"
    )
    
    st.divider()
    
    if scenario == "Aktív termelő létrehozása":
        show_active_producer_scenario()
    elif scenario == "Szezonális ajánlatok létrehozása":
        show_seasonal_scenario()
    elif scenario == "Kategória statisztika":
        show_category_scenario()
    elif scenario == "Regionális termelők":
        show_regional_scenario()

def show_active_producer_scenario():
    """
    Egy aktív termelő részletes ajánlattörténettel való generálása.
    Ez a szcenárió egy kiválasztott termelő számára hoz létre sok ajánlatot
    az elmúlt időszakból, hogy részletes statisztikát lehessen készíteni.
    """
    st.subheader("Aktív termelő részletes ajánlattörténettel")
    
    st.markdown("""
    Ez a szcenárió létrehoz egy aktív termelőt (vagy használ egy meglévőt), majd generál hozzá:
    - Több hónapnyi ajánlat-történetet
    - Különböző státuszú ajánlatokat
    - Különböző terméktípusokra vonatkozó ajánlatokat
    - Szezonális eloszlású ajánlatokat
    - Különböző árkategóriájú tételeket
    
    Az így létrehozott adatokkal részletes statisztikákat, trendeket és grafikonokat lehet elemezni.
    """)
    
    # Termelő beállításai
    st.write("### Termelő beállítása")
    
    # Meglévő termelő használata vagy új létrehozása
    use_existing = st.checkbox("Meglévő termelő használata", value=False)
    
    producer_id = None
    producer_data = None
    
    if use_existing:
        # Meglévő termelők listázása
        success, producers = users_api.get_users(params={"role": "termelő", "is_active": True})
        
        if success and producers:
            producer_options = [(p["id"], f"{p['contact_name']} ({p['email']})") for p in producers]
            selected_producer_id = st.selectbox(
                "Válassz termelőt:", 
                options=[p[0] for p in producer_options],
                format_func=lambda x: next((p[1] for p in producer_options if p[0] == x), "")
            )
            
            producer_id = selected_producer_id
            producer_data = next((p for p in producers if p["id"] == producer_id), None)
        else:
            st.error("Nem található aktív termelő a rendszerben. Hozz létre egy újat!")
            use_existing = False
    
    if not use_existing:
        # Új termelő létrehozása
        producer_profile = generate_producer_profile()
        
        st.write("#### Új termelő adatai")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Név és kapcsolattartási adatok
            contact_name = st.text_input("Kapcsolattó neve:", value=faker.name())
            email_prefix = st.text_input("Email előtag:", value=faker.user_name().lower().replace("_", ""))
            email_domain = st.text_input("Email domain:", value="termelok.hu")
            email = f"{email_prefix}@{email_domain}"
            phone_number = st.text_input("Telefonszám:", value=generate_magyar_telefonszam())
            
        with col2:
            # Cég adatok
            company_name = st.text_input("Cégnév:", value=producer_profile["company_name"])
            tax_id = st.text_input("Adószám:", value=f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}")
            region = st.selectbox("Régió:", options=list(MAGYAR_REGIOK.keys()), index=list(MAGYAR_REGIOK.keys()).index(producer_profile["region"]), key="active_producer_scenario_region")
            county = st.selectbox("Megye:", options=MAGYAR_REGIOK[region], index=MAGYAR_REGIOK[region].index(producer_profile["county"]) if producer_profile["county"] in MAGYAR_REGIOK[region] else 0, key="active_producer_scenario_county")
    
    # Ajánlatok generálásának beállításai
    st.write("### Ajánlattörténet beállítása")
    
    col1, col2 = st.columns(2)
    
    with col1:
        offer_count = st.slider("Generálandó ajánlatok száma:", min_value=20, max_value=500, value=100, step=10)
        history_months = st.slider("Ajánlattörténet időtartama (hónap):", min_value=1, max_value=36, value=12)
        
        # Jelenlegi dátum mint végdátum
        end_date = datetime.now().date()
        start_date = (end_date - timedelta(days=30 * history_months))
        
        # Dátumtartomány finomítása
        date_range = st.date_input(
            "Ajánlatok időtartománya:",
            value=(start_date, end_date),
            min_value=end_date - timedelta(days=365*3),  # max 3 év visszamenőleg
            max_value=end_date + timedelta(days=365),    # max 1 év előre
        )
        
        if isinstance(date_range, tuple) and len(date_range) == 2:
            start_date, end_date = date_range
        
    with col2:
        # Státuszok eloszlása
        st.write("#### Ajánlatok státusz eloszlása")
        created_ratio = st.slider("CREATED (Létrehozva) %:", min_value=0, max_value=100, value=15)
        confirmed_ratio = st.slider("CONFIRMED_BY_COMPANY (Megerősítve) %:", min_value=0, max_value=100, value=20)
        accepted_ratio = st.slider("ACCEPTED_BY_USER (Elfogadva) %:", min_value=0, max_value=100, value=30)
        rejected_ratio = st.slider("REJECTED_BY_USER (Elutasítva) %:", min_value=0, max_value=100, value=15)
        finalized_ratio = st.slider("FINALIZED (Véglegesítve) %:", min_value=0, max_value=100, value=20)
        
        # Ellenőrizzük, hogy az arányok összege 100
        total_ratio = created_ratio + confirmed_ratio + accepted_ratio + rejected_ratio + finalized_ratio
        if total_ratio != 100:
            st.warning(f"A státusz arányok összege {total_ratio}%, de 100%-nak kellene lennie!")
    
    # Termék-preferenciák
    st.write("### Termékkategória preferenciák")
    st.write("Állítsd be, milyen termékkategóriákat kínál a termelő és milyen arányban:")
    
    # Kategóriák és preferenciák beállítása
    col1, col2, col3 = st.columns(3)
    
    with col1:
        gabonak_enabled = st.checkbox("Gabonafélék", value=True)
        gabonak_ratio = st.slider("Gabonafélék aránya (%):", min_value=0, max_value=100, value=40, disabled=not gabonak_enabled)
    
    with col2:
        zoldsegek_enabled = st.checkbox("Zöldségfélék", value=True) 
        zoldsegek_ratio = st.slider("Zöldségfélék aránya (%):", min_value=0, max_value=100, value=40, disabled=not zoldsegek_enabled)
    
    with col3:
        gyumolcsok_enabled = st.checkbox("Gyümölcsök", value=True)
        gyumolcsok_ratio = st.slider("Gyümölcsök aránya (%):", min_value=0, max_value=100, value=20, disabled=not gyumolcsok_enabled)
    
    # Arányok összegének ellenőrzése
    enabled_categories = []
    category_ratios = {}
    
    if gabonak_enabled:
        enabled_categories.append("Gabonafélék")
        category_ratios["Gabonafélék"] = gabonak_ratio
    
    if zoldsegek_enabled:
        enabled_categories.append("Zöldségfélék")
        category_ratios["Zöldségfélék"] = zoldsegek_ratio
    
    if gyumolcsok_enabled:
        enabled_categories.append("Gyümölcsök")
        category_ratios["Gyümölcsök"] = gyumolcsok_ratio
    
    total_category_ratio = sum(category_ratios.values())
    
    if not enabled_categories:
        st.error("Legalább egy termékkategóriát engedélyezni kell!")
    elif total_category_ratio != 100:
        st.warning(f"A kategória arányok összege {total_category_ratio}%, de 100%-nak kellene lennie!")
    
    # Statisztikai előnézet generálása
    preview_button = st.checkbox("Statisztikai előnézet generálása", value=True)
    
    # Generálás gomb
    if st.button("Adatok generálása", type="primary"):
        if not enabled_categories:
            st.error("Legalább egy termékkategóriát engedélyezni kell!")
            return
            
        if total_ratio != 100:
            st.error(f"A státusz arányok összege {total_ratio}%, de 100%-nak kellene lennie!")
            return
            
        if total_category_ratio != 100:
            st.error(f"A kategória arányok összege {total_category_ratio}%, de 100%-nak kellene lennie!")
            return
        
        # Adatok generálása
        with st.spinner("Adatok generálása folyamatban..."):
            # 1. Ha szükséges, termelő létrehozása
            if not use_existing:
                # Új termelő létrehozása
                token = get_auth_token()
                headers = {"Authorization": f"Bearer {token}"}
                
                user_data = {
                    "email": email,
                    "contact_name": contact_name,
                    "company_name": company_name,
                    "tax_id": tax_id,
                    "phone_number": phone_number,
                    "role": "termelő",
                    "is_active": True,
                    "is_verified": True,
                    "password": "Password123"  # Alapértelmezett jelszó
                }
                
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/register",
                        json=user_data,
                        headers=headers
                    )
                    
                    if response.status_code == 200 or response.status_code == 201:
                        # Sikeres létrehozás
                        producer_data = response.json()
                        producer_id = producer_data["id"]
                        st.success(f"Új termelő sikeresen létrehozva: {contact_name} ({email})")
                    else:
                        st.error(f"Hiba a termelő létrehozása során: {response.text}")
                        return
                except Exception as e:
                    st.error(f"Hiba a termelő létrehozása során: {str(e)}")
                    return
            
            # 2. Ajánlatok generálása a termelőhöz
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # Kategóriák, terméktípusok lekérdezése
            success, categories = products_api.get_product_categories()
            if not (success and categories):
                st.error("Nem sikerült lekérni a termékkategóriákat!")
                return
            
            # Minden kategóriához lekérjük a típusokat
            all_product_types = {}
            all_quality_grades = {}
            
            for category in categories:
                cat_name = category["name"]
                
                # Csak az engedélyezett kategóriákat vesszük figyelembe
                if cat_name not in enabled_categories:
                    continue
                    
                success, types = products_api.get_product_types(category_id=category["id"])
                if success and types:
                    all_product_types[cat_name] = types
                    
                    # Minden típushoz lekérjük a minőségi osztályokat
                    for product_type in types:
                        if product_type.get("has_quality_grades", False):
                            success, grades = products_api.get_quality_grades(product_type_id=product_type["id"])
                            if success and grades:
                                all_quality_grades[product_type["id"]] = grades
            
            # Ajánlatok generálása
            generated_offers = []
            
            # Státusz arányok
            status_weights = [created_ratio/100, confirmed_ratio/100, accepted_ratio/100, rejected_ratio/100, finalized_ratio/100]
            status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
            
            # Kategória arányok
            category_weights = [category_ratios[cat]/100 for cat in enabled_categories]
            
            # Dátumtartomány
            date_range_days = (end_date - start_date).days
            
            # Haladásjelző
            progress_bar = st.progress(0)
            
            for i in range(offer_count):
                # Haladás frissítése
                progress_bar.progress((i + 1) / offer_count)
                
                # 1. Kategória választása az arányok alapján
                selected_category = random.choices(enabled_categories, weights=category_weights, k=1)[0]
                
                # 2. Terméktípus választása a kategórián belül
                available_types = all_product_types.get(selected_category, [])
                if not available_types:
                    continue
                
                selected_type = random.choice(available_types)
                
                # 3. Minőségi osztály választása, ha van
                selected_grade_id = None
                if selected_type.get("has_quality_grades", False):
                    available_grades = all_quality_grades.get(selected_type["id"], [])
                    if available_grades:
                        selected_grade = random.choice(available_grades)
                        selected_grade_id = selected_grade["id"]
                
                # 4. Szállítási dátum és mennyiség generálása
                # Véletlenszerű dátum a tartományon belül, de figyelembe véve a szezonalitást is
                termek_adatok = None
                for termek_nev in MEZOGAZDASAGI_TERMEKEK.get(selected_category, {}).keys():
                    if termek_nev.lower() in selected_type["name"].lower():
                        termek_adatok = MEZOGAZDASAGI_TERMEKEK[selected_category][termek_nev]
                        break
                
                if not termek_adatok:
                    # Ha nincs pontos egyezés, használjunk alapértelmezett értékeket
                    delivery_date = start_date + timedelta(days=random.randint(0, date_range_days))
                    quantity_in_kg = random.randint(100, 1000)
                    price = random.randint(100, 2000)  # Alap ár
                else:
                    # Realisztikus adatok generálása a terméktípushoz
                    realistic_data = generate_realis_ajanlat(
                        list(MEZOGAZDASAGI_TERMEKEK[selected_category].keys())[0],  # Első termék a kategóriából
                        selected_category,
                        start_date,
                        end_date
                    )
                    
                    delivery_date = datetime.fromisoformat(realistic_data["delivery_date"]).date()
                    quantity_in_kg = realistic_data["quantity_in_kg"]
                    price = realistic_data["confirmed_price"]
                
                # 5. Státusz választása az arányok alapján
                status = random.choices(status_options, weights=status_weights, k=1)[0]
                
                # 6. Ajánlat létrehozása
                offer_data = {
                    "user_id": producer_id,
                    "product_type_id": selected_type["id"],
                    "quality_grade_id": selected_grade_id,
                    "quantity_in_kg": quantity_in_kg,
                    "delivery_date": delivery_date.isoformat(),
                    "note": f"Ajánlat: {selected_type['name']} - {delivery_date.isoformat()}",
                    "status": status
                }
                
                try:
                    # Az ajánlat létrehozása és státuszának beállítása
                    success, result = create_offer_for_user(offer_data, status)
                    
                    if success:
                        # Lássuk el további adatokkal az eredményt a későbbi elemzéshez
                        result["category"] = selected_category
                        result["product_type_name"] = selected_type["name"]
                        result["grade_name"] = ""
                        
                        if selected_grade_id:
                            grade = next((g for g in all_quality_grades.get(selected_type["id"], []) if g["id"] == selected_grade_id), None)
                            if grade:
                                result["grade_name"] = grade["name"]
                                
                        generated_offers.append(result)
                    else:
                        st.warning(f"Hiba az ajánlat létrehozásakor: {result}")
                except Exception as e:
                    st.warning(f"Hiba az ajánlat létrehozásakor: {str(e)}")
            
            # Sikeres generálás
            progress_bar.empty()
            st.success(f"Sikeresen létrehozva {len(generated_offers)} ajánlat a kiválasztott termelőhöz!")
            
            # Statisztikai elemzés és dashboard, ha kérték
            if preview_button and generated_offers:
                st.header("Generált adatok statisztikai elemzése")
                
                # Dataframe létrehozása az ajánlatokból
                offer_data = []
                for offer in generated_offers:
                    delivery_date = datetime.fromisoformat(offer["delivery_date"].replace("Z", "+00:00")).date() if isinstance(offer["delivery_date"], str) else offer["delivery_date"]
                    
                    offer_data.append({
                        "id": offer["id"],
                        "product_type": offer["product_type_name"],
                        "category": offer["category"],
                        "quality_grade": offer["grade_name"],
                        "quantity_kg": offer["quantity_in_kg"],
                        "delivery_date": delivery_date,
                        "status": offer["status"],
                        "confirmed_price": offer.get("confirmed_price", 0)
                    })
                
                df = pd.DataFrame(offer_data)
                
                # Dátum oszlop átalakítása
                df["year"] = df["delivery_date"].apply(lambda x: x.year)
                df["month"] = df["delivery_date"].apply(lambda x: x.month)
                df["month_name"] = df["delivery_date"].apply(lambda x: calendar.month_name[x.month])
                
                # 1. Státuszok eloszlása
                st.subheader("Ajánlatok státuszának eloszlása")
                status_counts = df["status"].value_counts().reset_index()
                status_counts.columns = ["Státusz", "Darab"]
                
                # Státusz nevek magyarra fordítása
                status_counts["Státusz"] = status_counts["Státusz"].map({
                    "CREATED": "Létrehozva",
                    "CONFIRMED_BY_COMPANY": "Cég által visszaigazolva",
                    "ACCEPTED_BY_USER": "Termelő által elfogadva",
                    "REJECTED_BY_USER": "Termelő által elutasítva",
                    "FINALIZED": "Véglegesítve"
                })
                
                fig = px.pie(status_counts, values="Darab", names="Státusz", title="Ajánlatok státuszának eloszlása")
                st.plotly_chart(fig, use_container_width=True)
                
                # 2. Kategória eloszlás
                st.subheader("Termékkategóriák eloszlása")
                col1, col2 = st.columns(2)
                
                with col1:
                    category_counts = df["category"].value_counts().reset_index()
                    category_counts.columns = ["Kategória", "Darab"]
                    
                    fig = px.pie(category_counts, values="Darab", names="Kategória", title="Termékkategóriák eloszlása")
                    st.plotly_chart(fig, use_container_width=True)
                    
                with col2:
                    # Top 10 terméktípus
                    product_counts = df["product_type"].value_counts().reset_index()
                    product_counts.columns = ["Terméktípus", "Darab"]
                    product_counts = product_counts.head(10)
                    
                    fig = px.bar(product_counts, x="Terméktípus", y="Darab", title="Top 10 terméktípus")
                    st.plotly_chart(fig, use_container_width=True)
                
                # 3. Időbeli eloszlás
                st.subheader("Ajánlatok időbeli eloszlása")
                
                # Havi eloszlás
                df_monthly = df.groupby(["year", "month", "month_name"]).size().reset_index(name="count")
                df_monthly["date"] = df_monthly.apply(lambda row: datetime(int(row["year"]), int(row["month"]), 1), axis=1)
                df_monthly = df_monthly.sort_values("date")
                
                fig = px.line(df_monthly, x="date", y="count", title="Ajánlatok havi eloszlása")
                st.plotly_chart(fig, use_container_width=True)
                
                # 4. Mennyiségek és árak elemzése
                st.subheader("Mennyiségek és árak elemzése")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    # Doboz diagram a kategóriánkénti mennyiségekről
                    fig = px.box(df, x="category", y="quantity_kg", title="Kategóriánkénti mennyiségek (kg)")
                    st.plotly_chart(fig, use_container_width=True)
                    
                with col2:
                    # Ha van confirmed_price, akkor árak elemzése
                    if "confirmed_price" in df.columns:
                        df_with_price = df[df["confirmed_price"] > 0]
                        if not df_with_price.empty:
                            fig = px.box(df_with_price, x="category", y="confirmed_price", title="Kategóriánkénti árak (Ft)")
                            st.plotly_chart(fig, use_container_width=True)
                
                # 5. Statisztikai táblázatok
                st.subheader("Statisztikai összesítés")
                
                # Mennyiség és ár statisztikák kategóriánként
                agg_functions = {
                    "quantity_kg": ["count", "sum", "mean", "min", "max"],
                    "confirmed_price": ["mean", "min", "max"]
                }
                
                category_stats = df.groupby("category").agg(agg_functions)
                category_stats.columns = ["_".join(col).strip() for col in category_stats.columns.values]
                category_stats = category_stats.reset_index()
                
                # Oszlopnevek szépítése
                category_stats = category_stats.rename(columns={
                    "quantity_kg_count": "Ajánlatok száma",
                    "quantity_kg_sum": "Összes mennyiség (kg)",
                    "quantity_kg_mean": "Átlagos mennyiség (kg)",
                    "quantity_kg_min": "Minimum mennyiség (kg)",
                    "quantity_kg_max": "Maximum mennyiség (kg)",
                    "confirmed_price_mean": "Átlagos ár (Ft)",
                    "confirmed_price_min": "Minimum ár (Ft)",
                    "confirmed_price_max": "Maximum ár (Ft)"
                })
                
                st.write("#### Statisztikák termékkategóriánként")
                st.dataframe(category_stats.style.format({
                    "Átlagos mennyiség (kg)": "{:.2f}",
                    "Átlagos ár (Ft)": "{:.2f}",
                }), use_container_width=True)
                
            # Nyers adatok megjelenítése
            with st.expander("Nyers generált adatok"):
                st.json([offer for offer in generated_offers[:10]])  # Csak az első 10 ajánlatot mutatjuk

def show_seasonal_scenario():
    """
    Szezonális ajánlatok szimulálása különböző termelőkkel.
    Ez a szcenárió több termelőt generál, akik az év különböző időszakaiban
    jellemző terményeket kínálnak, figyelembe véve a szezonalitást.
    """
    st.subheader("Szezonális ajánlatok szimulálása")
    
    st.markdown("""
    Ez a szcenárió több termelőt hoz létre, akik az év különböző időszakaiban 
    jellemző terményeket kínálnak, figyelembe véve a mezőgazdasági szezonalitást.
    
    A generált adatok jól mutatják:
    - A termékek szezonális elérhetőségét
    - Az árak szezonális ingadozását
    - A különböző termelők és régiók specializációját
    """)
    
    # Beállítások
    col1, col2 = st.columns(2)
    
    with col1:
        producer_count = st.slider("Termelők száma:", min_value=3, max_value=20, value=8)
        start_year = st.number_input("Kezdő év:", min_value=datetime.now().year-2, max_value=datetime.now().year, value=datetime.now().year-1)
        end_year = st.number_input("Befejező év:", min_value=start_year, max_value=datetime.now().year+1, value=datetime.now().year)
    
    with col2:
        offers_per_producer = st.slider("Ajánlatok száma termelőnként:", min_value=10, max_value=100, value=30)
        include_current_month = st.checkbox("Beleértve a jelenlegi hónapot is", value=True)
        show_statistics = st.checkbox("Statisztikák megjelenítése", value=True)
    
    # Jelenlegi hónap
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # Hónapok kiválasztása
    all_months = [i for i in range(1, 13)]
    if not include_current_month and current_year == end_year:
        available_months = [m for m in all_months if m != current_month]
    else:
        available_months = all_months
    
    # Jelenlegi szezonális termékek meghatározása
    seasonal_products = get_szezonalis_termekek(current_month)
    
    st.write("### Aktuális szezonális termékek")
    
    # Kategóriánként listázzuk a szezonális termékeket
    for kategoria, termekek in seasonal_products.items():
        with st.expander(f"{kategoria} ({len(termekek)} termék)"):
            for termek, adatok in termekek.items():
                st.write(f"- **{termek}**: {adatok['min_price']}-{adatok['max_price']} Ft/{adatok['unit']}")
    
    # Generálás gomb
    if st.button("Szezonális adatok generálása", type="primary"):
        with st.spinner("Szezonális adatok generálása folyamatban..."):
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # 1. Termelők generálása
            generated_producers = []
            
            for i in range(producer_count):
                # Termelői profil generálása
                producer_profile = generate_producer_profile()
                
                # Specializáció - véletlen kategóriát választunk, de a termelőknek legyenek preferenciái
                category_preference = random.choice(list(MEZOGAZDASAGI_TERMEKEK.keys()))
                
                user_data = {
                    "email": f"szezonalistermelő{i+1}@termelok.hu",
                    "contact_name": faker.name(),
                    "company_name": producer_profile["company_name"],
                    "tax_id": f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}",
                    "phone_number": generate_magyar_telefonszam(),
                    "role": "termelő",
                    "is_active": True,
                    "is_verified": True,
                    "password": "Password123"
                }
                
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/register",
                        json=user_data,
                        headers=headers
                    )
                    
                    if response.status_code == 200 or response.status_code == 201:
                        producer_data = response.json()
                        producer_data["category_preference"] = category_preference
                        producer_data["region"] = producer_profile["region"]
                        producer_data["county"] = producer_profile["county"]
                        generated_producers.append(producer_data)
                    else:
                        st.warning(f"Hiba a termelő létrehozásakor: {response.text}")
                except Exception as e:
                    st.warning(f"Hiba a termelő létrehozásakor: {str(e)}")
            
            # 2. Kategóriák, terméktípusok lekérdezése
            success, categories = products_api.get_product_categories()
            if not (success and categories):
                st.error("Nem sikerült lekérni a termékkategóriákat!")
                return
            
            # Minden kategóriához lekérjük a típusokat
            all_product_types = {}
            all_quality_grades = {}
            
            for category in categories:
                cat_name = category["name"]
                
                success, types = products_api.get_product_types(category_id=category["id"])
                if success and types:
                    all_product_types[cat_name] = types
                    
                    # Minden típushoz lekérjük a minőségi osztályokat
                    for product_type in types:
                        if product_type.get("has_quality_grades", False):
                            success, grades = products_api.get_quality_grades(product_type_id=product_type["id"])
                            if success and grades:
                                all_quality_grades[product_type["id"]] = grades
            
            # 3. Ajánlatok generálása minden termelőhöz
            all_generated_offers = []
            
            # Haladásjelző
            progress_bar = st.progress(0)
            progress_text = st.empty()
            
            total_offers = producer_count * offers_per_producer
            offer_count = 0
            
            for producer_index, producer in enumerate(generated_producers):
                producer_id = producer["id"]
                preferred_category = producer["category_preference"]
                
                # Kategórianév alapján keressük meg a kategória ID-t
                category_id = None
                for cat in categories:
                    if cat["name"] == preferred_category:
                        category_id = cat["id"]
                        break
                
                # Ha nem találtuk meg, válasszunk véletlenszerűen
                if not category_id:
                    category_id = random.choice(categories)["id"]
                
                # Az adott termelőhöz tartozó ajánlatok generálása
                for j in range(offers_per_producer):
                    offer_count += 1
                    progress_bar.progress(offer_count / total_offers)
                    progress_text.text(f"Generálás folyamatban: {offer_count}/{total_offers} ajánlat")
                    
                    # 1. Válasszunk véletlenszerű hónapot és évet
                    random_year = random.randint(start_year, end_year)
                    random_month = random.choice(available_months)
                    
                    # Ha ez a jelenlegi év és hónap, és nem akarjuk belevenni, generáljunk újat
                    if not include_current_month and random_year == current_year and random_month == current_month:
                        random_month = random.choice([m for m in available_months if m != current_month])
                    
                    # 2. Nézzük meg, milyen termékek szezonálisak ebben a hónapban
                    seasonal_products_for_month = get_szezonalis_termekek(random_month)
                    
                    # 3. Válasszunk terméket a preferált kategóriából, ha van szezonális
                    product_type = None
                    selected_category = None
                    
                    # Először próbáljuk a preferált kategóriából
                    if preferred_category in seasonal_products_for_month:
                        selected_category = preferred_category
                    else:
                        # Ha nincs a preferáltban szezonális, válasszunk másik kategóriát
                        available_categories = list(seasonal_products_for_month.keys())
                        if available_categories:
                            selected_category = random.choice(available_categories)
                    
                    # Ha még mindig nincs kategória, használjuk a preferáltat akkor is
                    if not selected_category:
                        selected_category = preferred_category
                    
                    # Keressünk egy terméktípust ami megfelel a kategóriának
                    if selected_category in all_product_types:
                        available_types = all_product_types[selected_category]
                        if available_types:
                            product_type = random.choice(available_types)
                    
                    # Ha nincs megfelelő típus, válasszunk véletlenszerűen
                    if not product_type and all_product_types:
                        random_category = random.choice(list(all_product_types.keys()))
                        if all_product_types[random_category]:
                            product_type = random.choice(all_product_types[random_category])
                            selected_category = random_category
                    
                    if not product_type:
                        st.warning(f"Nem található terméktípus a generáláshoz!")
                        continue
                    
                    # 4. Minőségi osztály választása, ha van
                    selected_grade_id = None
                    if product_type.get("has_quality_grades", False):
                        available_grades = all_quality_grades.get(product_type["id"], [])
                        if available_grades:
                            selected_grade = random.choice(available_grades)
                            selected_grade_id = selected_grade["id"]
                    
                    # 5. Generáljunk realisztikus adatokat az ajánlathoz
                    # Először keressünk egy hasonló terméket a MEZOGAZDASAGI_TERMEKEK-ből
                    termek_nev = None
                    for t_nev in MEZOGAZDASAGI_TERMEKEK.get(selected_category, {}).keys():
                        if t_nev.lower() in product_type["name"].lower():
                            termek_nev = t_nev
                            break
                    
                    # Ha nem találtunk pontos egyezést, használjuk az első terméket a kategóriából
                    if not termek_nev and selected_category in MEZOGAZDASAGI_TERMEKEK:
                        termek_nev = list(MEZOGAZDASAGI_TERMEKEK[selected_category].keys())[0]
                    
                    # Ha még mindig nincs termék név, ugorjunk a következő ajánlatra
                    if not termek_nev:
                        continue
                    
                    # Generáljunk szállítási dátumot a választott hónapban
                    last_day = calendar.monthrange(random_year, random_month)[1]
                    delivery_date = datetime(random_year, random_month, random.randint(1, last_day)).date()
                    
                    # Generáljunk realisztikus mennyiséget és árat
                    if selected_category in MEZOGAZDASAGI_TERMEKEK and termek_nev in MEZOGAZDASAGI_TERMEKEK[selected_category]:
                        termek_adatok = MEZOGAZDASAGI_TERMEKEK[selected_category][termek_nev]
                        
                        # Egység alapján
                        if termek_adatok["unit"] == "tonna":
                            quantity = round(random.uniform(1, 25), 2)  # 1-25 tonna
                            quantity_in_kg = quantity * 1000  # konvertálás kg-ra
                        else:  # kg
                            quantity_in_kg = round(random.uniform(50, 2000), 2)  # 50-2000 kg
                        
                        # Ár - szezonon belül/kívül eltérő
                        szezon = random_month in termek_adatok["szezon"]
                        if szezon:
                            # Szezonban olcsóbb (nagy kínálat)
                            min_price_factor = 0.8
                            max_price_factor = 1.0
                        else:
                            # Szezonon kívül drágább (kis kínálat)
                            min_price_factor = 1.0
                            max_price_factor = 1.5
                        
                        price = round(random.uniform(
                            termek_adatok["min_price"] * min_price_factor,
                            termek_adatok["max_price"] * max_price_factor
                        ))
                    else:
                        # Alapértelmezett értékek, ha nincs pontos termék adat
                        quantity_in_kg = round(random.uniform(100, 1000), 2)
                        price = round(random.uniform(200, 2000))
                    
                    # 6. Generáljunk realisztikus státuszt
                    # Régebbi ajánlatok nagyobb valószínűséggel már lezárt státuszúak
                    current_date = datetime.now().date()
                    days_diff = (current_date - delivery_date).days
                    
                    if days_diff > 30:  # Régi ajánlat
                        status_weights = [0.05, 0.10, 0.20, 0.15, 0.50]  # 50% esély a véglegesített státuszra
                    elif days_diff > 0:  # Múltbeli, de közelebbi ajánlat
                        status_weights = [0.10, 0.15, 0.30, 0.20, 0.25]
                    else:  # Jövőbeli ajánlat
                        status_weights = [0.40, 0.30, 0.20, 0.05, 0.05]
                    
                    status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
                    status = random.choices(status_options, weights=status_weights, k=1)[0]
                    
                    # 7. Ajánlat létrehozása
                    offer_data = {
                        "user_id": producer_id,
                        "product_type_id": product_type["id"],
                        "quality_grade_id": selected_grade_id,
                        "quantity_in_kg": quantity_in_kg,
                        "delivery_date": delivery_date.isoformat(),
                        "note": f"Ajánlat: {product_type['name']} ({delivery_date.strftime('%Y-%m')})",
                        "status": "CREATED"  # Kezdetben mindig CREATED
                    }
                    
                    try:
                        # Az ajánlat létrehozása és státuszának beállítása
                        success, result = create_offer_for_user(offer_data, status)
                        
                        if success:
                            # Lássuk el további adatokkal az eredményt a későbbi elemzéshez
                            result["category"] = selected_category
                            result["product_type_name"] = product_type["name"]
                            result["grade_name"] = ""
                            result["producer_region"] = producer["region"]
                            result["producer_county"] = producer["county"]
                            result["producer_name"] = producer["contact_name"]
                            result["month"] = random_month
                            result["year"] = random_year
                            result["is_seasonal"] = random_month in MEZOGAZDASAGI_TERMEKEK.get(selected_category, {}).get(termek_nev, {}).get("szezon", [])
                            
                            if selected_grade_id:
                                grade = next((g for g in all_quality_grades.get(product_type["id"], []) if g["id"] == selected_grade_id), None)
                                if grade:
                                    result["grade_name"] = grade["name"]
                                    
                            all_generated_offers.append(result)
                        else:
                            st.warning(f"Hiba az ajánlat létrehozásakor: {result}")
                    except Exception as e:
                        st.warning(f"Hiba az ajánlat létrehozásakor: {str(e)}")
            
            # Generálás vége
            progress_bar.empty()
            progress_text.empty()
            
            if all_generated_offers:
                st.success(f"Sikeresen létrehozva {len(all_generated_offers)} szezonális ajánlat {len(generated_producers)} termelőhöz!")
                
                # Statisztikai elemzés, ha kérték
                if show_statistics and all_generated_offers:
                    st.header("Szezonális adatok elemzése")
                    
                    # Dataframe létrehozása
                    offer_data = []
                    for offer in all_generated_offers:
                        delivery_date = datetime.fromisoformat(offer["delivery_date"].replace("Z", "+00:00")).date() if isinstance(offer["delivery_date"], str) else offer["delivery_date"]
                        
                        offer_data.append({
                            "id": offer["id"],
                            "producer_name": offer["producer_name"],
                            "producer_region": offer["producer_region"],
                            "producer_county": offer["producer_county"],
                            "product_type": offer["product_type_name"],
                            "category": offer["category"],
                            "quality_grade": offer["grade_name"],
                            "quantity_kg": offer["quantity_in_kg"],
                            "delivery_date": delivery_date,
                            "month": offer["month"],
                            "year": offer["year"],
                            "status": offer["status"],
                            "confirmed_price": offer.get("confirmed_price", 0),
                            "is_seasonal": offer["is_seasonal"]
                        })
                    
                    df = pd.DataFrame(offer_data)
                    
                    # 1. Szezonális vs. nem szezonális ajánlatok
                    st.subheader("Szezonális vs. nem szezonális ajánlatok")
                    
                    seasonal_counts = df["is_seasonal"].value_counts().reset_index()
                    seasonal_counts.columns = ["Szezonális", "Darab"]
                    seasonal_counts["Szezonális"] = seasonal_counts["Szezonális"].map({True: "Szezonális", False: "Nem szezonális"})
                    
                    fig = px.pie(seasonal_counts, values="Darab", names="Szezonális", title="Szezonális és nem szezonális ajánlatok aránya")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 2. Havi eloszlás
                    st.subheader("Ajánlatok havi eloszlása")
                    
                    # Hónapok nevei magyarul
                    month_names = {
                        1: "Január", 2: "Február", 3: "Március", 4: "Április", 5: "Május", 6: "Június",
                        7: "Július", 8: "Augusztus", 9: "Szeptember", 10: "Október", 11: "November", 12: "December"
                    }
                    df["month_name"] = df["month"].map(month_names)
                    
                    # Havi eloszlás kategóriánként
                    monthly_category = df.groupby(["month", "month_name", "category"]).size().reset_index(name="count")
                    monthly_category = monthly_category.sort_values("month")
                    
                    fig = px.bar(monthly_category, x="month_name", y="count", color="category",
                                title="Ajánlatok havi eloszlása termékkategóriánként",
                                barmode="group")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 3. Árak havi változása
                    st.subheader("Árak szezonális változása")
                    
                    # Csak azok az ajánlatok, ahol van ár
                    df_with_price = df[df["confirmed_price"] > 0]
                    
                    if not df_with_price.empty:
                        # Átlagos árak havi bontásban kategóriánként
                        monthly_prices = df_with_price.groupby(["month", "month_name", "category"])["confirmed_price"].mean().reset_index()
                        monthly_prices = monthly_prices.sort_values("month")
                        
                        fig = px.line(monthly_prices, x="month_name", y="confirmed_price", color="category",
                                    title="Átlagos árak havi változása termékkategóriánként",
                                    labels={"confirmed_price": "Átlagár (Ft)"})
                        st.plotly_chart(fig, use_container_width=True)
                    
                    # 4. Regionális eloszlás
                    st.subheader("Regionális eloszlás")
                    
                    # Ajánlatok száma régiónként és kategóriánként
                    region_category = df.groupby(["producer_region", "category"]).size().reset_index(name="count")
                    
                    fig = px.bar(region_category, x="producer_region", y="count", color="category",
                                title="Ajánlatok száma régiónként és termékkategóriánként",
                                barmode="group")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 5. Termelőnkénti összesítés
                    st.subheader("Termelőnkénti összesítés")
                    
                    producer_stats = df.groupby("producer_name").agg({
                        "quantity_kg": ["count", "sum", "mean"],
                        "confirmed_price": ["mean"]
                    }).reset_index()
                    producer_stats.columns = ["_".join(col).strip() if col[1] else col[0] for col in producer_stats.columns.values]
                    
                    # Oszlopnevek szépítése
                    producer_stats = producer_stats.rename(columns={
                        "producer_name": "Termelő",
                        "quantity_kg_count": "Ajánlatok száma",
                        "quantity_kg_sum": "Összes mennyiség (kg)",
                        "quantity_kg_mean": "Átlagos mennyiség (kg)",
                        "confirmed_price_mean": "Átlagos ár (Ft)"
                    })
                    
                    st.dataframe(producer_stats.style.format({
                        "Átlagos mennyiség (kg)": "{:.2f}",
                        "Átlagos ár (Ft)": "{:.2f}",
                    }), use_container_width=True)
                
                # Nyers adatok megjelenítése
                with st.expander("Generált adatok mintája"):
                    st.json([offer for offer in all_generated_offers[:5]])  # Csak az első 5 ajánlatot mutatjuk
            else:
                st.warning("Nem sikerült ajánlatokat generálni!")

def show_category_scenario():
    """
    Kategória-specifikus termelői profilok generálása.
    Ez a szcenárió különböző termékkategóriákra szakosodott termelőket generál,
    akik csak a specializációjuknak megfelelő terméktípusokkal foglalkoznak.
    """
    st.subheader("Kategória-specifikus termelői profilok")
    
    st.markdown("""
    Ez a szcenárió különböző termékkategóriákra szakosodott termelőket hoz létre,
    akik csak a specializációjuknak megfelelő terméktípusokat kínálják.
    
    Hasznos olyan statisztikák és kimutatások készítéséhez, amelyek a különböző 
    termékkategóriák termelőit és ajánlatait hasonlítják össze.
    """)
    
    # Kategóriák lekérdezése
    success, categories = products_api.get_product_categories()
    if not (success and categories):
        st.error("Nem sikerült lekérni a termékkategóriákat!")
        return
    
    category_options = [(c["id"], c["name"]) for c in categories]
    
    # Beállítások
    col1, col2 = st.columns(2)
    
    with col1:
        # Kategóriánként hány termelő
        st.write("#### Termelők száma kategóriánként")
        
        producers_per_category = {}
        for cat_id, cat_name in category_options:
            producers_per_category[cat_id] = st.number_input(
                f"{cat_name} kategória termelőinek száma:", 
                min_value=1, 
                max_value=10, 
                value=3
            )
            
        total_producers = sum(producers_per_category.values())
        
        # Ajánlatok száma termelőnként
        min_offers = st.slider("Minimum ajánlatok száma termelőnként:", min_value=5, max_value=50, value=10)
        max_offers = st.slider("Maximum ajánlatok száma termelőnként:", min_value=min_offers, max_value=100, value=min_offers+20)
    
    with col2:
        # Dátum beállítások
        st.write("#### Időtartomány")
        
        date_range = st.date_input(
            "Ajánlatok időtartománya:",
            value=(datetime.now().date() - timedelta(days=90), datetime.now().date() + timedelta(days=90)),
            min_value=datetime.now().date() - timedelta(days=365),
            max_value=datetime.now().date() + timedelta(days=365)
        )
        
        if isinstance(date_range, tuple) and len(date_range) == 2:
            start_date, end_date = date_range
        else:
            start_date = datetime.now().date() - timedelta(days=90)
            end_date = datetime.now().date() + timedelta(days=90)
        
        # Statisztikák megjelenítése
        show_stats = st.checkbox("Statisztikák megjelenítése", value=True)
        
        st.write("#### Egyedi specialitás szintek")
        specialization_level = st.radio(
            "Specializáció szintje:",
            ["Alacsony (vegyes termékek)", "Közepes (főleg egy kategória)", "Magas (kizárólag egy kategória)"],
            index=1
        )
        
        
        # Specializáció szintjének megfelelő értékek
        if specialization_level == "Alacsony (vegyes termékek)":
            specialization_ratio = 0.5  # 50% eséllyel a fő kategóriából
        elif specialization_level == "Közepes (főleg egy kategória)":
            specialization_ratio = 0.8  # 80% eséllyel a fő kategóriából
        else:
            specialization_ratio = 1.0  # 100% eséllyel a fő kategóriából
    
    # Generálás gomb
    if st.button("Kategória-specifikus adatok generálása", type="primary"):
        with st.spinner("Adatok generálása folyamatban..."):
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # 1. Minden típushoz lekérjük a minőségi osztályokat
            all_product_types = {}
            all_quality_grades = {}
            
            for category in categories:
                cat_id = category["id"]
                cat_name = category["name"]
                
                success, types = products_api.get_product_types(category_id=cat_id)
                if success and types:
                    all_product_types[cat_id] = types
                    
                    # Minden típushoz lekérjük a minőségi osztályokat
                    for product_type in types:
                        if product_type.get("has_quality_grades", False):
                            success, grades = products_api.get_quality_grades(product_type_id=product_type["id"])
                            if success and grades:
                                all_quality_grades[product_type["id"]] = grades
            
            # 2. Termelők generálása kategóriánként
            all_producers = []
            all_offers = []
            
            # Haladásjelző
            progress_bar = st.progress(0)
            progress_text = st.empty()
            current_progress = 0
            total_progress = total_producers
            
            for cat_id, num_producers in producers_per_category.items():
                cat_name = next((c[1] for c in category_options if c[0] == cat_id), "Ismeretlen")
                
                # Kategóriához tartozó terméktípusok
                cat_product_types = all_product_types.get(cat_id, [])
                if not cat_product_types:
                    st.warning(f"Nincs terméktípus a(z) {cat_name} kategóriához!")
                    continue
                
                # Termelők generálása az adott kategóriához
                for i in range(num_producers):
                    current_progress += 1
                    progress_bar.progress(current_progress / total_progress)
                    progress_text.text(f"Termelők generálása: {current_progress}/{total_progress}")
                    
                    # Termelői profil generálása
                    producer_profile = generate_producer_profile()
                    
                    user_data = {
                        "email": f"{cat_name.lower().replace(' ', '')}_{i+1}@termelok.hu",
                        "contact_name": faker.name(),
                        "company_name": producer_profile["company_name"],
                        "tax_id": f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}",
                        "phone_number": generate_magyar_telefonszam(),
                        "role": "termelő",
                        "is_active": True,
                        "is_verified": True,
                        "password": "Password123"
                    }
                    
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/auth/register",
                            json=user_data,
                            headers=headers
                        )
                        
                        if response.status_code == 200 or response.status_code == 201:
                            producer_data = response.json()
                            producer_data["specialized_category_id"] = cat_id
                            producer_data["specialized_category_name"] = cat_name
                            producer_data["region"] = producer_profile["region"]
                            producer_data["county"] = producer_profile["county"]
                            all_producers.append(producer_data)
                            
                            # 3. Ajánlatok generálása a termelőhöz
                            producer_id = producer_data["id"]
                            
                            # Véletlen számú ajánlat a megadott tartományon belül
                            num_offers = random.randint(min_offers, max_offers)
                            
                            for j in range(num_offers):
                                # Specializáció alapján választunk terméktípust
                                if random.random() < specialization_ratio:
                                    # A fő kategóriából választunk
                                    product_type = random.choice(cat_product_types)
                                    product_category_id = cat_id
                                    product_category_name = cat_name
                                else:
                                    # Véletlenszerű más kategóriából választunk
                                    other_cat_ids = [c[0] for c in category_options if c[0] != cat_id]
                                    if other_cat_ids:
                                        random_cat_id = random.choice(other_cat_ids)
                                        random_cat_name = next((c[1] for c in category_options if c[0] == random_cat_id), "Ismeretlen")
                                        
                                        if random_cat_id in all_product_types and all_product_types[random_cat_id]:
                                            product_type = random.choice(all_product_types[random_cat_id])
                                            product_category_id = random_cat_id
                                            product_category_name = random_cat_name
                                        else:
                                            # Ha nincs más kategória vagy nincs terméktípus, maradunk az eredetinél
                                            product_type = random.choice(cat_product_types)
                                            product_category_id = cat_id
                                            product_category_name = cat_name
                                    else:
                                        # Ha nincs más kategória, maradunk az eredetinél
                                        product_type = random.choice(cat_product_types)
                                        product_category_id = cat_id
                                        product_category_name = cat_name
                                
                                # Minőségi osztály választása, ha van
                                selected_grade_id = None
                                if product_type.get("has_quality_grades", False):
                                    available_grades = all_quality_grades.get(product_type["id"], [])
                                    if available_grades:
                                        selected_grade = random.choice(available_grades)
                                        selected_grade_id = selected_grade["id"]
                                
                                # Generáljunk szállítási dátumot a megadott tartományon belül
                                date_range_days = (end_date - start_date).days
                                if date_range_days > 0:
                                    delivery_date = start_date + timedelta(days=random.randint(0, date_range_days))
                                else:
                                    delivery_date = start_date
                                
                                # Generáljunk realisztikus mennyiséget és árat
                                # Keressünk hasonló terméket a MEZOGAZDASAGI_TERMEKEK-ből
                                realistic_data = None
                                
                                for kategoria, termekek in MEZOGAZDASAGI_TERMEKEK.items():
                                    if kategoria != product_category_name:
                                        continue
                                        
                                    for termek_nev, adatok in termekek.items():
                                        if termek_nev.lower() in product_type["name"].lower():
                                            try:
                                                realistic_data = generate_realis_ajanlat(
                                                    termek_nev, 
                                                    kategoria, 
                                                    start_date, 
                                                    end_date
                                                )
                                                break
                                            except:
                                                pass
                                    
                                    if realistic_data:
                                        break
                                
                                # Ha nem találtunk realisztikus adatokat, generáljunk alapértelmezetteket
                                if realistic_data:
                                    quantity_in_kg = realistic_data["quantity_in_kg"]
                                    price = realistic_data["confirmed_price"]
                                else:
                                    if product_category_name == "Gabonafélék":
                                        quantity_in_kg = random.randint(1000, 25000)  # 1-25 tonna
                                        price = random.randint(50000, 90000)  # Ft/tonna
                                    else:
                                        quantity_in_kg = random.randint(50, 2000)  # 50-2000 kg
                                        price = random.randint(200, 2000)  # Ft/kg
                                
                                # Státusz generálása a dátum alapján
                                current_date = datetime.now().date()
                                days_diff = (current_date - delivery_date).days
                                
                                if days_diff > 30:  # Régi ajánlat
                                    status_weights = [0.05, 0.10, 0.20, 0.15, 0.50]  # 50% esély a véglegesített státuszra
                                elif days_diff > 0:  # Múltbeli, de közelebbi ajánlat
                                    status_weights = [0.10, 0.15, 0.30, 0.20, 0.25]
                                else:  # Jövőbeli ajánlat
                                    status_weights = [0.40, 0.30, 0.20, 0.05, 0.05]
                                
                                status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
                                status = random.choices(status_options, weights=status_weights, k=1)[0]
                                
                                # Ajánlat létrehozása
                                offer_data = {
                                    "user_id": producer_id,
                                    "product_type_id": product_type["id"],
                                    "quality_grade_id": selected_grade_id,
                                    "quantity_in_kg": quantity_in_kg,
                                    "delivery_date": delivery_date.isoformat(),
                                    "note": f"Ajánlat: {product_type['name']} - {delivery_date.isoformat()}",
                                    "status": "CREATED"  # Kezdetben mindig CREATED
                                }
                                
                                try:
                                    # Az ajánlat létrehozása és státuszának beállítása
                                    success, result = create_offer_for_user(offer_data, status)
                                    
                                    if success:
                                        # További adatok az elemzéshez
                                        result["product_category_id"] = product_category_id
                                        result["product_category_name"] = product_category_name
                                        result["product_type_name"] = product_type["name"]
                                        result["producer_specialized_category"] = cat_name
                                        result["is_specialized_product"] = (product_category_id == cat_id)
                                        
                                        if selected_grade_id:
                                            grade = next((g for g in all_quality_grades.get(product_type["id"], []) if g["id"] == selected_grade_id), None)
                                            if grade:
                                                result["grade_name"] = grade["name"]
                                                
                                        all_offers.append(result)
                                    else:
                                        st.warning(f"Hiba az ajánlat létrehozásakor: {result}")
                                except Exception as e:
                                    st.warning(f"Hiba az ajánlat létrehozásakor: {str(e)}")
                                    
                        else:
                            st.warning(f"Hiba a termelő létrehozásakor: {response.text}")
                    except Exception as e:
                        st.warning(f"Hiba a termelő létrehozásakor: {str(e)}")
            
            # Generálás vége
            progress_bar.empty()
            progress_text.empty()
            
            if all_producers and all_offers:
                st.success(f"Sikeresen létrehozva {len(all_producers)} kategória-specifikus termelő és {len(all_offers)} ajánlat!")
                
                # Statisztikai elemzés
                if show_stats:
                    st.header("Kategória-specifikus adatok elemzése")
                    
                    # Dataframe létrehozása
                    offer_data = []
                    for offer in all_offers:
                        delivery_date = datetime.fromisoformat(offer["delivery_date"].replace("Z", "+00:00")).date() if isinstance(offer["delivery_date"], str) else offer["delivery_date"]
                        
                        offer_data.append({
                            "id": offer["id"],
                            "producer_specialized_category": offer["producer_specialized_category"],
                            "product_category": offer["product_category_name"],
                            "product_type": offer["product_type_name"],
                            "is_specialized_product": offer["is_specialized_product"],
                            "quantity_kg": offer["quantity_in_kg"],
                            "delivery_date": delivery_date,
                            "status": offer["status"],
                            "confirmed_price": offer.get("confirmed_price", 0)
                        })
                    
                    df = pd.DataFrame(offer_data)
                    
                    # 1. Specializált vs. nem specializált ajánlatok
                    st.subheader("Specializált vs. nem specializált ajánlatok")
                    
                    specialization_counts = df["is_specialized_product"].value_counts().reset_index()
                    specialization_counts.columns = ["Specializált", "Darab"]
                    specialization_counts["Specializált"] = specialization_counts["Specializált"].map(
                        {True: "Fő kategória termékei", False: "Egyéb kategória termékei"}
                    )
                    
                    fig = px.pie(specialization_counts, values="Darab", names="Specializált", 
                                title="Termelők fő kategóriájához tartozó és egyéb ajánlatok aránya")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 2. Kategóriánkénti összesítés
                    st.subheader("Kategóriánkénti összesítés")
                    
                    # Ajánlatok száma kategóriánként
                    category_counts = df.groupby("product_category").size().reset_index(name="count")
                    category_counts = category_counts.sort_values("count", ascending=False)
                    
                    fig = px.bar(category_counts, x="product_category", y="count", 
                                title="Ajánlatok száma termékkategóriánként",
                                labels={"product_category": "Termékkategória", "count": "Ajánlatok száma"})
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 3. Specializált termelők hatékonysága
                    st.subheader("Specializáció hatása a mennyiségre és árakra")
                    
                    # Átlagos mennyiség és ár specializáció szerint
                    specialization_stats = df.groupby("is_specialized_product").agg({
                        "quantity_kg": ["mean", "median"],
                        "confirmed_price": ["mean", "median"]
                    }).reset_index()
                    
                    specialization_stats.columns = ["_".join(col).strip() if col[1] else col[0] for col in specialization_stats.columns.values]
                    
                    # Specializáció értékek átnevezése
                    specialization_stats["is_specialized_product"] = specialization_stats["is_specialized_product"].map(
                        {True: "Fő kategória termékei", False: "Egyéb kategória termékei"}
                    )
                    
                    # Oszlopnevek szépítése
                    specialization_stats = specialization_stats.rename(columns={
                        "is_specialized_product": "Specializáció",
                        "quantity_kg_mean": "Átlagos mennyiség (kg)",
                        "quantity_kg_median": "Medián mennyiség (kg)",
                        "confirmed_price_mean": "Átlagos ár (Ft)",
                        "confirmed_price_median": "Medián ár (Ft)"
                    })
                    
                    st.dataframe(specialization_stats.style.format({
                        "Átlagos mennyiség (kg)": "{:.2f}",
                        "Medián mennyiség (kg)": "{:.2f}",
                        "Átlagos ár (Ft)": "{:.2f}",
                        "Medián ár (Ft)": "{:.2f}"
                    }), use_container_width=True)
                    
                    # 4. Top terméktípusok kategóriánként
                    st.subheader("Top terméktípusok kategóriánként")
                    
                    # Kategóriánként választunk néhány terméktípust
                    for category in df["product_category"].unique():
                        with st.expander(f"{category} kategória top terméktípusai"):
                            # Az adott kategória terméktípusai
                            cat_products = df[df["product_category"] == category]
                            
                            if not cat_products.empty:
                                # Top terméktípusok
                                top_products = cat_products["product_type"].value_counts().reset_index()
                                top_products.columns = ["Terméktípus", "Darab"]
                                top_products = top_products.head(5)
                                
                                fig = px.bar(top_products, x="Terméktípus", y="Darab", 
                                            title=f"Top 5 terméktípus a(z) {category} kategóriában",
                                            color="Terméktípus")
                                st.plotly_chart(fig, use_container_width=True)
                                
                                # Terméktípusonkénti mennyiség
                                product_quantity = cat_products.groupby("product_type")["quantity_kg"].sum().reset_index()
                                product_quantity = product_quantity.sort_values("quantity_kg", ascending=False).head(5)
                                
                                fig = px.bar(product_quantity, x="product_type", y="quantity_kg", 
                                            title=f"Top 5 terméktípus összes mennyisége (kg) a(z) {category} kategóriában",
                                            labels={"product_type": "Terméktípus", "quantity_kg": "Összes mennyiség (kg)"},
                                            color="product_type")
                                st.plotly_chart(fig, use_container_width=True)
                            else:
                                st.info(f"Nincs adat a(z) {category} kategóriához")
                
                # Generált adatok mintája
                with st.expander("Nyers generált adatok mintája"):
                    st.json([offer for offer in all_offers[:5]])  # Csak az első 5 ajánlatot mutatjuk
            else:
                st.warning("Nem sikerült létrehozni a kategória-specifikus termelőket és ajánlatokat!")

def show_regional_scenario():
    """
    Regionális termelők kínálatának szimulálása.
    Ez a szcenárió különböző régiókban tevékenykedő termelőket generál,
    és a régiókra jellemző mezőgazdasági termékeket kínál.
    """
    st.subheader("Regionális termelők kínálata")
    
    st.markdown("""
    Ez a szcenárió különböző magyarországi régiókban tevékenykedő termelőket generál,
    figyelembe véve a regionális mezőgazdasági sajátosságokat.
    
    Hasznos olyan elemzésekhez, amelyek a különböző régiók közötti termelési különbségeket,
    ajánlati mintákat és árkülönbségeket vizsgálják.
    """)
    
    # Regionális beállítások
    st.write("### Regionális beállítások")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("#### Termelők száma régiónként")
        
        # Régió választó
        region_counts = {}
        for region in MAGYAR_REGIOK.keys():
            region_counts[region] = st.slider(
                f"{region}:", 
                min_value=0, 
                max_value=10, 
                value=3 if region in ["Észak-Alföld", "Dél-Alföld", "Dél-Dunántúl"] else 2
            )
        
        total_producers = sum(region_counts.values())
        if total_producers == 0:
            st.error("Legalább egy régióhoz rendelj termelőket!")
    
    with col2:
        # Ajánlatok száma és időtartomány
        st.write("#### Ajánlatok beállításai")
        
        min_offers_per_producer = st.slider("Minimum ajánlatok száma termelőnként:", min_value=5, max_value=30, value=10)
        max_offers_per_producer = st.slider("Maximum ajánlatok száma termelőnként:", min_value=min_offers_per_producer, max_value=50, value=min_offers_per_producer + 10)
        
        # Időtartomány
        date_range = st.date_input(
            "Ajánlatok időtartománya:",
            value=(datetime.now().date() - timedelta(days=180), datetime.now().date() + timedelta(days=90)),
            min_value=datetime.now().date() - timedelta(days=365),
            max_value=datetime.now().date() + timedelta(days=365)
        )
        
        if isinstance(date_range, tuple) and len(date_range) == 2:
            start_date, end_date = date_range
        else:
            start_date = datetime.now().date() - timedelta(days=180)
            end_date = datetime.now().date() + timedelta(days=90)
    
    # Regionális termék megfeleltetés - minden régióhoz termék preferenciák
    st.write("### Regionális termék preferenciák")
    st.write("Állítsd be, mely régiókban mely terméktípusok a jellemzőek:")
    
    # Előre definiált regionális termék preferenciák
    default_regional_preferences = {
        "Észak-Magyarország": {
            "Gabonafélék": 0.3,
            "Zöldségfélék": 0.3,
            "Gyümölcsök": 0.4
        },
        "Észak-Alföld": {
            "Gabonafélék": 0.5,
            "Zöldségfélék": 0.4,
            "Gyümölcsök": 0.1
        },
        "Dél-Alföld": {
            "Gabonafélék": 0.6,
            "Zöldségfélék": 0.3,
            "Gyümölcsök": 0.1
        },
        "Közép-Magyarország": {
            "Gabonafélék": 0.2,
            "Zöldségfélék": 0.5,
            "Gyümölcsök": 0.3
        },
        "Közép-Dunántúl": {
            "Gabonafélék": 0.4,
            "Zöldségfélék": 0.3,
            "Gyümölcsök": 0.3
        },
        "Nyugat-Dunántúl": {
            "Gabonafélék": 0.4,
            "Zöldségfélék": 0.2,
            "Gyümölcsök": 0.4
        },
        "Dél-Dunántúl": {
            "Gabonafélék": 0.3,
            "Zöldségfélék": 0.3,
            "Gyümölcsök": 0.4
        }
    }
    
    # Régiónkénti beállítások megjelenítése expanderekben
    regional_preferences = {}
    
    for region, prefs in default_regional_preferences.items():
        # Csak azokhoz a régiókhoz, ahol van termelő
        if region_counts.get(region, 0) > 0:
            with st.expander(f"{region} termék preferenciák"):
                st.write(f"Állítsd be a(z) {region} régió terméktípus arányait:")
                
                # Az adott régió preferenciái
                region_prefs = {}
                total_pref = 0
                
                for category, default_value in prefs.items():
                    pref_value = st.slider(
                        f"{category} aránya (%):", 
                        min_value=0, 
                        max_value=100, 
                        value=int(default_value * 100)
                    )
                    region_prefs[category] = pref_value / 100
                    total_pref += pref_value
                
                # Ellenőrzés, hogy összesen 100%
                if abs(total_pref - 100) > 0.01:
                    st.warning(f"A preferenciák összege {total_pref}%, de 100%-nak kellene lennie!")
                
                regional_preferences[region] = region_prefs
    
    # Generálás gomb
    show_stats = st.checkbox("Statisztikák megjelenítése", value=True)
    
    if st.button("Regionális adatok generálása", type="primary"):
        if total_producers == 0:
            st.error("Legalább egy régióhoz rendelj termelőket!")
            return
            
        with st.spinner("Regionális adatok generálása folyamatban..."):
            token = get_auth_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            # 1. Kategóriák és terméktípusok lekérdezése
            success, categories = products_api.get_product_categories()
            if not (success and categories):
                st.error("Nem sikerült lekérni a termékkategóriákat!")
                return
            
            # Kategória nevek és ID-k megfeleltetése
            category_mapping = {c["name"]: c["id"] for c in categories}
            
            # Minden kategóriához lekérjük a típusokat
            all_product_types = {}
            all_quality_grades = {}
            
            for category in categories:
                cat_id = category["id"]
                cat_name = category["name"]
                
                success, types = products_api.get_product_types(category_id=cat_id)
                if success and types:
                    all_product_types[cat_name] = types
                    
                    # Minden típushoz lekérjük a minőségi osztályokat
                    for product_type in types:
                        if product_type.get("has_quality_grades", False):
                            success, grades = products_api.get_quality_grades(product_type_id=product_type["id"])
                            if success and grades:
                                all_quality_grades[product_type["id"]] = grades
            
            # 2. Termelők és ajánlatok generálása régiónként
            all_producers = []
            all_offers = []
            
            # Haladásjelző
            progress_bar = st.progress(0)
            progress_text = st.empty()
            current_progress = 0
            
            # Összes generálandó termelő és ajánlat
            total_progress = total_producers
            
            # Régiónként haladunk
            for region, producer_count in region_counts.items():
                if producer_count <= 0:
                    continue
                    
                # Az adott régió megyéi
                region_counties = MAGYAR_REGIOK[region]
                
                # Régió termékpreferenciái
                region_prefs = regional_preferences.get(region, default_regional_preferences[region])
                
                # Termelők generálása a régióhoz
                for i in range(producer_count):
                    current_progress += 1
                    progress_bar.progress(current_progress / total_progress)
                    progress_text.text(f"Termelők generálása: {current_progress}/{total_progress}")
                    
                    # Véletlenszerű megye kiválasztása a régióból
                    county = random.choice(region_counties)
                    
                    # Termelői profil generálása
                    producer_profile = generate_producer_profile()
                    
                    # Felhasználó létrehozása
                    user_data = {
                        "email": f"{region.lower().replace('-', '').replace(' ', '')}_{i+1}@termelok.hu",
                        "contact_name": faker.name(),
                        "company_name": producer_profile["company_name"],
                        "tax_id": f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}-{random.randint(10, 99)}",
                        "phone_number": generate_magyar_telefonszam(),
                        "role": "termelő",
                        "is_active": True,
                        "is_verified": True,
                        "password": "Password123"
                    }
                    
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/auth/register",
                            json=user_data,
                            headers=headers
                        )
                        
                        if response.status_code == 200 or response.status_code == 201:
                            producer_data = response.json()
                            producer_data["region"] = region
                            producer_data["county"] = county
                            all_producers.append(producer_data)
                            
                            # 3. Ajánlatok generálása a termelőhöz
                            producer_id = producer_data["id"]
                            
                            # Véletlen számú ajánlat
                            num_offers = random.randint(min_offers_per_producer, max_offers_per_producer)
                            
                            # Ajánlatok generálása
                            for j in range(num_offers):
                                # Válasszunk kategóriát a régió preferenciái alapján
                                category_weights = list(region_prefs.values())
                                category_names = list(region_prefs.keys())
                                
                                selected_category = random.choices(category_names, weights=category_weights, k=1)[0]
                                
                                # Válasszunk terméktípust a kategóriából
                                available_types = all_product_types.get(selected_category, [])
                                if not available_types:
                                    continue
                                    
                                product_type = random.choice(available_types)
                                
                                # Minőségi osztály választása, ha van
                                selected_grade_id = None
                                if product_type.get("has_quality_grades", False):
                                    available_grades = all_quality_grades.get(product_type["id"], [])
                                    if available_grades:
                                        selected_grade = random.choice(available_grades)
                                        selected_grade_id = selected_grade["id"]
                                
                                # Szállítási dátum generálása
                                date_range_days = (end_date - start_date).days
                                if date_range_days > 0:
                                    delivery_date = start_date + timedelta(days=random.randint(0, date_range_days))
                                else:
                                    delivery_date = start_date
                                
                                # Realisztikus adatok generálása
                                realistic_data = None
                                
                                # Keressünk a terméknévhez illő realisztikus adatot
                                for termek_nev in MEZOGAZDASAGI_TERMEKEK.get(selected_category, {}).keys():
                                    if termek_nev.lower() in product_type["name"].lower():
                                        try:
                                            realistic_data = generate_realis_ajanlat(
                                                termek_nev, 
                                                selected_category, 
                                                start_date, 
                                                end_date
                                            )
                                            break
                                        except:
                                            pass
                                
                                # Ha nem találtunk, próbáljunk alapértelmezett értékeket generálni
                                if not realistic_data and MEZOGAZDASAGI_TERMEKEK.get(selected_category):
                                    try:
                                        # Vegyük az első terméket a kategóriából
                                        first_product = list(MEZOGAZDASAGI_TERMEKEK[selected_category].keys())[0]
                                        realistic_data = generate_realis_ajanlat(
                                            first_product, 
                                            selected_category, 
                                            start_date, 
                                            end_date
                                        )
                                    except:
                                        pass
                                
                                # Ha még mindig nincs adat, akkor alapértelmezett értékeket használunk
                                if realistic_data:
                                    quantity_in_kg = realistic_data["quantity_in_kg"]
                                    price = realistic_data["confirmed_price"]
                                else:
                                    if selected_category == "Gabonafélék":
                                        quantity_in_kg = random.randint(1000, 25000)  # 1-25 tonna
                                        price = random.randint(50000, 90000)  # Ft/tonna
                                    else:
                                        quantity_in_kg = random.randint(50, 2000)  # 50-2000 kg
                                        price = random.randint(200, 2000)  # Ft/kg
                                
                                # Régiós árkülönbségek szimulálása - néhány régióban kicsit drágább/olcsóbb
                                region_price_factors = {
                                    "Közép-Magyarország": 1.15,  # drágább
                                    "Nyugat-Dunántúl": 1.1,
                                    "Közép-Dunántúl": 1.05,
                                    "Dél-Dunántúl": 0.95,
                                    "Észak-Magyarország": 0.9,
                                    "Észak-Alföld": 0.9,
                                    "Dél-Alföld": 0.95
                                }
                                
                                # Ár módosítása régió szerint
                                price = round(price * region_price_factors.get(region, 1.0))
                                
                                # Státusz generálása
                                current_date = datetime.now().date()
                                days_diff = (current_date - delivery_date).days
                                
                                if days_diff > 30:  # Régi ajánlat
                                    status_weights = [0.05, 0.10, 0.20, 0.15, 0.50]  # 50% esély a véglegesített státuszra
                                elif days_diff > 0:  # Múltbeli, de közelebbi ajánlat
                                    status_weights = [0.10, 0.15, 0.30, 0.20, 0.25]
                                else:  # Jövőbeli ajánlat
                                    status_weights = [0.40, 0.30, 0.20, 0.05, 0.05]
                                
                                status_options = ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
                                status = random.choices(status_options, weights=status_weights, k=1)[0]
                                
                                # Ajánlat létrehozása
                                offer_data = {
                                    "user_id": producer_id,
                                    "product_type_id": product_type["id"],
                                    "quality_grade_id": selected_grade_id,
                                    "quantity_in_kg": quantity_in_kg,
                                    "delivery_date": delivery_date.isoformat(),
                                    "note": f"Ajánlat: {product_type['name']} - {region} régió",
                                    "status": "CREATED"  # Kezdetben mindig CREATED
                                }
                                
                                try:
                                    # Az ajánlat létrehozása és státuszának beállítása
                                    success, result = create_offer_for_user(offer_data, status)
                                    
                                    if success:
                                        # További adatok az elemzéshez
                                        result["product_category"] = selected_category
                                        result["product_type_name"] = product_type["name"]
                                        result["producer_region"] = region
                                        result["producer_county"] = county
                                        result["producer_name"] = producer_data["contact_name"]
                                        
                                        if selected_grade_id:
                                            grade = next((g for g in all_quality_grades.get(product_type["id"], []) if g["id"] == selected_grade_id), None)
                                            if grade:
                                                result["grade_name"] = grade["name"]
                                                
                                        all_offers.append(result)
                                    else:
                                        st.warning(f"Hiba az ajánlat létrehozásakor: {result}")
                                except Exception as e:
                                    st.warning(f"Hiba az ajánlat létrehozásakor: {str(e)}")
                        else:
                            st.warning(f"Hiba a termelő létrehozásakor: {response.text}")
                    except Exception as e:
                        st.warning(f"Hiba a termelő létrehozásakor: {str(e)}")
            
            # Generálás vége
            progress_bar.empty()
            progress_text.empty()
            
            if all_producers and all_offers:
                st.success(f"Sikeresen létrehozva {len(all_producers)} termelő {len(region_counts)} régióban és {len(all_offers)} ajánlat!")
                
                # Statisztikai elemzés és vizualizáció
                if show_stats and all_offers:
                    st.header("Regionális adatok elemzése")
                    
                    # Dataframe létrehozása
                    offer_data = []
                    for offer in all_offers:
                        delivery_date = datetime.fromisoformat(offer["delivery_date"].replace("Z", "+00:00")).date() if isinstance(offer["delivery_date"], str) else offer["delivery_date"]
                        
                        offer_data.append({
                            "id": offer["id"],
                            "producer_name": offer["producer_name"],
                            "producer_region": offer["producer_region"],
                            "producer_county": offer["producer_county"],
                            "product_type": offer["product_type_name"],
                            "category": offer["product_category"],
                            "quantity_kg": offer["quantity_in_kg"],
                            "delivery_date": delivery_date,
                            "status": offer["status"],
                            "confirmed_price": offer.get("confirmed_price", 0)
                        })
                    
                    df = pd.DataFrame(offer_data)
                    
                    # 1. Régiónkénti ajánlatok száma
                    st.subheader("Régiónkénti ajánlatok száma")
                    
                    region_counts = df["producer_region"].value_counts().reset_index()
                    region_counts.columns = ["Régió", "Ajánlatok száma"]
                    
                    fig = px.bar(region_counts, x="Régió", y="Ajánlatok száma", 
                                title="Ajánlatok száma régiónként",
                                color="Régió")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 2. Régiónkénti termék kategória eloszlás
                    st.subheader("Régiónkénti termék kategória eloszlás")
                    
                    region_category = df.groupby(["producer_region", "category"]).size().reset_index(name="count")
                    
                    fig = px.bar(region_category, x="producer_region", y="count", color="category",
                                title="Termékkategóriák eloszlása régiónként",
                                labels={"producer_region": "Régió", "count": "Ajánlatok száma", "category": "Kategória"},
                                barmode="group")
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 3. Régiónkénti átlagárak
                    st.subheader("Régiónkénti átlagárak")
                    
                    # Csak az elfogadott áras ajánlatok
                    df_with_price = df[df["confirmed_price"] > 0]
                    
                    if not df_with_price.empty:
                        # Gabonafélék és egyéb termékek külön vizsgálata
                        grain_prices = df_with_price[df_with_price["category"] == "Gabonafélék"].groupby("producer_region")["confirmed_price"].mean().reset_index()
                        grain_prices["category"] = "Gabonafélék"
                        
                        other_prices = df_with_price[df_with_price["category"] != "Gabonafélék"].groupby("producer_region")["confirmed_price"].mean().reset_index()
                        other_prices["category"] = "Egyéb termékek"
                        
                        # Összevonás
                        price_data = pd.concat([grain_prices, other_prices])
                        
                        fig = px.bar(price_data, x="producer_region", y="confirmed_price", color="category",
                                    title="Átlagárak régiónként és kategóriánként",
                                    labels={"producer_region": "Régió", "confirmed_price": "Átlagár (Ft)", "category": "Kategória"},
                                    barmode="group")
                        st.plotly_chart(fig, use_container_width=True)
                    
                    # 4. Régiónkénti top terméktípusok
                    st.subheader("Régiónkénti top terméktípusok")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        selected_region1 = st.selectbox("Válassz régiót az elemzéshez (1):", df["producer_region"].unique(), key="regional_analysis_region1")
                    
                    with col2:
                        selected_region2 = st.selectbox("Válassz régiót az elemzéshez (2):", 
                                                        [r for r in df["producer_region"].unique() if r != selected_region1],
                                                        index=min(1, len(df["producer_region"].unique())-1),
                                                        key="regional_analysis_region2")
                    
                    col1, col2 = st.columns(2)
                    
                    
                    with col1:
                        # Az első kiválasztott régió top terméktípusai
                        region1_data = df[df["producer_region"] == selected_region1]
                        
                        if not region1_data.empty:
                            region1_products = region1_data["product_type"].value_counts().reset_index()
                            region1_products.columns = ["Terméktípus", "Darab"]
                            region1_products = region1_products.head(5)
                            
                            fig = px.pie(region1_products, values="Darab", names="Terméktípus", 
                                        title=f"Top 5 terméktípus a(z) {selected_region1} régióban")
                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.info(f"Nincs adat a(z) {selected_region1} régióhoz")
                    
                    with col2:
                        # A második kiválasztott régió top terméktípusai
                        region2_data = df[df["producer_region"] == selected_region2]
                        
                        if not region2_data.empty:
                            region2_products = region2_data["product_type"].value_counts().reset_index()
                            region2_products.columns = ["Terméktípus", "Darab"]
                            region2_products = region2_products.head(5)
                            
                            fig = px.pie(region2_products, values="Darab", names="Terméktípus", 
                                        title=f"Top 5 terméktípus a(z) {selected_region2} régióban")
                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.info(f"Nincs adat a(z) {selected_region2} régióhoz")
                    
                    # 5. Részletes statisztika minden régióhoz
                    st.subheader("Részletes regionális statisztikák")
                    
                    # Régiónkénti aggregált statisztikák
                    regional_stats = df.groupby("producer_region").agg({
                        "id": "count",
                        "quantity_kg": ["sum", "mean"],
                        "confirmed_price": ["mean", "min", "max"]
                    }).reset_index()
                    
                    regional_stats.columns = ["_".join(col).strip() if col[1] else col[0] for col in regional_stats.columns.values]
                    
                    # Oszlopnevek szépítése
                    regional_stats = regional_stats.rename(columns={
                        "producer_region": "Régió",
                        "id_count": "Ajánlatok száma",
                        "quantity_kg_sum": "Összes mennyiség (kg)",
                        "quantity_kg_mean": "Átlagos mennyiség (kg)",
                        "confirmed_price_mean": "Átlagos ár (Ft)",
                        "confirmed_price_min": "Minimum ár (Ft)",
                        "confirmed_price_max": "Maximum ár (Ft)"
                    })
                    
                    st.dataframe(regional_stats.style.format({
                        "Átlagos mennyiség (kg)": "{:.2f}",
                        "Átlagos ár (Ft)": "{:.2f}",
                    }), use_container_width=True)
                
                # Generált adatok mintája
                with st.expander("Nyers generált adatok mintája"):
                    st.json([offer for offer in all_offers[:5]])  # Csak az első 5 ajánlatot mutatjuk
            else:
                st.warning("Nem sikerült regionális termelőket és ajánlatokat generálni!")

if __name__ == "__main__":
    show_data_generator()
