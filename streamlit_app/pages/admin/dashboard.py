# Adminisztrátori irányítópult
"""
Adminisztrátori irányítópult oldal.
"""
import streamlit as st
import datetime
import pandas as pd
from api import offers as offers_api
from api import users as users_api
from api import products as products_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from components.data_display import display_offer_table, display_status_chart
from components.activity_feed import render_dashboard_widgets
from utils.session import is_authenticated, get_current_user, init_session_state
from utils.formatting import format_status, format_quantity, format_price, format_role
from utils.config import get_page_title, get_offer_statuses
import uuid

def load_settings_to_session_state():
    """
    Beállítások betöltése a session state-be a backend API-ból.
    Ez biztosítja, hogy a beállítások elérhetőek legyenek akkor is, ha a session state elveszne.
    """
    # Lekérjük a beállításokat a backendről
    success, settings = users_api.get_user_default_settings()
    
    # Mindig írjuk ki a konzolra a teljes API választ a diagnosztikához
    print("==== API BEÁLLÍTÁSOK VÁLASZ ====")
    print(f"API hívás sikeres: {success}")
    print(f"API válasz: {settings}")
    print("==== API VÁLASZ RÉSZLETEK ====")
    if success and isinstance(settings, dict):
        for key, value in settings.items():
            print(f"{key}: {value}")
    print("==== API VÁLASZ VÉGE ====")
    
    # Csak akkor töltünk be adatokat, ha a settings ténylegesen nem üres
    if success and isinstance(settings, dict) and settings:
        # Létrehozzuk a user_settings objektumot a session state-ben
        st.session_state.user_settings = {
            "default_product_type": {
                "id": settings.get("default_product_type_id"),
                "name": settings.get("default_product_type_name", ""),
                "has_quality_grades": settings.get("has_quality_grades", False)
            },
            "default_quality_grade": {
                "id": settings.get("default_quality_grade_id"),
                "name": settings.get("default_quality_grade_name", "")
            },
            "default_quantity_unit": settings.get("default_quantity_unit", "kg"),
            "category": {
                "id": settings.get("default_category_id"),
                "name": settings.get("default_category_name", "")
            }
        }
        print("Beállítások sikeresen betöltve a session state-be a backendről")
        return True
    else:
        print("Nem sikerült betölteni a beállításokat - hiányos vagy üres API válasz")
        return False

def show_admin_dashboard():
    """
    Adminisztrátori irányítópult megjelenítése.
    """
    # Biztosítjuk, hogy a page_uuid inicializálva van
    init_session_state()
    
    # Debug: Kiírjuk a session state tartalmát
    print(f"Admin dashboard: page_uuid={st.session_state.get('page_uuid', 'nem létezik')}")
    
    # Beállítjuk a debug módot az oldal tetejére
    debug_mode = False
    
    st.title("Adminisztrátori Irányítópult")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Betöltjük a beállításokat a session state-be - mindig megpróbáljuk frissíteni
    if "user_settings" in st.session_state:
        del st.session_state.user_settings
    load_success = load_settings_to_session_state()
    
    # Debug információ megjelenítése az oldalon, ha engedélyezett
    if debug_mode:
        with st.expander("Debug Információ", expanded=False):
            st.write("### API Válasz Debug")
            success, settings = users_api.get_user_default_settings()
            st.write(f"API hívás sikeres: {success}")
            st.write(f"API válasz: {settings}")
            st.write("### Session State Debug")
            if "user_settings" in st.session_state:
                st.write("Session state tartalmaz beállításokat:")
                st.write(st.session_state.user_settings)
            else:
                st.write("Session state NEM tartalmaz beállításokat!")
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Üdvözlő üzenet
    st.write(f"### Üdvözöljük, {user.get('contact_name')}!")
    
    # Mai dátum és idő
    now = datetime.datetime.now()
    st.write(f"Dátum: {now.strftime('%Y. %m. %d.')} - {now.strftime('%H:%M')}")
    
    # Új tevékenységi widget megjelenítése
    render_dashboard_widgets()
    
    st.write("---")  # Elválasztó vonal
    
    # Rendszer áttekintés
    col1, col2, col3 = st.columns(3)
    
    # Felhasználók lekérése
    success_users, users_result = users_api.get_users()
    
    if success_users:
        users = users_result
        
        # Felhasználók számának összesítése szerepkörök szerint
        total_users = len(users)
        role_counts = {}
        
        for u in users:
            role = u.get("role", "")
            role_counts[role] = role_counts.get(role, 0) + 1
        
        with col1:
            with st.container(border=True):
                st.subheader("Felhasználói statisztikák")
                
                st.metric(label="Összes felhasználó", value=total_users)
                
                # Szerepkörök száma
                for role, count in role_counts.items():
                    st.metric(label=f"{format_role(role)}", value=count)
                
                # Gyors műveletek
                if st.button("Felhasználók kezelése", type="primary", key="manage_users"):
                    st.switch_page("pages/admin_users.py")
    else:
        with col1:
            with st.container(border=True):
                st.subheader("Felhasználói statisztikák")
                show_error(f"Hiba a felhasználók lekérésekor: {users_result}")
    
    # Termékek lekérése
    success_categories, categories_result = products_api.get_product_categories()
    success_types, types_result = products_api.get_product_types()
    
    if success_categories and success_types:
        categories = categories_result
        product_types = types_result
        
        with col2:
            with st.container(border=True):
                st.subheader("Termék statisztikák")
                
                st.metric(label="Összes termékkategória", value=len(categories))
                st.metric(label="Terméktípusok", value=len(product_types))
                
                # Típusok kategóriák szerinti eloszlása
                st.write("**Terméktípusok kategóriánként:**")
                
                for cat in categories:
                    cat_id = cat.get("id")
                    cat_name = cat.get("name", "")
                    count = sum(1 for t in product_types if t.get("category", {}).get("id") == cat_id)
                    
                    st.write(f"- {cat_name}: {count}")
                
                # Gyors műveletek
                if st.button("Termékek kezelése", type="primary", key="manage_products"):
                    st.switch_page("pages/admin_products.py")
    else:
        with col2:
            with st.container(border=True):
                st.subheader("Termék statisztikák")
                show_error(f"Hiba a termékek lekérésekor.")
    
    # Ajánlatok lekérése
    success_offers, offers_result = offers_api.get_offers()
    
    if success_offers:
        offers = offers_result
        
        with col3:
            with st.container(border=True):
                st.subheader("Ajánlat statisztikák")
                
                # Különböző státuszú ajánlatok számának kiszámítása
                total_offers = len(offers)
                
                status_counts = {}
                for status in get_offer_statuses().keys():
                    status_counts[status] = sum(1 for o in offers if o.get("status") == status)
                
                st.metric(label="Összes ajánlat", value=total_offers)
                
                # Mai beszállítások
                today = datetime.date.today()
                today_deliveries = sum(1 for o in offers if o.get("delivery_date") == today.strftime("%Y-%m-%d") and o.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"])
                
                st.metric(label="Mai beszállítások", value=today_deliveries)
                
                # Státusz szerinti eloszlás
                st.write("**Ajánlatok státusz szerint:**")
                
                for status, count in status_counts.items():
                    st.write(f"- {format_status(status)}: {count}")
                
                # Gyors műveletek
                if st.button("Ügyintézői kezelőfelület", type="primary", use_container_width=True):
                    st.switch_page("pages/operator_dashboard.py")
    else:
        with col3:
            with st.container(border=True):
                st.subheader("Ajánlat statisztikák")
                show_error(f"Hiba az ajánlatok lekérésekor: {offers_result}")
    
    # Rendszerállapot áttekintés
    st.subheader("Rendszerállapot")
    
    # Itt implementálható a rendszerállapot monitoring (pl. API válaszidők, hibák, stb.)
    system_metrics = {
        "Backend API": "Működik",
        "Adatbázis kapcsolat": "Működik",
        "Utolsó rendszerfrissítés": "2025.03.23 10:30",
        "API válaszidő": "125 ms"
    }
    
    # Példa metrikák megjelenítése
    system_col1, system_col2 = st.columns(2)
    
    with system_col1:
        for metric, value in list(system_metrics.items())[:len(system_metrics)//2]:
            st.metric(label=metric, value=value)
    
    with system_col2:
        for metric, value in list(system_metrics.items())[len(system_metrics)//2:]:
            st.metric(label=metric, value=value)
    
    # Rendszerkarbantartási funkciók
    st.subheader("Rendszerkarbantartás")
    
    # Példa funkciók
    maintenance_col1, maintenance_col2, maintenance_col3, maintenance_col4 = st.columns(4)
    
    with maintenance_col1:
        if st.button("Adatbázis biztonsági mentés", use_container_width=True):
            st.info("Biztonsági mentés funkció még nincs implementálva.")
    
    with maintenance_col2:
        if st.button("Cache törlése", use_container_width=True):
            st.info("Cache törlési funkció még nincs implementálva.")
    
    with maintenance_col3:
        if st.button("Rendszernapló megtekintése", use_container_width=True):
            st.info("Rendszernapló megtekintési funkció még nincs implementálva.")

    with maintenance_col4:
        if st.button("Oldaltérkép és Navigáció", type="primary", use_container_width=True):
            st.switch_page("pages/admin_sitemap.py")
    
    # Footer
    st.write("---")
    st.write("További funkciók eléréséhez használja az oldalsávon található menüt.")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=get_page_title("Adminisztrátori Irányítópult"),
        page_icon="🏠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük az adminisztrátori irányítópultot
    show_admin_dashboard()
