# Felhasználók kezelése
"""
Felhasználók részletes kezelése admin számára.
"""
import streamlit as st
import pandas as pd
from datetime import datetime
from api import users as users_api
from utils.session import is_authenticated, get_current_user
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.formatting import format_role, format_datetime
import app_config as config

def show_user_management():
    """
    Felhasználók részletes kezelése admin számára.
    """
    st.title("Felhasználók Kezelése")
    
    # Ellen<PERSON>rizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Fel<PERSON>z<PERSON><PERSON>ó adatainak lekérése
    user = get_current_user()
    
    # Ellen<PERSON><PERSON>zük, hogy admin-e
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Felhasználói azonosító lekérése a query paraméterből, ha van
    query_params = st.query_params()
    user_id = query_params.get("user_id", [None])[0]
    
    # Ha van konkrét felhasználó, akkor annak részletes kezelése
    if user_id:
        show_user_detail(int(user_id))
    else:
        # Különben felhasználók listázása
        show_user_list()

def show_user_list():
    """
    Felhasználók listázása és alap kezelése.
    """
    # Felhasználók lekérése
    success, result = users_api.get_users()
    
    # Szűrési lehetőségek
    st.subheader("Szűrési beállítások")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        search_name = st.text_input("Név/Email keresés", "")
    
    with col2:
        role_filter = st.selectbox(
            "Szerepkör", 
            ["Mind"] + list(config.USER_ROLES.keys())
        )
    
    with col3:
        status_filter = st.selectbox(
            "Státusz", 
            ["Mind", "Aktív", "Inaktív"]
        )
    
    # Felhasználói lista megjelenítése
    if success:
        users = result
        
        # Szűrés a keresési feltételek alapján
        if search_name:
            users = [user for user in users if 
                     search_name.lower() in user.get("contact_name", "").lower() or 
                     search_name.lower() in user.get("email", "").lower() or
                     search_name.lower() in user.get("company_name", "").lower()]
        
        if role_filter != "Mind":
            users = [user for user in users if user.get("role") == role_filter]
            
        if status_filter != "Mind":
            is_active = status_filter == "Aktív"
            users = [user for user in users if user.get("is_active") == is_active]
        
        if not users:
            st.info("Nincs találat a megadott szűrési feltételeknek megfelelően.")
            return
        
        # Felhasználók táblázatos megjelenítése
        users_data = []
        for u in users:
            users_data.append({
                "ID": u.get("id"),
                "Név": u.get("contact_name", ""),
                "Email": u.get("email", ""),
                "Cégnév": u.get("company_name", ""),
                "Szerepkör": format_role(u.get("role", "")),
                "Státusz": "Aktív" if u.get("is_active") else "Inaktív",
                "Regisztráció dátuma": format_datetime(u.get("created_at", ""))
            })
        
        # Táblázat létrehozása
        df = pd.DataFrame(users_data)
        
        # Eszköztár
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.subheader(f"Felhasználók listája ({len(users_data)} találat)")
        
        # Táblázat megjelenítése Streamlit dataframe-mel
        st.dataframe(
            df,
            use_container_width=True,
            column_config={
                "ID": st.column_config.NumberColumn("ID", format="%d"),
                "Név": st.column_config.TextColumn("Név"),
                "Email": st.column_config.TextColumn("Email"),
                "Cégnév": st.column_config.TextColumn("Cégnév"),
                "Szerepkör": st.column_config.TextColumn("Szerepkör"),
                "Státusz": st.column_config.TextColumn("Státusz"),
                "Regisztráció dátuma": st.column_config.TextColumn("Regisztráció dátuma")
            },
            hide_index=True
        )
        
        # Felhasználó kiválasztása szerkesztésre
        selected_user_id = st.selectbox(
            "Válasszon felhasználót a részletes kezeléshez:",
            options=[u.get("id") for u in users],
            format_func=lambda x: next((f"{u.get('contact_name')} ({u.get('email')})" for u in users if u.get("id") == x), "")
        )
        
        if st.button("Részletes kezelés", type="primary"):
            st.experimental_set_query_params(user_id=selected_user_id)
            st.rerun()
    else:
        show_error(f"Hiba a felhasználók lekérésekor: {result}")

def show_user_detail(user_id):
    """
    Egy konkrét felhasználó részletes kezelése.
    
    Args:
        user_id (int): Felhasználó azonosítója
    """
    # Visszalépés gomb
    if st.button("← Vissza a listához"):
        st.experimental_set_query_params()
        st.rerun()
    
    # Felhasználó lekérése
    success, result = users_api.get_user(user_id)
    
    if not success:
        show_error(f"Hiba a felhasználó adatainak lekérésekor: {result}")
        return
    
    user_data = result
    
    # Felhasználó adatainak megjelenítése
    st.subheader(f"Felhasználó adatai: {user_data.get('contact_name')}")
    
    # Állapotjelző mutatása
    status_value = "🟢 Aktív" if user_data.get("is_active") else "🔴 Inaktív"
    st.subheader(status_value, divider="rainbow")
    
    # Adatok kártyákban
    col1, col2 = st.columns(2)
    
    with col1:
        with st.container(border=True):
            st.subheader("Alapadatok")
            st.write(f"**Azonosító:** {user_data.get('id')}")
            st.write(f"**Név:** {user_data.get('contact_name')}")
            st.write(f"**Email:** {user_data.get('email')}")
            st.write(f"**Szerepkör:** {format_role(user_data.get('role', ''))}")
            st.write(f"**Telefon:** {user_data.get('phone_number', '-')}")
            st.write(f"**Regisztráció dátuma:** {format_datetime(user_data.get('created_at'))}")
    
    with col2:
        with st.container(border=True):
            st.subheader("Céges adatok")
            st.write(f"**Cégnév:** {user_data.get('company_name', '-')}")
            st.write(f"**Adószám:** {user_data.get('tax_id', '-')}")
    
    # Műveletek
    st.divider()
    st.subheader("Műveletek")

    # Kétoszlopos elrendezés a működésekhez
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Szerepkör módosítása
        with st.form(key="role_form"):
            st.subheader("Szerepkör módosítása")
            
            new_role = st.selectbox(
                "Új szerepkör", 
                options=list(config.USER_ROLES.keys()), 
                index=list(config.USER_ROLES.keys()).index(user_data.get("role", "termelő"))
            )
            
            if st.form_submit_button("Szerepkör módosítása"):
                success, result = users_api.set_user_role(user_id, new_role)
                
                if success:
                    show_success("Szerepkör sikeresen módosítva!")
                    st.rerun()
                else:
                    show_error(f"Hiba a szerepkör módosításakor: {result}")
    
    with col2:
        # Aktiválás/Deaktiválás
        with st.form(key="status_form"):
            is_active = user_data.get("is_active", False)
            status_text = "Deaktiválás" if is_active else "Aktiválás"
            
            st.subheader(f"Felhasználó {status_text.lower()}a")
            st.write(f"A felhasználó jelenlegi állapota: {'Aktív' if is_active else 'Inaktív'}")
            
            if st.form_submit_button(status_text, type="primary" if not is_active else "secondary"):
                if is_active:
                    success, result = users_api.deactivate_user(user_id)
                    message = "deaktiválva"
                else:
                    success, result = users_api.activate_user(user_id)
                    message = "aktiválva"
                
                if success:
                    show_success(f"Felhasználó sikeresen {message}!")
                    st.rerun()
                else:
                    show_error(f"Hiba a felhasználó {status_text.lower()}akor: {result}")
    
    with col3:
        # Törlés (veszélyes művelet)
        with st.form(key="delete_form"):
            st.subheader("Felhasználó törlése")
            st.warning("FIGYELEM! Ez a művelet nem visszafordítható!")
            
            confirm_email = st.text_input("Írja be az email címet a törlés megerősítéséhez:")
            
            if st.form_submit_button("Törlés", type="secondary"):
                if confirm_email == user_data.get("email", ""):
                    success, result = users_api.delete_user(user_id)
                    
                    if success:
                        show_success("Felhasználó sikeresen törölve!")
                        st.experimental_set_query_params()
                        st.rerun()
                    else:
                        show_error(f"Hiba a felhasználó törlésekor: {result}")
                else:
                    show_error("A megadott email cím nem egyezik meg a felhasználó email címével!")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Felhasználók kezelése - {config.APP_NAME}",
        page_icon="👥",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a felhasználókezelést
    show_user_management()
