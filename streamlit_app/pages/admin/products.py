"""
Admin termékke<PERSON><PERSON> oldal.
"""
import streamlit as st
import pandas as pd
from api import products as products_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.validators import validate_required, validate_numeric, validate_length
import app_config as config

def show_admin_products():
    """
    Admin termékkezelő oldal megjelenítése.
    """
    st.title("Termékek kezelése")
    
    # Ellen<PERSON>rizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # <PERSON><PERSON><PERSON>zük, hogy admin-e
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Funkciók választása
    management_options = [
        "Termékkategóriák kezelése", 
        "Terméktípusok kezelése", 
        "Minőségi besorolások kezelése"
    ]
    
    selected_management = st.selectbox("Válassza ki a kezelni kívánt elemet", options=management_options)
    
    if selected_management == "Termékkategóriák kezelése":
        show_category_management()
    elif selected_management == "Terméktípusok kezelése":
        show_product_type_management()
    elif selected_management == "Minőségi besorolások kezelése":
        show_quality_grade_management()

def show_category_management():
    """
    Termékkategóriák kezelésének megjelenítése.
    """
    st.header("Termékkategóriák kezelése")
    
    # Kategóriák lekérése
    success, categories = products_api.get_product_categories()
    
    if not success:
        show_error(f"Hiba a termékkategóriák lekérésekor: {categories}")
        return
    
    # Kategóriák listázása
    if categories:
        # DataFrame létrehozása
        category_data = []
        
        for cat in categories:
            category_data.append({
                "id": cat.get("id"),
                "name": cat.get("name", ""),
                "description": cat.get("description", ""),
                "created_at": cat.get("created_at", "")
            })
        
        df = pd.DataFrame(category_data)
        
        # Táblázat megjelenítése
        st.dataframe(
            df,
            column_config={
                "id": "Azonosító",
                "name": "Kategória neve",
                "description": "Leírás",
                "created_at": "Létrehozva"
            },
            hide_index=True,
            use_container_width=True
        )
    else:
        st.info("Nincsenek termékkategóriák a rendszerben.")
    
    # Új kategória létrehozása
    st.subheader("Új termékkategória létrehozása")
    
    with st.form("new_category_form"):
        category_name = st.text_input("Kategória neve *", placeholder="pl. Paprika")
        category_description = st.text_area("Leírás", placeholder="Kategória leírása", max_chars=500)
        
        # Mentés gomb
        submit = st.form_submit_button("Kategória létrehozása", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        is_valid, error = validate_required(category_name, "Kategória neve")
        if not is_valid:
            validation_errors.append(error)
        
        # Hossz ellenőrzés
        is_valid, error = validate_length(category_name, "Kategória neve", min_length=2, max_length=100)
        if not is_valid:
            validation_errors.append(error)
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
        else:
            # Kategória adatainak összeállítása
            category_data = {
                "name": category_name,
                "description": category_description if category_description else None
            }
            
            # Kategória létrehozása API hívással
            success, result = products_api.create_product_category(category_data)
            
            if success:
                show_success("Termékkategória sikeresen létrehozva!")
                st.rerun()
            else:
                show_error(f"Hiba a termékkategória létrehozásakor: {result}")
    
    # Kategória szerkesztése/törlése
    st.subheader("Termékkategória szerkesztése vagy törlése")
    
    if categories:
        selected_category = st.selectbox(
            "Válasszon kategóriát",
            options=[f"{cat.get('id')} - {cat.get('name')}" for cat in categories],
            format_func=lambda x: x
        )
        
        # Kategória azonosító kinyerése
        category_id = int(selected_category.split(" - ")[0])
        
        # Kiválasztott kategória megkeresése
        selected_cat = next((cat for cat in categories if cat.get("id") == category_id), None)
        
        if selected_cat:
            # Kategória szerkesztése form
            with st.form(f"edit_category_{category_id}_form"):
                edit_name = st.text_input("Kategória neve *", value=selected_cat.get("name", ""))
                edit_description = st.text_area("Leírás", value=selected_cat.get("description", ""), max_chars=500)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    update_button = st.form_submit_button("Kategória frissítése", type="primary", use_container_width=True)
                
                with col2:
                    delete_button = st.form_submit_button("Kategória törlése", type="secondary", use_container_width=True)
            
            if update_button:
                # Adatok validálása
                validation_errors = []
                
                # Kötelező mezők ellenőrzése
                is_valid, error = validate_required(edit_name, "Kategória neve")
                if not is_valid:
                    validation_errors.append(error)
                
                # Hossz ellenőrzés
                is_valid, error = validate_length(edit_name, "Kategória neve", min_length=2, max_length=100)
                if not is_valid:
                    validation_errors.append(error)
                
                # Ha van validációs hiba, megjelenítjük
                if validation_errors:
                    for error in validation_errors:
                        show_error(error)
                else:
                    # Frissítési adatok összeállítása
                    update_data = {
                        "name": edit_name,
                        "description": edit_description if edit_description else None
                    }
                    
                    # Kategória frissítése API hívással
                    success, result = products_api.update_product_category(category_id, update_data)
                    
                    if success:
                        show_success("Termékkategória sikeresen frissítve!")
                        st.rerun()
                    else:
                        show_error(f"Hiba a termékkategória frissítésekor: {result}")
            
            if delete_button:
                # Megerősítés kérése
                if st.session_state.get(f"confirm_delete_category_{category_id}", False):
                    # Kategória törlése API hívással
                    success, result = products_api.delete_product_category(category_id)
                    
                    if success:
                        show_success("Termékkategória sikeresen törölve!")
                        st.session_state[f"confirm_delete_category_{category_id}"] = False
                        st.rerun()
                    else:
                        show_error(f"Hiba a termékkategória törlésekor: {result}")
                        st.session_state[f"confirm_delete_category_{category_id}"] = False
                else:
                    st.warning(f"Biztosan törölni szeretné a '{selected_cat.get('name')}' kategóriát? Ez a művelet nem vonható vissza!")
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        if st.button("Igen, törlés", key=f"confirm_yes_{category_id}", type="primary", use_container_width=True):
                            st.session_state[f"confirm_delete_category_{category_id}"] = True
                            st.rerun()
                    
                    with col2:
                        if st.button("Mégsem", key=f"confirm_no_{category_id}", use_container_width=True):
                            st.session_state[f"confirm_delete_category_{category_id}"] = False
                            st.rerun()
    else:
        st.info("Nincsenek termékkategóriák a rendszerben.")

def show_product_type_management():
    """
    Terméktípusok kezelésének megjelenítése.
    """
    st.header("Terméktípusok kezelése")
    
    # Kategóriák lekérése
    success_cat, categories = products_api.get_product_categories()
    
    if not success_cat:
        show_error(f"Hiba a termékkategóriák lekérésekor: {categories}")
        return
    
    # Terméktípusok lekérése
    success, types = products_api.get_product_types()
    
    if not success:
        show_error(f"Hiba a terméktípusok lekérésekor: {types}")
        return
    
    # Kategória választó a szűréshez
    selected_filter_category = st.selectbox(
        "Szűrés kategória szerint",
        options=["Összes kategória"] + [cat.get("name", "") for cat in categories],
        key="filter_category"
    )
    
    # Terméktípusok listázása
    if types:
        # Szűrés kategória szerint, ha van kiválasztva
        filtered_types = types
        
        if selected_filter_category != "Összes kategória":
            filtered_types = [t for t in types if t.get("category", {}).get("name") == selected_filter_category]
        
        if filtered_types:
            # DataFrame létrehozása
            type_data = []
            
            for t in filtered_types:
                type_data.append({
                    "id": t.get("id"),
                    "name": t.get("name", ""),
                    "category": t.get("category", {}).get("name", ""),
                    "has_quality_grades": "Igen" if t.get("has_quality_grades") else "Nem",
                    "description": t.get("description", ""),
                    "created_at": t.get("created_at", "")
                })
            
            df = pd.DataFrame(type_data)
            
            # Táblázat megjelenítése
            st.dataframe(
                df,
                column_config={
                    "id": "Azonosító",
                    "name": "Terméktípus neve",
                    "category": "Kategória",
                    "has_quality_grades": "Van minőségi besorolás",
                    "description": "Leírás",
                    "created_at": "Létrehozva"
                },
                hide_index=True,
                use_container_width=True
            )
        else:
            st.info(f"Nincsenek terméktípusok a kiválasztott kategóriában.")
    else:
        st.info("Nincsenek terméktípusok a rendszerben.")
    
    # Új terméktípus létrehozása
    st.subheader("Új terméktípus létrehozása")
    
    # Ellenőrizzük, hogy vannak-e kategóriák
    if not categories:
        st.warning("Először hozzon létre legalább egy termékkategóriát!")
        return
    
    with st.form("new_type_form"):
        # Kategória kiválasztása
        type_category = st.selectbox(
            "Kategória *",
            options=[cat.get("name", "") for cat in categories],
            key="new_type_category"
        )
        
        # Kategória ID meghatározása
        type_category_id = next((cat.get("id") for cat in categories if cat.get("name") == type_category), None)
        
        type_name = st.text_input("Terméktípus neve *", placeholder="pl. TV paprika")
        has_quality_grades = st.checkbox("Van minőségi besorolása?", value=True)
        type_description = st.text_area("Leírás", placeholder="Terméktípus leírása", max_chars=500)
        
        # Mentés gomb
        submit = st.form_submit_button("Terméktípus létrehozása", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        is_valid, error = validate_required(type_name, "Terméktípus neve")
        if not is_valid:
            validation_errors.append(error)
        
        # Hossz ellenőrzés
        is_valid, error = validate_length(type_name, "Terméktípus neve", min_length=2, max_length=100)
        if not is_valid:
            validation_errors.append(error)
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
        else:
            # Típus adatainak összeállítása
            type_data = {
                "category_id": type_category_id,
                "name": type_name,
                "has_quality_grades": has_quality_grades,
                "description": type_description if type_description else None
            }
            
            # Típus létrehozása API hívással
            success, result = products_api.create_product_type(type_data)
            
            if success:
                show_success("Terméktípus sikeresen létrehozva!")
                st.rerun()
            else:
                show_error(f"Hiba a terméktípus létrehozásakor: {result}")
    
    # Terméktípus szerkesztése/törlése
    st.subheader("Terméktípus szerkesztése vagy törlése")
    
    if types:
        selected_type = st.selectbox(
            "Válasszon terméktípust",
            options=[f"{t.get('id')} - {t.get('name')} ({t.get('category', {}).get('name', '')})" for t in types],
            format_func=lambda x: x
        )
        
        # Típus azonosító kinyerése
        type_id = int(selected_type.split(" - ")[0])
        
        # Kiválasztott típus megkeresése
        selected_type_data = next((t for t in types if t.get("id") == type_id), None)
        
        if selected_type_data:
            # Típus szerkesztése form
            with st.form(f"edit_type_{type_id}_form"):
                # Kategória kiválasztása
                edit_category = st.selectbox(
                    "Kategória *",
                    options=[cat.get("name", "") for cat in categories],
                    index=next((i for i, cat in enumerate(categories) if cat.get("id") == selected_type_data.get("category", {}).get("id")), 0),
                    key=f"edit_type_category_{type_id}"
                )
                
                # Kategória ID meghatározása
                edit_category_id = next((cat.get("id") for cat in categories if cat.get("name") == edit_category), None)
                
                edit_name = st.text_input("Terméktípus neve *", value=selected_type_data.get("name", ""))
                edit_has_quality_grades = st.checkbox("Van minőségi besorolása?", value=selected_type_data.get("has_quality_grades", False))
                edit_description = st.text_area("Leírás", value=selected_type_data.get("description", ""), max_chars=500)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    update_button = st.form_submit_button("Terméktípus frissítése", type="primary", use_container_width=True)
                
                with col2:
                    delete_button = st.form_submit_button("Terméktípus törlése", type="secondary", use_container_width=True)
            
            if update_button:
                # Adatok validálása
                validation_errors = []
                
                # Kötelező mezők ellenőrzése
                is_valid, error = validate_required(edit_name, "Terméktípus neve")
                if not is_valid:
                    validation_errors.append(error)
                
                # Hossz ellenőrzés
                is_valid, error = validate_length(edit_name, "Terméktípus neve", min_length=2, max_length=100)
                if not is_valid:
                    validation_errors.append(error)
                
                # Ha van validációs hiba, megjelenítjük
                if validation_errors:
                    for error in validation_errors:
                        show_error(error)
                else:
                    # Frissítési adatok összeállítása
                    update_data = {
                        "category_id": edit_category_id,
                        "name": edit_name,
                        "has_quality_grades": edit_has_quality_grades,
                        "description": edit_description if edit_description else None
                    }
                    
                    # Típus frissítése API hívással
                    success, result = products_api.update_product_type(type_id, update_data)
                    
                    if success:
                        show_success("Terméktípus sikeresen frissítve!")
                        st.rerun()
                    else:
                        show_error(f"Hiba a terméktípus frissítésekor: {result}")
            
            if delete_button:
                # Megerősítés kérése
                if st.session_state.get(f"confirm_delete_type_{type_id}", False):
                    # Típus törlése API hívással
                    success, result = products_api.delete_product_type(type_id)
                    
                    if success:
                        show_success("Terméktípus sikeresen törölve!")
                        st.session_state[f"confirm_delete_type_{type_id}"] = False
                        st.rerun()
                    else:
                        show_error(f"Hiba a terméktípus törlésekor: {result}")
                        st.session_state[f"confirm_delete_type_{type_id}"] = False
                else:
                    st.warning(f"Biztosan törölni szeretné a '{selected_type_data.get('name')}' terméktípust? Ez a művelet nem vonható vissza!")
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        if st.button("Igen, törlés", key=f"confirm_yes_type_{type_id}", type="primary", use_container_width=True):
                            st.session_state[f"confirm_delete_type_{type_id}"] = True
                            st.rerun()
                    
                    with col2:
                        if st.button("Mégsem", key=f"confirm_no_type_{type_id}", use_container_width=True):
                            st.session_state[f"confirm_delete_type_{type_id}"] = False
                            st.rerun()
    else:
        st.info("Nincsenek terméktípusok a rendszerben.")

def show_quality_grade_management():
    """
    Minőségi besorolások kezelésének megjelenítése.
    """
    st.header("Minőségi besorolások kezelése")
    
    # Terméktípusok lekérése
    success, types = products_api.get_product_types()
    
    if not success:
        show_error(f"Hiba a terméktípusok lekérésekor: {types}")
        return
    
    # Csak minőségi besorolással rendelkező típusok
    types_with_grades = [t for t in types if t.get("has_quality_grades")]
    
    if not types_with_grades:
        st.warning("Nincsenek minőségi besorolással rendelkező terméktípusok a rendszerben!")
        return
    
    # Terméktípus választó a szűréshez
    selected_filter_type = st.selectbox(
        "Válasszon terméktípust",
        options=[f"{t.get('id')} - {t.get('name')} ({t.get('category', {}).get('name', '')})" for t in types_with_grades],
        key="filter_type"
    )
    
    # Típus azonosító kinyerése
    type_id = int(selected_filter_type.split(" - ")[0])
    
    # Minőségi besorolások lekérése
    success, grades = products_api.get_quality_grades(product_type_id=type_id)
    
    if not success:
        show_error(f"Hiba a minőségi besorolások lekérésekor: {grades}")
        return
    
    # Minőségi besorolások listázása
    if grades:
        # DataFrame létrehozása
        grade_data = []
        
        for g in grades:
            grade_data.append({
                "id": g.get("id"),
                "name": g.get("name", ""),
                "min_shoulder_diameter": g.get("min_shoulder_diameter"),
                "max_shoulder_diameter": g.get("max_shoulder_diameter"),
                "min_length": g.get("min_length"),
                "max_length": g.get("max_length"),
                "description": g.get("description", ""),
                "created_at": g.get("created_at", "")
            })
        
        df = pd.DataFrame(grade_data)
        
        # Táblázat megjelenítése
        st.dataframe(
            df,
            column_config={
                "id": "Azonosító",
                "name": "Besorolás neve",
                "min_shoulder_diameter": st.column_config.NumberColumn("Min. vállátmérő (mm)", format="%.1f"),
                "max_shoulder_diameter": st.column_config.NumberColumn("Max. vállátmérő (mm)", format="%.1f"),
                "min_length": st.column_config.NumberColumn("Min. hossz (mm)", format="%.1f"),
                "max_length": st.column_config.NumberColumn("Max. hossz (mm)", format="%.1f"),
                "description": "Leírás",
                "created_at": "Létrehozva"
            },
            hide_index=True,
            use_container_width=True
        )
    else:
        st.info("Nincsenek minőségi besorolások a kiválasztott terméktípushoz.")
    
    # Új minőségi besorolás létrehozása
    st.subheader("Új minőségi besorolás létrehozása")
    
    with st.form("new_grade_form"):
        # Kiválasztott terméktípus megjelenítése
        selected_type_data = next((t for t in types_with_grades if t.get("id") == type_id), None)
        st.info(f"Terméktípus: {selected_type_data.get('name')} ({selected_type_data.get('category', {}).get('name', '')})")
        
        grade_name = st.text_input("Besorolás neve *", placeholder="pl. Extra")
        
        col1, col2 = st.columns(2)
        
        with col1:
            min_shoulder = st.number_input("Minimum vállátmérő (mm)", value=0.0, step=0.1, format="%.1f")
            min_length = st.number_input("Minimum hossz (mm)", value=0.0, step=0.1, format="%.1f")
        
        with col2:
            max_shoulder = st.number_input("Maximum vállátmérő (mm)", value=0.0, step=0.1, format="%.1f")
            max_length = st.number_input("Maximum hossz (mm)", value=0.0, step=0.1, format="%.1f")
        
        grade_description = st.text_area("Leírás", placeholder="Besorolás leírása", max_chars=500)
        
        # Mentés gomb
        submit = st.form_submit_button("Besorolás létrehozása", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        is_valid, error = validate_required(grade_name, "Besorolás neve")
        if not is_valid:
            validation_errors.append(error)
        
        # Hossz ellenőrzés
        is_valid, error = validate_length(grade_name, "Besorolás neve", min_length=1, max_length=50)
        if not is_valid:
            validation_errors.append(error)
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
        else:
            # Besorolás adatainak összeállítása
            grade_data = {
                "product_type_id": type_id,
                "name": grade_name,
                "min_shoulder_diameter": min_shoulder if min_shoulder > 0 else None,
                "max_shoulder_diameter": max_shoulder if max_shoulder > 0 else None,
                "min_length": min_length if min_length > 0 else None,
                "max_length": max_length if max_length > 0 else None,
                "description": grade_description if grade_description else None
            }
            
            # Besorolás létrehozása API hívással
            success, result = products_api.create_quality_grade(grade_data)
            
            if success:
                show_success("Minőségi besorolás sikeresen létrehozva!")
                st.rerun()
            else:
                show_error(f"Hiba a minőségi besorolás létrehozásakor: {result}")
    
    # Minőségi besorolás szerkesztése/törlése
    if grades:
        st.subheader("Minőségi besorolás szerkesztése vagy törlése")
        
        selected_grade = st.selectbox(
            "Válasszon besorolást",
            options=[f"{g.get('id')} - {g.get('name')}" for g in grades],
            format_func=lambda x: x
        )
        
        # Besorolás azonosító kinyerése
        grade_id = int(selected_grade.split(" - ")[0])
        
        # Kiválasztott besorolás megkeresése
        selected_grade_data = next((g for g in grades if g.get("id") == grade_id), None)
        
        if selected_grade_data:
            # Besorolás szerkesztése form
            with st.form(f"edit_grade_{grade_id}_form"):
                edit_name = st.text_input("Besorolás neve *", value=selected_grade_data.get("name", ""))
                
                col1, col2 = st.columns(2)
                
                with col1:
                    edit_min_shoulder = st.number_input(
                        "Minimum vállátmérő (mm)", 
                        value=float(selected_grade_data.get("min_shoulder_diameter", 0) or 0),
                        step=0.1,
                        format="%.1f"
                    )
                    edit_min_length = st.number_input(
                        "Minimum hossz (mm)", 
                        value=float(selected_grade_data.get("min_length", 0) or 0),
                        step=0.1,
                        format="%.1f"
                    )
                
                with col2:
                    edit_max_shoulder = st.number_input(
                        "Maximum vállátmérő (mm)", 
                        value=float(selected_grade_data.get("max_shoulder_diameter", 0) or 0),
                        step=0.1,
                        format="%.1f"
                    )
                    edit_max_length = st.number_input(
                        "Maximum hossz (mm)", 
                        value=float(selected_grade_data.get("max_length", 0) or 0),
                        step=0.1,
                        format="%.1f"
                    )
                
                edit_description = st.text_area("Leírás", value=selected_grade_data.get("description", ""), max_chars=500)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    update_button = st.form_submit_button("Besorolás frissítése", type="primary", use_container_width=True)
                
                with col2:
                    delete_button = st.form_submit_button("Besorolás törlése", type="secondary", use_container_width=True)
            
            if update_button:
                # Adatok validálása
                validation_errors = []
                
                # Kötelező mezők ellenőrzése
                is_valid, error = validate_required(edit_name, "Besorolás neve")
                if not is_valid:
                    validation_errors.append(error)
                
                # Hossz ellenőrzés
                is_valid, error = validate_length(edit_name, "Besorolás neve", min_length=1, max_length=50)
                if not is_valid:
                    validation_errors.append(error)
                
                # Ha van validációs hiba, megjelenítjük
                if validation_errors:
                    for error in validation_errors:
                        show_error(error)
                else:
                    # Frissítési adatok összeállítása
                    update_data = {
                        "product_type_id": type_id,
                        "name": edit_name,
                        "min_shoulder_diameter": edit_min_shoulder if edit_min_shoulder > 0 else None,
                        "max_shoulder_diameter": edit_max_shoulder if edit_max_shoulder > 0 else None,
                        "min_length": edit_min_length if edit_min_length > 0 else None,
                        "max_length": edit_max_length if edit_max_length > 0 else None,
                        "description": edit_description if edit_description else None
                    }
                    
                    # Besorolás frissítése API hívással
                    success, result = products_api.update_quality_grade(grade_id, update_data)
                    
                    if success:
                        show_success("Minőségi besorolás sikeresen frissítve!")
                        st.rerun()
                    else:
                        show_error(f"Hiba a minőségi besorolás frissítésekor: {result}")
            
            if delete_button:
                # Megerősítés kérése
                if st.session_state.get(f"confirm_delete_grade_{grade_id}", False):
                    # Besorolás törlése API hívással
                    success, result = products_api.delete_quality_grade(grade_id)
                    
                    if success:
                        show_success("Minőségi besorolás sikeresen törölve!")
                        st.session_state[f"confirm_delete_grade_{grade_id}"] = False
                        st.rerun()
                    else:
                        show_error(f"Hiba a minőségi besorolás törlésekor: {result}")
                        st.session_state[f"confirm_delete_grade_{grade_id}"] = False
                else:
                    st.warning(f"Biztosan törölni szeretné a '{selected_grade_data.get('name')}' besorolást? Ez a művelet nem vonható vissza!")
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        if st.button("Igen, törlés", key=f"confirm_yes_grade_{grade_id}", type="primary", use_container_width=True):
                            st.session_state[f"confirm_delete_grade_{grade_id}"] = True
                            st.rerun()
                    
                    with col2:
                        if st.button("Mégsem", key=f"confirm_no_grade_{grade_id}", use_container_width=True):
                            st.session_state[f"confirm_delete_grade_{grade_id}"] = False
                            st.rerun()

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Termékek kezelése - {config.APP_NAME}",
        page_icon="🍅",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a termékkezelő oldalt
    show_admin_products()
