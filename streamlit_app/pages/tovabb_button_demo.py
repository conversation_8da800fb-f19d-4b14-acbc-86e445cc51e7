# TOVÁBB Button Demo oldal
"""
Egyszerű "TOVÁBB" gomb használat bemutató oldal.
"""
import streamlit as st
from components.continue_button import render_continue_button
from utils.page_utils import set_page_config

# Oldal beállítások
set_page_config("TOVÁBB Button Demo", "➡️")

# Oldal tartalma
st.title("TOVÁBB Gomb Demo")

st.markdown("""
Ez az oldal bemutatja az egyszerű "TOVÁBB" gomb használatát egy űrlapon.

A példa egy egyszerű űrlapot mutat be, amely a "TOVÁBB" gomb megnyomásakor menti az adatokat.
""")

# Űrlap mezők
st.subheader("Adatok megadása")

name = st.text_input("Név")
email = st.text_input("E-mail cím")
message = st.text_area("Üzenet")

# TOVÁBB gomb megjelenítése
if render_continue_button():
    # <PERSON><PERSON> mentése
    if name and email:
        st.success(f"Az adatok sikeresen mentve: {name}, {email}")
        
        # Adatok megjelenítése
        st.subheader("Mentett adatok")
        st.json({
            "name": name,
            "email": email,
            "message": message
        })
    else:
        st.error("A név és e-mail cím megadása kötelező!") 