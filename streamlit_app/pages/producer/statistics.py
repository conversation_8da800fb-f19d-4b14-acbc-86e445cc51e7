# Termelői statisztikák
"""
Termelői statisztik<PERSON> oldal.
"""
import streamlit as st
import datetime as dt
import pandas as pd
from api import offers as offers_api
from api import products as products_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error
from components.data_display import (
    display_status_chart, 
    display_quantity_by_product_chart, 
    display_monthly_quantity_chart,
    display_price_comparison_chart
)
from utils.session import is_authenticated, get_current_user
from utils.formatting import format_price, format_quantity
import app_config as config

def show_producer_statistics():
    """
    Termelői statisztikák oldal megjelenítése.
    """
    st.title("Statisztikáim")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasz<PERSON><PERSON>ó adatainak lekérése
    user = get_current_user()
    
    # Szűrési beállítások
    with st.expander("Szűrési beállítások", expanded=True):
        # Időszak választása
        st.subheader("Időszak választása")
        
        date_option = st.radio(
            "Statisztika időszaka",
            options=["Az összes ajánlat", "Idei év", "Előző év", "Egyéni időszak"],
            horizontal=True
        )
        
        # Dátum paraméterek beállítása
        params = {}
        
        if date_option == "Idei év":
            current_year = dt.datetime.now().year
            params["date_from"] = f"{current_year}-01-01"
            params["date_to"] = f"{current_year}-12-31"
        elif date_option == "Előző év":
            previous_year = dt.datetime.now().year - 1
            params["date_from"] = f"{previous_year}-01-01"
            params["date_to"] = f"{previous_year}-12-31"
        elif date_option == "Egyéni időszak":
            col1, col2 = st.columns(2)
            with col1:
                from_date = st.date_input("Kezdő dátum", value=dt.date.today() - dt.timedelta(days=30))
            with col2:
                to_date = st.date_input("Záró dátum", value=dt.date.today())
            
            params["date_from"] = from_date.strftime("%Y-%m-%d")
            params["date_to"] = to_date.strftime("%Y-%m-%d")
        
        # Először lekérjük az időszak ajánlatait a termékek szűréséhez
        success_period_offers, period_offers = offers_api.get_offers(params=params)
        
        if not success_period_offers:
            st.error(f"Hiba az időszak ajánlatainak lekérésekor: {period_offers}")
            return
        
        # Elérhető termékek az időszakban
        available_products = {}
        available_categories = set()
        available_types = {}
        available_grades = {}
        
        if period_offers:
            for offer in period_offers:
                # Kategória gyűjtése
                if offer.get("category_id"):
                    available_categories.add(offer.get("category_id"))
                
                # Terméktípus gyűjtése
                if offer.get("product_type_id"):
                    product_type_id = offer.get("product_type_id")
                    category_id = offer.get("category_id")
                    if category_id not in available_types:
                        available_types[category_id] = set()
                    available_types[category_id].add(product_type_id)
                
                # Minőségi besorolás gyűjtése
                if offer.get("quality_grade_id"):
                    product_type_id = offer.get("product_type_id")
                    if product_type_id not in available_grades:
                        available_grades[product_type_id] = set()
                    available_grades[product_type_id].add(offer.get("quality_grade_id"))
        
        # Termék szűrők
        st.subheader("Termék szűrők")
        
        # Termékkategóriák betöltése
        success_categories, categories = products_api.get_product_categories()
        if success_categories and categories:
            # Szűrjük a kategóriákat az időszak alapján
            filtered_categories = [
                cat for cat in categories 
                if not available_categories or cat.get("id") in available_categories
            ]
            
            selected_category = st.selectbox(
                "Termékkategória",
                options=[{"id": None, "name": "Minden kategória"}] + filtered_categories,
                format_func=lambda x: x.get("name", ""),
                key="category_filter"
            )
            
            selected_category_id = selected_category.get("id")
            if selected_category_id:
                params["category_id"] = selected_category_id
            
            # Terméktípusok betöltése a kiválasztott kategóriához
            success_types, product_types = products_api.get_product_types(
                category_id=selected_category_id if selected_category_id else None
            )
            
            if success_types and product_types:
                # Szűrjük a terméktípusokat az időszak és kategória alapján
                filtered_types = [
                    pt for pt in product_types 
                    if not available_types or (
                        selected_category_id in available_types and 
                        pt.get("id") in available_types[selected_category_id]
                    )
                ]
                
                selected_type = st.selectbox(
                    "Terméktípus",
                    options=[{"id": None, "name": "Minden típus"}] + filtered_types,
                    format_func=lambda x: x.get("name", ""),
                    key="type_filter"
                )
                
                selected_type_id = selected_type.get("id")
                if selected_type_id:
                    params["product_type_id"] = selected_type_id
                    
                    # Minőségi besorolások betöltése
                    if selected_type.get("has_quality_grades", False):
                        success_grades, quality_grades = products_api.get_quality_grades(selected_type_id)
                        
                        if success_grades and quality_grades:
                            # Szűrjük a minőségi besorolásokat az időszak és terméktípus alapján
                            filtered_grades = [
                                grade for grade in quality_grades 
                                if not available_grades or (
                                    selected_type_id in available_grades and 
                                    grade.get("id") in available_grades[selected_type_id]
                                )
                            ]
                            
                            selected_grade = st.selectbox(
                                "Minőségi besorolás",
                                options=[{"id": None, "name": "Minden minőség"}] + filtered_grades,
                                format_func=lambda x: x.get("name", ""),
                                key="grade_filter"
                            )
                            
                            if selected_grade.get("id"):
                                params["quality_grade_id"] = selected_grade.get("id")
            else:
                st.warning("Nem sikerült betölteni a terméktípusokat.")
        else:
            st.warning("Nem sikerült betölteni a termékkategóriákat.")
        
        # Szűrők állapotának megjelenítése
        active_filters = []
        if selected_category_id:
            active_filters.append(f"Kategória: {selected_category.get('name')}")
        if selected_type_id:
            active_filters.append(f"Típus: {selected_type.get('name')}")
        if 'selected_grade' in locals() and selected_grade.get("id"):
            active_filters.append(f"Minőség: {selected_grade.get('name')}")
        
        if active_filters:
            st.markdown("**Aktív szűrők:**")
            for filter_text in active_filters:
                st.markdown(f"- {filter_text}")
    
    # Debug: print request parameters
    print("==== REPORT API REQUEST ====")
    print(f"Request params: {params}")
    print("===========================")
    
    # Ajánlatok lekérése a backenddől a megadott szűrők alapján
    success, result = offers_api.get_offers(params=params)
    
    if success:
        offers = result
        
        if offers:
            # Statisztikák megjelenítése
            st.subheader(f"Statisztikai adatok ({len(offers)} ajánlat alapján)")
            
            # Összesített adatok - fix quantity type conversion
            total_quantity = sum(float(offer.get("quantity_in_kg", 0)) if isinstance(offer.get("quantity_in_kg"), (int, float, str)) and offer.get("quantity_in_kg") != "" else 0 for offer in offers)
            
            # Csak a véglegesített ajánlatokat vesszük figyelembe az ár statisztikákhoz
            finalized_offers = [offer for offer in offers if offer.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"]]
            
            total_finalized_quantity = sum(float(offer.get("confirmed_quantity", 0)) if isinstance(offer.get("confirmed_quantity"), (int, float, str)) and offer.get("confirmed_quantity") != "" else 0 for offer in finalized_offers)
            total_value = sum(float(offer.get("confirmed_quantity", 0)) * float(offer.get("confirmed_price", 0)) if isinstance(offer.get("confirmed_quantity"), (int, float, str)) and isinstance(offer.get("confirmed_price"), (int, float, str)) and offer.get("confirmed_quantity") != "" and offer.get("confirmed_price") != "" else 0 for offer in finalized_offers)
            
            # Metrikai kártyák
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    label="Összes felajánlott mennyiség",
                    value=format_quantity(total_quantity)
                )
            
            with col2:
                st.metric(
                    label="Véglegesített mennyiség",
                    value=format_quantity(total_finalized_quantity),
                    delta=f"{(total_finalized_quantity / total_quantity * 100):.1f}%" if total_quantity > 0 else "0%"
                )
            
            with col3:
                st.metric(
                    label="Teljes érték",
                    value=format_price(total_value)
                )
            
            # Státusz diagram
            st.subheader("Ajánlataim megoszlása státusz szerint")
            if any(offer.get("status") for offer in offers):
                display_status_chart(offers)
            else:
                st.info("Nincs megjeleníthető státusz adat.")
            
            # Termékek szerinti megoszlás
            st.subheader("Mennyiség megoszlása termékek szerint")
            if any(offer.get("product_name") and offer.get("quantity_in_kg") for offer in offers):
                display_quantity_by_product_chart(offers)
            else:
                st.info("Nincs megjeleníthető termék mennyiség adat.")
            
            # Időbeli eloszlás
            st.subheader("Havi összesített mennyiségek")
            if any(offer.get("delivery_date") and offer.get("quantity_in_kg") for offer in offers):
                display_monthly_quantity_chart(offers)
            else:
                st.info("Nincs megjeleníthető időbeli mennyiség adat.")
            
            # Árak statisztikája (ha vannak visszaigazolt árak)
            has_price_data = any(
                offer.get("confirmed_price") is not None or 
                offer.get("price") is not None 
                for offer in offers
            )
            
            if has_price_data:
                st.subheader("Átlagárak termékenként")
                display_price_comparison_chart(offers, group_by="product")
                
                st.subheader("Átlagárak alakulása havi bontásban")
                display_price_comparison_chart(offers, group_by="month")
            else:
                st.info("Nincs megjeleníthető áradat.")
            
            # Ajánlat elfogadási arány
            st.subheader("Ajánlat elfogadási arány")
            
            # Státuszok számolása
            status_counts = {}
            for status in config.OFFER_STATUSES.keys():
                status_counts[status] = sum(1 for offer in offers if offer.get("status") == status)
            
            # Elfogadási ráta számítása
            total_responded = status_counts.get("ACCEPTED_BY_USER", 0) + status_counts.get("REJECTED_BY_USER", 0) + status_counts.get("FINALIZED", 0)
            total_confirmed = status_counts.get("CONFIRMED_BY_COMPANY", 0) + total_responded
            
            acceptance_rate = (status_counts.get("ACCEPTED_BY_USER", 0) + status_counts.get("FINALIZED", 0)) / total_responded if total_responded > 0 else 0
            
            # Elfogadási ráta megjelenítése
            st.progress(acceptance_rate, text=f"Elfogadási arány: {acceptance_rate * 100:.1f}%")
            
            st.write(f"""
            - **Összes ajánlat:** {len(offers)}
            - **Feldolgozva (visszaigazolva):** {total_confirmed} ({total_confirmed / len(offers) * 100:.1f}%)
            - **Elfogadva/véglegesítve:** {status_counts.get("ACCEPTED_BY_USER", 0) + status_counts.get("FINALIZED", 0)}
            - **Elutasítva:** {status_counts.get("REJECTED_BY_USER", 0)}
            - **Függőben:** {status_counts.get("CREATED", 0) + status_counts.get("CONFIRMED_BY_COMPANY", 0)}
            """)
            
            # Adatok exportálása
            st.subheader("Adatok exportálása")
            if st.button("Adatok exportálása CSV formátumban"):
                # Adatok átalakítása DataFrame-re
                df = pd.DataFrame(offers)
                
                # CSV előkészítése
                csv = df.to_csv(index=False).encode('utf-8')
                
                # Letöltés gomb
                now = dt.datetime.now().strftime("%Y%m%d_%H%M%S")
                download_filename = f"ajanlataim_export_{now}.csv"
                
                st.download_button(
                    label="CSV letöltése",
                    data=csv,
                    file_name=download_filename,
                    mime="text/csv",
                )
        else:
            st.info("Nincsenek megjeleníthető ajánlatok a megadott időszakban.")
    else:
        show_error(f"Hiba az ajánlatok lekérésekor: {result}")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Statisztikáim - {config.APP_NAME}",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a termelői statisztikákat
    show_producer_statistics()
