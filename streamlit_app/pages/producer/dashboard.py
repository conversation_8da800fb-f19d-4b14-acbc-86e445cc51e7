# Termelői irányítópult
"""
Termelői irányítópult oldal.
"""
import streamlit as st
import datetime
from api import offers as offers_api
from api import users as users_api  # Adj hozzá egy import sort
from components.sidebar import render_sidebar
from components.notification import show_info, show_error
from components.data_display import display_offer_table, display_status_chart
from components.activity_feed import render_dashboard_widgets
from utils.session import is_authenticated, get_current_user, init_session_state
from utils.formatting import format_datetime, format_date
from utils.config import get_page_title
import uuid

def load_settings_to_session_state():
    """
    Beállítások betöltése a session state-be a backend API-ból.
    Ez biztosítja, hogy a beállítások elérhetőek legyenek akkor is, ha a session state elveszne.
    """
    # Lekérjük a beállításokat a backendről
    success, settings = users_api.get_user_default_settings()
    
    # Mindig írjuk ki a konzolra a teljes API választ a diagnosztikához
    print("==== API BEÁLLÍTÁSOK VÁLASZ ====")
    print(f"API hívás sikeres: {success}")
    print(f"API válasz: {settings}")
    print("==== API VÁLASZ RÉSZLETEK ====")
    if success and isinstance(settings, dict):
        for key, value in settings.items():
            print(f"{key}: {value}")
    print("==== API VÁLASZ VÉGE ====")
    
    # Csak akkor töltünk be adatokat, ha a settings ténylegesen nem üres
    if success and isinstance(settings, dict) and settings:
        # Adattisztítás: "NULL" értékek None-ra cserélése
        for key in settings:
            if settings[key] == "NULL":
                settings[key] = None
        
        # Létrehozzuk a user_settings objektumot a session state-ben
        st.session_state.user_settings = {
            "default_product_type": {
                "id": settings.get("default_product_type_id"),
                "name": settings.get("default_product_type_name", ""),
                "has_quality_grades": settings.get("has_quality_grades", False)
            },
            "default_quality_grade": {
                "id": settings.get("default_quality_grade_id"),
                "name": settings.get("default_quality_grade_name", "")
            },
            "default_quantity_unit": settings.get("default_quantity_unit", "kg"),
            "category": {
                "id": settings.get("default_category_id"),
                "name": settings.get("default_category_name", "")
            }
        }
        print("Beállítások sikeresen betöltve a session state-be a backendről")
        return True
    else:
        print("Nem sikerült betölteni a beállításokat - hiányos vagy üres API válasz")
        return False

def show_producer_dashboard():
    """
    Termelői irányítópult megjelenítése.
    """
    # Ne hívjuk az init_session_state-et itt, mert felülírhatja a bejelentkezési adatokat
    
    # Session state inicializálása a needs_rerun flag-hez
    if "needs_rerun" not in st.session_state:
        st.session_state.needs_rerun = False
    
    # Ha az előző művelet után újratöltés szükséges
    if st.session_state.needs_rerun:
        st.session_state.needs_rerun = False
        st.rerun()
    
    # Debug: Kiírjuk a session state tartalmát
    print(f"Producer dashboard: page_uuid={st.session_state.get('page_uuid', 'nem létezik')}")
    
    # Beállítjuk a debug módot az oldal tetejére
    debug_mode = False
    
    st.title("Termelői Irányítópult")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Betöltjük a beállításokat a session state-be - mindig megpróbáljuk frissíteni
    if "user_settings" in st.session_state:
        del st.session_state.user_settings
    load_success = load_settings_to_session_state()
    
    # Debug információ megjelenítése az oldalon, ha engedélyezett
    if debug_mode:
        with st.expander("Debug Információ", expanded=False):
            st.write("### API Válasz Debug")
            success, settings = users_api.get_user_default_settings()
            st.write(f"API hívás sikeres: {success}")
            st.write(f"API válasz: {settings}")
            st.write("### Session State Debug")
            if "user_settings" in st.session_state:
                st.write("Session state tartalmaz beállításokat:")
                st.write(st.session_state.user_settings)
            else:
                st.write("Session state NEM tartalmaz beállításokat!")
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Üdvözlő üzenet
    st.write(f"### Üdvözöljük, {user.get('contact_name')}!")
    
    # Mai dátum és idő
    now = datetime.datetime.now()
    st.write(f"Dátum: {now.strftime('%Y. %m. %d.')} - {now.strftime('%H:%M')}")
    
    # Új tevékenységi widget megjelenítése
    render_dashboard_widgets()
    
    st.write("---")  # Elválasztó vonal
    
    # Irányítópult főbb elemei
    col1, col2 = st.columns(2)
    
    with col1:
        # Gyors áttekintés keret
        with st.container(border=True):
            st.subheader("Gyors áttekintés")
            
            # Ajánlatok lekérése a backenddől
            success, result = offers_api.get_offers()
            
            if success:
                offers = result
                
                # Különböző státuszú ajánlatok számának kiszámítása
                total_offers = len(offers)
                pending_offers = sum(1 for o in offers if o.get("status") == "CREATED")
                confirmed_offers = sum(1 for o in offers if o.get("status") == "CONFIRMED_BY_COMPANY")
                waiting_approval = sum(1 for o in offers if o.get("status") in ["ACCEPTED_BY_USER", "REJECTED_BY_USER"])
                finalized_offers = sum(1 for o in offers if o.get("status") == "FINALIZED")
                
                # Megjelenítés metrika kártyákon
                metric_col1, metric_col2 = st.columns(2)
                with metric_col1:
                    st.metric(label="Összes ajánlat", value=total_offers)
                    st.metric(label="Jóváhagyásra vár", value=waiting_approval)
                
                with metric_col2:
                    st.metric(label="Függőben", value=pending_offers)
                    st.metric(label="Visszaigazolva", value=confirmed_offers)
                
                # Gyors műveletek
                st.write("### Gyors műveletek")
                button_col1, button_col2 = st.columns(2)
                
                with button_col1:
                    if st.button("Új ajánlat létrehozása", type="primary", use_container_width=True):
                        st.switch_page("pages/producer_create_offer.py")
                
                with button_col2:
                    if st.button("Ajánlataim megtekintése", use_container_width=True):
                        st.switch_page("pages/producer_offers.py")
            else:
                show_error(f"Hiba az ajánlatok lekérésekor: {result}")
    
    with col2:
        # Visszaigazolásra váró ajánlatok keret
        with st.container(border=True):
            st.subheader("Visszaigazolásra váró ajánlatok")
            
            # Lekérjük a visszaigazolt, de még nem kezelt ajánlatokat
            waiting_confirmation_param = {"status": "CONFIRMED_BY_COMPANY"}
            success, result = offers_api.get_offers(params=waiting_confirmation_param)
            
            if success:
                waiting_offers = result
                
                if waiting_offers:
                    # Táblázat készítése az ajánlatokról
                    df = display_offer_table(waiting_offers, with_actions=True, pagination=False)
                    
                    # Interaktív gombok az ajánlatokhoz
                    for _, row in df.iterrows():
                        offer_id = row["ID"]  # Changed from "Azonosító" to "ID"
                        st.write(f"**Ajánlat #{offer_id}** műveletek:")
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.button(f"Elfogad #{offer_id}", key=f"accept_{offer_id}", 
                                        type="primary", use_container_width=True):
                                success, result = offers_api.accept_offer(offer_id)
                                if success:
                                    show_info("Ajánlat sikeresen elfogadva!")
                                    # Beállítjuk a flag-et és hagyjuk a kód tetején lévő ellenőrzést újratölteni
                                    if "needs_rerun" not in st.session_state:
                                        st.session_state.needs_rerun = True
                                    else:
                                        st.session_state.needs_rerun = True
                                else:
                                    show_error(f"Hiba az ajánlat elfogadásakor: {result}")
                        
                        with col2:
                            if st.button(f"Elutasít #{offer_id}", key=f"reject_{offer_id}", 
                                        type="secondary", use_container_width=True):
                                success, result = offers_api.reject_offer(offer_id)
                                if success:
                                    show_info("Ajánlat sikeresen elutasítva!")
                                    # Beállítjuk a flag-et és hagyjuk a kód tetején lévő ellenőrzést újratölteni
                                    if "needs_rerun" not in st.session_state:
                                        st.session_state.needs_rerun = True
                                    else:
                                        st.session_state.needs_rerun = True
                                else:
                                    show_error(f"Hiba az ajánlat elutasításakor: {result}")
                        
                        st.divider()
                else:
                    st.info("Jelenleg nincs visszaigazolásra váró ajánlata.")
            else:
                show_error(f"Hiba az ajánlatok lekérésekor: {result}")

        # Alapértelmezett beállítások konténer
        with st.container(border=True):
            st.subheader("Alapértelmezett beállítások")
            
            # Alapértelmezett beállítások lekérése
            success, settings = users_api.get_user_default_settings()
            
            if success and settings:
                # Lekérdezzük a tényleges termék adatokat az ID-k alapján
                product_type_id = settings.get('default_product_type_id')
                quality_grade_id = settings.get('default_quality_grade_id')
                quantity_unit = settings.get('default_quantity_unit', 'kg')
                
                # Ha van terméktípus ID, lekérjük a terméktípus adatait
                product_type = None
                category = None
                if product_type_id:
                    from api import products as products_api
                    # Először lekérjük a kategóriákat
                    cat_success, categories = products_api.get_product_categories()
                    
                    # Majd lekérjük a terméktípusokat
                    # Végigmegyünk az összes kategórián, és megkeressük a terméktípust
                    if cat_success:
                        for cat in categories:
                            types_success, product_types = products_api.get_product_types(category_id=cat.get('id'))
                            if types_success:
                                found_product_type = next((pt for pt in product_types if pt.get('id') == product_type_id), None)
                                if found_product_type:
                                    product_type = found_product_type
                                    category = cat
                                    break
                
                # Ha van minőségi besorolás ID, lekérjük annak adatait
                quality_grade = None
                has_quality_grades = False
                if product_type_id and quality_grade_id:
                    from api import products as products_api
                    grades_success, quality_grades = products_api.get_quality_grades(product_type_id=product_type_id)
                    if grades_success:
                        has_quality_grades = len(quality_grades) > 0
                        quality_grade = next((grade for grade in quality_grades if grade.get('id') == quality_grade_id), None)
                
                # Megjelenítjük az alapértelmezett beállításokat
                st.write("**Alapértelmezett termék:**")
                
                # Lista létrehozása a megjelenítendő adatokról
                settings_items = []
                
                # Kategória név megjelenítése
                if category:
                    settings_items.append(f"**Kategória:** {category.get('name', 'Nincs beállítva')}")
                else:
                    settings_items.append("**Kategória:** Nincs beállítva")
                
                # Terméktípus név megjelenítése
                if product_type:
                    settings_items.append(f"**Típus:** {product_type.get('name', 'Nincs beállítva')}")
                else:
                    settings_items.append("**Típus:** Nincs beállítva")
                
                # Minőségi besorolás kezelése
                if has_quality_grades:
                    if quality_grade:
                        settings_items.append(f"**Minőségi besorolás:** {quality_grade.get('name', 'Nincs beállítva')}")
                    else:
                        settings_items.append("**Minőségi besorolás:** Nincs beállítva")
                else:
                    settings_items.append("**Minőségi besorolás:** Nem alkalmazható")
                
                # Mértékegység hozzáadása
                settings_items.append(f"**Mértékegység:** {quantity_unit}")
                
                # Pontokba szedett lista megjelenítése
                for item in settings_items:
                    st.markdown(f"- {item}")
                
                # Gombok a műveletek végrehajtásához
                col1, col2 = st.columns(2)
                
                with col1:
                    # Módosítás gomb
                    if st.button("Beállítások módosítása", use_container_width=True):
                        st.switch_page("pages/producer_profile.py")
                
                with col2:
                    # Újratöltés gomb
                    if st.button("Beállítások újratöltése", use_container_width=True):
                        # Töröljük a beállításokat a session state-ből, hogy újratöltődjön
                        if "user_settings" in st.session_state:
                            del st.session_state.user_settings
                        # Újratöltjük a beállításokat
                        load_settings_to_session_state()
                        # Újratöltjük az oldalt
                        st.rerun()
            else:
                st.info("Még nincsenek alapértelmezett beállításai")
                if st.button("Beállítások megadása", type="primary", use_container_width=True):
                    st.switch_page("pages/producer_profile.py")

    
    # Ajánlatok statisztikái
    st.write("## Ajánlataim statisztikái")
    
    if 'offers' in locals() and offers:
        # Ajánlatok státusz szerinti megoszlása
        display_status_chart(offers)
    
    # Footer
    st.write("---")
    st.write("További funkciók eléréséhez használja az oldalsávon található menüt.")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=get_page_title("Termelői Irányítópult"),
        page_icon="🏠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a termelői irányítópultot
    show_producer_dashboard()
