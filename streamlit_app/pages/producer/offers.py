# Ajánlatok kezelése
"""
Termelői ajánlatok listázása oldal.
"""
import streamlit as st
import datetime
from api import offers as offers_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success, toast_notification
from components.data_display import display_offer_table
from components.display_data_components import (
    display_offer_status_card,
    display_offer_detail_cards,
    display_quantity_indicator,
    display_status_history,
    display_offer_attachments,
    display_offer_actions,
)
from utils.session import is_authenticated, get_current_user, init_session_state
from utils.formatting import format_status
import app_config as config
import uuid

# Session state inicializálása
if "needs_rerun" not in st.session_state:
    st.session_state.needs_rerun = False

def show_producer_offers():
    """
    Termelői ajánlatok listázása oldal megjelenítése.
    """
    # Biztosítjuk, hogy a page_uuid inicializálva van
    init_session_state()
    
    # Debug: Kiírjuk a session state tartalmát
    print(f"Producer offers: page_uuid={st.session_state.get('page_uuid', 'nem létezik')}")
    
    # Ha az előző művelet után újratöltés szükséges
    if st.session_state.get("needs_rerun", False):
        # Csak visszaállítjuk a flaget, a Streamlit automatikusan újratölti az oldalt
        # amikor a session state változik
        st.session_state.needs_rerun = False
        
    st.title("Ajánlataim")
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    user = get_current_user()
    
    # Szűrési lehetőségek
    st.subheader("Szűrési lehetőségek")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Státusz szűrő
        status_options = ["Összes"] + [format_status(status) for status in config.OFFER_STATUSES.keys()]
        selected_status = st.selectbox("Státusz", options=status_options)
    
    with col2:
        # Dátum szűrő
        date_filter = st.selectbox(
            "Dátum szerint", 
            options=["Összes", "Jövőbeni beszállítások", "Korábbi beszállítások", "Következő 7 nap", "Következő 30 nap"]
        )
    
    with col3:
        # Rendezés
        sort_by = st.selectbox(
            "Rendezés", 
            options=["Beszállítás dátuma (növekvő)", "Beszállítás dátuma (csökkenő)", "Létrehozás dátuma (növekvő)", "Létrehozás dátuma (csökkenő)"]
        )
    
    # Szűrési paraméterek összeállítása
    params = {}
    
    # Státusz szűrő
    # Csak akkor szűrj, ha NEM "Összes" van kiválasztva
    if selected_status and selected_status != "Összes":
        # Visszakeressük a státusz kódot a neve alapján
        for status_code, status_data in config.OFFER_STATUSES.items():
            if status_data["name"] == selected_status:
                params["status"] = status_code
                break
    # Ha "Összes" van kiválasztva, nem adunk hozzá státusz filtert, így minden ajánlat megjelenik.
    
    # Dátum szűrő - JAVÍTVA
    if date_filter != "Összes":
        params["date_filter"] = date_filter.lower().replace(" ", "_").replace("ö", "o").replace("é", "e").replace("ő", "o").replace("ű", "u").replace("á", "a").replace("í", "i")
        
        # Most határozzuk meg a konkrét dátumokat a backend számára
        today = datetime.date.today()
        
        if date_filter == "Jövőbeni beszállítások":
            params["date_from"] = today.isoformat()
        elif date_filter == "Korábbi beszállítások":
            params["date_to"] = today.isoformat()
        elif date_filter == "Következő 7 nap":
            params["date_from"] = today.isoformat()
            params["date_to"] = (today + datetime.timedelta(days=7)).isoformat()
        elif date_filter == "Következő 30 nap":
            params["date_from"] = today.isoformat()
            params["date_to"] = (today + datetime.timedelta(days=30)).isoformat()
    
    # Rendezés
    if sort_by == "Beszállítás dátuma (növekvő)":
        params["sort_by"] = "delivery_date"
        params["sort_order"] = "asc"
    elif sort_by == "Beszállítás dátuma (csökkenő)":
        params["sort_by"] = "delivery_date"
        params["sort_order"] = "desc"
    elif sort_by == "Létrehozás dátuma (növekvő)":
        params["sort_by"] = "created_at"
        params["sort_order"] = "asc"
    elif sort_by == "Létrehozás dátuma (csökkenő)":
        params["sort_by"] = "created_at"
        params["sort_order"] = "desc"
    
    print(f"Params for API call: {params}")
    
    # Ajánlatok lekérése a backenddől
    success, result = offers_api.get_offers(params=params)
    
    if success:
        offers = result
        
        # Ha vannak ajánlatok, megjelenítjük őket
        if offers:
            # Ajánlatok megjelenítése táblázatban
            st.subheader(f"Ajánlatok listája ({len(offers)} találat)")
            df = display_offer_table(offers, with_actions=True)
            # Műveletek az ajánlatokhoz
            if df is not None:
                st.write("### Műveletek")
                
                for index, row in df.iterrows():
                    offer_id = row["ID"]
                    
                    # Részletes ajánlat adatok lekérése
                    success, offer = offers_api.get_offer(offer_id)
                    
                    if success:
                        # Ajánlat adatainak megjelenítése
                        with st.expander(f"Ajánlat #{offer_id} részletei"):
                            display_offer_status_card(offer)
                            display_offer_detail_cards(offer)
                            if offer.get("confirmed_quantity") is not None:
                                display_quantity_indicator(offer)
                            display_status_history(offer_id)
                            display_offer_attachments(offer_id)
                            display_offer_actions(offer)
                        
                        st.divider()
                    else:
                        show_error(f"Hiba az ajánlat részleteinek lekérésekor: {offer}")
        else:
            st.info("Nincsenek megjeleníthető ajánlatok a megadott szűrési feltételek alapján.")
    else:
        show_error(f"Hiba az ajánlatok lekérésekor: {result}")
    
    # Új ajánlat létrehozása gomb
    if st.button("Új ajánlat létrehozása", type="primary", key="create_new_offer"):
        st.switch_page("pages/producer_create_offer.py")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Ajánlataim - {config.APP_NAME}",
        page_icon="📋",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a termelői ajánlatok listáját
    show_producer_offers()
