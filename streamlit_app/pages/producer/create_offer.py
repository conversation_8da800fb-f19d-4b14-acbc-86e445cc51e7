"""
Termelői aj<PERSON>lat létrehoz<PERSON>a oldal.
"""
import streamlit as st
import datetime
from api import products as products_api
from api import users as users_api
from api import offers as offers_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user, init_session_state
from utils.validators import validate_required, validate_numeric, validate_date
import app_config as config
import requests
from sqlalchemy.orm import Session
from typing import Optional

# Session state inicializálása
for key in ["selected_category_id", "selected_type_id", "product_types", "quality_grades"]:
    if key not in st.session_state:
        st.session_state[key] = None if "id" in key else []

# Termékadatok betöltése backendből
def load_product_data():
    # Termékkategóriák lekérdezése
    success, categories = products_api.get_product_categories()
    
    if not success:
        st.error("Nem si<PERSON>ült lek<PERSON>rde<PERSON>ni a termékkategóriákat.")
        return None
    
    return categories

# Callback függvények definiálása a dinamikus dropdown-okhoz - form-on kív<PERSON>l használva
def update_types_by_category(selected_category, categories):
    if not selected_category:
        return [], None
    
    category = next((cat for cat in categories if cat["name"] == selected_category), None)
    if not category:
        return [], None
    
    st.session_state.selected_category_id = category["id"]
    success, types = products_api.get_product_types(category_id=category["id"])
    if success:
        # DEBUG: terméktípusok kiíratása konzolra
        print(f"Kategória: {selected_category}, típusok: {[t.get('name') for t in types]}")
        return types, None
    else:
        return [], f"Hiba a típusok betöltésekor: {types}"

def update_grades_by_type(selected_type, product_types):
    if not selected_type or not product_types:
        return [], None
    
    type_obj = next((typ for typ in product_types if typ["name"] == selected_type), None)
    if not type_obj:
        # DEBUG: Ha nem találtuk a terméktípust
        print(f"Nem találom a terméktípust: '{selected_type}' - Elérhető típusok: {[t.get('name') for t in product_types]}")
        return [], None
    
    st.session_state.selected_type_id = type_obj["id"]
    if type_obj["has_quality_grades"]:
        success, grades = products_api.get_quality_grades(product_type_id=type_obj["id"])
        if success:
            return grades, None
        else:
            return [], f"Hiba a minőségi besorolások betöltésekor: {grades}"
    else:
        return [], None

# Debug panel for default values handling
def show_default_values_debug():
    with st.expander("Alapértelmezett beállítások debug"):
        st.write("### Default Settings Load Debug")
        
        # Retrieve user settings with API
        success, user_settings = users_api.get_user_default_settings()
        
        st.write(f"API válasz sikeres: {success}")
        
        if success:
            # Process NULL strings to None
            for key, value in user_settings.items():
                if value == "NULL":
                    st.warning(f"❗ '{key}' értéke 'NULL' string! Konvertálás None-ra.")
                    user_settings[key] = None
            
            st.json(user_settings)
            
            st.write("### Default értékek feldolgozása")
            
            # Variables to track if values were found
            default_category_found = False 
            default_type_found = False
            default_quality_found = False
            
            # Check if we have a default category
            if user_settings.get('default_category_id'):
                st.write(f"✅ Alapértelmezett kategória ID: {user_settings.get('default_category_id')}")
                default_category_found = True
            else:
                st.write("❌ Nincs alapértelmezett kategória ID")
            
            # Check if we have a default product type
            if user_settings.get('default_product_type_id'):
                st.write(f"✅ Alapértelmezett terméktípus ID: {user_settings.get('default_product_type_id')}")
                default_type_found = True
            else:
                st.write("❌ Nincs alapértelmezett terméktípus ID")
            
            # Check if we have quality grade
            if user_settings.get('default_quality_grade_id'):
                st.write(f"✅ Alapértelmezett minőségi besorolás ID: {user_settings.get('default_quality_grade_id')}")
                default_quality_found = True
            else:
                st.write("❌ Nincs alapértelmezett minőségi besorolás ID")
            
            # Show quantity unit
            if user_settings.get('default_quantity_unit'):
                st.write(f"✅ Alapértelmezett mértékegység: {user_settings.get('default_quantity_unit')}")
            else:
                st.write("❌ Nincs alapértelmezett mértékegység megadva")
            
            # Available categories
            success, categories = products_api.get_product_categories()
            if success:
                cat_names = [cat.get('name') for cat in categories]
                st.write(f"Elérhető kategóriák: {', '.join(cat_names)}")
                
                if default_category_found:
                    cat_obj = next((cat for cat in categories if cat.get('id') == user_settings.get('default_category_id')), None)
                    if cat_obj:
                        st.write(f"✅ Alapértelmezett kategória neve: {cat_obj.get('name')}")
                        
                        # Check product types
                        success, product_types = products_api.get_product_types(category_id=cat_obj.get('id'))
                        if success:
                            type_names = [pt.get('name') for pt in product_types]
                            st.write(f"Elérhető terméktípusok: {', '.join(type_names)}")
                            
                            if default_type_found:
                                type_obj = next((pt for pt in product_types if pt.get('id') == user_settings.get('default_product_type_id')), None)
                                if type_obj:
                                    st.write(f"✅ Alapértelmezett terméktípus neve: {type_obj.get('name')}")
                                else:
                                    st.write("❌ Alapértelmezett terméktípus nem található az elérhető típusok között")
                        else:
                            st.write(f"❌ Hiba a terméktípusok lekérésekor: {product_types}")
                    else:
                        st.write("❌ Alapértelmezett kategória nem található az elérhető kategóriák között")

def show_create_offer():
    st.title("Új ajánlat létrehozása")
    
    # Don't call init_session_state here as it might overwrite login data
    
    # Check for authentication
    if not is_authenticated():
        st.warning("Ez az oldal bejelentkezést igényel.")
        return
    
    user = get_current_user()
    if not user or user.get("role", "").lower() != "termelő":
        st.error("Ehhez a funkcióhoz termelői jogosultság szükséges.")
        return
    
    # Debug mód ellenőrzése - nincs itt külön kapcsoló, csak a globális jelenik meg adminoknak
    is_admin = user and user.get("role", "").lower() == "admin"
    
    # Debug panel megjelenítése, ha a debug mód be van kapcsolva és admin a felhasználó
    if st.session_state.get("debug_mode", False) and is_admin:
        show_default_values_debug()
    
    # Inicializáljuk a session state-et, vagy frissítjük minden alkalommal
    # Lekérdezzük a felhasználói beállításokat
    success, settings = users_api.get_user_default_settings()
    
    if success:
        # Biztonságos konverzió: NULL stringek -> None, None értékek biztonságos kezelése
        if isinstance(settings, dict):
            cleaned_settings = {}
            for key, value in settings.items():
                if isinstance(value, str) and value == "NULL":
                    cleaned_settings[key] = None
                else:
                    cleaned_settings[key] = value
            
            # Beállítások mentése a session state-be (tisztított formátumban)
            st.session_state.user_settings = cleaned_settings
            
            # Erőltetjük a belső session értékek törlését, hogy újra betöltődjenek
            if 'prev_category' in st.session_state:
                del st.session_state.prev_category
            if 'prev_type' in st.session_state:
                del st.session_state.prev_type
        else:
            # Ha nem dict formátumú, akkor is használható formátumban tároljuk
            st.session_state.user_settings = {
                "default_category_id": None,
                "default_product_type_id": None,
                "default_quality_grade_id": None,
                "default_quantity_unit": "kg"
            }
    else:
        # Ha nem sikerült betölteni, üres értékeket állítunk be
        st.session_state.user_settings = {
            "default_category_id": None,
            "default_product_type_id": None,
            "default_quality_grade_id": None,
            "default_quantity_unit": "kg"
        }
    
    # Helper functions to safely get values regardless of format
    def get_default_category_id():
        """
        A kategória ID biztonságos lekérdezése a session state-ből.
        """
        if isinstance(st.session_state.user_settings, dict):
            # Közvetlen hozzáférés
            category_id = st.session_state.user_settings.get("default_category_id")
            # Ellenőrizzük, hogy ne legyen "NULL" string vagy None
            if category_id and category_id != "NULL":
                return category_id
            # Alternatív formátum
            if "category" in st.session_state.user_settings and st.session_state.user_settings.get("category", {}).get("id") and st.session_state.user_settings.get("category", {}).get("id") != "NULL":
                return st.session_state.user_settings.get("category", {}).get("id")
        return None
    
    def get_default_product_type_id():
        """
        A terméktípus ID biztonságos lekérdezése a session state-ből.
        """
        if isinstance(st.session_state.user_settings, dict):
            # Közvetlen hozzáférés
            type_id = st.session_state.user_settings.get("default_product_type_id")
            # Ellenőrizzük, hogy ne legyen "NULL" string vagy None
            if type_id and type_id != "NULL":
                return type_id
            # Alternatív formátum
            if "default_product_type" in st.session_state.user_settings and st.session_state.user_settings.get("default_product_type", {}).get("id") and st.session_state.user_settings.get("default_product_type", {}).get("id") != "NULL":
                return st.session_state.user_settings.get("default_product_type", {}).get("id")
        return None
    
    def get_default_quality_grade_id():
        """
        A minőségi besorolás ID biztonságos lekérdezése a session state-ből.
        """
        if isinstance(st.session_state.user_settings, dict):
            # Közvetlen hozzáférés
            grade_id = st.session_state.user_settings.get("default_quality_grade_id")
            # Ellenőrizzük, hogy ne legyen "NULL" string vagy None
            if grade_id and grade_id != "NULL":
                return grade_id
            # Alternatív formátum
            if "default_quality_grade" in st.session_state.user_settings and st.session_state.user_settings.get("default_quality_grade", {}).get("id") and st.session_state.user_settings.get("default_quality_grade", {}).get("id") != "NULL":
                return st.session_state.user_settings.get("default_quality_grade", {}).get("id")
        return None
        
    def get_default_quality_grade_name():
        if isinstance(st.session_state.user_settings, dict):
            # Try direct access first
            if "default_quality_grade_name" in st.session_state.user_settings and st.session_state.user_settings["default_quality_grade_name"] is not None:
                return st.session_state.user_settings.get("default_quality_grade_name", "")
            # Fall back to nested format
            if "default_quality_grade" in st.session_state.user_settings and st.session_state.user_settings.get("default_quality_grade", {}).get("name") is not None:
                return st.session_state.user_settings.get("default_quality_grade", {}).get("name", "")
        return ""
        
    def get_default_quantity_unit():
        """
        Az alapértelmezett mértékegység biztonságos lekérdezése a session state-ből.
        """
        if isinstance(st.session_state.user_settings, dict):
            unit = st.session_state.user_settings.get("default_quantity_unit")
            # Ellenőrizzük, hogy ne legyen "NULL" string vagy None és hogy érvényes egység-e
            if unit and unit != "NULL" and unit in ["kg", "tonna", "db"]:
                return unit
        return "kg"  # Alapértelmezett érték, ha nincs megadva vagy érvénytelen
    
    # Termékkategóriák betöltése
    categories = load_product_data()
    
    if not categories:
        return
    
    # Default kategória beállítása, ha még nincs category_select a session state-ben
    if 'category_select' not in st.session_state:
        # Keressük meg az alapértelmezett kategóriát ID alapján
        default_category_id = get_default_category_id()
        
        # Debug üzenet csak debug módban
        if st.session_state.get("debug_mode", False):
            st.write(f"Debug: Alapértelmezett kategória ID: {default_category_id}")
        
        # Ha van alapértelmezett kategória ID
        if default_category_id is not None:
            # Keressük meg a kategória nevét az ID alapján
            for cat in categories:
                if cat.get('id') == default_category_id:
                    st.session_state.category_select = cat.get('name')
                    
                    # Debug üzenet csak debug módban
                    if st.session_state.get("debug_mode", False):
                        st.write(f"Debug: Kategória találat ID alapján: {cat.get('name')}")
                    break
        
        # Ha nem találtuk meg, vagy nincs alapértelmezett, az első kategóriát választjuk
        if 'category_select' not in st.session_state and categories:
            st.session_state.category_select = categories[0].get('name')
            
            # Debug üzenet csak debug módban
            if st.session_state.get("debug_mode", False):
                st.write(f"Debug: Első kategória választva: {categories[0].get('name')}")
    
    # Form-on kívül kezeljük a kategória és típus változást
    col1, col2 = st.columns(2)
    with col1:
        category_options = [cat["name"] for cat in categories]
        if not category_options:
            st.error("Nem találhatók termékkategóriák. Kérjük, ellenőrizze a kapcsolatot vagy próbálja újra később.")
            category_options = [""]
        
        selected_index = 0
        if 'category_select' in st.session_state and st.session_state.category_select in category_options:
            selected_index = category_options.index(st.session_state.category_select)
        
        # A key paraméter helyett használjunk függvényt és változót a dupla beállítás elkerülésére
        temp_category = st.selectbox(
            "Termékkategória *",
            options=category_options,
            index=selected_index
        )
        
        # Csak akkor frissítsük a session state-t, ha a felhasználó valóban módosította az értéket
        if 'category_select' not in st.session_state or temp_category != st.session_state.category_select:
            st.session_state.category_select = temp_category
    
    # Ha változott a kategória, frissítjük a típusokat
    if temp_category and (not hasattr(st.session_state, 'prev_category') or st.session_state.prev_category != temp_category):
        # Debug üzenet csak debug módban és admin esetén
        if st.session_state.get("debug_mode", False) and is_admin:
            st.write(f"Debug: Kategória változott '{st.session_state.get('prev_category', 'None')}' -> '{temp_category}'")
            
        product_types, error = update_types_by_category(temp_category, categories)
        if error:
            show_error(error)
            
        # Debug üzenet csak debug módban
        if st.session_state.get("debug_mode", False) and is_admin:
            st.write(f"Lekértem a terméktípusokat a(z) '{temp_category}' kategóriához. Találtam: {len(product_types)} darabot")
            if product_types:
                st.write(f"Elérhető terméktípusok: {', '.join([t.get('name', '') for t in product_types])}")
            
        st.session_state.product_types = product_types
        st.session_state.prev_category = temp_category
        
        # Alapértelmezett típus beállítása a kategóriaváltás után - KRITIKUS JAVÍTÁS
        if product_types:
            # A felhasználó által megadott alapértelmezett terméktípus lekérdezése
            default_type_id = get_default_product_type_id()
            
            # Keressük a terméktípust ID alapján
            type_found = False
            for typ in product_types:
                if typ.get('id') == default_type_id:
                    st.session_state.type_select = typ.get('name')
                    type_found = True
                    # Debug infó
                    if st.session_state.get("debug_mode", False) and is_admin:
                        st.write(f"Debug: Alapértelmezett terméktípus kiválasztva: {typ.get('name')}")
                    break
            
            # Ha nem találtuk meg, csak akkor használjuk az első elemet
            if not type_found:
                # Már itt kerüljük a közvetlen felülírást konkrét terméktípussal
                st.session_state.type_select = product_types[0]["name"]
                # Debug infó
                if st.session_state.get("debug_mode", False) and is_admin:
                    st.write(f"Debug: Nem találtam alapértelmezett terméktípust, az első választva: {product_types[0]['name']}")
        else:
            st.session_state.type_select = ""
            
            # Debug üzenet csak debug módban
            if st.session_state.get("debug_mode", False) and is_admin:
                st.write("Nincs elérhető terméktípus, üres értéket állítottam be")
    
    # Form-on kívül kezeljük a típus változást
    with col2:
        type_options = [typ["name"] for typ in st.session_state.product_types] if hasattr(st.session_state, 'product_types') and st.session_state.product_types else []
        
        # Ha nincsenek típus opciók, jelezzük ezt a felhasználónak
        if not type_options and temp_category:
            st.error(f"Nem találhatók terméktípusok a(z) '{temp_category}' kategóriában.")
            type_options = [""]
            
            # Debug üzenet csak debug módban
            if st.session_state.get("debug_mode", False):
                st.write("Üres típus listát állítottam be, mert nincsenek típusok")
        
        # Az index kiszámítása biztonságosan
        selected_type_index = 0
        if type_options and "type_select" in st.session_state and st.session_state.type_select in type_options:
            selected_type_index = type_options.index(st.session_state.type_select)
            
            # Debug üzenet csak debug módban
            if st.session_state.get("debug_mode", False):
                st.write(f"Megtaláltam a típus indexét: {selected_type_index}, típus: {st.session_state.type_select}")
        
        # Típus választó megjelenítése
        selected_type = st.selectbox(
            "Terméktípus *",
            options=type_options,
            index=selected_type_index
        )
        
        # Frissítsük a type_select értékét a kiválasztott értékkel
        if type_options and selected_type != st.session_state.get("type_select", ""):
            st.session_state.type_select = selected_type
            # Debug üzenet csak debug módban
            if st.session_state.get("debug_mode", False):
                st.write(f"Debug: Felhasználó új típust választott: {selected_type}")
    
    # Típus változása esetén frissítjük a minőségi besorolásokat
    if hasattr(st.session_state, 'type_select') and (not hasattr(st.session_state, 'prev_type') or st.session_state.prev_type != st.session_state.type_select):
        st.session_state.prev_type = st.session_state.type_select
        
        # Debug üzenet csak debug módban
        if st.session_state.get("debug_mode", False):
            st.write(f"Terméktípus változott: {st.session_state.type_select}")
        
        quality_grades, error = update_grades_by_type(st.session_state.type_select, st.session_state.product_types)
        if error:
            show_error(error)
        
        # Minőségi besorolások frissítése
        st.session_state.quality_grades = quality_grades
        
        # Debug üzenet csak debug módban
        if st.session_state.get("debug_mode", False):
            grade_count = len(quality_grades) if quality_grades else 0
            st.write(f"Minőségi besorolások frissítve: {grade_count} darab besorolás található")
            if quality_grades:
                st.write(f"Minőségi besorolások: {', '.join([g.get('name', '') for g in quality_grades])}")
    
    # Űrlap megjelenítése
    with st.form("offer_form"):
        # Dátum megadása
        tomorrow = datetime.date.today() + datetime.timedelta(days=1)
        delivery_date = st.date_input(
            "Beszállítás dátuma *",
            value=tomorrow,
            min_value=tomorrow,
            help="Az a dátum, amikor a terméket beszállítaná"
        )
        
        # Minőségi besorolás megadása (ha a terméktípushoz tartozik)
        selected_grade_id = None
        if hasattr(st.session_state, 'quality_grades') and st.session_state.quality_grades:
            grade_options = [grade["name"] for grade in st.session_state.quality_grades]
            
            # Alapértelmezett minőségi besorolás - a korábban mentettből
            default_quality_grade_id = get_default_quality_grade_id()
            default_index = 0
            
            # Keressük a mentett minőségi besorolás indexét
            for i, grade in enumerate(st.session_state.quality_grades):
                if grade.get("id") == default_quality_grade_id:
                    default_index = i
                    break
            
            selected_grade = st.selectbox(
                "Minőségi besorolás *",
                options=grade_options,
                index=default_index,
                help="Válassza ki a termék minőségi besorolását"
            )
            
            # Kiválasztott besorolás azonosítója
            for grade in st.session_state.quality_grades:
                if grade["name"] == selected_grade:
                    selected_grade_id = grade["id"]
                    break
        
        # Mennyiség és mértékegység megadása
        col1, col2 = st.columns(2)
        
        with col1:
            quantity = st.number_input(
                "Mennyiség *",
                min_value=0.0,
                value=10.0,
                step=0.1,
                format="%.1f",
                help="Adja meg a termék mennyiségét"
            )
        
        with col2:
            unit_options = [
                {"name": "kg", "value": "kg"},
                {"name": "tonna", "value": "tonna"},
                {"name": "db", "value": "db"}
            ]
            
            # Alapértelmezett egység
            default_unit = get_default_quantity_unit()
            default_unit_index = 0
            
            for i, opt in enumerate(unit_options):
                if opt["value"] == default_unit:
                    default_unit_index = i
                    break
            
            unit = st.selectbox(
                "Mértékegység *",
                options=[u["value"] for u in unit_options],
                format_func=lambda x: next((u["name"] for u in unit_options if u["value"] == x), ""),
                index=default_unit_index,
                help="Válassza ki a mennyiség mértékegységét"
            )
        
        # Megjegyzés megadása
        note = st.text_area(
            "Megjegyzés",
            placeholder="Opcionális megjegyzés az ajánlathoz",
            help="Itt adhat meg további információkat az ajánlathoz"
        )
        
        # Küldés gomb
        submit = st.form_submit_button("Ajánlat létrehozása", type="primary", use_container_width=True)
        
        if submit:
            errors = []
            if delivery_date < tomorrow:
                errors.append("A beszállítás dátuma nem lehet múltbeli.")
            if not st.session_state.selected_category_id:
                errors.append("Termékkategória kiválasztása kötelező.")
            if not st.session_state.selected_type_id:
                errors.append("Terméktípus kiválasztása kötelező.")
            if st.session_state.quality_grades and not selected_grade_id:
                errors.append("Minőségi besorolás kiválasztása kötelező.")
            if quantity <= 0:
                errors.append("A mennyiségnek nagyobbnak kell lennie 0-nál.")

            if errors:
                for error in errors:
                    show_error(error)
            else:
                # Convert quantity based on unit for API compatibility
                quantity_value = quantity
                if unit == "tonna":
                    quantity_value = quantity * 1000
                    quantity_unit = "kg"  # Store as kg in database
                else:
                    quantity_unit = unit
                
                offer_data = {
                    "product_type_id": st.session_state.selected_type_id,
                    "quality_grade_id": selected_grade_id,
                    "quantity_value": quantity_value,
                    "quantity_unit": quantity_unit,
                    "delivery_date": delivery_date.strftime("%Y-%m-%d"),
                    "note": note or None
                }
                
                # Debug információ csak debug módban
                if st.session_state.get("debug_mode", False):
                    st.write("### Elküldendő ajánlat adatok")
                    st.json(offer_data)
                
                success, result = offers_api.create_offer(offer_data)
                if success:
                    show_success("Ajánlat sikeresen létrehozva!")
                    
                    # Sikeres létrehozás után jelezzük, hogy navigálni szeretnénk
                    st.session_state.navigate_to_offers = True
                    # Rerun a page to show the navigation button
                    st.rerun()
                else:
                    show_error(f"Hiba az ajánlat létrehozásakor: {result}")
    
    # Ha sikeres volt az ajánlat létrehozása és navigálni szeretnénk
    if st.session_state.get("navigate_to_offers", False):
        st.markdown("---")
        st.success("🎉 Ajánlat sikeresen létrehozva!")
        
        # Navigációs gombok
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("📋 Tovább az ajánlataimhoz", type="primary", use_container_width=True):
                # Töröljük a session state-ből a jelzést a navigáció előtt
                st.session_state.navigate_to_offers = False
                st.switch_page("pages/producer_offers.py")
            
            if st.button("➕ Új ajánlat létrehozása", use_container_width=True):
                # Reset the form by clearing the session state
                st.session_state.navigate_to_offers = False
                st.rerun()

if __name__ == "__main__":
    st.set_page_config(
        page_title=f"Új ajánlat létrehozása - {config.APP_NAME}",
        page_icon="📦",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    # Don't initialize session state here - it might overwrite login data
    render_sidebar()
    show_create_offer()
