# Profil szerkesztés
"""
Termelői profil szerkesztése oldal.
"""
import streamlit as st
from api import users as users_api
from api import products as products_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
from utils.auth_utils import validate_email, validate_password, validate_tax_id
from utils.validators import validate_phone, validate_required
import app_config as config
import datetime


def show_producer_profile():
    """
    Termelői profil oldal megjelenítése.
    """
    # Oldal beállítása
    st.title("Profil és beállítások")
    
    # Csak admin esetén jelenjen meg a debug opció
    user = get_current_user()
    is_admin = user and user.get("role", "").lower() == "admin"

    # Debug mód csak adminoknak
    if "debug_mode" not in st.session_state:
        st.session_state.debug_mode = False

    if is_admin:
        # Debug mód kapcsoló
        st.session_state.debug_mode = st.checkbox("🛠️ Debug mód", value=st.session_state.debug_mode, help="Debug információk megjelenítése")

    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("pages/auth_login.py")
        return
    
    # Felhasználó adatainak lekérése
    success, user_data = users_api.get_current_user()
    
    if not success:
        show_error(f"Hiba a felhasználói adatok lekérésekor: {user_data}")
        return
    
    # Tab megjelenítése a különböző beállításokhoz
    tab1, tab2, tab3 = st.tabs(["Személyes adatok", "Alapértelmezett beállítások", "Jelszó módosítása"])
    
    with tab1:
        show_personal_data_form(user_data)
    
    with tab2:
        show_default_settings_form(st.session_state.debug_mode)
    
    with tab3:
        show_password_change_form()
        
def show_personal_data_form(user_data):
    """
    Személyes adatok szerkesztése űrlap.
    
    Args:
        user_data (dict): Felhasználó adatai
    """
    st.header("Személyes adatok")
    
    with st.form("personal_data_form"):
        # Személyes adatok
        contact_name = st.text_input("Kapcsolattartó neve *", 
                                    value=user_data.get("contact_name", ""),
                                    placeholder="Teljes név")
        
        email = st.text_input("E-mail cím *", 
                            value=user_data.get("email", ""),
                            placeholder="<EMAIL>",
                            disabled=True,  # E-mail cím nem módosítható
                            help="Az e-mail cím nem módosítható. Ha változtatni szeretne, kérjük vegye fel a kapcsolatot az adminisztrátorral.")
        
        phone_number = st.text_input("Telefonszám *", 
                                    value=user_data.get("phone_number", ""),
                                    placeholder="+36 12 345 6789")
        
        # Cégadatok
        st.subheader("Cégadatok")
        is_company = st.checkbox("Cég nevében regisztráltam", 
                                value=bool(user_data.get("company_name") or user_data.get("tax_id")))
        
        company_name = st.text_input("Cégnév", 
                                    value=user_data.get("company_name", ""),
                                    placeholder="Cégnév", 
                                    disabled=not is_company,
                                    help="Csak céges regisztráció esetén töltse ki")
        
        tax_id = st.text_input("Adószám", 
                            value=user_data.get("tax_id", ""),
                            placeholder="12345678-1-23", 
                            disabled=not is_company,
                            help="Csak céges regisztráció esetén töltse ki")
        
        # Megerősítés az aktuális jelszóval
        st.subheader("Jóváhagyás")
        st.info("A módosítások érvényesítéséhez adja meg jelszavát.")
        current_password = st.text_input("Jelenlegi jelszó *", type="password")
        
        # Mentés gomb
        submit = st.form_submit_button("Adatok mentése", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        for field_name, field_value, display_name in [
            ("contact_name", contact_name, "Kapcsolattartó neve"),
            ("phone_number", phone_number, "Telefonszám"),
            ("current_password", current_password, "Jelenlegi jelszó")
        ]:
            is_valid, error = validate_required(field_value, display_name)
            if not is_valid:
                validation_errors.append(error)
        
        # Céges adatok ellenőrzése, ha céges
        if is_company:
            if not company_name:
                validation_errors.append("Cégnév megadása kötelező céges profil esetén.")
            if not tax_id:
                validation_errors.append("Adószám megadása kötelező céges profil esetén.")
            elif not validate_tax_id(tax_id):
                validation_errors.append("Érvénytelen adószám formátum. Helyes formátum: 12345678-1-23")
        
        # Telefonszám formátum ellenőrzése
        if phone_number:
            is_valid, error = validate_phone(phone_number)
            if not is_valid:
                validation_errors.append(error)
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
            return
        
        # Frissítési adatok összeállítása
        update_data = {
            "contact_name": contact_name,
            "phone_number": phone_number,
            "current_password": current_password
        }
        
        if is_company:
            update_data["company_name"] = company_name
            update_data["tax_id"] = tax_id
        else:
            update_data["company_name"] = None
            update_data["tax_id"] = None
        
        # Adatok frissítése API hívással
        success, result = users_api.update_current_user(update_data)
        
        if success:
            show_success("Személyes adatok sikeresen frissítve!")
            # Frissítjük az oldalt, hogy megjelenjenek a változtatások
            st.rerun()
        else:
            show_error(f"Hiba az adatok frissítésekor: {result}")

def show_default_settings_form(debug_mode=False):
    """
    Alapértelmezett beállítások űrlap.
    
    Args:
        debug_mode (bool): Debug mód állapota
    """
    st.header("Alapértelmezett beállítások")
    st.info("Az itt megadott beállítások automatikusan kitöltésre kerülnek új ajánlatok létrehozásakor.")

    # Inicializáljuk a session state változókat
    if "settings_saved_success" not in st.session_state:
        st.session_state.settings_saved_success = False
    
    # Sikerüzenet megjelenítése, ha sikeres volt a mentés
    if st.session_state.settings_saved_success:
        st.success("Alapértelmezett beállítások sikeresen mentve!")
        st.session_state.settings_saved_success = False
        
    # Csak az admin számára jelenítjük meg a debug információkat
    if debug_mode:
        with st.expander("RÉSZLETES DEBUG INFORMÁCIÓK"):
            st.write("### Aktuális felhasználó információi")
            user = get_current_user()
            st.write(f"**Felhasználó ID:** {user.get('id')}")
            st.write(f"**Felhasználó neve:** {user.get('contact_name')}")
            
            # Direkt SQL lekérdezés helyett csak API-t használunk
            st.write("### KÖZVETLEN ADATBÁZIS LEKÉRDEZÉS")
            st.info("Az adatbázis közvetlen elérése nem lehetséges a Streamlit alkalmazásból. Helyette az API-t használjuk.")
                
            # API-tól lekért adatok
            st.write("### API-tól lekért adatok")
            success, current_settings = users_api.get_user_default_settings()
            st.write(f"**API hívás sikeres:** {success}")
            st.write("**API válasz:**")
            st.json(current_settings)
            st.write(f"**Lekérés időpontja:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")

    # Termékkategóriák lekérése
    success, categories = products_api.get_product_categories()
    if not success:
        show_error(f"Hiba a termékkategóriák lekérésekor: {categories}")
        return
    
    # Felhasználó aktuális beállításainak lekérése
    success, current_settings = users_api.get_user_default_settings()
    current_settings = current_settings if success else {}
    
    # NULL értékek kezelése
    for key in current_settings:
        if current_settings[key] == "NULL":
            current_settings[key] = None
    
    # Debug információ - csak ha a debug mód be van kapcsolva
    if debug_mode:
        print("==== BETÖLTÖTT BEÁLLÍTÁSOK ====")
        print(f"API hívás sikeres: {success}")
        print(f"Betöltött beállítások: {current_settings}")
        print("==== BEÁLLÍTÁSOK RÉSZLETEK ====")
        for key, value in current_settings.items():
            print(f"{key}: {repr(value)} (típus: {type(value)})")
        print("==== BEÁLLÍTÁSOK VÉGE ====")
    
    # Kategória, terméktípus és minőségi besorolás inicializálása
    default_category_id = current_settings.get("default_category_id")
    default_product_type_id = current_settings.get("default_product_type_id")
    default_quality_grade_id = current_settings.get("default_quality_grade_id")
    default_unit = current_settings.get("default_quantity_unit", "kg")
    
    # Struktúrált adatok a megjelenítéshez
    default_product_type = {
        "id": default_product_type_id,
        "name": current_settings.get("default_product_type_name", ""),
        "category": {
            "id": default_category_id,
            "name": current_settings.get("default_category_name", "")
        }
    }
    
    default_quality_grade = {
        "id": default_quality_grade_id,
        "name": current_settings.get("default_quality_grade_name", "")
    }
    
    # Kategória-választás és értékének nyomon követése
    if "selected_category_id" not in st.session_state:
        # Ha van alapértelmezett terméktípus, akkor annak kategóriája
        if default_product_type and default_product_type.get("category"):
            st.session_state.selected_category_id = default_product_type.get("category", {}).get("id")
        else:
            st.session_state.selected_category_id = None
    
    # Kategória-opciók összeállítása
    category_options = [{"id": None, "name": "-- Válasszon kategóriát --"}] + categories
    
    # Index keresése a kiválasztott kategóriához - fontos a None kezelése
    selected_category_index = 0
    for i, cat in enumerate(category_options):
        if cat.get("id") == st.session_state.selected_category_id:
            selected_category_index = i
            break
    
    # Kategória-választó megjelenítése
    selected_category = st.selectbox(
        "Alapértelmezett termékkategória",
        options=[c.get("id") for c in category_options],
        format_func=lambda x: next((c["name"] for c in category_options if c["id"] == x), "-- Válasszon kategóriát --"),
        index=selected_category_index,
        key="category_selectbox"
    )
    
    # Kategóriaváltozás követése
    if st.session_state.selected_category_id != selected_category:
        st.session_state.selected_category_id = selected_category
        st.session_state.selected_type_id = None
        st.session_state.selected_grade_id = None

    # Terméktípusok lekérése a kiválasztott kategóriához
    product_types = []
    if st.session_state.selected_category_id:
        success, types_result = products_api.get_product_types(category_id=st.session_state.selected_category_id)
        if success:
            product_types = types_result
        else:
            show_error(f"Hiba a terméktípusok lekérésekor: {types_result}")
    
    # Terméktípus követése
    if "selected_type_id" not in st.session_state:
        # Ha van alapértelmezett terméktípus és egyezik a kategória
        if default_product_type and default_product_type.get("category", {}).get("id") == st.session_state.selected_category_id:
            st.session_state.selected_type_id = default_product_type.get("id")
        else:
            st.session_state.selected_type_id = None
    
    # Terméktípus-opciók összeállítása
    type_options = [{"id": None, "name": "-- Válasszon terméktípust --"}] + product_types
    selected_type_index = 0
    
    for i, t in enumerate(type_options):
        if t.get("id") == st.session_state.selected_type_id:
            selected_type_index = i
            break
    
    # Terméktípus választó megjelenítése
    selected_type = st.selectbox(
        "Alapértelmezett terméktípus",
        options=[t.get("id") for t in type_options],
        format_func=lambda x: next((t["name"] for t in type_options if t["id"] == x), "-- Válasszon terméktípust --"),
        index=selected_type_index,
        key="type_selectbox",
        disabled=not st.session_state.selected_category_id
    )
    
    # Terméktípus változásának követése
    if st.session_state.selected_type_id != selected_type:
        st.session_state.selected_type_id = selected_type
        st.session_state.selected_grade_id = None

    # Minőségi besorolások lekérése a kiválasztott terméktípushoz
    quality_grades = []
    has_quality_grades = False
    
    if st.session_state.selected_type_id:
        success, grades_result = products_api.get_quality_grades(product_type_id=st.session_state.selected_type_id)
        if success:
            quality_grades = grades_result
            has_quality_grades = len(quality_grades) > 0
        else:
            show_error(f"Hiba a minőségi besorolások lekérésekor: {grades_result}")
    
    # Minőségi besorolás követése
    if "selected_grade_id" not in st.session_state:
        # Ha van alapértelmezett minőségi besorolás és egyezik a terméktípus
        if default_quality_grade and default_product_type and default_product_type.get("id") == st.session_state.selected_type_id:
            st.session_state.selected_grade_id = default_quality_grade.get("id")
        else:
            st.session_state.selected_grade_id = None
    
    # Minőségi besorolás-opciók összeállítása
    grade_options = [{"id": None, "name": "-- Válasszon minőségi besorolást --"}] + quality_grades
    selected_grade_index = 0
    
    for i, g in enumerate(grade_options):
        if g.get("id") == st.session_state.selected_grade_id:
            selected_grade_index = i
            break
    
    # Minőségi besorolás választó megjelenítése, ha van minőségi besorolás
    if has_quality_grades:
        selected_grade = st.selectbox(
            "Alapértelmezett minőségi besorolás",
            options=[g.get("id") for g in grade_options],
            format_func=lambda x: next((g["name"] for g in grade_options if g["id"] == x), "-- Válasszon minőségi besorolást --"),
            index=selected_grade_index,
            key="grade_selectbox",
            disabled=not st.session_state.selected_type_id
        )
        
        # Minőségi besorolás változásának követése
        if st.session_state.selected_grade_id != selected_grade:
            st.session_state.selected_grade_id = selected_grade
    else:
        # Ha nincs minőségi besorolás, akkor kiírjuk
        st.info("A kiválasztott terméktípushoz nem tartozik minőségi besorolás.")
        selected_grade = None
        st.session_state.selected_grade_id = None

    # Mértékegység-opciók
    unit_options = [
        {"value": "kg", "name": "Kilogramm (kg)"}, 
        {"value": "tonna", "name": "Tonna (t)"},
        {"value": "db", "name": "Darab (db)"}
    ]
    
    # Mértékegység kiválasztása
    selected_unit_index = 0
    for i, unit in enumerate(unit_options):
        if unit["value"] == default_unit:
            selected_unit_index = i
            break
            
    selected_unit = st.selectbox(
        "Alapértelmezett mértékegység",
        options=[u["value"] for u in unit_options],
        format_func=lambda x: next((u["name"] for u in unit_options if u["value"] == x), ""),
        index=selected_unit_index,
        key="unit_selectbox"
    )

    # Mentés gomb
    if st.button("Beállítások mentése", type="primary", use_container_width=True):
        # Validáció
        validation_errors = []

        if not st.session_state.selected_category_id:
            validation_errors.append("Kérem, válasszon termékkategóriát!")

        if not st.session_state.selected_type_id:
            validation_errors.append("Kérem, válasszon terméktípust!")

        # Minőségi besorolás ellenőrzés, ha van minőségi besorolás a típushoz
        if has_quality_grades and not selected_grade:
            validation_errors.append("A kiválasztott terméktípushoz minőségi besorolás is tartozik. Kérem, válasszon minőségi besorolást!")

        # Hibák megjelenítése
        if validation_errors:
            for error in validation_errors:
                show_error(error)
            return

        # Mentési adatok összeállítása - Fontos, hogy egyik érték se legyen "NULL" string
        settings_data = {
            "default_category_id": st.session_state.selected_category_id,
            "default_product_type_id": st.session_state.selected_type_id,
            "default_quality_grade_id": st.session_state.selected_grade_id if has_quality_grades else None,
            "default_quantity_unit": selected_unit
        }

        # Kategória és terméktípus keresése és mentése a session state-be
        selected_category_obj = next((c for c in categories if c.get("id") == st.session_state.selected_category_id), {})
        selected_type_obj = next((t for t in product_types if t.get("id") == st.session_state.selected_type_id), {})
        
        # Ha van minőségi besorolás, azt is elmentsük
        if has_quality_grades and st.session_state.selected_grade_id:
            selected_grade_obj = next((g for g in quality_grades if g.get("id") == st.session_state.selected_grade_id), {})
        else:
            selected_grade_obj = {}
        
        # Adjunk hozzá extra információt a backend-nek, hogy teljeskörűen tároljon adatokat
        # Ez segít biztosítani, hogy az összes objektum megfelelően tárolva legyen
        settings_data["default_product_type_name"] = selected_type_obj.get("name", "")
        settings_data["default_quality_grade_name"] = selected_grade_obj.get("name", "") if selected_grade_obj else ""
        settings_data["default_category_name"] = selected_category_obj.get("name", "")
        settings_data["has_quality_grades"] = has_quality_grades

        # Ellenőrizzük, hogy biztosan ne küldünk NULL értékeket stringként
        for key, value in settings_data.items():
            if value == "NULL":
                settings_data[key] = None
        
        # Biztosítsuk, hogy None értékek helyett üres string kerüljön a backend-be, ahol szöveg típusú mező van
        for key in ["default_product_type_name", "default_quality_grade_name", "default_category_name"]:
            if settings_data[key] is None:
                settings_data[key] = ""
        
        # RÉSZLETES DEBUG - a konzolra és az oldalra is kiírjuk az adatokat
        if debug_mode:
            st.write("### RÉSZLETES DEBUG INFORMÁCIÓK")
            st.write("#### Elküldendő beállítások")
            debug_table = []
            
            for key, value in settings_data.items():
                value_repr = repr(value)
                value_type = type(value).__name__
                debug_table.append({"Mező": key, "Érték": value_repr, "Típus": value_type})
                print(f"{key}: {value_repr} (típus: {value_type})")
            
            st.table(debug_table)
        
        # API hívás a beállítások mentéséhez
        success, result = users_api.update_user_default_settings(settings_data)
        if debug_mode:
            st.write(f"#### API válasz a mentésre")
            st.write(f"Sikeres mentés: {success}")
            st.json(result)

        if success:
            # Frissítsük a session state-et a most tárolt értékekkel
            st.session_state.user_settings = {
                "default_category_id": st.session_state.selected_category_id,
                "default_category_name": selected_category_obj.get("name", ""),
                "default_product_type_id": st.session_state.selected_type_id,
                "default_product_type_name": selected_type_obj.get("name", ""),
                "default_quality_grade_id": st.session_state.selected_grade_id if has_quality_grades else None,
                "default_quality_grade_name": selected_grade_obj.get("name", "") if selected_grade_obj else "",
                "default_quantity_unit": selected_unit,
                "has_quality_grades": has_quality_grades
            }
            if debug_mode:
                # A mentés után azonnal ellenőrizzük, hogy tényleg elmentődött-e
                st.write("#### Ellenőrző API lekérdezés eredménye")
                verify_success, verify_result = users_api.get_user_default_settings()
                
                if verify_success:
                    st.write("Sikeres adatlekérés az API-tól:")
                    st.json(verify_result)
                    
                    # Ellenőrizzük, hogy a mentett értékek egyeznek-e
                    st.write("#### Mentett értékek ellenőrzése")
                    verification_table = []
                    
                    for key, sent_value in settings_data.items():
                        if key in verify_result:
                            received_value = verify_result[key]
                            match = str(received_value) == str(sent_value) if received_value != "NULL" and sent_value is not None else (received_value == "NULL" and sent_value is None)
                            verification_table.append({
                                "Mező": key, 
                                "Elküldött érték": repr(sent_value), 
                                "Visszakapott érték": repr(received_value),
                                "Egyezik?": match
                            })
                    
                    st.table(verification_table)
                else:
                    st.error(f"Hiba az adatlekérés során: {verify_result}")
            
            # Sikeres mentés üzenet
            st.session_state.settings_saved_success = True
            st.success("Alapértelmezett beállítások sikeresen mentve!")
        else:
            st.error(f"Hiba a beállítások mentése közben: {result}")

def show_password_change_form():
    """
    Jelszó módosítása űrlap.
    """
    st.header("Jelszó módosítása")
    
    # Session state használata a jelszó módosítás sikerének nyomon követésére
    if "password_change_success" not in st.session_state:
        st.session_state.password_change_success = False
    
    # Sikeres jelszómódosítás után visszajelzés
    if st.session_state.password_change_success:
        st.success("Jelszó sikeresen módosítva!")
        st.session_state.password_change_success = False  # Visszaállítjuk az állapotot
    
    with st.form("password_change_form"):
        current_password = st.text_input("Jelenlegi jelszó *", type="password")
        new_password = st.text_input("Új jelszó *", type="password", help="A jelszónak legalább 8 karakter hosszúnak kell lennie, tartalmaznia kell betűt és számot is.")
        confirm_password = st.text_input("Új jelszó megerősítése *", type="password")
        
        # Mentés gomb
        submit = st.form_submit_button("Jelszó módosítása", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        for field_name, field_value, display_name in [
            ("current_password", current_password, "Jelenlegi jelszó"),
            ("new_password", new_password, "Új jelszó"),
            ("confirm_password", confirm_password, "Új jelszó megerősítése")
        ]:
            is_valid, error = validate_required(field_value, display_name)
            if not is_valid:
                validation_errors.append(error)
        
        # Új jelszó ellenőrzése
        if new_password:
            is_valid, error = validate_password(new_password)
            if not is_valid:
                validation_errors.append(error)
        
        # Jelszó egyezés ellenőrzése
        if new_password and confirm_password and new_password != confirm_password:
            validation_errors.append("A megadott új jelszavak nem egyeznek.")
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
            return
        
        # Adatok összeállítása a jelszóváltoztatáshoz
        password_data = {
            "current_password": current_password,
            "new_password": new_password
        }
        
        # Jelszó módosítása API hívással
        success, result = users_api.change_password(password_data)
        
        if success:
            # Jelzés, hogy a jelszóváltás sikeres volt
            st.session_state.password_change_success = True
            # Oldal újratöltése
            st.rerun()
        else:
            show_error(f"Hiba a jelszó módosításakor: {result}")

# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=f"Profil szerkesztése - {config.APP_NAME}",
        page_icon="👤",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Megjelenítjük a profil szerkesztése oldalt
    show_producer_profile()
