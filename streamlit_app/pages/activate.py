"""
Fiók aktiválási oldal
"""
import streamlit as st
import requests
import time
import sys
import os

# Add the parent directory to sys.path to import app_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app_config import API_HOST

# Oldal konfiguráció
st.set_page_config(
    page_title="Fiók aktiválás - POM APP",
    page_icon="✅",
    layout="centered"
)

# Fejléc
st.title("✅ Fiók aktiválás")
st.markdown("---")

# Token lekérése az URL-ből
def get_token_from_url():
    """Token kinyerése az URL query paraméterekből"""
    try:
        # Streamlit query params
        params = st.query_params
        if "token" in params:
            return params["token"]
    except:
        pass
    return None

# Token ellenőrzése
token = get_token_from_url()

if not token:
    st.error("❌ Érvénytelen vagy hiányzó aktivációs token!")
    st.info("<PERSON><PERSON>rj<PERSON><PERSON>, használja az e-mailben kapott linket a fiók aktiválásához.")
    st.markdown("---")
    if st.button("↩️ Vissza a bejelentkezéshez"):
        st.switch_page("pages/auth_login.py")
else:
    # Automatikus aktiválás
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        with st.spinner("Fiók aktiválása folyamatban..."):
            try:
                backend_url = st.session_state.get('backend_url', API_HOST)
                
                # API hívás a fiók aktiváláshoz
                response = requests.get(
                    f"{backend_url}/api/auth/activate-account",
                    params={"token": token},
                    timeout=10
                )
                
                if response.status_code == 200:
                    st.success("🎉 Fiókja sikeresen aktiválva!")
                    st.balloons()
                    
                    st.info("🔐 Most már bejelentkezhet a rendszerbe.")
                    
                    # Belépés gomb
                    if st.button("🚀 Bejelentkezés", use_container_width=True):
                        st.switch_page("pages/auth_login.py")
                        
                elif response.status_code == 400:
                    try:
                        error_detail = response.json().get("detail", "Érvénytelen vagy lejárt token")
                    except:
                        error_detail = "Érvénytelen vagy lejárt token"
                    
                    st.error(f"❌ {error_detail}")
                    
                    if "lejárt" in error_detail.lower() or "expired" in error_detail.lower():
                        st.info("ℹ️ Kérjen új aktivációs e-mailt a rendszergazdától.")
                    
                    # Vissza gomb hiba esetén
                    if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
                        st.switch_page("pages/auth_login.py")
                        
                else:
                    try:
                        error_msg = response.json().get("detail", "Ismeretlen hiba")
                    except:
                        error_msg = f"HTTP {response.status_code}"
                    
                    st.error(f"❌ Hiba történt: {error_msg}")
                    st.error(f"Debug info: Status code: {response.status_code}")
                    
                    # Vissza gomb hiba esetén
                    if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
                        st.switch_page("pages/auth_login.py")
                        
            except requests.exceptions.ConnectionError:
                st.error("❌ Nem sikerült kapcsolódni a szerverhez.")
                st.error(f"Debug info: Backend URL: {backend_url}")
                st.info("💡 Ellenőrizze, hogy a backend szerver fut-e.")
                
                # Vissza gomb kapcsolódási hiba esetén
                if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
                    st.switch_page("pages/auth_login.py")
                    
            except requests.exceptions.Timeout:
                st.error("❌ A kérés túllépte az időkorlátot.")
                
                # Vissza gomb timeout esetén
                if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
                    st.switch_page("pages/auth_login.py")
                    
            except Exception as e:
                st.error(f"❌ Váratlan hiba történt: {str(e)}")
                st.error(f"Debug info: {type(e).__name__}")
                
                # Vissza gomb váratlan hiba esetén
                if st.button("↩️ Vissza a bejelentkezéshez", use_container_width=True):
                    st.switch_page("pages/auth_login.py")

# Stílus
st.markdown("""
<style>
    /* Középre igazítás */
    .block-container {
        max-width: 500px;
        padding-top: 3rem;
    }
    
    /* Gombok stílusa */
    .stButton > button {
        background-color: #4CAF50;
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
        border: none;
        transition: background-color 0.3s;
    }
    
    .stButton > button:hover {
        background-color: #45a049;
    }
    
    /* Spinner és success szöveg középre igazítása */
    .stSpinner > div {
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)