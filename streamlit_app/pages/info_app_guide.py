# ✅ Új fájl: pages/info_app_guide.py
# Alkalmazás bemutató oldal (GIF-ekkel)

import streamlit as st
from components.sidebar import render_sidebar
import app_config as config

def show_guide():
    """
    Alkalmazás funkcióinak vizuális bemutatása.
    """
    st.title("🌿 Alkalmazás bemutatása")
    st.caption("Segítség új felhasználóknak – Lépésről lépésre")

    st.markdown("""
    Üdvözöljük a **Kertész Mester  Rendszerben**!  
    Ez az alkalmazás abban seg<PERSON><PERSON>, ho<PERSON>n, gyorsabban és átláthatóbban kezelje zöldség beszállításait.
    """)

    st.divider()

    # Két hasábos bemutató: szöveg + animáció
    st.subheader("🟢 1. Regisztráció")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""
        Ha még nincs fi<PERSON>, kattintson a bal oldalon a **Regisztráció** men<PERSON>pontra, és adja meg:
        - E-mail cím
        - Jelszó
        - Alapvető adatok (név, gazdaság neve stb.)

        Pár kattintás, és már használhatja is a rendszert!
        """)
    with col2:
        st.image("https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExY2syM2pjYW12eDIzbzYxbDR6ZDB2MGloODNzbzNhYmlpaTZpeWJ4eiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/kYDuwCpCLGSuvX5poK/giphy.gif", caption="Regisztráció lépései", use_column_width=False, width=200)

    st.divider()

    st.subheader("🔑 2. Bejelentkezés")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""
        A regisztráció után használja az **e-mail címet és jelszavát** a belépéshez.
          
        Bejelentkezés után személyre szabott menüt lát, a szerepköre alapján.
        
        Elfelejtett jelszó esetén használja az Elfelejtett jelszó menüpontot.
        """)
    with col2:
        st.image("https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExd2oxaWJrbHY2YXo0czc1NHp4dTE0eGwzMW1hZnR0dWJ4eHozeHE5NyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/1TQSYL5vLR4I8Z6oXe/giphy.gif", caption="Bejelentkezés folyamata", use_column_width=False, width=200)

    st.divider()

    st.subheader("📦 3. Ajánlat leadása")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""
        Termelőként gyorsan megadhatja:
        - Milyen terméket szállítana (pl. TV paprika, paradicsom)
        - Milyen mennyiségben
        - Milyen minőségben
        - Mikorra

        Az ajánlatát egy kattintással beküldheti, majd figyelheti annak státuszát.
        """)
    with col2:
        st.image("https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExZzZwNGE3aHprbm5ra3Rxam40YmZ1N2hhaGFsNWtvb2k0MHpzb25wZSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/QU9UKGiWWeDMZW6ecZ/giphy.gif", caption="Ajánlat beküldése – bemutató", use_column_width=False, width=200)

    st.divider()

    st.subheader("✅ 4. Visszaigazolás és átvétel")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""
        A rendszerben visszajelzést kap:
        - Mennyit és milyen áron veszünk át
        - Mikor történik az átvétel

        Így Ön pontosan tudhatja, mire számíthat.
        """)
    with col2:
        st.image("https://media4.giphy.com/media/v1.Y2lkPTc5MGI3NjExM2g0NGZncGZjcWljeXYyMncwYndncWZpNjBra2hzZTZ5aGQ2bW1zZSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/EzNS1V8644lSYwMlmb/giphy.gif", caption="Ajánlat visszaigazolása", use_column_width=False, width=200)

    st.divider()

    st.subheader("📊 5. Statisztikák és átláthatóság")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""
        A belépést követően saját statisztikákat is megtekinthet:
        - Mennyit szállított be eddig
        - Milyen áron
        - Milyen időszakokban

        Ez segít a jobb tervezésben és az értékesítés optimalizálásában.
        """)
    with col2:
        st.image("https://media1.giphy.com/media/v1.Y2lkPTc5MGI3NjExbGgzZWhwdm13M3Z0azh0YXl4ZmtkOGxvMW93OHdyNzV0MzJ2NWdpdiZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9cw/39fvmqZ636rm8Hjpu7/giphy.gif", caption="Statisztikai áttekintés", use_column_width=False, width=200)

    st.divider()
    st.markdown("""
    ### ❓ Kérdése van?

    📩 Elérhetőség: [<EMAIL>](mailto:<EMAIL>)

    Köszönjük, hogy a digitális kertészkedést választotta!  
    """)

# Futtatás, ha oldal betöltődik
if __name__ == "__main__":
    st.set_page_config(
        page_title=f"Bemutató - {config.APP_NAME}",
        page_icon="📖",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    render_sidebar()
    show_guide()
