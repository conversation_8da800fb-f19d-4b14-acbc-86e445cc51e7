"""
Ügyintézői naptári nézet oldal (reszponzív, mobilbar<PERSON>t verzió).
"""
import streamlit as st
import datetime
from datetime import datetime as dt, timedelta
import pandas as pd
import plotly.graph_objects as go
import json
import base64
import io
import time

from api import offers as offers_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success, toast_notification
from utils.session import is_authenticated, get_current_user
from utils.formatting import format_quantity, format_price
from utils.responsive_ui import (
    detect_mobile, 
    get_theme_colors, 
    setup_responsive_ui, 
    display_card,
    create_responsive_columns,
    render_responsive_calendar
)
import app_config as config

# Oldal beállítások
st.set_page_config(
    page_title=f"Naptári nézet - {config.APP_NAME}",
    page_icon="📅",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Reszponzív UI beállítása
setup_responsive_ui()

# Oldalsáv megjelenítése
render_sidebar()

# Téma detektálás
is_dark = st.session_state.get("theme", "light") == "dark"
is_mobile = st.session_state.get("is_mobile", False)

# Oldal cím
st.title("📅 Naptári nézet")
if is_mobile:
    toast_notification("Mobilnézet aktiválva", "info")

# Oldalbeállítások és funkciók
def get_cached_data(cache_key, max_age_minutes=30):
    """Adatok lekérése a gyorsítótárból"""
    if cache_key in st.session_state:
        cache_data = st.session_state[cache_key]
        if time.time() - cache_data['timestamp'] < max_age_minutes * 60:
            return cache_data['data']
    return None

def cache_data(cache_key, data):
    """Adatok mentése a gyorsítótárba"""
    st.session_state[cache_key] = {
        'data': data,
        'timestamp': time.time()
    }

# Dátumkezelési segédfüggvények
def get_week_start(date):
    """Hét kezdő dátumának meghatározása"""
    return date - timedelta(days=date.weekday())

def get_week_end(date):
    """Hét záró dátumának meghatározása"""
    return date + timedelta(days=6-date.weekday())

def get_month_start(date):
    """Hónap kezdő dátumának meghatározása"""
    return datetime.date(date.year, date.month, 1)

def get_month_end(date):
    """Hónap záró dátumának meghatározása"""
    if date.month == 12:
        return datetime.date(date.year + 1, 1, 1) - timedelta(days=1)
    else:
        return datetime.date(date.year, date.month + 1, 1) - timedelta(days=1)

def get_download_link(df, filename, link_text, file_format="csv"):
    """Letöltési link generálása Excel/CSV fájlhoz"""
    if file_format == "excel":
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Adatok', index=False)
        excel_data = output.getvalue()
        b64 = base64.b64encode(excel_data).decode('utf-8')
        mime_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    else:  # CSV
        csv = df.to_csv(index=False)
        b64 = base64.b64encode(csv.encode()).decode()
        mime_type = 'text/csv'
    
    href = f'<a href="data:{mime_type};base64,{b64}" download="{filename}.{file_format}" style="text-decoration:none;color:#1976D2;">{link_text}</a>'
    return href

def load_calendar_events(start_date, end_date):
    """Naptári események betöltése az API-ról"""
    cache_key = f"calendar_events_{start_date}_{end_date}"
    
    # Cache ellenőrzés
    cached_events = get_cached_data(cache_key, max_age_minutes=15)
    if cached_events is not None:
        return cached_events
    
    # API hívás
    try:
        success, response = offers_api.get_calendar_events(
            params={
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        )
        
        if not success:
            show_error(f"Hiba történt az események lekérése során: {response}")
            return []
        
        # Lista -> szótár konverzió - csoportosítás dátum szerint
        events_by_date = {}
        if isinstance(response, list):
            for event in response:
                # A delivery_date mező tartalmazza a dátumot
                date_str = event.get('delivery_date', '')
                if date_str:
                    # A dátum formátum ellenőrzése és átalakítása ha szükséges
                    if 'T' in date_str:  # ISO formátum (pl. "2025-05-21T00:00:00")
                        date_str = date_str.split('T')[0]  # Csak a dátum rész kell
                    
                    # Hozzáadjuk a dátumhoz tartozó eseménylistához
                    if date_str not in events_by_date:
                        events_by_date[date_str] = []
                    events_by_date[date_str].append(event)
        else:
            # Ha mégis szótár (régi formátum), megtartjuk ahogy van
            events_by_date = response
        
        # Események feldolgozása
        events = []
        for date_str, daily_events in events_by_date.items():
            for event in daily_events:
                try:
                    date_obj = dt.strptime(date_str, "%Y-%m-%d").date()
                    
                    # Mennyiség és ár formázása
                    quantity = float(event.get('quantity_in_kg', 0))
                    price = float(event.get('confirmed_price', 0))
                    
                    # Esemény adatok
                    processed_event = {
                        'id': event.get('id', ''),
                        'date': date_obj.strftime("%Y-%m-%d"),
                        'title': f"{event.get('product_name', 'Termék')} - {format_quantity(quantity)}",
                        'quantity': quantity,
                        'price': price,
                        'status': event.get('status', ''),
                        'user_id': event.get('user_id', ''),
                        'user_name': event.get('user_name', 'Ismeretlen'),
                        'product_id': event.get('product_type_id', ''),
                        'product_name': event.get('product_name', 'Ismeretlen termék'),
                        'note': event.get('note', ''),
                        'color': get_status_color(event.get('status', '')),
                    }
                    events.append(processed_event)
                except Exception as e:
                    print(f"Hiba az esemény feldolgozása során: {str(e)}")
        
        # Események cache-elése
        cache_data(cache_key, events)
        return events
        
    except Exception as e:
        show_error(f"Váratlan hiba történt: {str(e)}")
        return []

def get_status_color(status):
    """Állapothoz tartozó szín lekérése"""
    colors = {
        'várható': '#FFA726',  # narancs
        'visszaigazolt': '#66BB6A',  # zöld
        'elutasított': '#EF5350',  # piros
        'feldolgozás alatt': '#42A5F5',  # kék
        'törölt': '#9E9E9E',  # szürke
    }
    return colors.get(status.lower(), '#9E9E9E')  # alapértelmezett szürke

def show_operator_calendar():
    """Naptári nézet oldal fő tartalma"""
    
    # Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        show_error("A naptári nézet megtekintéséhez be kell jelentkezni.")
        return
    
    # Jelenlegi dátum
    today = datetime.date.today()
    
    # Nézettípus választó
    view_options = {
        "napi": "Napi nézet",
        "heti": "Heti nézet",
        "havi": "Havi nézet",
        "egyedi": "Egyedi időszak"
    }
    
    # Mobilon egyszerűsített nézet
    if is_mobile:
        col1, col2 = st.columns([2, 1])
        with col1:
            view_type = st.selectbox("Nézet:", list(view_options.values()))
        with col2:
            refresh_button = st.button("🔄 Frissítés")
    else:
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            view_type = st.selectbox("Nézet:", list(view_options.values()))
        with col3:
            refresh_button = st.button("🔄 Frissítés", use_container_width=True)
    
    view_key = [k for k, v in view_options.items() if v == view_type][0]
    
    # Dátumválasztó inicializálása
    if "selected_date" not in st.session_state:
        st.session_state.selected_date = today
    
    if "date_range_start" not in st.session_state:
        st.session_state.date_range_start = today - timedelta(days=7)
    
    if "date_range_end" not in st.session_state:
        st.session_state.date_range_end = today + timedelta(days=7)
    
    # Dátumválasztók a különböző nézetekhez
    if view_key == "napi":
        # Napi nézethez dátumválasztó
        col1, col2 = create_responsive_columns([3, 1])
        with col1:
            selected_date = st.date_input("Dátum:", value=st.session_state.selected_date)
        with col2:
            if st.button("Ma"):
                selected_date = today
        
        st.session_state.selected_date = selected_date
        
        # Dátum tartomány beállítása az API híváshoz
        start_date = selected_date
        end_date = selected_date
        
    elif view_key == "heti":
        # Heti nézethez hét választó
        week_start = get_week_start(st.session_state.selected_date)
        week_end = get_week_end(week_start)
        
        col1, col2 = create_responsive_columns([3, 1])
        with col1:
            selected_date = st.date_input(
                "Hét kiválasztása:",
                value=st.session_state.selected_date
            )
        with col2:
            if st.button("Aktuális hét"):
                selected_date = today
        
        st.session_state.selected_date = selected_date
        
        # Hét kezdő és záró dátuma
        week_start = get_week_start(selected_date)
        week_end = get_week_end(week_start)
        
        st.write(f"Kiválasztott hét: {week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}")
        
        # Dátum tartomány beállítása az API híváshoz
        start_date = week_start
        end_date = week_end
        
    elif view_key == "havi":
        # Havi nézethez hónap választó
        month_start = get_month_start(st.session_state.selected_date)
        month_end = get_month_end(month_start)
        
        col1, col2 = create_responsive_columns([3, 1])
        with col1:
            selected_month = st.date_input(
                "Hónap kiválasztása:",
                value=st.session_state.selected_date
            )
        with col2:
            if st.button("Aktuális hónap"):
                selected_month = today
        
        st.session_state.selected_date = selected_month
        
        # Hónap kezdő és záró dátuma
        month_start = get_month_start(selected_month)
        month_end = get_month_end(month_start)
        
        st.write(f"Kiválasztott hónap: {month_start.strftime('%Y %B')}")
        
        # Dátum tartomány beállítása az API híváshoz
        start_date = month_start
        end_date = month_end
        
    else:  # "egyedi"
        # Egyedi dátum tartomány választó
        col1, col2 = create_responsive_columns([1, 1])
        with col1:
            start_date = st.date_input(
                "Kezdő dátum:",
                value=st.session_state.date_range_start
            )
        with col2:
            end_date = st.date_input(
                "Záró dátum:",
                value=st.session_state.date_range_end
            )
        
        # Dátum tartományok érvényességének ellenőrzése
        if start_date > end_date:
            st.error("A kezdő dátum nem lehet később, mint a záró dátum!")
            return
        
        st.session_state.date_range_start = start_date
        st.session_state.date_range_end = end_date
        
        st.write(f"Kiválasztott időszak: {start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}")
    
    # Adatok lekérése az API-ról
    events = load_calendar_events(start_date, end_date)
    
    # Ha üres a válasz, üzenet megjelenítése
    if not events:
        st.info("Nincs megjeleníthető esemény a kiválasztott időszakban.")
        return
    
    # Események DataFrame konvertálása
    df = pd.DataFrame(events)
    
    # Adatok szűrése és megjelenítése
    if not df.empty:
        # Szűrési lehetőségek hozzáadása
        if not is_mobile:
            col1, col2, col3 = st.columns(3)
            with col1:
                if 'status' in df.columns:
                    all_statuses = ['Összes'] + sorted(df['status'].unique().tolist())
                    selected_status = st.selectbox("Állapot:", all_statuses)
            with col2:
                if 'product_name' in df.columns:
                    all_products = ['Összes'] + sorted(df['product_name'].unique().tolist())
                    selected_product = st.selectbox("Termék:", all_products)
            with col3:
                if 'user_name' in df.columns:
                    all_users = ['Összes'] + sorted(df['user_name'].unique().tolist())
                    selected_user = st.selectbox("Termelő:", all_users)
        else:
            # Mobilon függőlegesen a szűrők
            if 'status' in df.columns:
                all_statuses = ['Összes'] + sorted(df['status'].unique().tolist())
                selected_status = st.selectbox("Állapot:", all_statuses)
            
            expander = st.expander("További szűrési lehetőségek")
            with expander:
                if 'product_name' in df.columns:
                    all_products = ['Összes'] + sorted(df['product_name'].unique().tolist())
                    selected_product = st.selectbox("Termék:", all_products)
                
                if 'user_name' in df.columns:
                    all_users = ['Összes'] + sorted(df['user_name'].unique().tolist())
                    selected_user = st.selectbox("Termelő:", all_users)
        
        # Szűrés alkalmazása
        filtered_df = df.copy()
        
        if 'status' in df.columns and selected_status != 'Összes':
            filtered_df = filtered_df[filtered_df['status'] == selected_status]
        
        if 'product_name' in df.columns and selected_product != 'Összes':
            filtered_df = filtered_df[filtered_df['product_name'] == selected_product]
        
        if 'user_name' in df.columns and selected_user != 'Összes':
            filtered_df = filtered_df[filtered_df['user_name'] == selected_user]
        
        # Exportálási lehetőségek
        export_col1, export_col2 = create_responsive_columns([3, 1])
        with export_col1:
            st.write(f"Szűrt események száma: {len(filtered_df)}")
        
        with export_col2:
            date_label = ""
            if view_key == "napi":
                date_label = selected_date.strftime("%Y-%m-%d")
            elif view_key == "heti":
                date_label = f"{week_start.strftime('%Y-%m-%d')}_{week_end.strftime('%Y-%m-%d')}"
            elif view_key == "havi":
                date_label = month_start.strftime("%Y_%m")
            else:
                date_label = f"{start_date.strftime('%Y-%m-%d')}_{end_date.strftime('%Y-%m-%d')}"
            
            if st.button("Exportálás"):
                # Excel export link
                excel_link = get_download_link(
                    filtered_df, 
                    f"calendar_export_{date_label}", 
                    "Excel letöltése", 
                    "excel"
                )
                st.markdown(excel_link, unsafe_allow_html=True)
                
                # CSV export link
                csv_link = get_download_link(
                    filtered_df, 
                    f"calendar_export_{date_label}", 
                    "CSV letöltése", 
                    "csv"
                )
                st.markdown(csv_link, unsafe_allow_html=True)
        
        # Különböző nézetek megjelenítése
        if view_key == "napi":
            # Napi nézet renderelése
            st.subheader(f"Napi nézet: {selected_date.strftime('%Y-%m-%d')}")
            render_daily_view(filtered_df, selected_date)
            
        elif view_key == "heti":
            # Heti nézet renderelése
            st.subheader(f"Heti nézet: {week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}")
            render_weekly_view(filtered_df, week_start)
            
        elif view_key == "havi":
            # Havi nézet renderelése
            st.subheader(f"Havi nézet: {month_start.strftime('%Y %B')}")
            render_monthly_view(filtered_df, month_start)
            
        else:  # "egyedi"
            # Egyedi időszak nézet renderelése
            st.subheader(f"Egyedi időszak: {start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}")
            render_custom_view(filtered_df, start_date, end_date)
        
        # Események táblázatos megjelenítése
        if is_mobile:
            # Mobilon kevesebb oszlop
            display_columns = ['date', 'title', 'status', 'user_name']
            st.dataframe(
                filtered_df[display_columns],
                use_container_width=True,
                hide_index=True
            )
        else:
            display_columns = ['date', 'title', 'price', 'status', 'user_name', 'note']
            st.dataframe(
                filtered_df[display_columns],
                use_container_width=True,
                hide_index=True
            )

def render_daily_view(df, selected_date):
    """Napi nézet megjelenítése"""
    # Csak a kiválasztott napra szűrés
    day_str = selected_date.strftime('%Y-%m-%d')
    day_events = df[df['date'] == day_str]
    
    if day_events.empty:
        st.info(f"Nincs esemény {day_str} napra.")
        return
    
    # Kártyás megjelenítés
    st.write("### Napi események")
    
    for _, event in day_events.iterrows():
        # Kártya tartalom összeállítása
        content = f"""
        <p><strong>Termék:</strong> {event.get('product_name', 'Ismeretlen')}</p>
        <p><strong>Mennyiség:</strong> {format_quantity(event.get('quantity', 0))}</p>
        <p><strong>Állapot:</strong> <span style="color:{get_status_color(event.get('status', ''))};">{event.get('status', 'Ismeretlen')}</span></p>
        <p><strong>Termelő:</strong> {event.get('user_name', 'Ismeretlen')}</p>
        """
        
        if event.get('note'):
            content += f"<p><strong>Megjegyzés:</strong> {event.get('note')}</p>"
        
        # Kártya megjelenítése
        display_card(
            title=event.get('title', 'Esemény'),
            content=content,
            icon="📦"
        )

def render_weekly_view(df, week_start):
    """Heti nézet megjelenítése"""
    # A hét dátumainak létrehozása
    week_dates = [week_start + timedelta(days=i) for i in range(7)]
    
    # Mobilbarát megjelenítés
    if is_mobile:
        # Mobilon napok egymás alatt
        for day in week_dates:
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%A')
            day_events = df[df['date'] == day_str]
            
            if not day_events.empty:
                st.write(f"### {day_name} ({day_str})")
                for _, event in day_events.iterrows():
                    # Kártya tartalom
                    content = f"""
                    <p><strong>Termék:</strong> {event.get('product_name', '')}</p>
                    <p><strong>Mennyiség:</strong> {format_quantity(event.get('quantity', 0))}</p>
                    <p><strong>Termelő:</strong> {event.get('user_name', '')}</p>
                    """
                    
                    # Kártya megjelenítése
                    display_card(
                        title=f"{event.get('status', '')} - {event.get('title', '')}",
                        content=content,
                        icon="📦"
                    )
    else:
        # Asztali nézeten táblázatos megjelenítés
        week_calendar = []
        
        for day in week_dates:
            day_str = day.strftime('%Y-%m-%d')
            day_name = day.strftime('%A')
            day_events = df[df['date'] == day_str]
            
            week_calendar.append({
                'date': day_str,
                'day_name': day_name,
                'events': day_events.to_dict('records')
            })
        
        # Naptár renderelése
        render_responsive_calendar(week_calendar, view_type="week")

def render_monthly_view(df, month_start):
    """Havi nézet megjelenítése"""
    month_end = get_month_end(month_start)
    
    # A hónap összes napjának létrehozása
    current_day = month_start
    month_days = []
    
    while current_day <= month_end:
        month_days.append(current_day)
        current_day += timedelta(days=1)
    
    # Naptár adatok összeállítása
    month_calendar = []
    
    for day in month_days:
        day_str = day.strftime('%Y-%m-%d')
        day_events = df[df['date'] == day_str]
        
        month_calendar.append({
            'date': day_str,
            'day': day.day,
            'events': day_events.to_dict('records')
        })
    
    # Értékek összegzése a hónapra
    total_quantity = df['quantity'].sum()
    if 'price' in df.columns:
        total_value = (df['quantity'] * df['price']).sum()
    else:
        total_value = 0
    
    # Összesítő kártya megjelenítése
    summary_content = f"""
    <p><strong>Összes mennyiség:</strong> {format_quantity(total_quantity)}</p>
    <p><strong>Összes érték:</strong> {format_price(total_value)}</p>
    <p><strong>Események száma:</strong> {len(df)}</p>
    """
    
    display_card(
        title=f"Havi összesítő: {month_start.strftime('%Y %B')}",
        content=summary_content,
        icon="📊"
    )
    
    # Naptár renderelése
    render_responsive_calendar(month_calendar, view_type="month")
    
    # Opcionális: grafikon a mennyiségek napi eloszlásáról
    if len(df) > 0 and not is_mobile:
        try:
            # Napi összegzések
            daily_sum = df.groupby('date')['quantity'].sum().reset_index()
            
            fig = go.Figure(data=go.Bar(
                x=daily_sum['date'],
                y=daily_sum['quantity'],
                marker_color='#90CAF9'
            ))
            
            fig.update_layout(
                title="Napi mennyiség eloszlás",
                xaxis_title="Dátum",
                yaxis_title="Mennyiség (kg)",
                template="plotly_white" if not is_dark else "plotly_dark"
            )
            
            st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            st.error(f"Hiba a grafikon rajzolása során: {str(e)}")

def render_custom_view(df, start_date, end_date):
    """Egyedi időszak nézet megjelenítése"""
    # Az időszakra eső napok generálása
    days = (end_date - start_date).days + 1
    custom_dates = [start_date + timedelta(days=i) for i in range(days)]
    
    # Értékek összegzése az időszakra
    total_quantity = df['quantity'].sum()
    if 'price' in df.columns:
        total_value = (df['quantity'] * df['price']).sum()
    else:
        total_value = 0
    
    # Összesítő kártya megjelenítése
    summary_content = f"""
    <p><strong>Időszak:</strong> {start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}</p>
    <p><strong>Összes mennyiség:</strong> {format_quantity(total_quantity)}</p>
    <p><strong>Összes érték:</strong> {format_price(total_value)}</p>
    <p><strong>Események száma:</strong> {len(df)}</p>
    """
    
    display_card(
        title="Időszak összesítő",
        content=summary_content,
        icon="📊"
    )
    
    # Események renderelése
    if len(df) > 0:
        # Asztali nézeten grafikon az adatokról
        if not is_mobile:
            try:
                # Napi összegzések
                daily_sum = df.groupby('date')['quantity'].sum().reset_index()
                
                fig = go.Figure(data=go.Bar(
                    x=daily_sum['date'],
                    y=daily_sum['quantity'],
                    marker_color='#42A5F5'
                ))
                
                fig.update_layout(
                    title="Napi mennyiség eloszlás",
                    xaxis_title="Dátum",
                    yaxis_title="Mennyiség (kg)",
                    template="plotly_white" if not is_dark else "plotly_dark"
                )
                
                st.plotly_chart(fig, use_container_width=True)
            except Exception as e:
                st.error(f"Hiba a grafikon rajzolása során: {str(e)}")
        
        # Termékenkénti megoszlás (kördiagram)
        try:
            if 'product_name' in df.columns:
                product_sum = df.groupby('product_name')['quantity'].sum().reset_index()
                
                fig = go.Figure(data=go.Pie(
                    labels=product_sum['product_name'],
                    values=product_sum['quantity'],
                    hole=0.4
                ))
                
                fig.update_layout(
                    title="Termékenkénti megoszlás",
                    template="plotly_white" if not is_dark else "plotly_dark"
                )
                
                st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            st.error(f"Hiba a kördiagram rajzolása során: {str(e)}")

def main():
    """Főprogram"""
    # Oldal tartalom megjelenítése
    show_operator_calendar()

if __name__ == "__main__":
    main() 