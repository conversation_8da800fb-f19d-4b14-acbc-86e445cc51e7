# TOVACVV Demo oldal
"""
TOVACVV validációs szab<PERSON>ly bemutató oldal.
"""
import streamlit as st
from components.tovacvv_form import render_tovacvv_form
from utils.page_utils import set_page_config

# Oldal beállítások
set_page_config("TOVACVV Demo", "🔒")

# Oldal tartalma
st.title("TOVACVV Validációs Szabály Demo")

st.markdown("""
Ez az oldal bemutatja a TOVACVV validációs szab<PERSON>ly hasz<PERSON>lat<PERSON>t.

A TOVACVV egy 3 karakteres kód, amely betűket és számokat tartalmazhat.
""")

# TOVACVV form megjelenítése
success, form_data = render_tovacvv_form()

# Eredmény megjelenítése
if success:
    st.markdown("---")
    st.subheader("Form adatok")
    st.json(form_data) 