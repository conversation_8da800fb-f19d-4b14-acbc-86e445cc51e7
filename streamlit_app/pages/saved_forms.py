# Mentett űrlapok kezelése
"""
Mentett űrlapok kezelése és folytatása.
"""
import streamlit as st
from utils.page_utils import set_page_config
from utils.storage import get_saved_forms, load_form, delete_form

# Oldal beállítások
set_page_config("Mentett űrlapok", "📝")

# Oldal tartalma
st.title("Mentett űrlapok kezelése")

st.markdown("""
Ez az oldal lehetővé teszi a mentett űrlapok kezelését és folytatását.

A mentett űrlapokat betöltheti, folytathatja a kitöltést, vagy tö<PERSON>.
""")

# Mentett űrlapok lekérése
saved_forms = get_saved_forms()

if not saved_forms:
    st.info("Nincsenek mentett űrlapok.")
else:
    # Mentett űrlapok listázása
    st.subheader("Mentett űrlapok")
    
    for form_id, form_info in saved_forms.items():
        with st.expander(f"Űrlap: {form_info['name']} (ID: {form_id})"):
            st.write(f"Utoljára módosítva: {form_info['last_modified']}")
            st.write(f"Lépés: {form_info['current_step'] + 1}/{form_info['total_steps']}")
            
            # Űrlap betöltése gomb
            if st.button("Űrlap betöltése", key=f"load_{form_id}"):
                # Űrlap betöltése
                form_data = load_form(form_id)
                
                # Űrlap adatok mentése a session state-be
                st.session_state.form_data = form_data
                st.session_state.current_step = form_info['current_step']
                
                # Átirányítás az űrlap oldalára
                st.markdown(f"[Átirányítás az űrlap oldalára](/tovabb_demo)")
            
            # Űrlap törlése gomb
            if st.button("Űrlap törlése", key=f"delete_{form_id}"):
                # Űrlap törlése
                delete_form(form_id)
                
                # Oldal újratöltése
                st.rerun() 