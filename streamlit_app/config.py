"""
Compatibility module for import config
"""
import os

# Application config
APP_NAME = os.getenv("APP_NAME", "POM APP")
COMPANY_NAME = os.getenv("COMPANY_NAME", "Zöldség Világ Kft.")

# API config
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")

# API kliens konfiguráció
API_CLIENT_CONFIG = {
    "timeout": 10,  # másodperc
    "retry_count": 3,
    "retry_backoff_factor": 0.5,
    "retry_status_forcelist": [408, 429, 500, 502, 503, 504],
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE"]
}

# Debug: Print config values
print(f"==== CONFIG LOADED ====")
print(f"APP_NAME: {APP_NAME}")
print(f"COMPANY_NAME: {COMPANY_NAME}")
print(f"API_HOST: {API_HOST}")
print(f"API_BASE_URL: {API_BASE_URL}")
print("=====================")

# Import all other attributes from app_config
from app_config import *
