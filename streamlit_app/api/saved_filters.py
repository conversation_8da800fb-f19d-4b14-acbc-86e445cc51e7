"""
Saved filters API client for Streamlit frontend
"""
import requests
import streamlit as st
import logging
from typing import List, Dict, Any, Optional

# Rugalmas import a session utils-hoz
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import get_auth_token
    logging.info("Successfully imported session utils from Docker path")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import get_auth_token
        logging.info("Successfully imported session utils directly")
    except ImportError:
        try:
            # Teljes útvonal (streamlit_app-ból)
            from streamlit_app.utils.session import get_auth_token
            logging.info("Successfully imported session utils from streamlit_app path")
        except ImportError:
            # Fallback funkció
            logging.warning("Could not import session utils, using fallback")
            def get_auth_token():
                """Fallback auth token getter"""
                return None

# Rugalmas import a config-hoz
try:
    # Docker környezetben ez a helyes import
    import app_config as config
except ImportError:
    try:
        # Streamlit app relatív útvonal
        from streamlit_app import app_config as config
    except ImportError:
        # Fallback config
        logging.warning("Could not import app_config, using fallback")
        class FallbackConfig:
            API_BASE_URL = "http://api:8000"  # Docker container service name
        config = FallbackConfig()

# Logger beállítása
logger = logging.getLogger(__name__)


def get_saved_filters(filter_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get all saved filters for the current user
    
    Args:
        filter_type: Optional filter type to filter by (e.g., "offer")
        
    Returns:
        List of saved filters or empty list on error
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for saved filters request")
            return []
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        params = {}
        if filter_type:
            params["filter_type"] = filter_type
        
        response = requests.get(
            f"{config.API_BASE_URL}/api/saved-filters",
            headers=headers,
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to get saved filters: {response.status_code} - {response.text}")
            return []
            
    except requests.RequestException as e:
        logger.error(f"Network error getting saved filters: {e}")
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting saved filters: {e}")
        return []


def get_saved_filter(filter_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific saved filter by ID
    
    Args:
        filter_id: ID of the filter to retrieve
        
    Returns:
        Saved filter data or None on error
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for saved filter request")
            return None
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{config.API_BASE_URL}/api/saved-filters/{filter_id}",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            logger.warning(f"Saved filter {filter_id} not found")
            return None
        else:
            logger.error(f"Failed to get saved filter {filter_id}: {response.status_code} - {response.text}")
            return None
            
    except requests.RequestException as e:
        logger.error(f"Network error getting saved filter {filter_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting saved filter {filter_id}: {e}")
        return None


def create_saved_filter(name: str, description: Optional[str], filter_type: str, 
                       filter_data: Dict[str, Any], is_default: bool = False) -> Optional[Dict[str, Any]]:
    """
    Create a new saved filter
    
    Args:
        name: Name of the filter
        description: Optional description
        filter_type: Type of filter (e.g., "offer")
        filter_data: Filter configuration data
        is_default: Whether this should be the default filter
        
    Returns:
        Created filter data or None on error
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for create saved filter request")
            return None
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "name": name,
            "description": description,
            "filter_type": filter_type,
            "filter_data": filter_data,
            "is_default": is_default
        }
        
        response = requests.post(
            f"{config.API_BASE_URL}/api/saved-filters",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 201:
            return response.json()
        else:
            logger.error(f"Failed to create saved filter: {response.status_code} - {response.text}")
            return None
            
    except requests.RequestException as e:
        logger.error(f"Network error creating saved filter: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error creating saved filter: {e}")
        return None


def update_saved_filter(filter_id: int, name: Optional[str] = None, 
                       description: Optional[str] = None, filter_data: Optional[Dict[str, Any]] = None,
                       is_default: Optional[bool] = None) -> Optional[Dict[str, Any]]:
    """
    Update an existing saved filter
    
    Args:
        filter_id: ID of the filter to update
        name: Optional new name
        description: Optional new description
        filter_data: Optional new filter data
        is_default: Optional new default status
        
    Returns:
        Updated filter data or None on error
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for update saved filter request")
            return None
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        data = {}
        if name is not None:
            data["name"] = name
        if description is not None:
            data["description"] = description
        if filter_data is not None:
            data["filter_data"] = filter_data
        if is_default is not None:
            data["is_default"] = is_default
        
        response = requests.put(
            f"{config.API_BASE_URL}/api/saved-filters/{filter_id}",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to update saved filter {filter_id}: {response.status_code} - {response.text}")
            return None
            
    except requests.RequestException as e:
        logger.error(f"Network error updating saved filter {filter_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error updating saved filter {filter_id}: {e}")
        return None


def delete_saved_filter(filter_id: int) -> bool:
    """
    Delete a saved filter
    
    Args:
        filter_id: ID of the filter to delete
        
    Returns:
        True if deleted successfully, False otherwise
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for delete saved filter request")
            return False
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.delete(
            f"{config.API_BASE_URL}/api/saved-filters/{filter_id}",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 204:
            return True
        else:
            logger.error(f"Failed to delete saved filter {filter_id}: {response.status_code} - {response.text}")
            return False
            
    except requests.RequestException as e:
        logger.error(f"Network error deleting saved filter {filter_id}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error deleting saved filter {filter_id}: {e}")
        return False


def set_default_filter(filter_id: int) -> Optional[Dict[str, Any]]:
    """
    Set a filter as the default for the user
    
    Args:
        filter_id: ID of the filter to set as default
        
    Returns:
        Updated filter data or None on error
    """
    try:
        token = get_auth_token()
        if not token:
            logger.warning("No auth token available for set default filter request")
            return None
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{config.API_BASE_URL}/api/saved-filters/{filter_id}/set-default",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to set default filter {filter_id}: {response.status_code} - {response.text}")
            return None
            
    except requests.RequestException as e:
        logger.error(f"Network error setting default filter {filter_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error setting default filter {filter_id}: {e}")
        return None