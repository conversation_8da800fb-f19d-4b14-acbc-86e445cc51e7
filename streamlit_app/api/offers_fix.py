# Ajánlat-kezelés API hívások - Javasolt javítás
"""
Ajánlat-kezelési API hívások javított verziója.
A fő get_offer függvény javítva a helyes ID kezeléssel.
"""
import requests
from utils.session import get_auth_token
import streamlit as st
import app_config as config
import datetime
from typing import List, Union
import math
import time
import logging

# Logger beállítása
logger = logging.getLogger(__name__)

def get_auth_headers():
    """
    Autentikációs fejlécek összeállítása.
    
    Returns:
        dict: Az autentikációs fejléceket tartalmazó szótár.
    """
    token = get_auth_token()
    if not token:
        return {}
    return {"Authorization": f"Bearer {token}"}

def get_offer(offer_id):
    """
    Egy ajánlat lekérdezése API hívás - JAVÍTOTT VERZIÓ
    
    Args:
        offer_id (int/str): A<PERSON><PERSON><PERSON> a<PERSON>osítója (rugalmas típuskezeléssel)
        
    Returns:
        tuple: (bool, dict/str) - <PERSON><PERSON><PERSON> (True/False) és ajánlat adatai vagy hibaüzenet
    """
    try:
        # Egyszerűsített ID konverzió - csak az alapvető típusellenőrzéssel
        original_id = offer_id  # Eredeti érték megőrzése a logoláshoz
        
        if isinstance(offer_id, str):
            try:
                offer_id = int(offer_id.strip())
            except (ValueError, TypeError):
                # Hagyni az eredeti formában, ha nem konvertálható
                # és hagyni az API-t, hogy kezelje a hibát
                pass
        
        # Debug információk
        logger.info(f"GET_OFFER API CALL: Original ID: {original_id}, Converted ID: {offer_id}, Type: {type(offer_id)}")
        
        # Ellenőrizzük, hogy van-e autentikáció
        headers = get_auth_headers()
        if not headers:
            return False, "Nincs bejelentkezve"
        
        # API kérés
        response = requests.get(
            f"{config.API_BASE_URL}/offers/{offer_id}",
            headers=headers,
            timeout=10  # 10 másodperces timeout
        )
        
        logger.info(f"API Response Status: {response.status_code}")
        
        # Sikeres válasz kezelése
        if response.status_code == 200:
            try:
                offer_data = response.json()
                
                # Ellenőrizzük a válasz formátumát
                if not isinstance(offer_data, dict):
                    logger.error(f"Invalid response format: {type(offer_data)}")
                    return False, f"Érvénytelen API válasz formátum: {type(offer_data)}"
                
                # Ellenőrizzük, hogy az ID létezik-e a válaszban
                if "id" not in offer_data:
                    logger.error(f"Missing ID in API response for offer {offer_id}")
                    return False, "Hiányzó ID az API válaszból"
                
                # Kiegészítjük a display nevekkel
                user = offer_data.get("user", {})
                offer_data["user_name"] = user.get("company_name", "") if user else ""
                
                product_type = offer_data.get("product_type", {})
                offer_data["product_name"] = product_type.get("name", "") if product_type else ""
                
                # Státusz fordítási map
                status_mapping = {
                    "CREATED": "Létrehozva",
                    "CONFIRMED_BY_COMPANY": "Megerősítve",
                    "ACCEPTED_BY_USER": "Elfogadva",
                    "REJECTED_BY_USER": "Elutasítva",
                    "FINALIZED": "Teljesítve",
                    "MODIFIED": "Módosítva"
                }
                
                # Státusz megjelenítési név hozzáadása
                if "status" in offer_data:
                    current_status = offer_data["status"]
                    offer_data["status_display"] = status_mapping.get(current_status, current_status)
                
                logger.info(f"Successfully loaded offer {offer_id}")
                return True, offer_data
                
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}")
                return False, f"Hiba a válasz feldolgozásakor: {str(e)}"
        
        # Hibaüzenet feldolgozása
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Ajánlat lekérdezési hiba")
        except Exception as e:
            logger.error(f"Error parsing error response: {str(e)}")
            error_message = f"Ajánlat lekérdezési hiba (HTTP {response.status_code})"
        
        # Specifikus hibaüzenetek
        if response.status_code == 404:
            return False, f"Nem található ajánlat ezzel az azonosítóval: {offer_id}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
        
    except requests.Timeout:
        logger.error(f"Timeout while fetching offer {offer_id}")
        return False, "Az API kérés időtúllépés miatt megszakadt"
    except requests.ConnectionError:
        logger.error(f"Connection error while fetching offer {offer_id}")
        return False, "Nem sikerült kapcsolódni az API szerverhez"
    except Exception as e:
        logger.error(f"Unexpected error in get_offer: {str(e)}")
        return False, f"Hiba az ajánlat lekérdezésekor: {str(e)}"

# Segédfüggvény az ajánlatok cache-eléséhez
def get_cached_offer(offer_id, max_age_seconds=300):
    """
    Ajánlat lekérése cache-ből, frissítéssel ha túl régi vagy hiányzó.
    
    Args:
        offer_id (int): Ajánlat azonosító
        max_age_seconds (int): Maximum cache élettartam másodpercben
        
    Returns:
        tuple: (success, offer_data/error_message)
    """
    current_time = time.time()
    
    # Inicializáljuk a cache-t, ha még nem létezik
    if "offer_cache" not in st.session_state:
        st.session_state.offer_cache = {}
    
    cache_key = str(offer_id)  # Bizonyosodjunk meg, hogy string kulcs legyen
    
    # Cache ellenőrzése
    if (cache_key in st.session_state.offer_cache and 
        "timestamp" in st.session_state.offer_cache[cache_key] and
        current_time - st.session_state.offer_cache[cache_key]["timestamp"] < max_age_seconds):
        # A cache elég friss
        logger.info(f"Using cached offer data for {offer_id}")
        return True, st.session_state.offer_cache[cache_key]["data"]
    
    # Cache frissítése API-n keresztül
    logger.info(f"Fetching fresh offer data for {offer_id}")
    success, result = get_offer(offer_id)
    
    if success:
        # Cache frissítése
        st.session_state.offer_cache[cache_key] = {
            "data": result,
            "timestamp": current_time
        }
        return True, result
    
    # Hiba esetén töröljük a cache-t, ha létezik
    if cache_key in st.session_state.offer_cache:
        del st.session_state.offer_cache[cache_key]
    
    return False, result

# Segédfüggvény az ajánlat ID-k normalizálásához
def normalize_id(id_value):
    """
    ID értékek normalizálása konzisztens típusra.
    
    Args:
        id_value (any): Az eredeti ID érték (str, int, stb.)
        
    Returns:
        int: Normalizált ID vagy None ha érvénytelen
    """
    if id_value is None:
        return None
        
    if isinstance(id_value, int):
        return id_value
        
    if isinstance(id_value, str):
        try:
            return int(id_value.strip())
        except (ValueError, TypeError):
            return None
    
    return None

# Segédfüggvény a HTTP hibák egységes kezeléséhez
def handle_api_error(response, operation_name):
    """
    API hibák egységes kezelése részletes üzenetekkel.
    
    Args:
        response (Response): API válasz objektum
        operation_name (str): Művelet neve (pl. "ajánlat lekérdezés")
        
    Returns:
        str: Felhasználóbarát hibaüzenet
    """
    try:
        error_data = response.json()
        if isinstance(error_data, dict) and "detail" in error_data:
            error_message = error_data["detail"]
        else:
            error_message = f"{operation_name} hiba"
    except Exception:
        error_message = f"{operation_name} hiba (HTTP {response.status_code})"
    
    # Speciális hibakódok kezelése
    if response.status_code == 404:
        return f"A kért erőforrás nem található ({operation_name})"
    elif response.status_code == 401:
        return f"Nincs megfelelő jogosultsága ({operation_name}). Kérjük jelentkezzen be újra."
    elif response.status_code == 400:
        return f"Érvénytelen kérés: {error_message}"
    elif response.status_code >= 500:
        return f"Szerver hiba történt ({operation_name}). Kérjük próbálja később."
        
    return f"{operation_name} hiba: {error_message}"