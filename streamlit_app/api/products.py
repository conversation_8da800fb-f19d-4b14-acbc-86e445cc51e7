# Termék-kezelés API hívások
"""
Termék-kezelési API hívások.
"""
import requests
import logging

# Rugalmas import a session utils-hoz
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import get_auth_token
    logging.info("Successfully imported session utils from Docker path in products.py")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import get_auth_token
        logging.info("Successfully imported session utils directly in products.py")
    except ImportError:
        try:
            # Teljes útvonal (streamlit_app-ból)
            from streamlit_app.utils.session import get_auth_token
            logging.info("Successfully imported session utils from streamlit_app path in products.py")
        except ImportError:
            # Fallback funkció
            logging.warning("Could not import session utils in products.py, using fallback")
            def get_auth_token():
                """Fallback auth token getter"""
                return None

# Rugalmas import a config-hoz
try:
    # Docker környezetben ez a helyes import
    import app_config as config
except ImportError:
    try:
        # Streamlit app relatív útvonal
        from streamlit_app import app_config as config
    except ImportError:
        # Fallback config
        logging.warning("Could not import app_config in products.py, using fallback")
        class FallbackConfig:
            API_BASE_URL = "http://api:8000"  # Docker container service name
        config = FallbackConfig()

# Közös importok az API-khoz
try:
    # Rugalmas import kezelés a központi import modulból
    from api.imports import get_auth_token, config
    logging.info("Successfully imported from common API imports")
except ImportError:
    try:
        # Docker környezetben lehet, hogy ez működik
        from pages.api.imports import get_auth_token, config
        logging.info("Successfully imported from Docker API imports path")
    except ImportError:
        # Ha nincs közös import modul, fallback
        logging.warning("Using fallback imports in products.py")
        try:
            # Docker környezetben ez a helyes import
            from pages.utils.session import get_auth_token
            import app_config as config
        except ImportError:
            # Közvetlen import (fejlesztői környezetben)
            try:
                from utils.session import get_auth_token
                import app_config as config
            except ImportError:
                # Fallback
                def get_auth_token():
                    """Fallback auth token getter"""
                    return None
                
                class FallbackConfig:
                    API_BASE_URL = "http://api:8000"
                config = FallbackConfig()

def get_product_categories(params=None):
    """
    Termékkategóriák lekérdezése API hívás
    
    Args:
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és kategóriák listája vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(
            f"{config.API_BASE_URL}/products/categories",
            headers=headers,
            params=params or {}
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategóriák lekérdezési hiba")
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_product_types(category_id=None, params=None):
    """
    Terméktípusok lekérdezése API hívás
    
    Args:
        category_id (int): Kategória azonosítója (opcionális)
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és típusok listája vagy hibaüzenet
    """
    print(f"==== GET_PRODUCT_TYPES API CALL ====")
    print(f"Category ID: {category_id}")
    print(f"Params: {params}")
    print(f"API Base URL: {config.API_BASE_URL}")
    
    token = get_auth_token()
    print(f"Token: {token[:10]}..." if token else "Token: None")
    
    if not token:
        print("No token available")
        return False, "Nincs bejelentkezve"
    
    headers = {"Authorization": f"Bearer {token}"}
    print(f"Headers: {headers}")
    
    query_params = params or {}
    if category_id:
        query_params["category_id"] = category_id
    
    print(f"Query params: {query_params}")
    
    try:
        # Construct the full URL
        url = f"{config.API_BASE_URL}/products/types"
        print(f"Full URL: {url}")
        
        response = requests.get(
            url,
            headers=headers,
            params=query_params
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response data count: {len(data)}")
            return True, data
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípusok lekérdezési hiba")
        print(f"Error: {error_message}")
        return False, error_message
    
    except requests.RequestException as e:
        print(f"Network error: {str(e)}")
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return False, f"Hiba: {str(e)}"
    finally:
        print("============================")

def get_quality_grades(product_type_id, params=None):
    """
    Minőségi besorolások lekérdezése API hívás
    
    Args:
        product_type_id (int): Terméktípus azonosítója
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és besorolások listája vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    query_params = params or {}
    query_params["product_type_id"] = product_type_id
    
    try:
        # FONTOS: ide a helyes végpont
        response = requests.get(
            f"{config.API_BASE_URL}/products/grades", 
            headers=headers,
            params=query_params
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolások lekérdezési hiba")
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_product_category(category_data):
    """
    Új termékkategória létrehozása API hívás (csak admin számára)
    
    Args:
        category_data (dict): Kategória adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott kategória vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/categories",
            headers=headers,
            json=category_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategória létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_product_type(type_data):
    """
    Új terméktípus létrehozása API hívás (csak admin számára)
    
    Args:
        type_data (dict): Típus adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott típus vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/types",
            headers=headers,
            json=type_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_quality_grade(grade_data):
    """
    Új minőségi besorolás létrehozása API hívás (csak admin számára)
    
    Args:
        grade_data (dict): Besorolás adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott besorolás vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/quality-grades",
            headers=headers,
            json=grade_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_product_category(category_id, category_data):
    """
    Termékkategória módosítása API hívás (csak admin számára)
    
    Args:
        category_id (int): Kategória azonosítója
        category_data (dict): Módosítandó adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített kategória vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/categories/{category_id}",
            headers=headers,
            json=category_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategória módosítási hiba")
        
        if response.status_code == 404:
            return False, "Nem található kategória ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_product_type(type_id, type_data):
    """
    Terméktípus módosítása API hívás (csak admin számára)
    
    Args:
        type_id (int): Típus azonosítója
        type_data (dict): Módosítandó adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített típus vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers,
            json=type_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus módosítási hiba")
        
        if response.status_code == 404:
            return False, "Nem található típus ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_quality_grade(grade_id, grade_data):
    """
    Minőségi besorolás módosítása API hívás (csak admin számára)
    
    Args:
        grade_id (int): Besorolás azonosítója
        grade_data (dict): Módosítandó adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített besorolás vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/quality-grades/{grade_id}",
            headers=headers,
            json=grade_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás módosítási hiba")
        
        if response.status_code == 404:
            return False, "Nem található besorolás ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_product_category(category_id):
    """
    Termékkategória törlése API hívás (csak admin számára)
    
    Args:
        category_id (int): Kategória azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és üzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/categories/{category_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, "Termékkategória sikeresen törölve"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategória törlési hiba")
        
        if response.status_code == 404:
            return False, "Nem található kategória ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "constraint" in error_message.lower():
            return False, "Nem törölhető a kategória, mert termékek tartoznak hozzá"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_product_type(type_id):
    """
    Terméktípus törlése API hívás (csak admin számára)
    
    Args:
        type_id (int): Típus azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és üzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, "Terméktípus sikeresen törölve"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus törlési hiba")
        
        if response.status_code == 404:
            return False, "Nem található típus ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "constraint" in error_message.lower():
            return False, "Nem törölhető a típus, mert ajánlatok vagy minőségi besorolások tartoznak hozzá"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_quality_grade(grade_id):
    """
    Minőségi besorolás törlése API hívás (csak admin számára)
    
    Args:
        grade_id (int): Besorolás azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és üzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/quality-grades/{grade_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, "Minőségi besorolás sikeresen törölve"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás törlési hiba")
        
        if response.status_code == 404:
            return False, "Nem található besorolás ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "constraint" in error_message.lower():
            return False, "Nem törölhető a besorolás, mert ajánlatok tartoznak hozzá"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_product_type(type_id):
    """
    Egy terméktípus lekérdezése API hívás
    
    Args:
        type_id (int): Terméktípus azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és típus adatai vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus lekérdezési hiba")
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_quality_grade(quality_grade_id):
    """
    Minőségi osztály adatainak lekérése a megadott azonosító alapján.
    
    Args:
        quality_grade_id (int): A minőségi osztály azonosítója
        
    Returns:
        tuple: (sikeres, eredmény) formátumban
    """
    try:
        # Autentikációs token lekérése
        token = get_auth_token()
        if not token:
            return False, "Nincs érvényes autentikációs token"
        
        # Fejlécek beállítása
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # API hívás
        response = requests.get(
            f"{config.API_BASE_URL}/products/quality-grades/{quality_grade_id}",
            headers=headers
        )
        
        # Válasz ellenőrzése
        if response.status_code == 200:
            return True, response.json()
        else:
            error_msg = f"Hiba a minőségi osztály lekérésekor: {response.status_code}"
            try:
                error_details = response.json()
                error_msg += f", {error_details.get('detail', '')}"
            except:
                error_msg += f", {response.text}"
            return False, error_msg
    
    except Exception as e:
        error_msg = f"Kivétel a minőségi osztály lekérésekor: {str(e)}"
        print(error_msg)  # Naplózás a konzolra
        return False, error_msg
