"""
Notifications API módulusa a frontend számára.
"""
from typing import Dict, Any, List, Tuple, Optional
import requests
import streamlit as st
from utils.session import get_auth_token, clear_session, set_user_session
import app_config as config

# ========================================================
# Modul-szintű változók a konfigurációhoz
# ========================================================
# Alapértelmezett végpont prefix
ENDPOINT_PREFIX = ""

def update_endpoint_prefix(prefix: str) -> None:
    """
    Frissíti az API végpont előtagját futásidőben, ha szükséges.
    
    Args:
        prefix (str): Az új előtag, amit a /notifications elé kell illeszteni
                      (pl. "/api" vagy "/api/v1" vagy "")
    """
    global ENDPOINT_PREFIX
    old_prefix = ENDPOINT_PREFIX
    ENDPOINT_PREFIX = prefix
    print(f"Notifications API - Végpont előtag frissítve: '{old_prefix}' -> '{prefix}'")

# ========================================================
# Segédfüggvények
# ========================================================

def _make_api_request(method, endpoint, data=None, json=None, params=None):
    """
    Általános API kérés küldése a felhasználókezelési végpontokra.
    
    Args:
        method (str): HTTP metódus (GET, POST, PUT, DELETE)
        endpoint (str): API végpont relatív elérési útja (pl. "/users/me")
        data (dict, optional): Form adatok
        json (dict, optional): JSON adatok
        params (dict, optional): Query paraméterek
        
    Returns:
        tuple: (bool, Any) - Sikeres kérés (True/False) és válasz vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        # Az előtag hozzáadása a végponthoz, ha van beállítva
        if ENDPOINT_PREFIX and not endpoint.startswith(ENDPOINT_PREFIX):
            full_endpoint = f"{ENDPOINT_PREFIX}{endpoint}"
        else:
            full_endpoint = endpoint
            
        # Teljes URL összeállítása
        url = f"{config.API_BASE_URL}{full_endpoint}"
        
        # Diagnosztikai információk
        print(f"API hívás: {method} {url}")
        print(f"Paraméterek: {params}")
        
        # Headers összeállítása
        headers = {"Authorization": f"Bearer {token}"}
        import streamlit as st
        from utils.session import get_current_user
        debug_mode = st.session_state.get("debug_mode", False)
        user = get_current_user()
        is_admin = user and user.get("role", "").lower() == "admin"
        if debug_mode and is_admin:
            print(f"[DEBUG] _make_api_request: endpoint={endpoint}, token első 10 karakter: {token[:10] if token else 'Nincs token'}")
        
        # Konfigurációk az API híváshoz
        timeout = config.API_CLIENT_CONFIG.get("timeout", 10)
        
        # Kérés küldése
        if method == "GET":
            response = requests.get(url, headers=headers, params=params, timeout=timeout)
        elif method == "POST":
            response = requests.post(url, headers=headers, data=data, json=json, timeout=timeout)
        elif method == "PUT":
            response = requests.put(url, headers=headers, data=data, json=json, timeout=timeout)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers, timeout=timeout)
        else:
            return False, f"Nem támogatott HTTP metódus: {method}"
        
        # Diagnosztika - státuszkód és válasz mérete
        if debug_mode and is_admin:
            print(f"[DEBUG] _make_api_request: válasz státusz: {response.status_code}")
            print(f"[DEBUG] _make_api_request: válasz első 100 karakter: {response.text[:100]}")
        print(f"Válasz mérete: {len(response.content)} bájt")
        
        # Státuszkód ellenőrzése
        if response.status_code == 200:
            try:
                response_data = response.json()
                return True, response_data
            except ValueError as e:
                print(f"Hiba a JSON elemzésénél: {str(e)}")
                # Megpróbáljuk a választ szövegként visszaadni
                return False, f"Érvénytelen JSON válasz: {response.text[:100]}"
        elif response.status_code == 401:
            # Érvénytelen token esetén töröljük a munkamenetet
            print("Érvénytelen token (401)")
            clear_session()
            return False, "Érvénytelen hitelesítés. Kérjük jelentkezzen be újra."
        elif response.status_code == 403:
            print("Hozzáférés megtagadva (403)")
            return False, "Nincs megfelelő jogosultsága a művelethez."
        elif response.status_code == 404:
            print(f"Nem található erőforrás (404): {endpoint}")
            return False, "A kért erőforrás nem található."
        else:
            try:
                error_data = response.json()
                error_message = error_data.get("detail", f"Hiba: {response.status_code}")
                print(f"API hibaüzenet: {error_message}")
                return False, error_message
            except ValueError:
                print(f"Nem JSON válasz, státusz: {response.status_code}")
                return False, f"Hiba: {response.status_code} - {response.text[:100]}"
    except requests.exceptions.Timeout:
        print(f"Időtúllépés az API hívásnál: {url}")
        return False, "Időtúllépés történt a kérés során. Kérjük próbálja újra később."
    except requests.exceptions.ConnectionError:
        print(f"Kapcsolódási hiba az API hívásnál: {url}")
        return False, "Nem sikerült kapcsolódni az API szerverhez. Kérjük ellenőrizze a hálózati kapcsolatát."
    except Exception as e:
        print(f"Kivétel az API hívásnál: {str(e)}")
        import traceback
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return False, f"Váratlan hiba történt: {str(e)}"


def get_notifications(
    include_read: bool = False,
    skip: int = 0,
    limit: int = 100
) -> Tuple[bool, Dict[str, Any]]:
    """
    Felhasználóhoz tartozó értesítések lekérése.
    """
    try:
        import streamlit as st
        from utils.session import get_auth_token, get_current_user
        debug_mode = st.session_state.get("debug_mode", False)
        user = get_current_user()
        is_admin = user and user.get("role", "").lower() == "admin"
        if debug_mode and is_admin:
            print(f"[DEBUG] get_notifications() hívás: include_read={include_read}, skip={skip}, limit={limit}")
        token = get_auth_token()
        if debug_mode and is_admin:
            print(f"[DEBUG] get_notifications() token első 10 karakter: {token[:10] if token else 'Nincs token'}")
        params = {
            "include_read": str(include_read).lower(),
            "skip": skip,
            "limit": limit
        }
        return _make_api_request("GET", "/notifications/me", params=params)
    except Exception as e:
        print(f"Kivétel a get_notifications függvényben: {str(e)}")
        import traceback
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return False, f"Váratlan hiba: {str(e)}"
    """
    Felhasználóhoz tartozó értesítések lekérése.
    
    Args:
        include_read (bool): Tartalmazza-e az olvasott értesítéseket (alapértelmezett: False)
        skip (int): Hány elemet hagyjon ki (lapozáshoz)
        limit (int): Maximum hány elemet adjon vissza (lapozáshoz)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és értesítések vagy hibaüzenet
    """
    try:
        print(f"Értesítések lekérése: include_read={include_read}, skip={skip}, limit={limit}")
        params = {
            "include_read": str(include_read).lower(),
            "skip": skip,
            "limit": limit
        }
        return _make_api_request("GET", "/notifications/me", params=params)
    except Exception as e:
        print(f"Kivétel a get_notifications függvényben: {str(e)}")
        import traceback
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return False, f"Váratlan hiba: {str(e)}"


def get_all_notifications(
    include_read: bool = False,
    skip: int = 0,
    limit: int = 100
) -> Tuple[bool, Dict[str, Any]]:
    """
    Összes értesítés lekérése (admin).
    
    Args:
        include_read (bool): Tartalmazza-e az olvasott értesítéseket (alapértelmezett: False)
        skip (int): Hány elemet hagyjon ki (lapozáshoz)
        limit (int): Maximum hány elemet adjon vissza (lapozáshoz)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és értesítések vagy hibaüzenet
    """
    params = {
        "include_read": str(include_read).lower(),
        "skip": skip,
        "limit": limit
    }
    return _make_api_request("GET", "/notifications/all", params=params)


def mark_notification_read(notification_id: int) -> Tuple[bool, Dict[str, Any]]:
    """
    Egy értesítés olvasottnak jelölése.
    
    Args:
        notification_id (int): Az értesítés azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres művelet (True/False) és frissített értesítés vagy hibaüzenet
    """
    return _make_api_request("PUT", f"/notifications/{notification_id}/read")


def mark_all_notifications_read() -> Tuple[bool, Dict[str, Any]]:
    """
    Összes értesítés olvasottnak jelölése.
    
    Returns:
        tuple: (bool, dict/str) - Sikeres művelet (True/False) és eredmény vagy hibaüzenet
    """
    return _make_api_request("PUT", "/notifications/read-all")


def create_notification(
    message: str,
    type: str = "info",
    user_id: Optional[int] = None,
    detail: Optional[str] = None,
    target_roles: Optional[str] = None,
    related_entity_type: Optional[str] = None,
    related_entity_id: Optional[int] = None
) -> Tuple[bool, Dict[str, Any]]:
    """
    Új értesítés létrehozása (admin).
    
    Args:
        message (str): Az értesítés üzenete
        type (str): Értesítés típusa (info, success, warning, error, update)
        user_id (int, optional): Címzett felhasználó azonosítója (ha van)
        detail (str, optional): Részletes leírás
        target_roles (str, optional): Célzott szerepkörök vesszővel elválasztva (pl. "admin,ügyintéző")
        related_entity_type (str, optional): Kapcsolódó entitás típusa (pl. "offer")
        related_entity_id (int, optional): Kapcsolódó entitás azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres művelet (True/False) és létrehozott értesítés vagy hibaüzenet
    """
    data = {
        "message": message,
        "type": type,
        "user_id": user_id,
        "detail": detail,
        "target_roles": target_roles,
        "related_entity_type": related_entity_type,
        "related_entity_id": related_entity_id
    }
    # None értékek eltávolítása
    data = {k: v for k, v in data.items() if v is not None}
    
    return _make_api_request("POST", "/notifications/", json=data) 