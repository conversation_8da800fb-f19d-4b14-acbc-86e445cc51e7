"""
Közös importálási segédmodul az API réteg számára.
Ez a modul rugalmasan kezeli az importálásokat a különböző környezetekben (fejlesztői és Docker).
"""
import logging
import sys
import os

# Konfigurálás
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Session utils import
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import (
        get_auth_token, 
        clear_session, 
        set_user_session,
        get_current_user,
        is_authenticated
    )
    logger.info("Successfully imported session utils from Docker path")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import (
            get_auth_token, 
            clear_session, 
            set_user_session,
            get_current_user,
            is_authenticated
        )
        logger.info("Successfully imported session utils directly")
    except ImportError:
        try:
            # Tel<PERSON><PERSON> (streamlit_app-ból)
            from streamlit_app.utils.session import (
                get_auth_token, 
                clear_session, 
                set_user_session,
                get_current_user,
                is_authenticated
            )
            logger.info("Successfully imported session utils from streamlit_app path")
        except ImportError:
            # Fallback funkciók
            logger.warning("Could not import session utils, using fallbacks")
            
            def get_auth_token():
                """Fallback auth token getter"""
                env_token = os.environ.get("API_AUTH_TOKEN")
                logger.info(f"Using fallback auth token from env: {bool(env_token)}")
                return env_token
            
            def clear_session():
                """Fallback session clearer"""
                logger.info("Using fallback clear_session (no-op)")
                pass
            
            def set_user_session(user_data, token):
                """Fallback session setter"""
                logger.info(f"Using fallback set_user_session: {user_data.get('name') if user_data else None}")
                pass
            
            def get_current_user():
                """Fallback current user getter"""
                logger.info("Using fallback get_current_user")
                return {"id": 1, "name": "Test User", "role": "operator"}
            
            def is_authenticated():
                """Fallback authentication checker"""
                logger.info("Using fallback is_authenticated (always True)")
                return True

try:
    # Try to import DEFAULT_SESSION_VARS from the session module
    from pages.utils.session import DEFAULT_SESSION_VARS
except ImportError:
    try:
        from utils.session import DEFAULT_SESSION_VARS
    except ImportError:
        try:
            from streamlit_app.utils.session import DEFAULT_SESSION_VARS
        except ImportError:
            # Fallback default session vars
            DEFAULT_SESSION_VARS = {
                "user": "user",
                "token": "auth_token",
                "authenticated": "authenticated",
            }

# Config import
try:
    # Docker környezetben ez a helyes import
    import app_config as config
    logger.info("Successfully imported app_config")
except ImportError:
    try:
        # Streamlit app relatív útvonal
        from streamlit_app import app_config as config
        logger.info("Successfully imported app_config from streamlit_app")
    except ImportError:
        # Fallback config
        logger.warning("Could not import app_config, using fallback")
        class FallbackConfig:
            API_BASE_URL = "http://api:8000"  # Docker container service name
            APP_NAME = "POM APP"
            DEBUG = True
            AUTH_COOKIE_NAME = "auth_token"
            SESSION_TIMEOUT = 1800  # 30 minutes
            SESSION_VARS = DEFAULT_SESSION_VARS
            
        config = FallbackConfig()
        logger.info("Using fallback config")

# Export everything
__all__ = [
    'get_auth_token',
    'clear_session',
    'set_user_session',
    'get_current_user',
    'is_authenticated',
    'DEFAULT_SESSION_VARS',
    'config'
] 