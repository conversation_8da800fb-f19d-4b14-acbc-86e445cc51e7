# Begin File: users.py
# File Location: streamlit_app/api/users.py

"""
Felhasználókezelési API hívások.

Ez a modul tartalmazza az összes felhasználókkal kapcsolatos API hívást,
amelyeket a Streamlit frontend használ a backend API-val való kommunikációhoz.
"""
import requests
import re
import os
import logging
import streamlit as st

# Konfigurálás
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Rugalmas import kezelés
try:
    # Központi import modul használata
    from api.imports import (
        get_auth_token, 
        clear_session, 
        set_user_session,
        config
    )
    logger.info("Successfully imported from common API imports in users.py")
except ImportError:
    try:
        # Docker környezetben ez a helyes import
        from pages.utils.session import get_auth_token, clear_session, set_user_session
        import app_config as config
        logger.info("Successfully imported session utils from Docker path in users.py")
    except ImportError:
        try:
            # Közvetlen import (fejlesztői környezetben)
            from utils.session import get_auth_token, clear_session, set_user_session
            import app_config as config
            logger.info("Successfully imported session utils directly in users.py")
        except ImportError:
            try:
                # Teljes útvonal (streamlit_app-ból)
                from streamlit_app.utils.session import get_auth_token, clear_session, set_user_session
                from streamlit_app import app_config as config
                logger.info("Successfully imported session utils from streamlit_app path in users.py")
            except ImportError:
                # Fallback funkciók
                logger.warning("Could not import session utils in users.py, using fallbacks")
                
                def get_auth_token():
                    """Fallback auth token getter"""
                    env_token = os.environ.get("API_AUTH_TOKEN")
                    logger.info(f"Using fallback auth token from env: {bool(env_token)}")
                    return env_token
                
                def clear_session():
                    """Fallback session clearer"""
                    logger.info("Using fallback clear_session (no-op)")
                    pass
                
                def set_user_session(user_data, token):
                    """Fallback session setter"""
                    logger.info(f"Using fallback set_user_session: {user_data.get('name') if user_data else None}")
                    pass
                    
                # Fallback config
                class FallbackConfig:
                    API_BASE_URL = "http://api:8000"  # Docker container service name
                    APP_NAME = "POM APP"
                    DEBUG = True
                config = FallbackConfig()
                logger.info("Using fallback config in users.py")

# Format validation error import (with fallback)
try:
    from utils.formatting import format_validation_error
except ImportError:
    try:
        from pages.utils.formatting import format_validation_error
    except ImportError:
        try:
            from streamlit_app.utils.formatting import format_validation_error
        except ImportError:
            # Fallback implementation
            def format_validation_error(error_data):
                """Fallback validation error formatter"""
                if isinstance(error_data, dict) and "detail" in error_data:
                    return error_data["detail"]
                return str(error_data)
            logger.warning("Using fallback format_validation_error in users.py")

# ========================================================
# Segédfüggvények
# ========================================================

def _make_api_request(method, endpoint, data=None, json=None, params=None):
    """
    Általános API kérés küldése a felhasználókezelési végpontokra.
    
    Args:
        method (str): HTTP metódus (GET, POST, PUT, DELETE)
        endpoint (str): API végpont relatív elérési útja (pl. "/users/me")
        data (dict, optional): Form adatok
        json (dict, optional): JSON adatok
        params (dict, optional): Query paraméterek
        
    Returns:
        tuple: (bool, Any) - Sikeres kérés (True/False) és válasz vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        # Teljes URL összeállítása
        url = f"{config.API_BASE_URL}{endpoint}"
        
        # Headers összeállítása
        headers = {"Authorization": f"Bearer {token}"}
        
        # Kérés küldése
        if method == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method == "POST":
            response = requests.post(url, headers=headers, data=data, json=json)
        elif method == "PUT":
            response = requests.put(url, headers=headers, data=data, json=json)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            return False, f"Nem támogatott HTTP metódus: {method}"
        
        # Válasz feldolgozása
        if response.ok:
            # Üres válasz esetén
            if not response.content:
                return True, None
                
            # JSON válasz esetén
            try:
                return True, response.json()
            except ValueError:
                return True, response.text
        
        # Hiba esetén
        try:
            error_data = response.json()
            error_message = error_data.get("detail", f"Hiba: {response.status_code}")
        except:
            error_message = response.text or f"Hiba: {response.status_code}"
        
        # Specifikus hibaüzenetek
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        if response.status_code == 404:
            return False, "Az erőforrás nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Váratlan hiba: {str(e)}"

# ========================================================
# Bejelentkezett felhasználó műveletek
# ========================================================

def get_current_user():
    """
    Aktuális bejelentkezett felhasználó adatainak lekérdezése.
    
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és felhasználó adatai vagy hibaüzenet
    """
    return _make_api_request("GET", "/users/me")

def update_current_user(user_data):
    """
    Aktuális felhasználó adatainak módosítása.
    
    Args:
        user_data (dict): Módosítandó felhasználói adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített adatok vagy hibaüzenet
    """
    return _make_api_request("PUT", "/users/me", json=user_data)

def change_password(password_data):
    """
    Aktuális felhasználó jelszavának módosítása.
    
    Args:
        password_data (dict): Jelszó adatok (current_password, new_password)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és válasz vagy hibaüzenet
    """
    return _make_api_request("POST", "/users/me/change-password", json=password_data)

def get_user_default_settings():
    """
    Felhasználói alapértelmezett beállítások lekérdezése
    
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés és beállítások
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    import datetime
    import json
    import traceback
    
    log_data = {
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "function": "get_user_default_settings",
        "token_exists": bool(token),
        "api_url": f"{config.API_BASE_URL}/users/me/settings"
    }
    
    print("========== API DEBUG KEZDETE ==========")
    print(f"Idő: {log_data['timestamp']}")
    print(f"Függvény: {log_data['function']}")
    print(f"API URL: {log_data['api_url']}")
    print(f"Hitelesítési token létezik: {log_data['token_exists']}")
    
    try:
        print("API hívás kezdete...")
        response = requests.get(
            f"{config.API_BASE_URL}/users/me/settings",
            headers=headers
        )
        
        log_data["status_code"] = response.status_code
        log_data["response_headers"] = dict(response.headers)
        
        print(f"Státusz kód: {response.status_code}")
        print(f"Válasz fejlécek: {json.dumps(dict(response.headers), indent=2)}")
        
        if response.status_code == 200:
            # Részletes naplózás
            settings_data = response.json()
            log_data["response_data"] = settings_data
            
            print("Nyers válasz JSON:")
            print(json.dumps(settings_data, indent=2))
            
            # NULL értékek kiemelése
            null_values = {}
            for key, value in settings_data.items():
                if value == "NULL":
                    null_values[key] = "NULL"
            
            if null_values:
                print(f"'NULL' stringként kapott értékek: {json.dumps(null_values, indent=2)}")
            else:
                print("Nincsenek 'NULL' stringként kapott értékek")
                
            # Ellenőrzés, hogy a beolvasott adatok megfelelőek-e
            print("Ellenőrzés az alapvető értékekre:")
            print(f"- default_product_type_id értéke: {settings_data.get('default_product_type_id')}")
            print(f"- default_quality_grade_id értéke: {settings_data.get('default_quality_grade_id')}")
            print(f"- default_category_id értéke: {settings_data.get('default_category_id')}")
            
            # Visszaadjuk a teljes válaszban kapott adatokat, módosítás nélkül
            return True, settings_data
        
        error_data = response.json()
        error_message = error_data.get("detail", "Felhasználói beállítások lekérdezési hiba")
        log_data["error"] = error_message
        
        print(f"Hiba: {error_message}")
        return False, error_message
    
    except requests.RequestException as e:
        error_message = f"Hálózati hiba: {str(e)}"
        log_data["error"] = error_message
        log_data["exception"] = traceback.format_exc()
        
        print(error_message)
        print(traceback.format_exc())
        return False, error_message
    
    except Exception as e:
        error_message = f"Hiba: {str(e)}"
        log_data["error"] = error_message
        log_data["exception"] = traceback.format_exc()
        
        print(error_message)
        print(traceback.format_exc())
        return False, error_message
    
    finally:
        print("========== API DEBUG VÉGE ==========")
        
        # Fájlba is kiírhatjuk a naplót
        try:
            import os
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            log_file = os.path.join(log_dir, "api_debug.log")
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_data, indent=2, ensure_ascii=False))
                f.write("\n---\n")
        except Exception as log_error:
            print(f"Nem sikerült a napló fájlba írása: {str(log_error)}")

def update_user_default_settings(settings_data):
    """
    Aktuális felhasználó alapbeállításainak módosítása.
    
    Args:
        settings_data (dict): Módosítandó beállítások
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített beállítások vagy hibaüzenet
    """
    import datetime
    import json
    import traceback
    
    log_data = {
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "function": "update_user_default_settings",
        "input_data": settings_data,
        "api_url": f"{config.API_BASE_URL}/users/me/settings"
    }
    
    print("\n========== API BEÁLLÍTÁS MENTÉS DEBUG KEZDETE ==========")
    print(f"Idő: {log_data['timestamp']}")
    print(f"Függvény: {log_data['function']}")
    print(f"API URL: {log_data['api_url']}")
    
    print("Beküldendő adatok:")
    print(json.dumps(settings_data, indent=2, ensure_ascii=False))
    
    # Ellenőrizzük a NULL értékeket
    null_values = {}
    for key, value in settings_data.items():
        if value == "NULL":
            null_values[key] = "NULL"
    
    if null_values:
        print(f"FIGYELMEZTETÉS: 'NULL' stringként küldött értékek: {json.dumps(null_values, indent=2)}")
    
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
        print(f"Auth token: {token[:10]}...{token[-10:]} (köztes karakterek elrejtve)")
    else:
        print("HIBA: Nincs auth token!")
    
    try:
        print(f"PUT kérés indítása az API-hoz...")
        response = requests.put(
            f"{config.API_BASE_URL}/users/me/settings",
            json=settings_data,
            headers=headers
        )
        
        log_data["status_code"] = response.status_code
        log_data["response_headers"] = dict(response.headers)
        
        print(f"Válasz státusz kód: {response.status_code}")
        print(f"Válasz fejlécek: {json.dumps(dict(response.headers), indent=2)}")
        
        try:
            response_data = response.json()
            log_data["response_data"] = response_data
            print("Válasz JSON adatok:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
        except:
            print(f"A válasz nem JSON formátumú. Nyers válasz: {response.text}")
            log_data["raw_response"] = response.text
        
        if response.status_code == 200:
            return True, response.json()
        
        # Hiba esetén
        error_data = response.json() if response.headers.get('content-type') == 'application/json' else {'detail': response.text}
        error_message = error_data.get("detail", "Felhasználói beállítások módosítási hiba")
        log_data["error"] = error_message
        
        print(f"Hiba: {error_message}")
        return False, error_message
    
    except requests.RequestException as e:
        error_message = f"Hálózati hiba: {str(e)}"
        log_data["error"] = error_message
        log_data["exception"] = traceback.format_exc()
        
        print(error_message)
        print(traceback.format_exc())
        return False, error_message
    
    except Exception as e:
        error_message = f"Hiba: {str(e)}"
        log_data["error"] = error_message
        log_data["exception"] = traceback.format_exc()
        
        print(error_message)
        print(traceback.format_exc())
        return False, error_message
    
    finally:
        print("========== API BEÁLLÍTÁS MENTÉS DEBUG VÉGE ==========\n")
        
        # Fájlba is kiírhatjuk a naplót
        try:
            import os
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            log_file = os.path.join(log_dir, "api_debug.log")
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_data, indent=2, ensure_ascii=False))
                f.write("\n---\n")
        except Exception as log_error:
            print(f"Nem sikerült a napló fájlba írása: {str(log_error)}")

# ========================================================
# Felhasználók lekérdezése és kezelése (admin műveletek)
# ========================================================

def get_users(params=None):
    """
    Felhasználók listájának lekérdezése (admin/ügyintéző számára).
    
    Args:
        params (dict, optional): Keresési paraméterek (szűrés, lapozás, stb.)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és felhasználók listája vagy hibaüzenet
    """
    return _make_api_request("GET", "/users", params=params)

def get_user(user_id):
    """
    Adott felhasználó adatainak lekérdezése azonosító alapján.
    
    Args:
        user_id (int): Felhasználó azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és felhasználó adatai vagy hibaüzenet
    """
    return _make_api_request("GET", f"/users/{user_id}")

def update_user(user_id, user_data):
    """
    Felhasználó adatainak módosítása azonosító alapján.
    
    Args:
        user_id (int): Felhasználó azonosítója
        user_data (dict): Módosítandó adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített adatok vagy hibaüzenet
    """
    return _make_api_request("PUT", f"/users/{user_id}", json=user_data)

def set_user_role(user_id, role):
    """
    Felhasználó szerepkörének módosítása (admin funkció).
    
    Args:
        user_id (int): Felhasználó azonosítója
        role (str): Új szerepkör (termelő, ügyintéző, admin)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített adatok vagy hibaüzenet
    """
    return _make_api_request("PUT", f"/users/{user_id}/role", json={"role": role})

def activate_user(user_id):
    """
    Felhasználó aktiválása (admin funkció).
    
    Args:
        user_id (int): Felhasználó azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres művelet (True/False) és válasz vagy hibaüzenet
    """
    return _make_api_request("PUT", f"/users/{user_id}/activate")

def deactivate_user(user_id):
    """
    Felhasználó deaktiválása (admin funkció).
    
    Args:
        user_id (int): Felhasználó azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres művelet (True/False) és válasz vagy hibaüzenet
    """
    return _make_api_request("PUT", f"/users/{user_id}/deactivate")

def delete_user(user_id):
    """
    Felhasználó törlése (admin funkció).
    
    Args:
        user_id (int): Felhasználó azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és visszaigazolás vagy hibaüzenet
    """
    success, result = _make_api_request("DELETE", f"/users/{user_id}")
    if success and not result:
        return True, "Felhasználó sikeresen törölve"
    return success, result

def get_all_farmers():
    """
    Az összes aktív termelő felhasználó lekérdezése.
    
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és termelők listája vagy hibaüzenet
    """
    params = {
        "role": "termelő",
        "is_active": True
    }
    success, response = _make_api_request("GET", "/users", params=params)
    
    if success and isinstance(response, list):
        # Rendezés contact_name szerint
        response.sort(key=lambda x: x.get("contact_name", ""))
        return True, response
    return success, response
