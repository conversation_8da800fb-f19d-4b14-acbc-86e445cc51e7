# Ajánlat-kezelés API hívások - Minim<PERSON><PERSON> jav<PERSON> a get_offer függvényhez
"""
Ez a fájl tartalmazza a get_offer függvény javított verziój<PERSON>t, amely helyesen kezeli 
az ID típusokat és robusztusabb hibakezelést tartalmaz.
"""
import logging
import requests
import app_config as config
from utils.session import get_auth_token

# Logger beállítása
logger = logging.getLogger(__name__)

def get_offer(offer_id):
    """
    Egy ajánlat lekérdezése API hívás - JAVÍTOTT VERZIÓ
    
    Args:
        offer_id (int/str): A<PERSON><PERSON><PERSON> a<PERSON>ító<PERSON> (rugalmas típuskezeléssel)
        
    Returns:
        tuple: (bool, dict/str) - <PERSON><PERSON><PERSON> lek<PERSON> (True/False) és ajánlat adatai vagy hibaüzenet
    """
    try:
        # Egyszerűsített ID konverzió - csak az alapvető típusellenőrzéssel
        original_id = offer_id  # Eredeti érték megőrzése a logoláshoz
        
        # ID konverzió rugalmas kezeléssel
        if isinstance(offer_id, str):
            try:
                offer_id = int(offer_id.strip())
            except (ValueError, TypeError):
                # Hagyni az eredeti formában, ha nem konvertálható
                # és hagyni az API-t, hogy kezelje a hibát
                pass
        
        # Debug információk naplózása
        logger.info(f"GET_OFFER API CALL: Original ID: {original_id}, Converted ID: {offer_id}, Type: {type(offer_id)}")
        
        # API kérés
        headers = get_auth_headers()
        response = requests.get(
            f"{config.API_BASE_URL}/offers/{offer_id}",
            headers=headers,
            timeout=10  # 10 másodperces timeout
        )
        
        logger.info(f"API Response Status: {response.status_code}")
        
        # Sikeres válasz kezelése
        if response.status_code == 200:
            try:
                offer_data = response.json()
                
                # Ellenőrizzük a válasz formátumát
                if not isinstance(offer_data, dict):
                    logger.error(f"Invalid response format: {type(offer_data)}")
                    return False, f"Érvénytelen API válasz formátum: {type(offer_data)}"
                
                # Ellenőrizzük, hogy az ID létezik-e a válaszban
                if "id" not in offer_data:
                    logger.error(f"Missing ID in API response for offer {offer_id}")
                    return False, "Hiányzó ID az API válaszból"
                
                # Kiegészítjük a display nevekkel
                user = offer_data.get("user", {})
                offer_data["user_name"] = user.get("company_name", "") if user else ""
                
                product_type = offer_data.get("product_type", {})
                offer_data["product_name"] = product_type.get("name", "") if product_type else ""
                
                # Státusz fordítási map
                status_mapping = {
                    "CREATED": "Létrehozva",
                    "CONFIRMED_BY_COMPANY": "Megerősítve",
                    "ACCEPTED_BY_USER": "Elfogadva",
                    "REJECTED_BY_USER": "Elutasítva",
                    "FINALIZED": "Teljesítve",
                    "MODIFIED": "Módosítva"
                }
                
                # Státusz megjelenítési név hozzáadása
                if "status" in offer_data:
                    current_status = offer_data["status"]
                    offer_data["status_display"] = status_mapping.get(current_status, current_status)
                
                logger.info(f"Successfully loaded offer {offer_id}")
                return True, offer_data
                
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}")
                return False, f"Hiba a válasz feldolgozásakor: {str(e)}"
        
        # Hibaüzenet feldolgozása
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Ajánlat lekérdezési hiba")
        except Exception as e:
            logger.error(f"Error parsing error response: {str(e)}")
            error_message = f"Ajánlat lekérdezési hiba (HTTP {response.status_code})"
        
        # Specifikus hibaüzenetek
        if response.status_code == 404:
            return False, f"Nem található ajánlat ezzel az azonosítóval: {offer_id}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
        
    except requests.Timeout:
        logger.error(f"Timeout while fetching offer {offer_id}")
        return False, "Az API kérés időtúllépés miatt megszakadt"
    except requests.ConnectionError:
        logger.error(f"Connection error while fetching offer {offer_id}")
        return False, "Nem sikerült kapcsolódni az API szerverhez"
    except Exception as e:
        logger.error(f"Unexpected error in get_offer: {str(e)}")
        return False, f"Hiba az ajánlat lekérdezésekor: {str(e)}"

def get_auth_headers():
    """
    Autentikációs fejlécek összeállítása.
    
    Returns:
        dict: Az autentikációs fejléceket tartalmazó szótár.
    """
    token = get_auth_token()
    if not token:
        return {}
    return {"Authorization": f"Bearer {token}"}