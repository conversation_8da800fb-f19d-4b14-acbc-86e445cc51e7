# Termék-kezelés API hívások
"""
Termék-kezelési API hívások.
"""
import requests
import logging

# Közös importok az API-khoz
try:
    # Rugalmas import kezelés a központi import modulból
    from api.imports import get_auth_token, config
    logging.info("Successfully imported from common API imports")
except ImportError:
    try:
        # Docker környezetben lehet, hogy ez működik
        from pages.api.imports import get_auth_token, config
        logging.info("Successfully imported from Docker API imports path")
    except ImportError:
        # Ha nincs közös import modul, fallback
        logging.warning("Using fallback imports in products.py")
        try:
            # Docker környezetben ez a helyes import
            from pages.utils.session import get_auth_token
            import app_config as config
        except ImportError:
            # Közvetlen import (fejlesztői környezetben)
            try:
                from utils.session import get_auth_token
                import app_config as config
            except ImportError:
                # Fallback
                def get_auth_token():
                    """Fallback auth token getter"""
                    return None
                
                class FallbackConfig:
                    API_BASE_URL = "http://api:8000"
                config = FallbackConfig()

def get_product_categories(params=None):
    """
    Termékkategóriák lekérdezése API hívás
    
    Args:
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és kategóriák listája vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(
            f"{config.API_BASE_URL}/products/categories",
            headers=headers,
            params=params or {}
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategóriák lekérdezési hiba")
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_product_types(category_id=None, params=None):
    """
    Terméktípusok lekérdezése API hívás
    
    Args:
        category_id (int): Kategória azonosítója (opcionális)
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és típusok listája vagy hibaüzenet
    """
    print(f"==== GET_PRODUCT_TYPES API CALL ====")
    print(f"Category ID: {category_id}")
    print(f"Params: {params}")
    print(f"API Base URL: {config.API_BASE_URL}")
    
    token = get_auth_token()
    print(f"Token: {token[:10]}..." if token else "Token: None")
    
    if not token:
        print("No token available")
        return False, "Nincs bejelentkezve"
    
    headers = {"Authorization": f"Bearer {token}"}
    print(f"Headers: {headers}")
    
    query_params = params or {}
    if category_id:
        query_params["category_id"] = category_id
    
    print(f"Query params: {query_params}")
    
    try:
        # Construct the full URL
        url = f"{config.API_BASE_URL}/products/types"
        print(f"Full URL: {url}")
        
        response = requests.get(
            url,
            headers=headers,
            params=query_params
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response data count: {len(data)}")
            return True, data
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípusok lekérdezési hiba")
        print(f"Error: {error_message}")
        return False, error_message
    
    except requests.RequestException as e:
        print(f"Network error: {str(e)}")
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return False, f"Hiba: {str(e)}"
    finally:
        print("============================")

def get_quality_grades(product_type_id, params=None):
    """
    Minőségi besorolások lekérdezése API hívás
    
    Args:
        product_type_id (int): Terméktípus azonosítója
        params (dict): Keresési paraméterek (opcionális)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és besorolások listája vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    query_params = params or {}
    query_params["product_type_id"] = product_type_id
    
    try:
        # FONTOS: ide a helyes végpont
        response = requests.get(
            f"{config.API_BASE_URL}/products/grades", 
            headers=headers,
            params=query_params
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolások lekérdezési hiba")
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_product_category(category_data):
    """
    Új termékkategória létrehozása API hívás (csak admin számára)
    
    Args:
        category_data (dict): Kategória adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott kategória vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/categories",
            headers=headers,
            json=category_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategória létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_product_type(type_data):
    """
    Új terméktípus létrehozása API hívás (csak admin számára)
    
    Args:
        type_data (dict): Típus adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott típus vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/types",
            headers=headers,
            json=type_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_quality_grade(grade_data):
    """
    Új minőségi besorolás létrehozása API hívás (csak admin számára)
    
    Args:
        grade_data (dict): Besorolás adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott besorolás vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/products/grades",
            headers=headers,
            json=grade_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_product_category(category_id, category_data):
    """
    Termékkategória frissítése API hívás (csak admin számára)
    
    Args:
        category_id (int): Kategória azonosítója
        category_data (dict): Frissített kategória adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres frissítés (True/False) és frissített kategória vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/categories/{category_id}",
            headers=headers,
            json=category_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Termékkategória frissítési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott kategória nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_product_type(type_id, type_data):
    """
    Terméktípus frissítése API hívás (csak admin számára)
    
    Args:
        type_id (int): Típus azonosítója
        type_data (dict): Frissített típus adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres frissítés (True/False) és frissített típus vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers,
            json=type_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus frissítési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott típus nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_quality_grade(grade_id, grade_data):
    """
    Minőségi besorolás frissítése API hívás (csak admin számára)
    
    Args:
        grade_id (int): Besorolás azonosítója
        grade_data (dict): Frissített besorolás adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres frissítés (True/False) és frissített besorolás vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/products/grades/{grade_id}",
            headers=headers,
            json=grade_data
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás frissítési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott besorolás nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_product_category(category_id):
    """
    Termékkategória törlése API hívás (csak admin számára)
    
    Args:
        category_id (int): Kategória azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és visszaigazoló vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/categories/{category_id}",
            headers=headers
        )
        
        if response.status_code == 204:
            return True, "A termékkategória sikeresen törölve"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Termékkategória törlési hiba")
        except:
            error_message = f"Törlési hiba: {response.status_code}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott kategória nem található"
        
        if response.status_code == 400:
            return False, "A kategória nem törölhető, mert vannak hozzá kapcsolódó elemek"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_product_type(type_id):
    """
    Terméktípus törlése API hívás (csak admin számára)
    
    Args:
        type_id (int): Típus azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és visszaigazoló vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers
        )
        
        if response.status_code == 204:
            return True, "A terméktípus sikeresen törölve"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Terméktípus törlési hiba")
        except:
            error_message = f"Törlési hiba: {response.status_code}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott típus nem található"
        
        if response.status_code == 400:
            return False, "A típus nem törölhető, mert vannak hozzá kapcsolódó elemek"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def delete_quality_grade(grade_id):
    """
    Minőségi besorolás törlése API hívás (csak admin számára)
    
    Args:
        grade_id (int): Besorolás azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és visszaigazoló vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/grades/{grade_id}",
            headers=headers
        )
        
        if response.status_code == 204:
            return True, "A minőségi besorolás sikeresen törölve"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Minőségi besorolás törlési hiba")
        except:
            error_message = f"Törlési hiba: {response.status_code}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott besorolás nem található"
        
        if response.status_code == 400:
            return False, "A besorolás nem törölhető, mert vannak hozzá kapcsolódó elemek"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_product_type(type_id):
    """
    Egy terméktípus lekérdezése API hívás
    
    Args:
        type_id (int): Terméktípus azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és típus adatai vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(
            f"{config.API_BASE_URL}/products/types/{type_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Terméktípus lekérdezési hiba")
        
        if response.status_code == 404:
            return False, "A megadott típus nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_quality_grade(quality_grade_id):
    """
    Egy minőségi besorolás lekérdezése API hívás
    
    Args:
        quality_grade_id (int): Minőségi besorolás azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és besorolás adatai vagy hibaüzenet
    """
    token = get_auth_token()
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(
            f"{config.API_BASE_URL}/products/grades/{quality_grade_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Minőségi besorolás lekérdezési hiba")
        
        if response.status_code == 404:
            return False, "A megadott besorolás nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}" 