def get_stats(report_type, from_date, to_date):
    """
    Get statistics data from the API
    """
    print(f"==== REPORT API REQUEST ====")
    print(f"Report type: {report_type}")
    print(f"Request params: {{'from_date': '{from_date}', 'to_date': '{to_date}'}}")
    print(f"===========================")
    
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    endpoint = "/stats"
    
    # Format parameters to match backend expectations
    api_params = {
        'date_from': from_date,
        'date_to': to_date
    }
    
    print(f"Transformed API params: {api_params}")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(
            f"{config.API_BASE_URL}{endpoint}",
            headers=headers,
            params=api_params
        )
        
        # Log response info
        print(f"API response status: {response.status_code}")
        
        try:
            stats = response.json()
        except:
            return False, "Hibás válasz a szervertől"
        
        print(f"==== REPORT API RESPONSE ====")
        print(f"Success: {response.status_code == 200}")
        print(f"Stats type: {type(stats)}")
        
        if isinstance(stats, dict):
            print(f"Stats keys: {stats.keys()}")
            print(f"--- Top-level data ---")
            print(f"total_offers: {stats.get('total_offers')}")
            print(f"accepted_offers: {stats.get('accepted_offers')}")
            print(f"total_quantity: {stats.get('total_quantity')}")
            print(f"total_value: {stats.get('total_value')}")
            
            # Check and log detailed data sections
            if 'producer_summary' in stats:
                producer_summary = stats.get('producer_summary', [])
                print(f"--- Producer data ---")
                print(f"producer_summary count: {len(producer_summary)}")
                if len(producer_summary) > 0:
                    print(f"First producer: {producer_summary[0]}")
            else:
                print("--- Producer data missing ---")
                
                # Try to see if data is available in alternate format
                if 'producer_offers' in stats:
                    producer_offers = stats.get('producer_offers', {})
                    print(f"producer_offers count: {len(producer_offers)}")
                    if producer_offers:
                        first_key = next(iter(producer_offers))
                        print(f"First producer_offer: {producer_offers[first_key]}")
            
            if 'product_summary' in stats:
                product_summary = stats.get('product_summary', [])
                print(f"--- Product data ---")
                print(f"product_summary count: {len(product_summary)}")
                if len(product_summary) > 0:
                    print(f"First product: {product_summary[0]}")
            else:
                print("--- Product data missing ---")
                
                # Try to see if data is available in alternate format
                if 'product_offers' in stats:
                    product_offers = stats.get('product_offers', {})
                    print(f"product_offers count: {len(product_offers)}")
                    if product_offers:
                        first_key = next(iter(product_offers))
                        print(f"First product_offer: {product_offers[first_key]}")
        else:
            print(f"Unexpected stats type: {type(stats)}")
        
        print(f"============================")
        
        if response.status_code == 200:
            return True, stats
        elif response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        else:
            return False, f"Hiba: {response.status_code}"
    
    except Exception as e:
        print(f"==== REPORT API ERROR ====")
        print(f"Error: {str(e)}")
        print(f"=========================")
        return False, f"Hiba: {str(e)}" 