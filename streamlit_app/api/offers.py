# Ajánlat-kezelés API hívások
"""
Ajánlat-kezelési API hívások.
"""
import requests
import streamlit as st
import datetime
from typing import List, Union
import math
import logging

# Rugalmas import a session utils-hoz
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import get_auth_token
    logging.info("Successfully imported session utils from Docker path")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import get_auth_token
        logging.info("Successfully imported session utils directly")
    except ImportError:
        try:
            # Teljes útvonal (streamlit_app-ból)
            from streamlit_app.utils.session import get_auth_token
            logging.info("Successfully imported session utils from streamlit_app path")
        except ImportError:
            # Fallback funkció
            logging.warning("Could not import session utils, using fallback")
            def get_auth_token():
                """Fallback auth token getter"""
                return None

# Rugalmas import a config-hoz
try:
    # Docker környezetben ez a helyes import
    import app_config as config
except ImportError:
    try:
        # Streamlit app relatív útvonal
        from streamlit_app import app_config as config
    except ImportError:
        # Fallback config
        logging.warning("Could not import app_config, using fallback")
        class FallbackConfig:
            API_BASE_URL = "http://api:8000"  # Docker container service name
        config = FallbackConfig()

# Logger beállítása
logger = logging.getLogger(__name__)

def get_auth_headers():
    """
    Autentikációs fejlécek összeállítása.
    
    Returns:
        dict: Az autentikációs fejléceket tartalmazó szótár.
    """
    token = get_auth_token()
    if not token:
        return {}
    return {"Authorization": f"Bearer {token}"}

def get_offers(params: dict = None) -> tuple[bool, Union[List[dict], str]]:
    """
    Ajánlatok lekérése a backenddől.
    
    Args:
        params (dict, optional): Szűrési paraméterek. Defaults to None.
        
    Returns:
        tuple[bool, Union[List[dict], str]]: (Sikeres lekérés (True/False), ajánlatok listája vagy hibaüzenet)
    """
    try:
        api_params = {}
        if params:
            for key, value in params.items():
                # API dokumentációban a dátum paraméterek neve date_from és date_to
                # A frontend és backend közötti konzisztencia érdekében átnevezzük
                # a paramétereket a megfelelő névkonvencióra
                if key == 'from_date':
                    # from_date paraméter átnevezése date_from-ra
                    key = 'date_from'
                elif key == 'to_date':
                    # to_date paraméter átnevezése date_to-ra
                    key = 'date_to'
                
                # Dátum formátumok egységesítése
                if key in ['date_from', 'date_to']:
                    # Biztosítjuk, hogy a dátum string formátumban legyen
                    if isinstance(value, datetime.date):
                        api_params[key] = value.isoformat()
                    elif isinstance(value, str):
                        api_params[key] = value
                else:
                    api_params[key] = value
        
        # Alapértelmezett limit beállítása, ha nincs megadva
        if 'limit' not in api_params:
            api_params['limit'] = 1000
        
        # Debug információk a paraméterekről        
        logger.info(f"API call to /offers with params: {api_params}")
                    
        response = requests.get(
            f"{config.API_BASE_URL}/offers",
            params=api_params,
            headers=get_auth_headers()
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlatok lekérési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
        
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_offer(offer_id):
    """
    Egy ajánlat lekérdezése API hívás
    
    Args:
        offer_id (int vagy str): Ajánlat azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és ajánlat adatai vagy hibaüzenet
    """
    try:
        # Biztonságosan konvertáljuk az offer_id-t integer-ré
        original_offer_id = offer_id  # Megőrizzük az eredeti értéket
        
        # Rugalmasabb ID konverzió
        try:
            # Próbáljuk meg konvertálni számra, ha string és úgy néz ki mint egy szám
            if isinstance(offer_id, str):
                # Eltávolítjuk a felesleges whitespace-t
                offer_id = offer_id.strip()
                # Megpróbáljuk számmá alakítani - engedélyezünk minden számszerű értéket
                try:
                    offer_id = int(offer_id)
                except (ValueError, TypeError):
                    # Ha nem sikerül, megtartjuk az eredeti formát - talán az API kezelni tudja
                    pass
        except Exception as e:
            # Ha bármilyen hiba történik a konverzióval, naplózzuk, de folytassuk az eredeti értékkel
            print(f"ID konverzió figyelmeztetés: {str(e)}, eredeti érték használata: {original_offer_id}")
        
        # Informatívabb naplózás
        print(f"==== GET_OFFER API CALL ====")
        print(f"Original ID: {original_offer_id}, Processed ID: {offer_id}, Type: {type(offer_id)}")
        
        # API hívás - nincs változás a hívásban, csak engedjük, hogy az API kezelje a különböző ID formátumokat
        try:
            response = requests.get(
                f"{config.API_BASE_URL}/offers/{offer_id}",
                headers=get_auth_headers(),
                timeout=10  # 10 másodperces timeout
            )
            
            # Debug info
            print(f"Status code: {response.status_code}")
            print(f"Response headers: {response.headers}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"Response data keys: {list(response_data.keys())}")
                except Exception as e:
                    print(f"Error parsing response JSON: {str(e)}")
                    print(f"Raw response: {response.text[:200]}...")
            else:
                print(f"Error response: {response.text}")
            
            print("============================")
            
            if response.status_code == 200:
                offers = response.json()
                # Csak a get_offers-ben legyen "mindig lista" logika!
                if isinstance(offers, dict):
                    # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                    if "id" in offers:
                        # Egy ajánlat részlete, ne tegyük listába!
                        user = offers.get("user")
                        offers["user_name"] = user.get("company_name") if user else ""
                        product_type = offers.get("product_type")
                        offers["product_name"] = product_type.get("name") if product_type else ""
                        
                        # Állapot megfeleltetés magyar és angol státuszok között
                        # Ez segíti a következetes státuszkezelést a felületen
                        status_mapping = {
                            "CREATED": "Létrehozva",
                            "CONFIRMED_BY_COMPANY": "Megerősítve",
                            "ACCEPTED_BY_USER": "Elfogadva",
                            "REJECTED_BY_USER": "Elutasítva",
                            "FINALIZED": "Teljesítve",
                            "MODIFIED": "Módosítva"
                        }
                        
                        # Ha van benne status mező, biztosítsuk hogy konzisztens legyen
                        if "status" in offers:
                            current_status = offers["status"]
                            if current_status in status_mapping:
                                offers["status_display"] = status_mapping[current_status]
                            else:
                                offers["status_display"] = current_status
                        
                        return True, offers
                    else:
                        offers = [offers]
                if isinstance(offers, list):
                    for offer in offers:
                        user = offer.get("user")
                        offer["user_name"] = user.get("company_name") if user else ""
                        product_type = offer.get("product_type")
                        offer["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                # Ha nem lista vagy dict, akkor hiba
                return False, f"Érvénytelen ajánlat-adat formátum: {type(offers)}"
            
            # Hibaüzenet feldolgozása
            try:
                error_data = response.json()
                error_message = error_data.get("detail", "Ajánlat lekérdezési hiba")
            except Exception:
                error_message = f"Ajánlat lekérdezési hiba (status: {response.status_code}): {response.text}"
            
            if response.status_code == 404:
                return False, f"Nem található ajánlat ezzel az azonosítóval: {offer_id}"
            
            if response.status_code == 401:
                return False, "Nincs megfelelő jogosultsága az ajánlat megtekintéséhez"
            
            return False, error_message
            
        except requests.Timeout:
            return False, "Az API kérés időtúllépés miatt megszakadt"
        except requests.ConnectionError:
            return False, "Nem sikerült kapcsolódni az API szerverhez"
        
    except Exception as e:
        import traceback
        print(f"==== GET_OFFER EXCEPTION ====")
        print(f"Error type: {type(e).__name__}")
        print(f"Error message: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        print("============================")
        return False, f"Váratlan hiba az ajánlat lekérdezésekor: {str(e)}"

def create_offer(offer_data, on_behalf_of_user_id=None):
    """
    Új ajánlat létrehozása API hívás
    
    Args:
        offer_data (dict): Ajánlat adatai
        on_behalf_of_user_id (int): Felhasználó azonosítója, akinek a nevében létrehozzuk (ügyintéző esetén)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott ajánlat vagy hibaüzenet
    """
    try:
        # Ha ügyintéző másnak hoz létre ajánlatot
        if on_behalf_of_user_id:
            offer_data["user_id"] = on_behalf_of_user_id
            # Használjuk a megfelelő végpontot a felhasználók nevében történő ajánlat létrehozásához
            response = requests.post(
                f"{config.API_BASE_URL}/offers/for-user",
                headers=get_auth_headers(),
                json=offer_data
            )
        else:
            # Használjuk a normál végpontot az ajánlat létrehozásához
            response = requests.post(
                f"{config.API_BASE_URL}/offers",
                headers=get_auth_headers(),
                json=offer_data
            )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "quantity" in error_message.lower():
            return False, "Érvénytelen mennyiség"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def create_offer_for_user(offer_data, status=None):
    """
    Új ajánlat létrehozása más felhasználó nevében API hívás (admin/ügyintéző funkció)
    
    Args:
        offer_data (dict): Ajánlat adatai, beleértve a user_id mezőt
        status (str): Opcionális státusz, amit be szeretnénk állítani
        
    Returns:
        tuple: (bool, dict/str) - Sikeres létrehozás (True/False) és létrehozott ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        # Eredeti admin token elmentése későbbi használatra
        admin_token = token
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Ellenőrizzük, hogy a felhasználó azonosítója meg van-e adva
        if not offer_data.get("user_id"):
            return False, "A felhasználó azonosítója (user_id) kötelező"
        
        # Először létrehozzuk az ajánlatot
        response = requests.post(
            f"{config.API_BASE_URL}/offers/for-user",
            headers=headers,
            json=offer_data
        )
        
        if response.status_code == 201:
            offer_result = response.json()
            
            # Ha megadtunk státuszt és az nem CREATED, akkor külön beállítjuk
            if status and status != "CREATED":
                offer_id = offer_result["id"]
                producer_id = offer_data["user_id"]  # A termelő (tulajdonos) azonosítója
                
                # Státusz frissítése megfelelő végponton keresztül
                status_endpoint = None
                
                if status == "CONFIRMED_BY_COMPANY":
                    # Cég általi megerősítés - Ezt az admin tudja csinálni
                    status_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/confirm"
                    status_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    status_response = requests.post(status_endpoint, headers=headers, json=status_data)
                elif status == "ACCEPTED_BY_USER" or status == "REJECTED_BY_USER":
                    # Termelői művelet - először meg kell erősíteni admin-ként
                    confirm_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/confirm"
                    confirm_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    confirm_response = requests.post(confirm_endpoint, headers=headers, json=confirm_data)
                    
                    if confirm_response.status_code == 200:
                        # Először lekérjük a termelő email címét
                        producer_response = requests.get(f"{config.API_BASE_URL}/users/{producer_id}", headers=headers)
                        if producer_response.status_code != 200:
                            return False, f"Hiba a termelő adatainak lekérésekor: {producer_response.text}"
                        
                        producer_data = producer_response.json()
                        producer_email = producer_data.get("email")
                        
                        if not producer_email:
                            return False, "Nem sikerült lekérni a termelő email címét"
                        
                        # FONTOS: Aktiváljuk a felhasználót, ha még nincs aktiválva
                        if not producer_data.get("is_active", False):
                            activate_response = requests.put(
                                f"{config.API_BASE_URL}/users/{producer_id}/activate",
                                headers=headers
                            )
                            if activate_response.status_code != 200:
                                return False, f"Hiba a termelő aktiválásakor: {activate_response.text}"
                        
                        # Bejelentkezés a termelő nevében
                        login_data = {
                            "username": producer_email,
                            "password": "Password123"  # Az alapértelmezett jelszó, amit általában használunk
                        }
                        login_response = requests.post(
                            f"{config.API_BASE_URL}/auth/login", 
                            data=login_data,
                            headers={"Content-Type": "application/x-www-form-urlencoded"}
                        )
                        
                        if login_response.status_code != 200:
                            return False, f"Hiba a termelő nevében történő bejelentkezéskor: {login_response.text}"
                        
                        producer_token = login_response.json().get("access_token")
                        producer_headers = {"Authorization": f"Bearer {producer_token}"}
                        
                        # Termelői művelet végrehajtása
                        if status == "ACCEPTED_BY_USER":
                            status_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/accept"
                            status_response = requests.post(status_endpoint, headers=producer_headers)
                        else:  # REJECTED_BY_USER
                            status_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/reject"
                            status_response = requests.post(status_endpoint, headers=producer_headers)
                    else:
                        return False, f"Hiba az ajánlat megerősítése során: {confirm_response.text}"
                elif status == "FINALIZED":
                    # Véglegesítés - először meg kell erősíteni admin-ként, aztán a termelő elfogadja, aztán az admin véglegesít
                    confirm_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/confirm"
                    confirm_data = {"confirmed_quantity": offer_data["quantity_in_kg"], "confirmed_price": 100}
                    confirm_response = requests.post(confirm_endpoint, headers=headers, json=confirm_data)
                    
                    if confirm_response.status_code == 200:
                        # Először lekérjük a termelő email címét
                        producer_response = requests.get(f"{config.API_BASE_URL}/users/{producer_id}", headers=headers)
                        if producer_response.status_code != 200:
                            return False, f"Hiba a termelő adatainak lekérésekor: {producer_response.text}"
                        
                        producer_data = producer_response.json()
                        producer_email = producer_data.get("email")
                        
                        if not producer_email:
                            return False, "Nem sikerült lekérni a termelő email címét"
                        
                        # FONTOS: Aktiváljuk a felhasználót, ha még nincs aktiválva
                        if not producer_data.get("is_active", False):
                            activate_response = requests.put(
                                f"{config.API_BASE_URL}/users/{producer_id}/activate",
                                headers=headers
                            )
                            if activate_response.status_code != 200:
                                return False, f"Hiba a termelő aktiválásakor: {activate_response.text}"
                        
                        # Bejelentkezés a termelő nevében
                        login_data = {
                            "username": producer_email,
                            "password": "Password123"  # Az alapértelmezett jelszó, amit általában használunk
                        }
                        login_response = requests.post(
                            f"{config.API_BASE_URL}/auth/login", 
                            data=login_data,
                            headers={"Content-Type": "application/x-www-form-urlencoded"}
                        )
                        
                        if login_response.status_code != 200:
                            return False, f"Hiba a termelő nevében történő bejelentkezéskor: {login_response.text}"
                        
                        producer_token = login_response.json().get("access_token")
                        producer_headers = {"Authorization": f"Bearer {producer_token}"}
                        
                        # Termelő elfogadja
                        accept_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/accept"
                        accept_response = requests.post(accept_endpoint, headers=producer_headers)
                        
                        if accept_response.status_code != 200:
                            return False, f"Hiba az ajánlat elfogadása során: {accept_response.text}"
                        
                        # Admin véglegesíti az eredeti tokennel
                        status_endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/finalize"
                        status_response = requests.post(status_endpoint, headers={"Authorization": f"Bearer {admin_token}"})
                    else:
                        return False, f"Hiba az ajánlat megerősítése során: {confirm_response.text}"
                
                # Ellenőrizzük, hogy sikeres volt-e a státusz módosítás
                if status_endpoint and status_response.status_code != 200:
                    return False, f"Hiba az ajánlat {status} státuszra állítása során: {status_response.text}"
                
                # Frissített ajánlat adatok lekérése az admin tokennel
                get_offer_response = requests.get(
                    f"{config.API_BASE_URL}/offers/{offer_id}", 
                    headers={"Authorization": f"Bearer {admin_token}"}
                )
                if get_offer_response.status_code == 200:
                    return True, get_offer_response.json()
            
            return True, offer_result
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat létrehozási hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_offer(offer_id, offer_data):
    """
    Ajánlat módosítása API hívás (csak a létrehozó vagy ügyintéző módosíthatja)
    
    Args:
        offer_id (int): Ajánlat azonosítója
        offer_data (dict): Módosítandó adatok
        
    Returns:
        tuple: (bool, dict/str) - Sikeres módosítás (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{config.API_BASE_URL}/offers/{offer_id}",
            headers=headers,
            json=offer_data
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat módosítási hiba")
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem módosítható"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def confirm_offer(offer_id, confirmation_data):
    """
    Ajánlat visszaigazolása API hívás (ügyintéző számára)
    
    Args:
        offer_id (int): Ajánlat azonosítója
        confirmation_data (dict): Visszaigazolási adatok (confirmed_quantity, confirmed_price)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres visszaigazolás (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/offers/{offer_id}/confirm",
            headers=headers,
            json=confirmation_data
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat visszaigazolási hiba")
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem igazolható vissza"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def accept_offer(offer_id):
    """
    Ajánlat elfogadása API hívás (termelő számára)
    
    Args:
        offer_id (int): Ajánlat azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres elfogadás (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        # Try POST method instead of PUT since the error suggests Method Not Allowed
        response = requests.post(
            f"{config.API_BASE_URL}/offers/{offer_id}/accept",
            headers=headers
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        # Add debug info to see the actual error
        error_message = f"HTTP {response.status_code}: {response.text}"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", error_message)
        except:
            # If can't parse JSON, use the raw text
            pass
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 405:
            # Explicitly handle Method Not Allowed error with improved error message
            return False, "Method Not Allowed: Az API végpont nem támogatja ezt a metódust. Kérjük, ellenőrizze a dokumentációt."
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem fogadható el"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def reject_offer(offer_id):
    """
    Ajánlat elutasítása API hívás (termelő számára)
    
    Args:
        offer_id (int): Ajánlat azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres elutasítás (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        # Use POST instead of PUT for consistency with accept_offer
        response = requests.post(
            f"{config.API_BASE_URL}/offers/{offer_id}/reject",
            headers=headers
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        # Add debug info to see the actual error
        error_message = f"HTTP {response.status_code}: {response.text}"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", error_message)
        except:
            # If can't parse JSON, use the raw text
            pass
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 405:
            # Explicitly handle Method Not Allowed error
            return False, "Method Not Allowed: Az API végpont nem támogatja ezt a metódust. Kérjük, ellenőrizze a dokumentációt."
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem utasítható el"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def finalize_offer(offer_id):
    """
    Ajánlat véglegesítése API hívás (ügyintéző számára)
    
    Args:
        offer_id (int): Ajánlat azonosítója
        
    Returns:
        tuple: (bool, dict/str) - Sikeres véglegesítés (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{config.API_BASE_URL}/offers/{offer_id}/finalize",
            headers=headers
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat véglegesítési hiba")
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem véglegesíthető"
        
        if response.status_code == 405:
            return False, "Method Not Allowed: A szerver nem támogatja ezt a HTTP metódust ennél a végpontnál"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_offer_logs(offer_id):
    """
    Ajánlathoz tartozó státusztörténet (naplóbejegyzések) lekérése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        tuple: (sikeres, eredmény) formátumban, ahol az eredmény a naplóbejegyzések listája vagy hibaüzenet
    """
    try:
        # Autentikációs token lekérése
        token = get_auth_token()
        if not token:
            return False, "Nincs érvényes autentikációs token"
        
        # API hívás
        url = f"{config.API_BASE_URL}/offers/{offer_id}/logs"
        
        # Fejlécek beállítása
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # Kérés küldése
        logger.info(f"Fetching logs for offer {offer_id}")
        response = requests.get(url, headers=headers)
        
        # Válasz ellenőrzése
        if response.status_code == 200:
            return True, response.json()
        elif response.status_code == 404:
            # Ha nincsenek naplóbejegyzések, üres listát adunk vissza
            logger.info(f"No logs found for offer {offer_id}")
            return True, []
        else:
            error_msg = f"Hiba a státusztörténet lekérésekor: {response.status_code}"
            try:
                error_details = response.json()
                error_msg += f", {error_details.get('detail', '')}"
            except:
                error_msg += f", {response.text}"
            logger.error(error_msg)
            return False, error_msg
    
    except Exception as e:
        error_msg = f"Kivétel a státusztörténet lekérésekor: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def get_offer_attachments(offer_id):
    """
    Ajánlathoz tartozó csatolmányok lekérése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
        
    Returns:
        tuple: (sikeres, eredmény) formátumban, ahol az eredmény a csatolmányok listája vagy hibaüzenet
    """
    try:
        # Autentikációs token lekérése
        token = get_auth_token()
        if not token:
            return False, "Nincs érvényes autentikációs token"
        
        # API hívás
        url = f"{config.API_BASE_URL}/offers/{offer_id}/attachments"
        
        # Fejlécek beállítása
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # Kérés küldése
        logger.info(f"Fetching attachments for offer {offer_id}")
        response = requests.get(url, headers=headers)
        
        # Válasz ellenőrzése
        if response.status_code == 200:
            return True, response.json()
        elif response.status_code == 404:
            # Ha nincsenek csatolmányok, üres listát adunk vissza
            logger.info(f"No attachments found for offer {offer_id}")
            return True, []
        else:
            error_msg = f"Hiba a csatolmányok lekérésekor: {response.status_code}"
            try:
                error_details = response.json()
                error_msg += f", {error_details.get('detail', '')}"
            except:
                error_msg += f", {response.text}"
            logger.error(error_msg)
            return False, error_msg
    
    except Exception as e:
        error_msg = f"Kivétel a csatolmányok lekérésekor: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def get_calendar_events(params=None):
    """
    Naptári események (ajánlatok) lekérése a backendről.
    
    Args:
        params (dict, optional): Szűrési paraméterek (date_from/start_date, date_to/end_date, status)
    
    Returns:
        tuple: (success, data)
    """
    # Debug info
    print(f"==== GET_CALENDAR_EVENTS API CALL ====")
    print(f"Params: {params}")
    
    # Használjuk ugyanazt az API végpontot, amit a táblázatos nézet is használ
    # Ez a fix: calendar specifikus végpont helyett az általános offers végpontot használjuk
    url = f"{config.API_BASE_URL}/offers"
    headers = {"Authorization": f"Bearer {get_auth_token()}"}
    
    # Format parameters to match backend expectations
    api_params = {}
    if params:
        # Kezeljük a különböző paraméter neveket egységesen
        if 'date_from' in params:
            api_params['date_from'] = params['date_from']
        elif 'start_date' in params:
            api_params['date_from'] = params['start_date']
        elif 'from_date' in params:
            api_params['date_from'] = params['from_date']
            
        if 'date_to' in params:
            api_params['date_to'] = params['date_to']
        elif 'end_date' in params:
            api_params['date_to'] = params['end_date']
        elif 'to_date' in params:
            api_params['date_to'] = params['to_date']
            
        if 'status' in params:
            api_params['status'] = params['status']
    
    print(f"Transformed API params: {api_params}")
    
    try:
        # Ensure any date objects are converted to strings
        for key, value in api_params.items():
            if isinstance(value, (datetime.date, datetime.datetime)):
                api_params[key] = value.strftime("%Y-%m-%d")
        
        # Make the API request
        response = requests.get(url, headers=headers, params=api_params)
        
        print(f"Status code: {response.status_code}")
        print(f"Request URL: {response.request.url}")
        
        # Parse the response data
        try:
            response_data = response.json()
            print(f"Response data type: {type(response_data)}")
            if isinstance(response_data, dict):
                print(f"Response keys: {list(response_data.keys())}")
            elif isinstance(response_data, list):
                print(f"Response list length: {len(response_data)}")
                if response_data:
                    print(f"First item keys: {list(response_data[0].keys()) if isinstance(response_data[0], dict) else 'Not a dict'}")
        except Exception as e:
            print(f"Error parsing JSON: {str(e)}")
            print(f"Response content: {response.text[:200]}...")  # Print first 200 chars
            return False, f"Hiba a válasz feldolgozásakor: {str(e)}"
        
        # Check if the response data is valid
        if response.status_code == 200:
            # Handle both list and dictionary responses
            if isinstance(response_data, list):
                print(f"Data count: {len(response_data)}")
                if response_data and len(response_data) > 0:
                    print(f"First item keys: {list(response_data[0].keys())}")
                    print(f"First item delivery_date: {response_data[0].get('delivery_date')}")
                    print(f"First item status: {response_data[0].get('status')}")
                return True, response_data
            elif isinstance(response_data, dict):
                # Convert empty dict to empty list for consistency
                if not response_data:
                    print("Empty dictionary response, converting to empty list")
                    return True, []
                # If it's a dict with actual data, try to extract the data
                if "data" in response_data and isinstance(response_data["data"], list):
                    data_list = response_data["data"]
                    print(f"Extracted list from dict response, count: {len(data_list)}")
                    if data_list and len(data_list) > 0:
                        print(f"First item keys: {list(data_list[0].keys())}")
                    return True, data_list
                # Otherwise, convert the dict itself to a list item
                print("Converting dict response to a list with single item")
                return True, [response_data]
            else:
                print(f"Unexpected response data type: {type(response_data)}")
                return False, f"Váratlan válasz formátum: {type(response_data)}"
        elif response.status_code == 422:
            print(f"Validation error: {response_data}")
            error_detail = "Validációs hiba"
            if isinstance(response_data, dict) and "detail" in response_data:
                error_detail = response_data["detail"]
            return False, f"Érvénytelen lekérdezési paraméterek: {error_detail}"
        elif response.status_code == 401:
            return False, "Hozzáférés megtagadva. Jelentkezzen be újra."
        else:
            return False, f"Hiba a naptári események lekérésekor: {response.status_code}"
    except Exception as e:
        print(f"Exception in get_calendar_events: {str(e)}")
        print("====================================")
        return False, f"Hiba: {str(e)}"

def get_statistics(params=None):
    """
    Statisztikák lekérdezése API hívás
    
    Args:
        params (dict): Keresési paraméterek (date_from, date_to, product_type_id, stb.)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és statisztikai adatok vagy hibaüzenet
    """
    try:
        # Dátumok formázása
        if params:
            api_params = params.copy()
            for key in ['date_from', 'date_to']:
                if key in api_params:
                    value = api_params[key]
                    if isinstance(value, datetime.datetime):
                        api_params[key] = value.date().strftime("%Y-%m-%d")
                    elif isinstance(value, datetime.date):
                        api_params[key] = value.strftime("%Y-%m-%d")
                    elif isinstance(value, str):
                        # Ha a string tartalmaz időkomponenst, eltávolítjuk
                        if 'T' in value:
                            api_params[key] = value.split('T')[0]
        else:
            api_params = {}
        
        print(f"==== STATISTICS API CALL ====")
        print(f"API endpoint: {config.API_BASE_URL}/offers/statistics")
        print(f"Params: {api_params}")
        
        response = requests.get(
            f"{config.API_BASE_URL}/offers/statistics",
            headers=get_auth_headers(),
            params=api_params,
            timeout=10  # Timeout a válasz megvárására
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Content type: {response.headers.get('content-type', 'Nem meghatározott')}")
        
        if response.status_code == 200:
            try:
                # JSON válasz feldolgozása
                stats_data = response.json()
                print(f"Response data: {type(stats_data)}")
                if isinstance(stats_data, dict):
                    print(f"Response keys: {list(stats_data.keys())}")
                
                # Közvetlenül adjuk vissza a statisztikai adatokat
                return True, stats_data
            except ValueError as json_err:
                print(f"JSON parsing error: {str(json_err)}")
                print(f"Response content: {response.text[:200]}...")
                return False, f"Hálózati hiba: {str(json_err)}"
        
        # Hibaállapot kezelése
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Statisztikák lekérdezési hiba")
        except ValueError:
            error_message = f"Statisztikák lekérdezési hiba: HTTP {response.status_code}"
            if response.text:
                error_message += f" - {response.text[:100]}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A statisztikai végpont nem található"
        
        return False, error_message
    
    except requests.RequestException as e:
        print(f"Network error: {str(e)}")
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return False, f"Hiba: {str(e)}"
    """
    Statisztikák lekérdezése API hívás
    
    Args:
        params (dict): Keresési paraméterek (date_from, date_to, group_by, stb.)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és statisztikai adatok vagy hibaüzenet
    """
    try:
        # Dátumok formázása
        if params:
            api_params = params.copy()
            for key in ['date_from', 'date_to']:
                if key in api_params:
                    value = api_params[key]
                    if isinstance(value, datetime.datetime):
                        api_params[key] = value.date().strftime("%Y-%m-%d")
                    elif isinstance(value, datetime.date):
                        api_params[key] = value.strftime("%Y-%m-%d")
                    elif isinstance(value, str):
                        # Ha a string tartalmaz időkomponenst, eltávolítjuk
                        if 'T' in value:
                            api_params[key] = value.split('T')[0]
        else:
            api_params = {}

        response = requests.get(
            f"{config.API_BASE_URL}/offers/statistics",
            headers=get_auth_headers(),
            params=api_params
        )
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", "Statisztikák lekérdezési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def update_offer_status(offer_id, new_status, confirmation_data=None):
    """
    Ajánlat státuszának frissítése API hívás
    
    Args:
        offer_id (int): Ajánlat azonosítója
        new_status (str): Új státusz
        confirmation_data (dict, optional): Visszaigazolási adatok (confirmed_quantity, confirmed_price)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres frissítés (True/False) és frissített ajánlat vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Státusz alapján különböző végpontok és adatok
        if new_status == "CONFIRMED_BY_COMPANY":
            if not confirmation_data:
                return False, "Hiányzó visszaigazolási adatok (mennyiség és ár)"
            endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/confirm"
            response = requests.post(endpoint, headers=headers, json=confirmation_data)
        
        elif new_status == "ACCEPTED_BY_USER":
            endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/accept"
            response = requests.post(endpoint, headers=headers)
        
        elif new_status == "REJECTED_BY_USER":
            endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/reject"
            response = requests.post(endpoint, headers=headers)
        
        elif new_status == "FINALIZED":
            endpoint = f"{config.API_BASE_URL}/offers/{offer_id}/finalize"
            response = requests.post(endpoint, headers=headers)
        
        else:
            return False, f"Nem támogatott státusz: {new_status}"
        
        if response.status_code == 200:
            offers = response.json()
            # Csak a get_offers-ben legyen "mindig lista" logika!
            if isinstance(offers, dict):
                # Ellenőrizzük, hogy ez tényleg lista-e vagy egy ajánlat részlete!
                if "id" in offers:
                    # Egy ajánlat részlete, ne tegyük listába!
                    user = offers.get("user")
                    offers["user_name"] = user.get("company_name") if user else ""
                    product_type = offers.get("product_type")
                    offers["product_name"] = product_type.get("name") if product_type else ""
                    return True, offers
                else:
                    offers = [offers]
            if isinstance(offers, list):
                for offer in offers:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                return True, offers
            # Ha nem lista vagy dict, akkor hiba
            return False, "Érvénytelen ajánlat-adat!"
        
        error_data = response.json()
        error_message = error_data.get("detail", f"Hiba az ajánlat {new_status} státuszra állítása során")
        
        if response.status_code == 404:
            return False, "Nem található ajánlat ezzel az azonosítóval"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 400 and "status" in error_message.lower():
            return False, "Az ajánlat jelenlegi státuszában nem módosítható"
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def get_offers_paginated(page=1, page_size=20, params=None):
    """
    Ajánlatok lapozható lekérése a backenddől adattranszfer csökkentése érdekében.
    
    Args:
        page (int): Az aktuális oldal száma (1-től kezdődik).
        page_size (int): Oldalankénti elemszám.
        params (dict, optional): További szűrési paraméterek. Defaults to None.
        
    Returns:
        tuple[bool, dict]: (Sikeres lekérés, dict ami tartalmazza az ajánlatokat és a lapozási adatokat)
            A visszatérési érték sikeres esetben:
            {
                "items": [ajánlat_objektumok_listája],
                "total": összes_ajánlat_száma,
                "page": aktuális_oldal,
                "page_size": oldalankénti_elemszám,
                "pages": összes_oldalak_száma
            }
    """
    try:
        api_params = {"page": page, "page_size": page_size, "paginated": True}
        
        # Egyéb paraméterek hozzáadása
        if params:
            for key, value in params.items():
                if key in ['date_from', 'date_to']:
                    # Biztosítjuk, hogy a dátum string formátumban legyen
                    if isinstance(value, datetime.date):
                        api_params[key] = value.isoformat()
                    elif isinstance(value, str):
                        api_params[key] = value
                else:
                    api_params[key] = value
                    
        response = requests.get(
            f"{config.API_BASE_URL}/offers",  # Használja a standard /offers végpontot /offers/paginated helyett
            params=api_params,
            headers=get_auth_headers()
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            # Kezeljük azt az esetet is, ha csak egy egyszerű lista jön vissza és nem lapozott válasz
            if isinstance(response_data, list):
                # Átalakítjuk a választ a lapozott formátumra
                for offer in response_data:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                
                # A kliensnek megfelelő formátumra alakítjuk
                formatted_response = {
                    "items": response_data,
                    "total": len(response_data),
                    "page": page,
                    "page_size": page_size,
                    "pages": max(1, math.ceil(len(response_data) / page_size))
                }
                return True, formatted_response
            
            # Ellenőrizzük, hogy a válasz a várt formátumban van-e
            if not isinstance(response_data, dict):
                return False, "Érvénytelen válaszformátum"
                
            # Ajánlatokat gazdagítjuk a termelő és termék nevekkel
            if "items" in response_data and isinstance(response_data["items"], list):
                for offer in response_data["items"]:
                    user = offer.get("user")
                    offer["user_name"] = user.get("company_name") if user else ""
                    product_type = offer.get("product_type")
                    offer["product_name"] = product_type.get("name") if product_type else ""
                
            return True, response_data
        
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlatok lapozható lekérési hiba")
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        return False, error_message
        
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"


def get_offer_statistics(params=None):
    """
    Ajánlat statisztikák lekérése az API-n keresztül.
    
    Args:
        params (dict, optional): Szűrési paraméterek (product_type_id, category_id, quality_grade_id, date_from, date_to)
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a statisztikák vagy hibaüzenet
    """
    try:
        logger.info(f"Statisztikák lekérése paraméterekkel: {params}")
        
        # Build query parameters
        query_params = {}
        if params:
            for key, value in params.items():
                if value is not None:
                    query_params[key] = value
        
        # Debug URL
        full_url = f"{config.API_BASE_URL}/offers/statistics"
        logger.info(f"Statistics API URL: {full_url}")
        logger.info(f"Statistics API params: {query_params}")
        
        response = requests.get(
            full_url,
            params=query_params,
            headers=get_auth_headers(),
            timeout=30
        )
        
        logger.info(f"Statistics API response status: {response.status_code}")
        logger.info(f"Statistics API response text: {response.text[:200]}...")
        
        if response.status_code == 200:
            statistics = response.json()
            logger.info(f"Statistics API response: {statistics}")
            
            # NO UI OUTPUT IN API FUNCTIONS - this was causing nested expander errors
            # Log debug info instead
            logger.info(f"API Response Keys: {list(statistics.keys()) if isinstance(statistics, dict) else 'Not a dict'}")
            if isinstance(statistics, dict):
                # Log key statistics info
                if 'average_price' in statistics:
                    logger.info(f"Raw Average Price: {statistics['average_price']}")
                if 'total_offers' in statistics:
                    logger.info(f"Total Offers Found: {statistics['total_offers']}")
                if 'confirmed_offers_count' in statistics:
                    logger.info(f"Offers Used in Calculation: {statistics['confirmed_offers_count']}")
                if 'calculation_details' in statistics:
                    details = statistics['calculation_details']
                    logger.info(f"Calculation Details: Used {details.get('offers_used_for_average', 'N/A')} offers")
                    logger.info(f"Filtering: {details.get('filtering_criteria', 'N/A')}")
                
                # Log full response for detailed debugging (truncated)
                logger.debug(f"Full API Response: {str(statistics)[:500]}...")
            
            return True, statistics
        
        # Hibaüzenet feldolgozása
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Statisztikák lekérdezési hiba")
        except Exception:
            error_message = f"Statisztikák lekérdezési hiba (status: {response.status_code}): {response.text}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága a statisztikák megtekintéséhez"
        
        if response.status_code == 422:
            return False, f"Érvénytelen paraméterek: {error_message}"
        
        return False, error_message
        
    except requests.Timeout:
        return False, "A statisztikák lekérése időtúllépés miatt megszakadt"
    except requests.ConnectionError:
        return False, "Nem sikerült kapcsolódni az API szerverhez a statisztikák lekéréséhez"
    except Exception as e:
        logger.error(f"Hiba a statisztikák lekérése során: {str(e)}")
        return False, f"Váratlan hiba a statisztikák lekérésekor: {str(e)}"
