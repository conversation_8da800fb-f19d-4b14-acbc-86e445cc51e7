# Autentikációs API hívások
"""
Autentikációs API hívások.
"""
import requests
import streamlit as st
import app_config as config
from utils.session import set_user_session, clear_session, DEFAULT_SESSION_VARS
from urllib.parse import urlencode

# Debug: <PERSON><PERSON>r<PERSON>k a config modul tartalmát
print(f"Auth - Config module: {dir(config)}")
print(f"Auth - Config SESSION_VARS: {getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)}")
print(f"Auth - API_BASE_URL: {getattr(config, 'API_BASE_URL', 'Not found')}")

def login(email, password):
    """
    Bejelentkezési API hívás
    """
    try:
        login_url = f"{config.API_BASE_URL}/auth/login"
        print(f"==== LOGIN API CALL ====")
        print(f"Login URL: {login_url}")
        print(f"Email: {email}")
        
        response = requests.post(
            login_url,
            data={"username": email, "password": password},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        # Debug információ
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"Token received: {token[:10]}..." if token else "Token: None")
            
            # Felhasználói adatok lekérése a /me végpontról
            me_url = f"{config.API_BASE_URL}/auth/me"
            print(f"Me URL: {me_url}")
            
            me_response = requests.get(
                me_url,
                headers={"Authorization": f"Bearer {token}"}
            )
            
            print(f"Me response status: {me_response.status_code}")
            print(f"Me response headers: {me_response.headers}")
            print(f"Me response text: {me_response.text}")
            
            if me_response.status_code == 200:
                user_data = me_response.json()
                print(f"User data: {user_data}")
                
                # Munkamenet beállítása
                session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
                token_key = session_vars.get("token", "auth_token")
                user_key = session_vars.get("user", "user")
                auth_key = session_vars.get("authenticated", "authenticated")
                
                st.session_state[token_key] = token
                st.session_state[user_key] = user_data
                st.session_state[auth_key] = True
                
# Debug output removed for production
                
                return True, user_data
            else:
                print(f"Failed to get user data: {me_response.status_code}")
                print(f"Me response text: {me_response.text}")
                user_data = {"email": email, "role": "unknown"}
                return True, user_data
        
        error_data = None
        try:
            error_data = response.json()
        except:
            error_data = {"detail": "Ismeretlen hiba történt a válasz feldolgozása során"}
            
        error_message = error_data.get("detail", "Bejelentkezési hiba")
        print(f"Login error: {error_message}")
        
        if response.status_code == 401:
            return False, "Hibás e-mail cím vagy jelszó."
        elif response.status_code == 404:
            return False, f"A végpont nem található: {login_url}"
        
        return False, error_message
    
    except requests.RequestException as e:
        print(f"Network error: {str(e)}")
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return False, f"Hiba: {str(e)}"
    finally:
        print("============================")

def register(user_data):
    """
    Regisztrációs API hívás
    
    Args:
        user_data (dict): Felhasználó regisztrációs adatai
        
    Returns:
        tuple: (bool, dict/str) - Sikeres regisztráció (True/False) és visszaadott adatok vagy hibaüzenet
    """
    try:
        response = requests.post(
            f"{config.API_BASE_URL}/auth/register",
            json=user_data
        )
        
        if response.status_code == 201:
            return True, response.json()
        
        error_data = response.json()
        error_message = error_data.get("detail", "Regisztrációs hiba")
        
        if response.status_code == 400 and "already exists" in error_message:
            return False, "Ez az e-mail cím már regisztrálva van."
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def request_password_reset(email):
    """
    Jelszó-visszaállítási kérés API hívás
    
    Args:
        email (str): Felhasználó e-mail címe
        
    Returns:
        tuple: (bool, str) - Sikeres kérés (True/False) és üzenet
    """
    try:
        response = requests.post(
            f"{config.API_BASE_URL}/auth/password-reset-request",
            json={"email": email}
        )
        
        if response.status_code == 200:
            # The backend always returns 200 to prevent user enumeration
            # So we always show a success message
            return True, "Ha a megadott email cím regisztrálva van a rendszerben, akkor elküldtük a jelszó-visszaállítási linket. Kérjük, ellenőrizze a postaládáját."
        
        error_data = response.json()
        error_message = error_data.get("detail", "Jelszó-visszaállítási hiba")
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def reset_password(token, new_password):
    """
    Jelszó-visszaállítás API hívás
    
    Args:
        token (str): Jelszó-visszaállítási token
        new_password (str): Új jelszó
        
    Returns:
        tuple: (bool, str) - Sikeres visszaállítás (True/False) és üzenet
    """
    try:
        response = requests.post(
            f"{config.API_BASE_URL}/auth/reset-password",
            json={"token": token, "new_password": new_password}
        )
        
        if response.status_code == 200:
            return True, "Jelszó sikeresen visszaállítva. Most már bejelentkezhet az új jelszavával."
        
        error_data = response.json()
        error_message = error_data.get("detail", "Jelszó-visszaállítási hiba")
        
        if response.status_code == 400 and "expired" in error_message.lower():
            return False, "A jelszó-visszaállítási link lejárt. Kérjük, igényeljen újat."
        
        if response.status_code == 404:
            return False, "Érvénytelen jelszó-visszaállítási link."
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"

def verify_email(token):
    """
    E-mail cím megerősítése API hívás
    
    Args:
        token (str): E-mail megerősítési token
        
    Returns:
        tuple: (bool, str) - Sikeres megerősítés (True/False) és üzenet
    """
    try:
        response = requests.post(
            f"{config.API_BASE_URL}/auth/verify-email",
            json={"token": token}
        )
        
        if response.status_code == 200:
            return True, "E-mail cím sikeresen megerősítve. Most már bejelentkezhet."
        
        error_data = response.json()
        error_message = error_data.get("detail", "E-mail megerősítési hiba")
        
        if response.status_code == 400 and "already verified" in error_message.lower():
            return False, "Ez az e-mail cím már meg van erősítve. Bejelentkezhet."
        
        if response.status_code == 404:
            return False, "Érvénytelen megerősítési link."
        
        return False, error_message
    
    except requests.RequestException as e:
        return False, f"Hálózati hiba: {str(e)}"
    
    except Exception as e:
        return False, f"Hiba: {str(e)}"
