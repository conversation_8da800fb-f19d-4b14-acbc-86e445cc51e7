# streamlit_app/utils/page_utils.py
import streamlit as st
from components.sidebar import render_sidebar
from utils.responsive_ui import setup_responsive_ui, get_theme_colors

def setup_page(title=None):
    """Beállítja az oldalt: rendereli az oldalsávot és beállítja a címet."""
    # Oldalsáv megjelenítése
    render_sidebar()
    
    # Cím beállítása, ha meg van adva
    if title:
        st.title(title)

def set_page_config(title, icon, layout="wide"):
    """
    Oldal beállítások.
    
    Args:
        title (str): Oldal címe
        icon (str): Oldal ikonja
        layout (str, optional): Oldal elrendezése. Defaults to "wide".
    """
    st.set_page_config(
        page_title=title,
        page_icon=icon,
        layout=layout,
        initial_sidebar_state="expanded"
    )
    
    # Reszponzív UI beállítás
    setup_responsive_ui()
    
    # Téma színek lekérése
    colors = get_theme_colors()
    
    # CSS stílusok
    st.markdown(f"""
    <style>
    .stButton>button {{
        width: 100%;
    }}
    .stTextInput>div>div>input {{
        width: 100%;
    }}
    .stTextArea>div>div>textarea {{
        width: 100%;
    }}
    
    /* Oldal címsor és elválasztó vonal */
    h1 {{
        color: {colors["text_color"]};
    }}
    hr {{
        border-color: {colors["border_color"]};
    }}
    </style>
    """, unsafe_allow_html=True)
    
    # Oldal címe
    st.title(f"{icon} {title}")
    
    # Elválasztó vonal
    st.markdown("---")
