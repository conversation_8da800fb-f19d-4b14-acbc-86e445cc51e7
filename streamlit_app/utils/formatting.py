# Formázási segédfüggvények
"""
Formázással kapcsolatos segédfüggvények.
"""
import datetime
import streamlit as st
import app_config as config

def format_date(date_str):
    """
    Dátum formázása olvasható formátumra.
    
    Args:
        date_str (str): D<PERSON>tum sztring (YYYY-MM-DD)
        
    Returns:
        str: Form<PERSON>zott dátum (pl. 2023. 04. 12.)
    """
    if not date_str:
        return ""
    
    try:
        date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
        return date_obj.strftime("%Y. %m. %d.")
    except ValueError:
        return date_str

def format_datetime(datetime_str):
    """
    Dátum és idő formázása olvasható formátumra.
    
    Args:
        datetime_str (str or datetime): Dátum és idő sztring vagy datetime objektum
        
    Returns:
        str: Form<PERSON><PERSON>tt dátum és idő (pl. 2023. 04. 12. 14:30)
    """
    if not datetime_str:
        return ""
    
    # Ha már datetime objektum, k<PERSON>z<PERSON>lenü<PERSON> használjuk
    if isinstance(datetime_str, datetime.datetime):
        return datetime_str.strftime("%Y. %m. %d. %H:%M")
    
    try:
        datetime_obj = datetime.datetime.strptime(datetime_str, "%Y-%m-%dT%H:%M:%S")
        return datetime_obj.strftime("%Y. %m. %d. %H:%M")
    except ValueError:
        try:
            # Másodlagos formátum próba ISO formátummal
            datetime_obj = datetime.datetime.fromisoformat(datetime_str)
            return datetime_obj.strftime("%Y. %m. %d. %H:%M")
        except ValueError:
            return datetime_str

def format_price(price):
    """
    Ár formázása olvasható formátumra.
    Ha az ár egész szám, nem jelenít meg tizedesjegyeket.
    
    Args:
        price (int, float, str): Ár. Lehet szám vagy stringként reprezentált szám.
        
    Returns:
        str: Formázott ár (pl. "1 234,56 Ft" vagy "1 200 Ft").
    """
    if price is None:
        return ""
    
    currency_suffix = " Ft"  # A pénznem fixen " Ft"

    try:
        # Konvertálás float-ra a numerikus formázáshoz
        price_float = float(price)
        
        # Ellenőrizzük, hogy az érték egész szám-e
        if price_float.is_integer():
            # Egész szám: formázás ezres elválasztóval (szóköz), tizedesjegyek nélkül
            formatted_value = f"{int(price_float):,}".replace(",", " ")
        else:
            # Nem egész szám: formázás két tizedesjeggyel, 
            # ezres elválasztó (szóköz), tizedesvessző (vessző).
            formatted_value = f"{price_float:,.2f}".replace(",", " ").replace(".", ",")
            
        return f"{formatted_value}{currency_suffix}"
    except (ValueError, TypeError):
        # String-ként adjuk vissza, ha a konverzió sikertelen
        return f"{price}{currency_suffix}" if price else ""

    """
    Ár formázása olvasható formátumra.
    
    Args:
        price (float): Ár
        
    Returns:
        str: Formázott ár (pl. 1 234,56 Ft)
    """
    if price is None:
        return ""
    
    try:
        # Convert to float to ensure numeric formatting
        price_float = float(price)
        return f"{price_float:,.2f} Ft".replace(",", " ").replace(".", ",")
    except (ValueError, TypeError):
        # Return as string if conversion fails
        return f"{price} Ft" if price else ""

def format_number(value, suffix=""):
    """
    Szám formázása olvasható formátumra.
    Ha a szám egész, nem jelenít meg tizedesjegyeket.
    
    Args:
        value (int, float, str): Szám. Lehet szám vagy stringként reprezentált szám.
        suffix (str): Opcionális utótag, pl. " kg" vagy " db". 
                      Ha szóközt szeretnénk a szám és az utótag közé, azt az utótagnak kell tartalmaznia.
        
    Returns:
        str: Formázott szám (pl. "1 234,56 kg" vagy "1 200 db").
    """
    if value is None:
        return ""
    
    try:
        # Konvertálás float-ra a numerikus formázáshoz
        value_float = float(value)
        
        # Ellenőrizzük, hogy az érték egész szám-e
        if value_float.is_integer():
            # Egész szám: formázás ezres elválasztóval (szóköz), tizedesjegyek nélkül
            formatted_numeric_part = f"{int(value_float):,}".replace(",", " ")
        else:
            # Nem egész szám: formázás két tizedesjeggyel, 
            # ezres elválasztó (szóköz), tizedesvessző (vessző).
            formatted_numeric_part = f"{value_float:,.2f}".replace(",", " ").replace(".", ",")
            
        return f"{formatted_numeric_part}{suffix}"
    except (ValueError, TypeError):
        # String-ként adjuk vissza, ha a konverzió sikertelen
        return f"{value}{suffix}" if value else ""

    """
    Szám formázása olvasható formátumra.
    
    Args:
        value (float): Szám
        suffix (str): Opcionális utótag, pl. " kg"
        
    Returns:
        str: Formázott szám (pl. 1 234,56 kg)
    """
    if value is None:
        return ""
    
    try:
        # Konvertálás float-ra a numerikus formázáshoz
        value_float = float(value)
        # Ezer elválasztó és tizedesvessző
        formatted = f"{value_float:,.2f}".replace(",", " ").replace(".", ",")
        return f"{formatted}{suffix}"
    except (ValueError, TypeError):
        # String-ként adjuk vissza, ha a konverzió sikertelen
        return f"{value}{suffix}" if value else ""

def format_currency(value, currency="Ft"):
    """
    Pénzösszeg formázása olvasható formátumra.
    Ha az összeg egész szám, nem jelenít meg tizedesjegyeket.
    
    Args:
        value (int, float, str): Az összeg. Lehet szám vagy stringként reprezentált szám.
        currency (str): Pénznem, alapértelmezetten Ft.
        
    Returns:
        str: Formázott összeg (pl. "1 234,56 Ft" vagy "1 200 Ft").
    """
    if value is None:
        return ""
    
    try:
        # Konvertálás float-ra a numerikus formázáshoz
        value_float = float(value)
        
        # Ellenőrizzük, hogy az érték egész szám-e
        if value_float.is_integer():
            # Egész szám: formázás ezres elválasztóval (szóköz), tizedesjegyek nélkül
            # Az int() konverzió itt biztosítja, hogy pl. 1200.0 mint 1200 legyen formázva.
            # A : , formátum ezres elválasztót (vesszőt) tesz bele, amit aztán szóközre cserélünk.
            formatted_value = f"{int(value_float):,}".replace(",", " ")
        else:
            # Nem egész szám: formázás két tizedesjeggyel, 
            # ezres elválasztó (szóköz), tizedesvessző (vessző).
            # Az f-string "{value_float:,.2f}" pl. "1,234.56" formátumot ad.
            # Ezt alakítjuk át a magyar formátumra:
            # 1. A vesszőt (ezres elválasztó) szóközre cseréljük: "1 234.56"
            # 2. A pontot (tizedesjel) vesszőre cseréljük: "1 234,56"
            formatted_value = f"{value_float:,.2f}".replace(",", " ").replace(".", ",")
            
        return f"{formatted_value} {currency}"
    except (ValueError, TypeError):
        # String-ként adjuk vissza, ha a konverzió sikertelen,
        # az eredeti kód logikáját követve.
        return f"{value} {currency}" if value else ""


def format_quantity(quantity, unit="kg"):
    """
    Mennyiség formázása olvasható formátumra.
    Ha a mennyiség egész szám, nem jelenít meg tizedesjegyeket.
    Automatikusan tonnára vált, ha az érték >= 1000 kg (csak súly esetén).
    
    Args:
        quantity (int, float, str): Mennyiség. Lehet szám vagy stringként reprezentált szám.
        unit (str): Mértékegység (alapértelmezetten "kg").
        
    Returns:
        str: Formázott mennyiség (pl. "1 234,56 kg", "2,50 tonna", "2 tonna", "500 kg", "150 db").
             Ha a quantity nem alakítható számmá, az eredeti quantity stringként, mértékegység nélkül.
    """
    if quantity is None:
        return ""
    
    quantity_float: float
    try:
        # Biztosítjuk, hogy a mennyiség float típusú legyen
        quantity_float = float(quantity)
    except (ValueError, TypeError):
        # Ha nem lehet float-tá alakítani, visszaadjuk az eredeti értéket stringként (az eredeti függvény logikája szerint)
        return str(quantity) 
    
    value_to_format: float
    display_unit: str

    # Ha tonnában kell megjeleníteni (ha nagyobb vagy egyenlő, mint 1000 kg)
    # Ez csak súly alapú egységekre vonatkozik (kg, tonna), darabszámnál nem
    if unit == "kg" and quantity_float >= 1000:
        value_to_format = quantity_float / 1000
        display_unit = "tonna"
    else:
        value_to_format = quantity_float
        display_unit = unit
    
    # Szám formázása az új logika szerint
    formatted_numeric_part: str
    
    # Special handling for "db" (pieces) - always show as integer
    if unit == "db":
        formatted_numeric_part = f"{int(value_to_format):,}".replace(",", " ")
    elif value_to_format.is_integer():
        # Egész szám: formázás ezres elválasztóval (szóköz), tizedesjegyek nélkül
        formatted_numeric_part = f"{int(value_to_format):,}".replace(",", " ")
    else:
        # Nem egész szám: formázás két tizedesjeggyel, 
        # ezres elválasztó (szóköz), tizedesvessző (vessző).
        formatted_numeric_part = f"{value_to_format:,.2f}".replace(",", " ").replace(".", ",")
        
    return f"{formatted_numeric_part} {display_unit}"

def format_status(status):
    """
    Ajánlat státuszának formázása olvasható formátumra.
    
    Args:
        status (str): Státusz kód (pl. CREATED)
        
    Returns:
        str: Formázott státusz név
    """
    if status not in config.OFFER_STATUSES:
        return status
    
    return config.OFFER_STATUSES[status]["name"]

def get_status_color(status):
    """
    Visszaadja az ajánlat státuszához tartozó színt.
    
    Args:
        status (str): Státusz kód (pl. CREATED)
        
    Returns:
        str: Státuszhoz tartozó HTML színkód
    """
    if status not in config.OFFER_STATUSES:
        return "#808080"  # Alapértelmezett szürke
    
    return config.OFFER_STATUSES[status]["color"]

def format_role(role):
    """
    Felhasználói szerepkör formázása olvasható formátumra.
    
    Args:
        role (str): Szerepkör kód (pl. termelő)
        
    Returns:
        str: Formázott szerepkör név
    """
    if role not in config.USER_ROLES:
        return role
    
    return config.USER_ROLES[role]

def format_validation_error(error_detail):
    """
    API validációs hibák formázása olvasható formátumra.
    
    Args:
        error_detail (str/dict/list): API válaszból származó hibarészlet
        
    Returns:
        str: Formázott hibaüzenet
    """
    # Ha már szöveges formátumú, visszaadjuk változtatás nélkül
    if isinstance(error_detail, str):
        return error_detail
    
    # Ha szótár formátumú
    if isinstance(error_detail, dict):
        # Pydantic validációs hiba formátum
        if "loc" in error_detail and "msg" in error_detail:
            field = ".".join([str(item) for item in error_detail["loc"]])
            message = error_detail["msg"]
            return f"{field}: {message}"
        
        # Egyszerű mező-üzenet pár
        messages = []
        for field, msg in error_detail.items():
            if isinstance(msg, list):
                # Ha a hibaüzenet lista, összefűzzük
                field_msgs = ", ".join(msg)
                messages.append(f"{field}: {field_msgs}")
            else:
                messages.append(f"{field}: {msg}")
        
        if messages:
            return "\n".join(messages)
    
    # Ha lista formátumú
    if isinstance(error_detail, list):
        formatted_errors = []
        for item in error_detail:
            formatted_error = format_validation_error(item)
            if formatted_error:
                formatted_errors.append(formatted_error)
        
        if formatted_errors:
            return "\n".join(formatted_errors)
    
    # Egyéb esetben próbáljuk stringként kezelni
    return str(error_detail)
