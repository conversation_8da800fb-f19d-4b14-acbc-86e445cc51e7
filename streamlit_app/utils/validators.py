# Adatvalid<PERSON><PERSON>ós függvények
"""
Adatvalid<PERSON><PERSON>ós segédfüggvények.
"""
import re
from datetime import datetime, timed<PERSON>ta

def validate_required(value, field_name):
    """
    Kötelező mező ellenőrzése.
    
    Args:
        value: Az ellenőrizendő érték
        field_name (str): A mező neve
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None or value == "":
        return False, f"A {field_name} megadása kötelező."
    
    return True, None

def validate_phone(value):
    """
    Telefonszám formátumának ellenőrzése.
    
    Args:
        value: Az ellenőrizendő telefonszám
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None or value == "":
        return True, None
    
    # Telefonszám formátum ellenőrzése (magyar formátum)
    phone_pattern = r"^(\+36|06)?[0-9]{1,2}[0-9]{3}[0-9]{4}$"
    
    if not re.match(phone_pattern, value.replace(" ", "").replace("-", "")):
        return False, "Érvénytelen telefonszám formátum. Példa: +36201234567 vagy 06201234567"
    
    return True, None

def validate_date(date_str, field_name="Dátum", allow_past=False, min_days=0, max_days=None):
    """
    Dátum érvényességének ellenőrzése.
    
    Args:
        date_str (str): Ellenőrizendő dátum (YYYY-MM-DD)
        field_name (str): Mező neve (hibaüzenethez)
        allow_past (bool): Engedélyezett-e a múltbeli dátum
        min_days (int): Minimális napok száma a mai naptól
        max_days (int): Maximális napok száma a mai naptól
        
    Returns:
        tuple: (bool, str) - True/False és hibaüzenet, ha van
    """
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
        today = datetime.now().date()
        
        if not allow_past and date_obj < today:
            return False, f"{field_name} nem lehet múltbeli dátum."
        
        if min_days > 0:
            min_date = today + timedelta(days=min_days)
            if date_obj < min_date:
                return False, f"{field_name} legalább {min_days} nappal előre kell lennie."
        
        if max_days is not None:
            max_date = today + timedelta(days=max_days)
            if date_obj > max_date:
                return False, f"{field_name} legfeljebb {max_days} nappal előre lehet."
                
        return True, ""
        
    except (ValueError, TypeError):
        return False, f"Érvénytelen dátum formátum. Használja a YYYY-MM-DD formátumot."

def validate_numeric(value, field_name, min_value=None, max_value=None):
    """
    Numerikus érték érvényességének ellenőrzése.
    
    Args:
        value: Ellenőrizendő érték
        field_name (str): Mező neve (hibaüzenethez)
        min_value (float): Minimum érték (opcionális)
        max_value (float): Maximum érték (opcionális)
        
    Returns:
        tuple: (bool, str) - True/False és hibaüzenet, ha van
    """
    try:
        num_value = float(value)
        
        if min_value is not None and num_value < min_value:
            return False, f"{field_name} értéke nem lehet kisebb, mint {min_value}."
        
        if max_value is not None and num_value > max_value:
            return False, f"{field_name} értéke nem lehet nagyobb, mint {max_value}."
            
        return True, ""
        
    except (ValueError, TypeError):
        return False, f"{field_name} értékének számnak kell lennie."

def validate_length(value, field_name, min_length=None, max_length=None):
    """
    Mező hosszának ellenőrzése.
    
    Args:
        value: Az ellenőrizendő érték
        field_name (str): A mező neve
        min_length (int, optional): Minimális hossz. Defaults to None.
        max_length (int, optional): Maximális hossz. Defaults to None.
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None:
        return True, None
    
    value_length = len(str(value))
    
    if min_length is not None and value_length < min_length:
        return False, f"A {field_name} minimum {min_length} karakter hosszú lehet."
    
    if max_length is not None and value_length > max_length:
        return False, f"A {field_name} maximum {max_length} karakter hosszú lehet."
    
    return True, None

def validate_tovacvv(value, field_name="TOVACVV"):
    """
    TOVACVV formátum ellenőrzése.
    
    Args:
        value (str): Ellenőrizendő TOVACVV érték
        field_name (str): Mező neve (hibaüzenethez)
        
    Returns:
        tuple: (bool, str) - True/False és hibaüzenet, ha van
    """
    if not isinstance(value, str):
        return False, f"{field_name} értékének szövegnek kell lennie."
    
    # TOVACVV formátum ellenőrzése: 3 karakter, csak betűk és számok
    pattern = r"^[A-Za-z0-9]{3}$"
    if not re.match(pattern, value):
        return False, f"{field_name} formátuma érvénytelen. Pontosan 3 karaktert kell tartalmaznia, amely betű vagy szám lehet."
    
    return True, ""

def validate_email(value):
    """
    E-mail cím formátumának ellenőrzése.
    
    Args:
        value: Az ellenőrizendő e-mail cím
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None or value == "":
        return True, None
    
    # E-mail formátum ellenőrzése
    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    
    if not re.match(email_pattern, value):
        return False, "Érvénytelen e-mail cím formátum."
    
    return True, None

def validate_number(value, field_name, min_value=None, max_value=None):
    """
    Számérték ellenőrzése.
    
    Args:
        value: Az ellenőrizendő érték
        field_name (str): A mező neve
        min_value (float, optional): Minimális érték. Defaults to None.
        max_value (float, optional): Maximális érték. Defaults to None.
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None or value == "":
        return True, None
    
    try:
        number = float(value)
    except ValueError:
        return False, f"A {field_name} számérték kell legyen."
    
    if min_value is not None and number < min_value:
        return False, f"A {field_name} nem lehet kisebb, mint {min_value}."
    
    if max_value is not None and number > max_value:
        return False, f"A {field_name} nem lehet nagyobb, mint {max_value}."
    
    return True, None

def validate_date(value, field_name, min_date=None, max_date=None):
    """
    Dátum ellenőrzése.
    
    Args:
        value: Az ellenőrizendő dátum
        field_name (str): A mező neve
        min_date (datetime.date, optional): Minimális dátum. Defaults to None.
        max_date (datetime.date, optional): Maximális dátum. Defaults to None.
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if value is None:
        return True, None
    
    if min_date is not None and value < min_date:
        return False, f"A {field_name} nem lehet korábbi, mint {min_date}."
    
    if max_date is not None and value > max_date:
        return False, f"A {field_name} nem lehet későbbi, mint {max_date}."
    
    return True, None
