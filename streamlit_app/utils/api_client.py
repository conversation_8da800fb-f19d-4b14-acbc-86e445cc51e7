"""
API kliens modul a Streamlit alkalmazáshoz.
"""
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import streamlit as st
import logging
import time
import json
from typing import Dict, Any, Optional, Tuple, Union, Callable
from functools import wraps
from app_config import API_BASE_URL, API_CLIENT_CONFIG

# Logolás beállítása
logger = logging.getLogger(__name__)

def cacheable(func):
    """
    Dekorátor az API válaszok gyorsítótárazásához.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Gyorsítótár kulcs generálása
        cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
        
        # Ha van gyorsítótárazott válasz, azt adjuk vissza
        if "api_cache" in st.session_state and cache_key in st.session_state.api_cache:
            logger.debug(f"Cache hit: {cache_key}")
            return st.session_state.api_cache[cache_key]
        
        # Ha nincs gyorsítótárazott válasz, végrehajtjuk a függvényt
        result = func(*args, **kwargs)
        
        # Gyorsítótárazzuk az eredményt
        if "api_cache" not in st.session_state:
            st.session_state.api_cache = {}
        st.session_state.api_cache[cache_key] = result
        
        return result
    return wrapper

class APIClient:
    """
    API kliens osztály a backend API-val való kommunikációhoz.
    """
    def __init__(self, base_url: str = API_BASE_URL, timeout: Optional[int] = None, retry_count: Optional[int] = None):
        """
        Inicializálja az API klienst.
        
        Args:
            base_url: Az API alap URL-je
            timeout: Időkorlát másodpercben (opcionális)
            retry_count: Újrapróbálkozások száma (opcionális)
        """
        self.base_url = base_url
        self.timeout = timeout or API_CLIENT_CONFIG["timeout"]
        self.retry_count = retry_count or API_CLIENT_CONFIG["retry_count"]
        
        # Újrapróbálkozási stratégia beállítása
        self.retry_strategy = Retry(
            total=self.retry_count,
            backoff_factor=API_CLIENT_CONFIG["retry_backoff_factor"],
            status_forcelist=API_CLIENT_CONFIG["retry_status_forcelist"],
            allowed_methods=API_CLIENT_CONFIG["retry_allowed_methods"]
        )
        
        # HTTP adapter beállítása
        self.session = requests.Session()
        self.session.mount("http://", HTTPAdapter(max_retries=self.retry_strategy))
        self.session.mount("https://", HTTPAdapter(max_retries=self.retry_strategy))
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Visszaadja a kérésekhez szükséges fejléceket.
        
        Returns:
            Dict[str, str]: A fejlécek
        """
        headers = {"Content-Type": "application/json"}
        if "token" in st.session_state:
            headers["Authorization"] = f"Bearer {st.session_state.token}"
        return headers
    
    def _handle_response(self, response: requests.Response) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Feldolgozza az API választ.
        
        Args:
            response: Az API válasz
            
        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
        """
        if response.status_code >= 200 and response.status_code < 300:
            return response.json() if response.content else None, None
        
        error_message = None
        try:
            error_data = response.json()
            error_message = error_data.get("detail", str(error_data))
        except:
            error_message = response.text or f"HTTP {response.status_code} hiba"
        
        # Részletes hibaüzenetek
        if response.status_code == 400:
            error_message = f"Hibás kérés: {error_message}"
        elif response.status_code == 401:
            error_message = "Hitelesítési hiba: Érvénytelen vagy lejárt token"
            st.session_state.authenticated = False
        elif response.status_code == 403:
            error_message = "Hozzáférés megtagadva: Nincs megfelelő jogosultság"
        elif response.status_code == 404:
            error_message = f"Az erőforrás nem található: {error_message}"
        elif response.status_code == 408:
            error_message = "Időtúllépés: A kérés túl sokáig tartott"
        elif response.status_code == 429:
            error_message = "Túl sok kérés: Kérjük, várjon egy kicsit"
        elif response.status_code >= 500:
            error_message = f"Szerver hiba: {error_message}"
        
        logger.error(f"API hiba: {error_message}")
        return None, error_message
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        GET kérés küldése.
        
        Args:
            endpoint: Az API végpont
            params: Query paraméterek (opcionális)
            
        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        try:
            response = self.session.get(url, headers=self._get_headers(), params=params, timeout=self.timeout)
            return self._handle_response(response)
        except requests.exceptions.RequestException as e:
            logger.error(f"GET kérés hiba: {str(e)}")
            return None, f"Hálózati hiba: {str(e)}"
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        POST kérés küldése.
        
        Args:
            endpoint: Az API végpont
            data: A küldendő adatok
            
        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        try:
            response = self.session.post(url, headers=self._get_headers(), json=data, timeout=self.timeout)
            return self._handle_response(response)
        except requests.exceptions.RequestException as e:
            logger.error(f"POST kérés hiba: {str(e)}")
            return None, f"Hálózati hiba: {str(e)}"
    
    def put(self, endpoint: str, data: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        PUT kérés küldése.
        
        Args:
            endpoint: Az API végpont
            data: A küldendő adatok
            
        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        try:
            response = self.session.put(url, headers=self._get_headers(), json=data, timeout=self.timeout)
            return self._handle_response(response)
        except requests.exceptions.RequestException as e:
            logger.error(f"PUT kérés hiba: {str(e)}")
            return None, f"Hálózati hiba: {str(e)}"
    
    def delete(self, endpoint: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        DELETE kérés küldése.
        
        Args:
            endpoint: Az API végpont
            
        Returns:
            Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        try:
            response = self.session.delete(url, headers=self._get_headers(), timeout=self.timeout)
            return self._handle_response(response)
        except requests.exceptions.RequestException as e:
            logger.error(f"DELETE kérés hiba: {str(e)}")
            return None, f"Hálózati hiba: {str(e)}"

@cacheable
def api_request(method: str, endpoint: str, data: Optional[Dict[str, Any]] = None, params: Optional[Dict[str, Any]] = None) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """
    API kérés küldése gyorsítótárazással.
    
    Args:
        method: HTTP metódus (GET, POST, PUT, DELETE)
        endpoint: Az API végpont
        data: A küldendő adatok (opcionális)
        params: Query paraméterek (opcionális)
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: A válasz adatai és esetleges hibaüzenet
    """
    client = APIClient()
    
    if method.upper() == "GET":
        return client.get(endpoint, params)
    elif method.upper() == "POST":
        return client.post(endpoint, data or {})
    elif method.upper() == "PUT":
        return client.put(endpoint, data or {})
    elif method.upper() == "DELETE":
        return client.delete(endpoint)
    else:
        error_message = f"Nem támogatott HTTP metódus: {method}"
        logger.error(error_message)
        return None, error_message

def clear_api_cache():
    """
    Törli az API gyorsítótárat.
    """
    if "api_cache" in st.session_state:
        st.session_state.api_cache = {}
        logger.info("API gyorsítótár törölve")

def handle_api_error(error, operation_type):
    """
    API hibák egységes kezelése részletes hibaüzenetekkel és felhasználói visszajelzéssel.
    
    Args:
        error (str): A hibaüzenet.
        operation_type (str): A művelet típusa (pl. "ajánlatok betöltése").
    """
    error_message = str(error)
    
    # Hibaüzenet naplózása
    logger.error(f"API error during {operation_type}: {error_message}")
    
    # 401/403 hibakódok kezelése (hitelesítési hibák)
    if "401" in error_message or "Unauthorized" in error_message or "403" in error_message:
        st.error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        # Clear auth token
        if 'token' in st.session_state:
            st.session_state.token = None
        st.rerun()
    
    # 404 hibakódok kezelése (nem található erőforrás)
    elif "404" in error_message or "Not Found" in error_message:
        st.error(f"A kért erőforrás nem található. ({operation_type})")
    
    # Kapcsolódási problémák kezelése
    elif "Connection" in error_message or "timeout" in error_message.lower():
        st.error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
    
    # Egyéb hibák kezelése
    else:
        st.error(f"Váratlan hiba történt: {error_message} ({operation_type})")

def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """
    Biztonságos API hívás végrehajtása egységes hibakezeléssel.
    
    Ez a függvény egy API hívást próbál végrehajtani, és egységesen kezeli a lehetséges hibákat.
    Hasznos az ismétlődő try-except blokkok elkerülésére az alkalmazásban.
    
    Args:
        api_function (callable): Az API funkció, amelyet meg kell hívni.
        error_operation_name (str): A művelet neve hibaüzenetekhez.
        *args: További pozíciós argumentumok az API funkcióhoz.
        **kwargs: További kulcsszó argumentumok az API funkcióhoz.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az API hívás eredményét vagy a hibaüzenetet tartalmazza.
    """
    try:
        logger.info(f"Calling API function: {api_function.__name__} with args: {args}, kwargs: {kwargs}")
        api_result = api_function(*args, **kwargs)
        
        # Handle different return formats
        if isinstance(api_result, tuple):
            if len(api_result) == 2:
                success, result = api_result
            elif len(api_result) == 1:
                # Single value returned, assume it's the result and success is True
                success, result = True, api_result[0]
            elif len(api_result) > 2:
                # More than 2 values, take first two
                logger.warning(f"API function {api_function.__name__} returned {len(api_result)} values, expected 2. Taking first two.")
                success, result = api_result[0], api_result[1]
            else:
                # Empty tuple
                success, result = False, "Empty result from API"
        else:
            # Not a tuple, assume it's the result and success is True
            success, result = True, api_result
        
        if not success:
            logger.error(f"API returned error for {error_operation_name}: {result}")
            handle_api_error(result, error_operation_name)
        
        return success, result
    except Exception as e:
        logger.error(f"Exception in {error_operation_name}: {str(e)}")
        handle_api_error(e, error_operation_name)
        return False, str(e)

def lazy_load_cache(cache_key: str, data_loader_func: Callable, cache_ttl: int = 300):
    """
    Adatok lekérése gyorsítótár használatával, csak szükség esetén tölti be az adatokat.
    
    Args:
        cache_key (str): Egyedi azonosító a gyorsítótárazott adatnak
        data_loader_func (callable): Adatbetöltő függvény, amely végrehajtásra kerül, ha nincs gyorsítótárazott adat
        cache_ttl (int, optional): A gyorsítótár élettartama másodpercben. Defaults to 300.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az eredményt tartalmazza
    """
    # Gyorsítótár inicializálása, ha még nem létezik
    if "data_cache" not in st.session_state:
        st.session_state.data_cache = {}
    
    # Cache időbélyegek inicializálása, ha még nem létezik
    if "data_cache_timestamps" not in st.session_state:
        st.session_state.data_cache_timestamps = {}
    
    # Ellenőrizzük, hogy van-e gyorsítótárazott adat és friss-e
    current_time = time.time()
    if (cache_key in st.session_state.data_cache and 
        cache_key in st.session_state.data_cache_timestamps and
        current_time - st.session_state.data_cache_timestamps[cache_key] < cache_ttl):
        
        logger.debug(f"Using cached data for key: {cache_key}")
        return st.session_state.data_cache[cache_key]
    
    # Ha nincs gyorsítótárazott adat vagy lejárt, betöltjük az adatokat
    logger.debug(f"Loading fresh data for key: {cache_key}")
    try:
        result = data_loader_func()
        
        # Az eredményt eltároljuk a gyorsítótárban
        st.session_state.data_cache[cache_key] = result
        st.session_state.data_cache_timestamps[cache_key] = current_time
        
        return result
    except Exception as e:
        logger.error(f"Error in lazy_load_cache for key {cache_key}: {str(e)}")
        return False, str(e)

def get_paginated_data(api_func, params, page_size=None, mobile_page_size=None, tablet_page_size=None):
    """
    Eszközmérethez igazított lapozási segédfüggvény API hívásokhoz.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        page_size (int, optional): Alapértelmezett lapozási méret. Defaults to None.
        mobile_page_size (int, optional): Mobil eszközökön használt lapozási méret. Defaults to None.
        tablet_page_size (int, optional): Tablet eszközökön használt lapozási méret. Defaults to None.
        
    Returns:
        tuple: (success, data, total_items)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Eszközmérethez igazított lapozási méret
    if is_mobile and mobile_page_size is not None:
        effective_page_size = mobile_page_size
    elif is_tablet and tablet_page_size is not None:
        effective_page_size = tablet_page_size
    elif page_size is not None:
        effective_page_size = page_size
    else:
        # Alapértelmezett értékek eszköz szerint
        effective_page_size = 5 if is_mobile else (10 if is_tablet else 20)
    
    # Aktuális oldal lekérése a session state-ből
    current_page = st.session_state.get("current_page", 1)
    
    # Lapozási paraméterek hozzáadása
    pagination_params = {
        "offset": (current_page - 1) * effective_page_size,
        "limit": effective_page_size
    }
    
    # Paraméterek összeolvasztása
    merged_params = {**params, **pagination_params}
    
    # API hívás
    try:
        success, result = api_func(merged_params)
        
        if success:
            # Ellenőrizzük, hogy a válasz tartalmaz-e meta információkat a teljes elemszámról
            if isinstance(result, dict) and "meta" in result and "total" in result["meta"]:
                total_items = result["meta"]["total"]
                data = result.get("data", [])
                return success, data, total_items
            
            # Ha a válasz csak egy lista, akkor a teljes elemszámot nem ismerjük
            if isinstance(result, list):
                return success, result, len(result)
            
            # Egyéb esetekben próbáljunk a result-ból adatokat kinyerni
            if isinstance(result, dict):
                data = result.get("data", result)
                total = result.get("total", len(data) if isinstance(data, list) else 0)
                return success, data, total
            
            # Visszatérünk az eredeti eredménnyel
            return success, result, 0
            
        else:
            logger.error(f"API hívás sikertelen: {result}")
            return False, result, 0
            
    except Exception as e:
        logger.error(f"Hiba az API hívás során: {str(e)}")
        return False, str(e), 0

def get_responsive_data(api_func, params, simplify_for_mobile=True):
    """
    Reszponzív adatlekérés, amely eszközmérethez igazított adatszerkezetet ad vissza.
    Mobil eszközökön egyszerűsíti az adatszerkezetet a gyorsabb betöltés érdekében.
    
    Args:
        api_func (callable): Az API függvény, amely a lekérdezést végrehajtja
        params (dict): Az API függvény paraméterei
        simplify_for_mobile (bool, optional): Mobilon egyszerűsítse-e az adatszerkezetet. Defaults to True.
        
    Returns:
        tuple: (success, data)
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # API hívás
    try:
        success, result = api_func(params)
        
        if not success:
            return False, result
        
        # Mobil esetén egyszerűsítés, ha a flag engedélyezi
        if (is_mobile or is_tablet) and simplify_for_mobile:
            # Adatszerkezet egyszerűsítése eszköz szerint
            if isinstance(result, list):
                simplified_result = []
                for item in result:
                    if isinstance(item, dict):
                        # Csak a fontosabb mezőket tartjuk meg
                        simplified_item = {}
                        
                        # Kulcsok kiválasztása az eszköz alapján
                        important_keys = get_important_keys(is_mobile, is_tablet)
                        
                        # Fontos mezők megőrzése
                        for key in important_keys:
                            if key in item:
                                simplified_item[key] = item[key]
                        
                        # Beágyazott objektumok egyszerűsítése
                        for key, value in item.items():
                            if isinstance(value, dict) and not key in simplified_item:
                                # A beágyazott objektumból csak az id és name mezőket tartjuk meg
                                if "id" in value:
                                    simplified_item[f"{key}_id"] = value["id"]
                                if "name" in value:
                                    simplified_item[f"{key}_name"] = value["name"]
                        
                        simplified_result.append(simplified_item)
                    else:
                        simplified_result.append(item)
                
                return True, simplified_result
                
            elif isinstance(result, dict):
                simplified_result = {}
                
                # Ha van data kulcs, akkor azon alkalmazzuk az egyszerűsítést
                if "data" in result and isinstance(result["data"], list):
                    simplified_result["data"] = get_responsive_data(lambda _: (True, result["data"]), None, True)[1]
                    
                    # Meta adatok megtartása
                    if "meta" in result:
                        simplified_result["meta"] = result["meta"]
                        
                    return True, simplified_result
                
                # Fontos mezők megőrzése
                important_keys = get_important_keys(is_mobile, is_tablet)
                for key in important_keys:
                    if key in result:
                        simplified_result[key] = result[key]
                
                return True, simplified_result
        
        # Ha nincs egyszerűsítés, vagy nem mobil/tablet, visszaadjuk az eredeti adatokat
        return True, result
    
    except Exception as e:
        logger.error(f"Hiba a reszponzív adatlekérés során: {str(e)}")
        return False, str(e)

def get_important_keys(is_mobile=False, is_tablet=False):
    """
    Fontosabb mezők listája az eszköz mérete alapján.
    
    Args:
        is_mobile (bool, optional): Mobil eszköz esetén. Defaults to False.
        is_tablet (bool, optional): Tablet eszköz esetén. Defaults to False.
        
    Returns:
        list: Fontosabb mezők listája
    """
    # Minimális mezők mobilon
    if is_mobile:
        return [
            "id", "name", "title", "status", "created_at", "quantity", "quantity_in_kg", 
            "price", "confirmed_price", "delivery_date", "product_type_id", "user_id"
        ]
    
    # Bővebb mezők tableten
    if is_tablet:
        return [
            "id", "name", "title", "description", "status", "created_at", "updated_at",
            "quantity", "quantity_in_kg", "price", "confirmed_price", "delivery_date", 
            "product_type_id", "user_id", "note", "tags"
        ]
    
    # Minden mező asztali eszközökön
    return None  # None esetén nem szűrünk mezőket

def fetch_data_with_progress(api_func, operation_name, params=None):
    """
    Adatok betöltése vizuális folyamatjelzővel.
    
    Ez a függvény egy Streamlit progress bar-t jelenít meg, miközben
    betölti az adatokat az API-ból, majd visszaadja az eredményt.
    
    Args:
        api_func (callable): Az API funkció, amelyet meg kell hívni.
        operation_name (str): A művelet neve hibaüzenetekhez.
        params (dict, optional): Paraméterek az API híváshoz.
        
    Returns:
        tuple: (sikeres, eredmény) formában
    """
    # Képernyőméret információk lekérése
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Folyamatjelző mobilon és tabeten kompaktabb
    if is_mobile or is_tablet:
        # Egyszerűbb folyamatjelző használata
        with st.spinner(f"{operation_name} folyamatban..."):
            return safe_api_call(api_func, operation_name, params or {})
    else:
        # Folyamatjelző megjelenítése
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Hálózati kérés előtti szöveg
        status_text.text(f"{operation_name} folyamatban...")
        
        try:
            # Cache kulcs generálása a lekérdezési paraméterekből
            cache_params = params.copy() if params else {}
            cache_key = f"{api_func.__name__}_{json.dumps(cache_params, sort_keys=True)}"
            
            # Adatok betöltése a lazy_load_cache segítségével
            success, result = lazy_load_cache(
                cache_key=cache_key,
                data_loader_func=lambda: api_func(params or {}),
                cache_ttl=120  # 2 perces cache
            )
            
            # Folyamatjelző frissítése
            progress_bar.progress(50)
            
            if success:
                # Folyamatjelző frissítése
                progress_bar.progress(100)
                status_text.text(f"{operation_name} sikeresen befejeződött")
            else:
                # Hiba esetén
                progress_bar.progress(100)
                status_text.text(f"Hiba: {result}")
                # Hiba kezelése
                handle_api_error(result, operation_name)
            
            # Várakozás, hogy a felhasználó lássa az eredményt
            time.sleep(0.5)
            
            # Folyamatjelző és státusz törlése
            progress_bar.empty()
            status_text.empty()
            
            return success, result
            
        except Exception as e:
            # Folyamatjelző és státusz törlése hiba esetén
            progress_bar.empty()
            status_text.empty()
            
            # Hiba kezelése
            handle_api_error(e, operation_name)
            
            # Hiba visszaadása
            logger.error(f"Error in fetch_data_with_progress: {str(e)}")
            return False, str(e)
