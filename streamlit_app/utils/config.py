"""
Configuration utility functions for the Streamlit application.
"""
import os
import app_config as app_config

def get_app_name():
    """
    Get the application name with fallback.
    
    Returns:
        str: The application name.
    """
    return getattr(app_config, 'APP_NAME', os.getenv('APP_NAME', 'POM APP'))

def get_company_name():
    """
    Get the company name with fallback.
    
    Returns:
        str: The company name.
    """
    return getattr(app_config, 'COMPANY_NAME', os.getenv('COMPANY_NAME', 'Termelo'))

def get_page_title(title):
    """
    Get a page title with the application name.
    
    Args:
        title (str): The page title.
        
    Returns:
        str: The full page title including the application name.
    """
    return f"{title} - {get_app_name()}"

def get_offer_statuses():
    """
    Get the offer statuses with fallback.
    
    Returns:
        dict: A dictionary of offer status codes and their display names.
    """
    return getattr(app_config, 'OFFER_STATUSES', {
        'CREATED': '<PERSON>étre<PERSON>zva',
        'CONFIRMED_BY_COMPANY': 'Visszaigazolva',
        'ACCEPTED_BY_USER': 'Elfogadva',
        'REJECTED_BY_USER': 'Elutasítva',
        'FINALIZED': 'Véglegesítve',
        'CANCELLED': 'Törölve'
    }) 