# Responsive UI Components for Streamlit

Ez a dokumentáció bemutatja a reszponzív UI komponensek használatát a Streamlit alkalmazásokban.

## Bevezetés

A `responsive_ui.py` modul különböző eszközökre optimalizált UI komponenseket és segédfüggvényeket tartalmaz, amely<PERSON> lehetővé teszik, hogy a Streamlit alkalmazásunk megfelelően jelenjen meg különböző képernyőméreteken (asztali gép, tablet, mobiltelefon).

## Tartalomjegyzék

1. [Alapfunkciók](#alapfunkciók)
2. [UI Komponensek](#ui-komponensek)
3. [P<PERSON>ldák](#példák)
4. [Gyakori használati minták](#gyakori-használati-minták)

## Alapfunkciók

### Képernyőméret <PERSON>

```python
from utils.responsive_ui import detect_mobile, setup_responsive_ui, inject_screen_detection

# A streamlit oldal elején
st.set_page_config(page_title="Reszponzív Demo", layout="wide")

# Reszponzív UI beállítása
setup_responsive_ui()

# VAGY külön-külön:
# Mobileszköz detektálása
detect_mobile()

# Részletesebb képernyőméret érzékelés
inject_screen_detection()
```

A `setup_responsive_ui()` függvény automatikusan beállítja a szükséges CSS stílusokat és JavaScript kódot a reszponzív UI-hoz. Ez a függvény használja a `detect_mobile()` függvényt is.

### Session State változók

A modul a következő session state változókat használja:

- `st.session_state.is_mobile`: `True`, ha a felhasználó mobileszközön van
- `st.session_state.is_tablet`: `True`, ha a felhasználó tableten van
- `st.session_state.screen_width`: A képernyő szélessége pixelben
- `st.session_state.screen_height`: A képernyő magassága pixelben

### Téma színek

A modul automatikusan kezeli a világos és sötét témát, a színek lekérhetők:

```python
from utils.responsive_ui import get_theme_colors

colors = get_theme_colors()
```

## UI Komponensek

### Reszponzív oszlopok

```python
from utils.responsive_ui import create_responsive_columns

# Alapértelmezett arányok (1:1)
col1, col2 = create_responsive_columns()

# Egyedi arányok (2:1)
col1, col2 = create_responsive_columns([2, 1])

# Mobilon ne rendezze egymás alá az oszlopokat
col1, col2 = create_responsive_columns([1, 1], mobile_stack=False)
```

### Reszponzív kártyák

```python
from utils.responsive_ui import display_card

# Egyszerű kártya
display_card(
    title="Fontos információ",
    content="<p>Ez egy fontos információ.</p>"
)

# Ikon és egyedi téma
display_card(
    title="Statisztika",
    content="<p>Statisztikai adatok...</p>",
    icon="📊",
    is_dark=True  # Sötét téma kényszerítése
)
```

### Szekció kártya (összecsukható/kibontható)

```python
from utils.responsive_ui import render_section_card

# Tartalmat függvényként kell átadni
def section_content():
    st.write("Ez a szekció tartalma")
    st.metric("Értékesítés", "100 db", "10%")

# Kártya renderelése
render_section_card(
    title="Részletes adatok",
    content=section_content,
    color="#1976D2",  # Kék szín
    icon="📊",
    key="unique_key",
    expanded=True,  # Alapértelmezetten nyitott
    is_mobile=None  # Automatikus detektálás
)
```

### Reszponzív tabok

```python
from utils.responsive_ui import render_responsive_tabs

# Tab tartalmak definiálása függvényekkel
def tab1_content():
    st.write("Első tab tartalma")

def tab2_content():
    st.write("Második tab tartalma")

# Tabok renderelése
render_responsive_tabs(
    {
        "Tab 1": tab1_content,
        "Tab 2": tab2_content
    },
    key_prefix="my_tabs"
)
```

### Reszponzív DataFrame

```python
from utils.responsive_ui import show_responsive_dataframe
import pandas as pd

# DataFrame létrehozása
df = pd.DataFrame({
    'A': [1, 2, 3],
    'B': [4, 5, 6],
    'C': [7, 8, 9],
    'D': [10, 11, 12],
    'E': [13, 14, 15]
})

# Reszponzív megjelenítés
show_responsive_dataframe(df, height=400)
```

### Reszponzív naptár

```python
from utils.responsive_ui import render_responsive_calendar

# Naptár események
events = [
    {
        'id': '1',
        'title': 'Esemény 1',
        'date': '2023-05-10',
        'status': 'confirmed_by_company'
    },
    {
        'id': '2',
        'title': 'Esemény 2',
        'date': '2023-05-15',
        'status': 'created'
    }
]

# Naptári adatok formázása a megfelelő formátumra
calendar_data = [
    {
        'date': '2023-05-10',
        'events': [events[0]]
    },
    {
        'date': '2023-05-15',
        'events': [events[1]]
    }
]

# Naptár renderelése
render_responsive_calendar(
    calendar_data,
    view_type="month",
    interactive=True  # Csak asztali nézeten lesz interaktív
)
```

### Akciógombok

```python
from utils.responsive_ui import render_responsive_action_bar

# Akciók definiálása
actions = [
    {
        "label": "Mentés",
        "key": "save_button",
        "color": "primary",
        "icon": "💾"
    },
    {
        "label": "Törlés",
        "key": "delete_button",
        "color": "secondary",
        "icon": "🗑️"
    }
]

# Gombok renderelése
results = render_responsive_action_bar(actions)

# Eredmények kezelése
if results.get("save_button"):
    st.write("Mentés gomb megnyomva")
if results.get("delete_button"):
    st.write("Törlés gomb megnyomva")
```

### Toast üzenetek

```python
from utils.responsive_ui import show_toast

# Sikeres művelet után
show_toast(
    message="Sikeres mentés!",
    type="success",
    duration=3000  # 3 másodperc
)

# Hiba esetén
show_toast(
    message="Hiba történt!",
    type="error",
    duration=5000  # 5 másodperc
)
```

## Példák

### Reszponzív Űrlap

```python
import streamlit as st
from utils.responsive_ui import setup_responsive_ui, create_responsive_columns

# UI beállítása
st.set_page_config(page_title="Reszponzív űrlap", layout="wide")
setup_responsive_ui()

# Cím
st.title("Reszponzív Űrlap")

# Űrlap oszlopok
col1, col2 = create_responsive_columns()

with col1:
    name = st.text_input("Név")
    email = st.text_input("Email")

with col2:
    age = st.number_input("Életkor", min_value=0, max_value=120)
    gender = st.selectbox("Nem", ["Férfi", "Nő", "Egyéb"])

# Gomb (mobilon teljes szélességű lesz)
if st.button("Küldés", use_container_width=True):
    st.success("Űrlap elküldve!")
```

### Reszponzív Dashboard

```python
import streamlit as st
import pandas as pd
import plotly.express as px
from utils.responsive_ui import setup_responsive_ui, create_responsive_columns, render_section_card

# UI beállítása
st.set_page_config(page_title="Dashboard", layout="wide")
setup_responsive_ui()

# Cím
st.title("Reszponzív Dashboard")

# Adatok generálása
df = pd.DataFrame({
    'Hónap': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'Eladások': [120, 150, 200, 180, 210],
    'Költségek': [90, 110, 120, 130, 150]
})

# KPI kártyák
kpi_cols = create_responsive_columns([1, 1, 1])

with kpi_cols[0]:
    st.metric("Összes eladás", f"{sum(df['Eladások']):,} db", "15%")

with kpi_cols[1]:
    st.metric("Átlagos eladás", f"{df['Eladások'].mean():.1f} db", "5.2%")

with kpi_cols[2]:
    st.metric("Nyereség", f"{sum(df['Eladások'] - df['Költségek']):,} Ft", "8.1%")

# Grafikonok
def sales_chart():
    fig = px.line(df, x='Hónap', y='Eladások', title='Havi eladások')
    st.plotly_chart(fig, use_container_width=True)

def costs_chart():
    fig = px.bar(df, x='Hónap', y='Költségek', title='Havi költségek')
    st.plotly_chart(fig, use_container_width=True)

def profit_chart():
    df['Nyereség'] = df['Eladások'] - df['Költségek']
    fig = px.area(df, x='Hónap', y='Nyereség', title='Havi nyereség')
    st.plotly_chart(fig, use_container_width=True)

# Szekciók megjelenítése
render_section_card("Eladások", sales_chart, color="#4CAF50", icon="📈")
render_section_card("Költségek", costs_chart, color="#F44336", icon="📉")
render_section_card("Nyereség", profit_chart, color="#2196F3", icon="💰")
```

## Gyakori használati minták

### Képernyőméret alapján feltételes renderelés

```python
import streamlit as st
from utils.responsive_ui import setup_responsive_ui

# UI beállítása
st.set_page_config(page_title="Feltételes renderelés", layout="wide")
setup_responsive_ui()

# Feltételes renderelés
is_mobile = st.session_state.get("is_mobile", False)
is_tablet = st.session_state.get("is_tablet", False)

if is_mobile:
    st.subheader("Mobilnézet")
    st.write("Egyszerűsített adatok")
elif is_tablet:
    st.subheader("Tabletnézet")
    st.write("Közepes részletesség")
else:
    st.subheader("Asztali nézet")
    st.write("Teljes részletesség")
```

### Adattábla különböző nézetekben

```python
import streamlit as st
import pandas as pd
from utils.responsive_ui import setup_responsive_ui, render_responsive_tabs

# UI beállítása
st.set_page_config(page_title="Adattábla nézetek", layout="wide")
setup_responsive_ui()

# Adatok
df = pd.DataFrame({
    'ID': range(1, 11),
    'Név': [f"Termék {i}" for i in range(1, 11)],
    'Ár': [1000 * i for i in range(1, 11)],
    'Készleten': [100 - i * 5 for i in range(1, 11)]
})

# Különböző nézetek
def table_view():
    st.dataframe(df)

def chart_view():
    st.bar_chart(df.set_index('Név')['Ár'])

def card_view():
    for _, row in df.iterrows():
        st.markdown(f"""
        <div style="border:1px solid #ddd; border-radius:5px; padding:10px; margin-bottom:10px;">
            <h3>{row['Név']}</h3>
            <p>ID: {row['ID']}</p>
            <p>Ár: {row['Ár']:,} Ft</p>
            <p>Készleten: {row['Készleten']} db</p>
        </div>
        """, unsafe_allow_html=True)

# Tabok renderelése
render_responsive_tabs(
    {
        "Táblázat": table_view,
        "Grafikon": chart_view,
        "Kártyák": card_view
    },
    key_prefix="product_views"
)
```

---

Ez a dokumentáció áttekintést nyújt a reszponzív UI komponensek alapvető használatáról. Részletesebb információkért tekintse meg a forráskódot és a példákat.
