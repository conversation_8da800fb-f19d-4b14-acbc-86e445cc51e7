"""
Navig<PERSON><PERSON><PERSON> segédfüggvények a Streamlit alkalmazáshoz.
"""
import streamlit as st
import app_config as config
from utils.session import get_current_user, clear_session, is_authenticated

def show_sidebar_menu():
    """
    <PERSON><PERSON><PERSON><PERSON>ti a felhasználó szerepköréhez tartozó oldalsáv menüt.
    """
    if not is_authenticated():
        menu_items = config.PUBLIC_MENU
    else:
        user = get_current_user()
        role = user.get("role", "").lower()
        
        if role in config.ROLE_MENUS:
            menu_items = config.ROLE_MENUS[role]
        else:
            # Fallback az alapértelmezett menüre
            menu_items = config.PUBLIC_MENU
            st.sidebar.warning("Ismeretlen felhasználói szerepkör. Alapértelmezett menü megjelenítése.")
    
    # Menü megjelenítése
    for item in menu_items:
        label = item.get("label", "")
        icon = item.get("icon", "")
        path = item.get("path", "")
        
        # <PERSON><PERSON>pont megjelenítése
        if st.sidebar.button(f"{icon} {label}"):
            st.switch_page(path)
    
    # Kijelentkezés gomb, ha be van jelentkezve
    if is_authenticated():
        if st.sidebar.button("🚪 Kijelentkezés"):
            clear_session()
            st.rerun()  # Oldal újratöltése a munkamenet törlése után

def check_role_access(allowed_roles):
    """
    Ellenőrzi, hogy a felhasználó rendelkezik-e a szükséges jogosultsággal.
    
    Args:
        allowed_roles (list): Engedélyezett szerepkörök listája
        
    Returns:
        bool: True, ha a felhasználó jogosult, egyébként False
    """
    if not is_authenticated():
        st.error("Ez az oldal bejelentkezést igényel.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("main.py")
        return False
    
    user = get_current_user()
    role = user.get("role", "").lower()
    
    if not allowed_roles or role in [r.lower() for r in allowed_roles]:
        return True
    
    st.error(f"Nincs jogosultsága az oldal megtekintéséhez. Az Ön szerepköre: {role}")
    
    # Átirányítás a megfelelő irányítópultra
    if role == "termelő":
        st.switch_page("pages/producer_dashboard.py")
    elif role == "ügyintéző":
        st.switch_page("pages/operator_dashboard.py")
    elif role == "admin":
        st.switch_page("pages/admin_dashboard.py")
    else:
        st.switch_page("main.py")
    
    return False

def get_query_params():
    """
    Visszaadja az URL lekérdezési paramétereit.
    
    Returns:
        dict: A lekérdezési paraméterek szótára
    """
    query_params = st.query_params
    return dict(query_params) if query_params else {} 