from api import users

def get_user_name_by_id(user_id: int) -> str:
    """
    <PERSON><PERSON><PERSON><PERSON><PERSON> a felhasz<PERSON><PERSON><PERSON> nevét (contact_name vagy email) az ID alapján.
    Args:
        user_id (int): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ítója
    Returns:
        str: Felhasználó neve vagy e-mail címe, vagy az ID stringként, ha nem található.
    """
    # Csak a session tokennel dolgozunk, API_AUTH_TOKEN-t nem használunk!
    success, user_data = users.get_user(user_id)
    if success and isinstance(user_data, dict):
        name = user_data.get('contact_name')
        if name:
            return name
        email = user_data.get('email')
        if email:
            return email
    # Jogosultsághiány vagy hiba esetén:
    return f"Zöldség Világ Kft. ({user_id})"
