# Autentik<PERSON><PERSON><PERSON> segédfüggvények
"""
Autentikációval kapcsolatos segédfüggvények.
"""
import re
import streamlit as st
from utils.session import clear_session

def validate_email(email):
    """
    <PERSON><PERSON><PERSON><PERSON>, hogy az e-mail cím formátuma helyes-e.
    
    Args:
        email (str): <PERSON><PERSON><PERSON>endő e-mail cím
        
    Returns:
        bool: True, ha az e-mail form<PERSON><PERSON>a helyes, egy<PERSON>bk<PERSON>t False
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None

def validate_password(password):
    """
    <PERSON><PERSON><PERSON><PERSON>, hogy a jelszó megfelel-e a követelményeknek.
    
    Args:
        password (str): Ellen<PERSON><PERSON><PERSON><PERSON> jelszó
        
    Returns:
        tuple: (bool, str) - True/False és hibaüzenet, ha van
    """
    if len(password) < 8:
        return False, "A jelszónak legalább 8 karakter hosszúnak kell lennie."
    
    if not any(char.isdigit() for char in password):
        return False, "A jelszónak tartalmaznia kell legalább egy számot."
    
    if not any(char.isalpha() for char in password):
        return False, "A jelszónak tartalmaznia kell legalább egy betűt."
    
    return True, ""

def validate_tax_id(tax_id):
    """
    Ellenőrzi az adószám formátumát.
    
    Args:
        tax_id (str): Ellenőrizendő adószám
        
    Returns:
        bool: True, ha az adószám formátuma helyes, egyébként False
    """
    # Magyar adószám formátum: 12345678-1-23
    pattern = r"^\d{8}-\d-\d{2}$"
    return re.match(pattern, tax_id) is not None

def logout():
    """
    Kijelentkezteti a felhasználót.
    """
    clear_session()
    st.success("Sikeres kijelentkezés!")
    st.rerun()
