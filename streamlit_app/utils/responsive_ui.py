"""
Reszponzív UI segédfüggvények és komponensek.
"""
import streamlit as st
import json

def detect_mobile():
    """
    Mobileszköz detektálása és tárolása a session state-ben.
    
    Ezt a függvényt az oldal elejére kell helyezni a betöltés során.
    """
    if "is_mobile_detected" not in st.session_state:
        st.session_state.is_mobile_detected = False
        st.session_state.is_mobile = False
        st.session_state.is_tablet = False
        st.session_state.screen_width = 1200  # Default value
        
        # JavaScript a böngésző szélesség ellenőrzésére
        st.markdown("""
        <script>
        // Mobileszköz detektálás és session tárolás
        function checkDeviceType() {
            const width = window.innerWidth;
            const isMobile = width < 768;
            const isTablet = width >= 768 && width < 992;
            
            window.parent.postMessage({
                type: "streamlit:setSessionState", 
                data: {
                    is_mobile: isMobile,
                    is_tablet: isTablet,
                    screen_width: width
                }
            }, "*");
            
            console.log("Device detection:", {isMobile, isTablet, width});
        }
        
        // Betöltéskor és átméretezéskor is ellenőrzünk
        checkDeviceType();
        window.addEventListener('resize', checkDeviceType);
        </script>
        """, unsafe_allow_html=True)
        
        st.session_state.is_mobile_detected = True

def get_theme_colors():
    """
    Az aktuális téma színeinek lekérése.
    
    Returns:
        dict: A téma színei
    """
    # Téma detektálás (alapértelmezetten a session state-ből)
    is_dark = st.session_state.get("theme", "light") == "dark"
    
    return {
        "bg_color": "#1E1E1E" if is_dark else "#FFFFFF",
        "text_color": "#FFFFFF" if is_dark else "#333333",
        "accent_color": "#90CAF9" if is_dark else "#1976D2",
        "success_color": "#66BB6A" if is_dark else "#43A047",
        "warning_color": "#FFA726" if is_dark else "#FB8C00",
        "danger_color": "#EF5350" if is_dark else "#E53935",
        "info_color": "#42A5F5" if is_dark else "#2196F3",
        "muted_color": "#9E9E9E" if is_dark else "#9E9E9E",
        "border_color": "#555555" if is_dark else "#DDDDDD",
        "card_bg_color": "#2D2D2D" if is_dark else "#F8F9FA",
        "card_border_color": "#444444" if is_dark else "#E9ECEF"
    }

def setup_responsive_ui():
    """
    Reszponzív UI beállítása.
    
    Ezt a függvényt az oldal elejére kell helyezni, a set_page_config után.
    """
    # Mobileszköz detektálása
    detect_mobile()
    
    # Reszponzív CSS stílusok beszúrása
    colors = get_theme_colors()
    
    st.markdown(f"""
    <style>
    /* Általános mobilbarát beállítások */
    @media (max-width: 768px) {{
        /* Táblázatok és dataframe-ek */
        .stDataFrame, .stTable {{
            width: 100%;
            overflow-x: auto;
        }}
        
        /* Gombok teljes szélességű megjelenítése */
        .stButton > button {{
            width: 100%;
            margin: 2px 0;
        }}
        
        /* Input mezők és szövegmezők */
        .stTextInput > div > div > input,
        .stTextArea > div > div > textarea {{
            width: 100%;
        }}
        
        /* Naptár optimalizálás */
        .calendar-container {{
            font-size: 0.8em;
        }}
        
        .calendar-day {{
            min-height: 60px;
            padding: 5px;
        }}
        
        .calendar-event {{
            padding: 2px;
            margin: 1px 0;
        }}
        
        /* Header átméretezése mobilon */
        .element-container div[data-testid="stHeading"] {{
            font-size: 1.5em !important;
        }}
        
        /* Táblázat kisebbre átméretezése */
        .dataframe {{ 
            font-size: 0.8rem !important;
        }}
        
        /* Szűkített paddingok */
        .block-container {{
            padding-top: 1rem !important;
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
            padding-bottom: 1rem !important;
        }}
    }}
    
    /* Tablet nézet (768-992px) */
    @media (min-width: 768px) and (max-width: 992px) {{
        /* Táblázat mérete tableten */
        .dataframe {{
            font-size: 0.9rem !important;
        }}
        
        /* Szűkített paddingok */
        .block-container {{
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }}
    }}
    
    /* Sötét téma optimalizálások */
    .dark-mode {{
        background-color: {colors["bg_color"]};
        color: {colors["text_color"]};
    }}
    
    .dark-mode .calendar-header {{
        background-color: {colors["bg_color"]};
        color: {colors["text_color"]};
        border-bottom: 1px solid {colors["border_color"]};
    }}
    
    .dark-mode .calendar-day {{
        background-color: {colors["bg_color"]};
        border: 1px solid {colors["border_color"]};
    }}
    
    .dark-mode .calendar-event {{
        background-color: {colors["accent_color"]};
        color: white;
    }}
    
    /* Reszponzív kártya komponensek */
    .responsive-card {{
        border: 1px solid {colors["card_border_color"]};
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: {colors["card_bg_color"]};
        transition: transform 0.2s, box-shadow 0.2s;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }}
    
    .responsive-card:hover {{
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }}
    
    .responsive-card-header {{
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid {colors["border_color"]};
        padding-bottom: 10px;
        margin-bottom: 10px;
    }}
    
    .responsive-card-title {{
        font-size: 1.2em;
        font-weight: bold;
        margin: 0;
    }}
    
    .responsive-card-content {{
        margin-top: 10px;
    }}
    
    /* Reszponzív form elemek */
    .responsive-form-group {{
        margin-bottom: 15px;
    }}
    
    .responsive-form-label {{
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }}
    
    /* Badge stílus */
    .badge {{
        display: inline-block;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.8em;
        font-weight: bold;
    }}
    
    .badge-success {{
        background-color: {colors["success_color"]};
        color: white;
    }}
    
    .badge-warning {{
        background-color: {colors["warning_color"]};
        color: white;
    }}
    
    .badge-danger {{
        background-color: {colors["danger_color"]};
        color: white;
    }}
    
    .badge-info {{
        background-color: {colors["info_color"]};
        color: white;
    }}
    
    /* Status pill stílusok */
    .status-pill {{
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: bold;
        text-align: center;
    }}
    
    /* Toast/alert üzenetek */
    .toast-container {{
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        max-width: 300px;
    }}
    
    .toast {{
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        animation: fadeIn 0.3s ease-in;
    }}
    
    .toast-success {{
        background-color: {colors["success_color"]};
        color: white;
    }}
    
    .toast-error {{
        background-color: {colors["danger_color"]};
        color: white;
    }}
    
    .toast-warning {{
        background-color: {colors["warning_color"]};
        color: white;
    }}
    
    .toast-info {{
        background-color: {colors["info_color"]};
        color: white;
    }}
    
    @keyframes fadeIn {{
        from {{ opacity: 0; transform: translateY(-20px); }}
        to {{ opacity: 1; transform: translateY(0); }}
    }}
    </style>
    """, unsafe_allow_html=True)

def display_card(title, content, icon=None, is_dark=None):
    """
    Kártya megjelenítése.
    
    Args:
        title (str): Kártya címe
        content (str): Kártya tartalma (HTML)
        icon (str, optional): Kártya ikonja. Defaults to None.
        is_dark (bool, optional): Sötét téma. Ha None, akkor az aktuális téma alapján dönti el.
    """
    if is_dark is None:
        is_dark = st.session_state.get("theme", "light") == "dark"
    
    colors = get_theme_colors()
    
    # Kártya stílusok
    card_style = f"""
    border: 1px solid {colors["border_color"]};
    border-radius: 5px;
    padding: 15px;
    margin: 10px 0;
    background-color: {colors["bg_color"]};
    color: {colors["text_color"]};
    """
    
    # Kártya cím stílusok
    title_style = f"""
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 10px;
    """
    
    # HTML generálása
    html = f"""
    <div style="{card_style}">
        <div style="{title_style}">{icon or ""} {title}</div>
        <div>{content}</div>
    </div>
    """
    
    st.markdown(html, unsafe_allow_html=True)

def create_responsive_columns(ratios=None, mobile_stack=True):
    """
    Reszponzív oszlopok létrehozása.
    
    Args:
        ratios (list, optional): Oszlop arányok. Defaults to None.
        mobile_stack (bool, optional): Mobilon egymás alá rendezi. Defaults to True.
    
    Returns:
        list: Streamlit oszlopok listája
    """
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Ha mobilnézet és mobile_stack=True, akkor egyenlő 1-es oszlopok
    if is_mobile and mobile_stack:
        return [st.column(1) for _ in (ratios or [1])]
    
    # Tabletnézet esetén egyszerűsített oszlopok, ha sok oszlop van
    if is_tablet and ratios and len(ratios) > 3:
        # Tablet eszközön maximum 3 oszlopot engedünk
        simplified_ratios = ratios[:3]
        # Az arányokat normalizáljuk, hogy 1 legyen az összegük
        total = sum(simplified_ratios)
        normalized_ratios = [r/total for r in simplified_ratios]
        return st.columns(normalized_ratios)
    
    # Egyébként az adott arányokkal, vagy alapértelmezettekkel
    return st.columns(ratios or [1, 1])

def render_responsive_calendar(calendar_data, view_type="month", interactive=False):
    """
    Reszponzív naptár renderelése
    
    Args:
        calendar_data (list): A naptári adatok
        view_type (str): A naptár típusa ("month", "week")
        interactive (bool): Interaktív mód engedélyezése
    """
    import streamlit as st
    import datetime
    from datetime import date
    from components.calendar_component import render_css_calendar, render_interactive_calendar
    
    # Naptári adatok formázása az események számára
    events = []
    month_date = date.today()  # Alapértelmezett dátum
    
    if view_type == "month":
        # Havi nézet esetén átalakítjuk a calendar_data-t
        for day_data in calendar_data:
            # Ha van date mező, abból kiolvassuk a hónapot
            if 'date' in day_data and day_data['date']:
                try:
                    date_str = day_data['date']
                    if isinstance(date_str, str):
                        parts = date_str.split('-')
                        if len(parts) == 3:
                            month_date = date(int(parts[0]), int(parts[1]), 1)
                except Exception:
                    pass  # Hibás dátum esetén marad az alapértelmezett
                
            # Események hozzáadása
            for event in day_data.get('events', []):
                events.append(event)
    else:
        # Heti vagy napi nézet esetén
        for day_data in calendar_data:
            # Ha van date mező, abból kiolvassuk a dátumot
            if 'date' in day_data and day_data['date']:
                try:
                    date_str = day_data['date']
                    if isinstance(date_str, str):
                        parts = date_str.split('-')
                        if len(parts) == 3:
                            month_date = date(int(parts[0]), int(parts[1]), int(parts[2]))
                except Exception:
                    pass  # Hibás dátum esetén marad az alapértelmezett
                
            # Események hozzáadása
            for event in day_data.get('events', []):
                events.append(event)
    
    # Mobilon mindig egyszerű naptár
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    
    if interactive and not is_mobile and not is_tablet:
        # Interaktív naptár csak asztali nézetben
        render_interactive_calendar(events, month_date)
    else:
        # Egyszerű CSS naptár (mobilbarát)
        render_css_calendar(events, month_date)

def render_responsive_tabs(tabs_content, key_prefix="tab"):
    """
    Reszponzív tabok megjelenítése a képernyőméret alapján.
    Mobilon dropdown menüként, nagyobb képernyőkön tabként.
    
    Args:
        tabs_content (dict): Tab címek és tartalom függvények dictionary-je.
                           pl: {"Tab1": tab1_function, "Tab2": tab2_function}
        key_prefix (str): Egyedi prefix a session state kulcsokhoz
                          
    Returns:
        None
    """
    is_mobile = st.session_state.get('is_mobile', False)
    
    tab_state_key = f"{key_prefix}_selected"
    
    # Inicializáljuk a session state-et, ha még nincs
    if tab_state_key not in st.session_state:
        st.session_state[tab_state_key] = list(tabs_content.keys())[0]
    
    # Mobil eszközön dropdown megjelenítés
    if is_mobile:
        selected_tab = st.selectbox(
            "Válasszon nézetet:",
            options=list(tabs_content.keys()),
            index=list(tabs_content.keys()).index(st.session_state[tab_state_key]),
            key=f"{key_prefix}_selector"
        )
        st.session_state[tab_state_key] = selected_tab
        
        # A kiválasztott tab tartalmának megjelenítése
        tabs_content[selected_tab]()
    
    # Asztali nézetben tab komponens használata
    else:
        tab_objects = st.tabs(list(tabs_content.keys()))
        
        # Minden tab tartalom renderelése a megfelelő tabban
        for i, (tab_name, tab_func) in enumerate(tabs_content.items()):
            with tab_objects[i]:
                tab_func()

def show_responsive_dataframe(df, use_container_width=True, height=None):
    """
    DataFrame megjelenítése reszponzív módon.
    Mobil eszközön egyszerűsített nézet, asztali eszközön teljes.
    
    Args:
        df (pd.DataFrame): A megjelenítendő DataFrame
        use_container_width (bool): Teljes szélesség használata
        height (int, optional): Táblázat magassága
    """
    is_mobile = st.session_state.get('is_mobile', False)
    is_tablet = st.session_state.get('is_tablet', False)
    
    if is_mobile:
        # Mobilon csak a legfontosabb oszlopokat jelenítjük meg
        if len(df.columns) > 4:
            # Adott esetben okosabb oszlop választási logikát is implementálhatunk itt
            mobile_cols = df.columns[:4]
            st.dataframe(
                df[mobile_cols], 
                use_container_width=use_container_width,
                height=height or 300
            )
            if st.checkbox("Összes oszlop megjelenítése", key="show_all_columns"):
                st.dataframe(
                    df, 
                    use_container_width=use_container_width,
                    height=height or 400
                )
        else:
            # Ha kevés oszlop van, mindent megjelenítünk
            st.dataframe(
                df, 
                use_container_width=use_container_width,
                height=height or 300
            )
    elif is_tablet:
        # Tableten kompakt nézet, de több oszloppal
        st.dataframe(
            df, 
            use_container_width=use_container_width,
            height=height or 400
        )
    else:
        # Asztali nézetben teljes megjelenítés
        st.dataframe(
            df, 
            use_container_width=use_container_width,
            height=height or 500
        )

def show_toast(message, type="info", duration=3000):
    """
    Toast üzenet megjelenítése a képernyő tetején.
    
    Args:
        message (str): Üzenet szövege
        type (str): Üzenet típusa: "info", "success", "warning", "error"
        duration (int): Megjelenés időtartama ezredmásodpercben
    """
    # Toast container létrehozása, ha még nem létezik
    if "toast_container" not in st.session_state:
        st.session_state.toast_container = True
        st.markdown("""
        <div class="toast-container"></div>
        """, unsafe_allow_html=True)
    
    # Toast ID generálása
    import uuid
    toast_id = f"toast_{uuid.uuid4().hex}"
    
    # Toast típus CSS osztálya
    toast_class = f"toast toast-{type}"
    
    # Toast HTML
    toast_html = f"""
    <div id="{toast_id}" class="{toast_class}">
        {message}
    </div>
    """
    
    # JavaScript a toast megjelenítéséhez és eltüntetéséhez
    js = f"""
    <script>
        // Toast létrehozása
        (function() {{
            const toastContainer = document.querySelector('.toast-container');
            const toastElement = document.createElement('div');
            toastElement.innerHTML = `{toast_html}`;
            
            // Toast hozzáadása a containerhez
            toastContainer.appendChild(toastElement.firstElementChild);
            
            // Toast eltüntetése a megadott idő után
            setTimeout(() => {{
                const toast = document.getElementById('{toast_id}');
                if (toast) {{
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateY(-20px)';
                    toast.style.transition = 'opacity 0.3s, transform 0.3s';
                    
                    setTimeout(() => {{
                        if (toast && toast.parentNode) {{
                            toast.parentNode.removeChild(toast);
                        }}
                    }}, 300);
                }}
            }}, {duration});
        }})();
    </script>
    """
    
    # JavaScript beszúrása
    st.markdown(js, unsafe_allow_html=True)

def render_responsive_action_bar(actions, is_compact=None):
    """
    Reszponzív akciógombsor megjelenítése.
    Mobilon egy sorban egymás alatt, nagyobb képernyőn egy sorban egymás mellett.
    
    Args:
        actions (list): Akció leírók listája, mindegyik egy dict:
                     {"label": "Gomb szövege", "key": "egyedi_kulcs", 
                      "color": "primary/secondary/success/danger", 
                      "icon": "opcionális ikon"}
        is_compact (bool, optional): Kompakt megjelenítés kényszerítése. 
                                    Ha None, a képernyőméret alapján dönti el.
    
    Returns:
        dict: A megnyomott gomb kulcsát és értékét adja vissza
    """
    if is_compact is None:
        is_compact = st.session_state.get('is_mobile', False)
    
    results = {}
    
    if is_compact:
        # Egymás alatti gombok (egy oszlop)
        for action in actions:
            label = action.get("label", "Akció")
            key = action.get("key", f"action_{label}")
            color = action.get("color", "primary")
            icon = action.get("icon", "")
            
            # A teljes címke az ikonnal
            full_label = f"{icon} {label}" if icon else label
            
            # Megfelelő gomb típus kiválasztása a szín alapján
            if color == "primary":
                results[key] = st.button(full_label, key=key, type="primary", use_container_width=True)
            elif color == "secondary":
                results[key] = st.button(full_label, key=key, type="secondary", use_container_width=True)
            else:
                results[key] = st.button(full_label, key=key, use_container_width=True)
    else:
        # Egymás melletti gombok (több oszlop)
        cols = st.columns(len(actions))
        
        for i, action in enumerate(actions):
            label = action.get("label", "Akció")
            key = action.get("key", f"action_{label}")
            color = action.get("color", "primary")
            icon = action.get("icon", "")
            
            # A teljes címke az ikonnal
            full_label = f"{icon} {label}" if icon else label
            
            with cols[i]:
                # Megfelelő gomb típus kiválasztása a szín alapján
                if color == "primary":
                    results[key] = st.button(full_label, key=key, type="primary", use_container_width=True)
                elif color == "secondary":
                    results[key] = st.button(full_label, key=key, type="secondary", use_container_width=True)
                else:
                    results[key] = st.button(full_label, key=key, use_container_width=True)
    
    return results

def render_section_card(title, content, color="#1976D2", icon=None, key=None, expanded=True, is_mobile=None):
    """
    Szekció kártya megjelenítése bővíthető/összehúzható tartalommal.
    
    Args:
        title (str): Kártya címe
        content (callable): Függvény, amely a kártya tartalmát jeleníti meg
        color (str): Kártya fejléc színe
        icon (str, optional): Opcionális ikon a címben
        key (str, optional): Egyedi kulcs a session state-hez
        expanded (bool): Alapértelmezetten kinyitott
        is_mobile (bool, optional): Mobilnézet használata. Ha None, automatikus detektálás.
    """
    if is_mobile is None:
        is_mobile = st.session_state.get('is_mobile', False)
    
    # Session state kulcs a kinyitott/bezárt állapothoz
    expand_key = f"{key}_expanded" if key else f"section_expanded_{title}"
    
    # Inicializálás, ha még nem létezik
    if expand_key not in st.session_state:
        st.session_state[expand_key] = expanded
    
    # Egyedi divider szín a kártyához
    colors = get_theme_colors()
    
    # Kártya fejléc CSS osztályok (mobilon kisebb padding)
    header_padding = "10px 15px" if is_mobile else "15px 20px"
    
    # Create the JavaScript as a raw string to avoid f-string escaping issues
    js_function = """
    function toggleSection(key) {
        // JavaScript az expand állapot váltásához
        const content = document.getElementById('content-' + key);
        if (content.style.display === 'none') {
            content.style.display = '';
            // Streamlit session state módosítása
            const data = {};
            data[key] = true;
            window.parent.postMessage({
                type: "streamlit:setSessionState",
                data: data
            }, "*");
        } else {
            content.style.display = 'none';
            // Streamlit session state módosítása
            const data = {};
            data[key] = false;
            window.parent.postMessage({
                type: "streamlit:setSessionState",
                data: data
            }, "*");
        }
    }
    """
    
    # Kártya HTML with separate JavaScript part to avoid f-string issues
    st.markdown(f"""
    <div class="responsive-card" style="border-top: 3px solid {color}; margin-bottom: 20px;">
        <div class="responsive-card-header" style="padding: {header_padding};">
            <div class="responsive-card-title" style="color: {color};">
                {icon or ""} {title}
            </div>
            <div>
                <button 
                    onclick="toggleSection('{expand_key}')" 
                    style="background: none; border: none; cursor: pointer; font-size: 1.2em; color: {colors['text_color']};"
                >
                    {("➖" if st.session_state[expand_key] else "➕")}
                </button>
            </div>
        </div>
        <div id="content-{expand_key}" style="display: {'' if st.session_state[expand_key] else 'none'};">
            <div class="responsive-card-content" style="padding: 0 15px 15px 15px;">
                <!-- Itt lesz a tartalom -->
            </div>
        </div>
    </div>
    
    <script>
    {js_function}
    </script>
    """, unsafe_allow_html=True)
    
    # Ha a kártya kinyitott állapotban van, megjeleníti a tartalmát
    if st.session_state[expand_key]:
        content()  # A kapott függvény meghívása a tartalom megjelenítéséhez

def inject_screen_detection():
    """
    Képernyőméret érzékelés JavaScript kódjának beillesztése.
    A képernyő méretét a session state-ben tárolja.
    """
    st.markdown("""
    <script>
    // Képernyőméret érzékelés
    function detectScreenSize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const isMobile = width < 768;
        const isTablet = width >= 768 && width < 992;
        
        // Adatok küldése a Streamlit session state-be
        window.parent.postMessage({
            type: "streamlit:setSessionState",
            data: {
                screen_width: width,
                screen_height: height,
                is_mobile: isMobile,
                is_tablet: isTablet
            }
        }, "*");
        
        console.log("Screen detection:", {width, height, isMobile, isTablet});
    }
    
    // Futtatás betöltéskor és ablak átméretezéskor
    detectScreenSize();
    window.addEventListener('resize', detectScreenSize);
    </script>
    """, unsafe_allow_html=True)
    
    # Alapértelmezett értékek, ha még nem léteznek
    if "screen_width" not in st.session_state:
        st.session_state.screen_width = 1200
        
    if "screen_height" not in st.session_state:
        st.session_state.screen_height = 800
        
    if "is_mobile" not in st.session_state:
        st.session_state.is_mobile = False
        
    if "is_tablet" not in st.session_state:
        st.session_state.is_tablet = False