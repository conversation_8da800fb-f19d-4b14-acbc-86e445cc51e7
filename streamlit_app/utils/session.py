"""
Streamlit munkamenet kez<PERSON><PERSON>i funkciók.
"""
import streamlit as st
import app_config as config
import uuid
import time
import logging
import os
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Debug: <PERSON><PERSON><PERSON><PERSON><PERSON> a config modul tartalmát
print(f"Session - Config module: {dir(config)}")
print(f"Session - Config SESSION_VARS: {getattr(config, 'SESSION_VARS', 'Not found')}")

# Default session variables
DEFAULT_SESSION_VARS = {
    "user": "user",
    "token": "auth_token",  # Changed to match code conventions elsewhere
    "authenticated": "authenticated",
}

# Additional session keys we'll use directly with session_state
SESSION_ACTIVITY_KEY = "last_activity"
SESSION_TOKEN_EXPIRY_KEY = "token_expiry"

# Session timeout in seconds (30 minutes)
SESSION_TIMEOUT = 1800

try:
    # Try to import from fixed_imports first
    from streamlit_app.pages.operator.offer_management.fixed_imports import is_authenticated as _is_authenticated
    from streamlit_app.pages.operator.offer_management.fixed_imports import get_current_user as _get_current_user
    logger.info("Using authentication functions from fixed_imports")
except ImportError:
    try:
        # Try relative import
        from pages.operator.offer_management.fixed_imports import is_authenticated as _is_authenticated
        from pages.operator.offer_management.fixed_imports import get_current_user as _get_current_user
        logger.info("Using authentication functions from relative fixed_imports")
    except ImportError:
        # Fallback implementation
        logger.warning("Using fallback implementation for authentication functions")
        def _is_authenticated():
            """Fallback implementation for checking if a user is authenticated"""
            return True
            
        def _get_current_user():
            """Fallback implementation for getting the current user"""
            return {
                "id": 1,
                "username": "operator",
                "role": "ügyintéző",  # Note: Using Hungarian role name as in the code
                "name": "Test Operator",
                "permissions": ["view_offers", "edit_offers", "change_status"]
            }

def init_session_state():
    """
    Munkamenet állapot inicializálása
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Initialize session state variables if they don't exist
    # DON'T overwrite existing values, only initialize missing keys
    for key, default_key in session_vars.items():
        if default_key not in st.session_state:
            st.session_state[default_key] = None
    
    # Initialize token expiry if it doesn't exist
    if SESSION_TOKEN_EXPIRY_KEY not in st.session_state:
        st.session_state[SESSION_TOKEN_EXPIRY_KEY] = None
    
    # Initialize page_uuid if it doesn't exist
    if "page_uuid" not in st.session_state:
        st.session_state.page_uuid = str(uuid.uuid4())

def check_session_timeout():
    """Check if the session has timed out and log out if necessary"""
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    
    # Skip if not authenticated
    if not st.session_state.get(auth_key, False):
        return
    
    current_time = time.time()
    last_activity = st.session_state.get(SESSION_ACTIVITY_KEY, current_time)
    
    # If session has timed out
    if current_time - last_activity > SESSION_TIMEOUT:
        logger.info("Session timed out, logging out user")
        clear_session()
        # Show message on next page load
        st.session_state.show_timeout_message = True
    else:
        # Update last activity time
        st.session_state[SESSION_ACTIVITY_KEY] = current_time

def set_user_session(user_data, token):
    """
    Felhasználói munkamenet beállítása
    
    Args:
        user_data (dict): Felhasználói adatok
        token (str): JWT token
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Store user data and token
    st.session_state[session_vars.get("user", "user")] = user_data
    st.session_state[session_vars.get("token", "auth_token")] = token
    st.session_state[session_vars.get("authenticated", "authenticated")] = True
    
    # Set token expiry
    if token:
        expiry = time.time() + (60 * 60)  # 1 hour from now
        st.session_state[SESSION_TOKEN_EXPIRY_KEY] = expiry

def clear_session():
    """
    Munkamenet törlése
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Clear all session variables
    for key, default_key in session_vars.items():
        if default_key in st.session_state:
            del st.session_state[default_key]
    
    # Clear token expiry
    if SESSION_TOKEN_EXPIRY_KEY in st.session_state:
        del st.session_state[SESSION_TOKEN_EXPIRY_KEY]
    
    # Clear additional session flags
    if "just_logged_in" in st.session_state:
        del st.session_state["just_logged_in"]

def update_activity():
    """Update the last activity timestamp to prevent timeouts"""
    st.session_state[SESSION_ACTIVITY_KEY] = time.time()

def get_auth_token():
    """
    Visszaadja a jelenlegi autentikációs tokent.
    
    Returns:
        str: JWT token vagy None, ha nincs bejelentkezve a felhasználó
    """
    print(f"==== GET AUTH TOKEN ====")
    
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    token_key = session_vars.get("token", "auth_token")
    print(f"Token key: {token_key}")
    
    # Check if token exists
    token = st.session_state.get(token_key)
    print(f"Token found: {bool(token)}")
    if token:
        print(f"Token value: {token[:10]}...")
        # Csak session token esetén ellenőrizd a lejáratot!
        expiry = st.session_state.get(SESSION_TOKEN_EXPIRY_KEY)
        if expiry and time.time() > expiry:
            print("Token expired")
            clear_session()
            return None
        print("============================")
        return token
    # Ha nincs session token, próbáljuk környezeti változóból (lejárat ellenőrzés NINCS, az API-ra bízzuk)
    env_token = os.environ.get("API_AUTH_TOKEN")
    print(f"Env token found: {bool(env_token)}")
    if env_token:
        print(f"Env token value: {env_token[:10]}...")
        print("============================")
        return env_token
    print("============================")
    return None

def get_current_user():
    """
    Visszaadja a bejelentkezett felhasználó adatait.
    
    Returns:
        dict: A felhasználó adatai vagy None, ha nincs bejelentkezve
    """
    # First try the normal session-based user data
    try:
        # Get session variables with fallback
        session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
        user_key = session_vars.get("user", "user")
        
        # Update activity
        update_activity()
        
        # Check authentication
        if not is_authenticated():
            return None
        
        user_data = st.session_state.get(user_key)
        if user_data:
            return user_data
            
        # If no user data is found, fall back to the fixed implementation for development
        logger.warning("No user data found, falling back to fixed implementation")
        return _get_current_user()
    except Exception as e:
        # If any error occurs, fall back to the fixed implementation for development
        logger.error(f"Error in get_current_user: {str(e)}, falling back to fixed implementation")
        return _get_current_user()

def is_authenticated():
    """
    Ellenőrzi, hogy a felhasználó be van-e jelentkezve.
    
    Returns:
        bool: True, ha a felhasználó be van jelentkezve, egyébként False
    """
    # First try the normal session-based authentication
    try:
        # Get session variables with fallback
        session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
        auth_key = session_vars.get("authenticated", "authenticated")
        token_key = session_vars.get("token", "auth_token")
        
        # Check if authentication flag is set
        auth_flag = st.session_state.get(auth_key, False)
        token_value = st.session_state.get(token_key)
        
# Debug output removed for production
        
        # If auth flag is set, make sure we also have a token
        if auth_flag and not token_value:
            logger.warning("Authentication flag set but no token found, clearing session")
# Debug output removed for production
            # Don't clear session immediately, check for environment token first
            env_token = get_auth_token()  # This checks environment variables too
            if not env_token:
                clear_session()
                return False
        
        # Check token expiry if available
        if auth_flag and st.session_state.get(SESSION_TOKEN_EXPIRY_KEY):
            if time.time() > st.session_state.get(SESSION_TOKEN_EXPIRY_KEY):
                logger.warning("Token expired, clearing session")
                clear_session()
                return False
        
        # Check if token exists
        token = get_auth_token()
        if not token:
            # If no token is found, check if we still have authentication flag set
            if auth_flag:
                logger.warning("Authentication flag set but no token found, clearing session")
                clear_session()
                return False
            # If no token is found, fall back to the fixed implementation for development
            logger.warning("No token found, falling back to fixed implementation")
            return _is_authenticated()
            
        # If we get here with auth_flag set and token exists, user is authenticated
        return auth_flag
    except Exception as e:
        # If any error occurs, fall back to the fixed implementation for development
        logger.error(f"Error in is_authenticated: {str(e)}, falling back to fixed implementation")
        return _is_authenticated()

def get_user_role():
    """
    Visszaadja a bejelentkezett felhasználó szerepkörét.
    
    Returns:
        str: A felhasználó szerepköre vagy None, ha nincs bejelentkezve
    """
    user = get_current_user()
    if user:
        return user.get("role")
    return None

def has_role(required_roles):
    """
    Ellenőrzi, hogy a felhasználó rendelkezik-e a megadott szerepkörrel.
    
    Args:
        required_roles (str or list): A szükséges szerepkör vagy szerepkörök listája
    
    Returns:
        bool: True, ha a felhasználó rendelkezik a szükséges szerepkörrel, egyébként False
    """
    if not is_authenticated():
        return False
    
    user_role = get_user_role()
    if not user_role:
        return False
    
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    return user_role in required_roles
