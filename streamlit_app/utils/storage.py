"""
Storage utilities for handling saved forms.
"""
import os
import json
import time
from datetime import datetime

# Mentett űrlapok könyvtára
SAVE_DIR = "saved_forms"

# Könyvt<PERSON><PERSON>, ha nem létezik
if not os.path.exists(SAVE_DIR):
    os.makedirs(SAVE_DIR)

def get_saved_forms():
    """
    Lek<PERSON>ri a mentett űrlapokat.
    
    Returns:
        dict: Mentett űrlapok adatai
    """
    saved_forms = {}
    
    # Mentett űrlapok listázása
    for filename in os.listdir(SAVE_DIR):
        if filename.endswith(".json"):
            form_id = filename[:-5]  # .json kiterjesztés eltávolítása
            
            # Űrlap betöltése
            form_path = os.path.join(SAVE_DIR, filename)
            
            try:
                with open(form_path, "r", encoding="utf-8") as f:
                    form_data = json.load(f)
                
                # Űrlap adatok hozzáadása
                saved_forms[form_id] = {
                    "name": form_data.get("name", "Névtelen űrlap"),
                    "last_modified": datetime.fromtimestamp(os.path.getmtime(form_path)).strftime("%Y-%m-%d %H:%M:%S"),
                    "current_step": form_data.get("current_step", 0),
                    "total_steps": form_data.get("total_steps", 0)
                }
            except Exception as e:
                print(f"Hiba az űrlap betöltésekor: {form_id}, {e}")
    
    return saved_forms

def save_form(form_id, form_data, name=None):
    """
    Ment egy űrlapot.
    
    Args:
        form_id (str): Űrlap azonosítója
        form_data (dict): Űrlap adatai
        name (str, optional): Űrlap neve. Defaults to None.
    
    Returns:
        bool: Sikeres mentés esetén True, egyébként False
    """
    try:
        # Űrlap adatok előkészítése
        save_data = {
            "name": name or "Névtelen űrlap",
            "current_step": form_data.get("current_step", 0),
            "total_steps": form_data.get("total_steps", 0),
            "data": form_data
        }
        
        # Űrlap mentése
        form_path = os.path.join(SAVE_DIR, f"{form_id}.json")
        
        with open(form_path, "w", encoding="utf-8") as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"Hiba az űrlap mentésekor: {form_id}, {e}")
        return False

def load_form(form_id):
    """
    Betölt egy mentett űrlapot.
    
    Args:
        form_id (str): Űrlap azonosítója
    
    Returns:
        dict: Űrlap adatai, hiba esetén None
    """
    try:
        # Űrlap betöltése
        form_path = os.path.join(SAVE_DIR, f"{form_id}.json")
        
        if not os.path.exists(form_path):
            return None
        
        with open(form_path, "r", encoding="utf-8") as f:
            form_data = json.load(f)
        
        return form_data.get("data", {})
    except Exception as e:
        print(f"Hiba az űrlap betöltésekor: {form_id}, {e}")
        return None

def delete_form(form_id):
    """
    Töröl egy mentett űrlapot.
    
    Args:
        form_id (str): Űrlap azonosítója
    
    Returns:
        bool: Sikeres törlés esetén True, egyébként False
    """
    try:
        # Űrlap törlése
        form_path = os.path.join(SAVE_DIR, f"{form_id}.json")
        
        if os.path.exists(form_path):
            os.remove(form_path)
            return True
        
        return False
    except Exception as e:
        print(f"Hiba az űrlap törlésekor: {form_id}, {e}")
        return False 