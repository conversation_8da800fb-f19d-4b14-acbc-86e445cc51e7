#!/bin/bash

# Szkript a szimbolikus linkek létrehozásához a Streamlit oldalakhoz
# Ez a szkript a helyi környezetben fog futni

# Debug: Kiírjuk a könyvtár tartalmát
echo "Current directory: $(pwd)"
echo "Directory contents:"
ls -la

# Auth oldalak
for file in pages/auth/*.py; do
  filename=$(basename "$file")
  # Csak a Python fájlokat linkeljük, az __init__.py-t kihagyjuk
  if [[ "$filename" != "__init__.py" && "$filename" != "__pycache__" ]]; then
    ln -sf "auth/$filename" "pages/auth_$filename"
    echo "Link létrehozva: pages/auth_$filename -> auth/$filename"
  fi
done

# Admin oldalak
for file in pages/admin/*.py; do
  filename=$(basename "$file")
  if [[ "$filename" != "__init__.py" && "$filename" != "__pycache__" ]]; then
    ln -sf "admin/$filename" "pages/admin_$filename"
    echo "Link létrehozva: pages/admin_$filename -> admin/$filename"
  fi
done

# Operator oldalak
for file in pages/operator/*.py; do
  filename=$(basename "$file")
  if [[ "$filename" != "__init__.py" && "$filename" != "__pycache__" ]]; then
    ln -sf "operator/$filename" "pages/operator_$filename"
    echo "Link létrehozva: pages/operator_$filename -> operator/$filename"
  fi
done

# Producer oldalak
for file in pages/producer/*.py; do
  filename=$(basename "$file")
  if [[ "$filename" != "__init__.py" && "$filename" != "__pycache__" ]]; then
    ln -sf "producer/$filename" "pages/producer_$filename"
    echo "Link létrehozva: pages/producer_$filename -> producer/$filename"
  fi
done

# Components könyvtár linkelése
echo "Components directory contents:"
ls -la components/

# Utils könyvtár linkelése
echo "Utils directory contents:"
ls -la utils/

echo "Szimbolikus linkek létrehozása befejeződött!"
