# Fő alkalmazás belépési pont
"""
POM APP - Streamlit Frontend
Fő alkalmazás belépési pont
"""
import streamlit as st
import app_config as config
import sys
from pathlib import Path

# Add streamlit_app to Python path to ensure components can be found
current_file = Path(__file__)
app_root = current_file.parent
if app_root not in sys.path:
    sys.path.insert(0, str(app_root))

# Import the original sidebar component
from components.sidebar import render_sidebar

# R<PERSON>lmas import a session utils-hoz
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import init_session_state, DEFAULT_SESSION_VARS, is_authenticated, get_current_user, clear_session
    # Napl<PERSON><PERSON><PERSON> hozz<PERSON>ad<PERSON>a a hibakeresés megkönnyítéséhez
    import logging
    logging.info("Main.py: Successfully imported session utils from Docker path")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import init_session_state, DEFAULT_SESSION_VARS, is_authenticated, get_current_user, clear_session
        import logging
        logging.info("Main.py: Successfully imported session utils directly")
    except ImportError:
        try:
            # Teljes útvonal (streamlit_app-ból)
            from streamlit_app.utils.session import init_session_state, DEFAULT_SESSION_VARS, is_authenticated, get_current_user, clear_session
            import logging
            logging.info("Main.py: Successfully imported session utils from streamlit_app path")
        except ImportError:
            # Fallback for session utilities if import fails
            import logging
            logging.warning("Main.py: Could not import session utilities, using fallbacks")
            
            # Init session state fallback
            DEFAULT_SESSION_VARS = {"user": None, "token": None}
            
            def init_session_state():
                """Fallback session state initializer"""
                for key, value in DEFAULT_SESSION_VARS.items():
                    if key not in st.session_state:
                        st.session_state[key] = value
                return st.session_state
            
            def is_authenticated():
                """Fallback authentication check"""
                return "user" in st.session_state and st.session_state["user"] is not None
            
            def get_current_user():
                """Fallback current user"""
                return st.session_state.get("user", {"id": 1, "name": "Test User", "role": "operator", "email": "<EMAIL>"})
            
            def clear_session():
                """Fallback session clear"""
                for key in DEFAULT_SESSION_VARS.keys():
                    if key in st.session_state:
                        del st.session_state[key]

from pages.auth.login import show_login
from pages.producer.dashboard import show_producer_dashboard
from pages.operator.dashboard import show_operator_dashboard
from pages.admin.dashboard import show_admin_dashboard

# Debug: Kiírjuk a config modul tartalmát
print(f"Main - Config module: {dir(config)}")
print(f"Main - API_BASE_URL: {getattr(config, 'API_BASE_URL', 'Not found')}")

# Oldal konfigurálása
st.set_page_config(
    page_title=config.APP_NAME,
    page_icon="🌱",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Munkamenet inicializálása
init_session_state()

# Oldalsáv renderelése
render_sidebar()

def main():
    # Debug: Kiírjuk a session state tartalmát
    print(f"Session state: {st.session_state}")
    
    # Bejelentkezés ellenőrzése
    if not is_authenticated():
        show_login()
    else:
        # Bejelentkezett felhasználó adatainak lekérése
        user = get_current_user()
        role = user.get("role", "").lower()
        
        # Üdvözlő üzenet
        st.success(f"Üdvözöljük, {user.get('name', user.get('email', 'Felhasználó'))}!")
        
        # Szerepkör alapján a megfelelő irányítópult megjelenítése
        if role == "termelő":
            show_producer_dashboard()
        elif role == "ügyintéző":
            show_operator_dashboard()
        elif role == "admin":
            show_admin_dashboard()
        else:
            st.warning(f"Ismeretlen felhasználói szerepkör: {role}")
            st.write("Kérjük, jelentkezzen ki és próbáljon újra bejelentkezni.")
            
            if st.button("Kijelentkezés"):
                clear_session()
                st.rerun()  # Oldal újratöltése

if __name__ == "__main__":
    main()
