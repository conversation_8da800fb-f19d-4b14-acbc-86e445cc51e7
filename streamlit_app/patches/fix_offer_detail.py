#!/usr/bin/env python3
"""
Javítás az "Érvénytelen ajánlat azonosító: 2" hibára.

Ez a patch script két fő problémát old meg:

1. Az offer_id típuskonverziój<PERSON><PERSON> kap<PERSON>olatos hibát javítja az API hívásokban.
2. Javítja a hibakezelést, hogy jobban kezeljük az elérhetetlen vagy hiányzó ajánlatokat.

Használata:
    python3 patches/fix_offer_detail.py
"""

import os
import sys
import logging
import shutil
from datetime import datetime

# Alap könyvtár és logging beállítása
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger('patch_offers')

def backup_file(file_path):
    """Biztonsági másolatot készít egy fájlról."""
    if not os.path.exists(file_path):
        logger.error(f"A fájl nem létezik: {file_path}")
        return False
    
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    backup_path = f"{file_path}.bak.{timestamp}"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Biztonsági másolat létrehozva: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Hiba a biztonsági másolat készítésekor: {str(e)}")
        return False

def patch_get_offer_function():
    """Kijavítja a get_offer függvényt az offers.py API fájlban."""
    offers_api_path = os.path.join(BASE_DIR, 'api', 'offers.py')
    offers_fix_path = os.path.join(BASE_DIR, 'api', 'offers_fix_minimal.py')
    
    # Ellenőrizzük, hogy a javított fájl létezik-e
    if not os.path.exists(offers_fix_path):
        logger.error(f"A javított fájl nem található: {offers_fix_path}")
        return False
    
    # Biztonsági másolat készítése
    if not backup_file(offers_api_path):
        logger.error("Nem sikerült biztonsági másolatot készíteni, a javítás megszakítva.")
        return False
    
    # Beolvassuk a javított függvényt
    with open(offers_fix_path, 'r', encoding='utf-8') as fix_file:
        fixed_content = fix_file.read()
    
    # Beolvassuk az eredeti fájlt
    with open(offers_api_path, 'r', encoding='utf-8') as api_file:
        api_content = api_file.read()
    
    # Keressük meg a get_offer függvényt az eredeti fájlban
    start_marker = "def get_offer(offer_id):"
    end_marker_patterns = ["def create_offer(", "def get_offers_paginated(", "def create_offer_for_user("]
    
    start_pos = api_content.find(start_marker)
    if start_pos == -1:
        logger.error(f"A get_offer függvény nem található a fájlban: {offers_api_path}")
        return False
    
    # Keressük meg a függvény végét
    end_pos = -1
    for pattern in end_marker_patterns:
        pos = api_content.find(pattern, start_pos)
        if pos != -1 and (end_pos == -1 or pos < end_pos):
            end_pos = pos
    
    if end_pos == -1:
        logger.error("Nem sikerült meghatározni a get_offer függvény végét.")
        return False
    
    # Keressük meg a javított get_offer függvényt
    fixed_start_pos = fixed_content.find(start_marker)
    fixed_end_pos = -1
    for pattern in end_marker_patterns + ["def get_auth_headers():"]:
        pos = fixed_content.find(pattern, fixed_start_pos)
        if pos != -1 and (fixed_end_pos == -1 or pos < fixed_end_pos):
            fixed_end_pos = pos
    
    if fixed_start_pos == -1 or fixed_end_pos == -1:
        logger.error("Nem sikerült kinyerni a javított get_offer függvényt.")
        return False
    
    # Kinyerjük a javított függvényt
    fixed_function = fixed_content[fixed_start_pos:fixed_end_pos].strip()
    
    # Helyettesítjük a régi függvényt az újjal
    new_content = api_content[:start_pos] + fixed_function + "\n\n" + api_content[end_pos:]
    
    # Kiírjuk a frissített fájlt
    try:
        with open(offers_api_path, 'w', encoding='utf-8') as api_file:
            api_file.write(new_content)
        logger.info(f"A get_offer függvény sikeresen javítva: {offers_api_path}")
        return True
    except Exception as e:
        logger.error(f"Hiba a fájl írásakor: {str(e)}")
        return False

def patch_show_offer_detail_function():
    """Javítja a show_offer_detail függvényt az offer_management.py fájlban."""
    # Itt a másodlagos javítás, ami a show_offer_detail függvény ID kezelését és hibakezelését javítja
    management_path = os.path.join(BASE_DIR, 'pages', 'operator', 'offer_management.py')
    
    # Ellenőrizzük, hogy a fájl létezik-e
    if not os.path.exists(management_path):
        logger.error(f"A fájl nem található: {management_path}")
        return False
    
    # Biztonsági másolat készítése
    if not backup_file(management_path):
        logger.error("Nem sikerült biztonsági másolatot készíteni, a javítás megszakítva.")
        return False
    
    # Beolvassuk az eredeti fájlt
    with open(management_path, 'r', encoding='utf-8') as mgmt_file:
        mgmt_content = mgmt_file.read()
    
    # Csak az ID validation részt javítjuk
    id_validation_old = """    # Biztosítjuk, hogy az offer_id megfelelő típusú legyen
    try:
        # Ha string, akkor megpróbáljuk konvertálni int-té
        if isinstance(offer_id, str):
            if offer_id.isdigit():
                offer_id = int(offer_id)
            else:
                logger.error(f"Non-numeric offer ID string: {offer_id}")
                st.error(f"Érvénytelen ajánlat azonosító formátum: {offer_id}")
                return
        # Ha nem integer vagy nem tudtuk konvertálni
        elif not isinstance(offer_id, int):
            logger.error(f"Invalid offer ID type: {type(offer_id)}")
            st.error(f"Érvénytelen ajánlat azonosító típus: {offer_id}")
            return
            
        logger.info(f"Processing offer ID: {offer_id} (type: {type(offer_id)})")
    except Exception as e:
        logger.error(f"Exception during offer ID validation: {str(e)}")
        st.error(f"Hiba az ajánlat azonosító feldolgozásakor: {offer_id}")
        return"""
    
    id_validation_new = """    # Rugalmasabb ID kezelés
    try:
        # Bármilyen típusú ID-t elfogadunk, az API kezelni fogja
        # Ezt a részletesebb ellenőrzést a get_offer függvény végzi
        logger.info(f"Processing offer ID: {offer_id} (type: {type(offer_id)})")
    except Exception as e:
        logger.error(f"Exception during offer ID validation: {str(e)}")
        st.error(f"Hiba az ajánlat azonosító feldolgozásakor: {offer_id}")
        return"""
    
    # Helyettesítjük a régi ellenőrzést az újjal
    if id_validation_old in mgmt_content:
        new_content = mgmt_content.replace(id_validation_old, id_validation_new)
        
        # Kiírjuk a frissített fájlt
        try:
            with open(management_path, 'w', encoding='utf-8') as mgmt_file:
                mgmt_file.write(new_content)
            logger.info(f"A show_offer_detail függvény ID-kezelése sikeresen javítva: {management_path}")
            return True
        except Exception as e:
            logger.error(f"Hiba a fájl írásakor: {str(e)}")
            return False
    else:
        logger.warning(f"A javítandó ID-kezelési kód nem található a fájlban. A fájl valószínűleg már javítva lett.")
        return False

def main():
    """A fő javítási folyamat végrehajtása."""
    logger.info("Ajánlat részletek megjelenítési hiba javítása megkezdve...")
    
    api_fixed = patch_get_offer_function()
    ui_fixed = patch_show_offer_detail_function()
    
    if api_fixed and ui_fixed:
        logger.info("A javítás sikeresen elvégezve mindkét fájlon!")
        print("\n✅ JAVÍTÁS SIKERES: Az ajánlat részletek megjelenítési hiba javítva.\n")
        print("A hiba oka az volt, hogy a get_offer API függvény túl szigorúan ellenőrizte")
        print("az ajánlat azonosító típusát, és a hibakezelés nem volt elég robusztus a különböző")
        print("típusú azonosítók kezelésére.")
        print("\nA javítás rugalmassá tette az azonosító kezelését, és javította a hibakezelést.")
        return 0
    else:
        logger.error("A javítás részben vagy teljesen sikertelen volt.")
        print("\n❌ JAVÍTÁS SIKERTELEN: Kérjük, nézze meg a logokat a részletekért.\n")
        return 1

if __name__ == "__main__":
    sys.exit(main())