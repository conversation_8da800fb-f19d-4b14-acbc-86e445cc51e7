#!/bin/bash
# Sz<PERSON><PERSON>t az "import config" lecseréléséhez "import app_config as config"-ra

# 1. Lecser<PERSON><PERSON>j<PERSON><PERSON> a sima "import config" sorokat
find . -type f -name "*.py" -exec sed -i 's/^import config$/import app_config as config/g' {} \;

# 2. Keressük az "import config" sort, amik után esetleg megjegyzés vagy egyéb tartalom lehet
find . -type f -name "*.py" -exec sed -i 's/^import config /import app_config as config /g' {} \;

# 3. <PERSON>ressük azokat a helyeket, ahol "import config" van egy sor közepén vagy végén
find . -type f -name "*.py" -exec sed -i 's/ import config$/ import app_config as config/g' {} \;
find . -type f -name "*.py" -exec sed -i 's/ import config / import app_config as config /g' {} \;

# 4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> "from ... import config" formát
find . -type f -name "*.py" -exec sed -i 's/from .* import config/import app_config as config/g' {} \;

echo "Importok frissítése befejeződött." 