# Adatmegjelenítő komponensek
"""
Adatmegjelenítő komponensek.
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from utils.formatting import format_date, format_price, format_quantity, format_status, get_status_color, format_datetime, format_number, format_currency
from utils.config import get_offer_statuses
from api import users as users_api
from api import products as products_api
import uuid
from utils.session import init_session_state
import app_config as config
from datetime import datetime

# Modify the import section to avoid circular imports
# We'll define wrapper functions that import within the function body instead of at module level

def display_offer_status_card(offer):
    """Wrapper for display_offer_status_card from display_data_components"""
    from components.display_data_components import display_offer_status_card as _display_offer_status_card
    return _display_offer_status_card(offer)

def display_offer_detail_cards(offer, is_mobile=False):
    """Wrapper for display_offer_detail_cards from display_data_components"""
    from components.display_data_components import display_offer_detail_cards as _display_offer_detail_cards
    return _display_offer_detail_cards(offer, is_mobile)

def display_offer_details(offer_id):
    """Wrapper for display_offer_details from display_data_components"""
    from components.display_data_components import display_offer_details as _display_offer_details
    return _display_offer_details(offer_id)

def display_offer_attachments(offer_id):
    """Wrapper for display_offer_attachments from display_data_components"""
    from components.display_data_components import display_offer_attachments as _display_offer_attachments
    return _display_offer_attachments(offer_id)

def display_offer_timeline(offer):
    """Wrapper for display_offer_timeline from display_data_components"""
    from components.display_data_components import display_offer_timeline as _display_offer_timeline
    return _display_offer_timeline(offer)

def display_quantity_indicator(offer):
    """Wrapper for display_quantity_indicator from display_data_components"""
    from components.display_data_components import display_quantity_indicator as _display_quantity_indicator
    return _display_quantity_indicator(offer)

def display_status_history(offer_id):
    """Wrapper for display_status_history from display_data_components"""
    from components.display_data_components import display_status_history as _display_status_history
    return _display_status_history(offer_id)

def display_collapsible_details(offer):
    """Wrapper for display_collapsible_details from display_data_components"""
    from components.display_data_components import display_collapsible_details as _display_collapsible_details
    return _display_collapsible_details(offer)

try:
    from st_aggrid import AgGrid, GridOptionsBuilder, JsCode
    AGGRID_AVAILABLE = True
except ImportError:
    AGGRID_AVAILABLE = False

# Cache beállítása az API hívásokhoz
@st.cache_data(ttl=300)  # 5 perces cache
def fetch_users():
    """
    Felhasználók lekérdezése cache-el.
    
    Returns:
        dict: Felhasználók szótára ID alapján
    """
    success_users, result_users = users_api.get_users()
    users_dict = {}
    if success_users:
        for user in result_users:
            users_dict[user.get("id")] = user
    return users_dict

@st.cache_data(ttl=300)  # 5 perces cache
def fetch_products():
    """
    Termékek lekérdezése cache-el.
    
    Returns:
        dict: Termékek szótára ID alapján
    """
    success_products, result_products = products_api.get_product_types()
    products_dict = {}
    if success_products:
        for product in result_products:
            products_dict[product.get("id")] = product
    return products_dict

@st.cache_data(ttl=300)  # 5 perces cache
def fetch_categories():
    """
    Kategóriák lekérdezése cache-el.
    
    Returns:
        dict: Kategóriák szótára ID alapján
    """
    success_categories, result_categories = products_api.get_product_categories()
    categories_dict = {}
    if success_categories:
        for category in result_categories:
            categories_dict[category.get("id")] = category
    return categories_dict

@st.cache_data(ttl=300)  # 5 perces cache
def fetch_quality_grades(product_id):
    """
    Minőségi besorolások lekérdezése cache-el.
    
    Args:
        product_id (int): Termék azonosító
        
    Returns:
        dict: Minőségi besorolások szótára ID alapján
    """
    success_grades, result_grades = products_api.get_quality_grades(product_type_id=product_id)
    grades_dict = {}
    if success_grades:
        for grade in result_grades:
            grades_dict[grade.get("id")] = grade
    return grades_dict

def is_light(hex_color):
    """
    Eldönti, hogy a megadott hex szín világos-e.
    Args:
        hex_color (str): Hex színkód (#RRGGBB)
    Returns:
        bool: True, ha világos, False ha sötét
    """
    hex_color = str(hex_color).lstrip('#')
    if len(hex_color) != 6:
        return False
    try:
        r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    except Exception:
        return False
    brightness = (r*299 + g*587 + b*114) / 1000
    return brightness > 180

def display_offer_table(offers, with_actions=False, pagination=True):
    """
    Ajánlatok megjelenítése táblázatban.

    Megjelenő oszlopok és sorrend:
        - ID
        - Beszállítás dátuma
        - Termék
        - Mennyiség
        - Visszaigazolt mennyiség
        - Visszaigazolt ár
        - Státusz
    A "Termelő" oszlop nem jelenik meg. Ha nincs visszaigazolt mennyiség vagy ár, akkor is szerepel a fejlécben üres értékkel.

    Args:
        offers (list): Ajánlatok listája
        with_actions (bool): Műveletek megjelenítése
        pagination (bool): Lapozás engedélyezése (alapértelmezetten True)

    Returns:
        pd.DataFrame: Táblázat objektum
    """
    if not offers:
        st.info("Nincsenek megjeleníthető ajánlatok.")
        return None
    
    # Adatok előkészítése
    data = []
    for offer in offers:
        # Alapvető adatok
        row = {
            "ID": offer.get("id"),
            "Státusz": format_status(offer.get("status", "")),
            "Beszállítás dátuma": format_date(offer.get("delivery_date", "")),
            "Termék": offer.get("product_type", {}).get("name", "Ismeretlen termék"),
            "Mennyiség": format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)), offer.get('quantity_unit', 'kg'))
        }
        
        # Termelő neve
        user = offer.get("user", {})
        row["Termelő"] = user.get("company_name", user.get("contact_name", "Ismeretlen termelő"))
        
        # Visszaigazolt adatok (ha vannak)
        if offer.get("status") in ["CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]:
            row["Visszaigazolt mennyiség"] = format_quantity(offer.get('confirmed_quantity', 0), offer.get('quantity_unit', 'kg'))
            
            # Biztonságos ár formázás
            conf_price = offer.get('confirmed_price')
            if conf_price is not None:
                try:
                    price_val = int(float(conf_price))
                    row["Visszaigazolt ár"] = f"{price_val:,} Ft".replace(",", " ")
                except (ValueError, TypeError):
                    row["Visszaigazolt ár"] = "0 Ft"
            else:
                row["Visszaigazolt ár"] = "0 Ft"
        
        # Eredeti adatok mentése a részletes nézethez
        row["formatted_delivery_date"] = format_date(offer.get("delivery_date", ""))
        row["formatted_quantity"] = format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)), offer.get('quantity_unit', 'kg'))
        
        # Visszaigazolt értékek formázott verzióinak mentése, ha vannak
        if offer.get("confirmed_quantity") is not None:
            row["formatted_confirmed_quantity"] = format_quantity(offer.get('confirmed_quantity', 0))
        
        # Biztonságos ár formázás
        if offer.get("confirmed_price") is not None:
            try:
                conf_price = offer.get("confirmed_price")
                if conf_price is not None:
                    price_val = int(float(conf_price))
                    row["formatted_confirmed_price"] = f"{price_val:,} Ft".replace(",", " ")
                else:
                    row["formatted_confirmed_price"] = "0 Ft"
            except (ValueError, TypeError):
                row["formatted_confirmed_price"] = "0 Ft"
        
        # Státusz szín kód
        status_code = offer.get("status", "")
        color = "#23272e"
        if status_code in config.OFFER_STATUSES:
            mapped_color = config.OFFER_STATUSES[status_code].get("color", "#23272e")
            if not mapped_color or mapped_color in [None, "", "#fff", "#ffffff"] or is_light(mapped_color):
                color = "#23272e"
            else:
                color = mapped_color
        row["status_color"] = str(color) if color else "#23272e"
        data.append(row)
    
    # Megjelenítendő oszlopok - csak és kizárólag a specifikált sorrendben
    # Megjelenítendő oszlopok rövidített fejléccel
    columns = [
        "ID",
        "Státusz",
        "Beszállítás",
        "Termék",
        "Mennyiség",
        "Vissz. menny.",
        "Vissz. ár"
    ]
    # Mapping a forrásadatokból a rövidített oszlopokra
    column_mapping = {
        "ID": "ID",
        "Beszállítás": "Beszállítás dátuma",
        "Termék": "Termék",
        "Mennyiség": "Mennyiség",
        "Vissz. menny.": "Visszaigazolt mennyiség",
        "Vissz. ár": "Visszaigazolt ár",
        "Státusz": "Státusz"
    }

    
    # DataFrame létrehozása
    df = pd.DataFrame(data)

    # Átnevezzük az oszlopokat a rövidített fejlécre
    df = df.rename(columns={
        "Beszállítás dátuma": "Beszállítás",
        "Visszaigazolt mennyiség": "Vissz. menny.",
        "Visszaigazolt ár": "Vissz. ár"
    })

    # Töltsük ki a hiányzó oszlopokat és cellákat üres stringgel, hogy biztosan megjelenjen
    for col in columns + ["status_color"]:
        if col not in df.columns:
            df[col] = ""
    df = df.fillna("")
    
    # Mindig a natív Streamlit DataFrame-et használjuk, az AgGrid logika ki van kapcsolva
    # Státusz szövegek rövidítése csak a táblázat megjelenítéshez
    status_display_map = {
        "Cég által visszaigazolva": "Visszaigazolva",
        "Termelő által elfogadva": "Termelő: elfogadva",
        "Termelő által elutasítva": "Termelő: elutasítva"
    }

    def status_color_map(status_display):
        # A háttérszínt a státusz KÓD alapján rendeljük hozzá, ne a rövidített szöveghez
        # Megkeressük a státusz kódot a display szöveg alapján
        status_code = None
        for code, data in config.OFFER_STATUSES.items():
            if format_status(code) == status_display or status_display_map.get(data["name"], data["name"]) == status_display:
                status_code = code
                break
        if status_code:
            color = config.OFFER_STATUSES[status_code].get("color", "#23272e")
            if not color or color in [None, "", "#fff", "#ffffff"] or is_light(color):
                color = "#23272e"
        else:
            color = "#23272e"
        return f"background-color: {color}; color: white; font-weight: bold;"

    def highlight_status_column(s):
        # A cella szövegét is rövidítsük, ha szükséges
        styled = []
        for val in s:
            display_text = status_display_map.get(val, val)
            style = status_color_map(val) if s.name == "Státusz" else ""
            styled.append(style)
        return styled

    # A státusz oszlop szövegének rövidítése a DataFrame-ben is (csak a táblázatban)
    if "Státusz" in df.columns:
        df["Státusz"] = df["Státusz"].replace(status_display_map)

    def highlight_status_column(s):
        return [status_color_map(val) if s.name == "Státusz" else "" for val in s]

    styled_df = df[columns].style.apply(highlight_status_column, axis=0)
    st.dataframe(
        styled_df,
        hide_index=True,
        column_config={
            "ID": st.column_config.NumberColumn("ID", width="small"),
            "Beszállítás": st.column_config.TextColumn("Beszállítás", width="medium"),
            "Termék": st.column_config.TextColumn("Termék", width="medium"),
            "Mennyiség": st.column_config.TextColumn("Mennyiség", width="small"),
            "Vissz. menny.": st.column_config.TextColumn("Vissz. menny.", width="small"),
            "Vissz. ár": st.column_config.TextColumn("Vissz. ár", width="small"),
            "Státusz": st.column_config.TextColumn("Státusz", width="medium")
        },
        use_container_width=True
    )


    # Ha lapozás engedélyezve van és van elég adat
    if pagination and len(offers) > 10:
        page_size = 10
        total_pages = (len(offers) + page_size - 1) // page_size
        
        col1, col2, col3 = st.columns([1, 3, 1])
        with col2:
            current_page = st.number_input(
                "Oldal",
                min_value=1,
                max_value=total_pages,
                value=1,
                key=f"pagination_{uuid.uuid4()}"
            )
            
            st.caption(f"Összesen {total_pages} oldal")
    
    return df

def display_status_chart(offers):
    """
    Ajánlatok státusz szerinti megoszlásának megjelenítése kördiagramon.
    
    Args:
        offers (list): Ajánlatok listája
    """
    if not offers:
        st.info("Nincsenek adatok a diagramhoz.")
        return
    
    # Adatok aggregálása státuszok szerint
    df = pd.DataFrame(offers)
    status_counts = df["status"].value_counts().reset_index()
    status_counts.columns = ["status", "count"]
    
    # Státusznevek és színek hozzáadása
    status_counts["status_name"] = status_counts["status"].apply(lambda x: format_status(x))
    status_counts["color"] = status_counts["status"].apply(lambda x: get_status_color(x))
    
    # Kördiagram létrehozása
    fig = px.pie(
        status_counts, 
        values="count", 
        names="status_name",
        title="Ajánlatok megoszlása státusz szerint",
        color_discrete_sequence=status_counts["color"].tolist()
    )
    
    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(uniformtext_minsize=12, uniformtext_mode='hide')
    
    st.plotly_chart(fig, use_container_width=True)

def display_quantity_by_product_chart(offers):
    """
    Termékek szerinti mennyiség megoszlás diagram megjelenítése.
    
    Args:
        offers (list): Ajánlatok listája
    """
    # Adatok összegyűjtése
    product_data = {}
    for offer in offers:
        product_name = offer.get("product_name", "Ismeretlen termék")
        # Biztonságos konverzió
        try:
            qty = offer.get("quantity_value", offer.get("quantity_in_kg"))
            quantity = float(qty) if qty is not None and qty != "" else 0.0
        except (ValueError, TypeError):
            quantity = 0.0
        
        if product_name not in product_data:
            product_data[product_name] = {
                "total_quantity": 0,
                "offer_count": 0
            }
        
        product_data[product_name]["total_quantity"] += quantity
        product_data[product_name]["offer_count"] += 1
    
    if not product_data:
        st.info("Nincs megjeleníthető termék adat.")
        return
    
    # DataFrame létrehozása
    df = pd.DataFrame([
        {
            "Termék": product,
            "Mennyiség": data["total_quantity"],
            "Ajánlatok száma": data["offer_count"]
        }
        for product, data in product_data.items()
    ])
    
    # Kördiagram
    fig = px.pie(
        df,
        values="Mennyiség",
        names="Termék",
        title="Mennyiség megoszlása termékek szerint",
        hover_data=["Ajánlatok száma"]
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_monthly_quantity_chart(offers):
    """
    Havi mennyiség diagram megjelenítése.
    
    Args:
        offers (list): Ajánlatok listája
    """
    # Adatok összegyűjtése
    monthly_data = {}
    for offer in offers:
        delivery_date = offer.get("delivery_date")
        if not delivery_date:
            continue
            
        # Dátum konvertálása
        try:
            if isinstance(delivery_date, str):
                date = datetime.strptime(delivery_date, "%Y-%m-%d").date()
            elif isinstance(delivery_date, datetime):
                date = delivery_date.date()
            else:
                continue
                
            month_key = date.strftime("%Y-%m")
            # Biztonságos konverzió
            try:
                qty = offer.get("quantity_value", offer.get("quantity_in_kg"))
                quantity = float(qty) if qty is not None and qty != "" else 0.0
            except (ValueError, TypeError):
                quantity = 0.0
            
            if month_key not in monthly_data:
                monthly_data[month_key] = {
                    "total_quantity": 0,
                    "offer_count": 0
                }
            
            monthly_data[month_key]["total_quantity"] += quantity
            monthly_data[month_key]["offer_count"] += 1
        except:
            continue
    
    if not monthly_data:
        st.info("Nincs megjeleníthető havi adat.")
        return
    
    # DataFrame létrehozása
    df = pd.DataFrame([
        {
            "Hónap": month,
            "Mennyiség": data["total_quantity"],
            "Ajánlatok száma": data["offer_count"]
        }
        for month, data in monthly_data.items()
    ])
    
    # Dátum szerinti rendezés
    df = df.sort_values("Hónap")
    
    # Vonaldiagram
    fig = go.Figure()
    
    # Mennyiség (bal tengely)
    fig.add_trace(go.Bar(
        x=df["Hónap"],
        y=df["Mennyiség"],
        name="Mennyiség",
        marker_color='rgba(58, 71, 80, 0.6)'
    ))
    
    # Ajánlatok száma (jobb tengely)
    fig.add_trace(go.Scatter(
        x=df["Hónap"],
        y=df["Ajánlatok száma"],
        name="Ajánlatok száma",
        marker_color='rgb(246, 78, 139)',
        mode='lines+markers',
        yaxis='y2'
    ))
    
    # Két tengely beállítása
    fig.update_layout(
        title="Havi beszállítások mennyisége és ajánlatok száma",
        xaxis=dict(title="Hónap"),
        yaxis=dict(title="Mennyiség"),
        yaxis2=dict(title="Ajánlatok száma", overlaying="y", side="right"),
        barmode='group',
        legend=dict(x=0.01, y=0.99)
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_price_comparison_chart(offers, group_by="product"):
    """
    Ár összehasonlító diagram megjelenítése.
    
    Args:
        offers (list): Ajánlatok listája
        group_by (str): Csoportosítás típusa ("product" vagy "month")
    """
    # Adatok összegyűjtése
    price_data = {}
    for offer in offers:
        # Ár meghatározása (először a visszaigazolt árat nézzük, ha nincs, akkor az eredetit)
        price = None
        
        try:
            conf_price = offer.get("confirmed_price")
            if conf_price is not None:
                price = float(conf_price)
        except (ValueError, TypeError):
            pass
            
        if price is None:
            try:
                orig_price = offer.get("price")
                if orig_price is not None:
                    price = float(orig_price)
            except (ValueError, TypeError):
                pass
            
        if price is None:
            continue
            
        if group_by == "product":
            key = offer.get("product_name", "Ismeretlen termék")
        else:  # month
            delivery_date = offer.get("delivery_date")
            if not delivery_date:
                continue
                
            try:
                if isinstance(delivery_date, str):
                    date = datetime.strptime(delivery_date, "%Y-%m-%d").date()
                elif isinstance(delivery_date, datetime):
                    date = delivery_date.date()
                else:
                    continue
                    
                key = date.strftime("%Y-%m")
            except:
                continue
        
        if key not in price_data:
            price_data[key] = {
                "prices": [],
                "quantities": []
            }
        
        # Biztonságos konverzió
        try:
            qty = offer.get("quantity_value", offer.get("quantity_in_kg"))
            quantity = float(qty) if qty is not None and qty != "" else 0.0
        except (ValueError, TypeError):
            quantity = 0.0
        price_data[key]["prices"].append(price)
        price_data[key]["quantities"].append(quantity)
    
    if not price_data:
        st.info("Nincs megjeleníthető áradat.")
        return
    
    # DataFrame létrehozása
    df = pd.DataFrame([
        {
            "Név": key,
            "Átlagár": sum(p * q for p, q in zip(data["prices"], data["quantities"])) / sum(data["quantities"]) if sum(data["quantities"]) > 0 else 0,
            "Min ár": min(data["prices"]),
            "Max ár": max(data["prices"]),
            "Mennyiség": sum(data["quantities"])
        }
        for key, data in price_data.items()
    ])
    
    # Dátum szerinti rendezés, ha havi bontás
    if group_by == "month":
        df = df.sort_values("Név")
    else:
        df = df.sort_values("Átlagár", ascending=False)
    
    # Oszlopdiagram átlagárakkal és hibasávokkal
    fig = go.Figure()
    
    # Átlagár oszlopok
    fig.add_trace(go.Bar(
        x=df["Név"],
        y=df["Átlagár"],
        name="Átlagár",
        error_y=dict(
            type='data',
            symmetric=False,
            array=df["Max ár"] - df["Átlagár"],
            arrayminus=df["Átlagár"] - df["Min ár"]
        )
    ))
    
    # Mennyiség vonal (jobb tengely)
    fig.add_trace(go.Scatter(
        x=df["Név"],
        y=df["Mennyiség"],
        name="Mennyiség",
        mode='lines+markers',
        yaxis='y2'
    ))
    
    # Két tengely beállítása
    fig.update_layout(
        title=f"{'Termékenkénti' if group_by == 'product' else 'Havi'} átlagárak és mennyiségek",
        xaxis=dict(title="Termék" if group_by == "product" else "Hónap"),
        yaxis=dict(title="Ár (Ft/egység)"),
        yaxis2=dict(title="Mennyiség", overlaying="y", side="right"),
        legend=dict(x=0.01, y=0.99)
    )
    
    st.plotly_chart(fig, use_container_width=True)