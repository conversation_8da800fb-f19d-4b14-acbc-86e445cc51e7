# TOVACVV form komponens
"""
TOVACVV form komponens a validá<PERSON><PERSON> szab<PERSON>ly bemu<PERSON>.
"""
import streamlit as st
from utils.validators import validate_tovacvv

def render_tovacvv_form():
    """
    TOVACVV form megjelenítése és validálása.
    
    Returns:
        tuple: (bool, dict) - <PERSON>keres-e a validáció és a form adatai
    """
    st.subheader("TOVACVV Form")
    
    # Form mezők
    tovacvv = st.text_input("TOVACVV kód", max_chars=3, help="Adjon meg egy 3 karakteres TOVACVV kódot (betűk és számok)")
    
    # Validáció
    is_valid, error_message = validate_tovacvv(tovacvv)
    
    if not is_valid and tovacvv:
        st.error(error_message)
    
    # Mentés gomb
    if st.button("Mentés", type="primary"):
        if is_valid or not tovacvv:
            st.success("A TOVACVV kód sikeresen mentve!")
            return True, {"tovacvv": tovacvv}
        else:
            st.error("<PERSON><PERSON><PERSON>, javítsa ki a hibákat a mentés előtt.")
            return False, {}
    
    return False, {} 