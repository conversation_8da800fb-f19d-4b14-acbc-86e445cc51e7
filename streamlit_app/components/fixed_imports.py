"""
Centralized import helper for formatting functions and other utilities.
This file helps prevent import errors across different environments.
"""
import logging
import app_config as config

# Configure logger
logger = logging.getLogger(__name__)

# Try different import paths for formatting functions
try:
    from utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date, get_status_color, format_number, format_currency
    logging.info("Using formatting functions from utils.formatting")
except ImportError:
    try:
        from streamlit_app.utils.formatting import format_status, format_quantity, format_price, format_datetime, format_date, get_status_color, format_number, format_currency
        logging.info("Using formatting functions from streamlit_app.utils.formatting")
    except ImportError:
        # Fallback implementations
        logging.warning("All formatting imports failed, using inline implementations")
        def format_status(status):
            return status
            
        def format_datetime(dt):
            if isinstance(dt, str):
                return dt
            try:
                return dt.strftime("%Y-%m-%d %H:%M") if dt else "-"
            except:
                return str(dt) if dt else "-"
                
        def format_date(d):
            if isinstance(d, str):
                return d
            try:
                return d.strftime("%Y-%m-%d") if d else "-"
            except:
                return str(d) if d else "-"
                
        def format_price(p):
            try:
                return f"{int(p):,} Ft" if p is not None else "-"
            except:
                return str(p) if p else "-"
                
        def format_quantity(q):
            try:
                return f"{float(q):,.2f}" if q is not None else "-"
            except:
                return str(q) if q else "-"
                
        def format_number(n):
            try:
                return f"{float(n):,.2f}" if n is not None else "-"
            except:
                return str(n) if n else "-"
                
        def format_currency(c):
            try:
                return f"{int(c):,} Ft" if c is not None else "-"
            except:
                return str(c) if c else "-"
                
        def get_status_color(status):
            """
            Visszaadja a státuszhoz tartozó színt, fallback: sötétszürke.
            Args:
                status (str): Ajánlat státuszkód
            Returns:
                str: HEX színkód
            """
            color = config.OFFER_STATUSES.get(status, {}).get("color", "#23272e")
            if not color or color in [None, "", "#fff", "#ffffff"]:
                return "#23272e"
            return color

# Try different import paths for session utilities
try:
    from utils.session import is_authenticated, get_current_user
    logging.info("Using authentication functions from utils.session")
except ImportError:
    try:
        from streamlit_app.utils.session import is_authenticated, get_current_user
        logging.info("Using authentication functions from streamlit_app.utils.session")
    except ImportError:
        # Fallback implementation
        logging.warning("Authentication imports failed, using inline implementations")
        def is_authenticated():
            return True
            
        def get_current_user():
            return {
                "id": 1,
                "username": "operator",
                "role": "ügyintéző",
                "name": "Test Operator",
                "permissions": ["view_offers", "edit_offers", "change_status"]
            }

# Export all formatting functions in a dictionary for easy import
formatting = {
    "format_status": format_status,
    "format_datetime": format_datetime,
    "format_date": format_date,
    "format_price": format_price,
    "format_quantity": format_quantity,
    "format_number": format_number,
    "format_currency": format_currency,
    "get_status_color": get_status_color
} 