# Értesítés komponens
"""
Értesítés komponens.
"""
import streamlit as st

def show_success(message, icon="✅"):
    """
    Sikeres művelet értesítés megjelenítése.
    
    Args:
        message (str): <PERSON>jelení<PERSON>ő üzenet
        icon (str): Emoji ikon
    """
    st.success(f"{icon} {message}")

def show_info(message, icon="ℹ️"):
    """
    Információs értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.info(f"{icon} {message}")

def show_warning(message, icon="⚠️"):
    """
    Figyelmeztető értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.warning(f"{icon} {message}")

def show_error(message, icon="❌"):
    """
    Hiba értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.error(f"{icon} {message}")

def show_notification(message, type="info"):
    """
    Értesítés megjelenítése típus alapján.
    
    Args:
        message (str): Megjelenítendő üzenet
        type (str): Értesítés típusa (success, info, warning, error)
    """
    if type == "success":
        show_success(message)
    elif type == "info":
        show_info(message)
    elif type == "warning":
        show_warning(message)
    elif type == "error":
        show_error(message)
    else:
        show_info(message)

def toast_notification(message, type="info"):
    """
    Toast értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        type (str): Értesítés típusa (success, info, warning, error)
    """
    if type == "success":
        st.toast(message, icon="✅")
    elif type == "info":
        st.toast(message, icon="ℹ️")
    elif type == "warning":
        st.toast(message, icon="⚠️")
    elif type == "error":
        st.toast(message, icon="❌")
    else:
        st.toast(message, icon="ℹ️")
