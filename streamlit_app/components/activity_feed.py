"""
Activity feed component for displaying recent activity and notifications.
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import random
from utils.session import get_current_user, is_authenticated, get_auth_token
from utils.formatting import format_datetime
import requests
import app_config as config
import time
import os
import traceback

# Glob<PERSON>lis fejlesztői mód beállítása a környezeti változók alapján
DEVELOPER_MODE = os.environ.get('ENVIRONMENT') == 'development' or os.environ.get('RUNNING_IN_DOCKER', 'nem') == 'igen'
FORCE_REAL_DATA = True  # Mindig valós adatokat jelenítsünk meg, még ha azok generáltak is

print(f"==== ÉRTESÍTÉS KOMPONENS BETÖLTÉSE KEZDŐDIK ====")
print(f"Fejlesztői mód: {DEVELOPER_MODE}")
print(f"API_BASE_URL: {config.API_BASE_URL}")

# Az API importálása try-except blokkban
try:
    from api import notifications as notifications_api
    # Ellenőrizzük, hogy a modul ténylegesen tartalmazza-e a szükséges függvényeket
    if not hasattr(notifications_api, 'get_notifications'):
        print("HIBA: notifications_api modult sikerült importálni, de hiányzik a get_notifications függvény")
        NOTIFICATIONS_API_AVAILABLE = False
    else:
        NOTIFICATIONS_API_AVAILABLE = True
        print("SIKER: notifications_api modul sikeresen betöltve")
except (ImportError, ModuleNotFoundError) as e:
    print(f"HIBA: Nem sikerült betölteni a notifications_api modult: {str(e)}")
    NOTIFICATIONS_API_AVAILABLE = False
    # Eltávolítjuk az automata figyelmeztetést a streamlit UI-ról
    # st.warning("Értesítések API nem elérhető, mock adatok kerülnek megjelenítésre")

# Értesítés típusok és kategóriák megjelenítési beállításai
NOTIFICATION_TYPES = {
    "info": {"icon": "ℹ️", "color": "blue", "priority": 3},
    "success": {"icon": "✅", "color": "green", "priority": 2},
    "warning": {"icon": "⚠️", "color": "orange", "priority": 1},
    "error": {"icon": "❌", "color": "red", "priority": 0},
    "update": {"icon": "🔄", "color": "purple", "priority": 2}
}

# Értesítési kategóriák
NOTIFICATION_CATEGORIES = {
    "system": {"name": "Rendszer", "icon": "🖥️", "description": "Rendszerrel kapcsolatos értesítések"},
    "offer": {"name": "Ajánlatok", "icon": "📝", "description": "Ajánlatokkal kapcsolatos értesítések"},
    "delivery": {"name": "Beszállítások", "icon": "🚚", "description": "Beszállításokkal kapcsolatos értesítések"},
    "user": {"name": "Felhasználók", "icon": "👤", "description": "Felhasználókkal kapcsolatos értesítések"},
    "product": {"name": "Termékek", "icon": "🌽", "description": "Termékekkel kapcsolatos értesítések"}
}

# Szerepkörönkénti releváns kategóriák prioritási sorrendben
ROLE_RELEVANCE = {
    "admin": ["system", "user", "offer", "product", "delivery"],
    "ügyintéző": ["offer", "delivery", "product", "user", "system"],
    "termelő": ["offer", "delivery", "product", "system", "user"]
}

# Szerepkörönként releváns értesítések (bővíthető)
ROLE_RELEVANT_NOTIFICATIONS = {
    "admin": {
        "high": ["error", "warning"], 
        "medium": ["system_update", "new_user", "user_issue"],
        "low": ["info", "success"]
    },
    "ügyintéző": {
        "high": ["new_offer", "delivery_today", "delivery_issue"],
        "medium": ["offer_status_change", "delivery_tomorrow"],
        "low": ["system_notification"]
    },
    "termelő": {
        "high": ["offer_confirmed", "offer_rejected", "offer_issue"],
        "medium": ["delivery_reminder", "price_update"],
        "low": ["system_notification", "general_info"]
    }
}

    
def get_real_activities(max_items=10, include_read=False, user_role=None):
    """
    Lekéri a valós értesítéseket az API-ból.

    Args:
        max_items (int): Maximum hány értesítést kérünk le.
        include_read (bool): Olvasott értesítéseket is kérjük-e.
        user_role (str): Felhasználó szerepköre (szűréshez).

    Returns:
        list: Az értesítések listája, vagy üres lista hiba esetén.
        str: Hibaüzenet, ha nem sikerült lekérni az értesítéseket.
    """
    if not is_authenticated():
        print("Felhasználó nincs bejelentkezve, nem tudunk értesítéseket lekérni")
        return [], "Nincs bejelentkezett felhasználó."
    if not NOTIFICATIONS_API_AVAILABLE:
        print("Értesítések API nem érhető el (modul hiányzik vagy hibás)")
        return [], "Az értesítési API nem elérhető."
    try:
        token = get_auth_token()
        if not token:
            print("Nincs érvényes bejelentkezési token")
            return [], "Nincs érvényes bejelentkezési token."
        start_time = time.time()
        print(f"API hívás kezdése: notifications_api.get_notifications(include_read={include_read}, limit={max_items})")
        success, result = notifications_api.get_notifications(
            include_read=include_read,
            limit=max_items
        )
        elapsed_time = time.time() - start_time
        print(f"API hívás befejezve: {elapsed_time:.2f} mp alatt")
        if not success or not isinstance(result, dict) or "items" not in result:
            print("Értesítések lekérése sikertelen vagy üres válasz érkezett")
            reason = ""
            if not success and isinstance(result, str):
                print(f"Hibaüzenet: {result}")
                reason = f"API hiba: {result}"
            elif not success:
                reason = "API-hívás sikertelen ismeretlen okból."
            elif not isinstance(result, dict):
                reason = "API válasz típusa nem dict."
            elif "items" not in result:
                reason = 'API válaszban nincs "items" kulcs.'
            else:
                reason = "Ismeretlen ok."
            return [], reason
        activities = []
        for item in result.get("items", []):
            try:
                created_at = item.get("created_at")
                if isinstance(created_at, str):
                    timestamp = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                else:
                    timestamp = created_at
            except Exception as e:
                from datetime import datetime
                print(f"Hiba a dátum konvertáláskor: {str(e)} - Érték: {item.get('created_at')}")
                timestamp = datetime.now()
            category = "system"
            rel_entity_type = item.get("related_entity_type", "").lower()
            if rel_entity_type in ["offer", "offers"]:
                category = "offer"
            elif rel_entity_type in ["delivery", "deliveries"]:
                category = "delivery"
            elif rel_entity_type in ["user", "users"]:
                category = "user"
            elif rel_entity_type in ["product", "products", "product_type", "product_category"]:
                category = "product"
            activities.append({
                "id": item.get("id"),
                "type": item.get("type", "info"),
                "category": category,
                "message": item.get("message", ""),
                "timestamp": timestamp,
                "is_read": item.get("is_read", False),
                "detail": item.get("detail")
            })
        if not activities:
            from datetime import datetime
            activities.append({
                "id": "welcome-1",
                "type": "info",
                "category": "system",
                "message": "Üdvözlünk a Termelő rendszerben! Itt jelennek meg a fontos értesítéseid.",
                "timestamp": datetime.now(),
                "is_read": False,
                "detail": None
            })
        if user_role and user_role in ROLE_RELEVANCE:
            relevant_categories = ROLE_RELEVANCE[user_role]
            for activity in activities:
                type_priority = NOTIFICATION_TYPES.get(activity.get("type", "info"), {}).get("priority", 3)
                category = activity.get("category", "system")
                if category in relevant_categories:
                    category_priority = len(relevant_categories) - relevant_categories.index(category)
                else:
                    category_priority = 0
                unread_bonus = 3 if not activity.get("is_read", False) else 0
                activity["priority"] = type_priority + category_priority + unread_bonus
            activities.sort(key=lambda x: (x.get("priority", 0), x.get("timestamp")), reverse=True)
        print(f"Sikeresen lekérdezett értesítések száma: {len(activities)}")
        return activities, None
    except Exception as e:
        import traceback
        from datetime import datetime
        print(f"Értesítések lekérése közben kivétel történt: {str(e)}")
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return [], f"Váratlan hiba történt: {str(e)}"

    """
    Lekéri a valós értesítéseket az API-ból.

    Args:
        max_items (int): Maximum hány értesítést kérünk le.
        include_read (bool): Olvasott értesítéseket is kérjük-e.
        user_role (str): Felhasználó szerepköre (nem használt).

    Returns:
        list: Az értesítések listája, vagy üres lista hiba esetén.
        str: Hibaüzenet, ha nem sikerült lekérni az értesítéseket.
    """
    if not NOTIFICATIONS_API_AVAILABLE:
        return [], "Az értesítési API nem elérhető."
    try:
        print(f"Értesítések lekérése: include_read={include_read}, limit={max_items}")
        success, result = notifications_api.get_notifications(include_read=include_read, limit=max_items)
        if success and result and isinstance(result, dict) and "items" in result:
            return result["items"], None
        else:
            return [], "Nem sikerült értesítéseket lekérni az API-ból."
    except Exception as e:
        print(f"Kivétel a get_real_activities függvényben: {str(e)}")
        import traceback
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return [], f"Váratlan hiba történt értesítések lekérésekor: {str(e)}"
    """
    Valós értesítések lekérése az API-ból.
    
    Args:
        max_items: Maximum hány elemet kérünk le
        include_read: Olvastott értesítéseket is kérjük-e
        user_role: Felhasználó szerepköre (szűréshez)
        
    Returns:
        list: Az értesítések listája
    """
    # Ha a felhasználó nincs bejelentkezve, üres listával térünk vissza
    if not is_authenticated():
        print("Felhasználó nincs bejelentkezve, nem tudunk értesítéseket lekérni")
        return []
    
    # Ha fejlesztői módban vagyunk és nincs API elérés, generáljunk adatokat
    # Ha nincs API, üres listával térünk vissza (éles környezetben)
    if not NOTIFICATIONS_API_AVAILABLE:
        print("Értesítések API nem érhető el (modul hiányzik vagy hibás)")
        return []
    
    try:
        # Ellenőrizzük a token meglétét 
        token = get_auth_token()
        if not token:
            print("Nincs érvényes bejelentkezési token")
            return []
        
        # Értesítések lekérése az API-ból
        start_time = time.time()
        print(f"API hívás kezdése: notifications_api.get_notifications(include_read={include_read}, limit={max_items})")
        
        success, result = notifications_api.get_notifications(
            include_read=include_read,
            limit=max_items
        )
        
        # API hívás idejének mérése
        elapsed_time = time.time() - start_time
        print(f"API hívás befejezve: {elapsed_time:.2f} mp alatt")
        
        # Debug információ
        print(f"Értesítések API válasz: sikeres={success}, eredmény típusa={type(result)}")
        if isinstance(result, dict):
            print(f"Találatok száma: {len(result.get('items', []))}")
        
        # Ha sikertelen, üres listával térünk vissza
        if not success or not isinstance(result, dict) or "items" not in result:
            print("Értesítések lekérése sikertelen vagy üres válasz érkezett")
            
            # Részletesebb hiba információ
            reason = ""
            if not success and isinstance(result, str):
                print(f"Hibaüzenet: {result}")
                reason = f"API hiba: {result}"
            elif not success:
                reason = "API-hívás sikertelen ismeretlen okból."
            elif not isinstance(result, dict):
                reason = "API válasz típusa nem dict."
            elif "items" not in result:
                reason = 'API válaszban nincs "items" kulcs.'
            else:
                reason = "Ismeretlen ok."
            st.session_state["last_activity_feed_error"] = reason
            st.info(f"⚠️ Nem sikerült valós értesítéseket betölteni. Generált adatokat látsz. Ok: {reason}")
            return []
            
        # API válasz átalakítása a megfelelő formátumra
        activities = []
        for item in result.get("items", []):
            try:
                # Próbáljuk meg datetime objektumként kezelni a dátumot
                created_at = item.get("created_at")
                if isinstance(created_at, str):
                    timestamp = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                else:
                    # Ha már datetime objektum, használjuk közvetlenül
                    timestamp = created_at
            except Exception as e:
                # Hibakezelés, ha probléma van a dátum konvertálással
                print(f"Hiba a dátum konvertáláskor: {str(e)} - Érték: {item.get('created_at')}")
                timestamp = datetime.now()  # Fallback: jelenlegi idő
            
            # Kategória meghatározása
            category = "system"  # Alapértelmezett kategória
            rel_entity_type = item.get("related_entity_type", "").lower()
            if rel_entity_type in ["offer", "offers"]:
                category = "offer"
            elif rel_entity_type in ["delivery", "deliveries"]:
                category = "delivery"
            elif rel_entity_type in ["user", "users"]:
                category = "user"
            elif rel_entity_type in ["product", "products", "product_type", "product_category"]:
                category = "product"
            
            activities.append({
                "id": item.get("id"),
                "type": item.get("type", "info"),
                "category": category,
                "message": item.get("message", ""),
                "timestamp": timestamp,
                "is_read": item.get("is_read", False),
                "detail": item.get("detail")
            })
        
        # Ha nincs egyetlen értesítés sem, adjunk hozzá egy alap üdvözlő értesítést
        if not activities:
            from datetime import datetime
            activities.append({
                "id": "welcome-1",
                "type": "system",
                "category": "system",
                "message": "Üdvözlünk a Termelő rendszerben! Itt jelennek meg a fontos értesítéseid.",
                "timestamp": datetime.now(),
                "is_read": False,
                "detail": None
            })
        
        # Ha meg van adva szerepkör, szűrjük a legrelevánsabb értesítésekre
        if user_role and user_role in ROLE_RELEVANCE:
            # Szerepkörhöz tartozó kategóriák fontossági sorrendben
            relevant_categories = ROLE_RELEVANCE[user_role]
            
            # Prioritás kiszámítása: típus prioritás (0-3) + kategória relevancia (fordított sorrend 5-0)
            for activity in activities:
                type_priority = NOTIFICATION_TYPES.get(activity.get("type", "info"), {}).get("priority", 3)
                category = activity.get("category", "system")
                
                # Ha a kategória szerepel a releváns kategóriákban, fordított sorrendje lesz a prioritása
                if category in relevant_categories:
                    category_priority = len(relevant_categories) - relevant_categories.index(category)
                else:
                    category_priority = 0
                
                # Olvasatlan értesítések magasabb prioritást kapnak
                unread_bonus = 3 if not activity.get("is_read", False) else 0
                
                # Összes prioritás kiszámítása
                activity["priority"] = type_priority + category_priority + unread_bonus
            
            # Rendezzük prioritás szerint csökkenő sorrendben, majd dátum szerint csökkenő sorrendben
            activities.sort(key=lambda x: (x.get("priority", 0), x.get("timestamp")), reverse=True)
        
        print(f"Sikeresen lekérdezett értesítések száma: {len(activities)}")
        print(f"[DEBUG] Visszaadott activities: {activities}")
        return activities
    except Exception as e:
        # Kivételek esetén, ha fejlesztői módban vagyunk, generáljunk adatokat
        print(f"Értesítések lekérése közben kivétel történt: {str(e)}")
        print(f"Kivétel részletei: {traceback.format_exc()}")
        
        return []


def check_api_connection():
    """
    Ellenőrzi, hogy az API elérhető-e és működik-e
    
    Returns:
        bool: True ha az API elérhető és működik, False egyébként
    """
    print("==== API kapcsolat ellenőrzése ====")
    
    # Fejlesztői környezetben mindig sikeresen térünk vissza
    if DEVELOPER_MODE:
        print("- Fejlesztői módban vagyunk, sikeres API kapcsolat feltételezése")
        return True
    
    # 1. Ellenőrizzük, hogy a modul betöltése sikeres volt-e
    if not NOTIFICATIONS_API_AVAILABLE:
        print("- API modul nem elérhető")
        return False
    
    # 2. Ellenőrizzük, hogy a felhasználó be van-e jelentkezve
    if not is_authenticated():
        print("- Felhasználó nincs bejelentkezve")
        return False
    
    # 3. Ellenőrizzük, hogy van-e érvényes token
    token = get_auth_token()
    if not token:
        print("- Hiányzó autentikációs token")
        return False
    
    # 4. Ellenőrizzük a konfigurációban beállított API URL-t
    api_url = config.API_BASE_URL
    if not api_url:
        print("- Hiányzó API URL a konfigurációban")
        return False
    
    print(f"- API URL: {api_url}")
    print(f"- Token: {token[:5]}...{token[-5:] if token and len(token) > 10 else ''}")
    
    # 5. Próbáljunk elérni egy ismert, működő végpontot
    try:
        # Ellenőrizzük a /users/me végpontot, ami biztosan működik
        print(f"- Ismert végpont ellenőrzése: {api_url}/users/me")
        test_response = requests.get(
            f"{api_url}/users/me",
            headers={"Authorization": f"Bearer {token}"},
            timeout=5
        )
        print(f"  > Státuszkód: {test_response.status_code}")
        if test_response.status_code == 200:
            print("  > Sikeres teszt kérés")
            return True
        else:
            print(f"  > Sikertelen teszt kérés: {test_response.text[:100]}")
    except Exception as e:
        print(f"- Teszt végpont hívás hiba: {str(e)}")
    
    return False


def mark_notification_as_read(notification_id):
    """
    Értesítés olvasottnak jelölése.
    
    Args:
        notification_id: Az értesítés azonosítója
        
    Returns:
        bool: Sikeres művelet (True/False)
    """
    # Ha az API nem elérhető, hamist térünk vissza
    if not NOTIFICATIONS_API_AVAILABLE:
        return False
        
    # Ha a felhasználó nincs bejelentkezve, hamist térünk vissza
    if not is_authenticated():
        return False
    
    try:
        print(f"Értesítés olvasottnak jelölése: {notification_id}")
        success, result = notifications_api.mark_notification_read(notification_id)
        
        if not success:
            print(f"Értesítés olvasottnak jelölése sikertelen: {result}")
            st.error(f"Nem sikerült az értesítést olvasottnak jelölni: {result}")
        
        return success
    except Exception as e:
        print(f"Hiba az értesítés olvasottnak jelölésekor: {str(e)}")
        print(f"Kivétel részletei: {traceback.format_exc()}")
        st.error(f"Hiba az értesítés olvasottnak jelölésekor: {str(e)}")
        return False


def mark_all_notifications_as_read():
    """
    Összes értesítés olvasottnak jelölése.
    
    Returns:
        bool: Sikeres művelet (True/False)
    """
    # Ha az API nem elérhető, hamist térünk vissza
    if not NOTIFICATIONS_API_AVAILABLE:
        return False
        
    # Ha a felhasználó nincs bejelentkezve, hamist térünk vissza
    if not is_authenticated():
        return False
    
    try:
        print("Összes értesítés olvasottnak jelölése...")
        success, result = notifications_api.mark_all_notifications_read()
        
        if not success:
            print(f"Értesítések olvasottnak jelölése sikertelen: {result}")
            
        return success
    except Exception as e:
        print(f"Hiba az összes értesítés olvasottnak jelölésekor: {str(e)}")
        print(f"Kivétel részletei: {traceback.format_exc()}")
        return False


def display_activity_list(activities: list) -> None:
    """
    Mobilbarát értesítési lista megjelenítése.

    Args:
        activities (list): Az értesítések listája.
    """
    if not activities or not isinstance(activities, list):
        st.info("Nincsenek értesítések megjelenítésre.")
        return

    is_mobile = st.session_state.get('is_mobile', False)
    column_ratio = [1, 5, 2] if is_mobile else [1, 8, 3]

    for i, activity in enumerate(activities):
        if not isinstance(activity, dict):
            continue  # Hibás adatstruktúra, átugorjuk

        notification_id = activity.get("id", f"unknown_{i}")
        title = activity.get("title", "Ismeretlen értesítés")
        message = activity.get("message", "")
        timestamp = activity.get("timestamp", "")
        is_read = activity.get("is_read", False)

        # Egyedi kulcs minden gombhoz
        key = f"read_btn_{i}_{notification_id}"

        cols = st.columns(column_ratio)
        with cols[0]:
            st.markdown("🔔" if not is_read else "✅")
        with cols[1]:
            # Rövidebb üzenet mobilon
            message_text = message if not is_mobile else (message[:50] + "..." if len(message) > 50 else message)
            st.markdown(f"**{title}**  \n{message_text}")
            if timestamp:
                st.caption(format_datetime(timestamp))
        with cols[2]:
            # Csak integer id esetén jelenjen meg az Olvasva gomb
            can_mark_read = False
            try:
                int_id = int(notification_id)
                can_mark_read = True
            except (ValueError, TypeError):
                can_mark_read = False
            if not is_read and can_mark_read:
                button_style = "padding: 2px 5px;" if is_mobile else ""
                if st.button("Olvasva", key=key):
                    if mark_notification_as_read(notification_id):
                        st.success("Elolvasva")
                        st.session_state.needs_rerun = True
            elif not is_read and not can_mark_read:
                st.caption("Nem olvasott, de nem törölhető (demo)")


def render_activity_feed(max_items=5, show_all_button=True, height=None):
    """
    Értesítési feed megjelenítése.
    
    Args:
        max_items (int): Maximum hány értesítést jelenítsen meg.
        show_all_button (bool): "Összes megjelenítése" gomb megjelenítése.
        height (int): Opcionális paraméter - már nem használt.
    """
    # Get current user and role
    user = get_current_user()
    user_role = user.get("role", "").lower() if user else None
    
    # Konténer az értesítésekhez
    feed_container = st.container(border=True)
    
    with feed_container:
        # Fejléc és frissítés gomb egy sorban, mobilbarát
        col1, col2 = st.columns([4, 1])
        with col1:
            st.subheader("🔔 Tevékenységek és értesítések")
        with col2:
            if st.button("🔄", key="refresh_notifications_btn", help="Értesítések frissítése"):
                st.session_state.refresh_notifications = True
                if "activity_data" in st.session_state:
                    del st.session_state.activity_data
                st.rerun()
        
        # Force refresh token az adatbetöltés gombbal
        if "refresh_notifications" not in st.session_state:
            st.session_state.refresh_notifications = False
        
        # Fejlesztői módban mindig feltételezzük a sikeres API kapcsolatot
        api_available = True if DEVELOPER_MODE else check_api_connection()
        
        # Initialize or get activity data from session state
        if "activity_data" not in st.session_state or st.session_state.get("refresh_notifications", False):
            st.session_state.refresh_notifications = False
            # API-n keresztül próbáljuk meg lekérni az adatokat
            activities, error = get_real_activities(max_items=15, user_role=user_role)
            
            if activities and not error:
                st.session_state.activity_data = activities
            else:
                # Ha nem sikerült adatot lekérni, jelenítsünk meg hibaüzenetet
                st.session_state.activity_data = []
                if error:
                    st.error(f"⚠️ {error}")
                else:
                    st.error("Nem sikerült értesítéseket lekérni az API-ból.")
                return
        
        activities = st.session_state.activity_data
        
        if not activities:
            st.info("Nincsenek értesítések.")
            return
        
        # Csoportosítsuk kategóriák szerint, ha van legalább 5 értesítés
        if len(activities) >= 5:
            # Kategóriák szerinti csoportosítás
            categories = {}
            for activity in activities:
                category = activity.get("category", "system")
                if category not in categories:
                    categories[category] = []
                categories[category].append(activity)
            
            # Hány kategória van?
            num_categories = len(categories)
            
            # Ha van felhasználói szerepkör és több mint 1 kategória, tabulátor nézet
            if user_role and num_categories > 1:
                # Rendezzük a kategóriákat a szerepkörnek megfelelő prioritás szerint
                sorted_categories = []
                if user_role in ROLE_RELEVANCE:
                    # Vegyük a szerepkörhöz tartozó releváns kategóriákat
                    for category in ROLE_RELEVANCE[user_role]:
                        if category in categories:
                            sorted_categories.append(category)
                else:
                    # Ha nincs definiálva relevancia, használjuk az eredeti sorrendet
                    sorted_categories = list(categories.keys())
                
                # Korlátozzuk a kategóriákat 5-re
                sorted_categories = sorted_categories[:5]
                
                # Fülek létrehozása minden kategóriához
                tabs = st.tabs([f"{NOTIFICATION_CATEGORIES.get(cat, {}).get('icon', '�')} {NOTIFICATION_CATEGORIES.get(cat, {}).get('name', cat.title())}" for cat in sorted_categories])
                
                # Értesítések megjelenítése kategóriánként
                for i, category in enumerate(sorted_categories):
                    with tabs[i]:
                        category_activities = categories[category]
                        # Rendezzük olvasatlanság és dátum szerint
                        category_activities.sort(key=lambda x: (not x.get("is_read", False), x.get("timestamp")), reverse=True)
                        
                        # Csak a legfontosabb max_items/2 elemet mutatjuk kategóriánként
                        display_activities = category_activities[:max(3, int(max_items/2))]
                        
                        if not display_activities:
                            st.info(f"Nincsenek {NOTIFICATION_CATEGORIES.get(category, {}).get('name', category)} értesítések")
                        else:
                            # Megjelenítjük az értesítéseket
                            display_activity_list(display_activities)
                            
                            # Ha van több elem, mint amit megjelenítünk, mutassunk egy "Több" gombot
                            remaining = len(category_activities) - len(display_activities)
                            if remaining > 0:
                                if st.button(f"További {remaining} értesítés mutatása", key=f"more_{category}"):
                                    # Az összes kategória aktivitást megjelenítjük
                                    display_activity_list(category_activities)
            else:
                # Egyszerű lista nézet
                display_activity_list(activities[:max_items])
        else:
            # Ha kevés értesítés van, egyszerű lista nézet
            display_activity_list(activities)
        
        # Show all/less button
        if show_all_button and len(activities) > max_items:
            if "show_all_activities" not in st.session_state:
                st.session_state.show_all_activities = False
                
            button_text = "Kevesebb mutatása" if st.session_state.get("show_all_activities", False) else "Összes mutatása"
            
            if st.button(button_text, key="toggle_activities", use_container_width=True):
                st.session_state.show_all_activities = not st.session_state.get("show_all_activities", False)
                st.rerun()
        
        # Csak akkor jelenítjük meg az "Összes olvasottnak jelölése" gombot, ha valós adatokat használunk
        if activities and NOTIFICATIONS_API_AVAILABLE:
            if st.button("Összes olvasottnak jelölése", key="mark_all_read"):
                if mark_all_notifications_as_read():
                    st.success("Minden értesítés olvasottnak jelölve")
                    st.rerun()
                else:
                    st.error("Nem sikerült az értesítéseket olvasottnak jelölni")

def render_quick_stats(stats: dict = None) -> None:
    """
    Gyors statisztikai kártyák megjelenítése a dashboard tetején.

    Args:
        stats (dict, optional): Statisztikai adatok szótára. Ha None, API-ból lekérjük.
    """
    if stats is None:
        # Valós statisztikák lekérése, ha API elérhető
        try:
            from api import offers as offers_api
            success, offers = offers_api.get_offers()
            if success and offers:
                active_offers = sum(1 for o in offers if o.get("status") in ["CREATED", "CONFIRMED_BY_COMPANY"])
                today = datetime.now().strftime("%Y-%m-%d")
                today_deliveries = sum(1 for o in offers if o.get("delivery_date") == today and o.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"])
                waiting_approval = sum(1 for o in offers if o.get("status") == "CONFIRMED_BY_COMPANY")
                def parse_kg(val):
                    try:
                        return float(val)
                    except (TypeError, ValueError):
                        return 0
                monthly_volume = sum(parse_kg(o.get("quantity_in_kg", 0)) for o in offers if o.get("status") in ["ACCEPTED_BY_USER", "FINALIZED"])
                stats = {
                    "Aktív ajánlatok": active_offers,
                    "Mai beszállítások": today_deliveries,
                    "Jóváhagyásra vár": waiting_approval,
                    "Havi forgalom": f"{int(monthly_volume):,} kg".replace(",", " ")
                }
                print(f"Statisztikák sikeresen lekérdezve: {stats}")
            else:
                stats = {
                    "Aktív ajánlatok": 0,
                    "Mai beszállítások": 0,
                    "Jóváhagyásra vár": 0,
                    "Havi forgalom": "0 kg"
                }
        except Exception as e:
            print(f"Hiba a statisztikák lekérésekor: {str(e)}")
            print(f"Kivétel részletei: {traceback.format_exc()}")
            stats = {
                "Aktív ajánlatok": 0,
                "Mai beszállítások": 0,
                "Jóváhagyásra vár": 0,
                "Havi forgalom": "0 kg"
            }
    icons = ["📊", "🚚", "⏳", "📈"]
    is_mobile = st.session_state.get('is_mobile', False)
    if is_mobile:
        # 2x2 elrendezés mobilon
        stat_items = list(stats.items())
        for i in range(0, len(stat_items), 2):
            cols = st.columns(2)
            for j in range(2):
                if i+j < len(stat_items):
                    label, value = stat_items[i+j]
                    icon = icons[i+j]
                    with cols[j]:
                        st.markdown(
                            f"<div style='text-align: center; padding: 8px; border-radius: 5px; border: 1px solid #ddd;'>" +
                            f"<div style='font-size: 20px;'>{icon}</div>" +
                            f"<div style='font-weight: bold; font-size: 16px;'>{value}</div>" +
                            f"<div style='color: gray; font-size: 12px;'>{label}</div>" +
                            f"</div>",
                            unsafe_allow_html=True
                        )
    else:
        # 4x1 asztali elrendezés
        columns = st.columns(4)
        for i, (label, value) in enumerate(stats.items()):
            with columns[i]:
                st.markdown(
                    f"<div style='text-align: center; padding: 10px; border-radius: 5px; border: 1px solid #ddd;'>" +
                    f"<div style='font-size: 24px;'>{icons[i]}</div>" +
                    f"<div style='font-weight: bold; font-size: 20px;'>{value}</div>" +
                    f"<div style='color: gray; font-size: 14px;'>{label}</div>" +
                    f"</div>",
                    unsafe_allow_html=True
                )


def display_activity_list(activities):
    """
    Értesítések megjelenítése listában.

    Args:
        activities (list): Értesítések listája.
    """
    is_mobile = st.session_state.get('is_mobile', False)
    column_ratio = [1, 5, 2] if is_mobile else [1, 8, 3]
    for i, activity in enumerate(activities):
        if not isinstance(activity, dict):
            continue  # Hibás adatstruktúra, átugorjuk

        notification_id = activity.get("id", f"unknown_{i}")
        title = activity.get("title", "Ismeretlen értesítés")
        message = activity.get("message", "")
        timestamp = activity.get("timestamp", "")
        is_read = activity.get("is_read", False)

        # Egyedi kulcs minden gombhoz
        key = f"read_btn_{i}_{notification_id}"

        cols = st.columns(column_ratio)
        with cols[0]:
            st.markdown("🔔" if not is_read else "✅")
        with cols[1]:
            # Rövidebb üzenet mobilon
            message_text = message if not is_mobile else (message[:50] + "..." if len(message) > 50 else message)
            st.markdown(f"**{title}**  \n{message_text}")
            if timestamp:
                st.caption(format_datetime(timestamp))
        with cols[2]:
            # Csak integer id esetén jelenjen meg az Olvasva gomb
            can_mark_read = False
            try:
                int_id = int(notification_id)
                can_mark_read = True
            except (ValueError, TypeError):
                can_mark_read = False
            if not is_read and can_mark_read:
                button_style = "padding: 2px 5px;" if is_mobile else ""
                if st.button("Olvasva", key=key):
                    if mark_notification_as_read(notification_id):
                        st.success("Elolvasva")
                        st.session_state.needs_rerun = True
            elif not is_read and not can_mark_read:
                st.caption("Nem olvasott, de nem törölhető (demo)")


def render_dashboard_widgets() -> None:
    """
    Dashboard összes widgetjének megjelenítése egy függvényben.
    """
    detect_mobile_device()
    render_quick_stats()
    render_activity_feed(max_items=3)



def detect_mobile_device():
    """
    JavaScript kód a képernyőméret detektálásához. Beállítja a session_state['is_mobile'] értékét.
    """
    st.markdown("""
    <script>
        function detectMobile() {
            return window.innerWidth <= 768;
        }
        if (window.parent) {
            const isMobile = detectMobile();
            const streamlitEvent = new CustomEvent("streamlit:setComponentValue", {detail: {is_mobile: isMobile}});
            window.parent.dispatchEvent(streamlitEvent);
        }
    </script>
    """, unsafe_allow_html=True)

# Betöltéskor ellenőrizzük, hogy működik-e az API kapcsolat
print("==== ÉRTESÍTÉS KOMPONENS INICIALIZÁLVA ====")
print(f"Docker környezet: {os.environ.get('RUNNING_IN_DOCKER', 'nem')}")
print(f"API Base URL: {config.API_BASE_URL}")
print(f"API Host környezeti változó: {os.environ.get('API_HOST', 'nincs beállítva')}")
try:
    result = check_api_connection()
    print(f"API kapcsolat ellenőrzés eredménye: {'SIKERES' if result else 'SIKERTELEN'}")
except Exception as e:
    print(f"API kapcsolat ellenőrzés kivételt generált: {str(e)}")
    print(f"Kivétel részletei: {traceback.format_exc()}") 