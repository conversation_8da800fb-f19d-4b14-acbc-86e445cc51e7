"""
<PERSON><PERSON><PERSON><PERSON>tez<PERSON> vizu<PERSON><PERSON> (modern UI, Streamlit).
"""
from typing import Any, Dict
import streamlit as st
import app_config as config
from .data_display import format_status, format_date, format_quantity, format_datetime
from api import offers as offers_api


def get_status_color(status: str) -> str:
    """
    Visszaadja a státuszhoz tartozó színt, fallback: sötétszürke.
    Args:
        status (str): <PERSON><PERSON><PERSON><PERSON> státuszkód
    Returns:
        str: HEX színkód
    """
    color = config.OFFER_STATUSES.get(status, {}).get("color", "#23272e")
    if not color or color in [None, "", "#fff", "#ffffff"]:
        return "#23272e"
    return color


def display_offer_status_card(offer: Dict[str, Any]) -> None:
    """
    Státuszjelző kártya sötét, modern dizájnnal.
    Args:
        offer (dict): <PERSON><PERSON><PERSON><PERSON> adata<PERSON>
    """
    status = offer.get("status", "")
    status_text = format_status(status)
    status_color = get_status_color(status)
    st.markdown(
        f"""
        <div style='padding: 20px; border-radius: 8px; background-color: {status_color}; color: white; 
        display: flex; flex-direction: column; margin-bottom: 20px;'>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <div style='font-size: 22px; font-weight: bold;'>{status_text}</div>
                <div style='background-color: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 16px;'>
                    ID: {offer.get('id')}
                </div>
            </div>
            <div style='margin-top: 10px; font-size: 14px;'>
                Utoljára módosítva: {format_datetime(offer.get('updated_at', ''))}
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )


def display_offer_detail_cards(offer: Dict[str, Any], is_mobile: bool = False) -> None:
    """
    Strukturált, sötét témájú információs kártyák (táblázatszerű sorok, mobilbarát).
    Args:
        offer (dict): Ajánlat adatai
        is_mobile (bool, optional): Mobilnézet használata. Defaults to False.
    """
    css = """
    <style>
    .offer-card {
        min-height: 220px;
        background-color: #1e2230; 
        color: #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
    }
    .card-title {
        color: #3584e4;
        font-size: 18px;
        font-weight: bold;
        margin-top: 0;
        margin-bottom: 16px;
        border-bottom: 1px solid #3a3f4b;
        padding-bottom: 8px;
    }
    .card-row {
        display: flex;
        justify-content: space-between;
        padding: 6px 0;
        border-bottom: 1px solid #2c3142;
    }
    .card-label {
        font-weight: bold;
        color: #a0a0a0;
    }
    .card-value {
        text-align: right;
    }
    @media (max-width: 900px) {
        .stColumns { flex-direction: column !important; }
    }
    </style>
    """
    st.markdown(css, unsafe_allow_html=True)
    
    # Adjust layout based on device type
    if is_mobile:
        # Mobile layout - stack everything in a single column
        col1, col2, col3 = st.columns([1, 1, 1])
    else:
        # Desktop layout - three equal columns
        col1, col2, col3 = st.columns(3)
    with col1:
        st.markdown(
            f"""
            <div class='offer-card'>
                <h4 class='card-title'>Ajánlat adatai</h4>
                <div class='card-row'>
                    <span class='card-label'>Azonosító:</span>
                    <span class='card-value'>{offer.get('id')}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Beszállítás:</span>
                    <span class='card-value'>{format_date(offer.get('delivery_date', ''))}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Termék:</span>
                    <span class='card-value'>{offer.get('product_type', {}).get('name', '')}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Minőség:</span>
                    <span class='card-value'>{(offer.get('quality_grade') or {}).get('name', '')}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Mennyiség:</span>
                    <span class='card-value'>{format_quantity(offer.get('quantity_value', offer.get('quantity_in_kg', 0)), offer.get('quantity_unit', 'kg'))}</span>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )
    with col2:
        st.markdown(
            f"""
            <div class='offer-card'>
                <h4 class='card-title'>Visszaigazolás</h4>
                <div class='card-row'>
                    <span class='card-label'>Visszaigazolt mennyiség:</span>
                    <span class='card-value'>{format_quantity(offer.get('confirmed_quantity', 0), offer.get('quantity_unit', 'kg'))}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Visszaigazolt ár:</span>
                    <span class='card-value'>{offer.get('confirmed_price', '')} Ft</span>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )
    with col3:
        st.markdown(
            f"""
            <div class='offer-card'>
                <h4 class='card-title'>Egyéb információk</h4>
                <div class='card-row'>
                    <span class='card-label'>Létrehozva:</span>
                    <span class='card-value'>{format_datetime(offer.get('created_at', ''))}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Utoljára módosítva:</span>
                    <span class='card-value'>{format_datetime(offer.get('updated_at', ''))}</span>
                </div>
                <div class='card-row'>
                    <span class='card-label'>Beszállítás:</span>
                    <span class='card-value'>{format_date(offer.get('delivery_date', ''))}</span>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

def display_status_history(offer_id: int) -> None:
    """
    Ajánlat állapotváltozásainak történetét megjelenítő komponens.

    Args:
        offer_id (int): Az ajánlat azonosítója.

    Megjeleníti az összes státuszváltási bejegyzést időrendben (legrégebbitől a legújabbig).
    Hibás vagy hiányzó timestamp esetén a bejegyzés a lista végére kerül.
    """
    success, history = offers_api.get_offer_logs(offer_id)
    if not success or not history:
        st.info("Nincs elérhető állapotváltozási történet.")
        return
    st.markdown("### Állapotváltozási napló")

    # Idő szerinti rendezés (created_at szerint növekvő)
    def safe_created_at(entry):
        dt = entry.get('created_at')
        return dt if dt is not None else '9999-12-31T23:59:59'

    sorted_history = sorted(history, key=safe_created_at)

    for entry in sorted_history:
        status = entry.get('new_status', '-')
        created_at = entry.get('created_at', '-')
        # Módosító: mindig a changed_by alapján kérjük le a nevet, ha van, különben "Rendszer"
        from utils.user_utils import get_user_name_by_id
        if entry.get('changed_by'):
            modified_by = get_user_name_by_id(entry['changed_by'])
        else:
            modified_by = 'Rendszer'
        note = entry.get('note', '')

        st.markdown(
            f"""
            <div style='display: flex; margin-bottom: 12px; align-items: center;'>
                <div style='width: 12px; height: 12px; border-radius: 50%; background-color: {get_status_color(status)}; margin-right: 12px;'></div>
                <div style='flex-grow: 1;'>
                    <div style='font-weight: bold;'>{format_status(status)}</div>
                    <div style='font-size: 13px; color: #6c757d;'>
                        {format_datetime(created_at)} • Módosította: {modified_by}
                    </div>
                    <div style='font-size: 13px; color: #adb5bd;'>{note}</div>
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )



def display_offer_attachments(offer_id: int) -> None:
    """
    Ajánlathoz csatolt dokumentumok megjelenítése.
    Args:
        offer_id (int): Ajánlat azonosító
    """
    success, offer = offers_api.get_offer(offer_id)
    if not success or not offer or not offer.get('attachments'):
        st.info("Nincs csatolmány az ajánlathoz.")
        return

    st.write("**Csatolmányok:**")
    for att in offer.get('attachments', []):
        st.markdown(f"- [{att.get('filename', 'Fájl')}]({att.get('url', '#')})")


def display_offer_actions(offer: Dict[str, Any], use_container_width: bool = False) -> None:
    """
    Ajánlathoz kapcsolódó műveletek megjelenítése CSAK saját ajánlatra, státusz és tulajdonos szerint.

    Args:
        offer (dict): Ajánlat adatai
        use_container_width (bool, optional): A gombok a konténer teljes szélességét kitöltsék-e. 
                                             Mobile eszközöknél True ajánlott. Alapértelmezett: False.
    """
    import streamlit as st
    from api import offers as offers_api
    from components.notification import show_success, show_error
    from utils.session import get_current_user

    status = offer.get("status", "")
    offer_id = offer.get("id")
    user = get_current_user()
    # Indok: csak a saját ajánlatomhoz jelenjenek meg a műveletek
    if not user or offer.get("user_id") != user.get("id"):
        st.info("Csak a saját ajánlatodon végezhetsz műveleteket.")
        return

    st.markdown("### Műveletek")
    col1, col2, col3, col4 = st.columns(4)

    # --- Visszaigazolás gomb (csak admin vagy ügyintéző) ---
    # Indok: csak admin vagy ügyintéző, és csak CREATED vagy MODIFIED státuszban
    with col1:
        if user.get("role") in ["admin", "ügyintéző"] and status in ["CREATED", "MODIFIED"]:
            # Visszaigazolási adatok bekérése
            st.write("Visszaigazolási adatok:")
            unit = offer.get('quantity_unit', 'kg')
            confirmed_quantity = st.number_input(
                f"Visszaigazolt mennyiség ({unit})",
                min_value=0.0,
                value=float(offer.get("quantity_value", offer.get("quantity_in_kg", 0))),
                step=0.1,
                key=f"confirm_qty_{offer_id}"
            )
            confirmed_price = st.number_input(
                f"Visszaigazolt ár (Ft/{unit})",
                min_value=0,
                value=int(offer.get("price", 0)),
                step=100,
                key=f"confirm_price_{offer_id}"
            )
            if st.button("Visszaigazolás", key=f"btn_confirm_{offer_id}", type="primary", use_container_width=use_container_width):
                if confirmed_quantity <= 0 or confirmed_price <= 0:
                    show_error("A mennyiség és ár megadása kötelező!")
                else:
                    try:
                        confirmation_data = {"confirmed_quantity": confirmed_quantity, "confirmed_price": confirmed_price}
                        success, result = offers_api.update_offer_status(
                            offer_id, "CONFIRMED_BY_COMPANY", confirmation_data=confirmation_data
                        )
                        if success:
                            show_success("Ajánlat visszaigazolva!")
                            st.session_state.needs_rerun = True
                        else:
                            show_error(f"Hiba visszaigazoláskor: {result}")
                    except Exception as e:
                        show_error(f"Hiba visszaigazoláskor: {str(e)}")

    # --- Elfogadás gomb ---
    # Indok: csak CONFIRMED_BY_COMPANY státuszban
    with col2:
        if status == "CONFIRMED_BY_COMPANY":
            if st.button("Elfogadás", key=f"btn_accept_{offer_id}", type="primary", use_container_width=use_container_width):
                try:
                    success, result = offers_api.update_offer_status(offer_id, "ACCEPTED_BY_USER")
                    if success:
                        show_success("Ajánlat elfogadva!")
                        st.session_state.needs_rerun = True
                    else:
                        show_error(f"Hiba elfogadáskor: {result}")
                except Exception as e:
                    show_error(f"Hiba elfogadáskor: {str(e)}")

    # --- Elutasítás gomb (minden felhasználónak, csak CONFIRMED_BY_COMPANY státuszban) ---
    # Indok: Bármilyen szerepkörben elutasítható, ha CONFIRMED_BY_COMPANY státuszban van az ajánlat
    with col3:
        if status == "CONFIRMED_BY_COMPANY":
            if st.button("Elutasítás", key=f"btn_reject_{offer_id}", type="secondary", use_container_width=use_container_width):
                try:
                    success, result = offers_api.update_offer_status(offer_id, "REJECTED_BY_USER")
                    if success:
                        show_success("Ajánlat elutasítva!")
                        st.session_state.needs_rerun = True
                    else:
                        show_error(f"Hiba elutasításkor: {result}")
                except Exception as e:
                    show_error(f"Hiba elutasításkor: {str(e)}")

    # --- Törlés gomb (csak admin vagy ügyintéző) ---
    # Indok: csak admin vagy ügyintéző, és csak CREATED vagy MODIFIED státuszban
    with col4:
        if user.get("role") in ["admin", "ügyintéző"] and status in ["CREATED", "MODIFIED"]:
            if st.button("Törlés", key=f"btn_delete_{offer_id}", type="secondary", use_container_width=use_container_width):
                confirm = st.checkbox("Erősítse meg a törlést!", key=f"confirm_delete_{offer_id}")
                if confirm:
                    try:
                        success, result = offers_api.delete_offer(offer_id)
                        if success:
                            show_success("Ajánlat törölve!")
                            st.session_state.needs_rerun = True
                        else:
                            show_error(f"Hiba törléskor: {result}")
                    except Exception as e:
                        show_error(f"Hiba törléskor: {str(e)}")



def display_quantity_indicator(offer: Dict[str, Any]) -> None:
    """
    Grafikus mennyiségi indikátor fejlett sötét témával és dinamikus színnel.
    Args:
        offer (dict): Ajánlat adatai
    """
    # Biztonságos konverzió: None esetén vagy érvénytelen érték esetén 0-t használunk
    try:
        qty_in_kg = offer.get('quantity_value', offer.get('quantity_in_kg'))
        original_qty = float(qty_in_kg) if qty_in_kg is not None else 0.0
    except (ValueError, TypeError):
        original_qty = 0.0
        
    try:
        conf_qty = offer.get('confirmed_quantity')
        confirmed_qty = float(conf_qty) if conf_qty is not None else 0.0
    except (ValueError, TypeError):
        confirmed_qty = 0.0
        
    if confirmed_qty > 0:
        percentage = min(100, int((confirmed_qty / original_qty) * 100)) if original_qty > 0 else 0
        if percentage < 50:
            color = "#e74c3c"  # vörös
        elif percentage < 80:
            color = "#f39c12"  # narancssárga
        else:
            color = "#2ecc71"  # zöld
        st.markdown(
            f"""
            <div style='background-color: #1e2230; padding: 16px; border-radius: 8px; margin: 16px 0;'>
                <h4 style='color: #3584e4; margin-top: 0; font-size: 18px; margin-bottom: 12px;'>
                    Mennyiségi arány
                </h4>
                <div style='display: flex; justify-content: space-between; margin-bottom: 4px;'>
                    <span style='color: #a0a0a0;'>Eredeti mennyiség</span>
                    <span style='color: #e0e0e0;'>{format_quantity(original_qty, offer.get('quantity_unit', 'kg'))}</span>
                </div>
                <div style='display: flex; justify-content: space-between; margin-bottom: 8px;'>
                    <span style='color: #a0a0a0;'>Visszaigazolt mennyiség</span>
                    <span style='color: #e0e0e0;'>{format_quantity(confirmed_qty, offer.get('quantity_unit', 'kg'))}</span>
                </div>
                <div style='background-color: #2c3142; border-radius: 4px; height: 12px; margin: 12px 0;'>
                    <div style='width: {percentage}%; background-color: {color}; height: 12px; border-radius: 4px;'></div>
                </div>
                <div style='text-align: center; color: {color}; font-weight: bold;'>
                    {percentage}%
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )


def display_offer_timeline(offer: Dict[str, Any]) -> None:
    """
    Ajánlat idővonal komponens fejlettebb sötét UI-val.
    Ajánlat idővonal komponens időbélyegekkel.
    Args:
        offer (dict): Ajánlat adatai
    """
    events = [
        {"date": offer.get("created_at"), "label": "Létrehozva"},
        {"date": offer.get("updated_at"), "label": "Módosítva"},
        {"date": offer.get("delivery_date"), "label": "Beszállítás"}
    ]
    events = [e for e in events if e["date"]]
    events.sort(key=lambda x: x["date"])
    
    if not events:
        st.info("Nincs idővonal adat.")
        return
    
    # Fejléc
    st.markdown("<h4>Ajánlat idővonala</h4>", unsafe_allow_html=True)
    
    # Rugalmas flexbox alapú idővonal
    timeline_html = """
    <div style='margin: 30px 0; position: relative;'>
        <!-- Vízszintes vonal az idővonalon -->
        <div style='position: absolute; height: 2px; background-color: #e9ecef; width: 100%; top: 12px; z-index: 1;'></div>
        
        <!-- Flex konténer az eseményekhez -->
        <div style='display: flex; justify-content: space-between; position: relative; z-index: 2;'>
    """
    
    # Események generálása
    for event in events:
        timeline_html += f"""
        <div style='flex: 1; text-align: center;'>
            <div style='width: 24px; height: 24px; background-color: #3584e4; border-radius: 50%; margin: 0 auto; position: relative; z-index: 3;'></div>
            <p style='text-align: center; margin-top: 8px; font-size: 12px;'>
                {event['label']}<br>{format_datetime(event['date'])}
            </p>
        </div>
        """
    
    timeline_html += """
        </div>
    </div>
    """
    
    st.markdown(timeline_html, unsafe_allow_html=True)


def display_collapsible_details(offer: Dict[str, Any]) -> None:
    """
    Összecsukható részletek blokk további információkkal.
    Args:
        offer (dict): Ajánlat adatai
    """
    with st.expander("További részletek"):
        col1, col2 = st.columns(2)
        with col1:
            st.write("**Termelő adatai**")
            st.write(f"Név: {offer.get('user', {}).get('company_name', '')}")
            st.write(f"Email: {offer.get('user', {}).get('email', '')}")
            st.write(f"Telefon: {offer.get('user', {}).get('contact_phone', '')}")
        with col2:
            st.write("**Technikai információk**")
            st.write(f"Létrehozva: {format_datetime(offer.get('created_at', ''))}")
            st.write(f"Utoljára módosítva: {format_datetime(offer.get('updated_at', ''))}")


def display_offer_details(offer_id: int) -> None:
    """
    Ajánlat részletes megjelenítése modernebb, áttekinthetőbb UI elemekkel.
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    success, offer = offers_api.get_offer(offer_id)
    if not success or not offer:
        st.error("Az ajánlat nem található!")
        return
    display_offer_status_card(offer)
    display_offer_detail_cards(offer)
    if offer.get("confirmed_quantity") is not None:
        display_quantity_indicator(offer)
    display_offer_timeline(offer)
    display_collapsible_details(offer)