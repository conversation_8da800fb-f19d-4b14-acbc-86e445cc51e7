# Oldalsáv komponens
"""
Oldalsáv komponens.
"""
import streamlit as st
import app_config as config
# <PERSON><PERSON>lmas import a session utils-hoz
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import is_authenticated, get_current_user, clear_session
    # Naplózás hozz<PERSON>ad<PERSON>a a hibakeresés megkönnyítéséhez
    import logging
    logging.info("Successfully imported session utils from Docker path")
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import is_authenticated, get_current_user, clear_session
        import logging
        logging.info("Successfully imported session utils directly")
    except ImportError:
        try:
            # Tel<PERSON><PERSON> (streamlit_app-ból)
            from streamlit_app.utils.session import is_authenticated, get_current_user, clear_session
            import logging
            logging.info("Successfully imported session utils from streamlit_app path")
        except ImportError:
            # Fallback for authentication if import fails
            import logging
            logging.warning("Could not import session utilities, using fallbacks")
            def is_authenticated():
                """Fallback authentication check"""
                return True
            
            def get_current_user():
                """Fallback current user"""
                return {"id": 1, "name": "Test User", "role": "operator", "contact_name": "Test User"}
            
            def clear_session():
                """Fallback session clear"""
                if "user" in st.session_state:
                    del st.session_state.user
import datetime

# Dictionary of page paths and their display names
PAGE_NAMES = {
    "pages/operator_dashboard.py": "🏠 Ügyintézői irányítópult",
    "pages/operator_offers.py": "📋 Ajánlatok kezelése",
    "pages/operator_calendar.py": "📅 Naptári nézet",
    "pages/operator_reports.py": "📊 Összegzés és riportok",
    "pages/operator_calendar_responsive.py": "📱 Responsív naptár",
    "pages/operator_create_offer.py": "📦 Ajánlat létrehozása",
    "pages/producer_dashboard.py": "🏠 Termelői irányítópult",
    "pages/producer_profile.py": "👤 Profil szerkesztése",
    "pages/producer_create_offer.py": "📦 Új ajánlat leadása",
    "pages/producer_offers.py": "📋 Ajánlataim",
    "pages/producer_statistics.py": "📊 Statisztikáim",
    "pages/admin_dashboard.py": "🏠 Admin irányítópult",
    "pages/admin_users.py": "👥 Felhasználók kezelése",
    "pages/admin_products.py": "🍅 POM Termékek",
    "pages/admin_data_generator.py": "🧪 Tesztadat generátor",
    "pages/admin_sitemap.py": "🗺️ Oldaltérkép"
}

def init_favorites():
    """Initialize favorites in session state if not exists"""
    if "user_favorites" not in st.session_state:
        st.session_state.user_favorites = []

def toggle_favorite(page_path):
    """Toggle a page as favorite"""
    if page_path in st.session_state.user_favorites:
        st.session_state.user_favorites.remove(page_path)
    else:
        st.session_state.user_favorites.append(page_path)

def render_page_button(page_path, button_text, container=st, use_container_width=True, with_favorite=True, section_prefix=None):
    """Render a page navigation button with optional favorite star"""
    cols = container.columns([1, 8, 1]) if with_favorite else container.columns([1, 9])
    
    # Create a unique key with optional section prefix
    key_prefix = f"{section_prefix}_" if section_prefix else ""
    
    # Favorite star column
    if with_favorite:
        is_favorite = page_path in st.session_state.user_favorites
        star_emoji = "⭐" if is_favorite else "☆"
        if cols[0].button(star_emoji, key=f"{key_prefix}fav_{page_path}", help="Kedvencekhez adás/eltávolítás"):
            toggle_favorite(page_path)
            st.rerun()
    
    # Page button column
    if cols[1].button(button_text, key=f"{key_prefix}nav_{page_path}", use_container_width=use_container_width):
        st.switch_page(page_path)
    
    # Space column
    pass

def toggle_debug_mode():
    """Debug mód be/kikapcsolása"""
    if "debug_mode" not in st.session_state:
        st.session_state.debug_mode = False
    
    st.session_state.debug_mode = not st.session_state.debug_mode

def render_admin_controls():
    """Admin-specifikus vezérlőelemek renderelése a sidebarban"""
    user = get_current_user()
    if not user or user.get("role", "").lower() != "admin":
        return
    
    st.sidebar.markdown("---")
    st.sidebar.markdown("### Admin Eszközök")
    
    # Debug mód kapcsoló
    if "debug_mode" not in st.session_state:
        st.session_state.debug_mode = False
    
    st.session_state.debug_mode = st.sidebar.checkbox(
        "🛠️ Debug mód", 
        value=st.session_state.debug_mode,
        help="Debug információk megjelenítése az alkalmazásban"
    )

def detect_mobile() -> None:
    """
    Detect if the user is on a mobile device. Adds a manual toggle for admin users.
    Sets st.session_state['mobile_view'] accordingly.
    """
    if "mobile_view" not in st.session_state:
        st.session_state.mobile_view = False
    # Manual toggle for admin
    if is_authenticated() and get_current_user().get("role") == "admin":
        st.sidebar.checkbox("📱 Mobil nézet", key="mobile_view")

def render_mobile_navigation() -> None:
    """
    Render a mobile-friendly navigation sidebar.
    """
    st.markdown("### 📱 Navigáció")
    if is_authenticated():
        user = get_current_user()
        st.caption(f"👤 {user.get('contact_name', '')} ({user.get('role', '').capitalize()})")
        role = user.get("role", "").lower()
        menu_options = ["Főmenü"]
        if st.session_state.user_favorites:
            menu_options.append("⭐ Kedvencek")
        if role == "admin":
            menu_options.extend(["🔧 Admin", "🛠️ Eszközök"])
        selected_menu = st.selectbox("Menü", menu_options, label_visibility="collapsed")
        if selected_menu == "⭐ Kedvencek":
            render_mobile_menu_section(st.session_state.user_favorites)
        # Add more menu logic as needed

def render_mobile_menu_section(page_paths: list) -> None:
    """
    Render a section of menu items for mobile view.
    Args:
        page_paths (list): List of page paths to render as buttons.
    """
    for page_path in page_paths:
        if page_path in PAGE_NAMES:
            button_text = PAGE_NAMES[page_path]
            render_mobile_button(page_path, button_text)

def render_mobile_button(page_path: str, button_text: str) -> None:
    """
    Render a compact mobile navigation button.
    Args:
        page_path (str): The path of the page to navigate to.
        button_text (str): The text to display on the button.
    """
    if st.button(button_text, key=f"mobile_{page_path}", use_container_width=True):
        st.switch_page(page_path)

def render_mobile_header() -> None:
    """
    Render a compact header for mobile view.
    """
    cols = st.columns([8, 2])
    with cols[0]:
        st.write(f"### {config.APP_NAME}")
    with cols[1]:
        if is_authenticated():
            if st.button("🚪", help="Kijelentkezés"):
                clear_session()
                st.rerun()

def render_quick_actions() -> None:
    """
    Render quick action icon buttons for mobile frequently used actions.
    """
    cols = st.columns(4)
    with cols[0]:
        if st.button("🏠", help="Irányítópult", key="mobile_home"):
            role = get_current_user().get("role", "").lower()
            st.switch_page(f"pages/{role}_dashboard.py")
    # Add more quick actions as needed

def render_bottom_navbar() -> None:
    """
    Render a fixed bottom navigation bar for mobile devices.
    """
    st.markdown("""
    <style>
    .bottom-navbar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #23272e;
        padding: 10px;
        display: flex;
        justify-content: space-around;
        border-top: 1px solid #444;
        color: #fff;
        z-index: 1000;
    }
    </style>
    """, unsafe_allow_html=True)
    st.markdown('<div class="bottom-navbar">Mobil navigáció</div>', unsafe_allow_html=True)

def add_sidebar_toggle() -> None:
    """
    Add a button to toggle sidebar visibility on mobile.
    """
    if st.button("≡", key="toggle_sidebar"):
        st.session_state["sidebar_visible"] = not st.session_state.get("sidebar_visible", True)
        st.rerun()

def create_hamburger_menu() -> None:
    """
    Create a hamburger menu using st.expander for mobile navigation.
    """
    with st.expander("≡ Menü", expanded=False):
        role = get_current_user().get("role", "").lower()
        if role == "admin":
            st.markdown("### 🔧 Admin")
            render_mobile_menu_section(["pages/admin_dashboard.py", "pages/admin_users.py"])
        # Add more sections as needed

def render_sidebar() -> None:
    """
    Render the sidebar for the application, supporting both desktop and mobile views.
    """
    init_favorites()
    if "debug_mode" not in st.session_state:
        st.session_state.debug_mode = False
    detect_mobile()
    is_mobile = st.session_state.get("mobile_view", False)
    if is_mobile:
        render_mobile_header()
        render_mobile_navigation()
        render_quick_actions()
        render_bottom_navbar()
        add_sidebar_toggle()
        create_hamburger_menu()
    else:
        # --- DESKTOP LOGIKA MARAD VÁLTOZATLANUL ---
        with st.sidebar:
            # Application header
            st.title(config.APP_NAME)
            # Current day info
            current_date = datetime.datetime.now()
            st.caption(f"📅 {current_date.strftime('%Y. %m. %d.')} - {get_hungarian_day(current_date.weekday())}")
            st.text(f"Üzemeltető: {config.COMPANY_NAME}")
            st.divider()
            if not is_authenticated():
                with st.container():
                    # Login button
                    if st.button("🔑 Bejelentkezés", key="login_btn", use_container_width=True):
                        st.switch_page("main.py")
                
                    # Registration button
                    if st.button("📝 Regisztráció", key="register_btn", use_container_width=True):
                        st.switch_page("pages/auth_register.py")
                
                    # Forgot password
                    if st.button("🔄 Elfelejtett jelszó", key="forgot_pw_btn", use_container_width=True):
                        st.switch_page("pages/auth_reset_password.py")
                    
                    # PWA Installer link
                    if st.button("📱 App Telepítő", key="pwa_installer_btn", use_container_width=True):
                        st.switch_page("pages/pwa_installer.py")
                
                    # Info section
                    st.divider()
                    info_container = st.container(border=True)
                    with info_container:
                        st.info("ℹ️ Először jár itt?\n\nIsmerje meg az alkalmazás működését lépésről lépésre.")
                    
                        if st.button("📋 Alkalmazás bemutatása", key="app_intro_btn", use_container_width=True):
                            st.switch_page("pages/info_app_guide.py")
                
                    # Contact
                    st.text("💌 Kapcsolat: <EMAIL>")
                    st.text("📱 Verzió: 1.1.0 | Frissítve: 2025.07.10")
            else:
                # Logged in user information
                user = get_current_user()
                email = user.get("email", "")
                role = user.get("role", "").lower()
                name = user.get("contact_name", "")
            
                # User profile container
                user_container = st.container(border=True)
                with user_container:
                    if name:
                        st.write(f"👋 Üdvözöljük, {name}!")
                    else:
                        st.write(f"👋 Üdvözöljük!")
                
                    # Email display
                    st.caption(f"📧 {email}")
                
                    # Role
                    role_display = "Termelő"
                    role_emoji = "🌱"
                    if role == "admin":
                        role_display = "Adminisztrátor"
                        role_emoji = "🔧"
                    elif role == "ügyintéző":
                        role_display = "Ügyintéző"
                        role_emoji = "📊"
                
                    st.caption(f"{role_emoji} Szerepkör: {role_display}")
            
                # Favorites section if any favorites exist
                if st.session_state.user_favorites:
                    with st.expander("⭐ Kedvencek", expanded=True):
                        for page_path in st.session_state.user_favorites:
                            if page_path in PAGE_NAMES:
                                render_page_button(page_path, PAGE_NAMES[page_path], with_favorite=False, section_prefix="fav")
            
                st.divider()
            
                # Menu based on role
                if role == "admin":
                    # Admin menu
                    with st.expander("🔧 Admin menü", expanded=True):
                        render_page_button("pages/admin_dashboard.py", "🏠 Irányítópult", section_prefix="admin")
                        render_page_button("pages/admin_users.py", "👥 Felhasználók kezelése", section_prefix="admin")
                        render_page_button("pages/admin_products.py", "🍅 POM Termékek", section_prefix="admin")
                
                    # Admin tools (collapsed by default)
                    with st.expander("🛠️ Admin eszközök", expanded=False):
                        render_page_button("pages/admin_data_generator.py", "🧪 Tesztadat generátor", section_prefix="admin_tools")
                        render_page_button("pages/admin_sitemap.py", "🗺️ Oldaltérkép", section_prefix="admin_tools")
                
                    # Producer functions (collapsed)
                    with st.expander("🌱 Termelői funkciók", expanded=False):
                        render_page_button("pages/producer_dashboard.py", "🏠 Termelői irányítópult", section_prefix="admin_producer")
                        render_page_button("pages/producer_profile.py", "👤 Profil szerkesztése", section_prefix="admin_producer")
                        render_page_button("pages/producer_create_offer.py", "📦 Új ajánlat leadása", section_prefix="admin_producer")
                        render_page_button("pages/producer_offers.py", "📋 Ajánlataim", section_prefix="admin_producer")
                        render_page_button("pages/producer_statistics.py", "📊 Statisztikáim", section_prefix="admin_producer")
                
                    # Operator functions (collapsed)
                    with st.expander("📊 Ügyintézői funkciók", expanded=False):
                        render_page_button("pages/operator_dashboard.py", "🏠 Ügyintézői irányítópult", section_prefix="admin_operator")
                        render_page_button("pages/operator_create_offer.py", "📦 Ajánlat létrehozása", section_prefix="admin_operator")
                        render_page_button("pages/operator_offers.py", "📋 Ajánlatok kezelése", section_prefix="admin_operator")
                        render_page_button("pages/operator_calendar.py", "📅 Naptári nézet", section_prefix="admin_operator")
                        render_page_button("pages/operator_calendar_responsive.py", "📱 Responsív naptár", section_prefix="admin_operator")
                        render_page_button("pages/operator_reports.py", "📊 Összegzés és riportok", section_prefix="admin_operator")
                
                    # Debug mód kapcsoló és státusz admin számára
                    if st.session_state.get("debug_mode", False):
                        st.sidebar.warning("🛠️ Debug mód AKTÍV!", icon="⚠️")
                    with st.expander("🐞 Fejlesztői beállítások", expanded=False):
                        debug_status = "Bekapcsolva ✅" if st.session_state.debug_mode else "Kikapcsolva ❌"
                        if st.button(f"Debug mód: {debug_status}", key="debug_toggle", use_container_width=True):
                            toggle_debug_mode()
                            st.rerun()

                elif role == "ügyintéző":
                    # Operator menu
                    with st.expander("📊 Ügyintézői menü", expanded=True):
                        render_page_button("pages/operator_dashboard.py", "🏠 Irányítópult", section_prefix="operator")
                        render_page_button("pages/operator_create_offer.py", "📦 Ajánlat létrehozása", section_prefix="operator")
                        render_page_button("pages/operator_offers.py", "📋 Ajánlatok kezelése", section_prefix="operator")
                        render_page_button("pages/operator_calendar.py", "📅 Naptári nézet", section_prefix="operator")
                        render_page_button("pages/operator_calendar_responsive.py", "📱 Responsív naptár", section_prefix="operator")
                        render_page_button("pages/operator_reports.py", "📊 Összegzés és riportok", section_prefix="operator")
                elif role == "termelő":
                    # Producer menu
                    with st.expander("🌱 Termelői menü", expanded=True):
                        render_page_button("pages/producer_dashboard.py", "🏠 Termelői irányítópult", section_prefix="producer")
                        render_page_button("pages/producer_profile.py", "👤 Profil szerkesztése", section_prefix="producer")
                        render_page_button("pages/producer_create_offer.py", "📦 Új ajánlat leadása", section_prefix="producer")
                        render_page_button("pages/producer_offers.py", "📋 Ajánlataim", section_prefix="producer")
                        render_page_button("pages/producer_statistics.py", "📊 Statisztikáim", section_prefix="producer")
            
                st.divider()
            
                # Show current time
                st.caption(f"🕒 Pontos idő: {datetime.datetime.now().strftime('%H:%M:%S')}")
            
                # Logout button
                logout_container = st.container(border=True)
                with logout_container:
                    if st.button("🚪 Kijelentkezés", type="primary", key="logout_btn", use_container_width=True):
                        clear_session()
                        st.rerun()

    # Csak admin számára jelenjen meg a debug mód kapcsoló és admin vezérlők
    user = get_current_user()
    if user and user.get("role", "").lower() == "admin":
        render_admin_controls()

def get_hungarian_day(weekday):
    """Get Hungarian day name from weekday number (0-6)"""
    days = ["Hétfő", "Kedd", "Szerda", "Csütörtök", "Péntek", "Szombat", "Vasárnap"]
    return days[weekday]
