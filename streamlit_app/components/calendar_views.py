"""
Naptár nézetek implementációja.

Ez a modul tartalmazza a különböző naptár nézetek osztályait, amelyek a calendar.py oldal számára biztosítanak
modularizált megjelenítési lehetőségeket.
"""
import streamlit as st
import pandas as pd
import datetime
from datetime import timedelta
import plotly.express as px
from components.calendar_component import render_css_calendar, render_interactive_calendar, render_card_calendar
from components.calendar_card import calendar_day_card, event_card, summary_card
from utils.responsive_ui import create_responsive_columns, detect_mobile, get_theme_colors
from utils.formatting import format_quantity, format_price


class CalendarView:
    """
    Naptár nézet alaposztály.
    
    Ez az osztály a közös funkcionalitást biztosítja a különböző naptár nézetek számára,
    mint például az események előkészítése, összesítő metrikák megjelenítése és naptár renderel<PERSON>e.
    """
    
    def __init__(self, data, date_range, use_interactive=False, card_only=False):
        """
        Naptár nézet inicializálása.
        
        Args:
            data (pd.DataFrame): Naptári adatok DataFrame-ben
            date_range (tuple): (start_date, end_date) - dátum tartomány
            use_interactive (bool): Interaktív naptár használata
            card_only (bool): Csak kártyaalapú nézet használata (minden eszközön)
        """
        self.data = data
        self.start_date, self.end_date = date_range
        self.use_interactive = use_interactive
        self.is_mobile = st.session_state.get("is_mobile", False)
        self.card_only = card_only
        self.check_if_dataframe_valid()
        
    def check_if_dataframe_valid(self):
        """
        Ellenőrzi, hogy a DataFrame érvényes-e, és szükség esetén inicializálja.
        """
        if self.data is None or not isinstance(self.data, pd.DataFrame):
            self.data = pd.DataFrame()
        
    def prepare_events(self):
        """
        Adatok konvertálása naptár események formátumára.
        
        Returns:
            list: Naptár események listája
        """
        calendar_events = []
        
        if self.data.empty:
            return calendar_events
            
        for _, row in self.data.iterrows():
            try:
                if 'delivery_date' not in row:
                    continue
                    
                delivery_date = row['delivery_date']
                if isinstance(delivery_date, str):
                    delivery_date = datetime.datetime.strptime(delivery_date, '%Y-%m-%d').date()
                elif isinstance(delivery_date, datetime.datetime):
                    delivery_date = delivery_date.date()
                
                # Állapot alapú színtérkép
                status = row.get('status', '')
                color_map = {
                    'CREATED': '#90CAF9',             # világoskék
                    'CONFIRMED_BY_COMPANY': '#66BB6A',  # zöld
                    'ACCEPTED_BY_USER': '#4CAF50',    # sötétebb zöld
                    'REJECTED_BY_USER': '#EF5350',    # piros
                    'FINALIZED': '#9C27B0'            # lila
                }
                
                color = color_map.get(status, '#9E9E9E')  # alapértelmezetten szürke
                
                # Termék típus és mennyiség megszerzése
                product_type_id = row.get('product_type_id', '')
                product_name = row.get('product_name', product_type_id)
                quantity = float(row.get('quantity_in_kg', 0))
                
                # Állapot fordítása magyarra
                status_translation = {
                    'CREATED': 'Létrehozva',
                    'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
                    'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
                    'REJECTED_BY_USER': 'Felhasználó által elutasítva',
                    'FINALIZED': 'Véglegesítve'
                }
                status_display = status_translation.get(status, status)
                
                # Esemény objektum összeállítása
                event = {
                    'id': row.get('id', ''),
                    'date': delivery_date.strftime('%Y-%m-%d'),
                    'title': f"{product_name}: {quantity:.1f} kg",
                    'quantity': quantity,
                    'color': color,
                    'status': status,
                    'status_display': status_display,
                    'note': row.get('note', ''),
                    'user_name': row.get('user_name', ''),
                    'product_name': product_name,
                    'price': float(row.get('confirmed_price', 0)) / quantity if quantity > 0 else 0
                }
                
                calendar_events.append(event)
            except Exception as e:
                st.error(f"Hiba az esemény feldolgozása során: {str(e)}")
                continue
                
        return calendar_events
    
    def show_summary_metrics(self):
        """
        Összesítő metrikák megjelenítése a kiválasztott időszakra.
        """
        if self.data.empty:
            st.info("Nincs megjeleníthető adat a kiválasztott időszakban.")
            return
            
        total_quantity = self.data['quantity_in_kg'].sum() if 'quantity_in_kg' in self.data.columns else 0
        total_value = self.data['confirmed_price'].sum() if 'confirmed_price' in self.data.columns else 0
        
        # Összesítő metrikák megjelenítése
        st.subheader("Összesítő metrikák")
        
        # Reszponzív oszlopok használata
        cols = create_responsive_columns([1, 1])
        
        with cols[0]:
            st.metric("Teljes mennyiség", f"{total_quantity:,.2f} kg")
        with cols[1]:
            st.metric("Teljes érték", f"{total_value:,.0f} Ft")
            
        # További metrikák számítása és megjelenítése
        unique_products = self.data['product_type_id'].nunique() if 'product_type_id' in self.data.columns else 0
        unique_users = self.data['user_id'].nunique() if 'user_id' in self.data.columns else 0
        
        cols = create_responsive_columns([1, 1, 1])
        
        with cols[0]:
            st.metric("Ajánlatok száma", f"{len(self.data)}")
        with cols[1]:
            st.metric("Egyedi termékek", f"{unique_products}")
        with cols[2]:
            st.metric("Egyedi felhasználók", f"{unique_users}")
    
    def render_calendar(self, calendar_events, reference_date):
        """
        Naptár renderelése az eseményekkel.
        
        Args:
            calendar_events (list): Naptár események listája
            reference_date (datetime.date): Referenciadátum a naptár megjelenítéséhez
        """
        # Ha card_only beállítása true, vagy mobileszközt használunk, akkor kártyaalapú nézetet használunk
        if self.card_only or self.is_mobile:
            render_card_calendar(calendar_events, (self.start_date, self.end_date))
        elif self.use_interactive and not self.is_mobile:
            # Interaktív naptár - csak asztali nézetben
            render_interactive_calendar(calendar_events, reference_date)
        else:
            # Egyszerű CSS naptár
            render_css_calendar(calendar_events, reference_date)
    
    def show_data_table(self, filtered_data=None):
        """
        Részletes adattáblázat megjelenítése.
        
        Args:
            filtered_data (pd.DataFrame, optional): Szűrt adatok. None esetén a teljes adatkészlet.
        """
        st.subheader("Részletes adatok")
        data_to_show = filtered_data if filtered_data is not None else self.data
        
        if data_to_show.empty:
            st.info("Nincs megjeleníthető adat.")
            return
            
        # Oszlopok átnevezése a jobb olvashatóság érdekében
        if not data_to_show.empty:
            columns_to_display = data_to_show.copy()
            column_map = {
                'delivery_date': 'Szállítási dátum',
                'product_type_id': 'Termék típus',
                'product_name': 'Termék neve',
                'quantity_in_kg': 'Mennyiség (kg)',
                'status': 'Állapot',
                'user_id': 'Felhasználó ID',
                'user_name': 'Felhasználó',
                'confirmed_price': 'Megerősített ár',
                'note': 'Megjegyzés'
            }
            columns_to_display = columns_to_display.rename(columns={k: v for k, v in column_map.items() if k in columns_to_display.columns})
            
            # Dátumok formázása
            if 'Szállítási dátum' in columns_to_display.columns:
                columns_to_display['Szállítási dátum'] = columns_to_display['Szállítási dátum'].dt.strftime('%Y-%m-%d')
            
            # Állapot fordítása magyarra
            if 'Állapot' in columns_to_display.columns:
                status_translation = {
                    'CREATED': 'Létrehozva',
                    'CONFIRMED_BY_COMPANY': 'Cég jóváhagyta',
                    'ACCEPTED_BY_USER': 'Felhasználó elfogadta',
                    'REJECTED_BY_USER': 'Felhasználó elutasította',
                    'FINALIZED': 'Véglegesítve'
                }
                columns_to_display['Állapot'] = columns_to_display['Állapot'].map(lambda x: status_translation.get(x, x))
                
            st.dataframe(columns_to_display, use_container_width=True)
            
            # Exportálási lehetőség biztosítása
            st.download_button(
                label="Táblázat exportálása CSV-be",
                data=columns_to_display.to_csv(index=False).encode('utf-8'),
                file_name=f"naptar_adatok_{self.start_date}_{self.end_date}.csv",
                mime="text/csv"
            )
            
            # Excel exportálás, ha szükséges
            if "xlsx" not in st.session_state:
                try:
                    import io
                    from xlsxwriter import Workbook
                    
                    # Excel fájl létrehozása memóriában
                    output = io.BytesIO()
                    workbook = Workbook(output, {'in_memory': True})
                    worksheet = workbook.add_worksheet()
                    
                    # Fejléc
                    for col_num, column_name in enumerate(columns_to_display.columns):
                        worksheet.write(0, col_num, column_name)
                    
                    # Adatok
                    for row_num, row in enumerate(columns_to_display.values):
                        for col_num, cell_value in enumerate(row):
                            worksheet.write(row_num + 1, col_num, cell_value)
                    
                    workbook.close()
                    output.seek(0)
                    
                    st.download_button(
                        label="Táblázat exportálása Excel-be",
                        data=output,
                        file_name=f"naptar_adatok_{self.start_date}_{self.end_date}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
                except ImportError:
                    # Ha a xlsxwriter nem elérhető, ne jelenítse meg az Excel exportálás gombot
                    pass
        
    def show(self):
        """
        Naptár nézet megjelenítése (absztrakt metódus).
        
        Ezt a metódust az alosztályoknak kell implementálniuk.
        """
        raise NotImplementedError("Az alosztályoknak implementálniuk kell ezt a metódust")


class DailyView(CalendarView):
    """
    Napi naptár nézet.
    """
    
    def show(self):
        """
        Napi nézet megjelenítése.
        """
        # A kiválasztott nap a start_date
        selected_date = self.start_date
        
        # Összesítő metrikák megjelenítése
        self.show_summary_metrics()
        
        # Adat szűrése a kiválasztott napra
        if not self.data.empty:
            # Ellenőrizzük, hogy a delivery_date oszlop létezik
            if 'delivery_date' in self.data.columns:
                day_mask = self.data['delivery_date'].dt.date == selected_date
                daily_data = self.data[day_mask]
                
                if daily_data.empty:
                    st.info(f"Nincs esemény a kiválasztott napra: {selected_date}")
                    # Üres naptár megjelenítése
                    empty_events = []
                    self.render_calendar(empty_events, selected_date)
                    return
                    
                # Események előkészítése és naptár megjelenítése
                calendar_events = self.prepare_events()
                self.render_calendar(calendar_events, selected_date)
                
                # Részletes adatok megjelenítése
                self.show_data_table(daily_data)
            else:
                st.error("Hiba: Az adatok nem tartalmazzák a 'delivery_date' oszlopot")
        else:
            st.info(f"Nincs esemény a kiválasztott napra: {selected_date}")
            # Üres naptár megjelenítése
            empty_events = []
            self.render_calendar(empty_events, selected_date)


class WeeklyView(CalendarView):
    """
    Heti naptár nézet.
    """
    
    def show(self):
        """
        Heti nézet megjelenítése.
        """
        # Hét kezdete és vége biztosítása
        week_start = self.start_date
        week_end = self.end_date
        
        # Összesítő metrikák megjelenítése
        self.show_summary_metrics()
        
        # Napi bontású grafikonok megjelenítése
        if not self.data.empty and 'delivery_date' in self.data.columns:
            # Események számának és mennyiségének napi bontása
            daily_counts = self.data.groupby(self.data['delivery_date'].dt.date).size().reset_index(name='Ajánlatok száma')
            daily_quantities = self.data.groupby(self.data['delivery_date'].dt.date).agg({'quantity_in_kg': 'sum'}).reset_index()
            daily_quantities.columns = ['delivery_date', 'Mennyiség (kg)']
            
            # Csak a hét napjait megjelnítjük (néha a csoportosítás más napokat is tartalmazhat)
            daily_counts = daily_counts[(daily_counts['delivery_date'] >= week_start) & (daily_counts['delivery_date'] <= week_end)]
            daily_quantities = daily_quantities[(daily_quantities['delivery_date'] >= week_start) & (daily_quantities['delivery_date'] <= week_end)]
            
            # Grafikonok megjelenítése egymás mellett
            st.subheader("Heti összesítés")
            cols = create_responsive_columns([1, 1])
            
            with cols[0]:
                fig1 = px.bar(
                    daily_counts,
                    x='delivery_date',
                    y='Ajánlatok száma',
                    labels={'delivery_date': 'Dátum', 'Ajánlatok száma': 'Ajánlatok száma'},
                    title='Ajánlatok napi bontásban'
                )
                fig1.update_layout(xaxis_tickformat='%Y-%m-%d')
                st.plotly_chart(fig1, use_container_width=True)
                
            with cols[1]:
                fig2 = px.bar(
                    daily_quantities,
                    x='delivery_date',
                    y='Mennyiség (kg)',
                    labels={'delivery_date': 'Dátum', 'Mennyiség (kg)': 'Mennyiség (kg)'},
                    title='Mennyiségek napi bontásban'
                )
                fig2.update_layout(xaxis_tickformat='%Y-%m-%d')
                st.plotly_chart(fig2, use_container_width=True)
        
        # Események előkészítése és naptár megjelenítése
        calendar_events = self.prepare_events()
        self.render_calendar(calendar_events, week_start)
        
        # Részletes adatok megjelenítése
        self.show_data_table()


class MonthlyView(CalendarView):
    """
    Havi naptár nézet.
    """
    
    def show(self):
        """
        Havi nézet megjelenítése.
        """
        # Összesítő metrikák megjelenítése
        self.show_summary_metrics()
        
        # Grafikonok megjelenítése
        if not self.data.empty and 'delivery_date' in self.data.columns:
            # Heti bontású adatok
            self.data['week'] = self.data['delivery_date'].dt.isocalendar().week
            weekly_data = self.data.groupby('week').agg({
                'id': 'count',
                'quantity_in_kg': 'sum',
                'confirmed_price': 'sum'
            }).reset_index()
            weekly_data.columns = ['Hét', 'Ajánlatok száma', 'Mennyiség (kg)', 'Érték']
            
            # Termék típus szerinti bontás
            if 'product_type_id' in self.data.columns:
                product_data = self.data.groupby('product_type_id').agg({
                    'quantity_in_kg': 'sum'
                }).reset_index().sort_values('quantity_in_kg', ascending=False)
                product_data.columns = ['Termék', 'Mennyiség (kg)']
                
                # Top 5 termék kiválasztása
                if len(product_data) > 5:
                    top_products = product_data.head(5)
                    other_quantity = product_data.iloc[5:]['Mennyiség (kg)'].sum()
                    other_row = pd.DataFrame([{'Termék': 'Egyéb', 'Mennyiség (kg)': other_quantity}])
                    product_data = pd.concat([top_products, other_row])
                
                # Grafikonok megjelenítése
                st.subheader("Havi összesítés")
                cols = create_responsive_columns([1, 1])
                
                with cols[0]:
                    fig1 = px.bar(
                        weekly_data,
                        x='Hét',
                        y='Mennyiség (kg)',
                        title='Heti mennyiségek'
                    )
                    st.plotly_chart(fig1, use_container_width=True)
                    
                with cols[1]:
                    fig2 = px.pie(
                        product_data,
                        values='Mennyiség (kg)',
                        names='Termék',
                        title='Termékek megoszlása'
                    )
                    st.plotly_chart(fig2, use_container_width=True)
        
        # Események előkészítése és naptár megjelenítése
        calendar_events = self.prepare_events()
        self.render_calendar(calendar_events, self.start_date)
        
        # Részletes adatok megjelenítése
        self.show_data_table()


class CustomView(CalendarView):
    """
    Egyedi időtartamú naptár nézet.
    """
    
    def show(self):
        """
        Egyedi nézet megjelenítése.
        """
        # Összesítő metrikák megjelenítése
        self.show_summary_metrics()
        
        # Események előkészítése
        calendar_events = self.prepare_events()
        
        # Ha nincs esemény, üzenet megjelenítése
        if not calendar_events:
            st.info("Nincs esemény a kiválasztott időszakban.")
        
        # Naptár megjelenítése - itt előnyben részesítjük a kártyaalapú nézetet, különösen hosszabb időszakok esetén
        date_difference = (self.end_date - self.start_date).days
        use_card_view = self.card_only or self.is_mobile or date_difference > 14
        
        # Naptár vagy kártyás nézet megjelenítése
        if use_card_view:
            render_card_calendar(calendar_events, (self.start_date, self.end_date))
        else:
            # Interaktív vagy egyszerű naptár - középdátumot választjuk referenciaként
            mid_date = self.start_date + (self.end_date - self.start_date) // 2
            
            if self.use_interactive and not self.is_mobile:
                render_interactive_calendar(calendar_events, mid_date)
            else:
                render_css_calendar(calendar_events, mid_date)
        
        # Részletes adatok megjelenítése
        self.show_data_table() 