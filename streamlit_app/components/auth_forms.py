# Bejelentkezési/regisztrációs űrlapok
"""
Autentikációs űrlapok komponensei.
"""
import streamlit as st
from api import auth
from utils.auth_utils import validate_email, validate_password, validate_tax_id
from utils.validators import validate_phone, validate_required, validate_length
from components.notification import show_error, show_success, show_info

def login_form():
    """
    Bejelentkezési űrlap komponens.
    
    Returns:
        bool: Siker<PERSON> bejelentkez<PERSON> esetén True, egyébként False
    """
    st.subheader("Bejelentkezés")
    
    with st.form("login_form"):
        email = st.text_input("E-mail cím", placeholder="<EMAIL>")
        password = st.text_input("Jelszó", type="password")
        submit = st.form_submit_button("Bejelentkezés", type="primary", use_container_width=True)
    
    if submit:
        if not email or not password:
            show_error("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg az e-mail címet és a jelszót.")
            return False
        
        if not validate_email(email):
            show_error("Érvénytelen e-mail cím formátum.")
            return False
        
        # Bejelentkezés API hívás
        success, result = auth.login(email, password)
        
        if success:
            show_success("Sikeres bejelentkezés!")
            return True
        else:
            show_error(f"Bejelentkezési hiba: {result}")
            return False
    
    return False

def register_form():
    """
    Regisztrációs űrlap komponens.
    
    Returns:
        bool: Sikeres regisztráció esetén True, egyébként False
    """
    st.subheader("Regisztráció")
    
    with st.form("register_form"):
        st.info("A csillaggal (*) jelölt mezők kitöltése kötelező.")
        
        # Személyes adatok
        st.subheader("Személyes adatok")
        contact_name = st.text_input("Kapcsolattartó neve *", placeholder="Teljes név")
        email = st.text_input("E-mail cím *", placeholder="<EMAIL>")
        phone_number = st.text_input("Telefonszám *", placeholder="+36 12 345 6789")
        
        # Cégadatok
        st.subheader("Cégadatok")
        is_company = st.checkbox("Cég nevében regisztrálok")
        company_name = st.text_input("Cégnév", placeholder="Cégnév", 
                                    help="Csak céges regisztráció esetén töltse ki")
        tax_id = st.text_input("Adószám", placeholder="12345678-1-23",
                              help="Csak céges regisztráció esetén töltse ki")
        
        # Jelszó
        st.subheader("Jelszó")
        password = st.text_input("Jelszó *", type="password", 
                                help="A jelszónak legalább 8 karakter hosszúnak kell lennie, tartalmaznia kell betűt és számot is.")
        password_confirm = st.text_input("Jelszó megerősítése *", type="password")
        
        # ÁSZF és regisztráció
        terms_accepted = st.checkbox("Elfogadom az Általános Szerződési Feltételeket *")
        submit = st.form_submit_button("Regisztráció", type="primary", use_container_width=True)
    
    if submit:
        # Adatok validálása
        validation_errors = []
        
        # Kötelező mezők ellenőrzése
        for field_name, field_value, display_name in [
            ("contact_name", contact_name, "Kapcsolattartó neve"),
            ("email", email, "E-mail cím"),
            ("phone_number", phone_number, "Telefonszám"),
            ("password", password, "Jelszó"),
            ("password_confirm", password_confirm, "Jelszó megerősítése")
        ]:
            is_valid, error = validate_required(field_value, display_name)
            if not is_valid:
                validation_errors.append(error)
        
        # Céges adatok ellenőrzése, ha céges regisztráció
        if is_company:
            if not company_name:
                validation_errors.append("Cégnév megadása kötelező céges regisztráció esetén.")
            if not tax_id:
                validation_errors.append("Adószám megadása kötelező céges regisztráció esetén.")
            elif not validate_tax_id(tax_id):
                validation_errors.append("Érvénytelen adószám formátum. Helyes formátum: 12345678-1-23")
        
        # E-mail formátum ellenőrzése
        if email and not validate_email(email):
            validation_errors.append("Érvénytelen e-mail cím formátum.")
        
        # Telefonszám formátum ellenőrzése
        if phone_number:
            is_valid, error = validate_phone(phone_number)
            if not is_valid:
                validation_errors.append(error)
        
        # Jelszó ellenőrzése
        if password:
            is_valid, error = validate_password(password)
            if not is_valid:
                validation_errors.append(error)
        
        # Jelszó egyezés ellenőrzése
        if password and password_confirm and password != password_confirm:
            validation_errors.append("A megadott jelszavak nem egyeznek.")
        
        # ÁSZF elfogadásának ellenőrzése
        if not terms_accepted:
            validation_errors.append("Az Általános Szerződési Feltételek elfogadása kötelező.")
        
        # Ha van validációs hiba, megjelenítjük
        if validation_errors:
            for error in validation_errors:
                show_error(error)
            return False
        
        # Regisztrációs adatok összeállítása
        user_data = {
            "email": email,
            "password": password,
            "contact_name": contact_name,
            "phone_number": phone_number,
            "role": "termelő"  # Alapértelmezetten termelőként regisztrálunk
        }
        
        if is_company:
            user_data["company_name"] = company_name
            user_data["tax_id"] = tax_id
        
        # Regisztráció API hívás
        success, result = auth.register(user_data)
        
        if success:
            show_success("Sikeres regisztráció! Kérjük, erősítse meg e-mail címét a kiküldött linkre kattintva.")
            return True
        else:
            show_error(f"Regisztrációs hiba: {result}")
            return False
    
    return False

def password_reset_request_form():
    """
    Jelszó-visszaállítási kérelem űrlap komponens.
    
    Returns:
        bool: Sikeres kérelem esetén True, egyébként False
    """
    st.subheader("Jelszó-visszaállítás")
    st.write("Adja meg az e-mail címét, amelyre a jelszó-visszaállítási linket küldjük.")
    
    with st.form("password_reset_form"):
        email = st.text_input("E-mail cím", placeholder="<EMAIL>")
        submit = st.form_submit_button("Jelszó-visszaállítási link kérése", type="primary", use_container_width=True)
    
    if submit:
        if not email:
            show_error("Kérjük, adja meg az e-mail címet.")
            return False
        
        if not validate_email(email):
            show_error("Érvénytelen e-mail cím formátum.")
            return False
        
        # Jelszó-visszaállítási kérelem API hívás
        success, result = auth.request_password_reset(email)
        
        if success:
            show_success(result)
            return True
        else:
            show_error(f"Hiba: {result}")
            return False
    
    return False

def password_reset_form(token):
    """
    Jelszó-visszaállítási űrlap komponens.
    
    Args:
        token (str): Jelszó-visszaállítási token
        
    Returns:
        bool: Sikeres visszaállítás esetén True, egyébként False
    """
    st.subheader("Új jelszó beállítása")
    st.write("Adja meg az új jelszavát.")
    
    with st.form("new_password_form"):
        password = st.text_input("Új jelszó", type="password", 
                                 help="A jelszónak legalább 8 karakter hosszúnak kell lennie, tartalmaznia kell betűt és számot is.")
        password_confirm = st.text_input("Új jelszó megerősítése", type="password")
        submit = st.form_submit_button("Jelszó módosítása", type="primary", use_container_width=True)
    
    if submit:
        if not password or not password_confirm:
            show_error("Kérjük, adja meg az új jelszót és erősítse meg.")
            return False
        
        # Jelszó ellenőrzése
        is_valid, error = validate_password(password)
        if not is_valid:
            show_error(error)
            return False
        
        # Jelszó egyezés ellenőrzése
        if password != password_confirm:
            show_error("A megadott jelszavak nem egyeznek.")
            return False
        
        # Jelszó-visszaállítás API hívás
        success, result = auth.reset_password(token, password)
        
        if success:
            show_success("Jelszava sikeresen megváltozott. Most már bejelentkezhet az új jelszavával.")
            return True
        else:
            show_error(f"Hiba: {result}")
            return False
    
    return False
