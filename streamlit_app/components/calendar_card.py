"""
Naptárnézet kártya komponensek a reszponzív felülethez.
"""
import streamlit as st
from utils.responsive_ui import display_card, get_theme_colors
from utils.formatting import format_quantity, format_price

def event_card(event, is_dark=None):
    """
    Esemény kártya megjelenítése.
    
    Args:
        event (dict): Esemény adatai
        is_dark (bool, optional): Sö<PERSON>t téma hasz<PERSON>a. Alapértelmezetten a session state-ből.
    """
    if is_dark is None:
        is_dark = st.session_state.get("theme", "light") == "dark"
    
    # Állapot színének meghatározása
    status_colors = {
        'várható': '#FFA726',  # narancs
        'visszaigazolt': '#66BB6A',  # zöld
        'elutasított': '#EF5350',  # piros
        'feldolgozás alatt': '#42A5F5',  # kék
        'törölt': '#9E9E9E',  # szürke
        # API státusz kódok
        'created': '#90CAF9',
        'confirmed_by_company': '#66BB6A',
        'accepted_by_user': '#4CAF50',
        'rejected_by_user': '#EF5350',
        'finalized': '#9C27B0'
    }
    
    status = event.get('status', '').lower()
    status_color = status_colors.get(status, '#9E9E9E')
    
    # Kártya tartalom
    content = f"""
    <div style="margin-bottom: 8px;">
        <span style="display:inline-block; width:12px; height:12px; border-radius:50%; background-color:{status_color}; margin-right:5px;"></span>
        <span style="font-weight:bold; color:{status_color};">{event.get('status', 'Ismeretlen')}</span>
    </div>
    <p><strong>Termék:</strong> {event.get('product_name', 'Ismeretlen')}</p>
    <p><strong>Mennyiség:</strong> {format_quantity(event.get('quantity', 0))}</p>
    """
    
    # Ár hozzáadása, ha van
    if event.get('price', 0) > 0:
        content += f"<p><strong>Ár:</strong> {format_price(event.get('price', 0))}</p>"
    
    # Termelő és megjegyzés hozzáadása
    content += f"<p><strong>Termelő:</strong> {event.get('user_name', 'Ismeretlen')}</p>"
    
    if event.get('note'):
        content += f"<p><strong>Megjegyzés:</strong> {event.get('note')}</p>"
    
    # Kártya megjelenítése
    display_card(
        title=event.get('title', 'Esemény'),
        content=content,
        icon="📦"
    )

def summary_card(title, data, icon="📊", is_dark=None):
    """
    Összesítő kártya megjelenítése.
    
    Args:
        title (str): Kártya címe
        data (dict): Összesítő adatok
        icon (str, optional): Ikon. Defaults to "📊".
        is_dark (bool, optional): Sötét téma használata. Alapértelmezetten a session state-ből.
    """
    if is_dark is None:
        is_dark = st.session_state.get("theme", "light") == "dark"
    
    # HTML tartalom összeállítása
    content = "<div style='display:flex; flex-direction:column; gap:10px;'>"
    
    # Időszak megjelenítése, ha van
    if 'időszak' in data:
        content += f"<p><strong>Időszak:</strong> {data['időszak']}</p>"
    
    # Kötelező elemek
    content += f"""
    <p><strong>Összes mennyiség:</strong> {format_quantity(data.get('mennyiség', 0))}</p>
    <p><strong>Összes érték:</strong> {format_price(data.get('érték', 0))}</p>
    <p><strong>Események száma:</strong> {data.get('események_száma', 0)}</p>
    """
    
    # Opcionális elemek
    if 'termékek_száma' in data:
        content += f"<p><strong>Termékek száma:</strong> {data['termékek_száma']}</p>"
    
    if 'termelők_száma' in data:
        content += f"<p><strong>Termelők száma:</strong> {data['termelők_száma']}</p>"
    
    content += "</div>"
    
    # Kártya megjelenítése
    display_card(title=title, content=content, icon=icon)

def calendar_day_card(date, events, is_dark=None):
    """
    Naptári nap kártya megjelenítése.
    
    Args:
        date (str): Dátum (YYYY-MM-DD) vagy dátum objektum
        events (list): Események listája a naphoz
        is_dark (bool, optional): Sötét téma használata. Alapértelmezetten a session state-ből.
    """
    if is_dark is None:
        is_dark = st.session_state.get("theme", "light") == "dark"
    
    is_mobile = st.session_state.get("is_mobile", False)
    colors = get_theme_colors()
    
    # Dátum formázása
    if hasattr(date, 'strftime'):
        date_str = date.strftime('%Y-%m-%d')
        day_name = date.strftime('%A')
        day_num = date.day
    else:
        # Ha string, akkor feltételezzük, hogy YYYY-MM-DD formátumban van
        import datetime
        date_obj = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        date_str = date
        day_name = date_obj.strftime('%A')
        day_num = date_obj.day
    
    # Ha nincs esemény, egyszerűbb megjelenítés
    if not events:
        content = f"""
        <div style="text-align:center; color:{colors['text_color']};">
            <p style="margin:0; font-size:0.8em;">Nincs esemény</p>
        </div>
        """
        
        display_card(
            title=f"{day_name} ({date_str})",
            content=content,
            icon=f"{day_num}"
        )
        return
    
    # Esemény összesítés
    total_quantity = sum(event.get('quantity', 0) for event in events)
    total_value = sum(event.get('quantity', 0) * event.get('price', 0) for event in events)
    
    # HTML tartalom összeállítása
    content = f"""
    <div style="margin-bottom:10px;">
        <strong>Események száma:</strong> {len(events)}<br>
        <strong>Összes mennyiség:</strong> {format_quantity(total_quantity)}
    </div>
    """
    
    # Mobilon csak az első 2-3 esemény
    max_events = 2 if is_mobile else 5
    
    # Események megjelenítése
    for i, event in enumerate(events[:max_events]):
        status = event.get('status', '').lower()
        status_colors = {
            'várható': '#FFA726',
            'visszaigazolt': '#66BB6A',
            'elutasított': '#EF5350',
            'feldolgozás alatt': '#42A5F5',
            'törölt': '#9E9E9E',
        }
        status_color = status_colors.get(status, '#9E9E9E')
        
        content += f"""
        <div style="border-left:3px solid {status_color}; padding-left:8px; margin-bottom:8px;">
            <div style="font-weight:bold;">{event.get('title', 'Esemény')}</div>
            <div style="font-size:0.8em;">
                {event.get('product_name', 'Ismeretlen')} - {event.get('user_name', 'Ismeretlen')}
            </div>
        </div>
        """
    
    # Ha több esemény van, mint amit megjelenítünk
    if len(events) > max_events:
        content += f"""
        <div style="text-align:center; font-style:italic; margin-top:5px;">
            + {len(events) - max_events} további esemény
        </div>
        """
    
    # Kártya megjelenítése
    display_card(
        title=f"{day_name} ({date_str})",
        content=content,
        icon=f"{day_num}"
    ) 