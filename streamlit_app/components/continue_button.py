# Continue Button Component
"""
Standardizált "TOVÁBB" (Continue) gomb komponens.
"""
import streamlit as st

def render_continue_button(on_click=None, label="TOVÁBB", key=None):
    """
    "TOVÁBB" (Continue) gomb megjelenítése.
    
    Args:
        on_click (function, optional): Kattintás eseménykezelő. Defaults to None.
        label (str, optional): Gomb felirata. Defaults to "TOVÁBB".
        key (str, optional): Gomb egyedi azonosítója. Defaults to None.
    
    Returns:
        bool: True, ha a gombra kattintottak, egyébként False
    """
    # Gomb megjelenítése
    if st.button(label, key=key, type="primary"):
        # Kattintás eseménykezelő meghívása, ha van
        if on_click:
            on_click()
        
        return True
    
    return False 