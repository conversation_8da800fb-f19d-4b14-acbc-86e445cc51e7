"""
Idővonal komponens a kronológiai események megjelenítéséhez.
"""
import streamlit as st
from typing import List, Dict, Any, Optional
from .data_display import format_datetime


def show_timeline(
    events: List[Dict[str, Any]],
    date_key: str = "date",
    label_key: str = "label",
    description_key: Optional[str] = "description",
    color: str = "#3584e4"
) -> None:
    """
    Események időrendi megjelenítése idővonal formátumban.
    
    Args:
        events (List[Dict[str, Any]]): <PERSON><PERSON><PERSON><PERSON><PERSON> list<PERSON>, ahol minden esemény egy szótár
        date_key (str): A dátumot tartalmazó kulcs neve az esemény szótárban
        label_key (str): A címkét tartalmazó kulcs neve az esemény szótárban
        description_key (Optional[str]): Az opcionális leírást tartalmazó kulcs neve
        color (str): Az idővonal színe
    """
    # Soroljuk az eseményeket időrendben
    events = sorted(events, key=lambda x: x.get(date_key, ""))
    
    if not events:
        st.info("Nincs megjeleníthető esemény.")
        return
    
    # Fejléc
    st.markdown("### Idővonal")
    
    # Oszlopok létrehozása
    cols = st.columns(len(events))
    
    # Események megjelenítése
    for i, (col, event) in enumerate(zip(cols, events)):
        with col:
            # Címke és dátum megjelenítése
            st.markdown(f"**{event.get(label_key, 'Esemény')}**")
            st.write(format_datetime(event.get(date_key, "")))
            
            # Leírás megjelenítése, ha van
            if description_key and event.get(description_key):
                st.caption(event.get(description_key))
            
            # Vizuális pont az idővonalon
            st.markdown(
                f"<div style='display: flex; justify-content: center; margin-top: 10px;'>"
                f"<div style='width: 20px; height: 20px; background-color: {color}; border-radius: 50%;'></div>"
                f"</div>", 
                unsafe_allow_html=True
            )
            
    # Vonal az események alatt
    st.markdown("---") 