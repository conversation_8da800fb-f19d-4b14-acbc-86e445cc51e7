# Multi-step Form Component
"""
Többlépéses űrlap komponens.
"""
import streamlit as st
import json
import os
from datetime import datetime
from components.continue_button import render_continue_button
from utils.storage import save_form

def render_multi_step_form(steps, validators, form_id=None, on_save=None):
    """
    Többlépéses űrlap megjelenítése.
    
    Args:
        steps (list): Lépés függvények listája
        validators (list): <PERSON><PERSON><PERSON><PERSON> függ<PERSON>k listája
        form_id (str, optional): Űrlap azonosítója. Defaults to None.
        on_save (function, optional): <PERSON><PERSON><PERSON> callback függvény. Defaults to None.
    
    Returns:
        tuple: (current_step, form_data)
    """
    # Űrlap adatok inicializálása
    if "form_data" not in st.session_state:
        st.session_state.form_data = {}
    
    if "current_step" not in st.session_state:
        st.session_state.current_step = 0
    
    # Lépések száma
    total_steps = len(steps)
    
    # Lépés függvény meghívása
    step_data = steps[st.session_state.current_step](st.session_state.form_data)
    
    # Űrlap adatok frissítése
    st.session_state.form_data.update(step_data)
    
    # Űrlap adatok frissítése a lépéssel
    st.session_state.form_data["current_step"] = st.session_state.current_step
    st.session_state.form_data["total_steps"] = total_steps
    
    # Mentés gomb
    if form_id and on_save:
        if st.button("Űrlap mentése"):
            # Űrlap mentése
            if save_form(form_id, st.session_state.form_data):
                # Callback függvény meghívása
                on_save(form_id, st.session_state.form_data)
            else:
                st.error("Hiba történt az űrlap mentésekor.")
    
    # Navigációs gombok
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        # Vissza gomb
        if st.session_state.current_step > 0:
            if st.button("Vissza"):
                st.session_state.current_step -= 1
                st.rerun()
    
    with col2:
        # Lépés jelző
        st.write(f"Lépés: {st.session_state.current_step + 1}/{total_steps}")
    
    with col3:
        # TOVÁBB gomb
        if st.session_state.current_step < total_steps - 1:
            if st.button("TOVÁBB"):
                # Validáció
                is_valid, errors = validators[st.session_state.current_step](st.session_state.form_data)
                
                if is_valid:
                    st.session_state.current_step += 1
                    st.rerun()
                else:
                    # Hibaüzenetek megjelenítése
                    for error in errors:
                        st.error(error)
        else:
            # Befejezés gomb
            if st.button("Befejezés"):
                # Validáció
                is_valid, errors = validators[st.session_state.current_step](st.session_state.form_data)
                
                if is_valid:
                    st.success("Az űrlap sikeresen kitöltve!")
                    
                    # Űrlap adatok törlése
                    st.session_state.form_data = {}
                    st.session_state.current_step = 0
                    
                    st.rerun()
                else:
                    # Hibaüzenetek megjelenítése
                    for error in errors:
                        st.error(error)
    
    return st.session_state.current_step, st.session_state.form_data

def save_form_data(form_id, form_data):
    """
    Űrlap adatok mentése.
    
    Args:
        form_id (str): Űrlap azonosító
        form_data (dict): Űrlap adatok
    """
    # Mentési könyvtár létrehozása, ha nem létezik
    save_dir = "saved_forms"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # Űrlap adatok mentése
    save_path = os.path.join(save_dir, f"{form_id}.json")
    with open(save_path, "w", encoding="utf-8") as f:
        json.dump(form_data, f, ensure_ascii=False, indent=2, default=str)

def load_form_data(form_id):
    """
    Űrlap adatok betöltése.
    
    Args:
        form_id (str): Űrlap azonosító
        
    Returns:
        dict: Űrlap adatok, ha létezik, egyébként None
    """
    # Mentési könyvtár ellenőrzése
    save_dir = "saved_forms"
    if not os.path.exists(save_dir):
        return None
    
    # Űrlap adatok betöltése
    save_path = os.path.join(save_dir, f"{form_id}.json")
    if not os.path.exists(save_path):
        return None
    
    with open(save_path, "r", encoding="utf-8") as f:
        return json.load(f)

def list_saved_forms():
    """
    Mentett űrlapok listázása.
    
    Returns:
        list: Mentett űrlapok listája
    """
    # Mentési könyvtár ellenőrzése
    save_dir = "saved_forms"
    if not os.path.exists(save_dir):
        return []
    
    # Mentett űrlapok listázása
    saved_forms = []
    for file_name in os.listdir(save_dir):
        if file_name.endswith(".json"):
            form_id = file_name[:-5]  # .json kiterjesztés eltávolítása
            save_path = os.path.join(save_dir, file_name)
            modified_time = os.path.getmtime(save_path)
            modified_date = datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M:%S")
            
            saved_forms.append({
                "id": form_id,
                "modified": modified_date
            })
    
    return saved_forms 