def event_details_modal(event, on_update=None):
    """
    Esemény részleteit megjelenítő modális ablak.
    
    Args:
        event (dict): <PERSON>z esemény adatai
        on_update (callable, optional): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely <PERSON>, ha az eseményt frissítették
    """
    import streamlit as st
    from utils.formatting import format_quantity, format_price
    from datetime import datetime
    
    # <PERSON><PERSON>ü<PERSON>, hogy van-e id az eseménynek
    if not event or 'id' not in event:
        st.error("Hiányzó esemény adatok")
        return
        
    # Az esemény azonosítója
    event_id = event.get('id', '')
    
    # Session state kulcsok az aktuális eseményhez
    modal_key = f"event_modal_{event_id}"
    note_key = f"event_note_{event_id}"
    status_key = f"event_status_{event_id}"
    
    # Inicializáljuk a session state változókat, ha szükséges
    if modal_key not in st.session_state:
        st.session_state[modal_key] = False
    if note_key not in st.session_state:
        st.session_state[note_key] = event.get('note', '')
    if status_key not in st.session_state:
        st.session_state[status_key] = event.get('status', '')
        
    # Modális megnyitása/bezárása funkciók
    def open_modal():
        st.session_state[modal_key] = True
        st.session_state[note_key] = event.get('note', '')
        st.session_state[status_key] = event.get('status', '')
        
    def close_modal():
        st.session_state[modal_key] = False
    
    # Modális tartalom
    if st.session_state[modal_key]:
        # Teljes képernyős overlay
        st.markdown("""
        <div class="modal-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        ">
        </div>
        """, unsafe_allow_html=True)
        
        # Modális ablak
        with st.container():
            # Modális fejléc
            st.subheader(f"Esemény részletei: {event.get('title', 'Esemény')}")
            
            # Két oszlop: bal oldalon adatok, jobb oldalon szerkesztés
            col1, col2 = st.columns([2, 1])
            
            with col1:
                # Esemény alapadatai
                st.markdown(f"**Azonosító:** {event_id}")
                
                # Dátum
                event_date = event.get('date', '')
                if event_date:
                    try:
                        if isinstance(event_date, str):
                            event_date = datetime.strptime(event_date, '%Y-%m-%d').date()
                        st.markdown(f"**Dátum:** {event_date.strftime('%Y-%m-%d')}")
                    except:
                        st.markdown(f"**Dátum:** {event_date}")
                
                # Termék és mennyiség
                st.markdown(f"**Termék:** {event.get('product_name', event.get('product_type_id', 'Ismeretlen'))}")
                st.markdown(f"**Mennyiség:** {format_quantity(event.get('quantity', 0))}")
                
                # Ár, ha van
                price = event.get('price', 0)
                if price:
                    st.markdown(f"**Ár:** {format_price(price)}")
                    
                    # Teljes érték
                    total = price * event.get('quantity', 0)
                    st.markdown(f"**Teljes érték:** {format_price(total)}")
                
                # Felhasználó
                user_name = event.get('user_name', 'Ismeretlen')
                st.markdown(f"**Felhasználó:** {user_name}")
                
                # Állapot
                status = event.get('status', 'Ismeretlen')
                status_colors = {
                    'CREATED': '#90CAF9',
                    'CONFIRMED_BY_COMPANY': '#66BB6A',
                    'ACCEPTED_BY_USER': '#4CAF50',
                    'REJECTED_BY_USER': '#EF5350',
                    'FINALIZED': '#9C27B0'
                }
                # Magyar fordítás a státuszokhoz
                status_translations = {
                    'CREATED': 'Létrehozva',
                    'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
                    'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
                    'REJECTED_BY_USER': 'Felhasználó által elutasítva',
                    'FINALIZED': 'Véglegesítve',
                    'UNKNOWN': 'Ismeretlen'
                }
                status_color = status_colors.get(status, '#9E9E9E')
                status_display = status_translations.get(status, 'Ismeretlen')
                
                st.markdown(f"""
                <div style="
                    display: inline-block;
                    padding: 5px 10px;
                    background-color: {status_color};
                    color: white;
                    border-radius: 4px;
                    font-weight: bold;
                    margin-top: 10px;
                ">
                    {status_display}
                </div>
                """, unsafe_allow_html=True)
                
                # Megjegyzés
                st.markdown(f"**Megjegyzés:** {event.get('note', 'Nincs megjegyzés')}")
            
            with col2:
                # Szerkesztési lehetőségek - ha a on_update callback meg van adva
                if on_update:
                    # Állapot módosítása
                    available_statuses = [
                        'CREATED',
                        'CONFIRMED_BY_COMPANY',
                        'ACCEPTED_BY_USER',
                        'REJECTED_BY_USER',
                        'FINALIZED'
                    ]
                    
                    # Magyar fordítások a státusz állapotokhoz
                    status_translations = {
                        'CREATED': 'Létrehozva',
                        'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
                        'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
                        'REJECTED_BY_USER': 'Felhasználó által elutasítva',
                        'FINALIZED': 'Véglegesítve'
                    }
                    
                    # Status fordítások a megjelenítéshez
                    status_display_options = [status_translations.get(s, s) for s in available_statuses]
                    
                    # Format opciók a kijelzőhöz: "CREATED - Létrehozva"
                    status_display_options_with_code = [
                        f"{s} - {status_translations.get(s, s)}" for s in available_statuses
                    ]
                    
                    selected_status_index = available_statuses.index(status) if status in available_statuses else 0
                    
                    # 2. Formázott lehetőségek a selectbox-hoz
                    status_display_options_with_code = [
                        f"{s} - {status_translations.get(s, s)}" for s in available_statuses
                    ]

                    # 3. Alapértelmezett érték a selectbox-ban
                    selected_status_display = st.selectbox(
                        "Állapot módosítása",
                        options=status_display_options_with_code,
                        index=selected_status_index,
                        key=status_key
                    )

                    # 4. Kinyerjük a státusz kódot a kiválasztott opció alapján (status kód a "-" előtti rész)
                    selected_status_code = selected_status_display.split(" - ")[0]
                    
                    # Megjegyzés szerkesztése
                    edited_note = st.text_area(
                        "Megjegyzés szerkesztése",
                        value=event.get('note', ''),
                        key=note_key
                    )
                    
                    # Mentés gomb
                    if st.button("Módosítások mentése"):
                        # Módosított esemény létrehozása
                        updated_event = event.copy()
                        updated_event['status'] = selected_status_code
                        updated_event['note'] = edited_note
                        
                        # Callback meghívása a frissített eseménnyel
                        on_update(updated_event)
                        
                        # Modális bezárása
                        close_modal()
                        st.rerun()
            
            # Bezárás gomb
            st.button("Bezárás", on_click=close_modal)
    
    return {
        'open_modal': open_modal,
        'is_open': st.session_state[modal_key]
    }

def render_css_calendar(events, month_date=None):
    """
    Reszponzív CSS alapú naptár megjelenítése
    
    Args:
        events (list): Az események listája
        month_date (datetime.date, optional): A megjelenítendő hónap
    """
    import streamlit as st
    import calendar
    import datetime
    from datetime import date, timedelta
    
    # Ha nincs megadva dátum, az aktuális hónapot használjuk
    if month_date is None:
        month_date = date.today()
    
    # Bizonyosodjunk meg róla, hogy a month_date datetime.date típusú
    if isinstance(month_date, str):
        try:
            # Próbáljuk meg dátummá konvertálni, ha stringként kaptuk
            parts = month_date.split('-')
            if len(parts) == 3:
                month_date = date(int(parts[0]), int(parts[1]), int(parts[2]))
            else:
                month_date = date.today()
        except Exception:
            month_date = date.today()
    elif isinstance(month_date, datetime.datetime):
        month_date = month_date.date()
    elif not isinstance(month_date, date):
        month_date = date.today()
    
    # Hónap első és utolsó napja
    first_day = date(month_date.year, month_date.month, 1)
    last_day = date(month_date.year, month_date.month + 1, 1) - timedelta(days=1) if month_date.month < 12 else date(month_date.year + 1, 1, 1) - timedelta(days=1)
    
    # Naptár fejléc (hónapnév és év)
    month_name = month_date.strftime("%B %Y")
    
    # Események normalizálása
    normalized_events = []
    events_by_date = {}  # Dátum szerinti csoportosítás
    
    if events:  # Ellenőrizzük, hogy nem None vagy üres lista
        for event in events:
            try:
                # Ellenőrizzük, hogy a dátum megfelelő formátumú-e
                event_date = event.get('date', event.get('delivery_date', ''))
                if isinstance(event_date, (datetime.date, datetime.datetime)):
                    event_date = event_date.strftime('%Y-%m-%d')
                
                # Alapértelmezett cím generálása, ha nincs megadva
                title = event.get('title', '')
                if not title:
                    title = f"Esemény #{event.get('id', '')}"
                
                # Magyar fordítás a státuszokhoz
                status_translations = {
                    'CREATED': 'Létrehozva',
                    'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
                    'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
                    'REJECTED_BY_USER': 'Felhasználó által elutasítva',
                    'FINALIZED': 'Véglegesítve',
                    'UNKNOWN': 'Ismeretlen'
                }
                status = event.get('status', 'N/A')
                status_display = status_translations.get(status, status)
                
                # Részletes információk előkészítése tooltiphez
                details = f"""
                <strong>Azonosító:</strong> {event.get('id', 'N/A')}<br>
                <strong>Státusz:</strong> {status_display}<br>
                <strong>Mennyiség:</strong> {event.get('quantity', 0):.1f} kg<br>
                """
                
                if event.get('user_name'):
                    details += f"<strong>Felhasználó:</strong> {event.get('user_name')}<br>"
                
                if event.get('note'):
                    details += f"<strong>Megjegyzés:</strong> {event.get('note')}"
                
                normalized_event = {
                    'id': event.get('id', ''),
                    'title': title,
                    'date': event_date,
                    'color': event.get('color', '#3498db'),
                    'note': event.get('note', ''),
                    'details': details,  # Tooltip tartalom
                    'status': event.get('status', ''),  # Állapot hozzáadása
                    'quantity': event.get('quantity', 0),  # Mennyiség hozzáadása
                    'product_name': event.get('product_name', ''),  # Termék név
                    'user_name': event.get('user_name', ''),  # Felhasználó név
                    'price': event.get('price', 0)  # Ár
                }
                normalized_events.append(normalized_event)
                
                # Események számának követése dátumonként
                if event_date not in events_by_date:
                    events_by_date[event_date] = []
                events_by_date[event_date].append(normalized_event)
            except Exception as e:
                print(f"Hiba az esemény normalizálása során: {str(e)}")
    
    # Modális ablakok előkészítése az eseményekhez
    event_modals = {}
    for event in normalized_events:
        if 'id' in event:
            event_id = event['id']
            # Esemény frissítési callback (itt nem használjuk, de a komponens megköveteli)
            def update_event(updated_event):
                st.session_state[f'updated_event_{event_id}'] = updated_event
                
            event_modals[event_id] = event_details_modal(event, on_update=update_event)
    
    # CSS stílusok beágyazása - sötét témához optimalizálva
    st.markdown("""
    <style>
    /* Naptár konténer */
    .calendar-container {
        width: 100%;
        font-family: 'Arial', sans-serif;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
    }

    /* Hónap fejléc */
    .month-header {
        background-color: #1e2c3a;
        color: white;
        padding: 10px 0;
        text-align: center;
        font-size: 1.5em;
        border-radius: 5px 5px 0 0;
        border-bottom: 1px solid #263c5a;
    }

    /* Naptár grid */
    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background-color: #263c5a;
        border: 1px solid #263c5a;
    }

    /* Napok fejléc */
    .weekday-header {
        background-color: #2c3e50;
        color: white;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        border-bottom: 1px solid #3a546a;
    }

    /* Nap cella */
    .day-cell {
        background-color: #1a2530;
        min-height: 80px;
        padding: 5px;
        text-align: right;
        position: relative;
        color: #e0e0e0;
        transition: background-color 0.2s;
    }

    .day-cell:hover {
        background-color: #263545;
    }

    /* Előző vagy következő hónap napja */
    .other-month {
        background-color: #131c25;
        color: #808080;
    }

    /* Mai nap */
    .today {
        background-color: #203040;
        font-weight: bold;
        border: 1px solid #4a7baa;
    }

    /* Eseménnyel rendelkező nap */
    .has-event {
        position: relative;
    }
    
    /* Esemény jelvény - mutatja hány esemény van az adott napon */
    .event-badge {
        position: absolute;
        top: 5px;
        left: 5px;
        min-width: 20px;
        height: 20px;
        background-color: #3498db;
        color: white;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75em;
        font-weight: bold;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        z-index: 2;
    }

    /* Esemény doboz */
    .event {
        background-color: #3498db;
        color: white;
        font-size: 0.8em;
        padding: 4px 6px;
        margin-top: 4px;
        border-radius: 4px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        border-left: 3px solid rgba(255,255,255,0.3);
        cursor: pointer; /* Kurzor mutatja, hogy interaktív */
    }
    
    /* Esemény állapot specifikus színek */
    .event-status-created {
        background-color: #90CAF9;
        color: #1a2530;
    }
    .event-status-created::after {
        content: "Létrehozva";
    }
    
    .event-status-confirmed_by_company {
        background-color: #66BB6A;
    }
    .event-status-confirmed_by_company::after {
        content: "Vállalat által elfogadva";
    }
    
    .event-status-accepted_by_user {
        background-color: #4CAF50;
    }
    .event-status-accepted_by_user::after {
        content: "Felhasználó által elfogadva";
    }
    
    .event-status-rejected_by_user {
        background-color: #EF5350;
    }
    .event-status-rejected_by_user::after {
        content: "Felhasználó által elutasítva";
    }
    
    .event-status-finalized {
        background-color: #9C27B0;
    }
    .event-status-finalized::after {
        content: "Véglegesítve";
    }

    /* Tooltip stílus */
    .calendar-tooltip {
        display: none;
        position: absolute;
        background-color: #2c3e50;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        z-index: 100;
        width: 250px;
        font-size: 0.9em;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        border: 1px solid #4a7baa;
        pointer-events: none;
    }
    
    /* Tooltip mutatása hover esetén */
    .event:hover .calendar-tooltip {
        display: block;
    }

    /* Mobilbarát nézet */
    @media (max-width: 768px) {
        .weekday-header {
            font-size: 0.8em;
            padding: 5px;
        }
        
        .day-cell {
            min-height: 60px;
            font-size: 0.9em;
        }
        
        .event {
            font-size: 0.7em;
        }
        
        .calendar-tooltip {
            width: 200px;
            font-size: 0.8em;
        }
    }

    /* Üres állapot üzenet */
    .calendar-empty-state {
        text-align: center;
        padding: 20px;
        background-color: #1a2530;
        border-radius: 0 0 5px 5px;
        color: #a0aec0;
        font-style: italic;
        border-top: 1px solid #263c5a;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # JavaScript a tooltip-hez
    st.markdown("""
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tooltipek mutatása hover esetén
        const events = document.querySelectorAll('.event');
        
        events.forEach(event => {
            event.addEventListener('mouseenter', function() {
                const tooltip = this.querySelector('.calendar-tooltip');
                if (tooltip) {
                    tooltip.style.display = 'block';
                }
            });
            
            event.addEventListener('mouseleave', function() {
                const tooltip = this.querySelector('.calendar-tooltip');
                if (tooltip) {
                    tooltip.style.display = 'none';
                }
            });
        });
    });
    </script>
    """, unsafe_allow_html=True)
    
    # Naptár HTML generálása
    calendar_html = f"""
    <div class="calendar-container">
        <div class="month-header">{month_name}</div>
        <div class="calendar-grid">
    """
    
    # Napnév fejléc
    weekday_names = ["Hétfő", "Kedd", "Szerda", "Csütörtök", "Péntek", "Szombat", "Vasárnap"]
    for day_name in weekday_names:
        calendar_html += f'<div class="weekday-header">{day_name}</div>'
    
    # Naptár grid létrehozása
    cal = calendar.monthcalendar(month_date.year, month_date.month)
    today = date.today()
    
    # Előző hónap utolsó napjai
    first_weekday = first_day.weekday()  # 0-6, ahol 0 = hétfő
    
    prev_month = date(month_date.year, month_date.month - 1, 1) if month_date.month > 1 else date(month_date.year - 1, 12, 1)
    prev_month_last_day = (first_day - timedelta(days=1)).day
    
    # Következő hónap első napjai
    last_weekday = last_day.weekday()  # 0-6, ahol 0 = hétfő
    days_to_add = 6 - last_weekday if last_weekday < 6 else 0
    
    # Teljes naptár grid
    # Heteket és napokat iteráljuk
    week_counter = 0
    
    # Előző hónap napjai
    for i in range(first_weekday):
        day_num = prev_month_last_day - first_weekday + i + 1
        calendar_html += f'<div class="day-cell other-month">{day_num}</div>'
        week_counter += 1
    
    # Aktuális hónap napjai
    for day in range(1, last_day.day + 1):
        current_date = date(month_date.year, month_date.month, day)
        date_str = current_date.strftime('%Y-%m-%d')
        
        # Speciális osztályok
        classes = ["day-cell"]
        
        if today.year == current_date.year and today.month == current_date.month and today.day == day:
            classes.append("today")
        
        # Események ellenőrzése erre a napra
        day_events = events_by_date.get(date_str, [])
        has_events = len(day_events) > 0
        
        if has_events:
            classes.append("has-event")
        
        # Nap cella kezdete
        calendar_html += f'<div class="{" ".join(classes)}">'
        
        # Esemény jelvény, ha van esemény
        if has_events:
            calendar_html += f'<div class="event-badge">{len(day_events)}</div>'
        
        # Nap szám
        calendar_html += f'{day}'
        
        # Események ezen a napon
        if has_events:
            # Maximum 3 esemény megjelenítése (mobilbarát)
            max_events = 3
            visible_events = day_events[:max_events]
            hidden_events = len(day_events) - max_events if len(day_events) > max_events else 0
            
            for event in visible_events:
                # Esemény stílusa státusz alapján
                status_class = f"event-status-{event['status'].lower()}" if event['status'] else ""
                
                # Egyedi esemény azonosító a kattintáshoz
                event_id = event.get('id', '')
                
                # Ha van id, egyedi azonosítóval látjuk el, hogy a modális megnyitásakor azonosítható legyen
                if event_id:
                    event_key = f"event_{event_id}"
                    calendar_html += f"""
                    <div class="event {status_class}" style="background-color: {event['color']};" onclick="streamlitClick('{event_key}')">
                        {event['title']}
                        <div class="calendar-tooltip">
                            {event['details']}
                        </div>
                    </div>
                    """
                    # JavaScript eseménykezelő a kattintáshoz
                    st.markdown(f"""
                    <script>
                    function streamlitClick(key) {{
                        // A Streamlit komponens kommunikációs API használata
                        const data = {{
                            event: 'streamlit:click',
                            key: key
                        }};
                        window.parent.postMessage(data, '*');
                    }}
                    </script>
                    """, unsafe_allow_html=True)
                    
                    # Egyedi kattintási eseménykezelők
                    if st.button(event['title'], key=event_key, type="primary", help="Kattintson a részletekért"):
                        if event_id in event_modals:
                            event_modals[event_id]['open_modal']()
                            st.rerun()
                else:
                    # Ha nincs id, egyszerű esemény div
                    calendar_html += f"""
                    <div class="event {status_class}" style="background-color: {event['color']};">
                        {event['title']}
                        <div class="calendar-tooltip">
                            {event['details']}
                        </div>
                    </div>
                    """
            
            # Ha több esemény van, jelezzük
            if hidden_events > 0:
                calendar_html += f'<div class="event" style="background-color: #607D8B; text-align: center;">+{hidden_events} további</div>'
        
        # Nap cella zárása
        calendar_html += '</div>'
        
        week_counter += 1
        
        # Új sor a hét végén
        if week_counter % 7 == 0 and day < last_day.day:
            # A hét vége, új sor
            week_counter = 0
    
    # Következő hónap napjai
    for i in range(days_to_add):
        day_num = i + 1
        calendar_html += f'<div class="day-cell other-month">{day_num}</div>'
    
    # Naptár HTML zárása
    calendar_html += """
        </div>
    </div>
    """
    
    # Ha nincs esemény, jelezzük
    if not normalized_events:
        calendar_html += """
        <div class="calendar-empty-state">
            Nincsenek események a kiválasztott időszakban.
        </div>
        """
    
    # HTML kód megjelenítése
    st.markdown(calendar_html, unsafe_allow_html=True)
    
    # Események részleteinek modális ablakainak megjelenítése
    for event_id, modal in event_modals.items():
        if modal['is_open']:
            # Nem kell semmit tenni, a modál már megjelenik a event_details_modal függvény hívásából
            pass

def render_interactive_calendar(events, month_date=None):
    """
    Interaktív JavaScript alapú naptár megjelenítése
    
    Args:
        events (list): Az események listája
        month_date (datetime.date, optional): A megjelenítendő hónap
    """
    import streamlit as st
    import streamlit.components.v1 as components
    import json
    import datetime
    from datetime import date
    
    # Ha nincs megadva dátum, az aktuális hónapot használjuk
    if month_date is None:
        month_date = date.today()
    
    # Bizonyosodjunk meg róla, hogy a month_date datetime.date típusú
    if isinstance(month_date, str):
        try:
            # Próbáljuk meg dátummá konvertálni, ha stringként kaptuk
            parts = month_date.split('-')
            if len(parts) == 3:
                month_date = date(int(parts[0]), int(parts[1]), int(parts[2]))
            else:
                month_date = date.today()
        except Exception:
            month_date = date.today()
    elif isinstance(month_date, datetime.datetime):
        month_date = month_date.date()
    elif not isinstance(month_date, date):
        month_date = date.today()
    
    # Események formátumának átalakítása JavaScript-nek megfelelően
    js_events = []
    if events:  # Ellenőrizzük, hogy nem None vagy üres lista
        for event in events:
            try:
                # Ellenőrizzük, hogy a dátum megfelelő formátumú-e
                event_date = event.get('date', event.get('delivery_date', ''))
                if isinstance(event_date, (datetime.date, datetime.datetime)):
                    event_date = event_date.strftime('%Y-%m-%d')
                
                # Alapértelmezett cím generálása, ha nincs megadva
                title = event.get('title', '')
                if not title:
                    title = f"Esemény #{event.get('id', '')}"
                
                js_event = {
                    'id': event.get('id', ''),
                    'title': title,
                    'date': event_date,
                    'color': event.get('color', '#3498db'),
                    'description': event.get('note', '')
                }
                js_events.append(js_event)
            except Exception as e:
                print(f"Hiba az esemény átalakítása során: {str(e)}")
    
    # A hónap, év és nap kinyerése a dátumból
    current_year = month_date.year
    current_month = month_date.month
    current_day = month_date.day
    
    # Beágyazott komponens HTML/JS
    html_code = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        <style>
        /* Naptár stílusok - sötét téma - teljes fekete háttérrel */
        html, body {{
            background-color: #0d1117 !important;  /* Sötét fekete háttér ami illeszkedik az alkalmazás hátteréhez */
            margin: 0;
            padding: 0;
            color: #e0e0e0;
            font-family: 'Arial', sans-serif;
        }}
        
        .calendar-wrapper {{
            font-family: 'Arial', sans-serif;
            max-width: 100%;
            margin: 0;
            padding: 0;
            border: none;
            background-color: #0d1117;
        }}
            
        .calendar-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #1e2c3a;
            color: white;
            padding: 15px;
            border-bottom: 1px solid #263c5a;
        }}
        
        .calendar-header h2 {{
            margin: 0;
            font-size: 1.5em;
            color: #e0e0e0;
        }}
        
        .calendar-controls {{
            display: flex;
            gap: 10px;
        }}
        
        .calendar-controls button {{
            background-color: #2c3e50;
            border: none;
            color: white;
            font-size: 1.2em;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }}
        
        .calendar-controls button:hover {{
            background-color: #3e5876;
        }}
        
        .calendar-grid {{
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            background-color: #121920;
        }}
        
        .calendar-weekday {{
            background-color: #2c3e50;
            color: #e0e0e0;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #3a546a;
        }}
        
        .calendar-day {{
            min-height: 100px;
            border: 1px solid #2c3e50;
            padding: 5px;
            position: relative;
            background-color: #1a2530;
            transition: background-color 0.2s;
        }}
        
        .calendar-day:hover {{
            background-color: #263545;
        }}
        
        .day-number {{
            position: absolute;
            top: 5px;
            right: 5px;
            font-weight: bold;
            color: #e0e0e0;
        }}
        
        .other-month {{
            background-color: #131c25;
            color: #808080;
        }}
        
        .today {{
            background-color: #203040;
            border: 1px solid #4a7baa;
        }}
        
        .calendar-event {{
            margin: 20px 2px 2px 2px;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 0.85em;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            border-left: 3px solid rgba(255,255,255,0.3);
            color: #ffffff;
        }}
        
        .event-popup {{
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #1e2c3a;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-radius: 6px;
            z-index: 1000;
            max-width: 80%;
            width: 400px;
            color: #e0e0e0;
            border: 1px solid #3a546a;
        }}
        
        .event-popup-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #3a546a;
            padding-bottom: 10px;
        }}
        
        .event-popup-header h3 {{
            margin: 0;
            color: #ffffff;
        }}
        
        .event-popup-close {{
            background: none;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
            color: #e0e0e0;
        }}
        
        .event-popup-close:hover {{
            color: #ffffff;
        }}
        
        .event-popup-content {{
            margin-bottom: 15px;
        }}
        
        .overlay {{
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 999;
        }}
        
        /* Üres állapot üzenet */
        .calendar-empty-state {{
            text-align: center;
            padding: 20px;
            background-color: #1a2530;
            color: #a0aec0;
            font-style: italic;
            border: 1px solid #2c3e50;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }}
        
        /* Mobilbarát nézet */
        @media (max-width: 768px) {{
            .calendar-grid {{
                font-size: 0.9em;
            }}
            
            .calendar-day {{
                min-height: 80px;
            }}
            
            .calendar-event {{
                font-size: 0.7em;
                padding: 2px 4px;
            }}
        }}
        </style>
    </head>
    <body>
        <div class="calendar-wrapper">
            <div class="calendar-header">
                <h2 id="calendar-title">Naptár</h2>
                <div class="calendar-controls">
                    <button id="prev-month"><i class="fas fa-chevron-left"></i></button>
                    <button id="current-month">Ma</button>
                    <button id="next-month"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
            <div id="calendar-container"></div>
        </div>
        
        <div id="event-popup" style="display: none;">
            <div class="overlay"></div>
            <div class="event-popup">
                <div class="event-popup-header">
                    <h3 id="event-title">Esemény</h3>
                    <button class="event-popup-close">&times;</button>
                </div>
                <div class="event-popup-content">
                    <p id="event-date">Dátum: </p>
                    <p id="event-description">Leírás: </p>
                </div>
            </div>
        </div>
        
        <script>
        // Kalendár adatok és változók
        const currentDate = new Date({month_date.year}, {month_date.month - 1}, {month_date.day});
        const today = new Date();
        let events = {json.dumps(js_events)};
        
        // Naptár inicializálása
        document.addEventListener('DOMContentLoaded', function() {{
            renderCalendar(currentDate);
            setupEventListeners();
        }});
        
        // Eseményfigyelők beállítása
        function setupEventListeners() {{
            document.getElementById('prev-month').addEventListener('click', function() {{
                const newDate = new Date(currentDate);
                newDate.setMonth(newDate.getMonth() - 1);
                renderCalendar(newDate);
            }});
            
            document.getElementById('next-month').addEventListener('click', function() {{
                const newDate = new Date(currentDate);
                newDate.setMonth(newDate.getMonth() + 1);
                renderCalendar(newDate);
            }});
            
            document.getElementById('current-month').addEventListener('click', function() {{
                renderCalendar(new Date());
            }});
            
            // Felugró ablak bezárása
            document.querySelector('.event-popup-close').addEventListener('click', function() {{
                document.getElementById('event-popup').style.display = 'none';
            }});
            
            document.querySelector('.overlay').addEventListener('click', function() {{
                document.getElementById('event-popup').style.display = 'none';
            }});
        }}
        
        // Naptár renderelése
        function renderCalendar(date) {{
            currentDate.setTime(date.getTime());
            
            const year = date.getFullYear();
            const month = date.getMonth();
            
            // Hónapnév beállítása
            const monthNames = ['Január', 'Február', 'Március', 'Április', 'Május', 'Június', 
                            'Július', 'Augusztus', 'Szeptember', 'Október', 'November', 'December'];
            document.getElementById('calendar-title').textContent = `${{monthNames[month]}} ${{year}}`;
            
            // Naptár grid létrehozása
            const calendarContainer = document.getElementById('calendar-container');
            calendarContainer.innerHTML = '';
            
            const calendarGrid = document.createElement('div');
            calendarGrid.className = 'calendar-grid';
            
            // Napok fejléc
            const daysOfWeek = ['H', 'K', 'Sze', 'Cs', 'P', 'Szo', 'V'];
            daysOfWeek.forEach(day => {{
                const weekdayElem = document.createElement('div');
                weekdayElem.className = 'calendar-weekday';
                weekdayElem.textContent = day;
                calendarGrid.appendChild(weekdayElem);
            }});
            
            // Hónap első napja
            const firstDay = new Date(year, month, 1);
            // Hét kezdőnapja (0: vasárnap, 1: hétfő, ..., 6: szombat)
            let dayOfWeek = firstDay.getDay() || 7; // Átalakítás hétfő = 1 formátumra
            dayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek; // Vasárnap = 7
            
            // Előző hónap utolsó napjai
            const daysInPrevMonth = new Date(year, month, 0).getDate();
            const prevMonthStartDay = daysInPrevMonth - dayOfWeek + 2;
            
            // Előző hónap napjai
            for (let i = prevMonthStartDay; i <= daysInPrevMonth; i++) {{
                const dayElem = createDayElement(new Date(year, month - 1, i), true);
                calendarGrid.appendChild(dayElem);
            }}
            
            // Aktuális hónap napjai
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            for (let i = 1; i <= daysInMonth; i++) {{
                const dayElem = createDayElement(new Date(year, month, i), false);
                calendarGrid.appendChild(dayElem);
            }}
            
            // Következő hónap napjai
            const totalDaysDisplayed = calendarGrid.children.length - 7;
            const daysFromNextMonth = 42 - totalDaysDisplayed;
            
            for (let i = 1; i <= daysFromNextMonth; i++) {{
                const dayElem = createDayElement(new Date(year, month + 1, i), true);
                calendarGrid.appendChild(dayElem);
            }}
            
            calendarContainer.appendChild(calendarGrid);
            
            // Üres állapot üzenet hozzáadása, ha nincs esemény
            if (events.length === 0) {{
                const emptyStateElem = document.createElement('div');
                emptyStateElem.className = 'calendar-empty-state';
                emptyStateElem.textContent = 'Nincsenek események a kiválasztott időszakban.';
                calendarContainer.appendChild(emptyStateElem);
            }}
        }}
        
        // Nap elem létrehozása
        function createDayElement(date, isOtherMonth) {{
            const dayElem = document.createElement('div');
            dayElem.className = 'calendar-day';
            
            // Egyéb osztályok hozzáadása
            if (isOtherMonth) {{
                dayElem.classList.add('other-month');
            }}
            
            // Mai nap kiemelése
            if (date.toDateString() === today.toDateString()) {{
                dayElem.classList.add('today');
            }}
            
            // Nap szám hozzáadása
            const dayNumber = document.createElement('div');
            dayNumber.className = 'day-number';
            dayNumber.textContent = date.getDate();
            dayElem.appendChild(dayNumber);
            
            // Események hozzáadása a naphoz
            const dateStr = formatDate(date);
            const dayEvents = events.filter(event => event.date === dateStr);
            
            dayEvents.forEach(event => {{
                const eventElem = document.createElement('div');
                eventElem.className = 'calendar-event';
                eventElem.textContent = event.title;
                eventElem.style.backgroundColor = event.color;
                
                // Eseményre kattintás kezelése
                eventElem.addEventListener('click', function() {{
                    showEventPopup(event);
                }});
                
                dayElem.appendChild(eventElem);
            }});
            
            return dayElem;
        }}
        
        // Dátum formázása YYYY-MM-DD formátumban
        function formatDate(date) {{
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${{year}}-${{month}}-${{day}}`;
        }}
        
        // Esemény részletek megjelenítése
        function showEventPopup(event) {{
            document.getElementById('event-title').textContent = event.title;
            document.getElementById('event-date').textContent = `Dátum: ${{formatDateForDisplay(event.date)}}`;
            document.getElementById('event-description').textContent = `Leírás: ${{event.description || 'Nincs leírás'}}`;
            document.getElementById('event-popup').style.display = 'block';
        }}
        
        // Dátum formázása olvasható formátumban
        function formatDateForDisplay(dateStr) {{
            const [year, month, day] = dateStr.split('-');
            return `${{year}}. ${{month}}. ${{day}}.`;
        }}
        </script>
    </body>
    </html>
    """
    
    # Komponens megjelenítése
    components.html(html_code, height=700)

def render_card_calendar(events, date_range):
    """
    Kártya-alapú naptárnézet megjelenítése, különösen hasznos mobil eszközökön.
    
    Args:
        events (list): Az események listája
        date_range (tuple): (start_date, end_date) a megjelenítendő időszak
    """
    import streamlit as st
    import datetime
    from datetime import date, timedelta
    from utils.responsive_ui import create_responsive_columns, get_theme_colors
    from components.calendar_card import calendar_day_card, event_card
    
    # Dátum tartomány feloldása
    start_date, end_date = date_range
    
    # Biztosítsuk, hogy megfelelő formátumúak legyenek a dátumok
    if isinstance(start_date, str):
        try:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
        except:
            start_date = date.today()
    elif isinstance(start_date, datetime.datetime):
        start_date = start_date.date()
        
    if isinstance(end_date, str):
        try:
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
        except:
            end_date = start_date
    elif isinstance(end_date, datetime.datetime):
        end_date = end_date.date()
    
    # Ha nincs vége megadva, a kezdődátumot használjuk
    if not end_date:
        end_date = start_date
    
    # Téma színek lekérdezése
    colors = get_theme_colors()
    
    # CSS stílusok a kártya naptárhoz
    st.markdown("""
    <style>
    .calendar-card-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background-color: #1e2c3a;
        border-radius: 8px 8px 0 0;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .calendar-card-date {
        font-weight: bold;
        font-size: 1.1em;
    }
    
    .calendar-card-controls {
        display: flex;
        gap: 8px;
    }
    
    .calendar-card-control {
        background-color: #2c3e50;
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .calendar-card-control:hover {
        background-color: #3498db;
    }
    
    .calendar-day-card {
        margin-bottom: 15px;
        transition: transform 0.2s;
    }
    
    .calendar-day-card:hover {
        transform: translateY(-3px);
    }
    
    .event-count-badge {
        display: inline-block;
        background-color: #3498db;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 8px;
    }
    
    .calendar-event-card {
        margin-bottom: 8px;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        background-color: #2c3e50;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .calendar-event-card:hover {
        transform: translateX(5px);
    }
    
    .calendar-event-card-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .calendar-event-card-details {
        font-size: 0.9em;
        color: #ccc;
    }
    
    .calendar-event-status {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.8em;
        margin-right: 5px;
    }
    
    @media (max-width: 768px) {
        .calendar-day-grid {
            display: block;
        }
        
        .calendar-day-card {
            width: 100%;
            margin-bottom: 10px;
        }
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Dátum navigáció megjelenítése
    with st.container():
        st.markdown(f"""
        <div class="calendar-card-nav">
            <div class="calendar-card-date">
                {start_date.strftime('%Y. %B %d')} - {end_date.strftime('%B %d')}
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    # Ha nincs adat, jelenítsünk meg üzenetet
    if not events:
        st.info("Nincs esemény a kiválasztott időszakban.")
        return
    
    # Modális ablakok előkészítése az eseményekhez
    event_modals = {}
    for event in events:
        if 'id' in event:
            event_id = event['id']
            # Esemény frissítési callback
            def update_event(updated_event):
                st.session_state[f'updated_event_{event_id}'] = updated_event
                # Frissítés sikeres jelzés
                st.session_state[f'update_success_{event_id}'] = True
                
            event_modals[event_id] = event_details_modal(event, on_update=update_event)
    
    # Események csoportosítása dátum szerint
    events_by_date = {}
    
    for event in events:
        try:
            # Dátum kinyerése
            event_date = event.get('date', event.get('delivery_date', ''))
            if isinstance(event_date, (datetime.date, datetime.datetime)):
                event_date = event_date.strftime('%Y-%m-%d')
            
            # Hozzáadás a megfelelő dátumhoz
            if event_date not in events_by_date:
                events_by_date[event_date] = []
            events_by_date[event_date].append(event)
        except Exception as e:
            st.error(f"Hiba az esemény csoportosítása során: {str(e)}")
    
    # Minden dátumra nap kártya megjelenítése
    # Előbb rendezzük a dátumokat
    sorted_dates = sorted(events_by_date.keys())
    
    for date_str in sorted_dates:
        try:
            # Dátum objektummá alakítás
            day_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
            
            # Ellenőrizzük, hogy a tartományban van-e
            if day_date < start_date or day_date > end_date:
                continue
            
            # Dátum fejléc
            st.markdown(f"### {day_date.strftime('%Y. %B %d, %A')}")
            
            # Események kártyái ezen a napon
            for event in events_by_date[date_str]:
                event_id = event.get('id', '')
                if not event_id:
                    continue
                
                # Státusz meghatározása
                status = event.get('status', 'UNKNOWN')
                status_colors = {
                    'CREATED': '#90CAF9',
                    'CONFIRMED_BY_COMPANY': '#66BB6A',
                    'ACCEPTED_BY_USER': '#4CAF50',
                    'REJECTED_BY_USER': '#EF5350',
                    'FINALIZED': '#9C27B0',
                    'UNKNOWN': '#9E9E9E'
                }
                # Magyar fordítás a státuszokhoz
                status_translations = {
                    'CREATED': 'Létrehozva',
                    'CONFIRMED_BY_COMPANY': 'Vállalat által elfogadva',
                    'ACCEPTED_BY_USER': 'Felhasználó által elfogadva',
                    'REJECTED_BY_USER': 'Felhasználó által elutasítva',
                    'FINALIZED': 'Véglegesítve',
                    'UNKNOWN': 'Ismeretlen'
                }
                status_color = status_colors.get(status, '#9E9E9E')
                status_display = status_translations.get(status, 'Ismeretlen')
                
                # Esemény mennyiség és termék információ
                quantity = event.get('quantity', 0)
                product_name = event.get('product_name', event.get('product_type_id', 'Ismeretlen'))
                
                # Esemény kártya
                with st.container():
                    st.markdown(f"""
                    <div class="calendar-event-card" style="border-left: 4px solid {status_color};">
                        <div class="calendar-event-card-title">
                            <span class="calendar-event-status" style="background-color: {status_color};">{status_display}</span>
                            {product_name}
                        </div>
                        <div class="calendar-event-card-details">
                            <strong>Mennyiség:</strong> {quantity:.1f} kg<br>
                            <strong>Felhasználó:</strong> {event.get('user_name', 'Ismeretlen')}
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Részletek gomb
                    if st.button(f"Részletek", key=f"event_details_{event_id}"):
                        if event_id in event_modals:
                            event_modals[event_id]['open_modal']()
                            st.rerun()
                    
                    # Frissítési jelzés megjelenítése, ha a frissítés sikeres volt
                    if f'update_success_{event_id}' in st.session_state and st.session_state[f'update_success_{event_id}']:
                        st.success("Esemény frissítése sikeres!")
                        # Reset success flag
                        st.session_state[f'update_success_{event_id}'] = False
                
        except Exception as e:
            st.error(f"Hiba a nap kártya megjelenítése során: {str(e)}")
    
    # Dátum tartomány napok, amikre nincs esemény
    empty_dates = []
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str not in events_by_date:
            empty_dates.append(current_date)
        current_date += timedelta(days=1)
    
    # Ha vannak üres napok, jelenítsük meg azokat is egy "Nincs esemény" kártyával
    if empty_dates:
        st.markdown("### Napok esemény nélkül")
        # Max 5 üres nap megjelenítése, hogy ne legyen túl hosszú lista
        max_empty = 5
        for empty_date in empty_dates[:max_empty]:
            st.markdown(f"""
            <div class="calendar-event-card" style="border-left: 4px solid #9E9E9E; opacity: 0.7;">
                <div class="calendar-event-card-title">
                    {empty_date.strftime('%Y. %B %d, %A')}
                </div>
                <div class="calendar-event-card-details">
                    Nincs esemény ezen a napon
                </div>
            </div>
            """, unsafe_allow_html=True)
        
        # Ha több mint 5 üres nap van, jelezzük
        if len(empty_dates) > max_empty:
            st.markdown(f"... és még {len(empty_dates) - max_empty} nap esemény nélkül.")
            
    # Események részleteinek modális ablakainak megjelenítése
    for event_id, modal in event_modals.items():
        if modal['is_open']:
            # Nem kell semmit tenni, a modál már megjelenik a event_details_modal függvény hívásából
            pass