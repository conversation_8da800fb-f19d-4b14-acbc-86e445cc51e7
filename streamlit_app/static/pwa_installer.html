<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POM APP Telepítő</title>
    
    <!-- PWA Meta tagek -->
    <meta name="theme-color" content="#1a1a1a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="POM">
    
    <!-- Manifest link -->
    <link rel="manifest" href="/static/manifest.json">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9IiMyRjM2MzYiPjxyZWN0IHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiBmaWxsPSIjMkYzNjM2Ii8+PHRleHQgeD0iNjQiIHk9IjcwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjM2IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzAwRkY3RiI+UE9NPC90ZXh0Pjwvc3ZnPg==">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2F3636 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #00FF7F;
            position: relative;
            overflow-x: hidden;
        }
        
        /* Matrix háttér effekt */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, #00FF7F 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, #00AA55 1px, transparent 1px);
            background-size: 50px 50px;
            opacity: 0.1;
            animation: matrix 20s linear infinite;
            z-index: -1;
        }
        
        @keyframes matrix {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-50px); }
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            width: 100%;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00FF7F;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 
                0 0 20px rgba(0, 255, 127, 0.3),
                inset 0 0 20px rgba(0, 255, 127, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .logo {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #1a1a1a, #2F3636);
            border: 3px solid #00FF7F;
            border-radius: 20px;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: bold;
            color: #00FF7F;
            box-shadow: 
                0 0 15px rgba(0, 255, 127, 0.5),
                inset 0 0 15px rgba(0, 255, 127, 0.1);
            text-shadow: 0 0 10px #00FF7F;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from {
                box-shadow: 
                    0 0 15px rgba(0, 255, 127, 0.5),
                    inset 0 0 15px rgba(0, 255, 127, 0.1);
            }
            to {
                box-shadow: 
                    0 0 25px rgba(0, 255, 127, 0.8),
                    inset 0 0 20px rgba(0, 255, 127, 0.2);
            }
        }
        
        h1 {
            font-size: 32px;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 0 10px #00FF7F;
            color: #00FF7F;
            letter-spacing: 2px;
        }
        
        p {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
            line-height: 1.6;
            color: #CCFFCC;
        }
        
        .install-btn {
            background: linear-gradient(45deg, #00AA55, #00FF7F);
            color: #000000;
            border: 2px solid #00FF7F;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            display: inline-block;
            text-decoration: none;
            box-shadow: 0 5px 15px rgba(0, 255, 127, 0.3);
            transition: all 0.3s ease;
            margin: 8px;
            font-family: inherit;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .install-btn:hover {
            background: linear-gradient(45deg, #00FF7F, #00AA55);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 127, 0.5);
            text-shadow: 0 0 5px #000000;
        }
        
        .manual-btn {
            background: linear-gradient(45deg, #FF6B00, #FF8C00);
            border-color: #FF8C00;
            color: #000000;
        }
        
        .manual-btn:hover {
            background: linear-gradient(45deg, #FF8C00, #FF6B00);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.5);
        }
        
        .info-btn {
            background: linear-gradient(45deg, #6A0DAD, #9932CC);
            border-color: #9932CC;
            color: #FFFFFF;
        }
        
        .info-btn:hover {
            background: linear-gradient(45deg, #9932CC, #6A0DAD);
            box-shadow: 0 8px 25px rgba(153, 50, 204, 0.5);
        }
        
        .instructions {
            margin-top: 25px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid #00FF7F;
            border-radius: 10px;
            text-align: left;
            font-size: 14px;
            line-height: 1.8;
            color: #CCFFCC;
        }
        
        .instructions h3 {
            color: #00FF7F;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
            text-shadow: 0 0 5px #00FF7F;
        }
        
        .instructions h4 {
            color: #00AA55;
            margin: 15px 0 10px 0;
            font-size: 16px;
        }
        
        .step {
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
        }
        
        .step::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #00FF7F;
        }
        
        .hidden {
            display: none;
        }
        
        .terminal-text {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .logo {
                width: 80px;
                height: 80px;
                font-size: 28px;
            }
            
            h1 {
                font-size: 26px;
            }
            
            .install-btn {
                padding: 12px 20px;
                font-size: 14px;
                margin: 5px;
            }
        }

        /* Sci-fi loading effect */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #00FF7F;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo terminal-text">POM</div>
        <h1 class="terminal-text">POM APP Telepítő</h1>
        <p class="terminal-text">Telepítsd fel a POM APP alkalmazást a készülékédre!</p>
        
        <button id="installBtn" class="install-btn hidden terminal-text">
            🚀 Rendszer Telepítés
        </button>
        
        <a href="http://dns72.com" class="install-btn manual-btn terminal-text">
            🌐 Portál Megnyitása
        </a>
        
        <div id="instructions" class="instructions hidden">
            <h3 class="terminal-text">⚡ Kézi Telepítési Protokoll ⚡</h3>
            <div id="androidInstructions" class="hidden">
                <h4 class="terminal-text">🤖 Android Rendszer:</h4>
                <div class="step terminal-text">Chrome böngésző indítása</div>
                <div class="step terminal-text">Navigálás: http://dns72.com</div>
                <div class="step terminal-text">Menü interfész elérése (⋮)</div>
                <div class="step terminal-text">"Hozzáadás a kezdőképernyőhöz" kiválasztása</div>
                <div class="step terminal-text">Név beállítása és "Hozzáadás" végrehajtása</div>
            </div>
            
            <div id="iosInstructions" class="hidden">
                <h4 class="terminal-text">🍎 iOS Rendszer:</h4>
                <div class="step terminal-text">Safari böngésző elindítása</div>
                <div class="step terminal-text">Navigálás: http://dns72.com</div>
                <div class="step terminal-text">Megosztás protokoll aktiválása (⬆️)</div>
                <div class="step terminal-text">Görgetés és "Hozzáadás a kezdőképernyőhöz" kiválasztása</div>
                <div class="step terminal-text">Azonosító beállítása és "Hozzáadás" végrehajtása</div>
            </div>
        </div>
        
        <button id="showInstructions" class="install-btn info-btn terminal-text">
            📋 Telepítési Protokoll
        </button>
    </div>

    <script>
        let deferredPrompt;
        const installBtn = document.getElementById('installBtn');
        const showInstructionsBtn = document.getElementById('showInstructions');
        const instructions = document.getElementById('instructions');
        const androidInstructions = document.getElementById('androidInstructions');
        const iosInstructions = document.getElementById('iosInstructions');

        // PWA telepítési esemény figyelése
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installBtn.classList.remove('hidden');
        });

        // Telepítés gomb eseménykezelő
        installBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
                // Loading effect
                const originalText = installBtn.innerHTML;
                installBtn.innerHTML = '<span class="loading"></span> Telepítés...';
                installBtn.disabled = true;
                
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                
                if (outcome === 'accepted') {
                    installBtn.innerHTML = '✅ POM APP Telepítve';
                    setTimeout(() => {
                        window.location.href = 'http://dns72.com';
                    }, 1500);
                } else {
                    installBtn.innerHTML = originalText;
                    installBtn.disabled = false;
                }
                
                deferredPrompt = null;
            }
        });

        // Platform felismerés
        function detectPlatform() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isAndroid = /android/.test(userAgent);
            
            return { isIOS, isAndroid };
        }

        // Útmutató megjelenítése/elrejtése
        showInstructionsBtn.addEventListener('click', () => {
            const { isIOS, isAndroid } = detectPlatform();
            
            instructions.classList.toggle('hidden');
            
            if (!instructions.classList.contains('hidden')) {
                if (isIOS) {
                    iosInstructions.classList.remove('hidden');
                    androidInstructions.classList.add('hidden');
                } else if (isAndroid) {
                    androidInstructions.classList.remove('hidden');
                    iosInstructions.classList.add('hidden');
                } else {
                    androidInstructions.classList.remove('hidden');
                    iosInstructions.classList.remove('hidden');
                }
                
                showInstructionsBtn.innerHTML = '❌ Protokoll Bezárása';
            } else {
                showInstructionsBtn.innerHTML = '📋 Telepítési Protokoll';
            }
        });

        // Service Worker regisztrálása
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('data:text/javascript;base64,c2VsZi5hZGRFdmVudExpc3RlbmVyKCdpbnN0YWxsJywgZnVuY3Rpb24oZXZlbnQpIHsKICBldmVudC53YWl0VW50aWwoc2VsZi5za2lwV2FpdGluZygpKTsKfSk7CgpzZWxmLmFkZEV2ZW50TGlzdGVuZXIoJ2FjdGl2YXRlJywgZnVuY3Rpb24oZXZlbnQpIHsKICBldmVudC53YWl0VW50aWwoc2VsZi5jbGllbnRzLmNsYWltKCkpOwp9KTsKCnNlbGYuYWRkRXZlbnRMaXN0ZW5lcignZmV0Y2gnLCBmdW5jdGlvbihldmVudCkgewogIGV2ZW50LnJlc3BvbmRXaXRoKGZldGNoKGV2ZW50LnJlcXVlc3QpKTsKfSk7')
                .then(() => console.log('POM Service Worker activated'))
                .catch(() => console.log('POM Service Worker initialization failed'));
        }

        // Terminal-style welcome message
        console.log(`
        ██████╗  ██████╗ ███╗   ███╗
        ██╔══██╗██╔═══██╗████╗ ████║
        ██████╔╝██║   ██║██╔████╔██║
        ██╔═══╝ ██║   ██║██║╚██╔╝██║
        ██║     ╚██████╔╝██║ ╚═╝ ██║
        ╚═╝      ╚═════╝ ╚═╝     ╚═╝
        
        Rendszer inicializálva. Telepítésre kész.
        `);

        // iOS felismerés és automatikus útmutató megjelenítése
        window.addEventListener('load', () => {
            const { isIOS } = detectPlatform();
            
            // Ha iOS, automatikusan mutassa a lépéseket
            if (isIOS) {
                // Kis késés után jelenítse meg az útmutatót
                setTimeout(() => {
                    instructions.classList.remove('hidden');
                    iosInstructions.classList.remove('hidden');
                    androidInstructions.classList.add('hidden');
                    showInstructionsBtn.innerHTML = '❌ Protokoll Bezárása';
                }, 1000);
            }
        });
    </script>
</body>
</html>