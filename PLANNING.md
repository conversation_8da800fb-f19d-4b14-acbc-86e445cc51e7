# Mezőgazdasági Termékkezel<PERSON> Rendszer - Fejlesztői Útmutató

## 1. Ren<PERSON>zer Áttekintés

A Mezőgazdasági Termékkezelő Rendszer célja mezőgazdasági termelők és ügyintézők támogatása termékek és ajánlatok kezelésében. A rendszer Python-alapú, modern webes technológiákkal.

## 2. Technológiai Stack Összefoglaló

- **Backend:** Python 3.11, FastAPI, SQLAlchemy, Pydantic
- **Frontend:** Streamlit, Pandas, Plotly
- **Adatbázis:** PostgreSQL
- **Infrastruktúra:** <PERSON><PERSON>, <PERSON>er Compose, Git

## 3. Dokumentációs Útmutató LLM-ek Számára

### 3.1 Dokumentáció Elérhetősége és Navigáció
A rendszer teljes dokumentációját a `docs` mappában találod. **<PERSON>ig először a `docs/DOCS_INDEX.md` fájlt olvasd el**, ez a központi navigációs pont, amely útmutatást ad az összes dokumentum eléréséhez.

### 3.2 Dokumentációs Struktúra
A dokumentáció hierarchikusan szervezett:
```
docs/
├── Backend Layer/        # API, Service, Data Access, Infrastructure
├── Database Layer/       # Models, Migrations, Relationships
├── Frontend Layer/       # Components, Pages, API Integration, Utils
├── Deployment & Operations/ # Docker, Testing, Debugging
├── System Architecture/  # Rendszer diagramok, áttekintések
└── DOCS_INDEX.md         # Fő navigációs pont - KEZDD ITT!
```

### 3.3 Fontos Tudnivalók LLM Elemzéshez
- Egyes dokumentumok elavultak lehetnek - ellenőrizd a dátumokat
- Értesítési rendszer csak valós API-adatokat használ, nincs mock fallback, kivéve üdvözlő értesítés
- Widget key-ek hash-alapúak az ütközések elkerülésére
- Az API kompatibilitás miatt csak integer ID-jű értesítések jelölhetők olvasottnak
- A debug információs dobozok csak fejlesztői környezetben jelennek meg
- A statisztika számítások float konverzióval működnek a robusztusság érdekében

## 4. API és Fő Komponensek

### 4.1 Fő API Végpontok
- `/api/users` - Felhasználókezelés
- `/api/products` - Termékkezelés
- `/api/offers` - Ajánlatkezelés
- `/api/notifications` - Értesítési rendszer
- `/api/statistics` - Statisztikák és riportok

### 4.2 Legfrissebb Fejlesztések
- Ajánlat részletező komponens vizuális modernizációja (2025-04-26)
- Értesítési rendszer frissítése (2025-04-27)

## 5. Fejlesztői Környezet

A fejlesztői környezet Docker konténerekben fut. Minden környezeti beállítás a `config.Settings` osztályon keresztül, Pydantic segítségével töltődik be környezeti változókból.

## 6. Hibaelhárítás LLM Elemzéshez

Ha hibába ütközöl a kód elemzése során, nézd meg a következő helyeken a megoldást:
- `docs/Deployment & Operations/Debugging/debug-architecture.md`
- `docs/Frontend Layer/Core Configuration/streamlit_app_config-architecture-doc.md`
- `docs/Backend Layer/Backend - short-architecture-doc.md`

## 7. Különleges Implementációs Részletek

- Frontend komponenseknél minden gomb egyedi key-t kap a DuplicateWidgetID hibák elkerülésére
- Hibakezelés és fallback logika van implementálva hiányzó adatok esetére
- Az értesítési feed UI-ja csak valódi értesítéseket jelenít meg
- Ha nincsenek értesítések, minden felhasználó kap egy üdvözlő értesítést`
