**Objective:**
Analyze the entire codebase in the current repository to generate a comprehensive set of "New Project Documentation." The goal is to produce the foundational documents that would typically be created at the start of a project. This documentation should be detailed enough for a new developer to understand the project's purpose, architecture, and technical implementation from scratch.

**Instructions:**
You are an expert software architect and technical writer. Perform a deep analysis of the provided source code and generate the following documents in a single, well-formatted Markdown file.

---

### 1. Project Charter (Inferred)

Infer the project's high-level goals and scope based on the codebase's functionality.

*   **Project Purpose and Business Value:** What is the primary problem this software solves? Who are the likely end-users?
*   **Key Features and Functionality:** List the top 5-7 core features available in the application.
*   **Scope:** Based on the features, define what is considered "in-scope" for this project.

---

### 2. Requirements Document (Extracted)

Extract the functional and non-functional requirements from the code.

*   **Functional Requirements:** Detail the specific actions the system can perform. For example, list API endpoints and their purposes, user-facing features, and data processing capabilities.
*   **Non-Functional Requirements:** Infer requirements related to performance, security, and maintainability. For example:
    *   Identify any dependencies related to authentication or authorization (e.g., JWT, OAuth).
    *   List any libraries used for logging, monitoring, or testing.
    *   Note any configurations that suggest scalability or performance considerations.

---

### 3. Software Design Document (SDD)

Generate a detailed technical design document based on the code structure and components.

*   **System Architecture Overview:**
    *   Describe the high-level architecture (e.g., Monolith, Microservices, Client-Server).
    *   Identify the main components, modules, or services and their responsibilities.
    *   Generate a system architecture diagram using Mermaid syntax (e.g., a flowchart or component diagram).

*   **Technology Stack and Dependencies:**
    *   List the primary programming languages, frameworks, and platforms used.
    *   List all key external libraries and dependencies, along with their versions and purpose (e.g., from `package.json`, `requirements.txt`, `pom.xml`).

*   **Data Design:**
    *   Analyze the database schema, models, or data structures.
    *   Describe the main data entities and their relationships.
    *   Detail how data is stored, accessed, and managed.

*   **API and Interface Specification:**
    *   Document the key public-facing APIs or internal service interfaces.
    *   For each major endpoint, specify its purpose, request/response format, and required parameters.

---

### 4. Getting Started Guide for New Developers

Create a step-by-step guide to help a new developer set up and run the project locally.

*   **Prerequisites:** List all required software, tools, and environment variables needed to build and run the project.
*   **Installation Steps:** Provide clear, command-line instructions for cloning the repository, installing dependencies, and performing initial setup.
*   **Configuration:** Explain how to configure the application, referencing any `.env.example` or configuration files.
*   **Running the Application:** Provide the command to start the application locally.
*   **Running Tests:** Provide the command to execute the test suite.

---

**Output Format:**
Present the entire output as a single, clean Markdown file. Use clear headers and formatting for each section as specified above.
