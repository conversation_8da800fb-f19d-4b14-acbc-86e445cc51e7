#!/usr/bin/env python3

# Add the get_offer_statistics function to offers.py
statistics_function = '''

def get_offer_statistics(params=None):
    """
    Aj<PERSON>lat statisztikák lekérése az API-n keresztül.
    
    Args:
        params (dict, optional): <PERSON><PERSON><PERSON><PERSON><PERSON> paraméterek (product_type_id, category_id, quality_grade_id, date_from, date_to)
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a statisztikák vagy hibaüzenet
    """
    try:
        logger.info(f"Statisztikák lekérése paraméterekkel: {params}")
        
        # Build query parameters
        query_params = {}
        if params:
            for key, value in params.items():
                if value is not None:
                    query_params[key] = value
        
        response = requests.get(
            f"{config.API_BASE_URL}/api/offers/statistics",
            params=query_params,
            headers=get_auth_headers(),
            timeout=30
        )
        
        logger.info(f"Statistics API response status: {response.status_code}")
        
        if response.status_code == 200:
            statistics = response.json()
            logger.info(f"Statistics API response: {statistics}")
            return True, statistics
        
        # Hibaüzenet feldolgozása
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Statisztikák lekérdezési hiba")
        except Exception:
            error_message = f"Statisztikák lekérdezési hiba (status: {response.status_code}): {response.text}"
        
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága a statisztikák megtekintéséhez"
        
        if response.status_code == 422:
            return False, f"Érvénytelen paraméterek: {error_message}"
        
        return False, error_message
        
    except requests.Timeout:
        return False, "A statisztikák lekérése időtúllépés miatt megszakadt"
    except requests.ConnectionError:
        return False, "Nem sikerült kapcsolódni az API szerverhez a statisztikák lekéréséhez"
    except Exception as e:
        logger.error(f"Hiba a statisztikák lekérése során: {str(e)}")
        return False, f"Váratlan hiba a statisztikák lekérésekor: {str(e)}"
'''

# Read the current file
with open('/home/<USER>/__GIT/termelo_v4/streamlit_app/api/offers.py', 'r') as f:
    content = f.read()

# Check if function already exists
if 'def get_offer_statistics(' not in content:
    # Add the function to the end
    with open('/home/<USER>/__GIT/termelo_v4/streamlit_app/api/offers.py', 'a') as f:
        f.write(statistics_function)
    print("✅ get_offer_statistics function added successfully!")
else:
    print("ℹ️ get_offer_statistics function already exists!")