#!/usr/bin/env python3
"""
Test script for Modern Offer Detail UI
"""
import streamlit as st
import sys
import os
from datetime import datetime, timedelta

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(page_title="Modern Offer Detail Test", layout="wide")

st.title("🎨 Modern Offer Detail UI Test")

# Test data
test_offer = {
    'id': 123,
    'status': 'CONFIRMED_BY_COMPANY',
    'quantity_in_kg': 150.5,
    'price': 850,
    'confirmed_quantity': 150,
    'confirmed_price': 850,
    'created_at': '2024-04-27T08:30:00Z',
    'confirmed_at': '2024-04-28T10:00:00Z',
    'delivery_date': (datetime.now() + timedelta(days=5)).isoformat(),
    'note': 'Premium minőségű bio termékek. Gondos csomagolás, id<PERSON><PERSON> történő szállít<PERSON> garantált.',
    'quality_parameters': {
        'Méret': 'Large',
        'Minőség': 'Extra',
        'Tanúsítvány': 'Bio',
        'Származás': 'Magyarország'
    },
    'user': {
        'contact_name': 'Nagy János',
        'company_name': 'BioFarm Kft.',
        'email': '<EMAIL>',
        'phone': '+36 30 123 4567',
        'address': '1234 Budapest, Kossuth Lajos utca 10.'
    },
    'product_type': {
        'name': 'Bio Sárgarépa',
        'category': {'name': 'Gyökérzöldségek'},
        'description': 'Friss, ropogós bio sárgarépa, gazdag béta-karotinban.',
        'unit': 'kg'
    }
}

# Sidebar options
st.sidebar.header("Test Options")

# Status selector
status_options = ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED']
test_offer['status'] = st.sidebar.selectbox("Select Status", status_options, index=1)

# Quantity and price sliders
test_offer['quantity_in_kg'] = st.sidebar.slider("Quantity (kg)", 10.0, 1000.0, 150.5, 0.5)
test_offer['price'] = st.sidebar.slider("Price (Ft/kg)", 100, 2000, 850, 50)

# Confirmed values
if st.sidebar.checkbox("Show confirmed values", value=True):
    test_offer['confirmed_quantity'] = st.sidebar.slider("Confirmed Quantity (kg)", 10.0, 1000.0, 150.0, 0.5)
    test_offer['confirmed_price'] = st.sidebar.slider("Confirmed Price (Ft/kg)", 100, 2000, 850, 50)
else:
    test_offer.pop('confirmed_quantity', None)
    test_offer.pop('confirmed_price', None)

# Test different views
tab1, tab2, tab3 = st.tabs(["Modern UI", "Resizable Panels", "Component Tests"])

with tab1:
    st.header("Modern Offer Detail View")
    
    try:
        from pages.operator.offer_management.modern_offer_detail_v2 import ModernOfferDetailView
        
        modern_view = ModernOfferDetailView(test_offer)
        modern_view.render()
        
        st.success("✅ Modern UI rendered successfully!")
        
    except Exception as e:
        st.error(f"Error loading Modern UI: {e}")
        st.exception(e)

with tab2:
    st.header("Resizable Panels Demo")
    
    try:
        from pages.operator.offer_management.resizable_panels import render_resizable_panel_system
        
        st.info("🔧 Drag the panel edges to resize them. Double-click headers to collapse/expand.")
        render_resizable_panel_system()
        
        st.success("✅ Resizable panels rendered successfully!")
        
    except Exception as e:
        st.error(f"Error loading resizable panels: {e}")
        st.exception(e)

with tab3:
    st.header("Individual Component Tests")
    
    # Test hero section
    if st.checkbox("Test Hero Section", value=True):
        st.subheader("Hero Section")
        try:
            from pages.operator.offer_management.modern_offer_detail_v2 import ModernOfferDetailView
            view = ModernOfferDetailView(test_offer)
            view.render_hero_section()
        except Exception as e:
            st.error(f"Hero section error: {e}")
    
    # Test cards
    if st.checkbox("Test Card Components", value=True):
        st.subheader("Card Components")
        
        col1, col2 = st.columns(2)
        
        with col1:
            try:
                from pages.operator.offer_management.modern_offer_detail_v2 import ModernOfferDetailView
                view = ModernOfferDetailView(test_offer)
                view.render_offer_summary_card()
            except Exception as e:
                st.error(f"Summary card error: {e}")
        
        with col2:
            try:
                view.render_product_details_card()
            except Exception as e:
                st.error(f"Product card error: {e}")
    
    # Test visualizations
    if st.checkbox("Test Data Visualizations", value=True):
        st.subheader("Data Visualizations")
        try:
            from pages.operator.offer_management.modern_offer_detail_v2 import ModernOfferDetailView
            view = ModernOfferDetailView(test_offer)
            view.render_data_visualizations()
        except Exception as e:
            st.error(f"Visualization error: {e}")

# Footer
st.markdown("---")
st.markdown("""
### 🚀 Modern UI Features

- **Gradient Hero Section** - Eye-catching header with key metrics
- **Animated Cards** - Smooth hover effects and transitions
- **Interactive Charts** - Plotly-based visualizations
- **Resizable Panels** - CodePen-inspired drag-to-resize functionality
- **Floating Action Buttons** - Quick access to common actions
- **Responsive Design** - Adapts to all screen sizes
- **Modern Color Palette** - Professional and visually appealing

This modern design maintains all functionality while providing a much more engaging user experience!
""")