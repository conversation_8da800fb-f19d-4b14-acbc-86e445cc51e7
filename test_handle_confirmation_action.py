#!/usr/bin/env python3
"""
Test script for handle_confirmation_action function
Demonstrates the Quick Action Bar integration functionality
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Handle Confirmation Action Test", 
    layout="wide", 
    initial_sidebar_state="expanded"
)

st.title("🎯 Handle Confirmation Action Test")
st.markdown("### Testing the Quick Action Bar integration function")

# Test data
test_offer = {
    'id': 789,
    'status': 'CREATED',
    'quantity_in_kg': 500,
    'price': 400,
    'product_type': {
        'name': 'Bio cukkini',
        'category': {'name': 'Zöldségek'}
    },
    'user': {
        'contact_name': 'Szabó Anna',
        'company_name': 'Zöld Kert Bt.',
        'email': '<EMAIL>',
        'phone': '+36309876543'
    },
    'delivery_date': '2025-07-01'
}

st.info("""
### 📋 handle_confirmation_action Function Test

Ez a teszt bemutatja, ho<PERSON>an m<PERSON>ködik a `handle_confirmation_action` függvény,
amely a Quick Action Bar és a stabil confirmation dialog közötti integrációt biztosítja.

**Működési elv:**
1. Quick Action Bar "Visszaigazolás" gomb → `quick_confirm_clicked_{offer_id}` session state
2. `handle_confirmation_action()` észleli ezt és modal-t nyit
3. Felhasználó kitölti a dialógust
4. Függvény visszaadja a confirmation adatokat
""")

# Sidebar controls
st.sidebar.markdown("### ⚙️ Test Controls")

# Test mode selector
test_mode = st.sidebar.radio(
    "Test Mode:",
    ["Step by Step Demo", "Automated Flow", "State Debugging"]
)

# Offer configuration
test_offer['quantity_in_kg'] = st.sidebar.number_input(
    "Original Quantity (kg):",
    min_value=1,
    value=500,
    step=50
)

test_offer['price'] = st.sidebar.number_input(
    "Original Price (Ft/kg):",
    min_value=1,
    value=400,
    step=25
)

st.markdown("---")

if test_mode == "Step by Step Demo":
    st.markdown("### 🔢 Step by Step Demo")
    
    # Step 1: Simulate Quick Action Bar click
    st.markdown("#### Step 1: Simulate Quick Action Bar")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("✓ Visszaigazolás", type="primary", key="step1_confirm"):
            st.session_state[f"quick_confirm_clicked_{test_offer['id']}"] = True
            st.success("✅ Quick confirm clicked! Session state set.")
    
    with col2:
        if st.button("🔄 Reset State", key="step1_reset"):
            # Clear all related session state
            keys_to_clear = [k for k in st.session_state.keys() if str(test_offer['id']) in k]
            for key in keys_to_clear:
                del st.session_state[key]
            st.success("🧹 All session state cleared!")
    
    with col3:
        st.metric("Session Keys", len([k for k in st.session_state.keys() if str(test_offer['id']) in k]))
    
    # Step 2: Show handle_confirmation_action in action
    st.markdown("#### Step 2: Handle Confirmation Action")
    
    if st.button("🎯 Run handle_confirmation_action()", key="step2_handle"):
        try:
            from pages.operator.offer_management.stable_confirmation_dialog import handle_confirmation_action
            
            result = handle_confirmation_action(test_offer, test_offer['id'])
            
            if result:
                quantity, price, note = result
                st.success(f"""
                ### ✅ Confirmation Received!
                - **Quantity**: {quantity:,.0f} kg
                - **Price**: {price:,.0f} Ft/kg  
                - **Total Value**: {(quantity * price):,.0f} Ft
                - **Note**: {note or 'No note'}
                """)
                
                # Show what the API call would look like
                st.code(f"""
# API Call that would be made:
update_offer_status(
    offer_id={test_offer['id']},
    new_status="CONFIRMED_BY_COMPANY", 
    confirmation_data={{
        "confirmed_quantity": {quantity},
        "confirmed_price": {price},
        "note": "{note or ''}"
    }}
)
                """, language="python")
                
            elif result is None:
                st.info("ℹ️ No action taken - dialog not completed yet")
            else:
                st.warning("⚠️ Confirmation was cancelled")
                
        except Exception as e:
            st.error(f"❌ Error: {e}")
            st.exception(e)
    
    # Step 3: Show session state
    st.markdown("#### Step 3: Session State Monitoring")
    
    relevant_keys = [k for k in st.session_state.keys() if str(test_offer['id']) in k]
    
    if relevant_keys:
        st.markdown("**Current session state:**")
        for key in relevant_keys:
            st.write(f"- `{key}`: {st.session_state[key]}")
    else:
        st.write("No relevant session state found")

elif test_mode == "Automated Flow":
    st.markdown("### 🤖 Automated Flow Test")
    
    # Auto-trigger the flow
    if st.button("🚀 Start Automated Test", type="primary", key="auto_start"):
        st.session_state["auto_test_running"] = True
        st.session_state[f"quick_confirm_clicked_{test_offer['id']}"] = True
    
    if st.session_state.get("auto_test_running", False):
        try:
            from pages.operator.offer_management.stable_confirmation_dialog import handle_confirmation_action
            
            st.info("🔄 Running handle_confirmation_action automatically...")
            
            # Call the function
            result = handle_confirmation_action(test_offer, test_offer['id'])
            
            if result:
                quantity, price, note = result
                st.success(f"""
                ### 🎉 Automated Test Complete!
                **Result**: Confirmation successful
                - Quantity: {quantity:,.0f} kg
                - Price: {price:,.0f} Ft/kg
                - Note: {note or 'None'}
                """)
                
                # Stop the automated test
                st.session_state["auto_test_running"] = False
                
            elif result is False:
                st.warning("❌ Automated Test: Confirmation was cancelled")
                st.session_state["auto_test_running"] = False
                
            else:
                # Dialog is showing - this is expected
                st.info("📋 Dialog is active - fill out the form to complete the test")
                
                # Add a cancel button for the automated test
                if st.button("🛑 Cancel Automated Test", key="auto_cancel"):
                    st.session_state["auto_test_running"] = False
                    # Clear related session state
                    keys_to_clear = [k for k in st.session_state.keys() if str(test_offer['id']) in k]
                    for key in keys_to_clear:
                        del st.session_state[key]
                    st.rerun()
                
        except Exception as e:
            st.error(f"❌ Automated test failed: {e}")
            st.session_state["auto_test_running"] = False

elif test_mode == "State Debugging":
    st.markdown("### 🔍 Session State Debugging")
    
    # Show all session state
    st.markdown("#### All Session State")
    all_keys = list(st.session_state.keys())
    
    if all_keys:
        for key in sorted(all_keys):
            if str(test_offer['id']) in key:
                st.write(f"🔑 **{key}**: `{st.session_state[key]}`")
            else:
                st.write(f"🔑 {key}: {type(st.session_state[key]).__name__}")
    else:
        st.write("No session state found")
    
    # Manual state manipulation
    st.markdown("#### Manual State Manipulation")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Set quick_confirm_clicked", key="debug_set_clicked"):
            st.session_state[f"quick_confirm_clicked_{test_offer['id']}"] = True
            st.success("Set quick_confirm_clicked")
    
    with col2:
        if st.button("Set show_modal", key="debug_set_modal"):
            st.session_state[f"show_confirmation_modal_{test_offer['id']}"] = True
            st.success("Set show_confirmation_modal")
    
    # Clear specific states
    st.markdown("#### Clear Specific States")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("Clear clicked state", key="debug_clear_clicked"):
            key = f"quick_confirm_clicked_{test_offer['id']}"
            if key in st.session_state:
                del st.session_state[key]
                st.success("Cleared clicked state")
    
    with col2:
        if st.button("Clear modal state", key="debug_clear_modal"):
            key = f"show_confirmation_modal_{test_offer['id']}"
            if key in st.session_state:
                del st.session_state[key]
                st.success("Cleared modal state")
    
    with col3:
        if st.button("Clear all states", key="debug_clear_all"):
            keys_to_clear = [k for k in st.session_state.keys() if str(test_offer['id']) in k]
            for key in keys_to_clear:
                del st.session_state[key]
            st.success(f"Cleared {len(keys_to_clear)} states")

# Function signature documentation
st.markdown("---")
st.markdown("### 📚 Function Documentation")

st.code("""
def handle_confirmation_action(offer: Dict[str, Any], offer_id: int) -> Optional[Tuple[float, float, str]]:
    '''
    Handle the confirmation action from Quick Action Bar.
    
    Args:
        offer: The offer data
        offer_id: The offer ID
        
    Returns:
        Tuple of (quantity, price, note) if confirmed, None otherwise
    '''
""", language="python")

st.markdown("""
**Flow:**
1. Quick Action Bar sets `quick_confirm_clicked_{offer_id}` = True
2. `handle_confirmation_action()` detects this and sets modal state
3. Modal shows confirmation dialog
4. User completes dialog
5. Function returns `(quantity, price, note)` or `None`

**Integration in offer_detail.py:**
```python
if action == "confirm":
    from .stable_confirmation_dialog import handle_confirmation_action
    result = handle_confirmation_action(offer, offer_id)
    if result:
        quantity, price, note = result
        # Call API...
```
""")

# Footer
st.markdown("---")
st.markdown("""
**🎯 Summary**: The `handle_confirmation_action` function provides a clean, 
direct integration point between the Quick Action Bar and the stable confirmation dialog system.
""")