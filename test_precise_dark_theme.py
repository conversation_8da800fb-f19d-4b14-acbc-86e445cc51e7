#!/usr/bin/env python3
"""
Test script for Precise Dark Theme Implementation
Pontos dark theme implement<PERSON><PERSON><PERSON> tesztelése
"""
import streamlit as st
import sys
import os
from datetime import datetime, timedelta

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Precise Dark Theme Test", 
    layout="wide", 
    initial_sidebar_state="collapsed"
)

# Custom CSS for the test page
st.markdown("""
<style>
    /* Dark background for the entire app */
    .stApp {
        background-color: #0a0a0a;
    }
    
    /* Hide Streamlit components for cleaner view */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* Custom title styling */
    .main-title {
        font-size: 2rem;
        color: #ffffff;
        margin-bottom: 2rem;
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
        border-radius: 12px;
        border: 1px solid #2a2a2a;
    }
</style>
""", unsafe_allow_html=True)

# Title
st.markdown('<h1 class="main-title">🌙 Precise Dark Theme - Offer Detail View</h1>', unsafe_allow_html=True)

# Test data - Confirmed offer with all data
test_offer = {
    'id': 2024,
    'status': 'CONFIRMED_BY_COMPANY',
    'quantity_in_kg': 250,
    'price': 850,
    'confirmed_quantity': 245,
    'confirmed_price': 850,
    'created_at': (datetime.now() - timedelta(days=3)).isoformat(),
    'confirmed_at': (datetime.now() - timedelta(days=2)).isoformat(),
    'delivery_date': (datetime.now() + timedelta(days=4)).isoformat(),
    'note': 'Premium bio termékek, gondos csomagolás. Kizárólag első osztályú minőség.',
    'user': {
        'contact_name': 'Kovács András',
        'company_name': 'BioGarden Kft.',
        'email': '<EMAIL>',
        'phone': '+36 30 987 6543',
        'address': '1234 Budapest, Zöldség utca 15.'
    },
    'product_type': {
        'name': 'Bio paradicsom',
        'category': {'name': 'Zöldségek'},
        'description': 'Friss, érett bio paradicsom, gazdag ízvilág'
    },
    'quality_parameters': {
        'Méret': 'Large (70-85mm)',
        'Szín': 'Élénk piros',
        'Érettség': 'Optimális',
        'Tanúsítvány': 'HU-ÖKO-01'
    },
    # Timeline adatok
    'status_history': [
        {
            'status': 'CREATED',
            'date': (datetime.now() - timedelta(days=3)).isoformat(),
            'user': 'Kovács András'
        },
        {
            'status': 'CONFIRMED_BY_COMPANY',
            'date': (datetime.now() - timedelta(days=2)).isoformat(),
            'user': 'Rendszer'
        }
    ]
}

# Feature comparison
with st.expander("📊 Összehasonlítás: Régi vs Új Design", expanded=False):
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🌑 Régi Dark Theme
        - Világosabb háttér (#0e1117)
        - Kerek sarkok mindenhol
        - Sok animáció
        - Komplex színátmenetek
        - Kisebb térközök
        """)
    
    with col2:
        st.markdown("""
        ### 🌙 Új Precise Dark Theme
        - Sötétebb háttér (#0a0a0a) ✨
        - Tisztább kártya design ✨
        - Színes felső sávok ✨
        - Nagyobb térközök ✨
        - Grid layout ✨
        """)

# Main content
try:
    from pages.operator.offer_management.modern_dark_theme_detail import DarkThemeOfferDetail
    
    # Create and render the dark theme view
    dark_view = DarkThemeOfferDetail(test_offer)
    dark_view.render()
    
except Exception as e:
    st.error(f"❌ Hiba a Dark Theme betöltésekor: {e}")
    st.exception(e)

# Visual guide
with st.expander("🎨 Design Elemek Magyarázata", expanded=False):
    st.markdown("""
    ### Főbb design elemek:
    
    1. **Header Card** 
       - Nagy méretű fejléc kártya gradiens háttérrel
       - Színes felső csík (6px)
       - Státusz badge tiszta megjelenéssel
    
    2. **Info Cards**
       - Minden kártyán színes felső sáv (4px)
       - Ikon + cím a fejlécben
       - Info sorok label/value párokkal
       - Highlight színezés fontos értékeknél
    
    3. **Grid Layout**
       - 2 oszlopos elrendezés
       - 2rem térköz a kártyák között
       - Responsive design
    
    4. **State Indicators**
       - Alul elhelyezett állapot jelzők
       - Aktív/inaktív állapotok
       - Hover effektek
    
    5. **Színek**
       - Háttér: #0a0a0a (nagyon sötét)
       - Kártya: #1a1a1a
       - Szöveg: #ffffff / #a0a0a0
       - Státusz színek: vibráló árnyalatok
    """)

# Performance info
with st.expander("⚡ Performance Optimalizációk", expanded=False):
    st.markdown("""
    - **Minimális animációk**: Csak a szükséges hover effektek
    - **Optimalizált CSS**: Tiszta, egyszerű szabályok
    - **Grid layout**: Natív CSS grid a jobb teljesítményért
    - **Lazy loading**: Komponensek csak szükség esetén töltődnek
    """)

# Footer info
st.markdown("---")
st.info("""
🎯 **Precise Dark Theme**: A példaképek alapján pontosan implementált sötét téma.
Tisztább design, jobb olvashatóság, professzionális megjelenés.
""")