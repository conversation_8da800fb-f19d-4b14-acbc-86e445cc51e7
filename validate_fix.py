#!/usr/bin/env python3
"""
Validate the duplicate widget key fix
"""

import ast
import sys

def validate_data_coordinator_fix():
    """Validate that the data_coordinator.py fix is properly implemented"""
    print("🔍 Validating data_coordinator.py fix...")
    
    try:
        with open("streamlit_app/pages/operator/offer_management/data_coordinator.py", 'r') as f:
            content = f.read()
        
        # Parse the AST to validate syntax
        try:
            tree = ast.parse(content)
            print("✅ File has valid Python syntax")
        except SyntaxError as e:
            print(f"❌ Syntax error: {e}")
            return False
        
        # Check for the problematic pattern (should NOT exist)
        if 'key=f"trace_details_{trace_id[:8]}"' in content:
            print("❌ Old problematic pattern still exists!")
            return False
        else:
            print("✅ Old problematic pattern removed")
        
        # Check for the fix (should exist)
        if 'generate_unique_widget_key("trace_details", trace_id, "debug_info")' in content:
            print("✅ New unique key generation found")
        else:
            print("❌ New unique key generation not found")
            return False
        
        # Check imports
        required_imports = [
            "from .widget_key_manager import",
            "generate_unique_widget_key",
            "cleanup_widget_keys_in_session"
        ]
        
        for imp in required_imports:
            if imp in content:
                print(f"✅ Required import found: {imp}")
            else:
                print(f"❌ Missing import: {imp}")
                return False
        
        # Check cleanup functions are called
        if "cleanup_widget_keys_in_session(" in content:
            print("✅ Cleanup functions are being called")
        else:
            print("❌ Cleanup functions not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def validate_widget_key_manager():
    """Validate that widget_key_manager.py exists and has required functions"""
    print("\n🔍 Validating widget_key_manager.py...")
    
    try:
        with open("streamlit_app/pages/operator/offer_management/widget_key_manager.py", 'r') as f:
            content = f.read()
        
        # Parse the AST to validate syntax
        try:
            tree = ast.parse(content)
            print("✅ Widget key manager has valid Python syntax")
        except SyntaxError as e:
            print(f"❌ Syntax error in widget key manager: {e}")
            return False
        
        # Check for required functions
        required_functions = [
            "def generate_unique_widget_key(",
            "def cleanup_widget_keys_in_session(",
            "def get_unique_widget_key(",
            "def cleanup_widget_keys("
        ]
        
        for func in required_functions:
            if func in content:
                print(f"✅ Required function found: {func.split('(')[0]}")
            else:
                print(f"❌ Missing function: {func.split('(')[0]}")
                return False
        
        # Check for key components
        if "uuid.uuid4()" in content:
            print("✅ UUID generation found")
        else:
            print("❌ UUID generation missing")
            return False
        
        if "time.time()" in content:
            print("✅ Timestamp generation found")
        else:
            print("❌ Timestamp generation missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_line_249_fix():
    """Specifically check that line 249 area has been fixed"""
    print("\n🔍 Checking specific line where error occurred...")
    
    try:
        with open("streamlit_app/pages/operator/offer_management/data_coordinator.py", 'r') as f:
            lines = f.readlines()
        
        # Check around line 288 (previously 249) for the fix
        for i, line in enumerate(lines[280:300], 281):
            if "show_trace_details = st.checkbox" in line:
                print(f"✅ Found checkbox at line {i}")
                # Check previous lines for unique key generation
                for j in range(max(0, i-5), i):
                    if "generate_unique_widget_key" in lines[j]:
                        print(f"✅ Unique key generation found at line {j+1}")
                        print(f"   Content: {lines[j].strip()}")
                        return True
                break
        
        print("❌ Could not find the specific fix location")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚨 DUPLICATE WIDGET KEY FIX VALIDATION")
    print("=" * 50)
    
    all_passed = True
    
    # Validate data coordinator fix
    if not validate_data_coordinator_fix():
        all_passed = False
    
    # Validate widget key manager
    if not validate_widget_key_manager():
        all_passed = False
    
    # Check specific line fix
    if not check_line_249_fix():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 VALIDATION SUCCESSFUL!")
        print("✅ The duplicate widget key issue has been properly fixed")
        print("🔧 Key improvements:")
        print("   • Unique widget keys using UUID + timestamp")
        print("   • Proper cleanup of old widget keys")
        print("   • Correct imports and function calls")
        print("   • Removed problematic trace_id[:8] pattern")
        print("\n🚀 The application should no longer experience duplicate widget key errors!")
    else:
        print("❌ VALIDATION FAILED!")
        print("   Some issues need to be addressed")
    
    sys.exit(0 if all_passed else 1)