#!/bin/bash

# Debug menü wrapper script
# Ez a script lehetővé teszi a debug menü indítását a projekt gyökérkönyvtárából

# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a debug könyvt<PERSON>r lé<PERSON>zik-e
if [ ! -d "debug" ]; then
    echo "Hiba: A debug könyvtár nem található!"
    exit 1
fi

# <PERSON><PERSON><PERSON>, hogy a debug_menu.sh létezik-e
if [ ! -f "debug/debug_menu.sh" ]; then
    echo "Hiba: A debug_menu.sh nem található a debug könyvtárban!"
    exit 1
fi

# <PERSON><PERSON>zük, hogy a debug_menu.sh futtatható-e
if [ ! -x "debug/debug_menu.sh" ]; then
    echo "A debug_menu.sh nem futtatható, futtathatóvá tétel..."
    chmod +x debug/debug_menu.sh
fi

# Debug menü indítása
cd debug && ./debug_menu.sh 