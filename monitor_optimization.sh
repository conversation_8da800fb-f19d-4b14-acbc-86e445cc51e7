#!/bin/bash

echo "🔍 Monitoring Optimized Price Trend Implementation"
echo "================================================="

echo "📊 Checking current container status..."
docker ps | grep -E "(streamlit|backend)"

echo ""
echo "🔍 Checking recent Streamlit logs for optimized calls..."
docker logs streamlit --tail 50 | grep -i "optimized\|ártrend\|price_trend" | tail -10

echo ""
echo "🔍 Checking recent backend logs for statistics calls..."
docker logs backend --tail 50 | grep -i "statistics" | tail -10

echo ""
echo "✅ Monitoring complete!"
echo ""
echo "💡 To test the optimization:"
echo "1. Go to http://localhost:8501"
echo "2. Navigate to offer management"
echo "3. Click on an offer to view details"
echo "4. Expand the 'Ártrend grafikon' section"
echo "5. Watch for optimized API calls in the logs"