#!/usr/bin/env python3
"""
Test script to verify that unit handling works for both 'kg' and 'db' units.
Tests the format_quantity function and display components.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'streamlit_app'))

from utils.formatting import format_quantity

def test_format_quantity():
    """Test the format_quantity function with different units"""
    
    print("Testing format_quantity function:")
    print("=" * 50)
    
    # Test with kg
    test_cases_kg = [
        (100, "kg", "100 kg"),
        (1000, "kg", "1 tonna"),
        (1234.56, "kg", "1,23 tonna"),
        (500.5, "kg", "500,5 kg"),
        (2500, "kg", "2,50 tonna")
    ]
    
    print("Testing 'kg' unit:")
    for quantity, unit, expected in test_cases_kg:
        result = format_quantity(quantity, unit)
        status = "✓" if result == expected else "✗"
        print(f"  {status} format_quantity({quantity}, '{unit}') = '{result}' (expected: '{expected}')")
    
    print()
    
    # Test with db
    test_cases_db = [
        (100, "db", "100 db"),
        (1000, "db", "1 000 db"),
        (1234.56, "db", "1 234 db"),  # Should show as integer
        (500.5, "db", "500 db"),     # Should show as integer
        (2500, "db", "2 500 db")
    ]
    
    print("Testing 'db' unit:")
    for quantity, unit, expected in test_cases_db:
        result = format_quantity(quantity, unit)
        status = "✓" if result == expected else "✗"
        print(f"  {status} format_quantity({quantity}, '{unit}') = '{result}' (expected: '{expected}')")

def test_offer_data_structures():
    """Test how offer data structures would look with different units"""
    
    print("\n\nTesting offer data structures:")
    print("=" * 50)
    
    # Sample offer with kg
    offer_kg = {
        'id': 1,
        'quantity_value': 1500,
        'quantity_unit': 'kg',
        'price': 450,
        'confirmed_quantity': 1200,
        'confirmed_price': 460
    }
    
    # Sample offer with db
    offer_db = {
        'id': 2,
        'quantity_value': 2500,
        'quantity_unit': 'db',
        'price': 15,
        'confirmed_quantity': 2000,
        'confirmed_price': 18
    }
    
    offers = [offer_kg, offer_db]
    
    for offer in offers:
        unit = offer.get('quantity_unit', 'kg')
        print(f"\nOffer {offer['id']} ({unit} unit):")
        print(f"  Original quantity: {format_quantity(offer['quantity_value'], unit)}")
        print(f"  Original price: {offer['price']} Ft/{unit}")
        print(f"  Original total: {offer['quantity_value'] * offer['price']:,.0f} Ft")
        print(f"  Confirmed quantity: {format_quantity(offer['confirmed_quantity'], unit)}")
        print(f"  Confirmed price: {offer['confirmed_price']} Ft/{unit}")
        print(f"  Confirmed total: {offer['confirmed_quantity'] * offer['confirmed_price']:,.0f} Ft")

if __name__ == "__main__":
    test_format_quantity()
    test_offer_data_structures()
    
    print("\n\nSummary:")
    print("=" * 50)
    print("✓ Updated format_quantity function to handle 'db' units properly")
    print("✓ Updated display_data_components.py to use dynamic units")
    print("✓ Updated data_display.py to remove hardcoded 'kg' references")
    print("✓ Updated confirmation_dialog.py to show dynamic units")
    print("\nThe system now properly handles both 'kg' and 'db' units in:")
    print("  - Eredeti mennyiség display")
    print("  - Eredeti ár display")
    print("  - Input field labels")
    print("  - Chart labels")
    print("  - All other quantity/price displays")