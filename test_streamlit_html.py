#!/usr/bin/env python3
"""Test if st.html is available"""
import streamlit as st

st.write(f"Streamlit version: {st.__version__}")
st.write(f"Has html attribute: {hasattr(st, 'html')}")

try:
    # Test basic HTML
    st.html("<p style='color: red;'>Test HTML</p>")
    st.success("st.html() works!")
except AttributeError as e:
    st.error(f"AttributeError: {e}")
    st.write("Let's try the old method:")
    st.markdown("<p style='color: red;'>Test HTML with markdown</p>", unsafe_allow_html=True)
except Exception as e:
    st.error(f"Other error: {type(e).__name__}: {e}")