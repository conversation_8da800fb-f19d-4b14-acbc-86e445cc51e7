#!/usr/bin/env python3
import requests
import json

print('🔐 API TESZT')
print('=' * 30)

base_url = 'http://localhost:8000'

# Login
login_data = {
    'username': '<EMAIL>',
    'password': 'password123'
}

try:
    print('🔑 Login...')
    login_response = requests.post(f'{base_url}/api/auth/login', json=login_data)
    print(f'Login status: {login_response.status_code}')
    
    if login_response.status_code == 200:
        token_data = login_response.json()
        token = token_data.get('access_token')
        print('✅ Login OK')
        
        headers = {'Authorization': f'Bearer {token}'}
        
        # Get offers
        print('📊 Offers...')
        offers_response = requests.get(f'{base_url}/api/offers', headers=headers)
        print(f'Offers status: {offers_response.status_code}')
        
        if offers_response.status_code == 200:
            offers = offers_response.json()
            print(f'Total offers: {len(offers)}')
            
            # Count confirmed offers
            confirmed_count = 0
            prices_found = 0
            
            for offer in offers:
                status = offer.get('status')
                if status in ['CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'FINALIZED']:
                    confirmed_count += 1
                    confirmed_price = offer.get('confirmed_price')
                    price = offer.get('price')
                    if confirmed_price or price:
                        prices_found += 1
                        print(f'Offer #{offer.get("id")}: status={status}, confirmed_price={confirmed_price}, price={price}')
            
            print(f'Confirmed offers: {confirmed_count}')
            print(f'With prices: {prices_found}')
        
        # Get statistics
        print('📈 Statistics...')
        stats_response = requests.get(f'{base_url}/api/offers/statistics', headers=headers)
        print(f'Stats status: {stats_response.status_code}')
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f'Statistics: {json.dumps(stats, indent=2)}')
    
    else:
        print(f'Login failed: {login_response.text}')

except Exception as e:
    print(f'Error: {e}')