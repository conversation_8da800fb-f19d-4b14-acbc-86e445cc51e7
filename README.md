# Mezőgazdasági Termékke<PERSON> (v4)

## Áttekintés

A Mezőgazdasági Termékkezelő Rendszer egy komplex alkalmazás, amely lehetővé teszi a mezőgazdasági termékek kezelését, nyo<PERSON> követését és elemzését. A rendszer a következő főbb komponensekből áll:

- Backend API (FastAPI)
- Frontend (Streamlit)
- Adatbázis (PostgreSQL)
- Hibakeresési Rendszer

## Funkciók

- Termékek kezelése és nyomon követése
- Felhasználói jogosultságok kezelése
- Operátor aj<PERSON><PERSON><PERSON> oldal
- Pénzügyi riportok és elemzések
- Strukturált naplózás és hibakeresés
- Docker alapú konténerizáció

## Telepítés

### Előfeltételek

- Docker és Docker Compose
- Python 3.11+
- Git

### Rendszer indítása

1. Klónozza le a repository-t:
```bash
git clone https://github.com/your-org/termelo_v4.git
cd termelo_v4
```

2. Indítsa el a rendszert:
```bash
docker-compose up -d
```

3. Inicializálja az adatbázist:
```bash
./init-db.sh
```

## Használat

### Hibakeresési Rendszer

A rendszer beépített hibakeresési eszközöket tartalmaz:

```bash
# Debug menü indítása
./debug_menu.sh

# Vagy közvetlenül a CLI használata
python -m debug.cli logs backend
python -m debug.cli analyze streamlit
python -m debug.cli live db
```

### Konténer Kezelés

```bash
# Konténerek kezelése
./manage_containers.sh start
./manage_containers.sh stop
./manage_containers.sh restart
```

## Fejlesztés

### Környezet beállítása

1. Függőségek telepítése:
```bash
pip install -r requirements.txt
```

2. Adatbázis migrációk futtatása:
```bash
alembic upgrade head
```

### Kódolási Szabványok

- PEP 8 követése
- Type hints használata
- Docstring-ek használata
- Clean Code elvek követése

## Dokumentáció

A részletes dokumentáció a `docs/` könyvtárban található:

- `fejlesztesi_allas.md`: Aktuális fejlesztési állapot
- `debug/README.md`: Hibakeresési rendszer dokumentációja

## Licenc

Ez a projekt a belső fejlesztésű Mezőgazdasági Termékkezelő Rendszer része, és annak licencfeltételei vonatkoznak rá. 