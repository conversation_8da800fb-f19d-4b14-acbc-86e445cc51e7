# User Interface Guides by Role
# Mezőgazdasági <PERSON> - Complete User Guides

## Table of Contents
1. [Getting Started - All Users](#getting-started---all-users)
2. [Producer (Termelő) User Guide](#producer-termelő-user-guide)
3. [Operator (Ügyintéző) User Guide](#operator-ügyintéző-user-guide)
4. [Administrator (Admin) User Guide](#administrator-admin-user-guide)
5. [Common Interface Elements](#common-interface-elements)
6. [Mobile Usage Guide](#mobile-usage-guide)
7. [Troubleshooting & Support](#troubleshooting--support)

---

## Getting Started - All Users

### 🔐 Initial Access & Authentication

**First-Time Registration:**
1. **Access the System:** Navigate to the application URL
2. **Registration Process:**
   - Click "Regisztráció" (Registration) on the login page
   - Fill in personal information:
     - Contact name (required)
     - Email address (required)
     - Phone number (required)
   - Company information (if applicable):
     - Company name
     - Tax ID number
   - Create secure password:
     - Minimum 8 characters
     - Must contain letters and numbers
     - Confirm password
   - Accept terms and conditions
   - Submit registration

3. **Account Activation:**
   - Check email for activation link
   - Click activation link to activate account
   - Return to login page

**Daily Login Process:**
1. Enter email address and password
2. Click "Bejelentkezés" (Login)
3. System redirects to role-appropriate dashboard

**Password Reset:**
1. Click "Elfelejtett jelszó?" (Forgot password?) on login page
2. Enter email address
3. Check email for reset link
4. Follow link to create new password

### 🧭 Navigation Basics

**Sidebar Navigation:**
- **Main Menu:** Role-specific navigation options
- **Favorites System:** Star pages for quick access
- **User Profile:** Current user information display
- **Logout:** Secure session termination

**Page Navigation:**
- **Breadcrumbs:** Show current location in application
- **Quick Actions:** Fast access to common operations
- **Search:** Find specific content across the system

---

## Producer (Termelő) User Guide

### 🏠 Producer Dashboard Overview

**Dashboard Components:**
- **Personal Statistics:** Your offer summary and performance
- **Recent Activity:** Latest offer updates and notifications
- **Quick Actions:** Fast access to common tasks
- **Settings Panel:** Personal preferences and defaults

**Key Metrics Displayed:**
- Total active offers
- Pending confirmations
- Completed deliveries
- Revenue summary

### 📦 Creating New Offers

**Step-by-Step Offer Creation:**

1. **Access Creation Form:**
   - Click "Új ajánlat leadása" (Create New Offer) from dashboard
   - Or navigate via sidebar: "📦 Új ajánlat leadása"

2. **Product Selection Process:**
   ```
   Step 1: Select Product Category
   ├── Choose from dropdown (e.g., "Paprika", "Paradicsom")
   └── System loads available product types
   
   Step 2: Select Product Type
   ├── Choose specific variety (e.g., "TV paprika", "Kápia paprika")
   └── System loads quality grades if applicable
   
   Step 3: Select Quality Grade (if applicable)
   ├── Choose quality level (e.g., "Extra", "I. osztály")
   └── View quality specifications
   ```

3. **Offer Details:**
   - **Quantity:** Enter amount in kilograms
     - System validates positive numbers
     - Decimal precision supported
   - **Delivery Date:** Select future delivery date
     - Calendar picker interface
     - Cannot select past dates
   - **Additional Notes:** Optional comments or specifications

4. **Smart Defaults:**
   - System pre-populates fields based on your previous preferences
   - Default product types and quality grades from your settings
   - Preferred quantity units (kg/tonne)

5. **Form Validation:**
   - Real-time validation feedback
   - Required field indicators
   - Error messages with correction guidance

6. **Submission:**
   - Review all information
   - Click "Ajánlat beküldése" (Submit Offer)
   - Confirmation message with offer ID

### 📋 Managing Your Offers

**Offers List Interface:**

1. **Access Your Offers:**
   - Navigate to "📋 Ajánlataim" (My Offers)
   - View all your submitted offers

2. **Filtering Options:**
   ```
   Status Filters:
   ├── Összes (All)
   ├── Létrehozva (Created)
   ├── Cég által visszaigazolva (Confirmed by Company)
   ├── Termelő által elfogadva (Accepted by Producer)
   ├── Termelő által elutasítva (Rejected by Producer)
   └── Véglegesítve (Finalized)
   
   Date Filters:
   ├── Összes (All)
   ├── Jövőbeni beszállítások (Future Deliveries)
   ├── Korábbi beszállítások (Past Deliveries)
   ├── Következő 7 nap (Next 7 Days)
   └── Következő 30 nap (Next 30 Days)
   
   Sorting Options:
   ├── Beszállítás dátuma (Delivery Date)
   └── Létrehozás dátuma (Creation Date)
   ```

3. **Offer Actions:**
   - **View Details:** Click on offer to see full information
   - **Edit Offer:** Modify details (only for certain statuses)
   - **Delete Offer:** Remove offer (only for specific statuses)
   - **Accept/Reject:** Respond to company confirmations

### 🔄 Offer Workflow Management

**Understanding Offer Statuses:**

1. **CREATED (Létrehozva):**
   - Your offer has been submitted
   - Waiting for company review
   - You can still edit or delete

2. **CONFIRMED_BY_COMPANY (Cég által visszaigazolva):**
   - Company has reviewed and confirmed your offer
   - May include price and quantity adjustments
   - **Action Required:** You must accept or reject

3. **ACCEPTED_BY_USER (Termelő által elfogadva):**
   - You have accepted the company's confirmation
   - Offer is approved and moving to finalization
   - No further action required from you

4. **REJECTED_BY_USER (Termelő által elutasítva):**
   - You have rejected the company's confirmation
   - Offer is closed
   - You can create a new offer if needed

5. **FINALIZED (Véglegesítve):**
   - Offer is complete and ready for delivery
   - Delivery details confirmed
   - Payment processing initiated

**Responding to Company Confirmations:**

1. **Notification:** You'll receive email notification of company confirmation
2. **Review Changes:** Check any price or quantity adjustments
3. **Decision Process:**
   - **Accept:** Click "Elfogadás" if terms are acceptable
   - **Reject:** Click "Elutasítás" with optional reason
4. **Confirmation:** System updates status and sends notifications

### 📊 Personal Statistics & Reports

**Statistics Dashboard:**

1. **Access Statistics:**
   - Navigate to "📊 Statisztikáim" (My Statistics)

2. **Available Metrics:**
   - **Offer Performance:**
     - Total offers submitted
     - Acceptance rate
     - Average offer value
     - Delivery performance
   
   - **Product Analysis:**
     - Most offered products
     - Seasonal trends
     - Quality grade distribution
   
   - **Financial Overview:**
     - Total revenue
     - Average price per kg
     - Payment status tracking

3. **Visual Reports:**
   - Interactive charts and graphs
   - Trend analysis over time
   - Comparative performance metrics
   - Export capabilities for external analysis

### ⚙️ Personal Settings Management

**User Settings Configuration:**

1. **Access Settings:**
   - Click on user profile in sidebar
   - Select "Beállítások" (Settings)

2. **Default Preferences:**
   - **Default Product Type:** Set your most common product
   - **Default Quality Grade:** Set preferred quality level
   - **Quantity Unit:** Choose kg or tonne
   - **Notification Preferences:** Email and in-app notifications

3. **Profile Information:**
   - Update contact information
   - Modify company details
   - Change password
   - Update phone number

4. **Smart Defaults Impact:**
   - New offer forms pre-populate with your defaults
   - Faster offer creation process
   - Consistent data entry

---

## Operator (Ügyintéző) User Guide

### 🏢 Operator Dashboard Overview

**Dashboard Components:**
- **System-Wide Statistics:** All offers across all producers
- **Pending Actions:** Offers requiring operator attention
- **Performance Metrics:** System efficiency and productivity
- **Quick Management Tools:** Fast access to common operations

**Key Metrics Displayed:**
- Total active offers in system
- Offers pending confirmation
- Daily/weekly processing volumes
- Producer activity levels

### 📋 Advanced Offer Management

**Comprehensive Offer Management Interface:**

1. **Access Offer Management:**
   - Navigate to "📋 Ajánlatok kezelése" (Offer Management)
   - Main operator workspace

2. **Advanced Filtering System:**
   ```
   Multi-Criteria Filters:
   ├── Producer/Company Filter
   │   ├── Search by company name
   │   ├── Filter by specific producers
   │   └── Producer performance metrics
   ├── Product Filters
   │   ├── Category selection
   │   ├── Product type filtering
   │   └── Quality grade filtering
   ├── Date Range Filters
   │   ├── Custom date ranges
   │   ├── Delivery date filtering
   │   └── Creation date filtering
   ├── Status Filters
   │   ├── Multiple status selection
   │   ├── Workflow stage filtering
   │   └── Priority indicators
   └── Quantity Filters
       ├── Minimum quantity thresholds
       ├── Maximum quantity limits
       └── Total value filtering
   ```

3. **Real-Time Filter Application:**
   - Filters apply instantly as you type
   - Visual feedback on active filters
   - Filter combination validation
   - Save frequently used filter combinations

4. **Saved Filter Management:**
   - Create custom filter presets
   - Share filters with other operators
   - Set default filters for quick access
   - Filter performance optimization

### 🔍 Advanced Search Capabilities

**Search Interface Features:**

1. **Full-Text Search:**
   - Search across all offer fields
   - Producer names and company information
   - Product descriptions and notes
   - Offer IDs and reference numbers

2. **Smart Search Features:**
   - Auto-complete suggestions
   - Typo tolerance and fuzzy matching
   - Search result highlighting
   - Search history and suggestions

3. **Search Filters Integration:**
   - Combine search with filters
   - Search within filtered results
   - Advanced query building
   - Export search results

### ✅ Offer Confirmation Process

**Step-by-Step Confirmation Workflow:**

1. **Review Incoming Offers:**
   - Filter for "CREATED" status offers
   - Sort by priority or delivery date
   - Review producer information and history

2. **Offer Evaluation:**
   - **Product Verification:** Confirm product specifications
   - **Quantity Assessment:** Evaluate requested quantities
   - **Delivery Date Review:** Check delivery schedule compatibility
   - **Producer History:** Review past performance

3. **Confirmation Process:**
   ```
   Confirmation Steps:
   ├── Open offer details
   ├── Review all specifications
   ├── Make adjustments if needed:
   │   ├── Modify quantity (if necessary)
   │   ├── Adjust price (if applicable)
   │   └── Add notes or requirements
   ├── Set confirmation details:
   │   ├── Confirmed quantity
   │   ├── Confirmed price per kg
   │   └── Special instructions
   └── Submit confirmation
   ```

4. **Bulk Confirmation Tools:**
   - Select multiple offers for batch processing
   - Apply standard confirmation templates
   - Bulk price adjustments
   - Mass notification sending

### 📅 Calendar and Schedule Management

**Calendar Interface Features:**

1. **Access Calendar View:**
   - Navigate to "📅 Naptári nézet" (Calendar View)
   - Visual delivery schedule overview

2. **Calendar Views:**
   - **Monthly View:** Overview of entire month
   - **Weekly View:** Detailed weekly schedule
   - **Daily View:** Hour-by-hour delivery planning

3. **Calendar Functions:**
   - **Delivery Planning:** Visualize delivery schedules
   - **Resource Allocation:** Plan logistics and resources
   - **Conflict Detection:** Identify scheduling conflicts
   - **Capacity Management:** Monitor daily/weekly capacity

4. **Interactive Calendar Features:**
   - Drag-and-drop offer rescheduling
   - Color-coded status indicators
   - Quick offer details on hover
   - Direct offer actions from calendar

### 📊 Comprehensive Reporting

**Advanced Analytics and Reporting:**

1. **Access Reports:**
   - Navigate to "📊 Összegzés és riportok" (Reports & Analytics)

2. **Report Categories:**
   ```
   Operational Reports:
   ├── Daily Processing Summary
   ├── Weekly Performance Metrics
   ├── Monthly Trend Analysis
   └── Quarterly Business Review
   
   Producer Reports:
   ├── Producer Performance Analysis
   ├── Producer Reliability Metrics
   ├── Producer Product Preferences
   └── Producer Payment History
   
   Product Reports:
   ├── Product Demand Analysis
   ├── Seasonal Trend Reports
   ├── Quality Grade Distribution
   └── Price Trend Analysis
   
   Financial Reports:
   ├── Revenue Analysis
   ├── Cost Center Reports
   ├── Profit Margin Analysis
   └── Payment Status Reports
   ```

3. **Interactive Dashboards:**
   - Real-time data visualization
   - Customizable chart types
   - Drill-down capabilities
   - Export to various formats

4. **Report Scheduling:**
   - Automated report generation
   - Email delivery scheduling
   - Custom report templates
   - Stakeholder distribution lists

### 🚀 Bulk Operations

**Efficient Mass Processing Tools:**

1. **Bulk Offer Processing:**
   - Select multiple offers using checkboxes
   - Apply actions to selected offers:
     - Bulk confirmation
     - Batch status updates
     - Mass email notifications
     - Bulk export operations

2. **Batch Communication:**
   - Send notifications to multiple producers
   - Broadcast system announcements
   - Bulk email with templates
   - SMS notifications (if configured)

3. **Data Export Tools:**
   - Export filtered offer lists
   - Generate reports in multiple formats
   - Bulk data download for analysis
   - Integration with external systems

---

## Administrator (Admin) User Guide

### 👑 Administrator Dashboard Overview

**System Administration Center:**
- **System Health Monitoring:** Overall system status and performance
- **User Activity Overview:** User engagement and system usage
- **Security Monitoring:** Access logs and security events
- **Configuration Management:** System settings and preferences

**Administrative Metrics:**
- Total system users and roles
- System performance indicators
- Database health and capacity
- Security audit summaries

### 👥 User Management

**Comprehensive User Administration:**

1. **Access User Management:**
   - Navigate to "👥 Felhasználók kezelése" (User Management)

2. **User List Management:**
   ```
   User Management Features:
   ├── User Search and Filtering
   │   ├── Search by name, email, company
   │   ├── Filter by role (Producer/Operator/Admin)
   │   └── Filter by status (Active/Inactive)
   ├── User Details Management
   │   ├── View complete user profiles
   │   ├── Edit user information
   │   ├── Update contact details
   │   └── Modify company information
   ├── Role Management
   │   ├── Assign user roles
   │   ├── Change role permissions
   │   ├── Role-based access control
   │   └── Permission auditing
   └── Account Management
       ├── Activate/Deactivate accounts
       ├── Reset user passwords
       ├── Unlock locked accounts
       └── Delete user accounts
   ```

3. **User Creation Process:**
   - **Manual User Creation:** Create users directly in admin panel
   - **Bulk User Import:** Import users from CSV/Excel files
   - **User Invitation System:** Send invitation emails to new users
   - **Role Assignment:** Set appropriate roles during creation

4. **User Activity Monitoring:**
   - Login history and patterns
   - User action logs
   - Performance metrics per user
   - Security event tracking

### 🍅 Product Catalog Management

**Complete Product Administration:**

1. **Access Product Management:**
   - Navigate to "🍅 Termékek kezelése" (Product Management)

2. **Product Hierarchy Management:**
   ```
   Product Structure Administration:
   ├── Category Management
   │   ├── Create new product categories
   │   ├── Edit existing categories
   │   ├── Delete unused categories
   │   └── Category description management
   ├── Product Type Management
   │   ├── Add new product types to categories
   │   ├── Configure quality grade requirements
   │   ├── Set product specifications
   │   └── Manage product descriptions
   └── Quality Grade Management
       ├── Define quality standards
       ├── Set dimensional specifications
       ├── Configure grading criteria
       └── Quality description management
   ```

3. **Product Configuration:**
   - **Quality Grade Settings:** Enable/disable quality grading per product
   - **Specification Management:** Set dimensional requirements (diameter, length)
   - **Description Management:** Maintain product descriptions and specifications
   - **Category Organization:** Organize products in logical hierarchies

4. **Product Data Validation:**
   - Data integrity checking
   - Duplicate detection and resolution
   - Specification validation
   - Quality standard compliance

### 🛠️ System Administration

**Advanced System Management:**

1. **Database Management:**
   ```
   Database Administration:
   ├── Database Reset Functions
   │   ├── Complete system reset
   │   ├── Preserve admin account
   │   ├── Reinitialize default data
   │   └── System backup before reset
   ├── Data Maintenance
   │   ├── Data cleanup utilities
   │   ├── Orphaned record removal
   │   ├── Data integrity checks
   │   └── Performance optimization
   └── Backup Management
       ├── Automated backup scheduling
       ├── Manual backup creation
       ├── Backup verification
       └── Restore procedures
   ```

2. **System Configuration:**
   - **Email Settings:** SMTP configuration and email templates
   - **Security Settings:** Password policies and session management
   - **Feature Toggles:** Enable/disable system features
   - **Performance Settings:** System optimization parameters

3. **Monitoring and Maintenance:**
   - **System Health Checks:** Automated system monitoring
   - **Performance Monitoring:** Response time and resource usage
   - **Error Tracking:** System error logs and resolution
   - **Capacity Planning:** Resource usage analysis and planning

### 🧪 Data Generation and Testing

**Development and Testing Tools:**

1. **Test Data Generator:**
   - Navigate to "🧪 Tesztadat generátor" (Test Data Generator)
   - Generate realistic test data for development
   - Create sample offers, users, and products
   - Performance testing data sets

2. **System Testing Tools:**
   - **Load Testing:** Simulate high user loads
   - **Data Validation:** Test data integrity
   - **Feature Testing:** Validate new features
   - **Integration Testing:** Test API integrations

### 📊 Advanced Analytics

**System-Wide Analytics and Reporting:**

1. **System Performance Analytics:**
   - User engagement metrics
   - System usage patterns
   - Performance bottleneck analysis
   - Capacity utilization reports

2. **Business Intelligence:**
   - Cross-producer analytics
   - Market trend analysis
   - Seasonal pattern identification
   - Predictive analytics

3. **Security Analytics:**
   - Access pattern analysis
   - Security event correlation
   - Threat detection and response
   - Compliance reporting

---

## Common Interface Elements

### 🧭 Navigation Components

**Sidebar Navigation:**
- **Role-Based Menu:** Different options for each user role
- **Favorites System:** Star frequently used pages
- **User Profile Display:** Current user information and role
- **Quick Actions:** Fast access to common operations
- **Logout Function:** Secure session termination

**Page Navigation:**
- **Breadcrumb Trail:** Shows current location in application
- **Page Titles:** Clear indication of current page function
- **Action Buttons:** Primary and secondary actions clearly marked
- **Back Navigation:** Easy return to previous pages

### 📊 Data Display Components

**Table Interfaces:**
- **Sortable Columns:** Click column headers to sort
- **Filterable Data:** Column-specific filtering options
- **Pagination:** Navigate through large datasets
- **Row Selection:** Select single or multiple rows
- **Export Options:** Download data in various formats

**Chart and Graph Components:**
- **Interactive Charts:** Hover for details, zoom and pan
- **Multiple Chart Types:** Bar, line, pie, scatter plots
- **Real-Time Updates:** Live data refresh capabilities
- **Export Functions:** Save charts as images or data

### 🔔 Notification System

**Notification Types:**
- **Success Messages:** Green notifications for successful operations
- **Error Messages:** Red notifications for errors and failures
- **Warning Messages:** Yellow notifications for important information
- **Info Messages:** Blue notifications for general information

**Notification Behavior:**
- **Auto-Dismiss:** Notifications automatically disappear after timeout
- **Manual Dismiss:** Click X to close notifications manually
- **Persistent Notifications:** Important messages remain until dismissed
- **Notification History:** Access previous notifications

### 📝 Form Components

**Form Elements:**
- **Required Field Indicators:** Red asterisks (*) mark required fields
- **Real-Time Validation:** Immediate feedback on input errors
- **Help Text:** Contextual help and examples
- **Progress Indicators:** Show completion progress in multi-step forms

**Form Behavior:**
- **Auto-Save:** Forms automatically save progress
- **Validation Feedback:** Clear error messages and correction guidance
- **Smart Defaults:** Pre-populated fields based on user preferences
- **Form Reset:** Clear all fields and start over

---

## Mobile Usage Guide

### 📱 Mobile Interface Overview

**Mobile-Optimized Features:**
- **Responsive Design:** Interface adapts to screen size
- **Touch-Friendly Controls:** Large buttons and touch targets
- **Swipe Navigation:** Gesture-based navigation
- **Mobile-Specific Layouts:** Optimized for small screens

**Mobile Navigation:**
- **Collapsible Sidebar:** Hamburger menu for navigation
- **Bottom Navigation:** Quick access to main functions
- **Pull-to-Refresh:** Refresh data with pull gesture
- **Touch Gestures:** Swipe, pinch, and tap interactions

### 📲 Mobile-Specific Features

**Touch Optimizations:**
- **Large Touch Targets:** Buttons sized for finger interaction
- **Gesture Support:** Swipe to navigate, pinch to zoom
- **Mobile Keyboards:** Appropriate keyboard types for input fields
- **Touch Feedback:** Visual feedback for touch interactions

**Mobile Performance:**
- **Fast Loading:** Optimized for mobile networks
- **Offline Capability:** Limited functionality without internet
- **Data Efficiency:** Minimized data usage
- **Battery Optimization:** Efficient resource usage

### 🔧 Mobile Settings

**Mobile Configuration:**
- **Display Settings:** Adjust text size and contrast
- **Notification Settings:** Configure mobile notifications
- **Offline Settings:** Set offline data preferences
- **Performance Settings:** Optimize for device capabilities

---

## Troubleshooting & Support

### 🔧 Common Issues and Solutions

**Login Problems:**
1. **Forgot Password:**
   - Use "Elfelejtett jelszó?" link on login page
   - Check email for reset instructions
   - Create new secure password

2. **Account Not Activated:**
   - Check email for activation link
   - Contact administrator if activation email not received
   - Verify email address spelling

3. **Login Errors:**
   - Verify email and password spelling
   - Check caps lock status
   - Clear browser cache and cookies

**Performance Issues:**
1. **Slow Loading:**
   - Check internet connection
   - Clear browser cache
   - Try different browser
   - Contact support if issues persist

2. **Page Not Loading:**
   - Refresh page (F5 or Ctrl+R)
   - Check browser compatibility
   - Disable browser extensions temporarily
   - Try incognito/private browsing mode

**Data Issues:**
1. **Missing Data:**
   - Check filter settings
   - Verify date ranges
   - Refresh page to reload data
   - Contact support if data should be present

2. **Form Errors:**
   - Check all required fields
   - Verify data format (dates, numbers)
   - Review validation messages
   - Try submitting again after corrections

### 📞 Getting Help

**Support Channels:**
1. **In-App Help:**
   - Look for help icons (?) throughout the interface
   - Hover over elements for tooltips
   - Check form validation messages

2. **Documentation:**
   - Access user guides from help menu
   - Review feature documentation
   - Check FAQ sections

3. **Technical Support:**
   - Contact system administrator
   - Report bugs through support channels
   - Provide detailed error descriptions

**Best Practices:**
- **Regular Backups:** Export important data regularly
- **Browser Updates:** Keep browser updated for best performance
- **Security:** Use strong passwords and log out when finished
- **Training:** Attend user training sessions when available

### 🛡️ Security Best Practices

**Account Security:**
- Use strong, unique passwords
- Log out when finished using the system
- Don't share login credentials
- Report suspicious activity immediately

**Data Protection:**
- Verify recipient before sharing sensitive information
- Use secure networks when accessing the system
- Keep personal information updated
- Follow company data handling policies

**System Security:**
- Keep browsers updated
- Use reputable antivirus software
- Avoid public computers for sensitive operations
- Report security concerns to administrators

---

## Conclusion

This comprehensive user guide provides detailed instructions for all user roles in the Mezőgazdasági Termékkezelő Rendszer. Each role has specific capabilities and responsibilities:

- **Producers** focus on creating and managing their offers
- **Operators** handle system-wide offer processing and management
- **Administrators** maintain the system and manage users and products

The interface is designed to be intuitive and efficient, with role-based customization ensuring each user sees only relevant functionality. Regular use of the system will increase familiarity and efficiency with these tools.

For additional support or training, contact your system administrator or refer to the built-in help system throughout the application.