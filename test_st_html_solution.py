#!/usr/bin/env python3
"""
Test script for the universal HTML rendering solution
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(page_title="Universal HTML Rendering Test", layout="wide")

st.title("🧪 Universal HTML Rendering Solution Test")

# Test if st.html is available
st.header("1. Checking st.html() availability")

col1, col2 = st.columns(2)

with col1:
    st.subheader("Direct Test")
    has_st_html = hasattr(st, 'html')
    st.write(f"**Has st.html attribute:** {has_st_html}")
    st.write(f"**Streamlit version:** {st.__version__}")
    
    if has_st_html:
        try:
            st.html("<div style='color: green;'>✅ st.html() works!</div>")
        except Exception as e:
            st.error(f"st.html() error: {e}")

with col2:
    st.subheader("Using our checker")
    try:
        from pages.operator.offer_management.html_renderer import check_st_html_availability
        status = check_st_html_availability()
        st.json(status)
    except Exception as e:
        st.error(f"Could not import checker: {e}")

st.markdown("---")

# Test the universal renderer
st.header("2. Testing Universal HTML Renderer")

try:
    from pages.operator.offer_management.html_renderer import render_html
    
    test_html = """
    <div style="border: 2px solid blue; padding: 20px; background: #E3F2FD;">
        <h3 style="color: #1976D2;">Universal Renderer Test</h3>
        <p>This content is rendered using our universal render_html() function.</p>
        <ul>
            <li>Automatically detects st.html() availability</li>
            <li>Falls back to components.html() if needed</li>
            <li>Auto-calculates height for fallback</li>
        </ul>
    </div>
    """
    
    used_st_html = render_html(test_html)
    
    if used_st_html:
        st.success("✅ Rendered with st.html()")
    else:
        st.info("ℹ️ Rendered with components.html() fallback")
        
except Exception as e:
    st.error(f"Error testing universal renderer: {e}")

st.markdown("---")

# Test the new NutritionFactsContainer
st.header("3. Testing Updated NutritionFactsContainer")

try:
    # Try v2 first, fallback to v1
    try:
        from pages.operator.offer_management.nutrition_facts_container_v2 import NutritionFactsContainer
        st.info("Using nutrition_facts_container_v2")
    except:
        from pages.operator.offer_management.nutrition_facts_container import NutritionFactsContainer
        st.info("Using original nutrition_facts_container")
    
    test_offer = {
        'id': 123,
        'status': 'CONFIRMED_BY_COMPANY',
        'status_display': 'Cég által visszaigazolva',
        'quantity_in_kg': 100.5,
        'price': 850,
        'confirmed_quantity': 100,
        'confirmed_price': 850,
        'created_at': '2024-04-27T08:30:00Z',
        'delivery_date': '2024-05-01',
        'note': 'Ez egy teszt megjegyzés a HTML renderelés teszteléséhez.',
        'user': {
            'contact_name': 'Teszt Felhasználó',
            'company_name': 'Teszt Cég Kft.',
            'email': '<EMAIL>',
            'phone': '+36 30 123 4567'
        },
        'product_type': {
            'name': 'Bio Sárgarépa',
            'category': {'name': 'Zöldségek'}
        }
    }
    
    # Enable debug mode
    debug = st.checkbox("Enable debug mode", value=True)
    
    container = NutritionFactsContainer(
        title="Ajánlat Részletek",
        subtitle="Test Render",
        icon="📋"
    )
    
    container.render(test_offer, debug_mode=debug)
    
    st.success("✅ NutritionFactsContainer rendered successfully!")
    
except Exception as e:
    st.error(f"Error with NutritionFactsContainer: {e}")
    st.exception(e)

st.markdown("---")

# Summary
st.header("4. Solution Summary")

st.markdown("""
### 🎯 Universal HTML Rendering Solution

**Key Features:**
1. **Automatic Detection**: Checks if `st.html()` is available
2. **Smart Fallback**: Uses `components.html()` when needed
3. **Height Calculation**: Auto-calculates height for fallback
4. **CSS Support**: Uses `st.markdown()` for CSS injection
5. **Backward Compatible**: Works with all Streamlit versions

**Implementation:**
```python
from html_renderer import render_html

# Just call render_html() - it handles everything!
render_html(your_html_content)
```

**Benefits:**
- ✅ Future-proof (ready for st.html when available)
- ✅ Works today (fallback to components.html)
- ✅ Preserves all styling and design
- ✅ No manual version checking needed
- ✅ Consistent API across all components
""")

st.info("""
💡 **The Nutrition Facts design is 100% preserved!**
All borders, styling, and layout remain exactly the same regardless of which rendering method is used.
""")