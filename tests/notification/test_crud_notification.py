import pytest
from sqlalchemy.orm import Session
from app.models.notification import Notification
from app.schemas.notification import NotificationCreate
from app.crud import notification as crud_notification

class DummyDB:
    """Egyszerű dummy osztály, hogy a db.commit(), db.refresh() ne dobjon hibát a mock során."""
    def add(self, obj): pass
    def commit(self): pass
    def refresh(self, obj): pass


def test_create_notification_requires_related_entity_type():
    """
    Teszt: Ha a related_entity_type mező nincs kitöltve, a create_notification ValueError-t dob.
    """
    db = DummyDB()
    # Hiányzó related_entity_type
    obj_in = NotificationCreate(
        user_id=1,
        type="info",
        message="Test message",
        detail=None,
        target_roles=None,
        related_entity_type=None,
        related_entity_id=None
    )
    with pytest.raises(ValueError) as excinfo:
        crud_notification.create_notification(db, obj_in)
    assert "related_entity_type" in str(excinfo.value)

    # Üres string
    obj_in.related_entity_type = "   "
    with pytest.raises(ValueError) as excinfo:
        crud_notification.create_notification(db, obj_in)
    assert "related_entity_type" in str(excinfo.value)


def test_create_notification_success():
    """
    Teszt: Sikeres értesítés létrehozás, ha van related_entity_type.
    """
    db = DummyDB()
    obj_in = NotificationCreate(
        user_id=1,
        type="info",
        message="Test message",
        detail=None,
        target_roles="admin",
        related_entity_type="offer",
        related_entity_id=123
    )
    # Nem dob kivételt
    try:
        crud_notification.create_notification(db, obj_in)
    except Exception:
        pytest.fail("Nem szabad kivételt dobnia, ha a related_entity_type helyesen van kitöltve.")
