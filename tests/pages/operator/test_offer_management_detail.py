"""
Tesztek az offer_detail.<PERSON><PERSON> <PERSON><PERSON>, am<PERSON><PERSON> ellenőrzik az új ajánlat részletező komponensek működését.
"""
import pytest
import sys
import os
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Streamlit mock beállítása
class MockStreamlit:
    def __init__(self):
        self.session_state = {}
        self.columns_return = [MagicMock(), MagicMock(), MagicMock()]
        self.tabs_return = [MagicMock(), MagicMock(), MagicMock()]

    def markdown(self, *args, **kwargs):
        pass

    def title(self, *args, **kwargs):
        pass

    def info(self, *args, **kwargs):
        pass

    def error(self, *args, **kwargs):
        pass

    def success(self, *args, **kwargs):
        pass

    def button(self, *args, **kwargs):
        return False

    def columns(self, *args, **kwargs):
        return self.columns_return[:len(args[0]) if args else 2]

    def tabs(self, *args, **kwargs):
        return self.tabs_return[:len(args[0]) if args else 3]

    def metric(self, *args, **kwargs):
        pass

    def text_area(self, *args, **kwargs):
        return ""

    def file_uploader(self, *args, **kwargs):
        return None

    def selectbox(self, *args, **kwargs):
        return args[1][0] if args and len(args) > 1 and args[1] else None

    def progress(self, *args, **kwargs):
        pass

    def warning(self, *args, **kwargs):
        pass

    def caption(self, *args, **kwargs):
        pass

    def code(self, *args, **kwargs):
        pass

    def download_button(self, *args, **kwargs):
        return False

    def rerun(self):
        pass


# Mock offer data for testing
@pytest.fixture
def mock_offer_data():
    return {
        "id": 1,
        "status": "CONFIRMED_BY_COMPANY",
        "created_at": datetime.now() - timedelta(days=5),
        "updated_at": datetime.now() - timedelta(days=2),
        "status_changed_at": datetime.now() - timedelta(days=2),
        "delivery_date": datetime.now() + timedelta(days=10),
        "quantity_in_kg": 500,
        "price": 350,
        "notes": "Kérem odafigyelni a szállítási határidőre.",
        "status_note": "Az ajánlat megerősítésre került a vállalat által.",
        "producer": {
            "id": 1,
            "contact_name": "Nagy József",
            "company_name": "Zöld Gazda Kft.",
            "email": "<EMAIL>",
            "phone": "+36 30 123 4567",
            "address": "9022 Győr, Termény utca 15."
        },
        "product_type": {
            "id": 2,
            "name": "Bio alma",
            "category": {
                "id": 1,
                "name": "Gyümölcsök"
            },
            "description": "Kiváló minőségű, vegyszermentes bio alma.",
            "unit": "kg"
        },
        "creator": {
            "id": 1,
            "contact_name": "Nagy József"
        },
        "confirmed_at": datetime.now() - timedelta(days=2)
    }


# Mocks for API calls
@pytest.fixture
def mock_api_functions():
    """Mock API függvények létrehozása a tesztekhez"""
    with patch('streamlit_app.pages.operator.offer_management.offer_detail.get_offer_details') as mock_get_details, \
         patch('streamlit_app.pages.operator.offer_management.offer_detail.get_offer_logs') as mock_get_logs, \
         patch('streamlit_app.pages.operator.offer_management.offer_detail.update_offer_status') as mock_update_status, \
         patch('streamlit_app.pages.operator.offer_management.offer_detail.get_offer_attachments') as mock_get_attachments, \
         patch('streamlit_app.pages.operator.offer_management.offer_detail.get_related_offers') as mock_get_related:
        
        # Mock visszatérési értékek beállítása
        mock_get_details.return_value = (True, mock_offer_data())
        mock_get_logs.return_value = (True, [
            {
                "id": 1,
                "offer_id": 1,
                "from_status": "CREATED",
                "to_status": "CONFIRMED_BY_COMPANY",
                "created_at": datetime.now() - timedelta(days=2),
                "user": {"id": 2, "contact_name": "Operátor Anna"},
                "note": "Az ajánlat megfelelő, elfogadjuk a feltételeket."
            }
        ])
        mock_update_status.return_value = (True, {"id": 1, "status": "ACCEPTED_BY_USER"})
        mock_get_attachments.return_value = (True, [])
        mock_get_related.return_value = (True, [])
        
        yield {
            "get_offer_details": mock_get_details,
            "get_offer_logs": mock_get_logs,
            "update_offer_status": mock_update_status,
            "get_offer_attachments": mock_get_attachments,
            "get_related_offers": mock_get_related
        }


# Tesztek az offer_detail.py funkcióihoz
@pytest.mark.parametrize("device_type", ["desktop", "tablet", "mobile"])
def test_show_offer_detail(monkeypatch, mock_api_functions, device_type):
    """
    Teszteli az ajánlat részletek megjelenítését különböző eszköztípusokon.
    
    Args:
        monkeypatch: Pytest monkeypatch fixture
        mock_api_functions: Mock API functions fixture
        device_type: Az eszköz típusa (desktop, tablet, mobile)
    """
    # Streamlit mock beállítása
    mock_st = MockStreamlit()
    monkeypatch.setattr("streamlit.markdown", mock_st.markdown)
    monkeypatch.setattr("streamlit.title", mock_st.title)
    monkeypatch.setattr("streamlit.info", mock_st.info)
    monkeypatch.setattr("streamlit.error", mock_st.error)
    monkeypatch.setattr("streamlit.success", mock_st.success)
    monkeypatch.setattr("streamlit.button", mock_st.button)
    monkeypatch.setattr("streamlit.columns", mock_st.columns)
    monkeypatch.setattr("streamlit.tabs", mock_st.tabs)
    monkeypatch.setattr("streamlit.metric", mock_st.metric)
    monkeypatch.setattr("streamlit.text_area", mock_st.text_area)
    monkeypatch.setattr("streamlit.selectbox", mock_st.selectbox)
    monkeypatch.setattr("streamlit.rerun", mock_st.rerun)
    
    # Session state beállítása
    mock_st.session_state = {
        'is_mobile': device_type == "mobile",
        'is_tablet': device_type == "tablet",
        'loading_logs': False,
        'loading_attachments': False,
        'loading_related': False
    }
    monkeypatch.setattr("streamlit.session_state", mock_st.session_state)
    
    # Importáljuk az offer_detail modult
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
    from streamlit_app.pages.operator.offer_management.offer_detail import show_offer_detail
    
    # Futtatjuk a tesztet
    show_offer_detail(1)
    
    # Ellenőrizzük, hogy meghívták-e a megfelelő API függvényeket
    mock_api_functions["get_offer_details"].assert_called_once_with(1)
    
    # Ellenőrizzük, hogy a megfelelő layout-ot használtuk-e
    if device_type == "mobile":
        assert mock_st.session_state['is_mobile'] == True
        assert mock_st.session_state['is_tablet'] == False
    elif device_type == "tablet":
        assert mock_st.session_state['is_mobile'] == False
        assert mock_st.session_state['is_tablet'] == True
    else:
        assert mock_st.session_state['is_mobile'] == False
        assert mock_st.session_state['is_tablet'] == False


def test_status_change_workflow(monkeypatch, mock_api_functions):
    """
    Teszteli a státuszváltás munkafolyamatot.
    
    Args:
        monkeypatch: Pytest monkeypatch fixture
        mock_api_functions: Mock API functions fixture
    """
    # Streamlit mock beállítása
    mock_st = MockStreamlit()
    monkeypatch.setattr("streamlit.markdown", mock_st.markdown)
    monkeypatch.setattr("streamlit.title", mock_st.title)
    monkeypatch.setattr("streamlit.info", mock_st.info)
    monkeypatch.setattr("streamlit.error", mock_st.error)
    monkeypatch.setattr("streamlit.success", mock_st.success)
    monkeypatch.setattr("streamlit.button", mock_st.button)
    monkeypatch.setattr("streamlit.columns", mock_st.columns)
    monkeypatch.setattr("streamlit.tabs", mock_st.tabs)
    monkeypatch.setattr("streamlit.metric", mock_st.metric)
    monkeypatch.setattr("streamlit.text_area", mock_st.text_area)
    monkeypatch.setattr("streamlit.selectbox", mock_st.selectbox)
    monkeypatch.setattr("streamlit.rerun", mock_st.rerun)
    
    # Session state beállítása - státuszváltás dialógus megjelenítése
    mock_st.session_state = {
        'is_mobile': False,
        'is_tablet': False,
        'loading_logs': False,
        'loading_attachments': False,
        'loading_related': False,
        'show_status_dialog_1': True,
        'new_status_1': 'ACCEPTED_BY_USER'
    }
    monkeypatch.setattr("streamlit.session_state", mock_st.session_state)
    
    # Button mock felülírása a megerősítéshez
    def mock_button_confirm(*args, **kwargs):
        if args and "Megerősítés" in args[0]:
            return True
        return False
    monkeypatch.setattr("streamlit.button", mock_button_confirm)
    
    # Importáljuk az offer_detail modult
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
    from streamlit_app.pages.operator.offer_management.offer_detail import show_offer_detail
    
    # Futtatjuk a tesztet
    show_offer_detail(1)
    
    # Ellenőrizzük, hogy meghívták-e a státuszváltás API függvényt
    mock_api_functions["update_offer_status"].assert_called_once()
    assert mock_api_functions["update_offer_status"].call_args[0][0] == 1  # offer_id
    assert mock_api_functions["update_offer_status"].call_args[0][1] == 'ACCEPTED_BY_USER'  # new_status
    
    # Ellenőrizzük, hogy törölte-e a dialógus állapotot
    assert 'show_status_dialog_1' not in mock_st.session_state


def test_load_offer_details_error(monkeypatch, mock_api_functions):
    """
    Teszteli a hibaesetet, amikor az ajánlat részletek betöltése sikertelen.
    
    Args:
        monkeypatch: Pytest monkeypatch fixture
        mock_api_functions: Mock API functions fixture
    """
    # API hiba szimuláció
    mock_api_functions["get_offer_details"].return_value = (False, "API hiba történt")
    
    # Streamlit mock beállítása
    mock_st = MockStreamlit()
    monkeypatch.setattr("streamlit.markdown", mock_st.markdown)
    monkeypatch.setattr("streamlit.title", mock_st.title)
    monkeypatch.setattr("streamlit.info", mock_st.info)
    monkeypatch.setattr("streamlit.error", mock_st.error)
    monkeypatch.setattr("streamlit.success", mock_st.success)
    monkeypatch.setattr("streamlit.button", mock_st.button)
    monkeypatch.setattr("streamlit.columns", mock_st.columns)
    monkeypatch.setattr("streamlit.tabs", mock_st.tabs)
    monkeypatch.setattr("streamlit.metric", mock_st.metric)
    
    # Error mock külön figyeljük
    error_messages = []
    def mock_error(message):
        error_messages.append(message)
    monkeypatch.setattr("streamlit.error", mock_error)
    
    # Session state beállítása
    mock_st.session_state = {
        'is_mobile': False,
        'is_tablet': False
    }
    monkeypatch.setattr("streamlit.session_state", mock_st.session_state)
    
    # Importáljuk az offer_detail modult
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
    from streamlit_app.pages.operator.offer_management.offer_detail import show_offer_detail
    
    # Futtatjuk a tesztet
    show_offer_detail(1)
    
    # Ellenőrizzük, hogy megjelent-e a hibaüzenet
    assert len(error_messages) > 0
    assert any("nem található vagy hiba történt" in msg for msg in error_messages)


def test_component_classes(monkeypatch):
    """
    Teszteli az új komponens osztályok konstruktorait és alapvető működését.
    
    Args:
        monkeypatch: Pytest monkeypatch fixture
    """
    # Streamlit mock beállítása
    mock_st = MockStreamlit()
    monkeypatch.setattr("streamlit.markdown", mock_st.markdown)
    monkeypatch.setattr("streamlit.info", mock_st.info)
    monkeypatch.setattr("streamlit.columns", mock_st.columns)
    monkeypatch.setattr("streamlit.progress", mock_st.progress)
    monkeypatch.setattr("streamlit.warning", mock_st.warning)
    monkeypatch.setattr("streamlit.button", mock_st.button)
    monkeypatch.setattr("streamlit.caption", mock_st.caption)
    monkeypatch.setattr("streamlit.code", mock_st.code)
    monkeypatch.setattr("streamlit.expander", lambda *args, **kwargs: MagicMock())
    
    # Session state beállítása
    mock_st.session_state = {}
    monkeypatch.setattr("streamlit.session_state", mock_st.session_state)
    
    # Importáljuk a komponens osztályokat
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
    from streamlit_app.pages.operator.offer_management.detail_components import (
        DetailContainer,
        StatusIndicator,
        EntityCard,
        Timeline,
        ActivityLog
    )
    from streamlit_app.pages.operator.offer_management.action_components import (
        ActionBar,
        StatusTransitionModal,
        ConfirmationModal
    )
    
    # Teszteljük a komponensek konstruktorait
    detail_container = DetailContainer(title="Teszt panel", icon="📝")
    status_indicator = StatusIndicator(status="CONFIRMED_BY_COMPANY")
    entity_card = EntityCard(title="Teszt entitás", data={"name": "Teszt"}, entity_type="product")
    timeline = Timeline(events=[
        {"date": datetime.now(), "label": "Esemény", "description": "Leírás"}
    ])
    activity_log = ActivityLog(logs=[
        {"created_at": datetime.now(), "user": {"contact_name": "Teszt User"}, "from_status": "A", "to_status": "B"}
    ])
    action_bar = ActionBar(offer_id=1, offer_status="CONFIRMED_BY_COMPANY")
    status_transition_modal = StatusTransitionModal(
        offer_id=1, 
        current_status="CONFIRMED_BY_COMPANY", 
        new_status="ACCEPTED_BY_USER"
    )
    confirmation_modal = ConfirmationModal(
        title="Művelet megerősítése", 
        question="Biztosan végrehajtod ezt a műveletet?", 
        action_name="Igen"
    )
    
    # Ellenőrizzük, hogy a konstruktorok megfelelően beállították-e a tulajdonságokat
    assert detail_container.title == "Teszt panel"
    assert detail_container.icon == "📝"
    
    assert status_indicator.status == "CONFIRMED_BY_COMPANY"
    
    assert entity_card.title == "Teszt entitás"
    assert entity_card.data == {"name": "Teszt"}
    assert entity_card.entity_type == "product"
    
    assert len(timeline.events) == 1
    assert timeline.events[0]["label"] == "Esemény"
    
    assert activity_log.log_type == "status"
    
    assert action_bar.offer_id == 1
    assert action_bar.offer_status == "CONFIRMED_BY_COMPANY"
    
    assert status_transition_modal.offer_id == 1
    assert status_transition_modal.current_status == "CONFIRMED_BY_COMPANY"
    assert status_transition_modal.new_status == "ACCEPTED_BY_USER"
    
    assert confirmation_modal.title == "Művelet megerősítése"
    assert confirmation_modal.question == "Biztosan végrehajtod ezt a műveletet?"
    assert confirmation_modal.action_name == "Igen" 