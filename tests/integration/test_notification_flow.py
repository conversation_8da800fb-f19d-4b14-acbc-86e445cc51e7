"""
Integration test: Notification end-to-end flow
- Létrehoz egy értesítést az API-n keresztül
- Lekéri az értesítéseket ugyanazzal a felhasználóval
- Ellen<PERSON><PERSON><PERSON>, hogy a létrehozott értesítés megjelenik
- Ol<PERSON>ottnak j<PERSON>, majd <PERSON>rz<PERSON> az állapotot
"""
import pytest
import requests
import os

API_BASE_URL = os.getenv("API_BASE_URL", "http://backend:8000/api")
TEST_USER_TOKEN = os.getenv("TEST_USER_TOKEN")  # Előre generált teszt token, vagy fixture-ből

@pytest.mark.skipif(TEST_USER_TOKEN is None, reason="Teszt user token nincs megadva (TEST_USER_TOKEN)")
def test_notification_end_to_end():
    headers = {"Authorization": f"Bearer {TEST_USER_TOKEN}"}
    
    # 1. <PERSON>rtesí<PERSON>s létrehozása
    payload = {
        "user_id": 1,  # Teszt user ID, szükség esetén paraméterezhető
        "type": "info",
        "message": "Integration test notification",
        "detail": "Ez egy integrációs teszt értesítés.",
        "target_roles": "admin",
        "related_entity_type": "offer",
        "related_entity_id": 12345
    }
    resp = requests.post(f"{API_BASE_URL}/notifications/", json=payload, headers=headers)
    assert resp.status_code == 200, f"Létrehozás sikertelen: {resp.text}"
    notification = resp.json()
    notification_id = notification["id"]
    
    # 2. Saját értesítések lekérése
    resp = requests.get(f"{API_BASE_URL}/notifications/me", headers=headers)
    assert resp.status_code == 200, f"Lekérés sikertelen: {resp.text}"
    items = resp.json().get("items", [])
    assert any(n["id"] == notification_id for n in items), "Létrehozott értesítés nem található a listában!"
    
    # 3. Olvasottnak jelölés
    resp = requests.put(f"{API_BASE_URL}/notifications/{notification_id}/read", headers=headers)
    assert resp.status_code == 200, f"Olvasottnak jelölés sikertelen: {resp.text}"
    notif = resp.json()
    assert notif["is_read"] is True, "Az értesítés nem lett olvasottnak jelölve!"
    
    # 4. Ellenőrzés: csak olvasatlan értesítések lekérése
    resp = requests.get(f"{API_BASE_URL}/notifications/me?include_read=false", headers=headers)
    assert resp.status_code == 200
    items = resp.json().get("items", [])
    assert all(n["id"] != notification_id for n in items), "Olvasott értesítés nem tűnt el az olvasatlan listából!"
