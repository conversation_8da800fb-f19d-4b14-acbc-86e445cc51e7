# Tesztelési dokumentáció – termelo_v4

Ez a dokument<PERSON><PERSON>ó bemutatja, hogyan mű<PERSON><PERSON><PERSON> jelenleg a projekt tesztelési rendszere, k<PERSON><PERSON><PERSON>n<PERSON>s tekintettel a konténeres (Docker) környezetre, a tesztfuttatásra, valamint a Streamlit és backend komponensek tesztelésére.

## Főbb elvek
- **Minden tesztet Docker konténerben futtatunk** a projekt docker-compose.yml előírásai szerint.
- A tesztek a `/tests` mappában találhatók, struktúrája tükrözi a fő alkalmazás moduljait.
- A tesztek Pytest-tel íródtak, minden új funkcióhoz kötelező az egységteszt.
- A frontend (Streamlit) és backend (FastAPI) tesztek futtatása eltérő környezetet igényelhet.

## Tesztfuttatás folyamata

### 1. Teszt entrypoint script
A `/tests/test_entrypoint.py` egy intelligens indítószkript, amely automatikusan felismeri, hogy melyik tesztmodulnak van szüksége a Streamlit frontend modulokra, és ennek megfelelően egészíti ki a PYTHONPATH-ot.

- **Használat:**
  ```bash
  docker compose exec backend python3 tests/test_entrypoint.py --test tests/components/test_sidebar.py
  ```
- A script automatikusan:
  - Felismeri, ha a teszt importálja a `streamlit_app`-ot.
  - Kiegészíti a PYTHONPATH-ot a `streamlit_app` útvonallal.
  - Meghívja a pytest-et a megfelelő környezettel.
  - Interaktív módban is futtatható, ha nincs --test argumentum, ilyenkor listázza a választható teszteket.

### 2. PYTHONPATH kezelés
- A backend konténerben a `/app` és `/app/streamlit_app` kerül a PYTHONPATH-ba, ha szükséges.
- Így a Streamlit-hez tartozó modulok (pl. `app_config`, `sidebar.py`) is importálhatók a tesztekből.

### 3. Session state és Streamlit sajátosságok
- A Streamlit session state NEM működik natívan pytest alatt, csak streamlit run alatt.
- A tesztekben minden szükséges kulcsot (pl. `mobile_view`, `user_favorites`) explicit inicializálni kell, vagy mock-olni kell a `st.session_state`-et.
- Ha a teszt session state hibát ad, ellenőrizd, hogy a teszt elején megtörténik-e az inicializálás vagy patch-elés.

### 4. Tesztstruktúra
- **/tests/components/** – Streamlit frontendhez tartozó tesztek
- **/tests/backend/** – Backend API tesztek
- Új modulhoz új tesztfájlt kell létrehozni, a név convention: `test_<modulnév>.py`

### 5. Tesztfuttatás példák
- **Oldschool:**
  ```bash
  docker compose exec backend pytest -v --maxfail=3 --disable-warnings tests/components/test_sidebar.py
  ```
- **Ajánlott:**
  ```bash
  docker compose exec backend python3 tests/test_entrypoint.py --test tests/components/test_sidebar.py
  ```
- **Interaktív tesztválasztás:**
  ```bash
  docker compose exec backend python3 tests/test_entrypoint.py
  ```

### 6. Hibaelhárítás
- Ha `ModuleNotFoundError` vagy session state hiba jelentkezik:
  - Ellenőrizd, hogy a PYTHONPATH megfelelő-e (használd az entrypoint scriptet!)
  - Inicializáld vagy mock-olj minden szükséges session state kulcsot a tesztben.
  - Ha új modulra van szükség a PYTHONPATH-ban, egészítsd ki a scriptet vagy a tesztet.

---

## Összefoglalás
A tesztelés Docker backend konténerben történik, a `/tests/test_entrypoint.py` script automatikusan kezeli a PYTHONPATH-ot és a tesztmodulok kiválasztását. A Streamlit session state-et minden tesztben explicit inicializálni vagy mock-olni kell. Minden új funkcióhoz kötelező egységtesztet írni, és a teszteknek a projekt struktúráját kell követniük.

Ha bármilyen kérdés vagy hiba merül fel a teszteléssel kapcsolatban, először ezt a README-t, majd a PLANNING.md-t és TASK.md-t ellenőrizd.
