import os
import sys
import subprocess
from pathlib import Path
import argparse


def discover_test_files(base_dir):
    """Recursively find all test_*.py files under base_dir, except this entrypoint."""
    base = Path(base_dir)
    return [str(f) for f in base.rglob('test_*.py') if f.name != 'test_entrypoint.py']


def requires_streamlit_app(test_file):
    """Check if the test file imports streamlit_app (by simple static analysis)."""
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read(5120)  # Only check first 5k chars for performance
    return (
        'from streamlit_app' in content or 'import streamlit_app' in content
    )


def main():
    parser = argparse.ArgumentParser(description="Projekt teszt entrypoint.")
    parser.add_argument('--test', type=str, help='Tesztfájl elérési útja')
    args = parser.parse_args()

    test_files = discover_test_files(os.path.dirname(__file__))
    if not test_files:
        print('Nincs tesztfájl a /tests mappában.')
        sys.exit(1)

    if args.test:
        test_file = args.test
        if not Path(test_file).exists():
            print(f'Nem található a tesztfájl: {test_file}')
            sys.exit(1)
    else:
        print('Elérhető tesztek:')
        for idx, f in enumerate(test_files):
            print(f'  {idx+1}. {f}')
        sel = input('Válassz tesztet (szám): ')
        try:
            test_file = test_files[int(sel)-1]
        except Exception:
            print('Hibás választás.')
            sys.exit(1)

    # Döntsük el, kell-e streamlit_app a PYTHONPATH-hoz
    extra_env = os.environ.copy()
    if requires_streamlit_app(test_file):
        streamlit_app_path = str(Path(__file__).parent.parent / 'streamlit_app')
        extra_env['PYTHONPATH'] = extra_env.get('PYTHONPATH', '') + f':{streamlit_app_path}'
        print(f'[INFO] PYTHONPATH kiegészítve: {extra_env["PYTHONPATH"]}')
    else:
        print('[INFO] Streamlit_app nem szükséges ehhez a teszthez.')

    # Pytest futtatása
    cmd = ['pytest', '-v', '--maxfail=3', '--disable-warnings', test_file]
    print(f'Futtatott parancs: {" ".join(cmd)}')
    subprocess.run(cmd, env=extra_env)


if __name__ == '__main__':
    main()
