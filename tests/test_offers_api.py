import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_offers_response_contains_user_and_product_type():
    """
    Teszteli, hogy a /offers végpont válasza tartalmazza-e a user és product_type objektumokat.
    """
    # Feltételezzük, hogy legalább egy ajánlat létezik
    response = client.get("/offers")
    assert response.status_code == 200
    offers = response.json()
    assert isinstance(offers, list)
    if offers:
        offer = offers[0]
        assert "user" in offer, "A válaszban nincs user mező!"
        assert isinstance(offer["user"], dict)
        assert "email" in offer["user"]
        assert "product_type" in offer, "A válaszban nincs product_type mező!"
        assert isinstance(offer["product_type"], dict)
        assert "name" in offer["product_type"]
