import pytest
import streamlit as st
from streamlit_app.components import sidebar
from unittest.mock import patch

def test_mobile_toggle_sets_session_state():
    """<PERSON><PERSON> esetén a mobil toggle állítja a session state-et."""
    st.session_state.clear()
    if "mobile_view" not in st.session_state:
        st.session_state.mobile_view = False
    if "user_favorites" not in st.session_state:
        st.session_state.user_favorites = []
    with patch("streamlit_app.components.sidebar.is_authenticated", return_value=True), \
         patch("streamlit_app.components.sidebar.get_current_user", return_value={"role": "admin"}):
        sidebar.detect_mobile()
        assert "mobile_view" in st.session_state
        # <PERSON><PERSON><PERSON><PERSON> tesztelése
        st.session_state.mobile_view = True
        assert st.session_state.mobile_view is True

def test_mobile_navigation_renders_without_error():
    """Mobil navigáció hívható és nem dob hibát bejelentkezett userrel."""
    st.session_state.clear()
    if "user_favorites" not in st.session_state:
        st.session_state.user_favorites = ["pages/admin_dashboard.py"]
    with patch("streamlit_app.components.sidebar.is_authenticated", return_value=True), \
         patch("streamlit_app.components.sidebar.get_current_user", return_value={"role": "admin", "contact_name": "Teszt Admin"}):
        sidebar.render_mobile_navigation()
        # Ha nem dob hibát, sikeres
        assert True

def test_sidebar_desktop_and_mobile_branch():
    """A render_sidebar() desktop/mobil ágat helyesen választja."""
    st.session_state.clear()
    if "mobile_view" not in st.session_state:
        st.session_state.mobile_view = True
    if "user_favorites" not in st.session_state:
        st.session_state.user_favorites = []
    # Mobil nézet
    st.session_state.mobile_view = True
    with patch("streamlit_app.components.sidebar.is_authenticated", return_value=True), \
         patch("streamlit_app.components.sidebar.get_current_user", return_value={"role": "admin"}):
        sidebar.render_sidebar()
    # Desktop nézet
    st.session_state.mobile_view = False
    with patch("streamlit_app.components.sidebar.is_authenticated", return_value=True), \
         patch("streamlit_app.components.sidebar.get_current_user", return_value={"role": "admin"}):
        sidebar.render_sidebar()
        assert True
