import pytest
import streamlit as st
from streamlit.testing.v1 import AppTest
from streamlit_app.components import data_display

@pytest.fixture
def offers():
    return [
        {
            "id": 1,
            "status": "CREATED",
            "delivery_date": "2025-04-22",
            "product_name": "<PERSON><PERSON><PERSON><PERSON>",
            "quantity_in_kg": 1000,
            "user_name": "Teszt Kft.",
        },
        {
            "id": 2,
            "status": "CONFIRMED_BY_COMPANY",
            "delivery_date": "2025-04-23",
            "product_name": "<PERSON>é<PERSON>",
            "quantity_in_kg": 500,
            "user_name": "Demo Bt.",
            "confirmed_quantity": 400,
            "confirmed_price": 120000,
        },
        {
            "id": 3,
            "status": "REJECTED_BY_USER",
            "delivery_date": "2025-04-24",
            "product_name": "Hagyma",
            "quantity_in_kg": 300,
            "user_name": "Mintagazda Zrt.",
        },
    ]

def test_display_offer_table_renders_html(tmp_path, offers):
    # AppTest a Streamlit teszteléshez
    at = AppTest.from_function(lambda: data_display.display_offer_table(offers))
    at.run()
    # <PERSON>ü<PERSON>, hogy van HTML táblázat a státusz színezéssel
    html_tables = [el for el in at.markdowns if '<table' in el.value and 'background:' in el.value]
    assert html_tables, "A státusz színezett HTML táblázat nem jelent meg a komponensben."
    # Ellenőrizzük, hogy minden státusz szerepel a HTML-ben
    for offer in offers:
        assert offer["product_name"] in html_tables[0].value
        assert offer["user_name"] in html_tables[0].value
        assert data_display.format_status(offer["status"]) in html_tables[0].value
    # Ellenőrizzük, hogy a CREATED státusz háttérszíne fallback értékkel jelenik meg
    created_status_html = [el.value for el in at.markdowns if 'CREATED' in el.value or '#23272e' in el.value]
    assert any('#23272e' in html for html in created_status_html), "A 'CREATED' státusz háttérszíne nem fallback (#23272e)!"
