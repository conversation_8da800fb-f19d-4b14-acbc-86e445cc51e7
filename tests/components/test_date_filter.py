"""
Tesztek a ui_components.py render_date_filter függvényéhez
"""
import pytest
import datetime
from unittest.mock import MagicMock, patch

# Az import előtt be kell <PERSON><PERSON><PERSON><PERSON> a mock-ot a Streamlit-hez
@pytest.fixture
def mock_streamlit():
    """Streamlit mock létrehozása a tesztekhez"""
    with patch('streamlit.tabs') as mock_tabs, \
         patch('streamlit.write') as mock_write, \
         patch('streamlit.checkbox') as mock_checkbox, \
         patch('streamlit.markdown') as mock_markdown, \
         patch('streamlit.columns') as mock_columns, \
         patch('streamlit.date_input') as mock_date_input, \
         patch('streamlit.warning') as mock_warning:
        
        # Tabokat visszaadó mock 
        mock_tab = MagicMock()
        mock_tabs.return_value = [mock_tab]
        
        # Oszlop mockokat létrehozni
        mock_col = MagicMock()
        mock_columns.return_value = [mock_col, mock_col]
        
        # Session state mock
        mock_session_state = {}
        streamlit_mock = MagicMock()
        streamlit_mock.session_state = mock_session_state
        
        # Egyéb Streamlit függvények mockjai
        yield {
            'tabs': mock_tabs,
            'write': mock_write,
            'checkbox': mock_checkbox,
            'markdown': mock_markdown,
            'columns': mock_columns,
            'date_input': mock_date_input,
            'warning': mock_warning,
            'session_state': mock_session_state,
            'tab_mock': mock_tab,
            'col_mock': mock_col
        }

def test_render_date_filter(mock_streamlit):
    """Teszteli a render_date_filter függvényt"""
    # Import a komponenst
    with patch('streamlit.session_state', mock_streamlit['session_state']):
        # Csak most importáljuk, miután a patch-ek érvénybe léptek
        from streamlit_app.pages.operator.offer_management.ui_components import render_date_filter
        
        # Teszt végrehajtása
        from_date, to_date = render_date_filter('test')
        
        # Ellenőrzések
        assert mock_streamlit['tabs'].called
        assert mock_streamlit['write'].called
        assert mock_streamlit['checkbox'].called
        assert mock_streamlit['markdown'].called
        assert mock_streamlit['columns'].called
        assert mock_streamlit['date_input'].called
        
        # Ellenőrizzük, hogy a visszaadott értékek dátum típusúak
        assert isinstance(from_date, datetime.date)
        assert isinstance(to_date, datetime.date)
        
        # Ellenőrizzük, hogy a from_date korábbi, mint a to_date
        assert from_date <= to_date
        
        # Ellenőrizzük, hogy a tab_key létrejött a session_state-ben
        assert 'date_tab_test' in mock_streamlit['session_state']

def test_render_date_filter_tab_change(mock_streamlit):
    """Teszteli a tab váltás működését a render_date_filter függvényben"""
    # Session state beállítása előzetesen
    mock_streamlit['session_state']['date_tab_test'] = 2  # A harmadik tab kiválasztása
    mock_streamlit['session_state']['tab_2'] = True
    
    with patch('streamlit.session_state', mock_streamlit['session_state']):
        # Import a komponenst
        from streamlit_app.pages.operator.offer_management.ui_components import render_date_filter
        
        # Teszt végrehajtása
        from_date, to_date = render_date_filter('test')
        
        # Ellenőrizzük, hogy a harmadik tab aktív
        assert mock_streamlit['session_state']['date_tab_test'] == 2
        
        # Ellenőrizzük, hogy a dátum értékek megfelelően lettek beállítva
        today = datetime.datetime.now().date()
        expected_selected_date = today + datetime.timedelta(days=7)  # A harmadik tab dátuma
        
        # A from_date a selected_date - 3 nap, a to_date a selected_date + 3 nap
        expected_from_date = expected_selected_date - datetime.timedelta(days=3)
        expected_to_date = expected_selected_date + datetime.timedelta(days=3)
        
        # A mock miatt a tényleges értékeket nem tudjuk ellenőrizni, de a logikát igen
        assert isinstance(from_date, datetime.date)
        assert isinstance(to_date, datetime.date)

def test_render_date_filter_invalid_dates(mock_streamlit):
    """Teszteli az érvénytelen dátumok kezelését a render_date_filter függvényben"""
    # Mock beállítása a date_input-ra, hogy érvénytelen dátumokat adjon vissza
    future_date = datetime.datetime.now().date() + datetime.timedelta(days=10)
    past_date = datetime.datetime.now().date() - datetime.timedelta(days=10)
    
    # Az első date_input a jövőbeli dátumot, a második a múltbeli dátumot adja vissza
    mock_streamlit['date_input'].side_effect = [future_date, past_date]
    
    with patch('streamlit.session_state', mock_streamlit['session_state']):
        # Import a komponenst
        from streamlit_app.pages.operator.offer_management.ui_components import render_date_filter
        
        # Teszt végrehajtása
        from_date, to_date = render_date_filter('test')
        
        # Ellenőrizzük, hogy a warning megjelent
        assert mock_streamlit['warning'].called
        
        # Az eredmény dátumoknak továbbra is érvényesnek kell lenniük
        assert isinstance(from_date, datetime.date)
        assert isinstance(to_date, datetime.date)
        assert from_date <= to_date 