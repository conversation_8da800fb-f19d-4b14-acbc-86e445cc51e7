#!/usr/bin/env python3
"""
Comprehensive test for API Data Diagnostic Tool integration
Tests the "0 Results Problem" diagnostic and solution framework
"""

import ast
import sys
import os

def test_file_structure():
    """Test that all required files exist and have valid syntax"""
    print("🔍 TESTING FILE STRUCTURE")
    print("=" * 40)
    
    required_files = [
        "streamlit_app/pages/operator/offer_management/api_data_diagnostic.py",
        "streamlit_app/pages/operator/offer_management.py"
    ]
    
    all_passed = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
            
            # Test syntax
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    ast.parse(f.read())
                print(f"✅ {file_path} has valid syntax")
            except SyntaxError as e:
                print(f"❌ {file_path} syntax error: {e}")
                all_passed = False
        else:
            print(f"❌ {file_path} missing")
            all_passed = False
    
    return all_passed

def test_api_diagnostic_components():
    """Test API diagnostic tool components"""
    print("\n🔬 TESTING API DIAGNOSTIC COMPONENTS")
    print("=" * 45)
    
    api_diagnostic_path = "streamlit_app/pages/operator/offer_management/api_data_diagnostic.py"
    
    try:
        with open(api_diagnostic_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_components = [
            ("APIDataDiagnostic class", "class APIDataDiagnostic:"),
            ("Producer existence test", "def _test_producer_exists("),
            ("Minimal filters test", "def _test_minimal_filters("),
            ("Date range test", "def _test_date_ranges("),
            ("Parameter variations test", "def _test_parameter_variations("),
            ("Similar producers analysis", "def _find_similar_producers("),
            ("Recommendations generator", "def _generate_recommendations("),
            ("UI render function", "def render_api_data_diagnostic_ui("),
            ("Full diagnosis runner", "def run_full_api_diagnosis("),
            ("Parameter test runner", "def run_quick_param_test("),
            ("Producer analysis runner", "def run_similar_producers_analysis("),
            ("Date test runner", "def run_date_range_test(")
        ]
        
        all_passed = True
        
        for component_name, pattern in required_components:
            if pattern in content:
                print(f"✅ {component_name}")
            else:
                print(f"❌ {component_name} missing")
                all_passed = False
        
        # Test critical functionality patterns
        critical_patterns = [
            ("Dict import handling", "from typing import Dict"),
            ("Error handling", "try:" and "except Exception"),
            ("Streamlit integration", "import streamlit as st"),
            ("Logging support", "import logging"),
            ("API client wrapper", "class APIClientWrapper"),
            ("Producer ID analysis", "producer_id in"),
            ("Date handling", "datetime"),
            ("JSON serialization", "st.json")
        ]
        
        for pattern_name, pattern in critical_patterns:
            if isinstance(pattern, tuple):
                # Multiple patterns required
                if all(p in content for p in pattern):
                    print(f"✅ {pattern_name}")
                else:
                    print(f"❌ {pattern_name} incomplete")
                    all_passed = False
            else:
                # Single pattern
                if pattern in content:
                    print(f"✅ {pattern_name}")
                else:
                    print(f"❌ {pattern_name} missing")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing API diagnostic components: {e}")
        return False

def test_offer_management_integration():
    """Test integration with offer management"""
    print("\n🔗 TESTING OFFER MANAGEMENT INTEGRATION")
    print("=" * 50)
    
    offer_mgmt_path = "streamlit_app/pages/operator/offer_management.py"
    
    try:
        with open(offer_mgmt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        integration_checks = [
            ("API diagnostic function", "def render_api_data_diagnostic_section():"),
            ("API diagnostic import", "from pages.operator.offer_management.api_data_diagnostic import"),
            ("Diagnostic section call", "render_api_data_diagnostic_section()"),
            ("Producer filter check", "has_producer_filter"),
            ("Date range extraction", "date_from"),
            ("Error handling", "except ImportError"),
            ("Producer ID conversion", "int(producer_info['id'])"),
            ("UI integration", "render_api_data_diagnostic_ui(")
        ]
        
        all_passed = True
        
        for check_name, pattern in integration_checks:
            if pattern in content:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name} missing")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing offer management integration: {e}")
        return False

def test_diagnostic_workflow():
    """Test the diagnostic workflow logic"""
    print("\n🔄 TESTING DIAGNOSTIC WORKFLOW")
    print("=" * 40)
    
    workflow_tests = [
        {
            'name': 'Producer Existence Validation',
            'description': 'Checks if producer exists in database',
            'key_components': ['_test_producer_exists', 'all_offers', 'found_producers']
        },
        {
            'name': 'Parameter Variation Testing',
            'description': 'Tests different API parameter combinations',
            'key_components': ['_test_parameter_variations', 'param_variations', 'working_combinations']
        },
        {
            'name': 'Date Range Analysis',
            'description': 'Tests different date ranges for data availability',
            'key_components': ['_test_date_ranges', 'test_ranges', 'date_analysis']
        },
        {
            'name': 'Similar Producers Discovery',
            'description': 'Finds other producers with data for comparison',
            'key_components': ['_find_similar_producers', 'producer_stats', 'top_producers']
        },
        {
            'name': 'Recommendations Generation',
            'description': 'Provides actionable recommendations',
            'key_components': ['_generate_recommendations', 'recommendations', 'test_results']
        }
    ]
    
    api_diagnostic_path = "streamlit_app/pages/operator/offer_management/api_data_diagnostic.py"
    
    try:
        with open(api_diagnostic_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        all_passed = True
        
        for test in workflow_tests:
            print(f"\n🧪 {test['name']}")
            print(f"   {test['description']}")
            
            components_found = 0
            for component in test['key_components']:
                if component in content:
                    components_found += 1
                    print(f"   ✅ {component}")
                else:
                    print(f"   ❌ {component} missing")
            
            if components_found == len(test['key_components']):
                print(f"   ✅ {test['name']} workflow complete")
            else:
                print(f"   ❌ {test['name']} workflow incomplete ({components_found}/{len(test['key_components'])})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing diagnostic workflow: {e}")
        return False

def generate_usage_guide():
    """Generate usage guide for the diagnostic tool"""
    print("\n📋 API DIAGNOSTIC TOOL USAGE GUIDE")
    print("=" * 50)
    
    print("""
🔬 **API Data Diagnostic Tool** - Zero Results Problem Solver

## Purpose:
Diagnose and solve the "Producer filter returns 0 results" problem by:
1. Validating producer existence in database
2. Testing different API parameter combinations  
3. Analyzing date range impacts
4. Finding working parameter patterns
5. Providing actionable recommendations

## How to Use:

### Step 1: Access Diagnostic Tool
1. Go to Operator > Offer Management
2. Set a producer filter (e.g., "Szabó Gábot")
3. Open diagnostic panel (🚨 button)
4. Scroll to "🔬 API ADAT DIAGNOSZTIKA" section

### Step 2: Run Diagnostics
Available diagnostic tests:

🔬 **TELJES DIAGNOSZTIKA** - Comprehensive analysis
📊 **Paraméter Teszt** - Quick parameter variation test
🔍 **Producer Keresés** - Find similar producers in database
📅 **Dátum Teszt** - Test different date ranges

### Step 3: Interpret Results
- ✅ Green = Working correctly
- ⚠️ Yellow = Warning, needs attention
- ❌ Red = Critical issue found

### Step 4: Apply Recommendations
The tool provides specific recommendations like:
- "Use simpler parameter combinations"
- "Expand date range"
- "Producer ID not found in database"
- "Try these working parameter patterns"

## Common Issues Solved:

1. **Producer Not Found**: Tool confirms if producer exists
2. **Too Many Parameters**: Identifies minimal working combinations
3. **Wrong Date Range**: Finds ranges with actual data
4. **API Filter Logic**: Tests different filter approaches

## Technical Details:

The tool tests these scenarios:
- Producer existence via multiple API approaches
- Minimal filter combinations (only producer_id vs all params)
- Different date ranges (3 months, 6 months, 1 year, no limit)
- Parameter variations (individual vs combined filters)
- Similar producer comparison

This comprehensive approach quickly identifies whether the issue is:
- Data availability (producer has no offers)
- Filter logic (API requires specific parameter format)  
- Date range (data exists but outside current range)
- Parameter redundancy (too many conflicting filters)
""")

if __name__ == "__main__":
    print("🚨 API DATA DIAGNOSTIC TOOL - COMPREHENSIVE VALIDATION")
    print("=" * 65)
    
    all_tests_passed = True
    
    # Test file structure
    if not test_file_structure():
        all_tests_passed = False
    
    # Test API diagnostic components
    if not test_api_diagnostic_components():
        all_tests_passed = False
    
    # Test offer management integration
    if not test_offer_management_integration():
        all_tests_passed = False
    
    # Test diagnostic workflow
    if not test_diagnostic_workflow():
        all_tests_passed = False
    
    print("\n" + "=" * 65)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - API DIAGNOSTIC TOOL READY!")
        print("🚀 The tool is ready to solve the '0 Results Problem'")
        print("📊 Comprehensive diagnostics available for producer filtering issues")
        
        # Generate usage guide
        generate_usage_guide()
        
    else:
        print("❌ SOME TESTS FAILED")
        print("🔧 Review the failed components before using the tool")
    
    sys.exit(0 if all_tests_passed else 1)