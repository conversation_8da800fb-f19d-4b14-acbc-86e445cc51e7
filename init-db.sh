#!/bin/bash

# <PERSON><PERSON><PERSON>, amíg az adatb<PERSON><PERSON>s elér<PERSON>t<PERSON> lesz
echo "Várakozás az adatbázis elérhetőségére..."
until PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c '\q'; do
  echo "Adatbázis még nem elérhető, várakozás..."
  sleep 2
done

echo "Adatbázis <PERSON>, migr<PERSON>ciók futtatása..."

# Migr<PERSON><PERSON>ós könyvtár létrehoz<PERSON>, ha nem létezik
mkdir -p migrations/versions

# Migrációs fájlok létrehozása, ha nem léteznek
if [ ! -f "migrations/env.py" ]; then
    echo "Migrációs környezet fájl létrehozása..."
    cat > migrations/env.py << 'EOL'
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

from app.core.config import settings
from app.db.base import Base
from app.models import *

config = context.config

if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline() -> None:
    url = f"**************************************/termelo_db"
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = f"**************************************/termelo_db"
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
EOL
fi

if [ ! -f "migrations/script.py.mako" ]; then
    echo "Migrációs template fájl létrehozása..."
    cat > migrations/script.py.mako << 'EOL'
"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision: str = ${repr(up_revision)}
down_revision: Union[str, None] = ${repr(down_revision)}
branch_labels: Union[str, Sequence[str], None] = ${repr(branch_labels)}
depends_on: Union[str, Sequence[str], None] = ${repr(depends_on)}

def upgrade() -> None:
    ${upgrades if upgrades else "pass"}

def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
EOL
fi

# Migrációk futtatása
echo "Migrációk futtatása..."

# Check if migration is working
if alembic current 2>&1 | grep -q "Can't locate revision"; then
  echo "Migration error detected, bypassing Alembic and creating tables directly..."
  
  # Drop alembic_version table to stop errors
  PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c "DROP TABLE IF EXISTS alembic_version;" || true
  
  # Create tables directly using SQLAlchemy
  echo "Creating tables using SQLAlchemy metadata..."
  python3 -c "
from app.db.base import Base
from app.models import *
from app.db.session import engine
Base.metadata.create_all(bind=engine)
print('Tables created successfully via SQLAlchemy')
" || true
else
  # Normal migration path
  echo "Running normal migration path..."
  alembic revision --autogenerate -m "Initial migration" || true
  alembic upgrade head || true
fi

echo "Adatbázis inicializálása kész!"
