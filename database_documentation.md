# Database Documentation

This document provides a detailed overview of the database schema, including table structures, entity relationships, and initial data analysis.

## 1. Database Structure

This section details all database tables, their columns, data types, and constraints.

### `users`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the user. |
| `email` | `String(255)` | `UNIQUE`, `NOT NULL`, `INDEX` | User's email address. |
| `password_hash` | `String(255)` | `NOT NULL` | Hashed password for the user. |
| `role` | `String(50)` | `NOT NULL`, `INDEX` | User's role (e.g., 'producer', 'admin'). |
| `company_name` | `String(255)` | | User's company name. |
| `tax_id` | `String(50)` | | User's tax identification number. |
| `contact_name` | `String(255)` | `NOT NULL` | Name of the contact person. |
| `phone_number` | `String(50)` | `NOT NULL` | User's phone number. |
| `is_active` | `Boolean` | `NOT NULL`, `DEFAULT: False` | Flag to indicate if the user account is active. |
| `activation_token` | `String(255)` | | Token for account activation. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the user was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the user. |

### `user_default_settings`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the default settings. |
| `user_id` | `Integer` | `FOREIGN KEY (users.id)`, `UNIQUE`, `NOT NULL` | The user these settings belong to. |
| `default_product_type_id` | `Integer` | `FOREIGN KEY (product_types.id)` | Default product type for the user. |
| `default_quality_grade_id` | `Integer` | `FOREIGN KEY (quality_grades.id)` | Default quality grade for the user. |
| `default_quantity_unit` | `String(10)` | `NOT NULL`, `DEFAULT: 'kg'` | Default unit for quantity. |
| `default_product_type_name` | `String(100)` | | Name of the default product type. |
| `default_quality_grade_name` | `String(100)` | | Name of the default quality grade. |
| `default_category_id` | `Integer` | | ID of the default category. |
| `default_category_name` | `String(100)` | | Name of the default category. |
| `has_quality_grades` | `Boolean` | `NOT NULL`, `DEFAULT: False` | Indicates if the default product has quality grades. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the settings were created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the settings. |

### `password_reset_tokens`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the token. |
| `user_id` | `Integer` | `FOREIGN KEY (users.id)`, `NOT NULL` | The user this token belongs to. |
| `token` | `String(255)` | `NOT NULL`, `UNIQUE` | The password reset token. |
| `expires_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Expiration date for the token. |
| `is_used` | `Boolean` | `NOT NULL`, `DEFAULT: False` | Flag to indicate if the token has been used. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the token was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the token. |

### `product_categories`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the product category. |
| `name` | `String(100)` | `NOT NULL`, `UNIQUE` | Name of the product category. |
| `description` | `Text` | | Description of the product category. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the category was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the category. |

### `product_types`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the product type. |
| `category_id` | `Integer` | `FOREIGN KEY (product_categories.id)`, `NOT NULL` | The category this product type belongs to. |
| `name` | `String(100)` | `NOT NULL` | Name of the product type. |
| `description` | `Text` | | Description of the product type. |
| `has_quality_grades` | `Boolean` | `NOT NULL`, `DEFAULT: True` | Indicates if this product type has quality grades. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the product type was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the product type. |

### `quality_grades`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the quality grade. |
| `product_type_id` | `Integer` | `FOREIGN KEY (product_types.id)`, `NOT NULL` | The product type this quality grade belongs to. |
| `name` | `String(50)` | `NOT NULL` | Name of the quality grade. |
| `min_shoulder_diameter` | `Numeric(5, 2)` | | Minimum shoulder diameter. |
| `max_shoulder_diameter` | `Numeric(5, 2)` | | Maximum shoulder diameter. |
| `min_length` | `Numeric(5, 2)` | | Minimum length. |
| `max_length` | `Numeric(5, 2)` | | Maximum length. |
| `description` | `Text` | | Description of the quality grade. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the quality grade was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the quality grade. |

### `offers`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the offer. |
| `user_id` | `Integer` | `FOREIGN KEY (users.id)`, `NOT NULL` | The user who owns the offer. |
| `product_type_id` | `Integer` | `FOREIGN KEY (product_types.id)`, `NOT NULL` | The product type of the offer. |
| `quality_grade_id` | `Integer` | `FOREIGN KEY (quality_grades.id)` | The quality grade of the offer. |
| `quantity_in_kg` | `Numeric(10, 2)` | `NOT NULL`, `CHECK: quantity_in_kg > 0` | Quantity of the product in kilograms. |
| `delivery_date` | `Date` | `NOT NULL` | Expected delivery date. |
| `status` | `String(50)` | `NOT NULL`, `DEFAULT: 'CREATED'`, `CHECK: status IN (...)` | Status of the offer. |
| `confirmed_quantity` | `Numeric(10, 2)` | `CHECK: confirmed_quantity > 0` | Confirmed quantity. |
| `confirmed_price` | `Numeric(10, 2)` | `CHECK: confirmed_price > 0` | Confirmed price. |
| `note` | `Text` | | Additional notes for the offer. |
| `created_by_user_id` | `Integer` | `FOREIGN KEY (users.id)`, `NOT NULL` | The user who created the offer. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the offer was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the offer. |

### `offer_logs`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the log entry. |
| `offer_id` | `Integer` | `FOREIGN KEY (offers.id)`, `NOT NULL` | The offer this log entry belongs to. |
| `old_status` | `String(50)` | | The previous status of the offer. |
| `new_status` | `String(50)` | `NOT NULL` | The new status of the offer. |
| `changed_by` | `Integer` | `FOREIGN KEY (users.id)`, `NOT NULL` | The user who made the change. |
| `note` | `Text` | | Additional notes for the status change. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the log was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the log. |

### `notifications`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the notification. |
| `user_id` | `Integer` | `FOREIGN KEY (users.id)` | The user this notification is for. |
| `type` | `String(50)` | `NOT NULL` | Type of the notification. |
| `message` | `String(255)` | `NOT NULL` | The notification message. |
| `detail` | `Text` | | Detailed information about the notification. |
| `is_read` | `Boolean` | `NOT NULL`, `DEFAULT: False` | Flag to indicate if the notification has been read. |
| `target_roles` | `String(255)` | | Roles targeted by the notification. |
| `related_entity_type` | `String(50)` | | Type of the related entity. |
| `related_entity_id` | `Integer` | | ID of the related entity. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the notification was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the notification. |

### `user_saved_filters`

| Column | Data Type | Constraints | Description |
|---|---|---|---|
| `id` | `Integer` | `PRIMARY KEY`, `INDEX` | Unique identifier for the saved filter. |
| `user_id` | `Integer` | `FOREIGN KEY (users.id)`, `NOT NULL` | The user who owns this filter. |
| `name` | `String(100)` | `NOT NULL` | Name of the filter. |
| `description` | `String(255)` | | Description of the filter. |
| `filter_type` | `String(50)` | `NOT NULL` | Type of the filter (e.g., 'offer'). |
| `is_default` | `Boolean` | `NOT NULL`, `DEFAULT: False` | Flag to indicate if this is the default filter. |
| `filter_data` | `JSON` | `NOT NULL` | The filter criteria stored in JSON format. |
| `created_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()` | Timestamp of when the filter was created. |
| `updated_at` | `DateTime` | `NOT NULL`, `DEFAULT: now()`, `ON UPDATE: now()` | Timestamp of the last update to the filter. |

## 2. Entity Relationships

This section describes the relationships between the database tables.

- **`users` and `offers`**: One-to-Many. A user can have multiple offers.
  - `users.id` <--> `offers.user_id`
  - `users.id` <--> `offers.created_by_user_id`
- **`users` and `offer_logs`**: One-to-Many. A user can change multiple offers, creating log entries.
  - `users.id` <--> `offer_logs.changed_by`
- **`users` and `notifications`**: One-to-Many. A user can have multiple notifications.
  - `users.id` <--> `notifications.user_id`
- **`users` and `user_default_settings`**: One-to-One. A user has one set of default settings.
  - `users.id` <--> `user_default_settings.user_id`
- **`users` and `password_reset_tokens`**: One-to-Many. A user can have multiple password reset tokens.
  - `users.id` <--> `password_reset_tokens.user_id`
- **`users` and `user_saved_filters`**: One-to-Many. A user can have multiple saved filters.
  - `users.id` <--> `user_saved_filters.user_id`
- **`offers` and `offer_logs`**: One-to-Many. An offer can have multiple log entries.
  - `offers.id` <--> `offer_logs.offer_id`
- **`product_categories` and `product_types`**: One-to-Many. A product category can have multiple product types.
  - `product_categories.id` <--> `product_types.category_id`
- **`product_types` and `quality_grades`**: One-to-Many. A product type can have multiple quality grades.
  - `product_types.id` <--> `quality_grades.product_type_id`
- **`product_types` and `offers`**: One-to-Many. A product type can be associated with multiple offers.
  - `product_types.id` <--> `offers.product_type_id`
- **`quality_grades` and `offers`**: One-to-Many. A quality grade can be associated with multiple offers.
  - `quality_grades.id` <--> `offers.quality_grade_id`

## 3. Initial Data Analysis

No files responsible for seeding the database with initial data were found. The `init-db.sh` script handles database creation and schema migration but does not insert any initial data. This suggests that the application expects to start with an empty database, and all data is created through user interaction or administrative actions.
