#!/usr/bin/env python3
"""
Verify HTML Rendering - Console <PERSON>rror Test
<PERSON>, hogy a konzol hibák befolyás<PERSON>ják-e a HTML renderelést
"""
import streamlit as st

st.set_page_config(page_title="HTML Rendering Verification", layout="wide")

st.title("🔍 HTML Rendering Verification")

st.info("""
### Console Errors Explained:
- **404 errors**: Streamlit looking for optional config files (harmless)
- **Analytics blocked**: Your ad blocker is working (good!)
- **Unrecognized features**: Browser capability checks (informational)
- **iframe warnings**: Expected for components.html() usage
""")

# Test 1: Basic HTML
st.markdown("### Test 1: Basic HTML Rendering")
st.markdown("""
<div style="background: #28a745; color: white; padding: 1rem; border-radius: 8px;">
    <h3 style="margin: 0;">✅ If you see this green box, HTML works!</h3>
    <p style="margin: 0.5rem 0 0 0;">Console errors don't affect HTML rendering.</p>
</div>
""", unsafe_allow_html=True)

# Test 2: CSS Classes
st.markdown("### Test 2: CSS Classes")
st.markdown("""
<style>
.success-box {
    background: #007bff;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}
.warning-box {
    background: #ffc107;
    color: #212529;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}
</style>
<div class="success-box">
    ✅ CSS Classes Working - Blue Box
</div>
<div class="warning-box">
    ⚠️ CSS Classes Working - Yellow Box
</div>
""", unsafe_allow_html=True)

# Test 3: Complex Structure
st.markdown("### Test 3: Complex HTML Structure")
st.markdown("""
<div style="background: #1e2230; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #0099e0;">
    <h4 style="color: white; margin: 0 0 1rem 0;">Complex Structure Test</h4>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
        <div>
            <div style="color: #a0a0a0; font-size: 0.875rem;">Label 1</div>
            <div style="color: white; font-size: 1.1rem; font-weight: bold;">Value 1</div>
        </div>
        <div>
            <div style="color: #a0a0a0; font-size: 0.875rem;">Label 2</div>
            <div style="color: white; font-size: 1.1rem; font-weight: bold;">Value 2</div>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

# Test 4: Inline JavaScript (Simple)
st.markdown("### Test 4: Simple Inline JavaScript")
st.markdown("""
<div style="background: #6c757d; color: white; padding: 1rem; border-radius: 8px;">
    <button onclick="this.innerHTML='✅ JavaScript Works!'" 
            style="background: white; color: #6c757d; border: none; padding: 0.5rem 1rem; 
                   border-radius: 4px; cursor: pointer; font-weight: bold;">
        Click Me to Test JavaScript
    </button>
</div>
""", unsafe_allow_html=True)

# Test 5: Components.html
st.markdown("### Test 5: Components.html (Creates iframe warning)")
import streamlit.components.v1 as components

html_string = """
<div style="background: #e74c3c; color: white; padding: 1rem; border-radius: 8px;">
    <h4 style="margin: 0;">✅ Components.html Working!</h4>
    <p style="margin: 0.5rem 0 0 0;">This creates the iframe sandbox warning in console.</p>
</div>
"""
components.html(html_string, height=100)

# Console Check Instructions
st.markdown("---")
st.markdown("### 🔍 How to Check Console:")
st.markdown("""
1. **Open Developer Tools**: Press `F12` or right-click → "Inspect"
2. **Go to Console tab**: You'll see the errors mentioned
3. **Check Elements tab**: You can see all HTML is properly rendered
4. **Network tab**: Shows the 404 requests (optional files)

**Important**: These console errors are normal and don't affect functionality!
""")

# Summary
st.success("""
### ✅ Summary:
If you can see all the colored boxes above, then HTML/CSS rendering is working perfectly 
despite the console errors. These errors are standard Streamlit behavior and can be ignored.
""")

# Dark Theme Test
with st.expander("🌙 Test Dark Theme Elements"):
    st.markdown("""
    <div style="background: #0a0a0a; padding: 2rem; border-radius: 8px;">
        <div style="background: #1e2230; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
            <h3 style="color: white; margin: 0;">Dark Theme Panel</h3>
            <p style="color: #e0e0e0; margin: 0.5rem 0 0 0;">Testing dark theme colors</p>
        </div>
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 0.5rem;">
            <div style="background: #0099e0; color: white; padding: 0.5rem; text-align: center; border-radius: 4px;">Blue</div>
            <div style="background: #10dc60; color: white; padding: 0.5rem; text-align: center; border-radius: 4px;">Green</div>
            <div style="background: #ff8c1a; color: white; padding: 0.5rem; text-align: center; border-radius: 4px;">Orange</div>
            <div style="background: #ff5045; color: white; padding: 0.5rem; text-align: center; border-radius: 4px;">Red</div>
        </div>
    </div>
    """, unsafe_allow_html=True)