#!/bin/bash

echo "Reset Alembic Version Table"
echo "This script will recreate the alembic_version table with a clean state"

docker-compose exec backend bash -c "
# Drop and recreate alembic_version table
echo \"Dropping alembic_version table...\"
PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c \"DROP TABLE IF EXISTS alembic_version;\"

echo \"Creating fresh alembic_version table...\"
PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c \"
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL,
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);
\"

# Create an initial version entry
echo \"Setting initial version state...\"
PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c \"
INSERT INTO alembic_version (version_num) VALUES ('0000000000000');
\"

echo \"Alembic version table has been reset to a clean state.\"
echo \"Next step: run 'docker-compose exec backend bash -c \"alembic revision --autogenerate -m initial_migration\"'\"
"

echo "Alembic version table has been reset. Restart the backend container to apply migrations."