#!/bin/bash

echo "Running database migration fix inside Docker container"

# Run the fix inside the backend container
docker-compose exec backend bash -c "
echo \"Fixing Alembic migration in database...\"
# Check if alembic_version table exists and has the problematic revision
if psql -h db -U postgres -d termelo_db -t -c \"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'alembic_version');\" | grep -q t; then
  echo \"Found alembic_version table, checking current version...\"
  current_revision=\$(psql -h db -U postgres -d termelo_db -t -c \"SELECT version_num FROM alembic_version;\")
  echo \"Current revision: \$current_revision\"
  
  # If the revision is d4dcfe073b14 (the missing one), reset it to the last known good revision
  if [[ \"\$current_revision\" == *\"d4dcfe073b14\"* ]]; then
    echo \"Found missing revision in database, resetting to 883694991a72...\"
    psql -h db -U postgres -d termelo_db -c \"UPDATE alembic_version SET version_num = '883694991a72';\"
    echo \"Database alembic version has been updated\"
  fi
else
  echo \"Alembic version table not found\"
fi

# Stamp with a known good revision
echo \"Stamping database with revision 883694991a72...\"
alembic stamp 883694991a72 || true

echo \"Migration state has been fixed. Restart the containers to apply changes.\"
"

echo "Fix completed. Restart containers with: docker-compose down && docker-compose up -d"