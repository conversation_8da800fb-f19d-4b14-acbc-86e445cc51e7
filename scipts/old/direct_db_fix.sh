#!/bin/bash

echo "Direct Database Fix for Alembic Version Table"
echo "This script will access the database container directly to fix the alembic_version table"

# Get the database container ID
DB_CONTAINER=$(docker-compose ps -q db)

if [ -z "$DB_CONTAINER" ]; then
  echo "Error: Database container not found. Is docker-compose running?"
  exit 1
fi

echo "Database container found: $DB_CONTAINER"

# Drop the alembic_version table
echo "Dropping alembic_version table..."
docker exec $DB_CONTAINER psql -U postgres -d termelo_db -c "DROP TABLE IF EXISTS alembic_version;"

# Create a new alembic_version table
echo "Creating fresh alembic_version table..."
docker exec $DB_CONTAINER psql -U postgres -d termelo_db -c "
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL, 
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);"

# Insert initial version
echo "Setting initial version state..."
docker exec $DB_CONTAINER psql -U postgres -d termelo_db -c "
INSERT INTO alembic_version (version_num) VALUES ('0000000000000');"

echo "Alembic version table has been reset to a clean state."
echo "Next step: restart your containers with 'docker-compose down && docker-compose up -d'"