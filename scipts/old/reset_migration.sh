#!/bin/bash

echo "Alembic migration reset script"
echo "This script will reset the Alembic migration state to fix the missing revision issue"

# Check for migrations directory without stopping execution
if [ ! -d "migrations/versions" ]; then
  echo "Warning: migrations/versions directory not found, but continuing..."
fi

# Connect to database and check if the alembic_version table exists
if PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'alembic_version');" | grep -q t; then
  echo "Found alembic_version table, retrieving current version..."
  current_revision=$(PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -t -c "SELECT version_num FROM alembic_version;")
  echo "Current revision: $current_revision"
  
  # If the revision is d4dcfe073b14 (the missing one), reset it to the last known good revision
  if [[ "$current_revision" == *"d4dcfe073b14"* ]]; then
    echo "Found missing revision in database, resetting to 883694991a72..."
    PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c "UPDATE alembic_version SET version_num = '883694991a72';"
    echo "Database alembic version has been updated"
  fi
else
  echo "Alembic version table not found, will be created during migration"
fi

# Look for known revisions in the migration files only if directory exists
if [ -d "migrations/versions" ] && [[ -n $(grep -r "d4dcfe073b14" migrations/versions/ 2>/dev/null) ]]; then
  echo "Found references to missing revision in migration files, fixing..."
  # Update any file that references the missing revision
  find migrations/versions -type f -name "*.py" -exec sed -i 's/d4dcfe073b14/883694991a72/g' {} \; 2>/dev/null || echo "Warning: Could not update migration files, but continuing..."
else
  echo "No references to missing revision found in files or directory not accessible"
fi

echo "Creating a clean migration revision if needed..."

# Stamp with the last known good revision
echo "Stamping database with revision 883694991a72..."
alembic stamp 883694991a72 || true

echo "Migration state has been reset. You can now run 'alembic upgrade head' to apply any pending migrations."