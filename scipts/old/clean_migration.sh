#!/bin/bash

echo "Running clean migration fix inside Docker container"

# Run the fix inside the backend container
docker-compose exec backend bash -c "
echo \"Cleaning Alembic migration state...\"

# Remove the alembic_version table if it exists
echo \"Removing existing alembic_version table if it exists...\"
psql -h db -U postgres -d termelo_db -c \"DROP TABLE IF EXISTS alembic_version;\"

# Clean up migrations directory
echo \"Backing up and recreating migrations directory...\"
if [ -d \"/app/migrations/versions\" ]; then
  mkdir -p /app/migrations/versions_backup
  cp -r /app/migrations/versions/* /app/migrations/versions_backup/ 2>/dev/null || true
  rm -rf /app/migrations/versions/*
fi

# Initialize a fresh alembic environment
echo \"Initializing a fresh alembic environment...\"
alembic init -t generic /tmp/alembic_temp || true
if [ -d \"/tmp/alembic_temp\" ]; then
  cp -r /tmp/alembic_temp/* /app/migrations/ 2>/dev/null || true
fi

# Create a new initial migration
echo \"Creating a new initial migration...\"
alembic revision --autogenerate -m \"fresh_migration\"

# Apply the migration
echo \"Applying the new migration...\"
alembic upgrade head

echo \"Migration state has been cleaned and reset. Check for any errors above.\"
"

echo "Clean migration completed. Verify database state and restart containers if needed."