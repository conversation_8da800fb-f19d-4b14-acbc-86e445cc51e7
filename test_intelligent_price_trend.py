#!/usr/bin/env python3
"""
Test the intelligent price trend analysis features
"""

import sys
import os
sys.path.append('streamlit_app/pages/operator/offer_management')

def test_date_analysis():
    """Test the intelligent date analysis function"""
    
    try:
        from advanced_price_trend_v2 import analyze_offer_dates_for_trend
        
        # Test case 1: Same year (optimal case)
        offer_same_year = {
            'delivery_date': '2025-05-23',
            'created_at': '2025-05-29T15:04:09.758Z'
        }
        
        result1 = analyze_offer_dates_for_trend(offer_same_year)
        print("🧪 Test 1 - Same Year:")
        print(f"   Delivery: {result1['delivery_year']}, Created: {result1['created_year']}")
        print(f"   Status: {result1['analysis']['status']}")
        print(f"   Recommendation: {result1.get('recommended_preset')}")
        print()
        
        # Test case 2: Different years (conflict case)
        offer_different_years = {
            'delivery_date': '2024-05-23',
            'created_at': '2025-05-29T15:04:09.758Z'
        }
        
        result2 = analyze_offer_dates_for_trend(offer_different_years)
        print("🧪 Test 2 - Different Years:")
        print(f"   Delivery: {result2['delivery_year']}, Created: {result2['created_year']}")
        print(f"   Status: {result2['analysis']['status']}")
        print(f"   Has mismatch: {result2['has_date_mismatch']}")
        print(f"   Recommendation: {result2.get('recommended_preset')}")
        print()
        
        # Test case 3: Historical case
        offer_historical = {
            'delivery_date': '2022-08-15',
            'created_at': '2022-08-10T10:30:00.000Z'
        }
        
        result3 = analyze_offer_dates_for_trend(offer_historical)
        print("🧪 Test 3 - Historical:")
        print(f"   Delivery: {result3['delivery_year']}, Created: {result3['created_year']}")
        print(f"   Status: {result3['analysis']['status']}")
        print(f"   Years from now: {result3['years_from_now']}")
        print(f"   Recommendation: {result3.get('recommended_preset')}")
        print()
        
        print("✅ All date analysis tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_preset_dates():
    """Test the smart preset date function"""
    
    try:
        from advanced_price_trend_v2 import get_preset_dates_smart
        from datetime import datetime
        
        # Test standard preset
        start1, end1 = get_preset_dates_smart("Idei év")
        print("🧪 Test Preset - Idei év:")
        print(f"   Start: {start1}, End: {end1}")
        
        # Test with smart suggestion
        suggestion = {
            'delivery_year': 2024,
            'recommended_preset': '2024. év'
        }
        
        start2, end2 = get_preset_dates_smart("2024. év", suggestion)
        print("🧪 Test Preset - Smart suggestion:")
        print(f"   Start: {start2}, End: {end2}")
        
        print("✅ Preset date tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Intelligent Price Trend Analysis")
    print("=" * 50)
    
    all_passed = True
    
    # Test 1: Date analysis
    if not test_date_analysis():
        all_passed = False
    
    print("-" * 30)
    
    # Test 2: Preset dates
    if not test_preset_dates():
        all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print()
        print("✅ Intelligent date analysis works")
        print("✅ Smart preset handling works") 
        print("✅ Ready for Streamlit testing")
        print()
        print("Next steps:")
        print("1. Restart Streamlit: docker-compose restart streamlit")
        print("2. Go to offer detail page")
        print("3. Open 'Fejlett Ártrend Elemzés'")
        print("4. Look for intelligent suggestions")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please fix issues before testing in Streamlit")

if __name__ == "__main__":
    main()