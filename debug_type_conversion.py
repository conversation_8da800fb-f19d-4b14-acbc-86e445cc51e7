#!/usr/bin/env python3
"""
Debug script for type conversion - standalone version
"""

def _to_float(value):
    """Érték biztonságos float konvertálása"""
    try:
        if value is None:
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # Eltávolítjuk a nem numerikus karaktereket
            cleaned = ''.join(c for c in str(value) if c.isdigit() or c in '.-')
            return float(cleaned) if cleaned else 0.0
        return 0.0
    except (ValueError, TypeError):
        return 0.0

def test_conversions():
    """Típuskonverziók tesztelése"""
    print("🧪 Típuskonverziók tesztelése...")
    
    test_cases = [
        (100, 100.0),           # int
        (100.5, 100.5),         # float
        ("100", 100.0),         # string number
        ("100.5", 100.5),       # string float
        ("100kg", 100.0),       # string with unit
        ("", 0.0),              # empty string
        (None, 0.0),            # None
        ("abc", 0.0),           # invalid string
        ("150.75", 150.75),     # decimal string
        (0, 0.0),               # zero
    ]
    
    all_passed = True
    
    for input_val, expected in test_cases:
        result = _to_float(input_val)
        status = "✅" if result == expected else "❌"
        if result != expected:
            all_passed = False
        print(f"{status} {repr(input_val)} -> {result} (várt: {expected})")
    
    print(f"\n{'🎉 Minden teszt sikeres!' if all_passed else '⚠️ Néhány teszt sikertelen!'}")
    
    # Progress calculation test
    print("\n📊 Progress számítás tesztelése...")
    
    offer_data = {
        'quantity_in_kg': "150",  # string
        'confirmed_quantity': "140"  # string
    }
    
    quantity = _to_float(offer_data.get('quantity_in_kg', 0))
    confirmed_quantity = _to_float(offer_data.get('confirmed_quantity', quantity))
    progress = (confirmed_quantity / quantity) if quantity > 0 else 0
    
    print(f"Quantity: {repr(offer_data.get('quantity_in_kg'))} -> {quantity}")
    print(f"Confirmed: {repr(offer_data.get('confirmed_quantity'))} -> {confirmed_quantity}")
    print(f"Progress: {progress} ({progress*100:.1f}%)")
    
    # Test the original error condition
    print(f"\nTeszt: quantity > 0 -> {quantity} > 0 = {quantity > 0}")
    
    return all_passed

if __name__ == "__main__":
    test_conversions()