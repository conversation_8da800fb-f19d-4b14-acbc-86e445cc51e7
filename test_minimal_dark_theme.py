#!/usr/bin/env python3
"""
Test script for Minimal Dark Theme
Minimal dark theme tesztelő script
"""
import streamlit as st
import sys
import os
from datetime import datetime, timedelta

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Minimal Dark Theme Test", 
    layout="wide", 
    initial_sidebar_state="collapsed"
)

st.title("🌙 Minimal Dark Theme - Teszt")

# Test data
test_offer = {
    'id': 2024001,
    'status': 'ACCEPTED_BY_USER',
    'quantity_in_kg': 180,
    'price': 750,
    'confirmed_quantity': 175,
    'confirmed_price': 750,
    'created_at': (datetime.now() - timedelta(days=4)).isoformat(),
    'confirmed_at': (datetime.now() - timedelta(days=3)).isoformat(),
    'accepted_at': (datetime.now() - timedelta(days=1)).isoformat(),
    'delivery_date': (datetime.now() + timedelta(days=3)).isoformat(),
    'note': 'Premium minőségű bio termékek. Frissen szedett, gondos csomagolás.',
    'user': {
        'contact_name': 'Szabó Mária',
        'company_name': 'EcoFarm Bt.',
        'email': '<EMAIL>',
        'phone': '+36 30 555 7890'
    },
    'product_type': {
        'name': 'Bio cukkini',
        'category': {'name': 'Zöldségek'},
        'description': 'Friss, ropogós bio cukkini'
    },
    'quality_parameters': {
        'Méret': 'Közepes (15-20cm)',
        'Színezet': 'Sötétzöld',
        'Tanúsítvány': 'HU-ÖKO-01',
        'Szedés dátuma': '2024-04-26'
    }
}

# Feature comparison
st.info("""
### 🌙 Minimal Dark Theme Jellemzők:
- **Egyszerű kód**: Minimális CSS, natív Streamlit komponensek
- **Gyors betöltés**: Optimalizált teljesítmény
- **Tiszta design**: Közelebb a példaképekhez
- **Responsive**: Automatikus alkalmazkodás
""")

# Main test
try:
    from pages.operator.offer_management.minimal_dark_theme import render_dark_theme_offer
    
    # Render the minimal dark theme
    render_dark_theme_offer(test_offer)
    
    st.success("✅ Minimal Dark Theme sikeresen renderelve!")
    
except Exception as e:
    st.error(f"❌ Hiba a Minimal Dark Theme betöltésekor: {e}")
    st.exception(e)

# Comparison section
st.markdown("---")
with st.expander("📊 Összehasonlítás: Különböző Dark Theme Verziók", expanded=False):
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 🌑 Original Dark Theme
        **Jellemzők:**
        - Komplex CSS animációk
        - Sok különböző komponens
        - HTML renderelés problémák
        - Bonyolult kód struktúra
        
        **Hátrányok:**
        - Lassú betöltés
        - Nehéz karbantartás
        - Kompatibilitási problémák
        """)
    
    with col2:
        st.markdown("""
        ### 🌙 Precise Dark Theme
        **Jellemzők:**
        - Grid alapú layout
        - Színes felső sávok
        - Modern kártya design
        - Enhanced komponensek
        
        **Hátrányok:**
        - Még mindig komplex
        - Sok custom CSS
        - Plotly hibák
        """)
    
    with col3:
        st.markdown("""
        ### ⭐ Minimal Dark Theme
        **Jellemzők:**
        - Natív Streamlit komponensek
        - Minimális CSS
        - Egyszerű kód
        - Gyors és megbízható
        
        **Előnyök:**
        - Könnyű karbantartás
        - Gyors betöltés
        - Kompatibilis
        - Tiszta design
        """)

# Technical details
with st.expander("🔧 Technikai Részletek", expanded=False):
    st.markdown("""
    ### Implementáció részletei:
    
    1. **CSS Optimalizáció**:
       - Csak 50 sor CSS
       - Natív Streamlit osztályok használata
       - Minimális custom stílusok
    
    2. **Komponens Rendszer**:
       - `st.container()` - Kártyák készítéséhez
       - `st.columns()` - Layout szervezés
       - `st.metric()` - Adatok megjelenítése
       - `st.progress()` - Progress bar
    
    3. **Színpaletta**:
       - Háttér: #0a0a0a
       - Kártya: #1a1a1a
       - Border: #2a2a2a
       - Accent: #10dc60, #ff8c1a, #0099e0
    
    4. **Responsivitás**:
       - CSS Grid automatikus adaptáció
       - Streamlit columns rendszer
       - Mobil-optimalizált méretek
    """)

# Footer
st.markdown("---")
st.markdown("""
**🎯 Eredmény**: A Minimal Dark Theme egyszerű, gyors és megbízható megoldás, 
amely pontosan azt nyújtja, amire szükség van - tiszta, modern dark theme-et natív Streamlit komponensekkel.
""")