# ACTUAL.md

## 2025-04-27 – Automatikus értesítés-generálás és notification feed véglegesítése
- Az automatikus értesítés-generálás backend és frontend oldalon is teljeskörű<PERSON> működik. <PERSON><PERSON> a<PERSON>, st<PERSON><PERSON><PERSON><PERSON><PERSON>, be<PERSON><PERSON><PERSON>ításhoz automatikusan generálódik notification, a frontend már csak valós értesítést jelenít meg, mock fallback nincs. Ha nincs <PERSON>, csak üdvözlő értesítés jelenik meg.
- Az "Olvasva" gomb csak integer id-jű értesítéseknél jelenik meg, így Pydantic hiba nem fordulhat elő.
- A display_activity_list-ben a nem int id-jű értesítéseknél magyarázó caption jelenik meg: "<PERSON><PERSON><PERSON>, de nem törölhető (demo)".
- Tesztelve: a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, rendszerüzenet – minden értesítés helyesen keletkezik és megjelenik.


## 2025-04-26 – Ajánlat részletező műveleti gombok státusz- és jogosultság-alapú megjelenítése

- Értesítési rendszer: Backend integrációs tesztek sikeresek, de a frontend továbbra is csak mock (nem valós) notification-t jelenít meg, a valós értesítések nem láthatók. (2025-04-26)


- Elkészült: Minden műveleti gomb csak a megfelelő státuszban és jogosultsággal jelenik meg és hajtja végre a műveletet. Admin/ügyintéző jogosultság kell a visszaigazolás/törléshez, elutasítás bármely szerepkörben CONFIRMED_BY_COMPANY státuszban, elfogadás csak CONFIRMED_BY_COMPANY státuszban. Hibás státuszváltás nem lehetséges. UX és hibakezelés javítva.

## 2025-04-26 – Ajánlat részletező vizuális fejlesztése, UX és hibakezelés

- Elkészült a részletező UI teljes újratervezése: strukturált, sötét témájú kártyák, dinamikus mennyiség indikátor, fejlett státusz kártya.
- Minden gomb egyedi key-t kapott, megszűntek a DuplicateWidgetID hibák.
- Hibakezelés, fallback logika hiányzó adatokhoz.
- Mobilbarát, átlátható, UX fókuszú megoldás, tápértéktáblázat-szerű layout.

## 2025-04-26 – Legutóbbi fejlesztések

- Értesítési feed: automatikus üdvözlő értesítés, ha nincs más. Widget key hash-alapú, olvasottnak jelölés csak int id-re. Statisztika float konverzió. Debug infobox eltávolítva.

## 2025-04-22 – Ajánlatok táblázat Termelő/Termék oszlop hibájának megoldása

### Aktuális állapot összefoglaló

- 2025-04-26: Elkészült a státusz cella dark theme fallback minden státuszhoz (világos mapping esetén is sötét háttér, #23272e).
- 2025-04-26: A natív Streamlit táblázat (st.dataframe) mostantól pandas Styler-rel színezi a státusz oszlopot, így minden platformon stabil és dark theme kompatibilis a megjelenítés.
- AgGrid-es színezés instabilitása miatt a natív táblázat a preferált megoldás.

### Mi volt a probléma?
- A Termelő (user_name) és Termék (product_name) oszlopok üresen jelentek meg az ajánlatok táblázatában, mert a backend válasz nem tartalmazta ezeket a mezőket közvetlenül.
- További hibát okozott, hogy a részletes ajánlat-lekérdezésnél (get_offer) listát adtunk vissza dict helyett, emiatt a frontend nem tudta lekérdezni a részleteket.

### Megoldás
- A backend API válaszait úgy módosítottuk, hogy minden ajánlat-objektumhoz hozzáadjuk a user_name (termelő cégneve) és product_name (termék neve) mezőket, akár listában, akár egyedi ajánlatként jön vissza az adat.
- A get_offer függvény most már mindig dict-et ad vissza, így a részletes nézetben nem lesz többé 'list' object has no attribute 'get' hiba.
- A főtáblázatban csak az alap adatok jelennek meg. Ha a felhasználó rákattint egy sorra, akkor az adott ajánlat részleteit külön panelen/modalban töltjük be, és ezek az adatok addig láthatók, amíg vissza nem lép.

### UX-javaslat
- A fő táblázat gyors marad, mert csak az alap adatok töltődnek be elsőre.
- A részletes adatok csak akkor töltődnek be, amikor a felhasználó ténylegesen igényli (kattintásra), így mindig frissek és teljesek.
- Ez a megközelítés jobb felhasználói élményt, átláthatóságot és teljesítményt biztosít.

---

## 2025-04-22 – Mennyiség és ár tizedesjegy nélküli megjelenítése

### Mi volt a kérés?
- A mennyiség (kg) és ár (Ft) értékek ne tartalmazzanak tizedesjegyet, mindenhol egész számként jelenjenek meg a táblázatban és a részletes nézetben is.

### Megoldás
- A format_number és format_currency helyett közvetlenül f-string konverziót használunk: `f"{{int(float(...))):,}} kg".replace(",", " ")` és `f"{{int(float(...))):,}} Ft".replace(",", " ")`.
- Így mindenhol egész számként, magyaros szóközös ezres tagolással jelennek meg ezek az értékek.
- A módosítás a `display_offer_table` komponensben történt, így a fő táblázatban és részletes nézetben is egységes a formátum.
