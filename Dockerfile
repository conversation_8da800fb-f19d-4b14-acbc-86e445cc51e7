# Hivatalos Python base image
FROM python:3.11-slim

# Munkakörnyezet beállítása
WORKDIR /app

# Ren<PERSON>zer szintű dependenciák telepítése
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Környezeti változók beállítása
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH=/app

# requirements másolása és függőségek telepítése
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Pydantic-settings hozzáadása
RUN pip install --no-cache-dir pydantic-settings

# Projekt fájlok másolása
COPY . .

# Port megnyitása
EXPOSE 8000

# Uvicorn indítása
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
