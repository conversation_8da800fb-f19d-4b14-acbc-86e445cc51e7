# New Project Documentation
# Mezőgazdasági Termékkezel<PERSON> Rendszer (Agricultural Product Management System)

## Table of Contents
1. [Project Charter (Inferred)](#1-project-charter-inferred)
2. [Requirements Document (Extracted)](#2-requirements-document-extracted)
3. [Software Design Document (SDD)](#3-software-design-document-sdd)
4. [Getting Started Guide for New Developers](#4-getting-started-guide-for-new-developers)

---

## 1. Project Charter (Inferred)

### Project Purpose and Business Value

The **Mezőgazdasági Termékkezelő Rendszer** (Agricultural Product Management System) is a comprehensive web-based platform designed to streamline the agricultural supply chain between producers (farmers) and a central purchasing company. The system addresses the critical need for efficient communication, offer management, and quality control in agricultural product procurement.

**Primary Problem Solved:**
- Eliminates manual, paper-based offer submission and tracking processes
- Provides real-time visibility into agricultural product availability and pricing
- Standardizes quality grading and product categorization
- Automates notification and approval workflows
- Enables data-driven decision making through comprehensive reporting

**End Users:**
- **Producers (Termelők):** Farmers and agricultural producers who submit product offers
- **Operators (Ügyintézők):** Company staff who review, confirm, and manage incoming offers
- **Administrators:** System administrators who manage users, products, and system configuration

### Key Features and Functionality

1. **User Management & Authentication**
   - Multi-role user system (Producer, Operator, Administrator)
   - JWT-based authentication with email activation
   - Password reset functionality
   - User profile management with company details

2. **Product Catalog Management**
   - Hierarchical product categorization (Categories → Types → Quality Grades)
   - Detailed quality specifications with dimensional parameters
   - Flexible product type configuration

3. **Offer Management System**
   - Producer offer submission with quantity, delivery date, and quality specifications
   - Multi-stage approval workflow (Created → Confirmed → Accepted/Rejected → Finalized)
   - Real-time offer tracking and status updates
   - Comprehensive offer history and audit trail

4. **Advanced Filtering & Search**
   - Saved filter functionality for frequently used search criteria
   - Complex filtering by date ranges, product types, quantities, and status
   - Full-text search capabilities

5. **Notification System**
   - Automated email notifications for offer status changes
   - In-app notification management
   - Customizable notification preferences

6. **Reporting & Analytics**
   - Offer statistics and trend analysis
   - Calendar-based offer visualization
   - Export functionality for data analysis

7. **Responsive Web Interface**
   - Modern Streamlit-based frontend with mobile responsiveness
   - Role-based dashboard customization
   - Dark theme support and accessibility features

### Scope

**In-Scope:**
- Complete offer lifecycle management from submission to finalization
- Multi-tenant user management with role-based access control
- Product catalog management with quality specifications
- Email-based notification system
- Comprehensive reporting and analytics
- RESTful API for potential third-party integrations
- Docker-based deployment and scaling

**Out-of-Scope (Inferred):**
- Financial transaction processing or payment systems
- Logistics and shipping management
- Inventory management beyond offer quantities
- Mobile native applications (web-responsive only)
- Multi-language support (currently Hungarian-focused)

---

## 2. Requirements Document (Extracted)

### Functional Requirements

#### Authentication & User Management
- **REQ-AUTH-001:** System shall support user registration with email verification
- **REQ-AUTH-002:** System shall provide JWT-based authentication with configurable token expiration
- **REQ-AUTH-003:** System shall support password reset via email tokens
- **REQ-AUTH-004:** System shall maintain three user roles: Producer, Operator, Administrator
- **REQ-AUTH-005:** System shall track user activation status and prevent inactive users from accessing the system

#### Product Management
- **REQ-PROD-001:** System shall support hierarchical product categorization (Category → Type → Quality Grade)
- **REQ-PROD-002:** System shall allow configuration of quality grades with dimensional specifications
- **REQ-PROD-003:** System shall support products with and without quality grade requirements
- **REQ-PROD-004:** System shall maintain product type descriptions and metadata

#### Offer Management
- **REQ-OFFER-001:** System shall allow producers to create offers with quantity, delivery date, and product specifications
- **REQ-OFFER-002:** System shall implement a five-stage offer workflow: CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
- **REQ-OFFER-003:** System shall validate offer data (positive quantities, valid dates, existing products)
- **REQ-OFFER-004:** System shall maintain complete audit trail of offer status changes
- **REQ-OFFER-005:** System shall support offer filtering by multiple criteria (date, status, product, user)
- **REQ-OFFER-006:** System shall provide calendar view of offers by delivery date

#### Notification System
- **REQ-NOTIF-001:** System shall send email notifications for offer status changes
- **REQ-NOTIF-002:** System shall maintain in-app notification history
- **REQ-NOTIF-003:** System shall support notification read/unread status tracking

#### Saved Filters
- **REQ-FILTER-001:** System shall allow users to save frequently used filter combinations
- **REQ-FILTER-002:** System shall support sharing saved filters between users
- **REQ-FILTER-003:** System shall maintain user-specific default filter preferences

#### API Requirements
- **REQ-API-001:** System shall provide RESTful API endpoints for all major operations
- **REQ-API-002:** System shall support pagination for large data sets
- **REQ-API-003:** System shall provide comprehensive API documentation
- **REQ-API-004:** System shall implement proper HTTP status codes and error handling

### Non-Functional Requirements

#### Security
- **REQ-SEC-001:** System shall use bcrypt for password hashing with salt
- **REQ-SEC-002:** System shall implement JWT tokens with configurable expiration (default 30 minutes)
- **REQ-SEC-003:** System shall validate all input data to prevent SQL injection and XSS attacks
- **REQ-SEC-004:** System shall use HTTPS for all communications (configured via reverse proxy)
- **REQ-SEC-005:** System shall implement CORS protection with configurable origins

#### Performance
- **REQ-PERF-001:** System shall support concurrent users with response times under 2 seconds for standard operations
- **REQ-PERF-002:** System shall implement database connection pooling for efficient resource usage
- **REQ-PERF-003:** System shall support pagination to handle large datasets efficiently

#### Scalability
- **REQ-SCALE-001:** System shall be containerized using Docker for horizontal scaling
- **REQ-SCALE-002:** System shall use PostgreSQL database for ACID compliance and scalability
- **REQ-SCALE-003:** System shall separate frontend and backend for independent scaling

#### Maintainability
- **REQ-MAINT-001:** System shall use SQLAlchemy ORM with Alembic migrations for database schema management
- **REQ-MAINT-002:** System shall implement structured logging with JSON format for monitoring
- **REQ-MAINT-003:** System shall include comprehensive test suite with pytest
- **REQ-MAINT-004:** System shall follow clean architecture principles with separated concerns

#### Monitoring & Debugging
- **REQ-MON-001:** System shall provide structured logging with configurable log levels
- **REQ-MON-002:** System shall include debug tools for container and log analysis
- **REQ-MON-003:** System shall support health check endpoints for monitoring

#### Email Integration
- **REQ-EMAIL-001:** System shall support SMTP configuration for email notifications
- **REQ-EMAIL-002:** System shall use HTML email templates for professional communication
- **REQ-EMAIL-003:** System shall handle email delivery failures gracefully

---

## 3. Software Design Document (SDD)

### System Architecture Overview

The Mezőgazdasági Termékkezelő Rendszer follows a modern **three-tier client-server architecture** with clear separation of concerns:

```mermaid
graph TB
    subgraph "Client Tier"
        UI[Streamlit Frontend<br/>Port 8501]
    end
    
    subgraph "Application Tier"
        API[FastAPI Backend<br/>Port 8000]
        subgraph "API Layers"
            ENDPOINTS[API Endpoints]
            SERVICES[Business Services]
            CRUD[Data Access Layer]
        end
    end
    
    subgraph "Data Tier"
        DB[(PostgreSQL Database<br/>Port 5432)]
    end
    
    subgraph "Infrastructure"
        DOCKER[Docker Containers]
        DEBUG[Debug Tools]
        EMAIL[SMTP Service]
    end
    
    UI -->|HTTP/REST| API
    API --> DB
    DOCKER --> UI
    DOCKER --> API
    DOCKER --> DB
    API --> EMAIL
    DEBUG --> DOCKER
```

#### Component Responsibilities

**Frontend (Streamlit)**
- User interface rendering and interaction
- Session management and authentication state
- API communication and data presentation
- Role-based page routing and access control

**Backend (FastAPI)**
- Business logic implementation
- API endpoint management
- Authentication and authorization
- Data validation and processing
- Email notification handling

**Database (PostgreSQL)**
- Persistent data storage
- ACID transaction support
- Relational data integrity
- Query optimization

**Infrastructure**
- Docker containerization for deployment
- Debug tools for monitoring and troubleshooting
- SMTP integration for email services

### Technology Stack and Dependencies

#### Core Technologies
- **Backend Framework:** FastAPI 0.104.1
- **Frontend Framework:** Streamlit 1.32.0
- **Database:** PostgreSQL 15
- **ORM:** SQLAlchemy 2.0.22
- **Migration Tool:** Alembic 1.12.1
- **Authentication:** JWT with python-jose 3.3.0
- **Password Hashing:** bcrypt 4.0.1 with passlib 1.7.4

#### Key Dependencies

**Backend Dependencies:**
```
fastapi==0.104.1          # Web framework
uvicorn==0.23.2           # ASGI server
sqlalchemy==2.0.22        # ORM
alembic==1.12.1           # Database migrations
pydantic>=2.0.0           # Data validation
psycopg2-binary==2.9.9    # PostgreSQL adapter
python-jose==3.3.0        # JWT handling
passlib==1.7.4            # Password hashing
bcrypt==4.0.1             # Encryption
email-validator==2.1.0.post1  # Email validation
aiosmtplib==3.0.1         # Async SMTP client
```

**Frontend Dependencies:**
```
streamlit==1.32.0         # Web UI framework
pandas==2.2.0             # Data manipulation
plotly==5.17.0            # Interactive charts
httpx==0.25.0             # HTTP client
```

**Development & Testing:**
```
pytest==7.4.3            # Testing framework
pytest-cov==4.1.0        # Coverage reporting
python-dotenv==1.0.0     # Environment management
```

**Infrastructure:**
```
Docker & Docker Compose   # Containerization
PostgreSQL 15             # Database server
```

### Data Design

#### Database Schema

The system uses a relational database design with the following core entities:

```mermaid
erDiagram
    User ||--o{ Offer : creates
    User ||--o{ OfferLog : logs
    User ||--o{ UserSavedFilter : saves
    User ||--o| UserDefaultSettings : has
    User ||--o{ PasswordResetToken : requests
    User ||--o{ Notification : receives
    
    ProductCategory ||--o{ ProductType : contains
    ProductType ||--o{ QualityGrade : has
    ProductType ||--o{ Offer : specifies
    QualityGrade ||--o{ Offer : grades
    
    Offer ||--o{ OfferLog : tracks
    
    User {
        int id PK
        string email UK
        string password_hash
        string role
        string company_name
        string tax_id
        string contact_name
        string phone_number
        boolean is_active
        string activation_token
        datetime created_at
        datetime updated_at
    }
    
    Offer {
        int id PK
        int user_id FK
        int product_type_id FK
        int quality_grade_id FK
        decimal quantity_in_kg
        date delivery_date
        string status
        decimal confirmed_quantity
        decimal confirmed_price
        text note
        int created_by_user_id FK
        datetime created_at
        datetime updated_at
    }
    
    ProductCategory {
        int id PK
        string name UK
        text description
        datetime created_at
        datetime updated_at
    }
    
    ProductType {
        int id PK
        int category_id FK
        string name
        text description
        boolean has_quality_grades
        datetime created_at
        datetime updated_at
    }
    
    QualityGrade {
        int id PK
        int product_type_id FK
        string name
        decimal min_shoulder_diameter
        decimal max_shoulder_diameter
        decimal min_length
        decimal max_length
        text description
        datetime created_at
        datetime updated_at
    }
```

#### Key Data Relationships

1. **User-Offer Relationship:** Many-to-many through different roles (creator vs. owner)
2. **Product Hierarchy:** Category → Type → Quality Grade (optional)
3. **Offer Workflow:** Tracked through OfferLog entries
4. **User Preferences:** Stored in UserDefaultSettings and UserSavedFilter

#### Data Access Patterns

- **Read-Heavy Operations:** Offer listing, filtering, and search
- **Write Operations:** Offer creation, status updates, user registration
- **Audit Trail:** All offer changes logged in OfferLog table
- **Soft Deletes:** Users can be deactivated rather than deleted

### API and Interface Specification

#### API Architecture

The API follows RESTful principles with the following structure:

```
/api/
├── auth/                 # Authentication endpoints
├── users/                # User management
├── products/             # Product catalog
├── offers/               # Offer management
├── admin/                # Administrative functions
├── notifications/        # Notification system
├── saved-filters/        # Saved filter management
└── ai/                   # AI compatibility layer
```

#### Core API Endpoints

**Authentication Endpoints:**
```
POST   /api/auth/register              # User registration
POST   /api/auth/login                 # User login
GET    /api/auth/activate-account      # Account activation
POST   /api/auth/password-reset-request # Password reset request
POST   /api/auth/password-reset-confirm # Password reset confirmation
GET    /api/auth/me                    # Current user info
```

**Offer Management Endpoints:**
```
GET    /api/offers                     # List offers (with filtering)
POST   /api/offers                     # Create new offer
GET    /api/offers/{id}                # Get offer details
PUT    /api/offers/{id}                # Update offer
DELETE /api/offers/{id}                # Delete offer
POST   /api/offers/{id}/confirm        # Confirm offer (operator)
POST   /api/offers/{id}/accept         # Accept offer (producer)
POST   /api/offers/{id}/reject         # Reject offer (producer)
POST   /api/offers/{id}/finalize       # Finalize offer (operator)
GET    /api/offers/{id}/logs           # Get offer history
GET    /api/offers/calendar            # Calendar view
GET    /api/offers/statistics          # Offer statistics
```

**Product Management Endpoints:**
```
GET    /api/products/categories        # List categories
GET    /api/products/types             # List product types
GET    /api/products/types/{id}        # Get product type details
GET    /api/products/quality-grades    # List quality grades
GET    /api/products/quality-grades/{id} # Get quality grade details
```

**User Management Endpoints:**
```
GET    /api/users                      # List users (admin)
GET    /api/users/{id}                 # Get user details
PUT    /api/users/{id}                 # Update user
POST   /api/users/{id}/change-password # Change password
GET    /api/users/{id}/default-settings # Get user defaults
PUT    /api/users/{id}/default-settings # Update user defaults
```

#### Request/Response Format

**Standard Response Format:**
```json
{
  "data": { ... },           // Response payload
  "message": "Success",      // Human-readable message
  "status": "success",       // Status indicator
  "timestamp": "2023-09-15T10:30:00Z"
}
```

**Error Response Format:**
```json
{
  "detail": "Error description",
  "status_code": 400,
  "timestamp": "2023-09-15T10:30:00Z"
}
```

**Pagination Format:**
```json
{
  "items": [...],            // Data items
  "total": 150,              // Total count
  "page": 1,                 // Current page
  "size": 25,                // Page size
  "pages": 6                 // Total pages
}
```

#### Authentication & Authorization

- **Authentication Method:** JWT Bearer tokens
- **Token Expiration:** 30 minutes (configurable)
- **Authorization Levels:**
  - **Public:** Registration, login, password reset
  - **Authenticated:** Basic user operations
  - **Role-Based:** Producer, Operator, Administrator specific endpoints
- **Security Headers:** CORS, Content-Type validation

---

## 4. Getting Started Guide for New Developers

### Prerequisites

Before setting up the project locally, ensure you have the following software installed:

**Required Software:**
- **Docker** (version 20.0 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Git** (for version control)
- **Python 3.11+** (for local development)
- **PostgreSQL Client Tools** (optional, for database inspection)

**Development Tools (Recommended):**
- **VS Code** or **PyCharm** (IDE)
- **Postman** or **Insomnia** (API testing)
- **pgAdmin** or **DBeaver** (database management)

### Installation Steps

#### 1. Clone the Repository

```bash
git clone https://github.com/your-org/termelo_v4.git
cd termelo_v4
```

#### 2. Environment Configuration

Create environment configuration files:

```bash
# Copy example environment file (if available)
cp .env.example .env

# Or create .env file with required variables
cat > .env << EOF
# Application Settings
APP_NAME=Mezőgazdasági Termékkezelő Rendszer
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DB_HOST=db
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=termelo_db
DATABASE_URL=**************************************/termelo_db

# Security Settings
SECRET_KEY=your-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# SMTP Configuration (configure with your SMTP server)
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_TLS=true
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Your Company Name

# Frontend Configuration
STREAMLIT_HOST=localhost
STREAMLIT_PORT=8501
API_HOST=http://backend:8000
API_BASE_URL=http://backend:8000/api
EOF
```

#### 3. Build and Start the Application

```bash
# Build and start all services
docker-compose up -d

# View logs to ensure services are starting correctly
docker-compose logs -f
```

#### 4. Initialize the Database

```bash
# Make the initialization script executable
chmod +x ./init-db.sh

# Run database initialization
./init-db.sh
```

#### 5. Verify Installation

Check that all services are running:

```bash
# Check service status
docker-compose ps

# Test backend API
curl http://localhost:8000/

# Test frontend (open in browser)
# http://localhost:8501
```

### Configuration

#### Database Configuration

The application uses PostgreSQL with the following default settings:
- **Host:** localhost (db in Docker)
- **Port:** 5433 (mapped from container port 5432)
- **Database:** termelo_db
- **Username:** postgres
- **Password:** postgres

#### Email Configuration

Configure SMTP settings in the `.env` file or environment variables:

```bash
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_TLS=true
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Your Company Name
```

#### API Configuration

The backend API runs on port 8000 with the following endpoints:
- **API Documentation:** http://localhost:8000/docs
- **API Base URL:** http://localhost:8000/api
- **Health Check:** http://localhost:8000/

#### Frontend Configuration

The Streamlit frontend runs on port 8501:
- **Application URL:** http://localhost:8501
- **API Connection:** Configured via `API_BASE_URL` environment variable

### Running the Application

#### Development Mode

For development with hot reloading:

```bash
# Start all services in development mode
docker-compose up

# Or start individual services
docker-compose up backend    # Backend only
docker-compose up streamlit  # Frontend only
docker-compose up db         # Database only
```

#### Production Mode

For production deployment:

```bash
# Start services in detached mode
docker-compose up -d

# View logs
docker-compose logs -f backend
docker-compose logs -f streamlit
```

#### Container Management

Use the provided management script:

```bash
# Start all containers
./manage_containers.sh start

# Stop all containers
./manage_containers.sh stop

# Restart all containers
./manage_containers.sh restart

# View container status
./manage_containers.sh status
```

### Running Tests

#### Backend Tests

```bash
# Run all backend tests
docker-compose exec backend pytest

# Run tests with coverage
docker-compose exec backend pytest --cov=app --cov-report=html

# Run specific test file
docker-compose exec backend pytest tests/test_offers_api.py

# Run tests with verbose output
docker-compose exec backend pytest -v
```

#### Frontend Tests

```bash
# Run frontend component tests
docker-compose exec backend python tests/test_entrypoint.py --test tests/components/test_sidebar.py

# Interactive test selection
docker-compose exec backend python tests/test_entrypoint.py
```

#### Integration Tests

```bash
# Run integration tests
docker-compose exec backend pytest tests/integration/

# Run notification flow tests
docker-compose exec backend pytest tests/integration/test_notification_flow.py
```

### Development Workflow

#### Database Migrations

```bash
# Create a new migration
docker-compose exec backend alembic revision --autogenerate -m "Description of changes"

# Apply migrations
docker-compose exec backend alembic upgrade head

# View migration history
docker-compose exec backend alembic history

# Rollback migration
docker-compose exec backend alembic downgrade -1
```

#### Debug Tools

The project includes comprehensive debugging tools:

```bash
# Start debug menu
./debug_menu.sh

# Or use debug CLI directly
docker-compose exec debug python -m debug.cli logs backend
docker-compose exec debug python -m debug.cli analyze streamlit
docker-compose exec debug python -m debug.cli live db
```

#### Code Quality

```bash
# Format code with black (if configured)
docker-compose exec backend black app/

# Run linting (if configured)
docker-compose exec backend flake8 app/

# Type checking (if configured)
docker-compose exec backend mypy app/
```

### Troubleshooting

#### Common Issues

**Database Connection Issues:**
```bash
# Check database status
docker-compose exec db pg_isready -U postgres

# Reset database
docker-compose down -v
docker-compose up -d db
./init-db.sh
```

**Port Conflicts:**
```bash
# Check port usage
netstat -tulpn | grep :8000
netstat -tulpn | grep :8501
netstat -tulpn | grep :5433

# Modify ports in docker-compose.yml if needed
```

**Container Issues:**
```bash
# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# View detailed logs
docker-compose logs --tail=100 backend
docker-compose logs --tail=100 streamlit
```

#### Getting Help

1. **Documentation:** Check the `docs/` directory for detailed documentation
2. **API Documentation:** Visit http://localhost:8000/docs for interactive API documentation
3. **Debug Tools:** Use the built-in debug system for troubleshooting
4. **Logs:** Check container logs for error messages and debugging information

### Next Steps

After successful installation:

1. **Create Admin User:** Use the API or admin interface to create your first admin user
2. **Configure Products:** Set up product categories, types, and quality grades
3. **Test Workflow:** Create test offers and walk through the complete workflow
4. **Customize Configuration:** Adjust settings for your specific requirements
5. **Set Up Monitoring:** Configure logging and monitoring for production use

For detailed information about specific components, refer to the comprehensive documentation in the `docs/` directory.