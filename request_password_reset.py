#!/usr/bin/env python3
"""
Password Reset Request Script
Sends a password reset request for a specific email address
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"  # Adjust if running on different host/port
EMAIL = "racz.joz<PERSON>@frissenszeretem.hu"

def send_password_reset_request():
    """Send password reset request to the API"""
    
    endpoint = f"{API_BASE_URL}/auth/password-reset-request"
    
    # Prepare the request data
    request_data = {
        "email": EMAIL
    }
    
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Password Reset Request")
    print("=" * 60)
    print(f"Target Email: {EMAIL}")
    print(f"API Endpoint: {endpoint}")
    print("=" * 60)
    
    try:
        # Send the POST request
        print("\nSending password reset request...")
        response = requests.post(
            endpoint,
            json=request_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=30
        )
        
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        # Check if request was successful
        if response.status_code == 200:
            print("\n✅ SUCCESS: Password reset request sent successfully!")
            
            # Parse response
            try:
                response_data = response.json()
                print(f"\nServer Response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                if "message" in response_data:
                    print(f"\nMessage: {response_data['message']}")
            except json.JSONDecodeError:
                print(f"\nRaw Response: {response.text}")
                
            print("\n📧 Next Steps:")
            print("1. Check the email <NAME_EMAIL>")
            print("2. Look for a password reset email")
            print("3. Click the reset link in the email")
            print("4. Enter a new password on the reset page")
            
        else:
            print(f"\n❌ ERROR: Request failed with status code {response.status_code}")
            
            # Try to parse error response
            try:
                error_data = response.json()
                print(f"\nError Details: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                
                if "detail" in error_data:
                    print(f"\nError Message: {error_data['detail']}")
            except json.JSONDecodeError:
                print(f"\nRaw Error Response: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("\n❌ ERROR: Could not connect to the API server")
        print(f"Please ensure the API is running at {API_BASE_URL}")
        print("\nTroubleshooting:")
        print("1. Check if the backend container is running: docker ps")
        print("2. Verify the API URL and port")
        print("3. Check if you're inside the Docker network or need to use localhost")
        return False
        
    except requests.exceptions.Timeout:
        print("\n❌ ERROR: Request timed out after 30 seconds")
        print("The server might be overloaded or not responding")
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"\n❌ ERROR: Request failed - {type(e).__name__}: {str(e)}")
        return False
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {type(e).__name__}: {str(e)}")
        return False
        
    print("\n" + "=" * 60)
    print("Password reset request completed")
    print("=" * 60)
    
    return response.status_code == 200

def main():
    """Main function"""
    print("\n🔐 Password Reset Request Script")
    print("This script will send a password reset request for:")
    print(f"   Email: {EMAIL}")
    print()
    
    # Confirm before sending
    confirm = input("Do you want to proceed? (yes/no): ").lower().strip()
    
    if confirm not in ['yes', 'y']:
        print("\nOperation cancelled.")
        sys.exit(0)
    
    # Send the request
    success = send_password_reset_request()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()