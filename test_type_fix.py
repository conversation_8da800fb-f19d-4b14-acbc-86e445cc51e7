#!/usr/bin/env python3
"""
Test script for type conversion fixes
Típuskonverzió javítások tesztelése
"""
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

from pages.operator.offer_management.minimal_dark_theme import _to_float, _format_quantity, _format_price

def test_type_conversions():
    """Típuskonverziók tesztelése"""
    print("🧪 Típuskonverziók tesztelése...")
    
    # Test cases
    test_cases = [
        (100, 100.0),           # int
        (100.5, 100.5),         # float
        ("100", 100.0),         # string number
        ("100.5", 100.5),       # string float
        ("100kg", 100.0),       # string with unit
        ("", 0.0),              # empty string
        (None, 0.0),            # None
        ("abc", 0.0),           # invalid string
    ]
    
    print("\n_to_float() tesztek:")
    for input_val, expected in test_cases:
        result = _to_float(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} {input_val} -> {result} (várt: {expected})")
    
    print("\n_format_quantity() tesztek:")
    quantity_tests = [100, "150", "200.5", None, "abc"]
    for val in quantity_tests:
        result = _format_quantity(val)
        print(f"✅ {val} -> '{result}'")
    
    print("\n_format_price() tesztek:")
    price_tests = [850, "750", "1200.5", None, "abc"]
    for val in price_tests:
        result = _format_price(val)
        print(f"✅ {val} -> '{result}'")
    
    print("\n🎉 Minden teszt sikeres!")

def test_progress_calculation():
    """Progress számítás tesztelése"""
    print("\n📊 Progress számítás tesztelése...")
    
    test_offers = [
        {"quantity_in_kg": 100, "confirmed_quantity": 95},
        {"quantity_in_kg": "150", "confirmed_quantity": "140"},
        {"quantity_in_kg": "200.5", "confirmed_quantity": 200},
        {"quantity_in_kg": 0, "confirmed_quantity": 0},
        {"quantity_in_kg": None, "confirmed_quantity": None},
    ]
    
    for i, offer in enumerate(test_offers):
        quantity = _to_float(offer.get('quantity_in_kg', 0))
        confirmed_quantity = _to_float(offer.get('confirmed_quantity', quantity))
        progress = (confirmed_quantity / quantity) if quantity > 0 else 0
        
        print(f"✅ Test {i+1}: {offer} -> Progress: {progress*100:.1f}%")
    
    print("🎉 Progress számítások sikeresek!")

if __name__ == "__main__":
    test_type_conversions()
    test_progress_calculation()
    print("\n🌟 Minden teszt sikeresen lefutott!")