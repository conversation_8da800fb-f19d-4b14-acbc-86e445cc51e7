# Offer Management System - Technical Documentation

## Table of Contents
- [Executive Summary](#executive-summary)
- [System Architecture](#system-architecture)
- [Directory Structure](#directory-structure)
- [Core Components](#core-components)
- [Module Documentation](#module-documentation)
- [API Reference](#api-reference)
- [Configuration and Setup](#configuration-and-setup)
- [Dependencies](#dependencies)
- [Usage Examples](#usage-examples)
- [Development Guidelines](#development-guidelines)
- [Troubleshooting](#troubleshooting)
- [Appendices](#appendices)

## Executive Summary

The Offer Management System is a comprehensive web-based application module designed for agricultural product trading operations. Built using Streamlit framework, it provides operators with sophisticated tools to manage, process, and analyze product offers from producers.

### Key Features
- **Multi-interface Support**: Modern React-like components and traditional table views
- **Advanced Filtering**: Multi-criteria search, saved filters, and user preferences
- **Responsive Design**: Mobile, tablet, and desktop optimized layouts
- **Real-time Processing**: Dynamic data loading with progressive pagination
- **Status Management**: Comprehensive offer lifecycle tracking
- **Export Capabilities**: CSV/Excel data export functionality
- **Statistical Analysis**: Interactive charts and metrics dashboards

### Target Audience
- **Primary Users**: Agricultural product operators and administrators
- **Secondary Users**: System developers and maintainers
- **Technical Stakeholders**: DevOps engineers and system architects

## System Architecture

### High-Level Architecture

The Offer Management System follows a modular, layered architecture pattern:

```
┌─────────────────────────────────────────────────────────────┐
│                    User Interface Layer                     │
├─────────────────────────────────────────────────────────────┤
│ • Modern Components (React-like)                            │
│ • Traditional UI Components                                 │
│ • Responsive Layout Management                              │
│ • Progressive Loading                                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Business Logic Layer                        │
├─────────────────────────────────────────────────────────────┤
│ • Offer Processing & Validation                             │
│ • Status Transition Management                              │
│ • Filter & Search Logic                                     │
│ • Statistical Analysis                                      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Data Access Layer                           │
├─────────────────────────────────────────────────────────────┤
│ • API Client Services                                       │
│ • Caching Mechanisms                                        │
│ • State Management                                          │
│ • Session Persistence                                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
├─────────────────────────────────────────────────────────────┤
│ • RESTful API Backend                                       │
│ • Database Persistence                                      │
│ • Authentication & Authorization                            │
│ • Configuration Management                                  │
└─────────────────────────────────────────────────────────────┘
```

### Design Patterns Used

1. **Modular Component Architecture**: Separated concerns with specialized modules
2. **Flexible Import Strategy**: Multiple import paths with fallback mechanisms
3. **State Management Pattern**: Centralized session state with caching
4. **API Wrapper Pattern**: Consistent error handling and response formatting
5. **Progressive Enhancement**: Basic functionality with advanced feature overlays
6. **Responsive Design Pattern**: Device-aware layout adaptation

## Directory Structure

```
streamlit_app/pages/operator/offer_management/
├── __init__.py                      # Package initialization
├── offer_management.py              # Main entry point (parent directory)
│
├── Core API Layer/
│   ├── api_client.py               # Primary API interface
│   └── api_helpers.py              # API utility functions
│
├── Business Logic Layer/
│   ├── actions.py                  # Offer action handlers
│   ├── data_processing.py          # Data transformation
│   ├── validation.py               # Input validation
│   └── utils.py                    # General utilities
│
├── UI Components/
│   ├── ui_components.py            # Traditional UI elements
│   ├── modern_components.py        # React-like components
│   ├── enhanced_ui_components.py   # Advanced UI widgets
│   ├── responsive_ui.py            # Mobile/tablet adaptation
│   └── display_mobile_offer_card.py # Mobile-specific cards
│
├── Data & State Management/
│   ├── state_management.py         # Session state handling
│   ├── state_hooks.py              # React-like state hooks
│   └── user_preferences.py         # User settings persistence
│
├── Filtering & Search/
│   ├── advanced_filters.py         # Complex filter logic
│   ├── advanced_sorting.py         # Multi-level sorting
│   ├── column_filters.py           # Excel-like filtering
│   ├── complex_filters.py          # Boolean search operations
│   ├── fuzzy_search.py             # Approximate matching
│   ├── fulltext_search.py          # Full-text search
│   ├── saved_filter_ui.py          # Saved filter management
│   └── filter_persistence.py       # Filter state persistence
│
├── Specialized Features/
│   ├── offer_detail.py             # Detailed offer view
│   ├── statistics_overview.py      # Analytics dashboard
│   ├── export_functions.py         # Data export utilities
│   ├── suggestions.py              # Context-aware suggestions
│   └── progressive_loading.py      # Paginated data loading
│
├── Supporting Components/
│   ├── confirmation_dialog.py      # Modal confirmations
│   ├── producer_autocomplete.py    # Producer search widget
│   ├── customizable_table.py       # Flexible data tables
│   ├── status_visualization.py     # Status indicators
│   └── visual_feedback.py          # User feedback systems
│
├── Testing & Development/
│   ├── test_*.py                   # Unit tests
│   ├── component_demo.py           # Component demonstrations
│   └── README_*.md                 # Development documentation
│
└── Infrastructure/
    ├── component_base.py           # Base component classes
    ├── react_like_components.py    # React-pattern implementation
    ├── custom_css_framework.py     # Styling framework
    └── html_rendering.py           # Custom HTML rendering
```

## Core Components

### 1. Main Application Entry Point

**File**: `offer_management.py` (parent directory)

The main application controller that orchestrates the entire offer management system.

#### Key Classes:

##### `ModernOfferManager`
```python
class ModernOfferManager:
    """Modern offer management central controller"""
    
    def __init__(self):
        self.initialize_session_state()
        self.setup_css()
    
    def render(self):
        """Main rendering method"""
        # Coordinate all UI components
```

**Responsibilities**:
- Session state initialization and management
- CSS styling injection and theme management
- Component coordination and rendering orchestration
- Filter state management and persistence
- API integration and error handling

### 2. API Client Layer

**File**: `api_client.py`

Provides abstracted interface to backend REST API with comprehensive error handling.

#### Key Functions:

```python
def get_offers(query_params=None) -> tuple[bool, Any]:
    """Retrieve offers with filtering parameters"""

def get_producers() -> tuple[bool, List[Dict]]:
    """Fetch all producer information"""

def update_offer_status(offer_id: str, status: str, data=None) -> tuple[bool, Any]:
    """Update offer status with transition validation"""

def delete_offer(offer_id: str) -> tuple[bool, str]:
    """Remove offer with confirmation"""
```

**Key Features**:
- Flexible import strategy with multiple fallback paths
- Consistent error handling and response formatting
- Docker and development environment compatibility
- Safe API call wrapper with timeout and retry logic

### 3. UI Component System

#### Traditional Components (`ui_components.py`)
```python
def render_offer_filters(page_id: str) -> tuple:
    """Render filtering interface with producer, status, and date filters"""

def render_active_filters_indicator():
    """Display currently active filter summary"""

def display_offer_table_with_actions(offers: List[Dict]):
    """Traditional tabular display with action buttons"""
```

#### Modern Components (`modern_components.py`)
```python
class StatefulFilterPanel(BaseComponent):
    """State-aware filter panel with React-like behavior"""
    
class OfferStatsCard(BaseComponent):
    """Interactive statistics display card"""

class ModernButton(BaseComponent):
    """Enhanced button with animations and state"""
```

### 4. State Management System

**File**: `state_management.py`

Centralized state management with caching and persistence.

```python
def init_page_state():
    """Initialize session state variables and caches"""

def inject_keyboard_shortcuts():
    """Add keyboard navigation support"""

def get_producer_details(producer_id: int) -> Dict:
    """Cached producer information retrieval"""
```

**State Categories**:
- **UI State**: Screen dimensions, view modes, selected items
- **Data Caches**: Offers, producers, products, API responses
- **User Preferences**: Saved filters, display settings
- **Navigation State**: Current page, selected views, breadcrumbs

## Module Documentation

### API Client Module (`api_client.py`)

#### Purpose
Provides unified interface to backend REST API with consistent error handling and response formatting.

#### Dependencies
- `streamlit` - Web framework
- `logging` - Error tracking and debugging
- `datetime` - Date/time handling for API parameters

#### Key Functions

##### `get_offers(query_params=None)`
Retrieves offers from the backend with optional filtering.

**Parameters:**
- `query_params` (dict, optional): Filter parameters including:
  - `status`: Offer status filter
  - `user_id`: Producer ID filter
  - `date_from`/`date_to`: Date range filters
  - `page`/`limit`: Pagination parameters

**Returns:**
- `tuple`: (success: bool, result: List[Dict] | str)

**Usage Example:**
```python
params = {
    'status': 'CREATED',
    'date_from': '2024-01-01',
    'page': 1,
    'limit': 20
}
success, offers = get_offers(params)
if success:
    # Process offers
    for offer in offers:
        print(f"Offer {offer['id']}: {offer['status']}")
```

##### `update_offer_status(offer_id, status, confirmation_data=None)`
Updates offer status with validation and confirmation data.

**Parameters:**
- `offer_id` (int/str): Unique offer identifier
- `status` (str): New status value
- `confirmation_data` (dict, optional): Additional data for status transition

**Status Workflow:**
```
CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
```

**Returns:**
- `tuple`: (success: bool, result: Dict | str)

### Data Processing Module (`data_processing.py`)

#### Purpose
Handles data transformation, validation, and preparation for display components.

#### Key Functions

##### `prepare_filter_params(producer_id, status, from_date, to_date)`
Converts UI filter values into API-compatible parameters.

**Parameters:**
- `producer_id` (int): Selected producer ID
- `status` (str): Selected status filter
- `from_date` (date): Start date for filtering
- `to_date` (date): End date for filtering

**Returns:**
- `dict`: API parameter dictionary

##### `load_offers_with_filters(query_params)`
Loads and processes offers with applied filters and caching.

**Features:**
- Automatic pagination handling
- Producer data enrichment
- Cache management with TTL
- Error recovery and fallback data

### UI Components Module (`ui_components.py`)

#### Purpose
Traditional UI components for the offer management interface.

#### Key Components

##### Filter Components
```python
def render_producer_filter(page_id: str) -> Optional[int]:
    """Producer selection with autocomplete functionality"""

def render_status_filter(page_id: str) -> Optional[str]:
    """Status multi-select with predefined options"""

def render_date_filter(page_id: str) -> tuple[date, date]:
    """Date range picker with validation"""
```

##### Display Components
```python
def display_offer_table_with_actions(offers: List[Dict]):
    """Traditional table view with inline actions"""

def render_active_filters_indicator():
    """Visual indicator of currently applied filters"""
```

### Modern Components Module (`modern_components.py`)

#### Purpose
React-like components with state management and advanced interactivity.

#### Architecture

##### Component Base Class
```python
class BaseComponent:
    """Base class for stateful components"""
    
    def __init__(self, component_id: str):
        self.component_id = component_id
        self.props = {}
    
    def use_state(self, initial_value, key: str):
        """React-like state hook"""
        state_key = f"{self.component_id}_{key}"
        if state_key not in st.session_state:
            st.session_state[state_key] = initial_value
        
        def setter(new_value):
            st.session_state[state_key] = new_value
            
        return st.session_state[state_key], setter
```

##### Specialized Components

###### `StatefulFilterPanel`
Advanced filter panel with state persistence and validation.

**Features:**
- Automatic state synchronization
- Theme-aware styling
- Validation feedback
- Export/import functionality

###### `OfferStatsCard`
Interactive statistics display with animations.

**Metrics Displayed:**
- Total offers count
- Total quantity and value
- Average pricing
- Status distribution

### State Management Module (`state_management.py`)

#### Purpose
Centralized state management with caching and session persistence.

#### Key Functions

##### `init_page_state()`
Initializes all session state variables and cache structures.

**Initialized Components:**
- **UI State**: Screen detection, view modes, pagination
- **Data Caches**: Offers, producers, products with TTL
- **User Preferences**: Saved filters, display settings
- **Navigation**: Current selections, breadcrumbs

##### Caching Strategy
```python
# Cache structure
st.session_state.api_cache = {
    'offers_cache': {'data': [], 'timestamp': datetime, 'ttl': 300},
    'producers_cache': {'data': [], 'timestamp': datetime, 'ttl': 3600},
    'products_cache': {'data': [], 'timestamp': datetime, 'ttl': 7200}
}
```

### Advanced Filtering System

The system provides multiple layers of filtering capabilities:

#### 1. Basic Filters (`ui_components.py`)
- Producer selection
- Status filtering  
- Date range selection
- Text search

#### 2. Advanced Filters (`advanced_filters.py`)
```python
def apply_advanced_filters(offers: List[Dict], filter_config: Dict) -> List[Dict]:
    """Apply complex filtering with multiple criteria"""
    
    # Boolean logic support: AND, OR, NOT operations
    # Range filters: quantity, price, date ranges
    # Pattern matching: regex support for text fields
```

#### 3. Saved Filters (`saved_filter_ui.py`)
Persistent filter configurations with user management.

**Features:**
- Save current filter state
- Load saved filter configurations
- Default filter per user
- Filter sharing between users
- Export/import filter configurations

#### 4. Complex Filters (`complex_filters.py`)
Advanced query building with SQL-like syntax support.

### Export and Reporting

#### Export Functions (`export_functions.py`)
```python
def export_offers_to_csv(offers: List[Dict]) -> str:
    """Convert offers to CSV format"""

def export_offers_to_excel(offers: List[Dict]) -> bytes:
    """Generate Excel file with formatting"""

def render_export_buttons(offers: List[Dict]):
    """UI controls for export functionality"""
```

#### Statistics Overview (`statistics_overview.py`)
Interactive dashboard with visualizations.

**Chart Types:**
- Status distribution pie charts
- Quantity trends over time
- Price analysis histograms
- Producer performance metrics

## API Reference

### REST Endpoints Used

The system interfaces with the following backend API endpoints:

#### Offers API
```
GET    /api/offers                 # List offers with filtering
GET    /api/offers/{id}            # Get specific offer details
PUT    /api/offers/{id}            # Update offer information
DELETE /api/offers/{id}            # Delete offer
POST   /api/offers/{id}/status     # Update offer status
GET    /api/offers/{id}/logs       # Get offer history
```

#### Users API
```
GET    /api/users                  # List users (producers)
GET    /api/users/{id}             # Get user details
GET    /api/users/me/settings      # Get current user preferences
PUT    /api/users/me/settings      # Update user preferences
```

#### Products API
```
GET    /api/products/types         # Get product type list
GET    /api/products/grades        # Get quality grade list
```

### Response Format

All API endpoints return responses in the following format:

```json
{
  "success": true|false,
  "data": {...} | [...],
  "error": "error message" | null,
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

## Configuration and Setup

### Environment Requirements

#### Python Dependencies
```
streamlit>=1.28.0
pandas>=1.5.0
plotly>=5.15.0
requests>=2.31.0
python-dateutil>=2.8.2
```

#### System Requirements
- Python 3.8 or higher
- Minimum 4GB RAM for development
- 8GB RAM recommended for production
- Modern web browser with JavaScript enabled

### Configuration Files

#### Application Configuration
The system uses flexible configuration loading:

```python
# Configuration priority order:
1. app_config module (Docker environment)
2. streamlit_app.app_config (Development)
3. FallbackConfig class (Minimal functionality)
```

#### Environment Variables
```bash
API_BASE_URL=http://api:8000          # Backend API URL
DEBUG_MODE=false                      # Enable debug logging
CACHE_TTL_SECONDS=300                 # Default cache timeout
MAX_OFFERS_PER_PAGE=50               # Pagination limit
ENABLE_KEYBOARD_SHORTCUTS=true        # Keyboard navigation
```

### Docker Configuration

#### Development Setup
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY streamlit_app/ ./streamlit_app/
EXPOSE 8501

CMD ["streamlit", "run", "streamlit_app/main.py", "--server.port=8501"]
```

#### Production Considerations
- Use multi-stage builds for smaller images
- Implement health checks for container orchestration
- Configure proper logging aggregation
- Set resource limits and monitoring

## Dependencies

### Core Dependencies

#### Frontend Framework
- **Streamlit**: Web application framework
  - Version: 1.28.0+
  - Usage: Primary UI framework, session management
  - Key Features: Component system, state management, responsive layouts

#### Data Processing
- **Pandas**: Data manipulation and analysis
  - Version: 1.5.0+
  - Usage: Data transformation, filtering, export functionality
  - Performance: Optimized for medium-scale datasets (< 100k records)

- **Plotly**: Interactive visualization
  - Version: 5.15.0+
  - Usage: Statistical charts, dashboard components
  - Features: Responsive charts, export capabilities

#### HTTP Client
- **Requests**: HTTP library for API communication
  - Version: 2.31.0+
  - Usage: Backend API integration
  - Features: Connection pooling, retry mechanisms, timeout handling

### Development Dependencies

#### Testing Framework
```python
pytest>=7.4.0              # Test framework
pytest-cov>=4.1.0          # Coverage reporting
pytest-mock>=3.11.0        # Mocking utilities
```

#### Code Quality
```python
black>=23.7.0               # Code formatting
flake8>=6.0.0              # Linting
mypy>=1.5.0                # Type checking
```

### Import Strategy

The system implements a flexible import strategy to handle different deployment environments:

```python
# Priority order for imports:
try:
    # 1. Docker environment (pages.* namespace)
    from pages.operator.offer_management.module import function
except ImportError:
    try:
        # 2. Direct import (development)
        from module import function
    except ImportError:
        try:
            # 3. Fully qualified (streamlit_app namespace)
            from streamlit_app.pages.operator.offer_management.module import function
        except ImportError:
            # 4. Fallback implementation
            def function(*args, **kwargs):
                return fallback_implementation()
```

## Usage Examples

### Basic Offer Management

#### Initialize the System
```python
import streamlit as st
from pages.operator.offer_management import ModernOfferManager

# Initialize the manager
manager = ModernOfferManager()

# Configure page
st.set_page_config(
    page_title="Offer Management",
    page_icon="🍎",
    layout="wide"
)

# Render the interface
manager.render()
```

#### Filter Offers
```python
# Set up filter parameters
filter_params = {
    'producer_id': 123,
    'status': 'CREATED',
    'date_from': '2024-01-01',
    'date_to': '2024-12-31'
}

# Apply filters and load data
manager.prepare_api_params()
manager.load_offers()

# Access filtered data
offers = st.session_state.offers_data
```

### Advanced Filtering

#### Create Complex Filter
```python
from pages.operator.offer_management.complex_filters import FilterCondition, FilterGroup

# Build complex filter
filter_group = FilterGroup(operator='AND')
filter_group.add_condition(
    FilterCondition('quantity_in_kg', '>', 100)
)
filter_group.add_condition(
    FilterCondition('price', 'between', [200, 500])
)

# Apply to offers
filtered_offers = filter_group.apply(offers)
```

#### Save Filter Configuration
```python
from pages.operator.offer_management.saved_filter_ui import create_saved_filter

# Save current filter state
current_filters = extract_current_filters()
result = create_saved_filter(
    name="High Value Offers",
    filter_data=current_filters,
    description="Offers over 100kg with price 200-500 Ft/kg",
    is_default=False
)
```

### Custom Component Development

#### Create Custom Component
```python
from pages.operator.offer_management.modern_components import BaseComponent

class CustomOfferCard(BaseComponent):
    """Custom offer display card"""
    
    def render(self):
        offer = self.props.get('offer', {})
        
        # Component state
        is_expanded, set_expanded = self.use_state(False, 'expanded')
        
        # Render UI
        with st.container():
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.write(f"**Offer #{offer.get('id')}**")
                st.write(f"Producer: {offer.get('producer_name')}")
                
            with col2:
                if st.button("Details", key=f"details_{offer['id']}"):
                    set_expanded(not is_expanded)
            
            if is_expanded:
                self.render_details(offer)
    
    def render_details(self, offer):
        """Render expanded offer details"""
        st.json(offer)

# Usage
card = CustomOfferCard(component_id="offer_card_123")
card.props = {'offer': offer_data}
card.render()
```

### API Integration

#### Custom API Call
```python
from pages.operator.offer_management.api_client import safe_api_call
from pages.api.offers import get_offer_statistics

# Make safe API call
success, stats = safe_api_call(
    get_offer_statistics,
    "statistical data retrieval",
    start_date='2024-01-01',
    end_date='2024-12-31'
)

if success:
    st.json(stats)
else:
    st.error(f"Failed to load statistics: {stats}")
```

### Export Functionality

#### Export Filtered Data
```python
from pages.operator.offer_management.export_functions import export_offers_to_excel

# Get current offers
offers = st.session_state.offers_data

# Generate Excel export
excel_data = export_offers_to_excel(offers)

# Provide download
st.download_button(
    label="📥 Download Excel",
    data=excel_data,
    file_name=f"offers_{datetime.now().strftime('%Y%m%d')}.xlsx",
    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
)
```

## Development Guidelines

### Code Organization Principles

#### 1. Modular Architecture
- Each module has a single, well-defined responsibility
- Clear separation between UI, business logic, and data access
- Consistent interface patterns across modules

#### 2. Import Strategy
```python
# Always implement fallback imports for environment flexibility
try:
    from preferred_path import module
except ImportError:
    try:
        from alternative_path import module
    except ImportError:
        # Implement minimal fallback
        def module(*args, **kwargs):
            return fallback_implementation()
```

#### 3. Error Handling
```python
# Consistent error handling pattern
def api_function():
    try:
        # Main operation
        result = perform_operation()
        return True, result
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        return False, str(e)
```

### Performance Considerations

#### 1. Caching Strategy
- Implement multi-level caching (session, memory, disk)
- Use appropriate TTL values based on data volatility
- Cache invalidation on data mutations

#### 2. Lazy Loading
```python
# Implement progressive loading for large datasets
def load_offers_progressively(page_size=20):
    for page in range(1, total_pages + 1):
        yield load_offers_page(page, page_size)
```

#### 3. State Management
- Minimize session state usage
- Use appropriate data structures (dict vs list)
- Clean up unused state variables

### Testing Guidelines

#### 1. Unit Testing
```python
import pytest
from unittest.mock import Mock, patch

def test_get_offers_success():
    """Test successful offer retrieval"""
    with patch('api_client.offers_api.get_offers') as mock_api:
        mock_api.return_value = (True, [{'id': 1, 'status': 'CREATED'}])
        
        success, result = get_offers({'status': 'CREATED'})
        
        assert success is True
        assert len(result) == 1
        assert result[0]['id'] == 1
```

#### 2. Integration Testing
```python
def test_filter_integration():
    """Test complete filtering workflow"""
    # Set up test data
    st.session_state.clear()
    
    # Initialize system
    manager = ModernOfferManager()
    
    # Apply filters
    filter_params = {'status': 'CREATED'}
    manager.load_offers()
    
    # Verify results
    assert 'offers_data' in st.session_state
```

### Security Considerations

#### 1. Input Validation
- Validate all user inputs before API calls
- Sanitize HTML content to prevent XSS
- Implement proper data type checking

#### 2. API Security
```python
def safe_api_call(api_func, operation_name, *args, **kwargs):
    """Secure API call wrapper"""
    try:
        # Add authentication headers
        # Implement request timeout
        # Log security events
        return api_func(*args, **kwargs)
    except Exception as e:
        # Security-aware error handling
        logger.security_warning(f"API call failed: {operation_name}")
        return False, "Operation failed"
```

#### 3. Data Protection
- Never log sensitive user data
- Implement proper session management
- Use HTTPS for all API communications

## Troubleshooting

### Common Issues and Solutions

#### 1. Import Errors

**Problem**: `ModuleNotFoundError` when importing components
```
ImportError: No module named 'pages.operator.offer_management.api_client'
```

**Solution**: 
1. Check the flexible import strategy is implemented
2. Verify the Python path includes the correct directories
3. Ensure all `__init__.py` files are present

```python
# Add to troubleshooting section of main module
import sys
import logging

logger.info(f"Python path: {sys.path}")
logger.info(f"Current working directory: {os.getcwd()}")
```

#### 2. Session State Issues

**Problem**: Session state variables not persisting between runs
```
KeyError: 'offers_data' not found in session_state
```

**Solution**:
1. Always check if key exists before accessing
2. Use proper initialization in `init_page_state()`
3. Implement defensive programming

```python
# Safe session state access
offers = st.session_state.get('offers_data', [])

# Or with initialization
if 'offers_data' not in st.session_state:
    st.session_state.offers_data = []
```

#### 3. API Connection Issues

**Problem**: Backend API not responding
```
requests.exceptions.ConnectionError: Connection refused
```

**Solutions**:
1. Verify API_BASE_URL configuration
2. Check network connectivity
3. Implement proper error handling and fallbacks

```python
# API health check
def check_api_health():
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if not check_api_health():
    st.error("Backend API is not available")
    st.info("Using cached data...")
```

#### 4. Performance Issues

**Problem**: Slow page loading with large datasets

**Solutions**:
1. Implement pagination
2. Use data filtering before display
3. Enable caching for expensive operations

```python
# Performance monitoring
import time

@st.cache_data(ttl=300)  # 5-minute cache
def expensive_data_operation():
    start_time = time.time()
    result = complex_computation()
    duration = time.time() - start_time
    logger.info(f"Operation completed in {duration:.2f}s")
    return result
```

#### 5. Responsive Design Issues

**Problem**: Layout broken on mobile devices

**Solutions**:
1. Test responsive breakpoints
2. Use appropriate column configurations
3. Implement device-specific layouts

```python
# Responsive design helper
def get_responsive_columns():
    is_mobile = st.session_state.get('is_mobile', False)
    if is_mobile:
        return [1]  # Single column on mobile
    else:
        return [2, 1]  # Two columns on desktop
```

### Debug Mode

Enable debug mode for additional logging and information:

```python
# Add to configuration
DEBUG_MODE = True

if DEBUG_MODE:
    # Display session state
    with st.expander("Debug Information"):
        st.write("Session State:", st.session_state)
        st.write("Screen Info:", {
            'width': st.session_state.get('screen_width'),
            'is_mobile': st.session_state.get('is_mobile'),
            'is_tablet': st.session_state.get('is_tablet')
        })
```

### Performance Monitoring

```python
# Performance monitoring decorator
import functools
import time

def monitor_performance(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start_time
        
        if duration > 1.0:  # Log slow operations
            logger.warning(f"Slow operation: {func.__name__} took {duration:.2f}s")
        
        return result
    return wrapper

# Usage
@monitor_performance
def load_offers_with_filters(params):
    # Implementation
    pass
```

## Appendices

### Appendix A: Status Definitions

#### Offer Status Workflow
```
CREATED                 # Initial offer creation
    ↓
CONFIRMED_BY_COMPANY   # Company confirms the offer
    ↓
ACCEPTED_BY_USER       # User accepts the confirmed offer
or
REJECTED_BY_USER       # User rejects the confirmed offer
    ↓
FINALIZED              # Final processing complete
```

#### Status Descriptions
- **CREATED**: New offer awaiting company review
- **CONFIRMED_BY_COMPANY**: Offer confirmed with price/quantity
- **ACCEPTED_BY_USER**: User accepted the confirmed terms
- **REJECTED_BY_USER**: User declined the confirmed terms
- **FINALIZED**: Offer processing completed

### Appendix B: Component Architecture

#### Component Hierarchy
```
ModernOfferManager
├── FilterPanel
│   ├── ProducerFilter
│   ├── StatusFilter
│   ├── DateRangeFilter
│   └── SearchFilter
├── StatisticsOverview
│   ├── MetricCards
│   └── Charts
├── OfferTable/OfferCards
│   ├── OfferRow/OfferCard
│   └── ActionButtons
└── DetailView
    ├── OfferDetails
    ├── StatusHistory
    └── ActionPanel
```

#### Component Communication
```
Parent Component
    ↓ (props)
Child Component
    ↑ (callbacks)
Session State
    ↔ (state management)
API Layer
```

### Appendix C: API Response Examples

#### Successful Offer List Response
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "user_id": 456,
      "product_type": {
        "id": 1,
        "name": "Alma"
      },
      "quantity_in_kg": 1000.0,
      "price": 250.0,
      "status": "CREATED",
      "created_at": "2024-01-15T10:30:00Z",
      "delivery_date": "2024-02-01T00:00:00Z",
      "note": "Kiváló minőség"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "pages": 3
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Invalid status parameter",
  "details": {
    "field": "status",
    "allowed_values": ["CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", "FINALIZED"]
  }
}
```

### Appendix D: Configuration Reference

#### Complete Environment Variables
```bash
# API Configuration
API_BASE_URL=http://api:8000
API_TIMEOUT_SECONDS=30
API_RETRY_COUNT=3

# Cache Configuration
CACHE_TTL_OFFERS=300
CACHE_TTL_PRODUCERS=3600
CACHE_TTL_PRODUCTS=7200

# UI Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
ENABLE_KEYBOARD_SHORTCUTS=true
ENABLE_MOBILE_VIEW=true

# Feature Flags
ENABLE_MODERN_INTERFACE=true
ENABLE_SAVED_FILTERS=true
ENABLE_STATISTICS=true
ENABLE_EXPORT=true

# Debug Configuration
DEBUG_MODE=false
LOG_LEVEL=INFO
PERFORMANCE_MONITORING=false
```

### Appendix E: Keyboard Shortcuts

#### Global Shortcuts
- `Alt + N`: New offer creation
- `Alt + R`: Refresh current view
- `Alt + S`: Focus search field
- `Alt + E`: Toggle edit mode
- `Escape`: Close modals/cancel operations

#### Navigation Shortcuts
- `Alt + ←/→`: Previous/Next page
- `J/K`: Navigate table rows
- `1-9`: Quick row selection
- `F`: Focus filter panel

#### Action Shortcuts
- `Alt + A`: Accept offer
- `Alt + D`: Delete offer
- `Alt + C`: Confirm offer
- `Enter`: Submit current form

---

*This documentation was generated for the Offer Management System v1.0. Last updated: December 2024*

**Document Information:**
- **Version**: 1.0.0
- **Generated**: December 2024
- **Format**: GitHub Flavored Markdown
- **Target Audience**: Technical teams, developers, system administrators
- **Review Status**: Draft for review

**Maintainers:**
- Development Team
- System Architecture Team
- Technical Documentation Team