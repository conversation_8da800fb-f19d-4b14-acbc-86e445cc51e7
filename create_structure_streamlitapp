#!/bin/bash

# Alapmappa
BASE_DIR="streamlit_app"

# Fájlokat létrehozó segédfüggvény
create_file() {
  local filepath=$1
  local comment=$2
  mkdir -p "$(dirname "$filepath")"
  echo "$comment" > "$filepath"
}

# Fájlstruktúra létrehozása
create_file "$BASE_DIR/main.py" "# Fő alkalmazás belépési pont"
create_file "$BASE_DIR/config.py" "# Konfigurációs beállítások"

# API modul
create_file "$BASE_DIR/api/__init__.py" ""
create_file "$BASE_DIR/api/auth.py" "# Autentikációs API hívások"
create_file "$BASE_DIR/api/users.py" "# Felhasználó-kezelés API hívások"
create_file "$BASE_DIR/api/products.py" "# Termék-kezelés API hívások"
create_file "$BASE_DIR/api/offers.py" "# Ajánlat-kezelés API hívások"

# Komponensek
create_file "$BASE_DIR/components/__init__.py" ""
create_file "$BASE_DIR/components/sidebar.py" "# Oldalsáv komponens"
create_file "$BASE_DIR/components/auth_forms.py" "# Bejelentkezési/regisztrációs űrlapok"
create_file "$BASE_DIR/components/notification.py" "# Értesítés komponens"
create_file "$BASE_DIR/components/data_display.py" "# Adatmegjelenítő komponensek"

# Pages / auth
create_file "$BASE_DIR/pages/__init__.py" ""
create_file "$BASE_DIR/pages/auth/__init__.py" ""
create_file "$BASE_DIR/pages/auth/login.py" "# Bejelentkezési oldal"
create_file "$BASE_DIR/pages/auth/register.py" "# Regisztrációs oldal"

# Pages / producer
create_file "$BASE_DIR/pages/producer/__init__.py" ""
create_file "$BASE_DIR/pages/producer/dashboard.py" "# Termelői irányítópult"
create_file "$BASE_DIR/pages/producer/profile.py" "# Profil szerkesztés"
create_file "$BASE_DIR/pages/producer/offers.py" "# Ajánlatok kezelése"
create_file "$BASE_DIR/pages/producer/statistics.py" "# Termelői statisztikák"

# Pages / operator
create_file "$BASE_DIR/pages/operator/__init__.py" ""
create_file "$BASE_DIR/pages/operator/dashboard.py" "# Ügyintézői irányítópult"
create_file "$BASE_DIR/pages/operator/offer_management.py" "# Ajánlatok kezelése"
create_file "$BASE_DIR/pages/operator/calendar_view.py" "# Naptári nézet"
create_file "$BASE_DIR/pages/operator/reports.py" "# Riportok és statisztikák"

# Pages / admin
create_file "$BASE_DIR/pages/admin/__init__.py" ""
create_file "$BASE_DIR/pages/admin/dashboard.py" "# Admin irányítópult"
create_file "$BASE_DIR/pages/admin/user_management.py" "# Felhasználók kezelése"
create_file "$BASE_DIR/pages/admin/product_management.py" "# Termékek kezelése"

# Utils modul
create_file "$BASE_DIR/utils/__init__.py" ""
create_file "$BASE_DIR/utils/session.py" "# Munkamenet kezelés"
create_file "$BASE_DIR/utils/formatting.py" "# Formázási segédfüggvények"
create_file "$BASE_DIR/utils/validators.py" "# Adatvalidációs függvények"
create_file "$BASE_DIR/utils/auth_utils.py" "# Autentikációs segédfüggvények"

echo "✅ A fájlstruktúra létrejött: $BASE_DIR/"
