#!/bin/bash

# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a debug konténer fut-e
CONTAINER_NAME="debug-tools"
CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)

if [ -z "$CONTAINER_ID" ]; then
    echo "Debug konténer nem fut. Ind<PERSON>tom a rendszert..."
    docker-compose up -d
    sleep 5
    CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)
    
    if [ -z "$CONTAINER_ID" ]; then
        echo "Hiba: A debug konténer nem indult el!"
        exit 1
    fi
fi

echo "Debug konténer fut (ID: $CONTAINER_ID)"
echo "Debug menü indítása..."

# Debug menü indítása a futó konténerben
docker exec -it $CONTAINER_ID bash /app/debug/debug_menu.sh

# Alternatív megoldás: konténerbe belépés
# Ha inkább be szeretn<PERSON><PERSON> lépni a konténerbe, és onnan ind<PERSON>tani a menüt, 
# akkor kommenteld ki a fenti sort, és kommenteld be az alábbi sort:
# docker exec -it $CONTAINER_ID bash 