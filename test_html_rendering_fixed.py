#!/usr/bin/env python3
"""
Test script to verify HTML rendering fixes using components.html
"""
import streamlit as st
import streamlit.components.v1 as components
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(page_title="HTML Rendering Fix Test", layout="wide")

st.title("🧪 HTML Rendering Fix Test - Using components.html")

# Test 1: Basic HTML rendering comparison
st.header("Test 1: Basic HTML Rendering Methods")
col1, col2, col3 = st.columns(3)

with col1:
    st.subheader("st.markdown (traditional)")
    st.markdown("<p style='color: red;'>This text should be red</p>", unsafe_allow_html=True)
    st.markdown("<div style='background-color: yellow; padding: 10px;'>Yellow background</div>", unsafe_allow_html=True)

with col2:
    st.subheader("components.html (for complex HTML)")
    components.html("<p style='color: red;'>This text should be red</p>", height=50)
    components.html("<div style='background-color: yellow; padding: 10px;'>Yellow background</div>", height=50)

with col3:
    st.subheader("Mixed approach")
    st.info("Use st.markdown for simple HTML, components.html for complex structures")

st.markdown("---")

# Test 2: CSS injection
st.header("Test 2: CSS Injection")
st.markdown("Using st.markdown for CSS (works well):")
st.markdown("""
<style>
.test-box {
    border: 2px solid blue;
    padding: 20px;
    margin: 10px 0;
    background-color: lightblue;
    border-radius: 10px;
}
.test-text {
    color: darkblue;
    font-weight: bold;
}
</style>
""", unsafe_allow_html=True)

st.markdown('<div class="test-box"><p class="test-text">This box uses CSS classes</p></div>', unsafe_allow_html=True)

st.markdown("---")

# Test 3: Complex HTML structure
st.header("Test 3: Complex HTML Table")
html_table = """
<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
    <thead>
        <tr style="background-color: #333; color: white;">
            <th style="padding: 10px; border: 1px solid #ddd;">Feature</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Method</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Best For</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;">Simple HTML</td>
            <td style="padding: 10px; border: 1px solid #ddd;">st.markdown(..., unsafe_allow_html=True)</td>
            <td style="padding: 10px; border: 1px solid #ddd;">✅ Inline styles, basic formatting</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
            <td style="padding: 10px; border: 1px solid #ddd;">Complex HTML</td>
            <td style="padding: 10px; border: 1px solid #ddd;">components.html(...)</td>
            <td style="padding: 10px; border: 1px solid #ddd;">✅ Tables, complex layouts, interactive content</td>
        </tr>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;">CSS Styles</td>
            <td style="padding: 10px; border: 1px solid #ddd;">st.markdown(&lt;style&gt;...&lt;/style&gt;, unsafe_allow_html=True)</td>
            <td style="padding: 10px; border: 1px solid #ddd;">✅ Global styles, CSS rules</td>
        </tr>
    </tbody>
</table>
"""
components.html(html_table, height=200)

st.markdown("---")

# Test 4: Import and test our updated components
st.header("Test 4: Updated Components")

try:
    from pages.operator.offer_management.nutrition_facts_container import NutritionFactsContainer
    from pages.operator.offer_management.detail_components import DetailContainer
    
    # Test NutritionFactsContainer
    st.subheader("NutritionFactsContainer Test")
    test_offer = {
        'id': 123,
        'status': 'CONFIRMED_BY_COMPANY',
        'status_display': 'Cég által visszaigazolva',
        'quantity_in_kg': 100,
        'price': 850,
        'created_at': '2024-04-27T08:30:00Z',
        'delivery_date': '2024-05-01',
        'user': {
            'contact_name': 'Test User',
            'company_name': 'Test Company'
        },
        'product_type': {
            'name': 'Test Product'
        }
    }
    
    nf_container = NutritionFactsContainer(title="Test Offer", subtitle="Offer #123")
    nf_container.render(test_offer, debug_mode=False)
    
    # Test DetailContainer
    st.subheader("DetailContainer Test")
    detail_container = DetailContainer(
        title="Test Detail Container",
        icon="📋",
        expandable=True,
        expanded=True
    )
    
    def test_content():
        st.write("This is test content inside the DetailContainer")
        st.markdown("<p style='color: green; font-weight: bold;'>This HTML content should be green and bold</p>", unsafe_allow_html=True)
    
    detail_container.render(test_content)
    
    st.success("✅ All components loaded and rendered successfully!")
    
except Exception as e:
    st.error(f"❌ Error loading components: {str(e)}")
    st.exception(e)

st.markdown("---")
st.info("""
💡 **Solution Summary:**
- For simple HTML and CSS: Use `st.markdown(..., unsafe_allow_html=True)`
- For complex HTML structures: Use `components.html(..., height=xxx)`
- This hybrid approach provides the best compatibility and performance
""")