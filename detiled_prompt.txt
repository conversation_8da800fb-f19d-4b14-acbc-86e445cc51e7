Objective:
Act as a senior full-stack architect tasked with creating a definitive guide to our application's codebase. The primary goal is to generate a comprehensive Markdown file named CODEBASE_OVERVIEW.md. This document must meticulously map out the entire monorepo, explaining how the Streamlit frontend, FastAPI backend, and PostgreSQL database interact to deliver the application's features. The analysis must be grounded in the specific technology stack provided.

Application Context:
Your analysis should be based on the following architectural and technological blueprint. Use this information to guide your discovery and interpretation of the code.

1. Component Responsibilities:

Frontend (Streamlit): Responsible for all user interface rendering, session state management, API calls to the backend, and role-based access control for pages.

Backend (FastAPI): Implements all business logic, manages API endpoints (including authentication and authorization), validates data schemas with Pydantic, and handles asynchronous tasks like email notifications.

Database (PostgreSQL): Serves as the single source of truth for persistent data, managed via SQLAlchemy ORM and Alembic for migrations, ensuring transactional integrity.

Infrastructure: The application is containerized with Docker. SMTP integration is used for email services.

2. Core Technology Stack:

Backend Framework: FastAPI 0.104.1

Frontend Framework: Streamlit 1.32.0

Database: PostgreSQL 15

ORM: SQLAlchemy 2.0.22

Migration Tool: Alembic 1.12.1

Authentication: JWT managed with python-jose==3.3.0

Password Hashing: bcrypt==4.0.1 via passlib==1.7.4

3. Key Dependencies to Investigate:

Backend:

text
fastapi==0.104.1
uvicorn==0.23.2
sqlalchemy==2.0.22
alembic==1.12.1
pydantic>=2.0.0
psycopg2-binary==2.9.9
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.0.1
email-validator==2.1.0.post1
aiosmtplib==3.0.1
Frontend:

text
streamlit==1.32.0
pandas==2.2.0
plotly==5.17.0
httpx==0.25.0
Step-by-Step Instructions:

Initial Codebase Scan:

Begin by scanning the entire repository to identify the directory structure. Differentiate between frontend source code (likely in a /frontend or /streamlit_app directory) and backend source code (/backend or /app).

Pinpoint the main application entry points for both the FastAPI server (main.py or app.py containing the FastAPI() instance) and the Streamlit application.

Backend Analysis (FastAPI):

Map out all API endpoints, including their HTTP methods, path parameters, and request/response models (Pydantic schemas).

Trace the dependency injection system. Identify router dependencies, security schemes (especially for JWT), and service-layer classes.

Specifically analyze the authentication flow. Document how python-jose is used to create and validate JWTs and how passlib/bcrypt are used for password hashing during user registration and login.

Detail the database interaction layer. Explain how SQLAlchemy models correspond to PostgreSQL tables and how Alembic migration scripts manage the schema. Show how repository or service patterns are used to query the database.

Frontend Analysis (Streamlit):

Document the page structure and routing logic. Explain how user roles determine which pages are accessible.

Identify all API calls made from the frontend to the backend using httpx. For each call, specify the corresponding backend endpoint it communicates with.

Analyze the session state management (st.session_state). Explain what user information (e.g., auth tokens, user data) is stored and how it's used to maintain a logged-in experience.

Describe how data fetched from the API is rendered using components like pandas DataFrames or plotly charts.

End-to-End Workflow Mapping:

Synthesize your findings by describing at least two critical end-to-end user workflows with code snippets:

User Authentication: Trace the flow from the Streamlit login form, to the httpx POST request, to the FastAPI /token endpoint, the validation of credentials, the creation of a JWT, and the storage of the token in the Streamlit session state.

Data Creation: Trace the flow from a user submitting a form in the Streamlit UI, to an httpx POST/PUT request with a data payload, to the FastAPI endpoint that validates the Pydantic model, calls a service function, and uses SQLAlchemy to persist the data in the PostgreSQL database.

Generate the CODEBASE_OVERVIEW.md file:

Organize the final output into the file CODEBASE_OVERVIEW.md using the following Markdown structure. Be precise and use code blocks for examples.

text
# Application Architecture Overview

## 1. High-Level Architecture
A brief summary describing how the Streamlit Frontend, FastAPI Backend, and PostgreSQL Database work together. Include a Mermaid.js diagram illustrating this three-tier architecture.

## 2. Component Deep Dive

### 2.1. Backend (FastAPI)
- **API Endpoints:** A table of all routes, their purpose, and required authentication.
- **Authentication Flow:** A detailed explanation of the JWT implementation using `python-jose` and `passlib`.
- **Database Layer:** An overview of the SQLAlchemy models and their relationships. Explain the role of Alembic.
- **Core Services:** Descriptions of key business logic modules.

### 2.2. Frontend (Streamlit)
- **Page Structure:** A map of all UI pages and their functions.
- **State Management:** How `st.session_state` is used for authentication and interactivity.
- **API Interaction:** A list of key functions that use `httpx` to communicate with the backend.

## 3. End-to-End Workflows

### 3.1. User Login and Session Creation
(Provide a step-by-step description with frontend and backend code snippets)

### 3.2. Example CRUD Operation (e.g., Creating a New Item)
(Provide a step-by-step description with frontend and backend code snippets)

## 4. Dependencies and Setup
- **Key Libraries:** A brief explanation of the role of major dependencies (e.g., `fastapi`, `streamlit`, `sqlalchemy`, `pydantic`).
- **Environment Setup:** Instructions on how to set up the development environment, referencing `requirements.txt` or similar files.
