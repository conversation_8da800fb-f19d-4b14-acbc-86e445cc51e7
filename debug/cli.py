#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parancssori interfész modul a Mezőgazdasági Termékkezelő Rendszer hibakeresési rendszeréhez.

Ez a modul biztosítja a hibakeresési rendszer fő parancssori interfészét,
és lehetővé teszi a logok elemzését, szűrését, és a konténerek kezelését.
"""

import os
import sys
import json
import time
import datetime
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any

from debug.container import ContainerManager, LogConfig, ColorManager
from debug.analyzer import LogAnalyzer
from debug.logger import Logger, LogLevel

def setup_parser() -> argparse.ArgumentParser:
    """
    Beállítja az argumentum parsert a parancssori interfészhez.
    
    Returns:
        argparse.ArgumentParser: Az argumentum parser
    """
    parser = argparse.ArgumentParser(
        description='Debug eszköztár a Mezőgazdasági Termékkezelő Rendszerhez.',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Globális opciók
    parser.add_argument('--color', dest='color', action='store_true',
                        help='Színes kimenet kényszerítése')
    parser.add_argument('--no-color', dest='no_color', action='store_true',
                        help='Színek letiltása')
    parser.add_argument('--config', dest='config_file', default=None,
                        help='Konfigurációs fájl elérési útja')
    
    # Részletes leírás
    parser.epilog = """
    Példák:
        python -m debug.cli logs streamlit            # Streamlit konténer logok
        python -m debug.cli logs backend --tail 100   # Backend konténer utolsó 100 log sora
        python -m debug.cli analyze streamlit         # Streamlit logok elemzése
        python -m debug.cli live streamlit            # Streamlit logok élő követése
        python -m debug.cli status                    # Konténer státuszok megjelenítése
        python -m debug.cli rotate                    # Log fájlok rotálása
    """
    
    # Alparancsok
    subparsers = parser.add_subparsers(dest='command', help='Parancsok')
    
    # logs parancs
    logs_parser = subparsers.add_parser('logs', help='Log fájlok lekérdezése')
    logs_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                           help='Szolgáltatás neve')
    logs_parser.add_argument('--tail', type=int, default=None,
                           help='Utolsó N sor megjelenítése')
    logs_parser.add_argument('--since', type=str, default=None,
                           help='Logok lekérdezése ettől az időponttól (pl. "2h" vagy "2024-01-01")')
    logs_parser.add_argument('--until', type=str, default=None,
                           help='Logok lekérdezése eddig az időpontig (pl. "1h" vagy "2024-01-01")')
    logs_parser.add_argument('--json', action='store_true',
                           help='JSON kimenet')
    
    # analyze parancs
    analyze_parser = subparsers.add_parser('analyze', help='Log fájlok elemzése')
    analyze_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                              help='Szolgáltatás neve')
    analyze_parser.add_argument('--json', action='store_true',
                              help='JSON kimenet')
    analyze_parser.add_argument('--detailed', action='store_true',
                              help='Részletes elemzés')
    
    # follow/live parancs
    follow_parser = subparsers.add_parser('follow', help='Log fájlok élő követése')
    follow_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                             help='Szolgáltatás neve')
    follow_parser.add_argument('--filter', type=str, default=None,
                             help='Szűrés megadott kifejezésre')
    
    # status parancs
    status_parser = subparsers.add_parser('status', help='Konténer státuszok lekérdezése')
    status_parser.add_argument('--json', action='store_true',
                             help='JSON kimenet')
    
    # compose parancs
    compose_parser = subparsers.add_parser('compose', help='Docker Compose műveletek')
    compose_subparsers = compose_parser.add_subparsers(dest='compose_command', help='Compose parancsok')
    
    # compose logs parancs
    compose_logs_parser = compose_subparsers.add_parser('logs', help='Docker Compose logok')
    compose_logs_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                                    help='Szolgáltatás neve')
    compose_logs_parser.add_argument('--follow', action='store_true',
                                    help='Logok folyamatos követése')
    
    # compose status parancs
    compose_status_parser = compose_subparsers.add_parser('status', help='Docker Compose státusz')
    
    # compose restart parancs
    compose_restart_parser = compose_subparsers.add_parser('restart', help='Konténer újraindítása')
    compose_restart_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                                       help='Szolgáltatás neve')
    
    # compose stop parancs
    compose_stop_parser = compose_subparsers.add_parser('stop', help='Konténer leállítása')
    compose_stop_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                                    help='Szolgáltatás neve')
    
    # compose start parancs
    compose_start_parser = compose_subparsers.add_parser('start', help='Konténer indítása')
    compose_start_parser.add_argument('service', choices=['streamlit', 'backend', 'db'],
                                     help='Szolgáltatás neve')
    
    # compose debug parancs
    compose_debug_parser = compose_subparsers.add_parser('debug', help='Debug mód kezelése')
    compose_debug_parser.add_argument('--enable', action='store_true',
                                     help='Debug mód bekapcsolása')
    compose_debug_parser.add_argument('--disable', action='store_true',
                                     help='Debug mód kikapcsolása')
    
    # rotate parancs
    rotate_parser = subparsers.add_parser('rotate', help='Log fájlok rotálása')
    rotate_parser.add_argument('--delete-old', action='store_true',
                             help='Régi log fájlok törlése')
    rotate_parser.add_argument('--compress', action='store_true',
                             help='Log fájlok tömörítése')
    
    # config parancs
    config_parser = subparsers.add_parser('config', help='Konfiguráció kezelése')
    config_subparsers = config_parser.add_subparsers(dest='config_command', help='Konfiguráció parancsok')
    
    # config show parancs
    config_show_parser = config_subparsers.add_parser('show', help='Konfiguráció megjelenítése')
    
    # config update parancs
    config_update_parser = config_subparsers.add_parser('update', help='Konfiguráció frissítése')
    config_update_parser.add_argument('--log-path', nargs=2, action='append', metavar=('SERVICE', 'PATH'),
                                     help='Log útvonal frissítése (többször megadható)')
    config_update_parser.add_argument('--container', nargs=2, action='append', metavar=('SERVICE', 'CONTAINER'),
                                     help='Konténer név frissítése (többször megadható)')
    config_update_parser.add_argument('--colors', choices=['enable', 'disable'],
                                     help='Színek engedélyezése/tiltása')
    config_update_parser.add_argument('--max-size', type=int,
                                     help='Log fájlok maximális mérete (MB)')
    config_update_parser.add_argument('--backup-count', type=int,
                                     help='Log fájlok biztonsági mentéseinek száma')
    
    return parser

def rotate_logs(args):
    """
    Log fájlok rotálása.
    
    Args:
        args: A parancssor argumentumok
    """
    color_manager = ColorManager(force_color=args.color, no_color=args.no_color)
    config = LogConfig(args.config_file)
    log_rotation_config = config.get_log_rotation_config()
    
    max_size_bytes = log_rotation_config.get("max_size_mb", 10) * 1024 * 1024
    backup_count = log_rotation_config.get("backup_count", 5)
    
    # Log könyvtár elérési útja
    log_dir = Path("debug/logs")
    if not log_dir.exists():
        print(color_manager.colorize("Log könyvtár nem létezik, létrehozás...", "info"))
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # Log fájlok listázása
    log_files = list(log_dir.glob("*.log"))
    if not log_files:
        print(color_manager.colorize("Nem található log fájl!", "warning"))
        return
    
    print(color_manager.colorize(f"Log rotáció indítása ({len(log_files)} fájl)...", "info"))
    
    import shutil
    import gzip
    
    for log_file in log_files:
        try:
            file_size = log_file.stat().st_size
            if file_size > max_size_bytes:
                print(color_manager.colorize(f"Rotálás: {log_file.name} (méret: {file_size / 1024 / 1024:.2f} MB)", "info"))
                
                # Régi rotált fájlok eltolása
                for i in range(backup_count - 1, 0, -1):
                    backup_file = log_file.with_suffix(f".log.{i}")
                    next_backup_file = log_file.with_suffix(f".log.{i+1}")
                    
                    if backup_file.exists():
                        if next_backup_file.exists():
                            next_backup_file.unlink()
                        shutil.move(str(backup_file), str(next_backup_file))
                
                # Aktuális log fájl átnevezése
                backup_file = log_file.with_suffix(".log.1")
                if backup_file.exists():
                    backup_file.unlink()
                
                # A jelenlegi log másolása és az eredeti törlése
                shutil.copy2(str(log_file), str(backup_file))
                log_file.write_text("")  # Üres fájl létrehozása
                
                if args.compress:
                    # Tömörítés gzip-pel
                    with open(backup_file, 'rb') as f_in:
                        with gzip.open(str(backup_file) + '.gz', 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    # Tömörítetlen változat törlése
                    backup_file.unlink()
                    print(color_manager.colorize(f"  Tömörítve: {backup_file.name}.gz", "info"))
            else:
                print(color_manager.colorize(
                    f"Kihagyás: {log_file.name} (méret: {file_size / 1024 / 1024:.2f} MB < {max_size_bytes / 1024 / 1024:.2f} MB)",
                    "info"
                ))
        except Exception as e:
            print(color_manager.colorize(f"Hiba a log fájl rotálása közben ({log_file.name}): {str(e)}", "error"))
    
    if args.delete_old:
        # Régi log fájlok törlése
        old_log_files = []
        for i in range(backup_count + 1, 100):  # Biztonsági felső határ
            old_files = list(log_dir.glob(f"*.log.{i}")) + list(log_dir.glob(f"*.log.{i}.gz"))
            old_log_files.extend(old_files)
        
        if old_log_files:
            print(color_manager.colorize(f"Régi log fájlok törlése ({len(old_log_files)} fájl)...", "info"))
            for old_file in old_log_files:
                try:
                    old_file.unlink()
                    print(color_manager.colorize(f"  Törölve: {old_file.name}", "info"))
                except Exception as e:
                    print(color_manager.colorize(f"Hiba a régi log fájl törlésekor ({old_file.name}): {str(e)}", "error"))

def update_config(args):
    """
    Konfiguráció frissítése.
    
    Args:
        args: A parancssor argumentumok
    """
    color_manager = ColorManager(force_color=args.color, no_color=args.no_color)
    config = LogConfig(args.config_file)
    
    if args.log_path:
        for service, path in args.log_path:
            if service not in ["streamlit", "backend", "db"]:
                print(color_manager.colorize(f"Érvénytelen szolgáltatás: {service}", "error"))
                continue
                
            # Jelenlegi útvonalak lekérdezése
            current_paths = config.get_log_paths(service)
            
            # Ellenőrizzük, hogy az útvonal valós-e
            path_obj = Path(path)
            if not path_obj.exists():
                print(color_manager.colorize(
                    f"Figyelmeztetés: A megadott útvonal nem létezik: {path}", 
                    "warning"
                ))
                
            # Útvonal hozzáadása, ha még nincs benne
            if path not in current_paths:
                current_paths.append(path)
                config.update_log_path(service, current_paths)
                print(color_manager.colorize(
                    f"Log útvonal hozzáadva: {service} -> {path}", 
                    "info"
                ))
    
    if args.container:
        for service, container in args.container:
            if service not in ["streamlit", "backend", "db"]:
                print(color_manager.colorize(f"Érvénytelen szolgáltatás: {service}", "error"))
                continue
                
            config.update_container_name(service, container)
            print(color_manager.colorize(
                f"Konténer név frissítve: {service} -> {container}", 
                "info"
            ))
    
    if args.colors:
        config.config["use_colors"] = (args.colors == "enable")
        config.save_config()
        print(color_manager.colorize(
            f"Színek {'engedélyezve' if args.colors == 'enable' else 'letiltva'}", 
            "info"
        ))
    
    if args.max_size:
        if "log_rotation" not in config.config:
            config.config["log_rotation"] = {}
        config.config["log_rotation"]["max_size_mb"] = args.max_size
        config.save_config()
        print(color_manager.colorize(
            f"Log fájlok maximális mérete beállítva: {args.max_size} MB", 
            "info"
        ))
    
    if args.backup_count:
        if "log_rotation" not in config.config:
            config.config["log_rotation"] = {}
        config.config["log_rotation"]["backup_count"] = args.backup_count
        config.save_config()
        print(color_manager.colorize(
            f"Log fájlok biztonsági mentéseinek száma beállítva: {args.backup_count}", 
            "info"
        ))

def show_config(args):
    """
    Konfiguráció megjelenítése.
    
    Args:
        args: A parancssor argumentumok
    """
    color_manager = ColorManager(force_color=args.color, no_color=args.no_color)
    config = LogConfig(args.config_file)
    
    print(color_manager.colorize(f"Konfigurációs fájl: {config.config_file}", "info"))
    print(json.dumps(config.config, indent=4, ensure_ascii=False))

def main():
    """Fő függvény a parancssori interfész futtatásához."""
    parser = setup_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'logs':
        # Docker konténer logok lekérdezése
        container_manager = ContainerManager(
            config_file=args.config_file,
            force_color=args.color,
            no_color=args.no_color
        )
        logs = container_manager.get_container_logs(
            service_name=args.service,
            tail=args.tail,
            since=args.since,
            until=args.until
        )
        
        if args.json:
            try:
                # JSON formátumú kimenet
                log_list = []
                for line in logs.splitlines():
                    if line.strip():
                        try:
                            log_list.append(json.loads(line))
                        except json.JSONDecodeError:
                            log_list.append({"raw_message": line})
                
                print(json.dumps(log_list, indent=4, ensure_ascii=False))
            except Exception as e:
                print(f"Hiba a JSON konvertálás során: {str(e)}")
                print(logs)
        else:
            # Nyers szöveg kimenet
            print(logs)
    
    elif args.command == 'analyze':
        # Log elemzés
        container_manager = ContainerManager(
            config_file=args.config_file,
            force_color=args.color,
            no_color=args.no_color
        )
        logs = container_manager.get_container_logs(service_name=args.service)
        
        log_analyzer = LogAnalyzer()
        results = log_analyzer.analyze_logs(logs=logs, service_name=args.service)
        
        if args.json:
            # JSON formátumú kimenet
            print(json.dumps(results, indent=4, ensure_ascii=False))
        else:
            # Formázott kimenet
            color_manager = ColorManager(force_color=args.color, no_color=args.no_color)
            
            print(color_manager.colorize("=== Log Elemzés Eredménye ===", "info"))
            print(f"Szolgáltatás: {args.service}")
            print(f"Időbélyeg: {results['timestamp']}")
            print(f"Log sorok száma: {results['log_lines_count']}")
            
            print(color_manager.colorize("\n=== Log Szintek ===", "info"))
            for level, count in results['levels'].items():
                if count > 0:
                    print(f"{level}: {count}")
            
            print(color_manager.colorize("\n=== Hiba Kategóriák ===", "info"))
            for category, count in results['categories'].items():
                if count > 0:
                    print(f"{category}: {count}")
            
            if args.detailed:
                print(color_manager.colorize("\n=== Trendek ===", "info"))
                print(color_manager.colorize("Hibagyakoriság:", "info"))
                for hour, count in results['trends']['error_frequency'].items():
                    print(f"{hour}: {count}")
                
                print(color_manager.colorize("\nLeggyakoribb hibák:", "info"))
                for error in results['trends']['common_errors']:
                    print(f"[{error['count']}x] {error['message']}")
            
            print(color_manager.colorize("\n=== Azonosított Problémák ===", "info"))
            for issue in results['identified_issues']:
                severity_color = "error" if issue['severity'] == "error" else "warning"
                print(color_manager.colorize(f"[{issue['severity']}] {issue['category']}: {issue['description']}", severity_color))
            
            print(color_manager.colorize("\n=== Javaslatok ===", "info"))
            for suggestion in results['suggestions']:
                print(f"- {suggestion}")
    
    elif args.command == 'follow':
        # Logok élő követése
        container_manager = ContainerManager(
            config_file=args.config_file,
            force_color=args.color,
            no_color=args.no_color
        )
        if args.filter:
            # Ha van szűrő, be kell állítani
            from debug.debug_compose import DockerComposeManager
            docker_compose_manager = DockerComposeManager()
            docker_compose_manager.set_container_filter(args.service, args.filter)
        
        # Logok folyamatos követése
        container_manager.get_container_logs(
            service_name=args.service,
            follow=True
        )
    
    elif args.command == 'status':
        # Konténer státuszok lekérdezése
        container_manager = ContainerManager(
            config_file=args.config_file,
            force_color=args.color,
            no_color=args.no_color
        )
        statuses = container_manager.refresh_container_statuses()
        
        if args.json:
            # JSON formátumú kimenet
            print(json.dumps(statuses, indent=4, ensure_ascii=False))
        else:
            # Táblázatos kimenet
            color_manager = ColorManager(force_color=args.color, no_color=args.no_color)
            
            print(color_manager.colorize("=== Konténer Státuszok ===", "info"))
            print(f"{'Szolgáltatás':<12} | {'Konténer ID':<12} | {'Státusz':<40} | {'Fut':<5}")
            print("-" * 80)
            
            for service, status in statuses.items():
                container_id = status.get('id', 'N/A')[:12]
                container_status = status.get('status', 'N/A')
                is_running = "Igen" if status.get('is_running', False) else "Nem"
                is_running_color = "info" if status.get('is_running', False) else "error"
                
                print(f"{service:<12} | {container_id:<12} | {container_status:<40} | {color_manager.colorize(is_running, is_running_color):<5}")
    
    elif args.command == 'compose':
        # Docker Compose műveletek
        from debug.debug_compose import DockerComposeManager
        docker_compose_manager = DockerComposeManager()
        
        if args.compose_command == 'logs':
            # Docker Compose logok
            docker_compose_manager.get_container_logs(
                service_name=args.service,
                follow=args.follow
            )
        
        elif args.compose_command == 'status':
            # Docker Compose státusz
            for service in ['streamlit', 'backend', 'db']:
                status = docker_compose_manager.get_container_status(service)
                print(f"{service}: {status.get('status', 'Unknown')}")
        
        elif args.compose_command == 'restart':
            # Konténer újraindítása
            result = docker_compose_manager.restart_container(args.service)
            if result:
                print(f"{args.service} konténer sikeresen újraindítva")
            else:
                print(f"Hiba a(z) {args.service} konténer újraindításakor")
        
        elif args.compose_command == 'stop':
            # Konténer leállítása
            result = docker_compose_manager.stop_container(args.service)
            if result:
                print(f"{args.service} konténer sikeresen leállítva")
            else:
                print(f"Hiba a(z) {args.service} konténer leállításakor")
        
        elif args.compose_command == 'start':
            # Konténer indítása
            result = docker_compose_manager.start_container(args.service)
            if result:
                print(f"{args.service} konténer sikeresen elindítva")
            else:
                print(f"Hiba a(z) {args.service} konténer indításakor")
        
        elif args.compose_command == 'debug':
            # Debug mód kezelése
            if args.enable:
                # Debug mód bekapcsolása
                print("Debug mód bekapcsolva")
            elif args.disable:
                # Debug mód kikapcsolása
                print("Debug mód kikapcsolva")
            else:
                # Állapot lekérdezése
                print("Debug mód állapota: ismeretlen")
    
    elif args.command == 'rotate':
        # Log fájlok rotálása
        rotate_logs(args)
    
    elif args.command == 'config':
        # Konfiguráció kezelése
        if args.config_command == 'show':
            # Konfiguráció megjelenítése
            show_config(args)
        elif args.config_command == 'update':
            # Konfiguráció frissítése
            update_config(args)
        else:
            # Érvénytelen parancs
            print("Érvénytelen konfiguráció parancs!")

if __name__ == '__main__':
    main() 