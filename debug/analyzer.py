#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Log elemző modul a Mezőgazdasági Termékkezelő Rendszer hibakeresési rendszeréhez.

Ez a modul felelős a log fájlok elemzéséért, mintázatok azonosításáért
és a problémák kategorizálásáért.
"""

import re
import json
import datetime
from collections import Counter, defaultdict
from typing import Dict, List, Set, Tuple, Optional, Any, Union

from debug.logger import LogLevel

class LogAnalyzer:
    """
    Log elemző osztály a hibák azonosítására és kategorizálására.
    
    Jellemzők:
        - Log szintek szerinti csoportosítás
        - Minta felismerés a logokban
        - API hibák azonosítása
        - Adatbázis problémák felismerése
        - Újraindítások detektálása
    """
    
    # Hibaüzenet minták
    ERROR_PATTERNS = {
        "api_error": [
            r"4\d\d [A-Z]+ /api",
            r"5\d\d [A-Z]+ /api",
            r"Error\s+calling\s+API",
            r"API\s+request\s+failed",
            r"API\s+timeout"
        ],
        "db_error": [
            r"(psycopg2|sqlalchemy)\..*?Error",
            r"database\s+is\s+locked",
            r"database\s+connection\s+.*?(failed|lost|error)",
            r"connection\s+to\s+database\s+.*?failed",
            r"query\s+execution\s+failed"
        ],
        "auth_error": [
            r"(Authentication|Authorization)\s+failed",
            r"Invalid\s+token",
            r"Token\s+expired",
            r"Unauthorized",
            r"Forbidden"
        ],
        "validation_error": [
            r"ValidationError",
            r"Validation\s+.*?failed",
            r"Invalid\s+(parameter|value|input)",
            r"Required\s+field\s+.*?missing"
        ],
        "server_error": [
            r"Internal\s+Server\s+Error",
            r"Server\s+.*?crashed",
            r"OutOfMemory",
            r"MemoryError",
            r"CPU\s+usage\s+.*?high"
        ],
        "restart": [
            r"(Starting|Restarting)\s+container",
            r"Application\s+.*?(starting|restarting)",
            r"Starting\s+application",
            r"Server\s+starting"
        ]
    }
    
    def __init__(self):
        """Inicializálja a log elemzőt."""
        # Előre lefordított regex minták
        self.compiled_patterns = {
            category: [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
            for category, patterns in self.ERROR_PATTERNS.items()
        }
    
    def analyze_logs(self, 
                    logs: str, 
                    service_name: str) -> Dict[str, Any]:
        """
        Elemzi a megadott naplófájlokat és visszaadja az eredményeket.
        
        Args:
            logs: A naplóbejegyzések szövege
            service_name: A szolgáltatás neve
            
        Returns:
            Dict[str, Any]: Az elemzés eredményei
        """
        # Eredmények inicializálása
        results = {
            "service": service_name,
            "timestamp": datetime.datetime.now().isoformat(),
            "log_lines_count": 0,
            "levels": {
                "critical": 0,
                "error": 0,
                "warning": 0,
                "info": 0,
                "debug": 0,
                "unknown": 0
            },
            "categories": {
                "api_error": 0,
                "db_error": 0,
                "auth_error": 0,
                "validation_error": 0,
                "server_error": 0,
                "restart": 0
            },
            "trends": {
                "error_frequency": {},
                "common_errors": []
            },
            "identified_issues": [],
            "suggestions": []
        }
        
        # Log sorok feldolgozása
        log_lines = logs.strip().split("\n")
        results["log_lines_count"] = len(log_lines)
        
        # Időszakonkénti hibastatisztika
        hourly_errors = defaultdict(int)
        
        # Hibák gyűjtése
        error_messages = []
        
        # Trace ID alapján csoportosított hibák
        trace_id_groups = defaultdict(list)
        
        # Log bejegyzések elemzése
        for line in log_lines:
            line = line.strip()
            if not line:
                continue
                
            # JSON formátumú log bejegyzés feldolgozása
            try:
                log_entry = json.loads(line)
                
                # Szint számlálása
                level = log_entry.get("level", "").lower()
                if level in results["levels"]:
                    results["levels"][level] += 1
                else:
                    results["levels"]["unknown"] += 1
                
                # Időszak rögzítése hibastatisztikához
                timestamp = log_entry.get("timestamp")
                if timestamp and (level == "error" or level == "critical"):
                    try:
                        dt = datetime.datetime.fromisoformat(timestamp)
                        hour_key = dt.strftime("%Y-%m-%d %H:00")
                        hourly_errors[hour_key] += 1
                        
                        # Hibaüzenet rögzítése
                        error_messages.append(log_entry.get("message", ""))
                    except (ValueError, TypeError):
                        pass
                
                # Trace ID alapján csoportosítás
                trace_id = log_entry.get("trace_id")
                if trace_id and (level == "error" or level == "critical"):
                    trace_id_groups[trace_id].append(log_entry)
                
                # Kategóriák ellenőrzése
                message = log_entry.get("message", "")
                exception_info = log_entry.get("exception", {})
                exception_message = exception_info.get("message", "")
                exception_traceback = exception_info.get("traceback", "")
                
                # Teljes szöveg a mintaillesztéshez
                full_text = f"{message} {exception_message} {exception_traceback}"
                
                # Kategóriák vizsgálata
                for category, patterns in self.compiled_patterns.items():
                    for pattern in patterns:
                        if pattern.search(full_text):
                            results["categories"][category] += 1
                            break
                
            except json.JSONDecodeError:
                # Nem JSON formátumú log bejegyzés
                results["levels"]["unknown"] += 1
                
                # Szöveges log elemzése
                for category, patterns in self.compiled_patterns.items():
                    for pattern in patterns:
                        if pattern.search(line):
                            results["categories"][category] += 1
                            break
                
                # Hiba esetén mentjük a sort
                if "error" in line.lower() or "critical" in line.lower() or "exception" in line.lower():
                    error_messages.append(line)
        
        # Óránkénti hibastatisztika számítása
        results["trends"]["error_frequency"] = dict(sorted(hourly_errors.items()))
        
        # Leggyakoribb hibák számítása
        error_counter = Counter(error_messages)
        results["trends"]["common_errors"] = [
            {"message": msg, "count": count}
            for msg, count in error_counter.most_common(10)
        ]
        
        # Mintázatok azonosítása és problémák kiemelése
        results["identified_issues"] = self._identify_issues(results, trace_id_groups)
        
        # Javaslatok készítése
        results["suggestions"] = self._generate_suggestions(results)
        
        return results
    
    def _identify_issues(self, 
                        results: Dict[str, Any], 
                        trace_id_groups: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Azonosítja a főbb problémákat a logok alapján.
        
        Args:
            results: Az elemzés eredményei
            trace_id_groups: Trace ID alapján csoportosított hibák
            
        Returns:
            List[Dict[str, Any]]: Az azonosított problémák listája
        """
        issues = []
        
        # API hibák vizsgálata
        if results["categories"]["api_error"] > 0:
            issues.append({
                "category": "API hiba",
                "severity": "error" if results["categories"]["api_error"] > 5 else "warning",
                "description": f"{results['categories']['api_error']} API hiba azonosítva",
                "count": results["categories"]["api_error"]
            })
        
        # Adatbázis hibák vizsgálata
        if results["categories"]["db_error"] > 0:
            issues.append({
                "category": "Adatbázis hiba",
                "severity": "error" if results["categories"]["db_error"] > 3 else "warning",
                "description": f"{results['categories']['db_error']} adatbázis hiba azonosítva",
                "count": results["categories"]["db_error"]
            })
        
        # Autentikációs hibák vizsgálata
        if results["categories"]["auth_error"] > 0:
            issues.append({
                "category": "Autentikációs hiba",
                "severity": "warning",
                "description": f"{results['categories']['auth_error']} autentikációs hiba azonosítva",
                "count": results["categories"]["auth_error"]
            })
        
        # Validációs hibák vizsgálata
        if results["categories"]["validation_error"] > 0:
            issues.append({
                "category": "Validációs hiba",
                "severity": "info",
                "description": f"{results['categories']['validation_error']} validációs hiba azonosítva",
                "count": results["categories"]["validation_error"]
            })
        
        # Szerver hibák vizsgálata
        if results["categories"]["server_error"] > 0:
            issues.append({
                "category": "Szerver hiba",
                "severity": "critical" if results["categories"]["server_error"] > 1 else "error",
                "description": f"{results['categories']['server_error']} szerver hiba azonosítva",
                "count": results["categories"]["server_error"]
            })
        
        # Újraindítások vizsgálata
        if results["categories"]["restart"] > 0:
            issues.append({
                "category": "Újraindítás",
                "severity": "warning" if results["categories"]["restart"] > 2 else "info",
                "description": f"{results['categories']['restart']} újraindítás észlelve",
                "count": results["categories"]["restart"]
            })
        
        # Súlyos hibák aránya
        error_count = results["levels"]["error"] + results["levels"]["critical"]
        total_count = sum(results["levels"].values())
        error_ratio = error_count / total_count if total_count > 0 else 0
        
        if error_ratio > 0.2:  # 20% felett
            issues.append({
                "category": "Magas hibaarány",
                "severity": "critical" if error_ratio > 0.5 else "error",
                "description": f"A hibák aránya {error_ratio:.1%}, ami magasnak számít",
                "count": error_count
            })
        
        # Összekapcsolt hibák elemzése
        if trace_id_groups:
            largest_trace_group = max(trace_id_groups.items(), key=lambda x: len(x[1]))
            trace_id, entries = largest_trace_group
            
            if len(entries) > 2:
                issues.append({
                    "category": "Hibacsoport",
                    "severity": "error",
                    "description": f"Összefüggő hibacsoport ({len(entries)} bejegyzés) azonosítva a(z) {trace_id} trace ID-val",
                    "count": len(entries),
                    "trace_id": trace_id
                })
        
        return issues
    
    def _generate_suggestions(self, results: Dict[str, Any]) -> List[str]:
        """
        Javaslatokat generál a problémák megoldására.
        
        Args:
            results: Az elemzés eredményei
            
        Returns:
            List[str]: A javaslatok listája
        """
        suggestions = []
        
        # API hibák
        if results["categories"]["api_error"] > 5:
            suggestions.append(
                "Ellenőrizze az API végpontok elérhetőségét és válaszidejét. "
                "Fontolja meg a timeout értékek növelését és a hibakezelés javítását."
            )
        
        # Adatbázis hibák
        if results["categories"]["db_error"] > 3:
            suggestions.append(
                "Vizsgálja meg az adatbázis kapcsolatokat és a konkurrens lekérdezéseket. "
                "Ellenőrizze az adatbázis terhelését és a kapcsolatok számát."
            )
        
        # Autentikációs hibák
        if results["categories"]["auth_error"] > 0:
            suggestions.append(
                "Ellenőrizze a token érvényességét és az autentikációs folyamatot. "
                "Vizsgálja meg a token lejárati idejét és a felhasználói jogosultságokat."
            )
        
        # Szerver hibák
        if results["categories"]["server_error"] > 0:
            suggestions.append(
                "Vizsgálja meg a szerver erőforrás-használatát (memória, CPU). "
                "Fontolja meg az erőforrások bővítését vagy az alkalmazás optimalizálását."
            )
        
        # Újraindítások
        if results["categories"]["restart"] > 2:
            suggestions.append(
                "Elemezze az újraindítások okát. Ellenőrizze a szerver stabilitását "
                "és keressen mintázatokat az újraindítások előtti logokban."
            )
        
        # Error ratio
        error_count = results["levels"]["error"] + results["levels"]["critical"]
        total_count = sum(results["levels"].values())
        error_ratio = error_count / total_count if total_count > 0 else 0
        
        if error_ratio > 0.2:
            suggestions.append(
                f"A hibák aránya ({error_ratio:.1%}) magasnak számít. "
                "Fontolja meg a hibakezelés javítását és a kritikus komponensek felülvizsgálatát."
            )
        
        # Általános javaslatok
        if not suggestions:
            suggestions.append(
                "A logok alapján nem azonosítottunk kritikus problémákat. "
                "Rendszeresen ellenőrizze a logokat az esetleges problémák korai észlelése érdekében."
            )
        
        return suggestions 