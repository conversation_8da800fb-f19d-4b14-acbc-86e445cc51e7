#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker Compose log kezelő modul a Mezőgazdasági Termékkezelő Rendszer hibakeresési rendszeréhez.

Ez a modul felelős a Docker Compose által generált logok kezeléséért,
beleértve a logok színkódolt megjelenítését, strukturált formázását
és a különböző típusú hibaüzenetek elkülönítését.
"""

import os
import re
import json
import time
import docker
import datetime
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from pathlib import Path

from debug.logger import Logger, LogLevel
from debug.container import ContainerManager
from debug.analyzer import LogAnalyzer

@dataclass
class ContainerLogConfig:
    """Konténer log konfiguráció."""
    container_id: str
    service_name: str
    color: str
    enabled: bool = True
    filter_pattern: Optional[str] = None
    last_position: int = 0

class DockerComposeManager:
    """
    Docker Compose log kezelő osztály.
    
    Ez az osztály felelős a Docker Compose által generált logok kezeléséért,
    beleértve a logok színkódolt megjelenítését, strukturált formázását
    és a különböző típusú hibaüzenetek elkülönítését.
    """
    
    # Színkódok a különböző log szintekhez
    LOG_COLORS = {
        "DEBUG": "\033[36m",     # Cián
        "INFO": "\033[32m",      # Zöld
        "WARNING": "\033[33m",   # Sárga
        "ERROR": "\033[31m",     # Piros
        "CRITICAL": "\033[35m",  # Magenta
        "RESET": "\033[0m"       # Alapértelmezett
    }
    
    # Konténer színek
    CONTAINER_COLORS = {
        "streamlit": "\033[94m",  # Kék
        "backend": "\033[92m",    # Világos zöld
        "db": "\033[93m",         # Világos sárga
        "RESET": "\033[0m"        # Alapértelmezett
    }
    
    def __init__(self):
        """Inicializálja a Docker Compose menedzsert."""
        self.docker_available = False
        
        # Logger inicializálása
        self.logger = Logger(
            service_name="debug-compose",
            log_file="debug/logs/debug_compose.log",
            console_output=True,
            json_output=False,
            min_level=LogLevel.INFO
        )
        
        # Docker klient inicializálása
        try:
            # Környezeti változók ellenőrzése
            docker_host = os.environ.get('DOCKER_HOST', '')
            self.logger.info(f"Docker Host beállítva: {docker_host}" if docker_host else "Docker Host nincs explicit beállítva")
            
            # Socket elérhetőség ellenőrzése Unix socket esetén
            if docker_host.startswith('unix://'):
                socket_path = docker_host.replace('unix://', '')
                if not os.path.exists(socket_path):
                    self.logger.error(f"Docker socket nem létezik: {socket_path}")
                else:
                    self.logger.info(f"Docker socket elérhető: {socket_path}")
                    # Jogosultságok ellenőrzése
                    try:
                        socket_stat = os.stat(socket_path)
                        current_uid = os.getuid()
                        current_gid = os.getgid()
                        self.logger.info(f"Socket jogosultságok: uid={socket_stat.st_uid}, gid={socket_stat.st_gid}, mode={oct(socket_stat.st_mode)}")
                        self.logger.info(f"Aktuális felhasználó: uid={current_uid}, gid={current_gid}")
                    except Exception as e:
                        self.logger.error(f"Hiba a socket jogosultságok ellenőrzésekor: {str(e)}")
            elif not docker_host:
                # Alapértelmezett socket ellenőrzése
                socket_path = '/var/run/docker.sock'
                if not os.path.exists(socket_path):
                    self.logger.error(f"Alapértelmezett Docker socket nem létezik: {socket_path}")
                else:
                    self.logger.info(f"Alapértelmezett Docker socket elérhető: {socket_path}")
            
            # Docker kliens létrehozása
            self.client = docker.from_env()
            # Docker elérhetőség ellenőrzése
            version = self.client.version()
            self.logger.info(f"Docker kapcsolat sikeres. Verzió: {version.get('Version', 'ismeretlen')}")
            self.docker_available = True
        except docker.errors.DockerException as e:
            self.logger.error(f"Docker kliens hiba: {str(e)}")
            self.docker_available = False
        except Exception as e:
            self.logger.error(f"Hiba a Docker klienshez való kapcsolódáskor: {str(e)}")
            self.docker_available = False
        
        if not self.docker_available:
            self.logger.warning("A debug rendszer korlátozott módban működik.")
        
        # További inicializálás
        self.container_manager = ContainerManager()
        self.log_analyzer = LogAnalyzer()
        
        # Log könyvtár inicializálása
        self.log_dir = Path("debug/logs")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Konténer konfigurációk inicializálása
        self.container_configs: Dict[str, ContainerLogConfig] = {}
        self._init_container_configs()
    
    def _init_container_configs(self):
        """Inicializálja a konténer konfigurációkat."""
        # Alapértelmezett konténer nevek
        default_containers = {
            "streamlit": "streamlit",
            "backend": "backend",
            "db": "termelo-db"
        }
        
        if not self.docker_available:
            # Ha a Docker nem elérhető, használjuk az alapértelmezett neveket
            for service_name, container_name in default_containers.items():
                self.container_configs[service_name] = ContainerLogConfig(
                    container_id=container_name,
                    service_name=service_name,
                    color=self.CONTAINER_COLORS.get(service_name, self.CONTAINER_COLORS["RESET"])
                )
            self.logger.warning("Docker nem elérhető, alapértelmezett konténer nevek használata.")
            return
        
        try:
            # Docker konténerek keresése név alapján
            containers = self.client.containers.list(all=True)
            found_services = set()
            
            for container in containers:
                for service_name, service_pattern in default_containers.items():
                    # Ellenőrizzük, hogy a konténer neve tartalmazza-e a szolgáltatás nevét
                    if service_pattern in container.name and service_name not in found_services:
                        self.container_configs[service_name] = ContainerLogConfig(
                            container_id=container.id,
                            service_name=service_name,
                            color=self.CONTAINER_COLORS.get(service_name, self.CONTAINER_COLORS["RESET"])
                        )
                        found_services.add(service_name)
                        self.logger.debug(f"Konténer megtalálva: {service_name} - {container.id} ({container.name})")
            
            # Ellenőrizzük, hogy minden szolgáltatáshoz találtunk-e konténert
            for service_name in default_containers.keys():
                if service_name not in found_services:
                    self.logger.warning(f"Konténer nem található a következő szolgáltatáshoz: {service_name}")
                    # Használjuk az alapértelmezett nevet
                    self.container_configs[service_name] = ContainerLogConfig(
                        container_id=default_containers[service_name],
                        service_name=service_name,
                        color=self.CONTAINER_COLORS.get(service_name, self.CONTAINER_COLORS["RESET"])
                    )
            
        except Exception as e:
            self.logger.error(f"Hiba a konténer konfigurációk inicializálásakor: {str(e)}")
            # Használjuk az alapértelmezett neveket hiba esetén
            for service_name, container_name in default_containers.items():
                self.container_configs[service_name] = ContainerLogConfig(
                    container_id=container_name,
                    service_name=service_name,
                    color=self.CONTAINER_COLORS.get(service_name, self.CONTAINER_COLORS["RESET"])
                )
    
    def check_docker_environment(self) -> bool:
        """
        Ellenőrzi a Docker környezetet.
        
        Returns:
            bool: True, ha a Docker elérhető, egyébként False
        """
        if not self.docker_available:
            return False
            
        try:
            # Docker daemon elérhetőség
            self.client.ping()
            
            # Docker Compose elérhetőség ellenőrzése
            import subprocess
            result = subprocess.run(['docker-compose', 'version'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  check=False)
            
            if result.returncode != 0:
                self.logger.warning("Docker Compose nem elérhető")
                return False
                
            return True
        except Exception as e:
            self.logger.error(f"Docker környezet nem elérhető: {str(e)}")
            self.docker_available = False
            return False
    
    def get_container_logs(self, 
                          service_name: str,
                          tail: Optional[int] = None,
                          since: Optional[str] = None,
                          follow: bool = False) -> str:
        """
        Lekéri egy konténer logjait.
        
        Args:
            service_name: A szolgáltatás neve
            tail: A visszaadandó log sorok száma
            since: Naplók visszaadása ettől az időponttól
            follow: A logok folyamatos követése
            
        Returns:
            str: A konténer logjai
        """
        config = self.container_configs.get(service_name)
        if not config:
            raise ValueError(f"Ismeretlen szolgáltatás: {service_name}")
        
        try:
            container = self.client.containers.get(config.container_id)
            logs = container.logs(
                tail=tail,
                since=since,
                follow=follow,
                timestamps=True,
                stream=True
            )
            
            if follow:
                return self._process_following_logs(logs, config)
            else:
                return self._process_logs(logs, config)
                
        except docker.errors.NotFound:
            self.logger.error(f"Konténer nem található: {config.container_id}")
            return ""
        except Exception as e:
            self.logger.error(f"Hiba a logok lekérésekor: {str(e)}")
            return ""
    
    def _process_logs(self, logs: str, config: ContainerLogConfig) -> str:
        """
        Feldolgozza a logokat.
        
        Args:
            logs: A nyers logok
            config: A konténer konfiguráció
            
        Returns:
            str: A feldolgozott logok
        """
        processed_logs = []
        
        for line in logs.decode('utf-8').split('\n'):
            if not line:
                continue
                
            # Időbélyeg és üzenet szétválasztása
            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d+Z)', line)
            if timestamp_match:
                timestamp = timestamp_match.group(1)
                message = line[len(timestamp_match.group(0)):].strip()
                
                # Log szint meghatározása
                log_level = self._determine_log_level(message)
                color = self.LOG_COLORS.get(log_level, self.LOG_COLORS["RESET"])
                
                # Szűrés alkalmazása, ha van
                if config.filter_pattern and not re.search(config.filter_pattern, message, re.IGNORECASE):
                    continue
                
                # Formázott log sor
                formatted_line = (
                    f"{config.color}[{config.service_name}]{self.LOG_COLORS['RESET']} "
                    f"{color}[{log_level}]{self.LOG_COLORS['RESET']} "
                    f"{timestamp} {message}"
                )
                
                processed_logs.append(formatted_line)
        
        return '\n'.join(processed_logs)
    
    def _process_following_logs(self, logs: str, config: ContainerLogConfig) -> str:
        """
        Feldolgozza a követett logokat.
        
        Args:
            logs: A nyers logok
            config: A konténer konfiguráció
            
        Returns:
            str: A feldolgozott logok
        """
        try:
            for log in logs:
                if not log:
                    continue
                    
                line = log.decode('utf-8')
                
                # Időbélyeg és üzenet szétválasztása
                timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d+Z)', line)
                if timestamp_match:
                    timestamp = timestamp_match.group(1)
                    message = line[len(timestamp_match.group(0)):].strip()
                    
                    # Log szint meghatározása
                    log_level = self._determine_log_level(message)
                    color = self.LOG_COLORS.get(log_level, self.LOG_COLORS["RESET"])
                    
                    # Szűrés alkalmazása, ha van
                    if config.filter_pattern and not re.search(config.filter_pattern, message, re.IGNORECASE):
                        continue
                    
                    # Formázott log sor
                    formatted_line = (
                        f"{config.color}[{config.service_name}]{self.LOG_COLORS['RESET']} "
                        f"{color}[{log_level}]{self.LOG_COLORS['RESET']} "
                        f"{timestamp} {message}"
                    )
                    
                    print(formatted_line)
                    
        except KeyboardInterrupt:
            self.logger.info("Log követés leállítva")
        except Exception as e:
            self.logger.error(f"Hiba a logok követésekor: {str(e)}")
    
    def _determine_log_level(self, message: str) -> str:
        """
        Meghatározza a log szintjét az üzenet alapján.
        
        Args:
            message: A log üzenet
            
        Returns:
            str: A log szint
        """
        message = message.upper()
        
        if "CRITICAL" in message or "FATAL" in message:
            return "CRITICAL"
        elif "ERROR" in message or "EXCEPTION" in message:
            return "ERROR"
        elif "WARNING" in message or "WARN" in message:
            return "WARNING"
        elif "DEBUG" in message:
            return "DEBUG"
        else:
            return "INFO"
    
    def save_logs_to_file(self, service_name: str, logs: str):
        """
        Elmenti a logokat egy fájlba.
        
        Args:
            service_name: A szolgáltatás neve
            logs: A logok
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.log_dir / f"{service_name}_{timestamp}.log"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(logs)
            self.logger.info(f"Logok mentve: {filename}")
        except Exception as e:
            self.logger.error(f"Hiba a logok mentésekor: {str(e)}")
    
    def set_container_filter(self, service_name: str, filter_pattern: Optional[str]):
        """
        Beállítja a konténer log szűrését.
        
        Args:
            service_name: A szolgáltatás neve
            filter_pattern: A szűrési minta
        """
        if service_name in self.container_configs:
            self.container_configs[service_name].filter_pattern = filter_pattern
            self.logger.info(f"Szűrés beállítva a {service_name} konténerhez: {filter_pattern}")
        else:
            self.logger.error(f"Ismeretlen szolgáltatás: {service_name}")
    
    def toggle_container_logging(self, service_name: str):
        """
        Be/kikapcsolja egy konténer logolását.
        
        Args:
            service_name: A szolgáltatás neve
        """
        if service_name in self.container_configs:
            config = self.container_configs[service_name]
            config.enabled = not config.enabled
            status = "bekapcsolva" if config.enabled else "kikapcsolva"
            self.logger.info(f"Logolás {status} a {service_name} konténerhez")
        else:
            self.logger.error(f"Ismeretlen szolgáltatás: {service_name}")
    
    def get_container_status(self, service_name: str) -> Dict[str, Any]:
        """
        Lekéri egy konténer állapotát.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            Dict[str, Any]: A konténer állapota
        """
        config = self.container_configs.get(service_name)
        if not config:
            raise ValueError(f"Ismeretlen szolgáltatás: {service_name}")
        
        try:
            container = self.client.containers.get(config.container_id)
            return {
                "id": container.id,
                "name": container.name,
                "status": container.status,
                "state": container.attrs["State"],
                "created": container.attrs["Created"],
                "image": container.image.tags[0] if container.image.tags else "none"
            }
        except docker.errors.NotFound:
            return {
                "id": config.container_id,
                "name": service_name,
                "status": "not_found",
                "error": "Konténer nem található"
            }
        except Exception as e:
            return {
                "id": config.container_id,
                "name": service_name,
                "status": "error",
                "error": str(e)
            }
    
    def restart_container(self, service_name: str) -> bool:
        """
        Újraindít egy konténert.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            bool: True, ha sikeres az újraindítás
        """
        config = self.container_configs.get(service_name)
        if not config:
            self.logger.error(f"Ismeretlen szolgáltatás: {service_name}")
            return False
        
        try:
            container = self.client.containers.get(config.container_id)
            container.restart()
            self.logger.info(f"Konténer újraindítva: {service_name}")
            return True
        except Exception as e:
            self.logger.error(f"Hiba a konténer újraindításakor: {str(e)}")
            return False
    
    def stop_container(self, service_name: str) -> bool:
        """
        Leállít egy konténert.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            bool: True, ha sikeres a leállítás
        """
        config = self.container_configs.get(service_name)
        if not config:
            self.logger.error(f"Ismeretlen szolgáltatás: {service_name}")
            return False
        
        try:
            container = self.client.containers.get(config.container_id)
            container.stop()
            self.logger.info(f"Konténer leállítva: {service_name}")
            return True
        except Exception as e:
            self.logger.error(f"Hiba a konténer leállításakor: {str(e)}")
            return False
    
    def start_container(self, service_name: str) -> bool:
        """
        Elindít egy konténert.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            bool: True, ha sikeres az indítás
        """
        config = self.container_configs.get(service_name)
        if not config:
            self.logger.error(f"Ismeretlen szolgáltatás: {service_name}")
            return False
        
        try:
            container = self.client.containers.get(config.container_id)
            container.start()
            self.logger.info(f"Konténer elindítva: {service_name}")
            return True
        except Exception as e:
            self.logger.error(f"Hiba a konténer indításakor: {str(e)}")
            return False 