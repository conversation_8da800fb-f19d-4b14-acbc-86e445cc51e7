#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strukturált naplózási rendszer a Mezőgazdasági Termékkezelő Rendszerhez.

Ez a modul biztosítja a projekt naplózási infrastruktúráj<PERSON>t, amely
JSON formátumú, id<PERSON>b<PERSON><PERSON>eggel ellátott, nyomkövethető logbejegyzéseket hoz létre.
"""

import json
import logging
import sys
import time
import uuid
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Union

class LogLevel(Enum):
    """Log szintek enumerációja"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class Logger:
    """
    Strukturált naplózó osztály, amely JSON formátumú naplóbejegyzéseket hoz létre.
    
    Jellemzők:
        - Strukturált JSON alapú naplózás
        - Időbélyeg minden bejegyzésben
        - Trace ID-k a hibák nyomkövetésére
        - Kontextuális adatok tárolása
        - Színkódolt konzol kimenet
    """
    
    # ANSI színkódok a konzol kimenethez
    COLORS = {
        LogLevel.DEBUG: "\033[36m",     # Cián
        LogLevel.INFO: "\033[32m",      # Zöld
        LogLevel.WARNING: "\033[33m",   # Sárga
        LogLevel.ERROR: "\033[31m",     # Piros
        LogLevel.CRITICAL: "\033[35m",  # Magenta
        "RESET": "\033[0m"
    }
    
    def __init__(self, 
                 service_name: str, 
                 log_file: Optional[str] = None,
                 console_output: bool = True,
                 json_output: bool = True,
                 min_level: LogLevel = LogLevel.INFO):
        """
        Inicializálja a naplózót.
        
        Args:
            service_name: A szolgáltatás neve (pl. "backend", "streamlit", "db")
            log_file: A log fájl elérési útja, ha van
            console_output: Logok megjelenítése a konzolon
            json_output: JSON formátumú kimenet
            min_level: Minimális naplózási szint
        """
        self.service_name = service_name
        self.log_file = log_file
        self.console_output = console_output
        self.json_output = json_output
        self.min_level = min_level
        self.context = {}
        
        # Log fájl megnyitása, ha van
        self.file_handler = None
        if log_file:
            try:
                self.file_handler = open(log_file, "a", encoding="utf-8")
            except Exception as e:
                sys.stderr.write(f"Hiba a log fájl megnyitásakor: {str(e)}\n")
    
    def __del__(self):
        """Bezárja a log fájlt a megsemmisítéskor"""
        if self.file_handler:
            self.file_handler.close()
    
    def set_context(self, **kwargs):
        """
        Beállítja a naplózás kontextusát.
        
        Args:
            **kwargs: Kulcs-érték párok, amelyek a kontextust alkotják
        """
        self.context.update(kwargs)
    
    def clear_context(self):
        """Törli a naplózás kontextusát"""
        self.context = {}
    
    def _should_log(self, level: LogLevel) -> bool:
        """
        Ellenőrzi, hogy a megadott szintet naplózni kell-e.
        
        Args:
            level: A naplózási szint
            
        Returns:
            bool: True, ha a szintet naplózni kell, egyébként False
        """
        levels = list(LogLevel)
        return levels.index(level) >= levels.index(self.min_level)
    
    def _format_log(self, 
                   level: LogLevel, 
                   message: str, 
                   trace_id: Optional[str] = None,
                   function_name: Optional[str] = None,
                   exception: Optional[Exception] = None,
                   **kwargs) -> Dict[str, Any]:
        """
        Formázza a naplóbejegyzést.
        
        Args:
            level: A naplózási szint
            message: A naplózandó üzenet
            trace_id: A nyomkövetési azonosító, ha van
            function_name: A függvény neve, ha van
            exception: A kivétel objektum, ha van
            **kwargs: További kulcs-érték párok
            
        Returns:
            Dict[str, Any]: A formázott naplóbejegyzés
        """
        now = datetime.now().isoformat()
        
        # Alap log struktúra
        log_entry = {
            "timestamp": now,
            "level": level.value,
            "service": self.service_name,
            "message": message
        }
        
        # Trace ID hozzáadása, ha nincs, akkor generálás
        if trace_id:
            log_entry["trace_id"] = trace_id
        
        # Függvény név hozzáadása, ha van
        if function_name:
            log_entry["function"] = function_name
        
        # Kivétel információ hozzáadása, ha van
        if exception:
            log_entry["exception"] = {
                "type": type(exception).__name__,
                "message": str(exception),
                "traceback": traceback.format_exc()
            }
        
        # Kontextus és további paraméterek hozzáadása
        if self.context:
            log_entry["context"] = self.context
        
        # További paraméterek hozzáadása
        for key, value in kwargs.items():
            log_entry[key] = value
        
        return log_entry
    
    def _write_log(self, log_entry: Dict[str, Any], level: LogLevel):
        """
        Kiírja a naplóbejegyzést a megfelelő kimenetekre.
        
        Args:
            log_entry: A naplóbejegyzés
            level: A naplózási szint
        """
        # JSON kimenet
        if self.json_output:
            json_log = json.dumps(log_entry, ensure_ascii=False)
            
            # Fájlba írás
            if self.file_handler:
                self.file_handler.write(json_log + "\n")
                self.file_handler.flush()
            
            # Konzolra írás
            if self.console_output:
                color = self.COLORS.get(level, self.COLORS["RESET"])
                reset = self.COLORS["RESET"]
                print(f"{color}{json_log}{reset}")
        
        # Egyszerű szöveges kimenet
        elif self.console_output:
            color = self.COLORS.get(level, self.COLORS["RESET"])
            reset = self.COLORS["RESET"]
            
            timestamp = log_entry.get("timestamp", "")
            service = log_entry.get("service", "")
            level_str = log_entry.get("level", "")
            message = log_entry.get("message", "")
            trace_id = log_entry.get("trace_id", "")
            
            trace_info = f" [trace_id={trace_id}]" if trace_id else ""
            
            print(f"{color}[{timestamp}] [{service}] [{level_str}]{trace_info} {message}{reset}")
    
    def log(self, 
            level: LogLevel, 
            message: str, 
            trace_id: Optional[str] = None,
            function_name: Optional[str] = None,
            exception: Optional[Exception] = None,
            **kwargs):
        """
        Naplóz egy üzenetet a megadott szinten.
        
        Args:
            level: A naplózási szint
            message: A naplózandó üzenet
            trace_id: A nyomkövetési azonosító, ha van
            function_name: A függvény neve, ha van
            exception: A kivétel objektum, ha van
            **kwargs: További kulcs-érték párok
        """
        if not self._should_log(level):
            return
        
        # Ha nincs trace_id, de van exception, generálunk egy trace_id-t
        if not trace_id and exception:
            trace_id = str(uuid.uuid4())
        
        log_entry = self._format_log(
            level=level,
            message=message,
            trace_id=trace_id,
            function_name=function_name,
            exception=exception,
            **kwargs
        )
        
        self._write_log(log_entry, level)
    
    # Kényelmi metódusok a különböző log szintekhez
    
    def debug(self, message: str, **kwargs):
        """Debug szintű üzenet naplózása"""
        self.log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Info szintű üzenet naplózása"""
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Warning szintű üzenet naplózása"""
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Error szintű üzenet naplózása"""
        self.log(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Critical szintű üzenet naplózása"""
        self.log(LogLevel.CRITICAL, message, **kwargs)
    
    def exception(self, message: str, exc: Exception, **kwargs):
        """
        Naplóz egy kivételt.
        
        Args:
            message: A naplózandó üzenet
            exc: A kivétel objektum
            **kwargs: További kulcs-érték párok
        """
        self.log(
            level=LogLevel.ERROR,
            message=message,
            exception=exc,
            **kwargs
        ) 