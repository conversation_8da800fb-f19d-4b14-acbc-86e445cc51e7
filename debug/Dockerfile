FROM python:3.11-slim

WORKDIR /app

# Alapvető csomagok telepítése
RUN apt-get update && apt-get install -y --no-install-recommends \
    bash \
    curl \
    procps \
    gcc \
    python3-dev \
    libyaml-dev \
    libc6-dev \
    apt-transport-https \
    ca-certificates \
    gnupg-agent \
    software-properties-common \
    vim \
    net-tools \
    iputils-ping \
    docker.io \
    docker-compose \
    && rm -rf /var/lib/apt/lists/*

# Cython telepítése
RUN pip install --no-cache-dir Cython

# Függőségek telepítése
COPY requirements.txt /app/debug/
RUN pip install --no-cache-dir -r /app/debug/requirements.txt

# Debug modul másolása
COPY . /app/debug/

# Szükséges könyvtárak létrehozása
RUN mkdir -p /app/debug/logs /app/debug/config

# Jogosultságok beállítása
RUN chmod +x /app/debug/debug_menu.sh

# Alapértelmezett konfiguráció létrehozása
RUN echo '{\
    "log_paths": {\
        "streamlit": ["/app/logs/streamlit.log", "/app/.streamlit/logs"],\
        "backend": ["/app/logs/backend.log", "/var/log/backend"],\
        "db": ["/var/log/postgresql/postgresql-15-main.log", "/var/lib/postgresql/data/log"]\
    },\
    "containers": {\
        "streamlit": "streamlit",\
        "backend": "backend",\
        "db": "termelo-db"\
    },\
    "use_colors": true,\
    "log_rotation": {\
        "max_size_mb": 10,\
        "backup_count": 5\
    }\
}' > /app/debug/config/log_paths.json

# Környezeti változók beállítása
ENV PYTHONPATH=/app
ENV API_BASE_URL=http://backend:8000/api
ENV DEBUG_CONFIG_FILE=/app/debug/config/log_paths.json
ENV DOCKER_HOST=unix:///var/run/docker.sock

# Belépési pont
ENTRYPOINT ["bash", "/app/debug/debug_menu.sh"]