#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker konténer kommunikációs modul a Mezőgazdasági Termékkezelő Rendszer hibakeresési rendszeréhez.

Ez a modul biztosítja a Docker konténerekkel való kommunikációt,
és lehetővé teszi a logok kinyerését közvetlenül a konténerekből.
"""

import os
import re
import json
import time
import subprocess
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path

class LogConfig:
    """
    Log konfiguráció osztály.
    
    Ez az osztály felelős a log útvonalak és egyéb konfigurációs opciók kezeléséért.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Inicializálja a log konfigurációt.
        
        Args:
            config_file: A konfigurációs fájl elérési útja
        """
        self.config_file = config_file or os.environ.get(
            "DEBUG_CONFIG_FILE", 
            os.path.join("debug", "config", "log_paths.json")
        )
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Betölti a konfigurációt.
        
        Returns:
            Dict[str, Any]: A konfiguráció
        """
        # Alapértelmezett konfiguráció
        default_config = {
            "log_paths": {
                "streamlit": ["/app/logs/streamlit.log", "/app/.streamlit/logs"],
                "backend": ["/app/logs/backend.log", "/var/log/backend"],
                "db": ["/var/log/postgresql/postgresql-15-main.log", "/var/lib/postgresql/data/log"]
            },
            "containers": {
                "streamlit": "streamlit",
                "backend": "backend",
                "db": "termelo-db"
            },
            "use_colors": True,
            "log_rotation": {
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
        
        try:
            config_dir = os.path.dirname(self.config_file)
            # Konfiguráció könyvtár létrehozása, ha nem létezik
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
                
            if not os.path.exists(self.config_file):
                # Írjuk ki a default konfigurációt a fájlba, ha nem létezik
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                print(f"Konfigurációs fájl létrehozva: {self.config_file}")
                return default_config
            
            # Konfiguráció betöltése
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Ellenőrizzük, hogy minden szükséges beállítás megvan-e
            # Ha hiányzik valami, használjuk az alapértelmezett értéket
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            
            return config
        except Exception as e:
            print(f"Hiba a konfiguráció betöltésekor: {str(e)}")
            return default_config
    
    def save_config(self) -> bool:
        """
        Menti a konfigurációt.
        
        Returns:
            bool: True, ha sikeres volt a mentés, egyébként False
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Hiba a konfiguráció mentésekor: {str(e)}")
            return False
    
    def get_log_paths(self, service_name: str) -> List[str]:
        """
        Lekéri egy szolgáltatás log útvonalait.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            List[str]: A log útvonalak listája
        """
        return self.config.get("log_paths", {}).get(service_name, [])
    
    def get_container_name(self, service_name: str) -> str:
        """
        Lekéri egy szolgáltatás konténer nevét.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            str: A konténer neve
        """
        return self.config.get("containers", {}).get(service_name, service_name)
    
    def use_colors(self) -> bool:
        """
        Ellenőrzi, hogy használjunk-e színeket.
        
        Returns:
            bool: True, ha használni kell a színeket, egyébként False
        """
        return self.config.get("use_colors", True)
    
    def get_log_rotation_config(self) -> Dict[str, Any]:
        """
        Lekéri a log rotáció konfigurációját.
        
        Returns:
            Dict[str, Any]: A log rotáció konfigurációja
        """
        return self.config.get("log_rotation", {"max_size_mb": 10, "backup_count": 5})
    
    def update_log_path(self, service_name: str, log_paths: List[str]) -> bool:
        """
        Frissíti egy szolgáltatás log útvonalait.
        
        Args:
            service_name: A szolgáltatás neve
            log_paths: Az új log útvonalak
            
        Returns:
            bool: True, ha sikeres volt a frissítés, egyébként False
        """
        if "log_paths" not in self.config:
            self.config["log_paths"] = {}
        
        self.config["log_paths"][service_name] = log_paths
        return self.save_config()
    
    def update_container_name(self, service_name: str, container_name: str) -> bool:
        """
        Frissíti egy szolgáltatás konténer nevét.
        
        Args:
            service_name: A szolgáltatás neve
            container_name: Az új konténer név
            
        Returns:
            bool: True, ha sikeres volt a frissítés, egyébként False
        """
        if "containers" not in self.config:
            self.config["containers"] = {}
        
        self.config["containers"][service_name] = container_name
        return self.save_config()

class ColorManager:
    """
    Színkezelő osztály.
    
    Ez az osztály felelős a színkódok kezeléséért különböző környezetekben.
    """
    
    # ANSI színkódok
    COLORS = {
        "debug": "\033[36m",      # Cián
        "info": "\033[32m",       # Zöld
        "warning": "\033[33m",    # Sárga
        "error": "\033[31m",      # Piros
        "critical": "\033[35m",   # Magenta
        "streamlit": "\033[94m",  # Kék
        "backend": "\033[92m",    # Világos zöld
        "db": "\033[93m",         # Világos sárga
        "reset": "\033[0m"        # Alapértelmezett
    }
    
    def __init__(self, force_color: bool = False, no_color: bool = False):
        """
        Inicializálja a színkezelőt.
        
        Args:
            force_color: Kényszeríti a színek használatát
            no_color: Letiltja a színek használatát
        """
        self.force_color = force_color
        self.no_color = no_color
        self.colors_enabled = self._check_color_support()
    
    def _check_color_support(self) -> bool:
        """
        Ellenőrzi a színkódolás támogatását.
        
        Returns:
            bool: True, ha a színkódolás támogatott, egyébként False
        """
        if self.no_color:
            return False
        if self.force_color:
            return True
            
        # Ellenőrizzük a terminál képességeit
        is_a_tty = hasattr(os, 'isatty') and os.isatty(1)
        dumb_term = os.environ.get('TERM') == 'dumb'
        no_color_env = 'NO_COLOR' in os.environ
        
        return is_a_tty and not dumb_term and not no_color_env
    
    def colorize(self, text: str, color_name: str) -> str:
        """
        Színezi a szöveget, ha támogatott.
        
        Args:
            text: A színezendő szöveg
            color_name: A szín neve
            
        Returns:
            str: A színezett szöveg
        """
        if not self.colors_enabled or color_name not in self.COLORS:
            return text
            
        return f"{self.COLORS[color_name]}{text}{self.COLORS['reset']}"

class ContainerManager:
    """
    Docker konténerek kezeléséért felelős osztály.
    
    Ez az osztály lehetővé teszi a Docker konténerek logfájljainak elérését,
    valamint a konténereken belüli parancsok végrehajtását.
    """
    
    def __init__(self, config_file: Optional[str] = None, force_color: bool = False, no_color: bool = False):
        """
        Inicializálja a konténer menedzsert.
        
        Args:
            config_file: A konfigurációs fájl elérési útja
            force_color: Kényszeríti a színek használatát
            no_color: Letiltja a színek használatát
        """
        self.config = LogConfig(config_file)
        self.color_manager = ColorManager(force_color=force_color, no_color=no_color or not self.config.use_colors())
        
        # Létrehozzuk a log könyvtárat, ha nem létezik
        log_dir = Path("debug/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        self.CONTAINERS = {
            service_name: self.config.get_container_name(service_name)
            for service_name in ["streamlit", "backend", "db"]
        }
        
        self.container_statuses = {}
        self.docker_available = self._check_docker_available()
        self.refresh_container_statuses()
    
    def _check_docker_available(self) -> bool:
        """
        Ellenőrzi, hogy a Docker parancs elérhető-e a rendszerben.
        
        Returns:
            bool: True, ha a Docker elérhető, egyébként False
        """
        try:
            # Először ellenőrizzük a környezeti változókat
            docker_host = os.environ.get('DOCKER_HOST', '')
            if docker_host:
                print(f"Docker Host beállítva: {docker_host}")
            
            # Docker létezésének ellenőrzése
            result = subprocess.run(['which', 'docker'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  check=False)
            
            if result.returncode != 0:
                print("Docker CLI nem található a rendszeren.")
                return False
            
            # Socket fájl ellenőrzése, ha Unix socketet használ
            socket_path = '/var/run/docker.sock'
            if docker_host.startswith('unix://'):
                socket_path = docker_host.replace('unix://', '')
            
            if os.path.exists(socket_path):
                print(f"Docker socket fájl elérhető: {socket_path}")
                # Ellenőrizzük a fájl jogosultságokat
                try:
                    socket_stat = os.stat(socket_path)
                    print(f"Socket jogosultságok: uid={socket_stat.st_uid}, gid={socket_stat.st_gid}, mode={oct(socket_stat.st_mode)}")
                    # Ellenőrizzük az aktuális felhasználó és csoport azonosítót
                    current_uid = os.getuid()
                    current_gid = os.getgid()
                    print(f"Aktuális felhasználó: uid={current_uid}, gid={current_gid}")
                except Exception as e:
                    print(f"Hiba a socket jogosultságok ellenőrzésekor: {str(e)}")
            else:
                print(f"Docker socket fájl nem elérhető: {socket_path}")
                
            # Docker démon elérhetőségének ellenőrzése
            result = subprocess.run(['docker', 'info'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  check=False)
            
            if result.returncode != 0:
                print(f"Docker démon nem érhető el. Hibaüzenet: {result.stderr}")
                return False
                
            print("Docker démon sikeresen elérve.")
            return True
        except Exception as e:
            print(f"Hiba a Docker elérhetőség ellenőrzésekor: {str(e)}")
            return False
    
    def refresh_container_statuses(self) -> Dict[str, Dict[str, Any]]:
        """
        Frissíti a konténerek állapotait.
        
        Returns:
            Dict[str, Dict[str, Any]]: A konténerek állapotai
        """
        if not self.docker_available:
            # Ha a Docker nem elérhető, használjuk a hardcoded értékeket
            for service, container_id in self.CONTAINERS.items():
                self.container_statuses[service] = {
                    "id": container_id,
                    "name": f"{service}-container",
                    "status": "Unknown (Docker not available)",
                    "is_running": False  # Feltételezzük, hogy nem fut
                }
            return self.container_statuses
            
        try:
            output = subprocess.check_output(['docker', 'ps', '-a', '--format', '{{.ID}}\t{{.Names}}\t{{.Status}}'], 
                                             text=True)
            
            container_statuses = {}
            
            for line in output.strip().split('\n'):
                if not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) >= 3:
                    container_id, container_name, status = parts
                    
                    # Állapot értelmezése
                    is_running = "Up" in status
                    
                    # Keressük meg melyik szolgáltatásunkról van szó
                    service_name = None
                    for service, service_container_name in self.CONTAINERS.items():
                        if service_container_name in container_name:
                            service_name = service
                            break
                    
                    if service_name:
                        container_statuses[service_name] = {
                            "id": container_id,
                            "name": container_name,
                            "status": status,
                            "is_running": is_running
                        }
            
            self.container_statuses = container_statuses
            return container_statuses
            
        except subprocess.CalledProcessError as e:
            error_msg = f"Hiba a konténer állapotok lekérdezésekor: {str(e)}"
            print(self.color_manager.colorize(error_msg, "error"))
            return {}
    
    def get_container_logs(self, 
                          service_name: str, 
                          tail: Optional[int] = None, 
                          since: Optional[str] = None,
                          follow: bool = False,
                          until: Optional[str] = None) -> str:
        """
        Lekéri egy konténer logjait.
        
        Args:
            service_name: A szolgáltatás neve
            tail: A visszaadandó log sorok száma
            since: Naplók visszaadása ettől az időponttól kezdve (pl. "2024-01-01T00:00:00")
            follow: A logok folyamatos követése (csak interaktív módban működik)
            until: Naplók visszaadása eddig az időpontig (pl. "2024-01-01T00:00:00")
            
        Returns:
            str: A konténer logjai
        """
        container_id = self.CONTAINERS.get(service_name)
        if not container_id:
            raise ValueError(f"Ismeretlen szolgáltatás: {service_name}")
        
        if not self.docker_available:
            # Ha a Docker nem elérhető, próbáljuk meg közvetlenül a log fájlokat olvasni
            return self._read_log_files_directly(service_name)
        
        cmd = ['docker', 'logs']
        
        if tail:
            cmd.extend(['--tail', str(tail)])
        
        if since:
            cmd.extend(['--since', since])
        
        if until:
            cmd.extend(['--until', until])
        
        if follow:
            cmd.append('--follow')
        
        cmd.append(container_id)
        
        try:
            if follow:
                # Folyamatos követés esetén valós időben írjuk ki a logokat
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,  # Soronkénti pufferelés
                    universal_newlines=True
                )
                
                try:
                    for line in process.stdout:
                        line = line.strip()
                        if line:
                            # Log szint meghatározása a színezéshez
                            log_level = "info"
                            if "ERROR" in line.upper() or "EXCEPTION" in line.upper():
                                log_level = "error"
                            elif "WARNING" in line.upper() or "WARN" in line.upper():
                                log_level = "warning"
                            elif "DEBUG" in line.upper():
                                log_level = "debug"
                            
                            # Formázott kiírás
                            service_prefix = self.color_manager.colorize(f"[{service_name}]", service_name)
                            log_line = self.color_manager.colorize(line, log_level)
                            print(f"{service_prefix} {log_line}")
                            
                except KeyboardInterrupt:
                    print("\nLog követés megszakítva.")
                finally:
                    process.terminate()
                
                return ""
            else:
                # Egyszerű log lekérés
                output = subprocess.check_output(cmd, text=True)
                return output
        except subprocess.CalledProcessError as e:
            error_msg = f"Hiba a konténer logok lekérésekor: {str(e)}"
            print(self.color_manager.colorize(error_msg, "error"))
            return ""
    
    def _read_log_files_directly(self, service_name: str) -> str:
        """
        Közvetlenül olvassa be a log fájlokat, ha a Docker nem elérhető.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            str: A log fájlok tartalma
        """
        log_paths = self.config.get_log_paths(service_name)
        log_content = ""
        
        if not log_paths:
            print(self.color_manager.colorize(
                f"Nincs konfigurált log útvonal a következő szolgáltatáshoz: {service_name}",
                "warning"
            ))
            return log_content
        
        for path in log_paths:
            path_obj = Path(path)
            
            if path_obj.is_file():
                try:
                    with open(path_obj, 'r', encoding='utf-8', errors='replace') as f:
                        content = f.read()
                        log_content += content + "\n"
                        print(self.color_manager.colorize(
                            f"Log fájl beolvasva: {path} ({len(content.splitlines())} sor)",
                            "info"
                        ))
                except Exception as e:
                    print(self.color_manager.colorize(
                        f"Hiba a log fájl olvasásakor ({path}): {str(e)}",
                        "error"
                    ))
            elif path_obj.is_dir():
                try:
                    log_files = list(path_obj.glob("*.log"))
                    if not log_files:
                        print(self.color_manager.colorize(
                            f"Nem található log fájl a következő könyvtárban: {path}",
                            "warning"
                        ))
                    
                    for file_path in log_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                                content = f.read()
                                log_content += content + "\n"
                                print(self.color_manager.colorize(
                                    f"Log fájl beolvasva: {file_path} ({len(content.splitlines())} sor)",
                                    "info"
                                ))
                        except Exception as e:
                            print(self.color_manager.colorize(
                                f"Hiba a log fájl olvasásakor ({file_path}): {str(e)}",
                                "error"
                            ))
                except Exception as e:
                    print(self.color_manager.colorize(
                        f"Hiba a log könyvtár olvasásakor ({path}): {str(e)}",
                        "error"
                    ))
            else:
                print(self.color_manager.colorize(
                    f"A megadott log útvonal nem létezik: {path}",
                    "warning"
                ))
        
        return log_content
    
    def exec_command(self, service_name: str, command: List[str]) -> Tuple[int, str, str]:
        """
        Parancs végrehajtása egy konténeren belül.
        
        Args:
            service_name: A szolgáltatás neve
            command: A végrehajtandó parancs és argumentumai
            
        Returns:
            Tuple[int, str, str]: Visszatérési kód, stdout, stderr
        """
        container_id = self.CONTAINERS.get(service_name)
        if not container_id:
            raise ValueError(f"Ismeretlen szolgáltatás: {service_name}")
        
        if not self.docker_available:
            # Ha a Docker nem elérhető, próbáljuk meg közvetlenül végrehajtani a parancsot
            try:
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                stdout, stderr = process.communicate()
                return process.returncode, stdout, stderr
            except Exception as e:
                return 1, "", str(e)
        
        cmd = ['docker', 'exec', container_id] + command
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate()
            return process.returncode, stdout, stderr
            
        except subprocess.CalledProcessError as e:
            return e.returncode, "", str(e)
    
    def get_log_files(self, service_name: str) -> List[str]:
        """
        Lekéri egy konténer log fájljainak listáját.
        
        Args:
            service_name: A szolgáltatás neve
            
        Returns:
            List[str]: A log fájlok listája
        """
        if not self.docker_available:
            # Ha a Docker nem elérhető, használjuk a közvetlen útvonalakat
            log_paths = {
                "streamlit": ["/app/logs", "/app/.streamlit/logs"],
                "backend": ["/app/logs", "/var/log/backend"],
                "db": ["/var/log/postgresql", "/var/lib/postgresql/data/log"]
            }
            
            paths = log_paths.get(service_name, ["/app/logs"])
            log_files = []
            
            for path in paths:
                if os.path.isdir(path):
                    try:
                        for file in os.listdir(path):
                            if file.endswith('.log'):
                                log_files.append(os.path.join(path, file))
                    except Exception as e:
                        print(f"Hiba a log könyvtár olvasásakor ({path}): {str(e)}")
                elif os.path.isfile(path):
                    log_files.append(path)
            
            return log_files
        
        if service_name == "streamlit":
            # Streamlit log fájlok
            return self._find_files_in_container(service_name, [
                "/app/logs",
                "/app/.streamlit"
            ])
        elif service_name == "backend":
            # Backend log fájlok
            return self._find_files_in_container(service_name, [
                "/app/logs",
                "/var/log"
            ])
        elif service_name == "db":
            # PostgreSQL log fájlok
            return self._find_files_in_container(service_name, [
                "/var/log/postgresql",
                "/var/lib/postgresql/data/log"
            ])
        else:
            raise ValueError(f"Ismeretlen szolgáltatás: {service_name}")
    
    def _find_files_in_container(self, service_name: str, paths: List[str]) -> List[str]:
        """
        Megkeresi a log fájlokat egy konténerben.
        
        Args:
            service_name: A szolgáltatás neve
            paths: A keresési útvonalak
            
        Returns:
            List[str]: A talált fájlok listája
        """
        log_files = []
        
        for path in paths:
            # Ellenőrizzük, hogy létezik-e a könyvtár
            returncode, stdout, stderr = self.exec_command(
                service_name, ["test", "-d", path, "&&", "echo", "exists"]
            )
            
            if "exists" not in stdout:
                continue
            
            # Keressük a log fájlokat
            returncode, stdout, stderr = self.exec_command(
                service_name, ["find", path, "-type", "f", "-name", "*.log"]
            )
            
            if returncode == 0 and stdout:
                log_files.extend(stdout.strip().split("\n"))
        
        return log_files
    
    def read_log_file(self, service_name: str, log_file: str) -> str:
        """
        Beolvassa egy konténer log fájlját.
        
        Args:
            service_name: A szolgáltatás neve
            log_file: A log fájl elérési útja
            
        Returns:
            str: A log fájl tartalma
        """
        if not self.docker_available:
            # Ha a Docker nem elérhető, próbáljuk meg közvetlenül olvasni a fájlt
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"Hiba a log fájl olvasásakor ({log_file}): {str(e)}")
                return ""
        
        returncode, stdout, stderr = self.exec_command(
            service_name, ["cat", log_file]
        )
        
        if returncode == 0:
            return stdout
        else:
            print(f"Hiba a log fájl olvasásakor: {stderr}")
            return ""
    
    def rotate_log_file(self, service_name: str, log_file: str) -> bool:
        """
        Elforgatja (rotálja) egy konténer log fájlját.
        
        Args:
            service_name: A szolgáltatás neve
            log_file: A log fájl elérési útja
            
        Returns:
            bool: True, ha sikeres, egyébként False
        """
        if not self.docker_available:
            # Ha a Docker nem elérhető, próbáljuk meg közvetlenül rotálni a fájlt
            try:
                if os.path.isfile(log_file):
                    timestamp = time.strftime("%Y%m%d%H%M%S")
                    new_log_file = f"{log_file}.{timestamp}"
                    os.rename(log_file, new_log_file)
                    with open(log_file, 'w', encoding='utf-8') as f:
                        f.write("")
                    return True
                else:
                    print(f"A log fájl nem létezik: {log_file}")
                    return False
            except Exception as e:
                print(f"Hiba a log fájl rotálásakor ({log_file}): {str(e)}")
                return False
        
        # Ellenőrizzük, hogy létezik-e a fájl
        returncode, stdout, stderr = self.exec_command(
            service_name, ["test", "-f", log_file, "&&", "echo", "exists"]
        )
        
        if "exists" not in stdout:
            print(f"A log fájl nem létezik: {log_file}")
            return False
        
        # Átnevezzük a fájlt
        timestamp = time.strftime("%Y%m%d%H%M%S")
        new_log_file = f"{log_file}.{timestamp}"
        
        returncode, stdout, stderr = self.exec_command(
            service_name, ["mv", log_file, new_log_file]
        )
        
        if returncode != 0:
            print(f"Hiba a log fájl rotálásakor: {stderr}")
            return False
        
        # Létrehozunk egy új üres log fájlt
        returncode, stdout, stderr = self.exec_command(
            service_name, ["touch", log_file]
        )
        
        if returncode != 0:
            print(f"Hiba az új log fájl létrehozásakor: {stderr}")
            return False
        
        return True 