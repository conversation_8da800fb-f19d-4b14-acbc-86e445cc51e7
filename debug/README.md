# Hibakeresési Rendszer - Mezőgazdasági Termékkezelő Rendszer

## Áttekintés

A hibakeresési rendszer a Mezőgazdasági Termékkezelő Rendszer logok kezeléséért, elemzéséért és megjelenítéséért felelős. A rendszer a következő főbb komponensekből áll:

- Log kezelés és elemzés
- Docker konténer kezelés
- Docker Compose log kezelés
- Interaktív parancssori felület

## Modulok

### Logger (`logger.py`)

Strukturált naplózási rendszer, amely JSON formátumú, időbélyeggel ellátott, nyomkövethető log bejegyzéseket biztosít:

- JSON alapú strukturált naplózás
- Időbélyegek minden bejegyzésben
- Trace ID-k a hibák nyomkövetésére
- Kontextuális adatok tárolása
- Színkódolt konzol kimenet
- Log fájlba írás támogatása

### Kont<PERSON><PERSON> (`container.py`)

Docker konténerek kezeléséért felelős modul:

- Konténer logok lekérdezése
- Konténeren belüli parancsok végrehajtása
- Konténerek állapotának lekérdezése
- Konténerek indítása, leállítása, újraindítása
- Log fájlok kezelése a konténereken belül
- Log fájlok rotálása

### Log Elemző (`analyzer.py`)

Log elemző modul a hibák azonosítására és kategorizálására:

- Mintázat felismerés a logokban
- Hibakategóriák azonosítása (API, adatbázis, autentikáció, stb.)
- Óránkénti hibastatisztika készítése
- Problémák csoportosítása
- Javaslatok generálása

### Docker Compose Kezelő (`debug_compose.py`)

Docker Compose specifikus log kezelő modul:

- Konténerek logjainak valós idejű követése
- Logok színkódolt megjelenítése
- Strukturált log formázás
- Különböző típusú hibaüzenetek elkülönítése
- Konténerek állapotának lekérdezése
- Konténer műveletek végrehajtása

### Parancssori Interfész (`cli.py`)

Fő parancssori interfész a hibakeresési rendszerhez:

- Logok megtekintése és szűrése
- Logok elemzése
- Docker Compose műveletek
- Konténer műveletek
- Debug mód kezelése

## Funkciók

### Log Kezelés

- Logok megtekintése és szűrése: A logok most már a Docker konténerekből közvetlenül elérhetők
- Logok elemzése és statisztikák készítése: A logok elemzése most már súlyosság szerint történik
- Logok keresése különböző szűrési feltételek alapján
- Log fájlok rotálása és archiválása

### Docker Konténer Kezelés

- Konténerek állapotának megjelenítése
- Konténer logok lekérdezése
- Konténer műveletek (újraindítás, leállítás, indítás)
- Konténer logok valós idejű követése

### Docker Compose Kezelés

- Konténerek logjainak valós idejű követése (elkülönítve)
- Konténerek állapotának megjelenítése
- Konténer műveletek (újraindítás, leállítás, indítás)
- Debug mód indítása/leállítása
- Logok szűrése és formázása
- Színkódolt log megjelenítés

## Használat

### Parancssori Interfész

A rendszer parancssori interfészt biztosít a következő parancsokkal:

```bash
# Logok megtekintése
python -m debug.cli logs streamlit             # Streamlit konténer logjai
python -m debug.cli logs backend --tail 100    # Backend konténer utolsó 100 logja
python -m debug.cli logs db --json             # Adatbázis konténer logjai JSON formátumban

# Logok elemzése
python -m debug.cli analyze streamlit          # Streamlit konténer logjainak elemzése
python -m debug.cli analyze backend --json     # Backend konténer logjainak elemzése JSON formátumban

# Logok valós idejű követése
python -m debug.cli live streamlit             # Streamlit konténer logjainak követése
python -m debug.cli live backend --filter "error"  # Backend konténer hibák követése

# Docker Compose műveletek
python -m debug.cli compose logs streamlit     # Streamlit konténer logjai
python -m debug.cli compose status             # Konténerek állapota
python -m debug.cli compose restart streamlit  # Streamlit konténer újraindítása
python -m debug.cli compose stop backend       # Backend konténer leállítása
python -m debug.cli compose start db           # Adatbázis konténer indítása

# Debug mód kezelése
python -m debug.cli debug on                   # Debug mód bekapcsolása
python -m debug.cli debug off                  # Debug mód kikapcsolása
```

### Interaktív Menü

A rendszer interaktív menüt is biztosít a `debug_menu.sh` script segítségével:

```bash
./debug_menu.sh
```

A menü a következő opciókat tartalmazza:

1. Logok megtekintése
2. Logok elemzése
3. Docker Compose Kezelése
   - Konténerek logjainak valós idejű követése
   - Konténerek állapotának megjelenítése
   - Konténer műveletek
   - Debug mód indítása/leállítása
4. Kilépés

## Telepítés

1. Függőségek telepítése:

```bash
pip install -r requirements.txt
```

2. Jogosultságok beállítása:

```bash
chmod +x debug_menu.sh
```

## Konfiguráció

A rendszer a következő konfigurációs lehetőségeket támogatja:

- Log szintek beállítása (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Log formátum beállítása (szöveg, JSON)
- Log könyvtár beállítása
- Docker konténer azonosítók beállítása
- Színkódolás beállítása

A konfigurációs beállítások a `debug/config/log_paths.json` fájlban találhatók, amely első indításkor automatikusan létrejön.

## Fejlesztés

### Új Funkció Hozzáadása

1. Implementáld az új funkcionalitást a megfelelő modulban
2. Frissítsd a parancssori interfészt
3. Frissítsd a menüt
4. Frissítsd a dokumentációt

### Kódolási Szabványok

- PEP 8 követése
- Type hints használata minden függvény paraméternél és visszatérési értéknél
- Docstring-ek használata minden osztálynál és függvénynél
- Clean Code elvek követése

### Tesztelés

A rendszer teszteléséhez használd a következő parancsokat:

```bash
# Unit tesztek futtatása
python -m pytest tests/

# Kód minőség ellenőrzése
flake8 debug/
mypy debug/
```

## Hibaelhárítás

### Gyakori Problémák

1. Docker nem elérhető
   - Ellenőrizd, hogy a Docker szolgáltatás fut-e
   - Ellenőrizd a Docker jogosultságokat

2. Log fájlok nem olvashatók
   - Ellenőrizd a fájl jogosultságokat
   - Ellenőrizd a log könyvtár létezését

3. Konténerek nem elérhetők
   - Ellenőrizd a konténer azonosítókat
   - Ellenőrizd a Docker Compose állapotát

### Naplózás

A rendszer saját logjai a `debug/logs` könyvtárban találhatók:

- `debug_cli.log`: Parancssori interfész logjai
- `debug_compose.log`: Docker Compose műveletek logjai
- `container.log`: Konténer műveletek logjai
- `analyzer.log`: Log elemzés logjai

## Licenc

Ez a projekt a belső fejlesztésű Mezőgazdasági Termékkezelő Rendszer része, és annak licencfeltételei vonatkoznak rá.