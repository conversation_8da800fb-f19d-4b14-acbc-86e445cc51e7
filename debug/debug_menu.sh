#!/bin/bash

# Debug menü script a Mezőgazdasági Termékkezelő Rendszer hibakeresési rendszeréhez
# Ez a script lehetővé teszi a debug rendszer funkcióinak indítását egy menüből

# Színek definiálása
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Python modul elérési út beállítása
export PYTHONPATH="/app:${PYTHONPATH}"

# Python parancs definiálása
PYTHON_CMD="python3"

# Ellenőrizzük, hogy a Python elérhető-e
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo -e "${RED}Hiba: A Python3 nem található! Kérlek telepítsd a Python3-at.${NC}"
    exit 1
fi

# Log fájlok elérési útjai
DEBUG_CLI_LOG="debug/logs/debug_cli.log"
DEBUG_COMPOSE_LOG="debug/logs/debug_compose.log"
CONTAINER_LOG="debug/logs/container.log"
ANALYZER_LOG="debug/logs/analyzer.log"

# Segédfüggvény a Python parancsok futtatásához
function run_python_command() {
    # Paraméterek tömbként kezelése a shell injection elkerülése érdekében
    local -a cmd=("$PYTHON_CMD" "-m" "debug.cli" "--color")
    # Paraméterek hozzáadása
    for arg in "$@"; do
        cmd+=("$arg")
    done
    
    echo -e "${CYAN}Parancs végrehajtása: ${cmd[*]}${NC}"
    
    # Parancs végrehajtása és hibaellenőrzés
    if ! "${cmd[@]}"; then
        local exit_code=$?
        echo -e "${RED}Hiba történt a parancs végrehajtása során! (exit kód: $exit_code)${NC}"
        return 1
    fi
    
    return 0
}

# Főmenü megjelenítése
function show_main_menu() {
    clear
    echo -e "${BLUE}=== Mezőgazdasági Termékkezelő Rendszer - Debug Menü ===${NC}"
    echo -e "${GREEN}1.${NC} Logok megtekintése"
    echo -e "${GREEN}2.${NC} Logok elemzése"
    echo -e "${GREEN}3.${NC} Logok követése"
    echo -e "${GREEN}4.${NC} Konténerek állapota"
    echo -e "${GREEN}5.${NC} Kilépés"
    echo -e "${YELLOW}Válassz egy opciót (1-5):${NC} "
}

# Főprogram
while true; do
    show_main_menu
    read -r choice
    
    case $choice in
        1)
            echo -e "${CYAN}Melyik szolgáltatás logjait szeretnéd megtekinteni?${NC}"
            echo -e "${GREEN}1.${NC} Backend"
            echo -e "${GREEN}2.${NC} Streamlit"
            echo -e "${GREEN}3.${NC} Adatbázis"
            read -r service_choice
            
            case $service_choice in
                1) run_python_command logs backend ;;
                2) run_python_command logs streamlit ;;
                3) run_python_command logs db ;;
                *) echo -e "${RED}Érvénytelen választás!${NC}" ;;
            esac
            ;;
        2)
            echo -e "${CYAN}Melyik szolgáltatás logjait szeretnéd elemzeni?${NC}"
            echo -e "${GREEN}1.${NC} Backend"
            echo -e "${GREEN}2.${NC} Streamlit"
            echo -e "${GREEN}3.${NC} Adatbázis"
            read -r service_choice
            
            case $service_choice in
                1) run_python_command analyze backend ;;
                2) run_python_command analyze streamlit ;;
                3) run_python_command analyze db ;;
                *) echo -e "${RED}Érvénytelen választás!${NC}" ;;
            esac
            ;;
        3)
            echo -e "${CYAN}Melyik szolgáltatás logjait szeretnéd követni?${NC}"
            echo -e "${GREEN}1.${NC} Backend"
            echo -e "${GREEN}2.${NC} Streamlit"
            echo -e "${GREEN}3.${NC} Adatbázis"
            read -r service_choice
            
            case $service_choice in
                1) run_python_command follow backend ;;
                2) run_python_command follow streamlit ;;
                3) run_python_command follow db ;;
                *) echo -e "${RED}Érvénytelen választás!${NC}" ;;
            esac
            ;;
        4)
            run_python_command containers
            ;;
        5)
            echo -e "${GREEN}Kilépés...${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Érvénytelen választás!${NC}"
            ;;
    esac
    
    echo -e "${YELLOW}Nyomj ENTER-t a folytatáshoz...${NC}"
    read -r
done 