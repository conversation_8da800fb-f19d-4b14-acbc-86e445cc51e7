#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mezőgazdasági Termékkezelő Rendszer Hibakeresési Rendszer.

Ez a csomag biztosítja a hibakeresési infrastruktúrát a Mezőgazdasági Termékkezelő
Rendszerhez, amely lehetővé teszi a különböző konténerek logjainak elemzését,
szűrését és a problémák azonosítását.

A főbb modulok:
    - logger: Strukturált JSON alapú naplózás
    - container: Konténerek kezelése és log fájlok elérése
    - analyzer: Log elemzés és probléma azonosítás
    - cli: Parancssori eszközök a rendszer használatához

Használat:
    # Log lekérdezése
    python -m debug.cli logs streamlit

    # Log elemzése
    python -m debug.cli analyze backend

    # Élő log követés
    python -m debug.cli live streamlit

    # Keresés a logokban
    python -m debug.cli search db "error"
"""

from debug.logger import Logger, LogLevel
from debug.container import ContainerManager
from debug.analyzer import LogAnalyzer

__all__ = [
    'Logger',
    'LogLevel',
    'ContainerManager',
    'LogAnalyzer'
]

__version__ = '1.0.0' 