#!/usr/bin/env python3
"""
Verify that the price trend API fixes are working correctly
"""

def verify_api_parameters():
    """Check that no unsupported date_field parameters remain in the code"""
    import os
    import re
    
    print("🔍 Verifying API parameter fixes...")
    
    # Files to check
    files_to_check = [
        "streamlit_app/pages/operator/offer_management/advanced_price_trend_v2.py",
        "streamlit_app/pages/operator/offer_management/simplified_enhanced_dark_theme.py"
    ]
    
    issues_found = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for problematic patterns
            if "'date_field': date_type" in content:
                issues_found.append(f"{file_path}: Still contains unsupported 'date_field' parameter")
            
            if "date_field.*date_type" in content:
                issues_found.append(f"{file_path}: Contains suspicious date_field usage")
            
            # Check for nested expanders
            expander_pattern = r'with st\.expander.*expanded=False.*:\s*.*with st\.expander'
            if re.search(expander_pattern, content, re.DOTALL):
                issues_found.append(f"{file_path}: May contain nested expanders")
                
            print(f"✅ Checked {file_path}")
        else:
            print(f"⚠️ File not found: {file_path}")
    
    if issues_found:
        print("\n❌ Issues found:")
        for issue in issues_found:
            print(f"  - {issue}")
        return False
    else:
        print("\n✅ All checks passed! API fixes look good.")
        return True

def verify_manual_filtering_function():
    """Check that the manual filtering function is properly implemented"""
    try:
        # Try to import the function
        import sys
        sys.path.append('streamlit_app/pages/operator/offer_management')
        
        from advanced_price_trend_v2 import get_offers_with_custom_date_filter
        
        print("✅ Manual filtering function imported successfully")
        
        # Check function signature
        import inspect
        sig = inspect.signature(get_offers_with_custom_date_filter)
        params = list(sig.parameters.keys())
        
        if 'params' in params and 'date_field' in params:
            print("✅ Function signature looks correct")
            return True
        else:
            print(f"❌ Function signature incorrect: {params}")
            return False
            
    except ImportError as e:
        print(f"❌ Could not import manual filtering function: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking function: {e}")
        return False

def verify_debug_information():
    """Check that debug information is updated"""
    print("\n🔍 Verifying debug information updates...")
    
    files_to_check = [
        "streamlit_app/pages/operator/offer_management/advanced_price_trend_v2.py",
        "streamlit_app/pages/operator/offer_management/simplified_enhanced_dark_theme.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for updated debug messages
            if "API NEM támogatja" in content or "API doesn't support" in content:
                print(f"✅ {file_path}: Contains API limitation warnings")
            else:
                print(f"⚠️ {file_path}: Missing API limitation warnings")
                
            if "Manual filtering" in content or "manuális szűrés" in content.lower():
                print(f"✅ {file_path}: Contains manual filtering explanations")
            else:
                print(f"⚠️ {file_path}: Missing manual filtering explanations")

def main():
    """Run all verification checks"""
    print("🔧 Ártrend Elemzés Fix Verification")
    print("=" * 50)
    
    all_good = True
    
    # Check 1: API parameters
    if not verify_api_parameters():
        all_good = False
    
    print()
    
    # Check 2: Manual filtering function
    if not verify_manual_filtering_function():
        all_good = False
    
    # Check 3: Debug information
    verify_debug_information()
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("✅ The price trend API fixes should be working correctly")
        print("\nNext steps:")
        print("1. Restart the Streamlit app")
        print("2. Test the price trend analysis with different date field options")
        print("3. Check that delivery_date works normally")
        print("4. Verify that created_at/confirmed_at show appropriate warnings")
    else:
        print("❌ SOME ISSUES FOUND")
        print("Please review and fix the issues above before testing")

if __name__ == "__main__":
    main()