# Application Specification: Termelo v5

## 1. Executive Summary

This document provides a detailed architectural and operational overview of the Termelo v5 application.

**Purpose:** The application is a comprehensive platform for managing business offers, targeted at specific user roles within an organization. It facilitates the creation, management, and analysis of offers, with a strong emphasis on price trend analysis and role-based access control.

**Key Stakeholders:**
*   **Producers:** Users responsible for creating and managing offers.
*   **Operators:** Users who oversee and manage the offer lifecycle.
*   **Administrators:** Users responsible for system configuration and user management.
*   **Business Analysts:** Users who leverage the platform's data for market analysis and strategic planning.

**Business Goals:**
*   Streamline the offer management process, reducing manual effort and errors.
*   Provide intelligent, data-driven insights into price trends to support decision-making.
*   Enhance collaboration and communication between different user roles.
*   Ensure a secure and reliable platform for sensitive business data.

## 2. High-Level Architecture Diagram

The system is designed with a modern client-server architecture, containerized for portability and scalability.

```
+----------------------+        +-------------------------+        +-----------------+
|      End User        |        |      Web Browser        |        |   Mobile Client |
+----------------------+        +-------------------------+        +-----------------+
           |                                |                                |
           |                                v                                v
+------------------------------------------------------------------------------------+
|                                   Frontend Layer                                   |
|                          (Streamlit Application Server)                            |
+------------------------------------------------------------------------------------+
           |                                      ^
           | (HTTP/S API Calls)                   | (HTML/CSS/JS)
           v                                      |
+------------------------------------------------------------------------------------+
|                                    Backend Layer                                   |
|                               (FastAPI RESTful API)                                |
+------------------------------------------------------------------------------------+
           |                                      ^
           | (SQL Queries)                        | (Data)
           v                                      |
+------------------------------------------------------------------------------------+
|                                  Data Store Layer                                  |
|                               (PostgreSQL Database)                                |
+------------------------------------------------------------------------------------+
```

**Description:**
*   **Frontend:** A Python-based web application built with the **Streamlit** framework. It serves the user interface and communicates with the backend via RESTful API calls.
*   **Backend:** A RESTful API built with the **FastAPI** framework. It handles all business logic, data processing, and communication with the database.
*   **Database:** A **PostgreSQL** relational database, managed by **SQLAlchemy** ORM and **Alembic** for schema migrations.
*   **Deployment:** The entire application stack is containerized using **Docker** and orchestrated with **docker-compose.yml**, enabling consistent environments for development, testing, and production.

## 3. Detailed Component Breakdown

### a. Backend (`app/`)

*   **Responsibility:** Core business logic, API endpoints, and database interactions.
*   **Dependencies:** FastAPI, SQLAlchemy, Pydantic, Alembic, python-jose[cryptography], passlib[bcrypt].
*   **Key Modules & Data Flow:**
    *   `main.py`: Application entry point, mounts the main API router.
    *   `api/api.py`: Aggregates all API endpoints from `api/endpoints/`.
    *   `api/endpoints/`: Defines API routes for different resources (e.g., users, offers, notifications). Each endpoint uses Pydantic schemas for request/response validation.
    *   `crud/`: Contains functions for Create, Read, Update, Delete operations, abstracting direct database interaction from the API layer.
    *   `models/`: Defines SQLAlchemy ORM models, representing database tables.
    *   `schemas/`: Defines Pydantic models for data validation and serialization, ensuring a clear contract for the API.
    *   `db/`: Manages database sessions and engine configuration.
    *   `core/security.py`: Implements password hashing, JWT creation, and token validation.
    *   **Data Flow:** An incoming HTTP request hits an endpoint in `api/endpoints`. The endpoint validates the request body using a Pydantic schema, calls a function in `crud/` to interact with the database, and returns a response serialized by another Pydantic schema.

### b. Frontend (`streamlit_app/`)

*   **Responsibility:** User interface and user experience.
*   **Dependencies:** Streamlit, Requests, Pandas.
*   **Key Modules & Data Flow:**
    *   `main.py`: The main entry point for the Streamlit application.
    *   `pages/`: Contains the different pages of the application, often structured by user role (e.g., `admin`, `producer`, `operator`).
    *   `api_client.py`: A dedicated client to handle all communication with the backend FastAPI. It abstracts away the details of making HTTP requests.
    *   `components/`: Reusable Streamlit components used across different pages.
    *   `utils/`: Utility functions for the frontend, such as data formatting or session state management.
    *   **Data Flow:** A user interacts with a page. The page's script calls `api_client.py` to fetch or send data to the backend. The response data is then rendered on the page using Streamlit's UI elements.

### c. Database Migrations (`migrations/`)

*   **Responsibility:** Managing the evolution of the database schema.
*   **Technology:** Alembic.
*   **Key Files:**
    *   `env.py`: Alembic runtime configuration.
    *   `versions/`: Contains individual, versioned migration scripts.

## 4. Data Architecture

*   **Database System:** PostgreSQL.
*   **ORM:** SQLAlchemy.
*   **Key Entities (Models):**
    *   `User`: Stores user information, including hashed passwords and roles.
    *   `Offer`: The central entity, containing details about business offers.
    *   `UserSavedFilter`: Allows users to save complex filter configurations for later use.
    *   `Notification`: Stores notifications for users.
*   **Relationships:**
    *   One-to-Many: A `User` can have multiple `UserSavedFilter`s.
    *   One-to-Many: A `User` can have multiple `Notification`s.
    *   Relationships between `User` and `Offer` are likely present but need further inspection of the model definitions to be fully specified.

## 5. API & Interface Documentation

The backend exposes a RESTful API.

*   **Authentication:** All protected endpoints require a JWT Bearer token in the `Authorization` header.
*   **Example Endpoint (`/api/v1/offers/`):**
    *   **Method:** `GET`
    *   **Description:** Retrieves a list of offers, with support for pagination and filtering.
    *   **Query Parameters:** `skip: int = 0`, `limit: int = 100`, plus other filter parameters.
    *   **Response:** A JSON array of offer objects.
*   **General Endpoint Structure:** The API follows standard REST conventions, using `GET` for retrieval, `POST` for creation, `PUT` for updates, and `DELETE` for removal.

## 6. Security Assessment

*   **Authentication & Authorization:**
    *   **AuthN:** Implemented using JWTs. `passlib` with `bcrypt` is used for password hashing, which is a strong standard.
    *   **AuthZ:** Role-based access control is implemented, but the enforcement logic in the backend API endpoints should be audited to ensure it's comprehensive.
*   **Encryption:**
    *   **In Transit:** Assumed to be handled by a reverse proxy (e.g., Nginx) providing TLS/SSL encryption.
    *   **At Rest:** Password are hashed. Database-level encryption is not configured in the repository and depends on the deployment environment.
*   **OWASP Top 10 Risks:**
    *   **SQL Injection:** Mitigated by the use of SQLAlchemy ORM.
    *   **Broken Authentication:** JWT implementation seems standard, but requires a strong, securely stored secret key.
    *   **Sensitive Data Exposure:** The application relies on `app.core.config.py` to manage settings. Secrets must be loaded from environment variables or a secrets management service, not hardcoded.
    *   **Cross-Site Scripting (XSS):** The Streamlit frontend is generally safe, but the use of `st.markdown(unsafe_allow_html=True)` is a potential vector and must be carefully audited. The presence of HTML rendering fix commits suggests this is a known issue.

## 7. Performance & Scalability Review

*   **Potential Bottlenecks:**
    *   **Streamlit Frontend:** Streamlit's session-per-user model can be resource-intensive and may not scale to a very large number of concurrent users.
    *   **Database:** Complex queries without proper indexing could slow down the API.
    *   **Single Backend Instance:** Without a load balancer and multiple instances, the backend is a single point of failure and a performance bottleneck.
*   **Optimization Opportunities:**
    *   Implement caching (e.g., with Redis) for frequently accessed, non-volatile data.
    *   Horizontally scale the FastAPI backend with a load balancer.
    *   Conduct database performance analysis and add indexes where necessary.

## 8. Testing & Quality Metrics

*   **Test Suite:** A `tests/` directory exists with a number of test files, indicating a commitment to testing.
*   **Test Types:**
    *   Unit tests for individual functions.
    *   API-level tests (`test_offers_api.py`).
    *   Frontend component tests (`test_streamlit_html.py`).
*   **Gaps:**
    *   Test coverage is unknown and should be measured.
    *   The large number of standalone test and verification scripts suggests a reliance on manual testing.
    *   No clear evidence of end-to-end or performance testing.

## 9. Deployment & Operations

*   **CI/CD:** There are no CI/CD pipeline configurations (e.g., `.github/workflows`, `.gitlab-ci.yml`) in the repository. This indicates that testing and deployment are likely manual processes.
*   **Infrastructure:** `docker-compose.yml` defines the application stack, but there is no infrastructure-as-code (e.g., Terraform) for provisioning the underlying server and database.
*   **Monitoring:** No dedicated monitoring or logging solution (e.g., ELK stack, Prometheus, Grafana) is configured.
*   **Rollback Strategy:** Likely a manual process involving `git` and `docker-compose`, which is slow and error-prone.

## 10. Dependencies & Licensing

A full dependency audit is required. Key dependencies include:

*   **Backend:** `fastapi`, `sqlalchemy`, `pydantic`, `alembic`, `python-jose`, `passlib` (all MIT License).
*   **Frontend:** `streamlit`, `requests`, `pandas` (Apache 2.0, Apache 2.0, BSD 3-Clause respectively).

**Risk:** A formal process for scanning and approving dependencies and their licenses is recommended.

## 11. Technical Debt & Risks

*   **Manual Processes (High Risk):** The lack of a CI/CD pipeline for automated testing and deployment is the most significant risk. It leads to slow releases, a high chance of human error, and difficulty in rolling back changes.
*   **Configuration & Secrets Management (High Risk):** Storing secrets in version control is a major security risk. A secure method for managing secrets is essential.
*   **Frontend Scalability (Medium Risk):** Streamlit may not be suitable for a large-scale, high-concurrency application.
*   **Code Organization (Low Risk):** The root directory is cluttered with numerous scripts and documentation files. This can be improved for better maintainability.

## 12. Actionable Recommendations

### Critical
*   **Implement CI/CD:** Set up a CI/CD pipeline (e.g., using GitHub Actions) to automate testing on every commit and automate deployment to staging and production environments.
*   **Externalize Secrets:** Remove all secrets from configuration files. Use environment variables (loaded via Docker Compose) or a dedicated secrets management tool (like HashiCorp Vault).

### High
*   **Enhance Test Coverage:** Implement a tool to measure test coverage and set a target (e.g., 80%). Write additional tests to cover critical business logic and close gaps.
*   **Implement Structured Logging & Monitoring:** Integrate a logging library and ship logs to a centralized service. Set up monitoring dashboards to track application performance and health.
*   **Conduct a Security Audit:** Perform a thorough security review, focusing on authorization logic and potential XSS vectors in the frontend.

### Medium
*   **Adopt a Code Formatter:** Use a tool like `black` and `isort` to enforce a consistent code style across the project.
*   **Refactor Standalone Scripts:** Consolidate the many utility and test scripts in the root directory into a more organized structure, such as a single management CLI.
*   **Database Indexing:** Analyze slow queries and add database indexes to improve performance.

### Low
*   **Organize Root Directory:** Move documentation files into the `docs/` directory and scripts into a `scripts/` directory to clean up the project root.
*   **Consolidate Documentation:** Merge the numerous Markdown files into a unified, structured documentation site using a tool like MkDocs or Sphinx.
