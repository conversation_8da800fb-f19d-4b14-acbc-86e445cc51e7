#!/usr/bin/env python3
"""
Test script for Fixed Enhanced Dark Theme
Javí<PERSON>tt enhanced dark theme tesztelése
"""
import streamlit as st
import sys
import os
from datetime import datetime, timedelta

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Fixed Enhanced Dark Theme Test", 
    layout="wide", 
    initial_sidebar_state="collapsed"
)

st.title("🔧 Fixed Enhanced Dark Theme - HTML Rendering Test")

# Test the HTML rendering fix
st.info("""
### 🔧 HTML Rendering Javítások:
- **Grid layouts** - Proper CSS grid with correct colors
- **Text visibility** - All text now visible on dark background  
- **Panel content** - Rendered HTML instead of raw text
- **JavaScript fixes** - Proper error handling for panel toggles
""")

# Test data with the same structure as the real offer
test_offer = {
    'id': 14,
    'status': 'CONFIRMED_BY_COMPANY',
    'quantity_in_kg': 6000,
    'price': 555,
    'confirmed_quantity': 5555,
    'confirmed_price': 555,
    'created_at': '2025-05-22T10:24:00Z',
    'confirmed_at': '2025-05-23T08:15:00Z',
    'delivery_date': '2025-05-23T00:00:00Z',
    'updated_at': '2025-05-28T14:13:00Z',
    'user': {
        'contact_name': 'Szabó Gábor',
        'company_name': 'Gabor TÉSZ',
        'email': '<EMAIL>',
        'phone': None
    },
    'product_type': {
        'name': 'Hegyes erős paprika',
        'category': {'name': None}
    }
}

# Before/After comparison
col1, col2 = st.columns(2)

with col1:
    st.markdown("""
    ### 🐛 Előtte (hibás):
    - Raw HTML szövegként jelent meg
    - Grid layout nem működött
    - Szövegek láthatatlanok
    - Panel toggle hibák
    """)

with col2:
    st.markdown("""
    ### ✅ Utána (javított):
    - Proper HTML rendering
    - Working CSS grid layouts
    - Visible text on dark background
    - Reliable panel interactions
    """)

# Test the fixed enhanced theme
try:
    from pages.operator.offer_management.enhanced_minimal_dark_theme import render_enhanced_dark_theme_offer
    
    st.markdown("---")
    st.markdown("### 🧪 Javított Enhanced Dark Theme Teszt")
    
    # Render the fixed enhanced dark theme
    render_enhanced_dark_theme_offer(test_offer)
    
    st.success("✅ Enhanced Dark Theme sikeresen renderelve javított HTML-lel!")
    
except Exception as e:
    st.error(f"❌ Hiba a Fixed Enhanced Dark Theme betöltésekor: {e}")
    st.exception(e)

# Testing controls
st.markdown("---")
st.markdown("### 🎮 Teszt Vezérlők")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("🔄 Újratöltés"):
        st.rerun()

with col2:
    if st.button("📊 Grid Teszt"):
        st.markdown("""
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; background: #1a1a1a; padding: 1rem; border-radius: 8px;">
            <div style="color: #a0a0a0;"><strong>Label:</strong></div>
            <div style="color: #ffffff;">Value</div>
            <div style="color: #a0a0a0;"><strong>Another Label:</strong></div>
            <div style="color: #ffffff;">Another Value</div>
        </div>
        """, unsafe_allow_html=True)

with col3:
    if st.button("🎨 CSS Teszt"):
        st.markdown("""
        <div class="info-panel" style="background: #1a1a1a; border: 1px solid #2a2a2a; border-radius: 12px; padding: 1rem;">
            <div style="border-top: 3px solid #10dc60;"></div>
            <h3 style="color: #ffffff;">Teszt Panel</h3>
            <div class="grid-layout" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div style="color: #a0a0a0;"><strong>Test Label:</strong></div>
                <div style="color: #ffffff;">Test Value</div>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Technical notes
with st.expander("🔧 Technikai Javítások", expanded=False):
    st.markdown("""
    ### Elvégzett javítások:
    
    1. **HTML Renderelés**:
       ```python
       # Előtte: raw HTML string
       return f"<div>{content}</div>"
       
       # Utána: proper HTML with styling
       return f'<div style="color: #ffffff;">{content}</div>'
       ```
    
    2. **Grid Layout CSS**:
       ```css
       .grid-layout {
           display: grid;
           grid-template-columns: 1fr 1fr;
           gap: 1rem;
           color: #ffffff;
       }
       ```
    
    3. **Text Visibility**:
       ```css
       .panel-content {
           color: #ffffff;
       }
       
       .grid-layout div:first-child {
           color: #a0a0a0; /* Labels */
       }
       
       .grid-layout div:last-child {
           color: #ffffff; /* Values */
       }
       ```
    
    4. **JavaScript Error Handling**:
       ```javascript
       function togglePanel(panelId) {
           const panel = document.getElementById(panelId);
           const arrow = document.getElementById(panelId + '-arrow');
           
           if (panel && arrow) {
               // Safe manipulation
           }
       }
       ```
    """)

# Footer
st.markdown("---")
st.markdown("""
**🎯 Eredmény**: Az Enhanced Dark Theme most már helyesen rendereli a HTML tartalmat,
proper grid layoutokkal és látható szövegekkel dark háttéren.
""")