#!/usr/bin/env python3
"""Test password reset email functionality"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_password_reset():
    """Test password reset email sending"""
    
    # Test with a known email
    test_email = "<EMAIL>"
    
    print(f"Testing password reset for: {test_email}")
    print("-" * 50)
    
    # Make password reset request
    response = requests.post(
        f"{BASE_URL}/api/auth/password-reset-request",
        json={"email": test_email}
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    if response.status_code == 200:
        print("\n✓ Password reset request successful!")
        print("Check the logs to see if email was sent.")
    else:
        print("\n✗ Password reset request failed!")
    
    return response.status_code == 200

if __name__ == "__main__":
    print("Password Reset Email Test")
    print("=" * 50)
    
    success = test_password_reset()
    
    print("\n" + "=" * 50)
    print("Now checking backend logs for email status...")
    print("\nRun this command to see the logs:")
    print("docker logs backend --tail 50 | grep -E '(email|Email|jelszó|password)'")