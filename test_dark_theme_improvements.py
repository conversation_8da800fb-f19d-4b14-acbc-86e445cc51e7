#!/usr/bin/env python3
"""
Test script for Dark Theme UI Improvements
Tesztelő szkript a sötét téma fejlesztésekhez
"""
import streamlit as st
import sys
import os
from datetime import datetime, timedelta

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(page_title="Dark Theme UI Test", layout="wide", initial_sidebar_state="expanded")

st.title("🌙 Dark Theme UI Fejlesztések Teszt")

# Test data with various states
test_offers = {
    'created': {
        'id': 101,
        'status': 'CREATED',
        'quantity_in_kg': 100,
        'price': 750,
        'created_at': datetime.now().isoformat(),
        'delivery_date': (datetime.now() + timedelta(days=7)).isoformat(),
        'note': 'Friss termék, kiv<PERSON><PERSON><PERSON> minőség garantált.',
        'user': {
            'contact_name': '<PERSON><PERSON>',
            'company_name': 'FreshFarm Kft.',
            'email': '<EMAIL>',
            'phone': '+36 20 555 1234'
        },
        'product_type': {
            'name': 'Bio alma',
            'category': {'name': 'Gyümölcsök'}
        }
    },
    'confirmed': {
        'id': 102,
        'status': 'CONFIRMED_BY_COMPANY',
        'quantity_in_kg': 150,
        'price': 850,
        'confirmed_quantity': 145,
        'confirmed_price': 850,
        'created_at': (datetime.now() - timedelta(days=2)).isoformat(),
        'confirmed_at': (datetime.now() - timedelta(days=1)).isoformat(),
        'delivery_date': (datetime.now() + timedelta(days=5)).isoformat(),
        'note': 'Premium minőségű bio termékek. Gondos csomagolás.',
        'user': {
            'contact_name': 'Nagy János',
            'company_name': 'BioFarm Kft.',
            'email': '<EMAIL>',
            'phone': '+36 30 123 4567'
        },
        'product_type': {
            'name': 'Bio sárgarépa',
            'category': {'name': 'Gyökérzöldségek'}
        }
    },
    'accepted': {
        'id': 103,
        'status': 'ACCEPTED_BY_USER',
        'quantity_in_kg': 200,
        'price': 900,
        'confirmed_quantity': 200,
        'confirmed_price': 900,
        'created_at': (datetime.now() - timedelta(days=3)).isoformat(),
        'confirmed_at': (datetime.now() - timedelta(days=2)).isoformat(),
        'accepted_at': (datetime.now() - timedelta(days=1)).isoformat(),
        'delivery_date': (datetime.now() + timedelta(days=3)).isoformat(),
        'user': {
            'contact_name': 'Szabó Márta',
            'company_name': 'GreenValley Zrt.',
            'email': '<EMAIL>',
            'phone': '+36 70 987 6543'
        },
        'product_type': {
            'name': 'Bio paradicsom',
            'category': {'name': 'Zöldségek'}
        }
    },
    'finalized': {
        'id': 104,
        'status': 'FINALIZED',
        'quantity_in_kg': 175,
        'price': 800,
        'confirmed_quantity': 175,
        'confirmed_price': 800,
        'created_at': (datetime.now() - timedelta(days=7)).isoformat(),
        'confirmed_at': (datetime.now() - timedelta(days=6)).isoformat(),
        'accepted_at': (datetime.now() - timedelta(days=5)).isoformat(),
        'finalized_at': (datetime.now() - timedelta(days=1)).isoformat(),
        'delivery_date': (datetime.now() - timedelta(days=2)).isoformat(),
        'user': {
            'contact_name': 'Tóth István',
            'company_name': 'NaturFarm Kft.',
            'email': '<EMAIL>',
            'phone': '+36 20 111 2222'
        },
        'product_type': {
            'name': 'Bio burgonya',
            'category': {'name': 'Gyökérzöldségek'}
        }
    }
}

# Sidebar options
st.sidebar.header("🎨 Teszt Opciók")

# Status selector
status_options = ['CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'FINALIZED']
selected_status = st.sidebar.selectbox(
    "Válassz státuszt:",
    status_options,
    format_func=lambda x: {
        'CREATED': '🔵 Létrehozva',
        'CONFIRMED_BY_COMPANY': '🟡 Visszaigazolva',
        'ACCEPTED_BY_USER': '🟢 Elfogadva',
        'FINALIZED': '✅ Véglegesítve'
    }[x]
)

# Get the corresponding test offer
status_key_map = {
    'CREATED': 'created',
    'CONFIRMED_BY_COMPANY': 'confirmed',
    'ACCEPTED_BY_USER': 'accepted',
    'FINALIZED': 'finalized'
}
test_offer = test_offers[status_key_map[selected_status]]

# Additional customization
st.sidebar.markdown("### 📊 Adatok testreszabása")
test_offer['quantity_in_kg'] = st.sidebar.slider("Mennyiség (kg)", 10.0, 500.0, float(test_offer['quantity_in_kg']), 5.0)
test_offer['price'] = st.sidebar.slider("Ár (Ft/kg)", 100, 2000, test_offer['price'], 50)

if test_offer.get('confirmed_quantity'):
    test_offer['confirmed_quantity'] = st.sidebar.slider(
        "Visszaigazolt mennyiség (kg)", 
        10.0, 
        float(test_offer['quantity_in_kg']), 
        float(test_offer.get('confirmed_quantity', test_offer['quantity_in_kg'])), 
        5.0
    )
    test_offer['confirmed_price'] = st.sidebar.slider(
        "Visszaigazolt ár (Ft/kg)", 
        100, 
        2000, 
        test_offer.get('confirmed_price', test_offer['price']), 
        50
    )

# Feature toggles
st.sidebar.markdown("### 🔧 Funkciók")
show_animations = st.sidebar.checkbox("Animációk megjelenítése", value=True)
show_theme_info = st.sidebar.checkbox("Téma információk", value=False)

# Main content
if show_theme_info:
    st.info("""
    ### 🎨 Fejlesztések:
    - **Jobb kontraszt**: Sötétebb háttér (#0a0b0d) és optimalizált szövegszínek
    - **Élénkebb színek**: Vibráló státusz színek (iOS inspired)
    - **Modern kártya design**: 16px lekerekítés, animált felső csík
    - **Glassmorphism**: Elmosott háttér effekt az action bar-on
    - **Animációk**: Pulzáló státusz badge, ripple effektek
    - **Téma váltó**: Világos/sötét téma kapcsoló localStorage támogatással
    """)

# Test tabs
tab1, tab2, tab3 = st.tabs(["🌙 Dark Theme UI", "📊 Komponens Tesztek", "🎨 CSS Fejlesztések"])

with tab1:
    st.header("Dark Theme Offer Detail")
    
    try:
        from pages.operator.offer_management.modern_dark_theme_detail import DarkThemeOfferDetail
        
        dark_view = DarkThemeOfferDetail(test_offer)
        dark_view.render()
        
        st.success("✅ Dark Theme UI sikeresen renderelve!")
        
    except Exception as e:
        st.error(f"Hiba a Dark Theme UI betöltésekor: {e}")
        st.exception(e)

with tab2:
    st.header("Komponens Tesztek")
    
    # Test individual components
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🎯 Státusz Badge Animációk")
        st.markdown("""
        <div style="padding: 20px; background: #1a1d23; border-radius: 12px;">
            <div class="status-badge status-elfogadva" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1.5rem; border-radius: 24px; background: rgba(70, 209, 96, 0.2); color: #46d160; border: 1px solid rgba(70, 209, 96, 0.3);">
                <span style="animation: pulse 2s ease-in-out infinite;">●</span>
                <span>Elfogadva</span>
            </div>
        </div>
        <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        </style>
        """, unsafe_allow_html=True)
        
        st.subheader("📊 Progress Bar Animációk")
        st.progress(0.75)
        st.caption("75% teljesítve - animált shimmer effekttel")
    
    with col2:
        st.subheader("🎨 Színpaletta")
        colors = {
            'Főháttér': '#0a0b0d',
            'Kártya háttér': '#1a1d23',
            'Elsődleges szöveg': '#f7f8fa',
            'Zöld státusz': '#46d160',
            'Sárga státusz': '#ffd93d',
            'Narancs státusz': '#ff9500',
            'Piros státusz': '#ff3b30'
        }
        
        for name, color in colors.items():
            st.markdown(f"""
            <div style="display: flex; align-items: center; gap: 10px; margin: 5px 0;">
                <div style="width: 40px; height: 40px; background: {color}; border-radius: 8px; border: 1px solid #2a3142;"></div>
                <span style="color: #c3c9d0;">{name}: <code>{color}</code></span>
            </div>
            """, unsafe_allow_html=True)

with tab3:
    st.header("CSS Fejlesztések Áttekintése")
    
    st.markdown("""
    ### 🎯 Implementált fejlesztések:
    
    1. **Enhanced Color Palette** ✅
       - Sötétebb főháttér jobb kontraszttal
       - Élénkebb státusz színek
       - Optimalizált szövegszínek
    
    2. **Modern Card Design** ✅
       - 16px border-radius
       - Animált hover effektek
       - Színes felső csík hover-on
       - Enhanced shadow rendszer
    
    3. **Interactive Elements** ✅
       - Pulzáló státusz badge
       - Ripple effekt gombokon
       - Smooth cubic-bezier animációk
    
    4. **Glassmorphism Effects** ✅
       - Blur backdrop-filter az action bar-on
       - Semi-transparent elemek
       - Modern depth érzet
    
    5. **Theme Toggle** ✅
       - Világos/sötét téma váltó
       - LocalStorage persistence
       - Smooth transitions
    """)
    
    # Animation showcase
    if show_animations:
        st.subheader("🎬 Animáció Bemutató")
        
        animation_html = """
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-top: 20px;">
            <div class="animation-card" style="background: #1a1d23; padding: 20px; border-radius: 16px; text-align: center; animation: fadeInUp 0.5s ease-out;">
                <h4 style="color: #f7f8fa;">Fade In Up</h4>
                <p style="color: #8892a0;">Kártyák megjelenése</p>
            </div>
            
            <div class="animation-card" style="background: #1a1d23; padding: 20px; border-radius: 16px; text-align: center; animation: slideInRight 0.5s ease-out;">
                <h4 style="color: #f7f8fa;">Slide In Right</h4>
                <p style="color: #8892a0;">Timeline elemek</p>
            </div>
            
            <div class="animation-card" style="background: #1a1d23; padding: 20px; border-radius: 16px; text-align: center;">
                <div style="animation: pulse 2s ease-in-out infinite;">
                    <h4 style="color: #46d160;">Pulzálás</h4>
                </div>
                <p style="color: #8892a0;">Státusz indikátorok</p>
            </div>
        </div>
        
        <style>
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        </style>
        """
        st.markdown(animation_html, unsafe_allow_html=True)

# Footer
st.markdown("---")
st.markdown("""
### 🚀 Dark Theme UI Főbb Jellemzők

- **Modern Design System**: Következetes színpaletta és spacing
- **Smooth Animations**: Cubic-bezier transitions minden interakciónál
- **Enhanced Contrast**: WCAG AA kompatibilis kontraszt arányok
- **Interactive Feedback**: Hover, active és focus állapotok
- **Theme Persistence**: LocalStorage alapú téma mentés
- **Responsive Layout**: Mobil-optimalizált megjelenés

Ez a modern dark theme design professzionális megjelenést biztosít, miközben kiváló felhasználói élményt nyújt!
""")