#!/usr/bin/env python3
"""
Test script to verify st.html() implementation for HTML rendering fixes
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(page_title="HTML Rendering Test", layout="wide")

st.title("🧪 HTML Rendering Test - st.html() Implementation")

# Test 1: Basic HTML rendering
st.header("Test 1: Basic HTML Rendering")
col1, col2 = st.columns(2)

with col1:
    st.subheader("Using st.markdown (old way)")
    st.markdown("<p style='color: red;'>This text should be red</p>", unsafe_allow_html=True)
    st.markdown("<div style='background-color: yellow; padding: 10px;'>Yellow background</div>", unsafe_allow_html=True)

with col2:
    st.subheader("Using st.html (new way)")
    st.html("<p style='color: red;'>This text should be red</p>")
    st.html("<div style='background-color: yellow; padding: 10px;'>Yellow background</div>")

st.markdown("---")

# Test 2: CSS injection
st.header("Test 2: CSS Injection")
st.html("""
<style>
.test-box {
    border: 2px solid blue;
    padding: 20px;
    margin: 10px 0;
    background-color: lightblue;
    border-radius: 10px;
}
</style>
<div class="test-box">
    <h3>Styled Box with CSS</h3>
    <p>This box should have a blue border and light blue background.</p>
</div>
""")

st.markdown("---")

# Test 3: Complex HTML structure
st.header("Test 3: Complex HTML Table")
st.html("""
<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
    <thead>
        <tr style="background-color: #333; color: white;">
            <th style="padding: 10px; border: 1px solid #ddd;">Feature</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Old Method</th>
            <th style="padding: 10px; border: 1px solid #ddd;">New Method</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;">Function</td>
            <td style="padding: 10px; border: 1px solid #ddd;">st.markdown(..., unsafe_allow_html=True)</td>
            <td style="padding: 10px; border: 1px solid #ddd;">st.html(...)</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
            <td style="padding: 10px; border: 1px solid #ddd;">Simplicity</td>
            <td style="padding: 10px; border: 1px solid #ddd;">❌ Requires extra parameter</td>
            <td style="padding: 10px; border: 1px solid #ddd;">✅ Direct HTML rendering</td>
        </tr>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;">Performance</td>
            <td style="padding: 10px; border: 1px solid #ddd;">⚡ Good</td>
            <td style="padding: 10px; border: 1px solid #ddd;">⚡ Better</td>
        </tr>
    </tbody>
</table>
""")

st.markdown("---")

# Test 4: Import and test our updated components
st.header("Test 4: Updated Components")

try:
    from pages.operator.offer_management.nutrition_facts_container import NutritionFactsContainer
    from pages.operator.offer_management.detail_components import DetailContainer
    
    # Test NutritionFactsContainer
    st.subheader("NutritionFactsContainer Test")
    test_offer = {
        'id': 123,
        'status': 'CONFIRMED_BY_COMPANY',
        'status_display': 'Cég által visszaigazolva',
        'quantity_in_kg': 100,
        'price': 850,
        'created_at': '2024-04-27T08:30:00Z',
        'delivery_date': '2024-05-01',
        'user': {
            'contact_name': 'Test User',
            'company_name': 'Test Company'
        },
        'product_type': {
            'name': 'Test Product'
        }
    }
    
    nf_container = NutritionFactsContainer(title="Test Offer", subtitle="Offer #123")
    nf_container.render(test_offer, debug_mode=True)
    
    # Test DetailContainer
    st.subheader("DetailContainer Test")
    detail_container = DetailContainer(
        title="Test Detail Container",
        icon="📋",
        expandable=True,
        expanded=True
    )
    
    def test_content():
        st.write("This is test content inside the DetailContainer")
        st.html("<p style='color: green;'>This HTML content should be green</p>")
    
    detail_container.render(test_content)
    
    st.success("✅ All components loaded and rendered successfully!")
    
except Exception as e:
    st.error(f"❌ Error loading components: {str(e)}")
    st.exception(e)

st.markdown("---")
st.info("💡 All HTML content above should render properly without showing raw HTML tags.")