"""Initial migration

Revision ID: 6c3b6d1382d5
Revises: ec0ed7b52da9
Create Date: 2025-05-24 07:30:07.292073

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6c3b6d1382d5'
down_revision: Union[str, None] = 'ec0ed7b52da9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
