"""Initial migration

Revision ID: 3cff32d3f6d2
Revises: 2ce4ab391eaf
Create Date: 2025-05-21 09:13:23.959801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3cff32d3f6d2'
down_revision: Union[str, None] = '2ce4ab391eaf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
