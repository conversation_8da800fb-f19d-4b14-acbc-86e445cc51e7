"""Initial migration

Revision ID: 373ad2f6fbf8
Revises: d5599ec21d05
Create Date: 2025-05-21 23:02:17.486508

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '373ad2f6fbf8'
down_revision: Union[str, None] = 'd5599ec21d05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
