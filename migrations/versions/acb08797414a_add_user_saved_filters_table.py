"""add_user_saved_filters_table

Revision ID: acb08797414a
Revises: 0be44b9f90e6
Create Date: 2025-05-21 21:56:46.478098

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'acb08797414a'
down_revision: Union[str, None] = '0be44b9f90e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_saved_filters',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('filter_type', sa.String(length=50), nullable=False),
    sa.Column('is_default', sa.<PERSON>(), nullable=False),
    sa.Column('filter_data', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_saved_filters_id'), 'user_saved_filters', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_saved_filters_id'), table_name='user_saved_filters')
    op.drop_table('user_saved_filters')
    # ### end Alembic commands ###
