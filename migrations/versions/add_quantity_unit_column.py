"""add quantity_unit column to offers table

Revision ID: add_quantity_unit_column
Revises: acb08797414a
Create Date: 2025-07-10 09:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_quantity_unit_column'
down_revision: Union[str, None] = 'ab7530ad63ee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Add quantity_unit column with default value 'kg' for existing records
    op.add_column('offers', sa.Column('quantity_unit', sa.String(length=10), nullable=False, server_default='kg'))
    
    # Rename quantity_in_kg to quantity_value for clarity
    op.alter_column('offers', 'quantity_in_kg', new_column_name='quantity_value')
    
    # Update the check constraint to use the new column name
    op.drop_constraint('check_quantity_positive', 'offers', type_='check')
    op.create_check_constraint('check_quantity_positive', 'offers', 'quantity_value > 0')
    
    # Add check constraint for valid units
    op.create_check_constraint(
        'check_quantity_unit_valid', 
        'offers', 
        "quantity_unit IN ('kg', 'tonna', 'db')"
    )
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Remove the unit constraint
    op.drop_constraint('check_quantity_unit_valid', 'offers', type_='check')
    
    # Restore the original constraint name
    op.drop_constraint('check_quantity_positive', 'offers', type_='check')
    op.create_check_constraint('check_quantity_positive', 'offers', 'quantity_in_kg > 0')
    
    # Rename quantity_value back to quantity_in_kg
    op.alter_column('offers', 'quantity_value', new_column_name='quantity_in_kg')
    
    # Remove the quantity_unit column
    op.drop_column('offers', 'quantity_unit')
    
    # ### end Alembic commands ###