"""Initial migration

Revision ID: 387b0eccaf8e
Revises: cd27fb95aad0
Create Date: 2025-05-28 09:11:17.714861

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '387b0eccaf8e'
down_revision: Union[str, None] = 'cd27fb95aad0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
