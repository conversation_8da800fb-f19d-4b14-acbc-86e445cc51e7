"""Initial migration

Revision ID: 271b9f62bc0d
Revises: d0a24df1f3c8
Create Date: 2025-05-22 11:52:20.381486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '271b9f62bc0d'
down_revision: Union[str, None] = 'd0a24df1f3c8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
