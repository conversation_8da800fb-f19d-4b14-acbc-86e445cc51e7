  `content`: `# Streamlit HTML Kódolási Útmutató és Hibajavítási Terv

## Azonosított problémák: Ajánlatok Részletes Nézet

Az ajánlatok részletes nézetében a következő HTML-kezelési problémák azonosíthatók a mellékelt képek és kód alapján:

1. **ActionBar hibás működése**: A felső műveletsáv nem megfelelően működik, a gombok megjelennek, de a sticky pozicionálás nem érvényesül.
2. **HTML elemek lezárásának problémái**: Számos helyen a HTML elemek nyitása és zárása nem következetes.
3. **CSS Specifikussági problémák**: A CSS szelektorok nem elég erősek ahhoz, hogy felülírják a Streamlit alapértelmezett stílusait.
4. **Komponensek közötti integráció problémák**: A különböző komponensek (StatusIndicator, DetailContainer, stb.) nem konzisztensen kezelik a HTML-t.

## Legjobb gyakorlatok a Működő Komponensek Alapján

A `display_data_components.py` fájl vizsgálata alapján a következő hatékony gyakorlatokat azonosítottam:

1. **Teljes HTML blokkok egyetlen st.markdown hívásban**:
   ```python
   st.markdown(
       f\"\"\"
       <div class='offer-card'>
           <h4 class='card-title'>Ajánlat adatai</h4>
           <!-- Tartalom itt -->
       </div>
       \"\"\",
       unsafe_allow_html=True
   )
   ```

2. **Globális CSS definíciók**:
   ```python
   css = \"\"\"
   <style>
   .offer-card {
       min-height: 220px;
       background-color: #1e2230; 
       color: #e0e0e0;
       /* több CSS tulajdonság itt */
   }
   /* további osztályok itt */
   </style>
   \"\"\"
   st.markdown(css, unsafe_allow_html=True)
   ```

3. **Feltételes tartalom beágyazása a HTML-en belül**:
   ```python
   st.markdown(
       f\"\"\"
       <div>
           <!-- Alap tartalom -->
           {f'<div>{some_conditional_content}</div>' if condition else ''}
       </div>
       \"\"\",
       unsafe_allow_html=True
   )
   ```

4. **Megfelelő hibakezekés és fallback mechanizmusok**:
   ```python
   try:
       qty_in_kg = offer.get('quantity_in_kg')
       original_qty = float(qty_in_kg) if qty_in_kg is not None else 0.0
   except (ValueError, TypeError):
       original_qty = 0.0
   ```

## Javítási Terv

### 1. ActionBar Komponens Teljes Újraírása

A jól működő `display_offer_status_card` funkcióhoz hasonlóan az ActionBar komponenst teljesen újra kell írni:

```python
def render(self, on_back=None, on_status_change=None, on_edit=None, on_export=None, on_more_actions=None):
    \"\"\"Megjeleníti a műveleti sávot.\"\"\"
    # Egyedi azonosító generálása a CSS számára
    unique_id = f\"actionbar_{self.offer_id}_{str(uuid.uuid4())[:8]}\"
    
    # CSS definiálása egy egységként
    st.markdown(f\"\"\"
    <style>
    .{unique_id} {{
        position: sticky !important;
        top: 0 !important;
        z-index: 999 !important;
        background-color: #1e1e1e !important;
        border-bottom: 1px solid #333 !important;
        padding: 8px 0 !important;
        margin-bottom: 20px !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
    }}
    </style>
    \"\"\", unsafe_allow_html=True)
    
    # Konténer kezdete a műveleti sávhoz
    st.markdown(f'<div class=\"{unique_id}\">', unsafe_allow_html=True)
    
    # Gombok megjelenítése
    with st.container():
        is_mobile = st.session_state.get('is_mobile', False)
        
        # Mobilnézet és asztali nézet elkülönítése
        if is_mobile:
            cols = st.columns(2)
            with cols[0]:
                if st.button(\"← Vissza\", key=f\"back_btn_{unique_id}\"):
                    if \"selected_offer_id\" in st.session_state:
                        del st.session_state.selected_offer_id
                    st.rerun()
            
            with cols[1]:
                status_options = [\"Státuszváltás...\"] + self._get_next_status_options()
                selected_status = st.selectbox(
                    label=\"\",
                    options=status_options,
                    key=f\"status_select_{unique_id}\",
                    label_visibility=\"collapsed\"
                )
                if selected_status != \"Státuszváltás...\":
                    st.session_state[f\"show_status_dialog_{self.offer_id}\"] = True
                    st.session_state[f\"new_status_{self.offer_id}\"] = selected_status
                    st.rerun()
        else:
            # Asztali nézet
            cols = st.columns(5)
            with cols[0]:
                if st.button(\"← Vissza\", key=f\"back_btn_{unique_id}\"):
                    if \"selected_offer_id\" in st.session_state:
                        del st.session_state.selected_offer_id
                    st.rerun()
            
            # További gombok...
            # [kód a további gombokhoz az asztali nézetben]
    
    # Konténer lezárása
    st.markdown('</div>', unsafe_allow_html=True)
```

### 2. DetailContainer Újratervezése

A `DetailContainer` osztályt újra kell tervezni, a jól működő kártya-stílusú megjelenítés mintájára:

```python
def render(self, content_callback, loading_text=\"Betöltés...\"):
    \"\"\"Konténer megjelenítése tartalommal.\"\"\"
    # Egyedi azonosító a konténerhez és a CSS-hez
    container_id = f\"detail_container_{str(uuid.uuid4())[:8]}\"
    
    # Betöltési állapot ellenőrzése
    loading = False
    if self.loading_key and self.loading_key in st.session_state:
        loading = st.session_state[self.loading_key]
    
    # CSS definíció
    st.markdown(f\"\"\"
    <style>
    .{container_id}_header {{
        background-color: {self.color};
        color: white;
        padding: 10px;
        border-radius: 5px 5px 0 0;
        margin-top: 15px;
        font-weight: bold;
    }}
    .{container_id}_content {{
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-top: none;
        border-radius: 0 0 5px 5px;
        padding: 15px;
    }}
    </style>
    \"\"\", unsafe_allow_html=True)
    
    # Kártya fejléc
    st.markdown(f'<div class=\"{container_id}_header\">{self.icon} {self.title}</div>', unsafe_allow_html=True)
    
    # Kártya tartalom kezdete
    st.markdown(f'<div class=\"{container_id}_content\">', unsafe_allow_html=True)
    
    # Tartalom megjelenítése
    with st.container():
        if loading:
            st.info(loading_text)
            # Skeleton loader megjelenítése
            st.markdown('<div style=\"background-color: #f0f2f6; height: 1em; width: 80%; margin: 0.5em 0; border-radius: 3px;\"></div>', unsafe_allow_html=True)
            st.markdown('<div style=\"background-color: #f0f2f6; height: 1em; width: 65%; margin: 0.5em 0; border-radius: 3px;\"></div>', unsafe_allow_html=True)
        else:
            # Tartalom renderelése
            try:
                content_callback()
            except Exception as e:
                st.error(f\"Hiba történt az adatok megjelenítése során: {str(e)}\")
    
    # Kártya tartalom lezárása
    st.markdown('</div>', unsafe_allow_html=True)
```

### 3. StatusIndicator Komponens Átdolgozása

A StatusIndicator komponenst a jól működő `display_offer_status_card` mintájára kell átdolgozni:

```python
def render(self):
    \"\"\"Státusz vizuális megjelenítése\"\"\"
    # ... (meglévő kód a szín és szöveg meghatározásához) ...
    
    # Időbélyeg elkészítése, ha van
    timestamp_text = \"\"
    if self.timestamp:
        if isinstance(self.timestamp, str):
            timestamp_text = self.timestamp
        else:
            try:
                timestamp_text = format_datetime(self.timestamp)
            except Exception:
                timestamp_text = str(self.timestamp)
    
    # Teljes HTML egy blokkban, egyértelműen definiált struktúrával
    st.markdown(
        f\"\"\"
        <div style=\"display: flex; align-items: center; margin-bottom: 15px;\">
            <div style=\"width: 15px; height: 15px; background-color: {color}; 
                       border-radius: 50%; margin-right: 10px;\"></div>
            <div style=\"font-weight: bold; font-size: 1.2em;\">{status_text}</div>
            {f'<div style=\"margin-left: 10px; color: #666; font-size: 0.8em;\">{timestamp_text}</div>' if timestamp_text else ''}
        </div>
        \"\"\",
        unsafe_allow_html=True
    )
```

### 4. ModálAblak Komponens Újratervezése

A modális ablakok megjelenítését is egységesíteni kell:

```python
def render_modal(title, content_function, key_prefix, on_confirm=None, on_cancel=None):
    \"\"\"
    Egységes modális ablak megjelenítése.
    
    Args:
        title (str): A modális ablak címe
        content_function (callable): Függvény, amely a modális ablak tartalmát megjeleníti
        key_prefix (str): Egyedi előtag a gombok és mezők kulcsaihoz
        on_confirm (callable, optional): Megerősítés gomb callback függvénye
        on_cancel (callable, optional): Mégsem gomb callback függvénye
    \"\"\"
    # Egyedi azonosító a modálhoz
    modal_id = f\"modal_{key_prefix}_{str(uuid.uuid4())[:8]}\"
    
    # Modál CSS definíciók
    st.markdown(f\"\"\"
    <style>
    .{modal_id} {{
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    }}
    .{modal_id}_title {{
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 8px;
    }}
    </style>
    \"\"\", unsafe_allow_html=True)
    
    # Modál konténer kezdete
    st.markdown(f'<div class=\"{modal_id}\">', unsafe_allow_html=True)
    
    # Modál cím
    st.markdown(f'<div class=\"{modal_id}_title\">{title}</div>', unsafe_allow_html=True)
    
    # Tartalom megjelenítése
    with st.container():
        content_function()
    
    # Gombok elkülönítő vonal után
    st.markdown('<hr style=\"margin: 20px 0;\">', unsafe_allow_html=True)
    
    # Gombok egy sorban
    col1, col2 = st.columns(2)
    
    confirmed = False
    with col1:
        if st.button(\"Megerősítés\", key=f\"confirm_{key_prefix}\", type=\"primary\"):
            if on_confirm and callable(on_confirm):
                on_confirm()
            confirmed = True
    
    with col2:
        if st.button(\"Mégsem\", key=f\"cancel_{key_prefix}\", type=\"secondary\"):
            if on_cancel and callable(on_cancel):
                on_cancel()
    
    # Modál konténer lezárása
    st.markdown('</div>', unsafe_allow_html=True)
    
    return confirmed
```

## Teljes megoldási javaslat

A fenti komponens-szintű változtatásokon túl a következő globális elveket kell alkalmazni:

1. **CSS egységesítése**: Egységes CSS osztályneveket és struktúrát kell használni, a stílusokat egy helyen definiálva.

2. **Egyedi azonosítók használata**: Minden komponenshez egyedi azonosítót kell generálni, hogy elkerüljük az ütközéseket.

3. **Konzisztens HTML struktúra**: Minden komponensnek egységes HTML-szerkezetet kell követnie, a jól működő példák alapján.

4. **HTML blokkok kezelése**: A HTML blokkokat egészben kell definiálni és megjeleníteni, nem pedig részenként.

5. **Feltételes tartalom kezelése**: A feltételes tartalmat a HTML-en belül kell kezelni, nem külön st.markdown hívásokkal.


  
