# SMTP Email Configuration Guide

## Overview
The system now supports sending emails via SMTP. When properly configured, password reset emails and other notifications will be sent to actual email addresses instead of just being logged.

## Configuration Steps

### 1. Update docker-compose.yml
Edit the `docker-compose.yml` file and update the SMTP settings in the backend service environment section:

```yaml
environment:
  # ... other settings ...
  
  # SMTP settings - configure these with your SMTP server details
  SMTP_HOST: smtp.gmail.com         # Your SMTP server
  SMTP_PORT: 587                    # SMTP port (587 for TLS, 465 for SSL)
  SMTP_TLS: "true"                  # Use TLS encryption
  SMTP_USER: <EMAIL>   # Your email address
  SMTP_PASSWORD: your-app-password  # Your email password or app password
  EMAILS_FROM_EMAIL: <EMAIL>  # From email address
  EMAILS_FROM_NAME: "Mezőgazdasági Termékkezelő Rendszer"  # From name
```

### 2. Gmail Configuration (Recommended)
If using Gmail:

1. **Enable 2-Factor Authentication** on your Google account
2. **Generate an App Password**:
   - Go to https://myaccount.google.com/apppasswords
   - Select "Mail" as the app
   - Generate a 16-character app password
   - Use this app password in `SMTP_PASSWORD`

### 3. Other SMTP Providers

#### Outlook/Office365
```yaml
SMTP_HOST: smtp-mail.outlook.com
SMTP_PORT: 587
SMTP_TLS: "true"
```

#### Yahoo
```yaml
SMTP_HOST: smtp.mail.yahoo.com
SMTP_PORT: 587
SMTP_TLS: "true"
```

#### SendGrid
```yaml
SMTP_HOST: smtp.sendgrid.net
SMTP_PORT: 587
SMTP_TLS: "true"
SMTP_USER: apikey
SMTP_PASSWORD: your-sendgrid-api-key
```

### 4. Rebuild and Restart

After updating the configuration:

```bash
# Rebuild the backend container to install aiosmtplib
docker compose build backend

# Restart the services
docker compose down
docker compose up -d
```

### 5. Test Email Sending

Run the test script:
```bash
python3 test_password_reset_email.py
```

Then check the logs:
```bash
docker logs backend -f | grep -E "(SMTP|email|Email)"
```

## Troubleshooting

### Email not sending?
1. Check logs for errors: `docker logs backend --tail 100`
2. Verify SMTP credentials are correct
3. Ensure your email provider allows SMTP access
4. Check if you need an app-specific password

### Common Issues
- **Authentication failed**: Use app password instead of regular password
- **Connection refused**: Check firewall/port settings
- **TLS error**: Try changing `SMTP_TLS` to `"false"` and `SMTP_PORT` to `25`

## Production Mode
In production, ensure:
- Set `ENVIRONMENT: production` in docker-compose.yml
- Use secure credentials (consider using Docker secrets)
- Monitor email delivery rates
- Set up proper error handling and alerts

## Development Mode
When `ENVIRONMENT: development` and no SMTP is configured, emails will only be logged to console.