#!/usr/bin/env python3
"""
Test script for Quick Action Bar
Tesztelő script a G<PERSON>rs Műveletek Sávhoz
"""
import streamlit as st
import sys
import os

# Add the streamlit_app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlit_app'))

st.set_page_config(
    page_title="Quick Action Bar Test", 
    layout="wide", 
    initial_sidebar_state="collapsed"
)

st.title("🚀 Quick Action Bar - Modern Sticky Header Test")

# Test data with different statuses
test_offers = [
    {
        'id': 1,
        'status': 'CREATED',
        'user': {'id': 1, 'name': 'Test User'},
        'product_type': {'name': 'Alma'},
    },
    {
        'id': 2,
        'status': 'CONFIRMED_BY_COMPANY',
        'user': {'id': 1, 'name': 'Test User'},
        'product_type': {'name': '<PERSON>ö<PERSON>'},
    },
    {
        'id': 3,
        'status': 'ACCEPTED_BY_USER',
        'user': {'id': 2, 'name': 'Other User'},
        'product_type': {'name': '<PERSON><PERSON><PERSON><PERSON>'},
    },
    {
        'id': 4,
        'status': 'FINALIZED',
        'user': {'id': 1, 'name': 'Test User'},
        'product_type': {'name': 'Barack'},
    }
]

# User role selection
st.sidebar.markdown("### 👤 User Settings")
user_role = st.sidebar.selectbox(
    "User Role:",
    ["admin", "operator", "ügyintéző", "producer", "guest"],
    index=0
)

user_id = st.sidebar.number_input("User ID:", min_value=1, value=1)

# Set user in session state
st.session_state["user"] = {
    "id": user_id,
    "role": user_role,
    "name": f"Test {user_role.title()}"
}

st.info(f"""
### 🧪 Test Configuration:
- **User Role**: {user_role}
- **User ID**: {user_id}
- **Permissions**: Based on role and offer ownership
""")

# Test different offers
st.markdown("---")
st.markdown("### 📋 Test Different Offer Statuses")

tab1, tab2 = st.tabs(["HTML Version", "Native Streamlit Version"])

with tab1:
    st.markdown("#### 🎨 HTML/CSS/JS Implementation")
    
    for offer in test_offers:
        with st.expander(f"Offer #{offer['id']} - {offer['status']}", expanded=True):
            st.write(f"**Product**: {offer['product_type']['name']}")
            st.write(f"**Owner**: User #{offer['user']['id']} ({offer['user']['name']})")
            
            # Import and render quick action bar
            try:
                from pages.operator.offer_management.quick_action_bar import render_quick_action_bar
                render_quick_action_bar(offer, offer['id'])
            except Exception as e:
                st.error(f"Error rendering HTML action bar: {e}")

with tab2:
    st.markdown("#### 🔧 Native Streamlit Implementation")
    
    for offer in test_offers:
        with st.expander(f"Offer #{offer['id']} - {offer['status']}", expanded=True):
            st.write(f"**Product**: {offer['product_type']['name']}")
            st.write(f"**Owner**: User #{offer['user']['id']} ({offer['user']['name']})")
            
            # Import and render native quick action bar
            try:
                from pages.operator.offer_management.quick_action_bar import render_quick_action_bar_native
                action = render_quick_action_bar_native(offer, offer['id'])
                
                if action:
                    st.success(f"Action triggered: **{action}**")
            except Exception as e:
                st.error(f"Error rendering native action bar: {e}")

# Feature comparison
st.markdown("---")
st.markdown("### 🔍 Feature Comparison")

col1, col2 = st.columns(2)

with col1:
    st.markdown("""
    #### HTML Version Features:
    - ✅ Sticky header with backdrop blur
    - ✅ Gradient backgrounds
    - ✅ Hover effects and animations
    - ✅ Dropdown menu
    - ✅ Keyboard shortcuts (Alt+B, Alt+E, etc.)
    - ✅ Responsive mobile layout
    - ❌ Complex JavaScript communication
    """)

with col2:
    st.markdown("""
    #### Native Version Features:
    - ✅ Simple and reliable
    - ✅ Direct Streamlit state management
    - ✅ Works on all devices
    - ✅ No JavaScript issues
    - ❌ No sticky positioning
    - ❌ Limited styling options
    - ✅ Easier to maintain
    """)

# Implementation notes
with st.expander("📝 Implementation Notes", expanded=False):
    st.markdown("""
    ### Quick Action Bar Implementation Details:
    
    1. **Permissions System**:
       - `is_owner`: Can edit/accept/reject their own offers
       - `is_operator`: Can confirm offers and finalize accepted ones
       - `is_admin`: Can delete offers (except finalized)
    
    2. **Status-based Actions**:
       - **CREATED**: Operators can confirm
       - **CONFIRMED_BY_COMPANY**: Owners can accept/reject
       - **ACCEPTED_BY_USER**: Operators can finalize
       - **FINALIZED**: Read-only (no actions except export/print)
    
    3. **Styling**:
       - Dark gradient background (#1a1a1a to #2a2a2a)
       - Color-coded action buttons (primary, success, warning, danger)
       - Responsive design with mobile breakpoint at 768px
    
    4. **Keyboard Shortcuts**:
       - `Alt + B` or `Alt + ←`: Go back
       - `Alt + E`: Edit (if permitted)
       - `Alt + P`: Print
       - `Alt + X`: Export
    """)

# Testing controls
st.markdown("---")
st.markdown("### 🎮 Testing Controls")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("🔄 Refresh", use_container_width=True):
        st.rerun()

with col2:
    if st.button("🎨 Test CSS Injection", use_container_width=True):
        st.markdown("""
        <style>
        .stApp {
            background-color: #0a0a0a !important;
        }
        </style>
        """, unsafe_allow_html=True)
        st.success("Dark background applied!")

with col3:
    if st.button("📊 Show Session State", use_container_width=True):
        st.json(dict(st.session_state))

# Footer
st.markdown("---")
st.markdown("""
**🎯 Result**: The Quick Action Bar provides a modern, sticky header interface for managing offer actions.
Both HTML and Native versions are available, with the Native version recommended for reliability.
""")