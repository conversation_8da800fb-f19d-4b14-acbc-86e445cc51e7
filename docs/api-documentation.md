# Mezőgazdasági Termékkezelő API Documentation

## Overview

The Mezőgazdasági Termékkezelő (Agricultural Product Management) API provides a comprehensive set of endpoints for managing agricultural products, offers, user accounts, and notifications. This documentation covers all available endpoints, authentication methods, data models, and usage examples.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

The API uses JWT token-based authentication. Most endpoints require authentication via a Bearer token.

### Obtaining a Token

To obtain an authentication token, send a POST request to `/auth/login` with your credentials:

```
POST /api/v1/auth/login
```

Request body:
```json
{
  "username": "<EMAIL>",
  "password": "your_password"
}
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### Using the Token

Include the token in the `Authorization` header of your requests:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Core Endpoints

### Authentication Endpoints

#### Register a new user

```
POST /api/v1/auth/register
```

Creates a new user account. The user will need to activate their account via email.

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "company_name": "Example Farm Ltd.",
  "tax_id": "********-1-42",
  "contact_name": "John Doe",
  "phone_number": "+***********"
}
```

Response:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "company_name": "Example Farm Ltd.",
  "tax_id": "********-1-42",
  "contact_name": "John Doe",
  "phone_number": "+***********",
  "role": "termelő",
  "is_active": false,
  "created_at": "2023-09-15T10:30:00",
  "updated_at": "2023-09-15T10:30:00"
}
```

#### Activate account

```
GET /api/v1/auth/activate-account?token={activation_token}
```

Activates a user account using the token sent via email.

Response:
```json
{
  "message": "Felhasználói fiók sikeresen aktiválva"
}
```

#### Login

```
POST /api/v1/auth/login
```

Authenticates a user and returns a JWT token.

Request body:
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### Request password reset

```
POST /api/v1/auth/password-reset-request
```

Sends a password reset link to the user's email.

Request body:
```json
{
  "email": "<EMAIL>"
}
```

Response:
```json
{
  "message": "Ha a megadott email cím regisztrálva van a rendszerben, akkor elküldtük a jelszó-visszaállítási linket"
}
```

#### Confirm password reset

```
POST /api/v1/auth/password-reset-confirm
```

Resets the user's password using the token sent via email.

Request body:
```json
{
  "token": "reset_token_from_email",
  "new_password": "new_password123"
}
```

Response:
```json
{
  "message": "Jelszó sikeresen visszaállítva"
}
```

#### Get current user info

```
GET /api/v1/auth/me
```

Returns information about the currently authenticated user.

Response:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "company_name": "Example Farm Ltd.",
  "tax_id": "********-1-42",
  "contact_name": "John Doe",
  "phone_number": "+***********",
  "role": "termelő",
  "is_active": true,
  "created_at": "2023-09-15T10:30:00",
  "updated_at": "2023-09-15T10:30:00"
}
```

### User Endpoints

#### Get current user

```
GET /api/v1/users/me
```

Returns information about the currently authenticated user.

Response: Same as `/auth/me`

#### Update current user

```
PUT /api/v1/users/me
```

Updates the current user's information.

Request body:
```json
{
  "company_name": "Updated Farm Name Ltd.",
  "tax_id": "87654321-1-42",
  "contact_name": "John Smith",
  "phone_number": "+36209876543"
}
```

Response: Updated user object

#### Change current user's password

```
POST /api/v1/users/me/change-password
```

Changes the current user's password.

Request body:
```json
{
  "current_password": "old_password",
  "new_password": "new_password123"
}
```

Response: User object

#### Get current user settings

```
GET /api/v1/users/me/settings
```

Returns the default settings for the current user.

Response:
```json
{
  "id": 1,
  "user_id": 1,
  "default_product_type_id": 2,
  "default_quality_grade_id": 3,
  "default_quantity_unit": "kg",
  "default_category_id": 1,
  "default_product_type_name": "TV paprika",
  "default_quality_grade_name": "Extra",
  "default_category_name": "Paprika",
  "has_quality_grades": true
}
```

#### Update current user settings

```
PUT /api/v1/users/me/settings
```

Updates the default settings for the current user.

Request body:
```json
{
  "default_product_type_id": 2,
  "default_quality_grade_id": 3,
  "default_quantity_unit": "kg",
  "default_category_id": 1
}
```

Response: Updated settings object

#### List users (admin/operator only)

```
GET /api/v1/users
```

Lists all users in the system. Only accessible by administrators and operators.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)
- `search`: Search term to filter users by name or email
- `role`: Filter users by role (e.g., "termelő", "ügyintéző", "admin")

Response:
```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "company_name": "Farm 1",
    "tax_id": "********-1-42",
    "contact_name": "John Doe",
    "phone_number": "+***********",
    "role": "termelő",
    "is_active": true,
    "created_at": "2023-09-15T10:30:00",
    "updated_at": "2023-09-15T10:30:00"
  },
  {
    "id": 2,
    "email": "<EMAIL>",
    "company_name": "Farm 2",
    "tax_id": "87654321-1-42",
    "contact_name": "Jane Smith",
    "phone_number": "+36209876543",
    "role": "termelő",
    "is_active": true,
    "created_at": "2023-09-16T11:45:00",
    "updated_at": "2023-09-16T11:45:00"
  }
]
```

#### Get user by ID

```
GET /api/v1/users/{user_id}
```

Returns information about a specific user. Users can only retrieve their own data, while administrators and operators can retrieve any user's data.

Path parameters:
- `user_id`: The ID of the user to retrieve

Response: User object

#### Update user

```
PUT /api/v1/users/{user_id}
```

Updates a user's information. Users can only update their own data, while administrators can update any user's data.

Path parameters:
- `user_id`: The ID of the user to update

Request body: Same as `PUT /users/me`

Response: Updated user object

#### Delete user

```
DELETE /api/v1/users/{user_id}
```

Deletes a user. Users can only delete their own accounts, while administrators can delete any user.

Path parameters:
- `user_id`: The ID of the user to delete

Response: No content (204)

#### Update user role (admin only)

```
PUT /api/v1/users/{user_id}/role
```

Updates a user's role. Only accessible by administrators.

Path parameters:
- `user_id`: The ID of the user to update

Request body:
```json
{
  "role": "ügyintéző"
}
```

Response: Updated user object

#### Activate user (admin only)

```
PUT /api/v1/users/{user_id}/activate
```

Activates a deactivated user. Only accessible by administrators.

Path parameters:
- `user_id`: The ID of the user to activate

Response: Updated user object

#### Deactivate user (admin only)

```
PUT /api/v1/users/{user_id}/deactivate
```

Deactivates an active user. Only accessible by administrators.

Path parameters:
- `user_id`: The ID of the user to deactivate

Response: Updated user object

### Product Endpoints

#### List product categories

```
GET /api/v1/products/categories
```

Lists all product categories.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)
- `search`: Search term to filter categories by name

Response:
```json
[
  {
    "id": 1,
    "name": "Paprika",
    "description": "Különböző paprikafélék",
    "created_at": "2023-09-15T10:30:00",
    "updated_at": "2023-09-15T10:30:00"
  },
  {
    "id": 2,
    "name": "Paradicsom",
    "description": "Különböző paradicsomfajták",
    "created_at": "2023-09-15T10:35:00",
    "updated_at": "2023-09-15T10:35:00"
  }
]
```

#### Get categories with types

```
GET /api/v1/products/categories/with-types
```

Lists all product categories with their associated product types.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)

Response:
```json
[
  {
    "id": 1,
    "name": "Paprika",
    "description": "Különböző paprikafélék",
    "created_at": "2023-09-15T10:30:00",
    "updated_at": "2023-09-15T10:30:00",
    "product_types": [
      {
        "id": 1,
        "name": "TV paprika",
        "description": "Tölteni való paprika",
        "has_quality_grades": true,
        "category_id": 1,
        "created_at": "2023-09-15T10:40:00",
        "updated_at": "2023-09-15T10:40:00"
      },
      {
        "id": 2,
        "name": "Kápia paprika",
        "description": "Kápia paprika",
        "has_quality_grades": true,
        "category_id": 1,
        "created_at": "2023-09-15T10:45:00",
        "updated_at": "2023-09-15T10:45:00"
      }
    ]
  }
]
```

#### Get category by ID

```
GET /api/v1/products/categories/{category_id}
```

Returns information about a specific product category.

Path parameters:
- `category_id`: The ID of the category to retrieve

Response: Category object

#### Create product category (admin only)

```
POST /api/v1/products/categories
```

Creates a new product category. Only accessible by administrators.

Request body:
```json
{
  "name": "Uborka",
  "description": "Különböző uborkafélék"
}
```

Response: Created category object

#### Update product category (admin only)

```
PUT /api/v1/products/categories/{category_id}
```

Updates a product category. Only accessible by administrators.

Path parameters:
- `category_id`: The ID of the category to update

Request body:
```json
{
  "name": "Uborka",
  "description": "Updated description for uborkafélék"
}
```

Response: Updated category object

#### Delete product category (admin only)

```
DELETE /api/v1/products/categories/{category_id}
```

Deletes a product category. Only accessible by administrators.

Path parameters:
- `category_id`: The ID of the category to delete

Response: No content (204)

#### List product types

```
GET /api/v1/products/types
```

Lists all product types.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)
- `search`: Search term to filter types by name
- `category_id`: Filter types by category ID

Response:
```json
[
  {
    "id": 1,
    "name": "TV paprika",
    "description": "Tölteni való paprika",
    "has_quality_grades": true,
    "category_id": 1,
    "created_at": "2023-09-15T10:40:00",
    "updated_at": "2023-09-15T10:40:00"
  },
  {
    "id": 2,
    "name": "Kápia paprika",
    "description": "Kápia paprika",
    "has_quality_grades": true,
    "category_id": 1,
    "created_at": "2023-09-15T10:45:00",
    "updated_at": "2023-09-15T10:45:00"
  }
]
```

#### Get product type by ID

```
GET /api/v1/products/types/{type_id}
```

Returns information about a specific product type.

Path parameters:
- `type_id`: The ID of the product type to retrieve

Response:
```json
{
  "id": 1,
  "name": "TV paprika",
  "description": "Tölteni való paprika",
  "has_quality_grades": true,
  "category_id": 1,
  "created_at": "2023-09-15T10:40:00",
  "updated_at": "2023-09-15T10:40:00",
  "category": {
    "id": 1,
    "name": "Paprika",
    "description": "Különböző paprikafélék",
    "created_at": "2023-09-15T10:30:00",
    "updated_at": "2023-09-15T10:30:00"
  }
}
```

#### Get product type with grades

```
GET /api/v1/products/types/{type_id}/with-grades
```

Returns information about a specific product type along with its quality grades.

Path parameters:
- `type_id`: The ID of the product type to retrieve

Response:
```json
{
  "id": 1,
  "name": "TV paprika",
  "description": "Tölteni való paprika",
  "has_quality_grades": true,
  "category_id": 1,
  "created_at": "2023-09-15T10:40:00",
  "updated_at": "2023-09-15T10:40:00",
  "quality_grades": [
    {
      "id": 1,
      "name": "Extra",
      "min_shoulder_diameter": 60.0,
      "min_length": 100.0,
      "description": "Extra minőségű TV paprika",
      "product_type_id": 1,
      "created_at": "2023-09-15T10:50:00",
      "updated_at": "2023-09-15T10:50:00"
    },
    {
      "id": 2,
      "name": "I. Osztály",
      "min_shoulder_diameter": 50.0,
      "max_shoulder_diameter": 59.0,
      "min_length": 80.0,
      "max_length": 99.0,
      "description": "I. osztályú TV paprika",
      "product_type_id": 1,
      "created_at": "2023-09-15T10:55:00",
      "updated_at": "2023-09-15T10:55:00"
    }
  ]
}
```

#### Create product type (admin only)

```
POST /api/v1/products/types
```

Creates a new product type. Only accessible by administrators.

Request body:
```json
{
  "name": "Kígyóuborka",
  "description": "Friss kígyóuborka",
  "has_quality_grades": true,
  "category_id": 3
}
```

Response: Created product type object

#### Update product type (admin only)

```
PUT /api/v1/products/types/{type_id}
```

Updates a product type. Only accessible by administrators.

Path parameters:
- `type_id`: The ID of the product type to update

Request body:
```json
{
  "name": "Kígyóuborka",
  "description": "Updated description for kígyóuborka",
  "has_quality_grades": true,
  "category_id": 3
}
```

Response: Updated product type object

#### Delete product type (admin only)

```
DELETE /api/v1/products/types/{type_id}
```

Deletes a product type. Only accessible by administrators.

Path parameters:
- `type_id`: The ID of the product type to delete

Response: No content (204)

#### List quality grades

```
GET /api/v1/products/grades
```

Lists all quality grades.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)
- `search`: Search term to filter grades by name
- `product_type_id`: Filter grades by product type ID

Response:
```json
[
  {
    "id": 1,
    "name": "Extra",
    "min_shoulder_diameter": 60.0,
    "min_length": 100.0,
    "description": "Extra minőségű TV paprika",
    "product_type_id": 1,
    "created_at": "2023-09-15T10:50:00",
    "updated_at": "2023-09-15T10:50:00"
  },
  {
    "id": 2,
    "name": "I. Osztály",
    "min_shoulder_diameter": 50.0,
    "max_shoulder_diameter": 59.0,
    "min_length": 80.0,
    "max_length": 99.0,
    "description": "I. osztályú TV paprika",
    "product_type_id": 1,
    "created_at": "2023-09-15T10:55:00",
    "updated_at": "2023-09-15T10:55:00"
  }
]
```

#### Get quality grade by ID

```
GET /api/v1/products/grades/{grade_id}
```

Returns information about a specific quality grade.

Path parameters:
- `grade_id`: The ID of the quality grade to retrieve

Response:
```json
{
  "id": 1,
  "name": "Extra",
  "min_shoulder_diameter": 60.0,
  "min_length": 100.0,
  "description": "Extra minőségű TV paprika",
  "product_type_id": 1,
  "created_at": "2023-09-15T10:50:00",
  "updated_at": "2023-09-15T10:50:00",
  "product_type": {
    "id": 1,
    "name": "TV paprika",
    "description": "Tölteni való paprika",
    "has_quality_grades": true,
    "category_id": 1,
    "created_at": "2023-09-15T10:40:00",
    "updated_at": "2023-09-15T10:40:00"
  }
}
```

#### Create quality grade (admin only)

```
POST /api/v1/products/grades
```

Creates a new quality grade. Only accessible by administrators.

Request body:
```json
{
  "name": "II. Osztály",
  "min_shoulder_diameter": 40.0,
  "max_shoulder_diameter": 49.0,
  "min_length": 70.0,
  "max_length": 79.0,
  "description": "II. osztályú TV paprika",
  "product_type_id": 1
}
```

Response: Created quality grade object

#### Update quality grade (admin only)

```
PUT /api/v1/products/grades/{grade_id}
```

Updates a quality grade. Only accessible by administrators.

Path parameters:
- `grade_id`: The ID of the quality grade to update

Request body:
```json
{
  "name": "II. Osztály",
  "min_shoulder_diameter": 40.0,
  "max_shoulder_diameter": 49.0,
  "min_length": 70.0,
  "max_length": 79.0,
  "description": "Updated description for II. osztályú TV paprika",
  "product_type_id": 1
}
```

Response: Updated quality grade object

#### Delete quality grade (admin only)

```
DELETE /api/v1/products/grades/{grade_id}
```

Deletes a quality grade. Only accessible by administrators.

Path parameters:
- `grade_id`: The ID of the quality grade to delete

Response: No content (204)

### Offer Endpoints

#### Create offer

```
POST /api/v1/offers
```

Creates a new offer.

Request body:
```json
{
  "product_type_id": 1,
  "quality_grade_id": 1,
  "quantity_in_kg": 1000,
  "delivery_date": "2023-10-15",
  "note": "Example offer note"
}
```

Response:
```json
{
  "id": 1,
  "product_type_id": 1,
  "quality_grade_id": 1,
  "quantity_in_kg": 1000,
  "delivery_date": "2023-10-15",
  "note": "Example offer note",
  "status": "CREATED",
  "user_id": 3,
  "created_by_user_id": 3,
  "confirmed_quantity": null,
  "confirmed_price": null,
  "created_at": "2023-09-20T14:30:00",
  "updated_at": "2023-09-20T14:30:00"
}
```

#### Create offer for user (operator only)

```
POST /api/v1/offers/for-user
```

Creates a new offer on behalf of another user. Only accessible by operators and administrators.

Request body:
```json
{
  "user_id": 3,
  "product_type_id": 1,
  "quality_grade_id": 1,
  "quantity_in_kg": 1000,
  "delivery_date": "2023-10-15",
  "note": "Example offer note"
}
```

Response: Created offer object

#### List offers

```
GET /api/v1/offers
```

Lists offers. Producers can only see their own offers, while operators and administrators can see all offers.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100)
- `user_id`: Filter offers by user ID (only for operators and admins)
- `product_type_id`: Filter offers by product type ID
- `status`: Filter offers by status
- `date_from`: Filter offers by delivery date (from)
- `date_to`: Filter offers by delivery date (to)

Response:
```json
[
  {
    "id": 1,
    "product_type_id": 1,
    "quality_grade_id": 1,
    "quantity_in_kg": 1000,
    "delivery_date": "2023-10-15",
    "note": "Example offer note",
    "status": "CREATED",
    "user_id": 3,
    "created_by_user_id": 3,
    "confirmed_quantity": null,
    "confirmed_price": null,
    "created_at": "2023-09-20T14:30:00",
    "updated_at": "2023-09-20T14:30:00",
    "user": {
      "id": 3,
      "email": "<EMAIL>",
      "company_name": "Producer Farm Ltd.",
      "tax_id": "********-1-42",
      "contact_name": "Producer User",
      "phone_number": "+***********",
      "role": "termelő",
      "is_active": true,
      "created_at": "2023-09-15T10:30:00",
      "updated_at": "2023-09-15T10:30:00"
    },
    "product_type": {
      "id": 1,
      "name": "TV paprika",
      "description": "Tölteni való paprika",
      "has_quality_grades": true,
      "category_id": 1,
      "created_at": "2023-09-15T10:40:00",
      "updated_at": "2023-09-15T10:40:00"
    },
    "quality_grade": {
      "id": 1,
      "name": "Extra",
      "min_shoulder_diameter": 60.0,
      "min_length": 100.0,
      "description": "Extra minőségű TV paprika",
      "product_type_id": 1,
      "created_at": "2023-09-15T10:50:00",
      "updated_at": "2023-09-15T10:50:00"
    },
    "created_by_user": {
      "id": 3,
      "email": "<EMAIL>",
      "company_name": "Producer Farm Ltd.",
      "tax_id": "********-1-42",
      "contact_name": "Producer User",
      "phone_number": "+***********",
      "role": "termelő",
      "is_active": true,
      "created_at": "2023-09-15T10:30:00",
      "updated_at": "2023-09-15T10:30:00"
    }
  }
]
```

#### List my offers

```
GET /api/v1/offers/my
```

Lists offers belonging to the current user.

Query parameters: Same as `GET /offers`

Response: List of offer objects

#### Get offers in calendar view

```
GET /api/v1/offers/calendar
```

Lists offers grouped by delivery date. Producers can only see their own offers, while operators and administrators can see all offers.

Query parameters:
- `date_from`: Start date (required)
- `date_to`: End date (required)

Response:
```json
{
  "2023-10-15": [
    {
      "id": 1,
      "product_type_id": 1,
      "quality_grade_id": 1,
      "quantity_in_kg": 1000,
      "delivery_date": "2023-10-15",
      "note": "Example offer note",
      "status": "CREATED",
      "user_id": 3,
      "created_by_user_id": 3,
      "confirmed_quantity": null,
      "confirmed_price": null,
      "created_at": "2023-09-20T14:30:00",
      "updated_at": "2023-09-20T14:30:00"
    }
  ],
  "2023-10-16": [
    {
      "id": 2,
      "product_type_id": 2,
      "quality_grade_id": 5,
      "quantity_in_kg": 500,
      "delivery_date": "2023-10-16",
      "note": "Another offer note",
      "status": "CREATED",
      "user_id": 3,
      "created_by_user_id": 3,
      "confirmed_quantity": null,
      "confirmed_price": null,
      "created_at": "2023-09-20T15:00:00",
      "updated_at": "2023-09-20T15:00:00"
    }
  ]
}
```

#### Get offer statistics

```
GET /api/v1/offers/statistics
```

Returns statistics about offers. Producers can only see statistics for their own offers, while operators and administrators can see statistics for all offers.

Query parameters:
- `date_from`: Filter statistics by delivery date (from)
- `date_to`: Filter statistics by delivery date (to)
- `product_type_id`: Filter statistics by product type ID
- `category_id`: Filter statistics by category ID
- `quality_grade_id`: Filter statistics by quality grade ID

Response:
```json
{
  "total_offers": 10,
  "total_quantity": 5000,
  "total_value": 1500000,
  "average_price": 300,
  "status_counts": {
    "CREATED": 3,
    "CONFIRMED_BY_COMPANY": 2,
    "ACCEPTED_BY_USER": 1,
    "REJECTED_BY_USER": 1,
    "FINALIZED": 3
  },
  "status_summary": [
    {
      "status": "CREATED",
      "count": 3
    },
    {
      "status": "CONFIRMED_BY_COMPANY",
      "count": 2
    },
    {
      "status": "ACCEPTED_BY_USER",
      "count": 1
    },
    {
      "status": "REJECTED_BY_USER",
      "count": 1
    },
    {
      "status": "FINALIZED",
      "count": 3
    }
  ],
  "daily_summary": [
    {
      "date": "2023-10-15",
      "count": 3,
      "quantity": 2000,
      "value": 600000
    },
    {
      "date": "2023-10-16",
      "count": 7,
      "quantity": 3000,
      "value": 900000
    }
  ]
}
```

#### Get offer by ID

```
GET /api/v1/offers/{offer_id}
```

Returns information about a specific offer. Producers can only see their own offers, while operators and administrators can see all offers.

Path parameters:
- `offer_id`: The ID of the offer to retrieve

Response: Detailed offer object

#### Update offer

```
PUT /api/v1/offers/{offer_id}
```

Updates an offer. Producers can only update their own offers, while operators can update any offer.

Path parameters:
- `offer_id`: The ID of the offer to update

Request body:
```json
{
  "product_type_id": 1,
  "quality_grade_id": 1,
  "quantity_in_kg": 1500,
  "delivery_date": "2023-10-20",
  "note": "Updated offer note"
}
```

Response: Updated offer object

#### Delete offer

```
DELETE /api/v1/offers/{offer_id}
```

Deletes an offer. Only the offer creator or an administrator can delete an offer, and only offers in the "CREATED" or "REJECTED_BY_USER" status can be deleted.

Path parameters:
- `offer_id`: The ID of the offer to delete

Response: No content (204)

#### Confirm offer (operator only)

```
POST /api/v1/offers/{offer_id}/confirm
```

Confirms an offer. Only accessible by operators and administrators.

Path parameters:
- `offer_id`: The ID of the offer to confirm

Request body:
```json
{
  "confirmed_quantity": 900,
  "confirmed_price": 300,
  "note": "Confirmed offer note"
}
```

Response: Confirmed offer object with updated status

#### Accept offer (producer only)

```
POST /api/v1/offers/{offer_id}/accept
```

Accepts a confirmed offer. Only the offer owner can accept an offer.

Path parameters:
- `offer_id`: The ID of the offer to accept

Response: Accepted offer object with updated status

#### Reject offer (producer only)

```
POST /api/v1/offers/{offer_id}/reject
```

Rejects a confirmed offer. Only the offer owner can reject an offer.

Path parameters:
- `offer_id`: The ID of the offer to reject

Query parameters:
- `note`: Optional note explaining the rejection reason

Response: Rejected offer object with updated status

#### Finalize offer (operator only)

```
POST /api/v1/offers/{offer_id}/finalize
```

Finalizes an accepted offer. Only accessible by operators and administrators.

Path parameters:
- `offer_id`: The ID of the offer to finalize

Response: Finalized offer object with updated status

#### Get offer logs

```
GET /api/v1/offers/{offer_id}/logs
```

Returns the status change logs for a specific offer. Producers can only see logs for their own offers, while operators and administrators can see logs for all offers.

Path parameters:
- `offer_id`: The ID of the offer to get logs for

Response:
```json
[
  {
    "id": 1,
    "offer_id": 1,
    "old_status": null,
    "new_status": "CREATED",
    "changed_by": 3,
    "note": "Ajánlat létrehozva",
    "created_at": "2023-09-20T14:30:00"
  },
  {
    "id": 2,
    "offer_id": 1,
    "old_status": "CREATED",
    "new_status": "CONFIRMED_BY_COMPANY",
    "changed_by": 2,
    "note": "Ajánlat visszaigazolva: 900 kg, 300 Ft/kg",
    "created_at": "2023-09-21T10:15:00"
  }
]
```

#### Get paginated offers

```
GET /api/v1/offers/paginated
```

Lists offers with pagination support. Producers can only see their own offers, while operators and administrators can see all offers.

Query parameters:
- `page`: Page number (starting from 1, default: 1)
- `page_size`: Number of items per page (default: 20, max: 100)
- `user_id`: Filter offers by user ID (only for operators and admins)
- `product_type_id`: Filter offers by product type ID
- `status`: Filter offers by status (can be comma-separated for multiple statuses)
- `date_from`: Filter offers by delivery date (from)
- `date_to`: Filter offers by delivery date (to)

Response:
```json
{
  "items": [
    {
      "id": 1,
      "product_type_id": 1,
      "quality_grade_id": 1,
      "quantity_in_kg": 1000,
      "delivery_date": "2023-10-15",
      "note": "Example offer note",
      "status": "CREATED",
      "user_id": 3,
      "created_by_user_id": 3,
      "confirmed_quantity": null,
      "confirmed_price": null,
      "created_at": "2023-09-20T14:30:00",
      "updated_at": "2023-09-20T14:30:00",
      "user": { ... },
      "product_type": { ... },
      "quality_grade": { ... },
      "created_by_user": { ... }
    }
  ],
  "total": 45,
  "page": 1,
  "page_size": 20,
  "pages": 3
}
```

### Notification Endpoints

#### Get current user's notifications

```
GET /api/v1/notifications/me
```

Returns notifications for the current user.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100, max: 500)
- `include_read`: Include read notifications (default: false)

Response:
```json
{
  "items": [
    {
      "id": 1,
      "type": "success",
      "message": "Ajánlatát visszaigazolta az ügyintéző",
      "detail": "Ajánlat azonosító: 1, visszaigazolt mennyiség: 900 kg, ár: 300 Ft/kg",
      "target_roles": null,
      "related_entity_type": "offer",
      "related_entity_id": 1,
      "user_id": 3,
      "is_read": false,
      "created_at": "2023-09-21T10:15:00",
      "updated_at": "2023-09-21T10:15:00"
    }
  ],
  "total": 5
}
```

#### Get all notifications (admin only)

```
GET /api/v1/notifications/all
```

Returns all notifications in the system. Only accessible by administrators.

Query parameters:
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100, max: 500)
- `include_read`: Include read notifications (default: false)

Response: Same format as `GET /notifications/me`

#### Create notification (admin only)

```
POST /api/v1/notifications
```

Creates a new notification. Only accessible by administrators.

Request body:
```json
{
  "type": "info",
  "message": "Rendszerkarbantartás lesz",
  "detail": "A rendszer karbantartás miatt nem lesz elérhető 2023. október 10-én 18:00 és 20:00 között.",
  "target_roles": "termelő,ügyintéző",
  "related_entity_type": "system",
  "related_entity_id": null,
  "user_id": null
}
```

Response: Created notification object

#### Mark notification as read

```
PUT /api/v1/notifications/{notification_id}/read
```

Marks a notification as read. Users can only mark their own notifications as read, while administrators can mark any notification as read.

Path parameters:
- `notification_id`: The ID of the notification to mark as read

Response: Updated notification object

#### Mark all notifications as read

```
PUT /api/v1/notifications/read-all
```

Marks all notifications for the current user as read.

Response:
```json
{
  "count": 5,
  "message": "5 értesítés olvasottnak jelölve"
}
```

### Admin Endpoints

#### Reset database (admin only)

```
POST /api/v1/admin/reset-database
```

Resets the database to its initial state. Only accessible by administrators.

Response:
```json
{
  "message": "Az adatbázis sikeresen visszaállítva alapállapotba. Az alapértelmezett termékkategóriák, terméktípusok és demo felhasználók létrehozva."
}
```

#### Update user settings (admin only)

```
PUT /api/v1/admin/users/{user_id}/settings
```

Updates a user's default settings. Only accessible by administrators.

Path parameters:
- `user_id`: The ID of the user whose settings to update

Request body: Same as `PUT /users/me/settings`

Response: Updated settings object

## Data Models

### User Models

#### User

- `id`: Integer - User ID
- `email`: String - Email address (unique)
- `password_hash`: String - Hashed password (not exposed in APIs)
- `role`: String - Role ("termelő", "ügyintéző", or "admin")
- `company_name`: String - Company name
- `tax_id`: String - Tax ID
- `contact_name`: String - Contact person's name
- `phone_number`: String - Contact phone number
- `is_active`: Boolean - Whether the user is active
- `activation_token`: String - Account activation token (not exposed in APIs)
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

#### User Default Settings

- `id`: Integer - Settings ID
- `user_id`: Integer - User ID
- `default_product_type_id`: Integer (optional) - Default product type ID
- `default_quality_grade_id`: Integer (optional) - Default quality grade ID
- `default_quantity_unit`: String - Default quantity unit ("kg" or "tonna")
- `default_category_id`: Integer (optional) - Default category ID
- `default_product_type_name`: String (optional) - Default product type name
- `default_quality_grade_name`: String (optional) - Default quality grade name
- `default_category_name`: String (optional) - Default category name
- `has_quality_grades`: Boolean - Whether the default product type has quality grades
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

#### Password Reset Token

- `id`: Integer - Token ID
- `user_id`: Integer - User ID
- `token`: String - Reset token
- `expires_at`: DateTime - Expiration timestamp
- `is_used`: Boolean - Whether the token has been used
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

### Product Models

#### Product Category

- `id`: Integer - Category ID
- `name`: String - Category name
- `description`: String (optional) - Description
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

#### Product Type

- `id`: Integer - Type ID
- `category_id`: Integer - Category ID
- `name`: String - Type name
- `description`: String (optional) - Description
- `has_quality_grades`: Boolean - Whether the type has quality grades
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

#### Quality Grade

- `id`: Integer - Grade ID
- `product_type_id`: Integer - Product type ID
- `name`: String - Grade name
- `min_shoulder_diameter`: Decimal (optional) - Minimum shoulder diameter (mm)
- `max_shoulder_diameter`: Decimal (optional) - Maximum shoulder diameter (mm)
- `min_length`: Decimal (optional) - Minimum length (mm)
- `max_length`: Decimal (optional) - Maximum length (mm)
- `description`: String (optional) - Description
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

### Offer Models

#### Offer

- `id`: Integer - Offer ID
- `user_id`: Integer - User ID (owner)
- `product_type_id`: Integer - Product type ID
- `quality_grade_id`: Integer (optional) - Quality grade ID
- `quantity_in_kg`: Decimal - Quantity in kilograms
- `delivery_date`: Date - Delivery date
- `status`: String - Status ("CREATED", "CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "REJECTED_BY_USER", or "FINALIZED")
- `confirmed_quantity`: Decimal (optional) - Confirmed quantity in kilograms
- `confirmed_price`: Decimal (optional) - Confirmed price per kilogram
- `note`: String (optional) - Note
- `created_by_user_id`: Integer - User ID of the creator
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

#### Offer Log

- `id`: Integer - Log ID
- `offer_id`: Integer - Offer ID
- `old_status`: String (optional) - Previous status
- `new_status`: String - New status
- `changed_by`: Integer - User ID of the user who made the change
- `note`: String (optional) - Note
- `created_at`: DateTime - Creation timestamp

### Notification Models

#### Notification

- `id`: Integer - Notification ID
- `user_id`: Integer (optional) - User ID (if targeted to a specific user)
- `type`: String - Type ("info", "success", "warning", "error", or "update")
- `message`: String - Message
- `detail`: String (optional) - Detailed description
- `is_read`: Boolean - Whether the notification has been read
- `target_roles`: String (optional) - Target roles (comma-separated)
- `related_entity_type`: String (optional) - Related entity type
- `related_entity_id`: Integer (optional) - Related entity ID
- `created_at`: DateTime - Creation timestamp
- `updated_at`: DateTime - Last update timestamp

## Status Flow

Offers follow a specific status flow:

1. `CREATED`: The initial status when an offer is created
2. `CONFIRMED_BY_COMPANY`: The offer has been confirmed by an operator
3. `ACCEPTED_BY_USER`: The offer has been accepted by the producer
4. `REJECTED_BY_USER`: The offer has been rejected by the producer
5. `FINALIZED`: The offer has been finalized by an operator

## Authentication Flow

1. Register a new user account using the `/auth/register` endpoint
2. Activate the account using the token sent via email and the `/auth/activate-account` endpoint
3. Log in using the `/auth/login` endpoint to obtain a JWT token
4. Include the token in the `Authorization` header of subsequent requests

## Usage Examples

### Creating and Managing an Offer (Producer Perspective)

1. Log in as a producer:
   ```
   POST /api/v1/auth/login
   {
     "username": "<EMAIL>",
     "password": "password123"
   }
   ```

2. Create a new offer:
   ```
   POST /api/v1/offers
   {
     "product_type_id": 1,
     "quality_grade_id": 1,
     "quantity_in_kg": 1000,
     "delivery_date": "2023-10-15",
     "note": "Example offer note"
   }
   ```

3. Check for notifications about offer confirmations:
   ```
   GET /api/v1/notifications/me
   ```

4. Accept a confirmed offer:
   ```
   POST /api/v1/offers/1/accept
   ```

### Processing Offers (Operator Perspective)

1. Log in as an operator:
   ```
   POST /api/v1/auth/login
   {
     "username": "<EMAIL>",
     "password": "password123"
   }
   ```

2. List all created offers:
   ```
   GET /api/v1/offers?status=CREATED
   ```

3. Confirm an offer:
   ```
   POST /api/v1/offers/1/confirm
   {
     "confirmed_quantity": 900,
     "confirmed_price": 300,
     "note": "Confirmed offer note"
   }
   ```

4. Check for accepted offers:
   ```
   GET /api/v1/offers?status=ACCEPTED_BY_USER
   ```

5. Finalize an accepted offer:
   ```
   POST /api/v1/offers/1/finalize
   ```

### Admin Tasks

1. Log in as an administrator:
   ```
   POST /api/v1/auth/login
   {
     "username": "<EMAIL>",
     "password": "admin123"
   }
   ```

2. Retrieve system statistics:
   ```
   GET /api/v1/offers/statistics
   ```

3. Create a new product category:
   ```
   POST /api/v1/products/categories
   {
     "name": "Uborka",
     "description": "Különböző uborkafélék"
   }
   ```

4. Create a new product type:
   ```
   POST /api/v1/products/types
   {
     "name": "Kígyóuborka",
     "description": "Friss kígyóuborka",
     "has_quality_grades": true,
     "category_id": 3
   }
   ```

5. Send a system notification to all users:
   ```
   POST /api/v1/notifications
   {
     "type": "info",
     "message": "Rendszerkarbantartás lesz",
     "detail": "A rendszer karbantartás miatt nem lesz elérhető 2023. október 10-én 18:00 és 20:00 között.",
     "target_roles": "termelő,ügyintéző,admin",
     "related_entity_type": "system"
   }
   ```

## Pagination

Many list endpoints support pagination using the following parameters:

- `skip`: Number of records to skip
- `limit`: Maximum number of records to return

The `/offers/paginated` endpoint provides more advanced pagination with:

- `page`: Page number (starting from 1)
- `page_size`: Number of items per page
- `total`: Total number of items
- `pages`: Total number of pages

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- 400 Bad Request: Invalid input or operation
- 401 Unauthorized: Missing or invalid authentication
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 422 Unprocessable Entity: Validation error
- 500 Internal Server Error: Server-side error

Error responses include a `detail` field explaining the error:

```json
{
  "detail": "A felhasználó már létezik: <EMAIL>"
}
```

## Rate Limits

The API does not currently implement rate limiting.

## Changelog

### Version 1.0.0 (2023-09-01)
- Initial release of the API

## Security Considerations

- JWT tokens expire after 30 minutes by default.
- Password reset tokens expire after 24 hours.
- Passwords are stored as bcrypt hashes.
- Users must activate their accounts via email before they can log in.
- Role-based access control is implemented for all endpoints.
- All inputs are validated using Pydantic models.
