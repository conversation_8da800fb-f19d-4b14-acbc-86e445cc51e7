# Frontend - streamlit_app_components Architecture

## 1. Overview

This document focuses on the `components` directory within the Streamlit frontend application, which constitutes a crucial part of a client-server architecture for managing agricultural product offers. These component modules provide reusable UI elements that maintain consistent styling, behavior, and interaction patterns throughout the application.

## 2. Component Directory Structure

```
streamlit_app/components/
├── __init__.py              # Package initialization
├── activity_feed.py         # Notifications and activity display
├── auth_forms.py            # Authentication forms (login, register, etc.)
├── calendar_card.py         # Calendar visualization card components
├── calendar_component.py    # Calendar interactive components
├── calendar_views.py        # Calendar view implementation classes
├── continue_button.py       # Standardized action button
├── data_display.py          # Data visualization components
├── display_data_components.py # Offer detail visualization
├── multi_step_form.py       # Multi-step form framework
├── notification.py          # Notification display utilities
├── sidebar.py               # Application navigation sidebar
├── tovacvv_form.py          # Example validation form component
```

## 3. Component Architecture Principles

The components in this application follow these architectural principles:

1. **Self-containment**: Each component encapsulates its functionality and manages its own state
2. **Reusability**: Components are designed to be reusable across different pages
3. **Parameterization**: Components accept configuration via parameters
4. **Consistent return patterns**: Components return standardized data structures
5. **Mobile-awareness**: Components adapt to different device types
6. **Progressive enhancement**: Components work with or without JavaScript

## 4. Core Component Types

### 4.1 Form Components

Components that collect and validate user input:

#### `auth_forms.py`

Implements authentication forms with validation:

```python
def login_form():
    """
    Bejelentkezési űrlap komponens.
    
    Returns:
        bool: Sikeres bejelentkezés esetén True, egyébként False
    """
    st.subheader("Bejelentkezés")
    
    with st.form("login_form"):
        email = st.text_input("E-mail cím", placeholder="<EMAIL>")
        password = st.text_input("Jelszó", type="password")
        submit = st.form_submit_button("Bejelentkezés", type="primary", use_container_width=True)
    
    if submit:
        if not email or not password:
            show_error("Kérjük, adja meg az e-mail címet és a jelszót.")
            return False
        
        if not validate_email(email):
            show_error("Érvénytelen e-mail cím formátum.")
            return False
        
        # Bejelentkezés API hívás
        success, result = auth.login(email, password)
        
        if success:
            show_success("Sikeres bejelentkezés!")
            return True
        else:
            show_error(f"Bejelentkezési hiba: {result}")
            return False
    
    return False
```

#### `multi_step_form.py`

Provides a framework for multi-step forms with validation:

```python
def render_multi_step_form(steps, validators, form_id=None, on_save=None):
    """
    Többlépéses űrlap megjelenítése.
    
    Args:
        steps (list): Lépés függvények listája
        validators (list): Validátor függvények listája
        form_id (str, optional): Űrlap azonosítója. Defaults to None.
        on_save (function, optional): Mentés callback függvény. Defaults to None.
    
    Returns:
        tuple: (current_step, form_data)
    """
    # Form data initialization
    if "form_data" not in st.session_state:
        st.session_state.form_data = {}
    
    if "current_step" not in st.session_state:
        st.session_state.current_step = 0
    
    # Number of steps
    total_steps = len(steps)
    
    # Call step function
    step_data = steps[st.session_state.current_step](st.session_state.form_data)
    
    # Update form data
    st.session_state.form_data.update(step_data)
    
    # Update form data with step
    st.session_state.form_data["current_step"] = st.session_state.current_step
    st.session_state.form_data["total_steps"] = total_steps
    
    # Navigation buttons and validation logic...
```

### 4.2 Display Components

Components that present data in structured formats:

#### `data_display.py`

Implements data visualization and table displays:

```python
def display_offer_table(offers, with_actions=False, pagination=True):
    """
    Ajánlatok megjelenítése táblázatban.

    Megjelenő oszlopok és sorrend:
        - ID
        - Beszállítás dátuma
        - Termék
        - Mennyiség
        - Visszaigazolt mennyiség
        - Visszaigazolt ár
        - Státusz
        
    Args:
        offers (list): Ajánlatok listája
        with_actions (bool): Műveletek megjelenítése
        pagination (bool): Lapozás engedélyezése (alapértelmezetten True)

    Returns:
        pd.DataFrame: Táblázat objektum
    """
    if not offers:
        st.info("Nincsenek megjeleníthető ajánlatok.")
        return None
    
    # Data preparation
    data = []
    for offer in offers:
        # Basic data
        row = {
            "ID": offer.get("id"),
            "Státusz": format_status(offer.get("status", "")),
            "Beszállítás dátuma": format_date(offer.get("delivery_date", "")),
            "Termék": offer.get("product_type", {}).get("name", "Ismeretlen termék"),
            "Mennyiség": format_quantity(offer.get('quantity_in_kg', 0))
        }
        
        # Additional data transformation and formatting...
        
        data.append(row)
    
    # DataFrame creation and display
    df = pd.DataFrame(data)
    
    # Styling and display logic...
    
    return df
```

#### `display_data_components.py`

Specialized components for offer detail visualizations:

```python
def display_offer_details(offer_id: int) -> None:
    """
    Ajánlat részletes megjelenítése modernebb, áttekinthetőbb UI elemekkel.
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    success, offer = offers_api.get_offer(offer_id)
    if not success or not offer:
        st.error("Az ajánlat nem található!")
        return
    display_offer_status_card(offer)
    display_offer_detail_cards(offer)
    if offer.get("confirmed_quantity") is not None:
        display_quantity_indicator(offer)
    display_offer_timeline(offer)
    display_collapsible_details(offer)
```

### 4.3 Navigation Components

Components that control application navigation:

#### `sidebar.py`

Implements the main navigation system with role-based access:

```python
def render_sidebar() -> None:
    """
    Render the sidebar for the application, supporting both desktop and mobile views.
    """
    init_favorites()
    if "debug_mode" not in st.session_state:
        st.session_state.debug_mode = False
    detect_mobile()
    is_mobile = st.session_state.get("mobile_view", False)
    
    if is_mobile:
        # Mobile navigation rendering
        render_mobile_header()
        render_mobile_navigation()
        render_quick_actions()
        render_bottom_navbar()
        add_sidebar_toggle()
        create_hamburger_menu()
    else:
        # Desktop navigation rendering
        with st.sidebar:
            # Application header
            st.title(config.APP_NAME)
            # Current day info
            current_date = datetime.datetime.now()
            st.caption(f"📅 {current_date.strftime('%Y. %m. %d.')} - {get_hungarian_day(current_date.weekday())}")
            
            # Authentication-dependent content
            if not is_authenticated():
                # Login options
                # ...
            else:
                # User profile and role-based menu
                user = get_current_user()
                role = user.get("role", "").lower()
                
                # Role-specific menu rendering
                if role == "admin":
                    # Admin menus
                    # ...
                elif role == "ügyintéző":
                    # Operator menus
                    # ...
                elif role == "termelő":
                    # Producer menus
                    # ...
```

### 4.4 Feedback Components

Components that provide user feedback:

#### `notification.py`

Standardized notification display utilities:

```python
def show_success(message, icon="✅"):
    """
    Sikeres művelet értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.success(f"{icon} {message}")

def show_info(message, icon="ℹ️"):
    """
    Információs értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.info(f"{icon} {message}")

def show_warning(message, icon="⚠️"):
    """
    Figyelmeztető értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.warning(f"{icon} {message}")

def show_error(message, icon="❌"):
    """
    Hiba értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.error(f"{icon} {message}")
```

#### `continue_button.py`

Standardized action button implementation:

```python
def render_continue_button(on_click=None, label="TOVÁBB", key=None):
    """
    "TOVÁBB" (Continue) gomb megjelenítése.
    
    Args:
        on_click (function, optional): Kattintás eseménykezelő. Defaults to None.
        label (str, optional): Gomb felirata. Defaults to "TOVÁBB".
        key (str, optional): Gomb egyedi azonosítója. Defaults to None.
    
    Returns:
        bool: True, ha a gombra kattintottak, egyébként False
    """
    # Button display
    if st.button(label, key=key, type="primary"):
        # Call click event handler if exists
        if on_click:
            on_click()
        
        return True
    
    return False
```

### 4.5 Complex Visualization Components

#### `activity_feed.py`

Implements a rich activity feed with real-time data:

```python
def render_activity_feed(max_items=5, show_all_button=True, height=None):
    """
    Értesítési feed megjelenítése.
    
    Args:
        max_items (int): Maximum hány értesítést jelenítsen meg.
        show_all_button (bool): "Összes megjelenítése" gomb megjelenítése.
        height (int): Opcionális paraméter - már nem használt.
    """
    # Get current user and role
    user = get_current_user()
    user_role = user.get("role", "").lower() if user else None
    
    # Container for notifications
    feed_container = st.container(border=True)
    
    with feed_container:
        # Header and refresh button in one row, mobile-friendly
        col1, col2 = st.columns([4, 1])
        with col1:
            st.subheader("🔔 Tevékenységek és értesítések")
        with col2:
            if st.button("🔄", key="refresh_notifications_btn", help="Értesítések frissítése"):
                st.session_state.refresh_notifications = True
                if "activity_data" in st.session_state:
                    del st.session_state.activity_data
                st.rerun()
        
        # Data loading and display logic...
```

#### Calendar components

The calendar visualization system consists of several coordinated components:

- `calendar_card.py`: Card-based visualization
- `calendar_component.py`: Interactive calendar components
- `calendar_views.py`: Class-based view implementations

Together they create a flexible calendar system that adapts to different view requirements.

## 5. Design Patterns

### 5.1 Component Initialization Pattern

Components follow a consistent initialization pattern:

```python
def render_component_name(param1, param2=default_value):
    """
    Component docstring with parameter descriptions.
    
    Args:
        param1: Description
        param2: Description with default
        
    Returns:
        return_type: Description of return value
    """
    # 1. Parameter validation/preparation
    
    # 2. State initialization from session_state
    if "component_state" not in st.session_state:
        st.session_state.component_state = initial_value
    
    # 3. Container creation for component
    component_container = st.container()
    
    # 4. Component rendering within container
    with component_container:
        # UI elements
        
    # 5. Event handling
    if st.button("Action"):
        # Handle action
        
    # 6. Return value preparation
    return result
```

### 5.2 OOP Calendar View Pattern

The calendar system implements a class hierarchy for different view types:

```python
class CalendarView:
    """
    Naptár nézet alaposztály.
    
    Ez az osztály a közös funkcionalitást biztosítja a különböző naptár nézetek számára,
    mint például az események előkészítése, összesítő metrikák megjelenítése és naptár renderelése.
    """
    
    def __init__(self, data, date_range, use_interactive=False, card_only=False):
        """
        Naptár nézet inicializálása.
        
        Args:
            data (pd.DataFrame): Naptári adatok DataFrame-ben
            date_range (tuple): (start_date, end_date) - dátum tartomány
            use_interactive (bool): Interaktív naptár használata
            card_only (bool): Csak kártyaalapú nézet használata (minden eszközön)
        """
        self.data = data
        self.start_date, self.end_date = date_range
        self.use_interactive = use_interactive
        self.is_mobile = st.session_state.get("is_mobile", False)
        self.card_only = card_only
        self.check_if_dataframe_valid()
        
    def prepare_events(self):
        """Data transformation logic"""
        
    def show_summary_metrics(self):
        """Metrics display logic"""
        
    def render_calendar(self, calendar_events, reference_date):
        """Rendering strategy selection"""
        
    def show(self):
        """Abstract method to be implemented by subclasses"""
        raise NotImplementedError("Az alosztályoknak implementálniuk kell ezt a metódust")


class DailyView(CalendarView):
    """Daily calendar view"""
    def show(self):
        """Daily view implementation"""
        
class WeeklyView(CalendarView):
    """Weekly calendar view"""
    def show(self):
        """Weekly view implementation"""
```

### 5.3 Strategy Pattern for Rendering

The calendar system uses a strategy pattern to select different rendering approaches:

```python
def render_calendar(self, calendar_events, reference_date):
    """
    Naptár renderelése az eseményekkel.
    
    Args:
        calendar_events (list): Naptár események listája
        reference_date (datetime.date): Referenciadátum a naptár megjelenítéséhez
    """
    # If card_only setting is true, or using mobile device, use card-based view
    if self.card_only or self.is_mobile:
        render_card_calendar(calendar_events, (self.start_date, self.end_date))
    elif self.use_interactive and not self.is_mobile:
        # Interactive calendar - desktop view only
        render_interactive_calendar(calendar_events, reference_date)
    else:
        # Simple CSS calendar
        render_css_calendar(calendar_events, reference_date)
```

### 5.4 Responsive Design Pattern

Components adapt to different device types:

```python
def detect_mobile_device():
    """
    JavaScript kód a képernyőméret detektálásához. Beállítja a session_state['is_mobile'] értékét.
    """
    st.markdown("""
    <script>
        function detectMobile() {
            return window.innerWidth <= 768;
        }
        if (window.parent) {
            const isMobile = detectMobile();
            const streamlitEvent = new CustomEvent("streamlit:setComponentValue", {detail: {is_mobile: isMobile}});
            window.parent.dispatchEvent(streamlitEvent);
        }
    </script>
    """, unsafe_allow_html=True)
```

## 6. UI Component Implementation Details

### 6.1 UI Construction Techniques

#### HTML/CSS Injection

Many components use direct HTML/CSS injection for more control over styling:

```python
st.markdown(
    f"""
    <div style='padding: 20px; border-radius: 8px; background-color: {status_color}; color: white; 
    display: flex; flex-direction: column; margin-bottom: 20px;'>
        <div style='display: flex; justify-content: space-between; align-items: center;'>
            <div style='font-size: 22px; font-weight: bold;'>{status_text}</div>
            <div style='background-color: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 16px;'>
                ID: {offer.get('id')}
            </div>
        </div>
        <div style='margin-top: 10px; font-size: 14px;'>
            Utoljára módosítva: {format_datetime(offer.get('updated_at', ''))}
        </div>
    </div>
    """,
    unsafe_allow_html=True
)
```

#### JavaScript Integration

Some components integrate JavaScript for enhanced interactivity:

```python
# JavaScript for tooltip functionality
st.markdown("""
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show tooltips on hover
    const events = document.querySelectorAll('.event');
    
    events.forEach(event => {
        event.addEventListener('mouseenter', function() {
            const tooltip = this.querySelector('.calendar-tooltip');
            if (tooltip) {
                tooltip.style.display = 'block';
            }
        });
        
        event.addEventListener('mouseleave', function() {
            const tooltip = this.querySelector('.calendar-tooltip');
            if (tooltip) {
                tooltip.style.display = 'none';
            }
        });
    });
});
</script>
""", unsafe_allow_html=True)
```

#### Streamlit Native Components

Many components use Streamlit's native UI elements for simpler cases:

```python
# Columns for responsive layout
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("Teljes mennyiség", f"{total_quantity:,.2f} kg")
with col2:
    st.metric("Teljes érték", f"{total_value:,.0f} Ft")
```

### 6.2 Data Transformation Techniques

Components often transform data for display:

```python
def prepare_events(self):
    """
    Adatok konvertálása naptár események formátumára.
    
    Returns:
        list: Naptár események listája
    """
    calendar_events = []
    
    if self.data.empty:
        return calendar_events
        
    for _, row in self.data.iterrows():
        try:
            if 'delivery_date' not in row:
                continue
                
            delivery_date = row['delivery_date']
            if isinstance(delivery_date, str):
                delivery_date = datetime.datetime.strptime(delivery_date, '%Y-%m-%d').date()
            elif isinstance(delivery_date, datetime.datetime):
                delivery_date = delivery_date.date()
            
            # Állapot alapú színtérkép
            status = row.get('status', '')
            color_map = {
                'CREATED': '#90CAF9',             # világoskék
                'CONFIRMED_BY_COMPANY': '#66BB6A',  # zöld
                'ACCEPTED_BY_USER': '#4CAF50',    # sötétebb zöld
                'REJECTED_BY_USER': '#EF5350',    # piros
                'FINALIZED': '#9C27B0'            # lila
            }
            
            color = color_map.get(status, '#9E9E9E')  # alapértelmezetten szürke
            
            # Event object creation
            event = {
                'id': row.get('id', ''),
                'date': delivery_date.strftime('%Y-%m-%d'),
                'title': f"{product_name}: {quantity:.1f} kg",
                # Additional event properties
            }
            
            calendar_events.append(event)
        except Exception as e:
            st.error(f"Hiba az esemény feldolgozása során: {str(e)}")
            continue
            
    return calendar_events
```

### 6.3 Mobile Adaptation Strategies

Components adapt to mobile devices with specialized layouts:

```python
def render_mobile_navigation() -> None:
    """
    Render a mobile-friendly navigation sidebar.
    """
    st.markdown("### 📱 Navigáció")
    if is_authenticated():
        user = get_current_user()
        st.caption(f"👤 {user.get('contact_name', '')} ({user.get('role', '').capitalize()})")
        role = user.get("role", "").lower()
        menu_options = ["Főmenü"]
        if st.session_state.user_favorites:
            menu_options.append("⭐ Kedvencek")
        if role == "admin":
            menu_options.extend(["🔧 Admin", "🛠️ Eszközök"])
        selected_menu = st.selectbox("Menü", menu_options, label_visibility="collapsed")
        if selected_menu == "⭐ Kedvencek":
            render_mobile_menu_section(st.session_state.user_favorites)
        # Additional mobile-specific logic
```

## 7. Component Integration

### 7.1 Component Dependencies

Components often depend on each other and shared utilities:

```
activity_feed.py
  ├── utils.session
  ├── utils.formatting
  ├── components.notification
  ├── api.notifications
  └── app_config

calendar_views.py
  ├── components.calendar_component
  ├── components.calendar_card
  ├── utils.responsive_ui
  ├── utils.formatting
  └── plotly.express
```

### 7.2 Component Integration Pattern

Components are designed to be integrated into pages with minimal coupling:

```python
# Example page integrating multiple components
import streamlit as st
from components.sidebar import render_sidebar
from components.activity_feed import render_activity_feed
from components.data_display import display_offer_table
from api import offers as offers_api

# Page configuration
st.set_page_config(page_title="Dashboard", page_icon="🏠", layout="wide")

# Render sidebar
render_sidebar()

# Page title
st.title("🏠 Dashboard")

# Activity feed component
render_activity_feed(max_items=3)

# Load data
success, offers = offers_api.get_offers()

# Display data
if success and offers:
    display_offer_table(offers)
else:
    st.info("No offers to display")
```

## 8. State Management

### 8.1 Session State Usage

Components use Streamlit's session state for persistent state:

```python
# Initialize component state
if "activity_data" not in st.session_state:
    st.session_state.activity_data = []

# Update component state
st.session_state.activity_data = activities

# Read component state
activities = st.session_state.activity_data

# Toggle state
if "show_all_activities" not in st.session_state:
    st.session_state.show_all_activities = False
    
button_text = "Kevesebb mutatása" if st.session_state.get("show_all_activities", False) else "Összes mutatása"

if st.button(button_text, key="toggle_activities", use_container_width=True):
    st.session_state.show_all_activities = not st.session_state.get("show_all_activities", False)
    st.rerun()
```

### 8.2 Component-Specific Keys

Components use unique keys to avoid collisions:

```python
# Unique key generation for pagination
page_size = 10
total_pages = (len(offers) + page_size - 1) // page_size

col1, col2, col3 = st.columns([1, 3, 1])
with col2:
    current_page = st.number_input(
        "Oldal",
        min_value=1,
        max_value=total_pages,
        value=1,
        key=f"pagination_{uuid.uuid4()}"
    )
```

## 9. Error Handling

### 9.1 Component Error Handling Pattern

Components implement comprehensive error handling:

```python
try:
    # API call to get activities
    success, result = notifications_api.get_notifications(include_read=include_read, limit=max_items)
    
    if success and result and isinstance(result, dict) and "items" in result:
        return result["items"], None
    else:
        return [], "Nem sikerült értesítéseket lekérni az API-ból."
except Exception as e:
    print(f"Kivétel a get_real_activities függvényben: {str(e)}")
    import traceback
    print(f"Kivétel részletei: {traceback.format_exc()}")
    return [], f"Váratlan hiba történt értesítések lekérésekor: {str(e)}"
```

### 9.2 Fallback Strategies

Components implement fallbacks for graceful degradation:

```python
# Try to get the status color
try:
    status_code = offer.get("status", "")
    color = "#23272e"
    if status_code in config.OFFER_STATUSES:
        mapped_color = config.OFFER_STATUSES[status_code].get("color", "#23272e")
        if not mapped_color or mapped_color in [None, "", "#fff", "#ffffff"] or is_light(mapped_color):
            color = "#23272e"
        else:
            color = mapped_color
    row["status_color"] = str(color) if color else "#23272e"
except:
    # Fallback to default color
    row["status_color"] = "#23272e"
```

## 10. Testing and Debugging

### 10.1 Debug Mode

Components support a debug mode for development:

```python
if st.session_state.get("debug_mode", False):
    st.sidebar.warning("🛠️ Debug mód AKTÍV!", icon="⚠️")
with st.expander("🐞 Fejlesztői beállítások", expanded=False):
    debug_status = "Bekapcsolva ✅" if st.session_state.debug_mode else "Kikapcsolva ❌"
    if st.button(f"Debug mód: {debug_status}", key="debug_toggle", use_container_width=True):
        toggle_debug_mode()
        st.rerun()
```

### 10.2 Component Testing

Components include internal logging:

```python
print(f"==== ÉRTESÍTÉS KOMPONENS BETÖLTÉSE KEZDŐDIK ====")
print(f"Fejlesztői mód: {DEVELOPER_MODE}")
print(f"API_BASE_URL: {config.API_BASE_URL}")

# API import in try-except block
try:
    from api import notifications as notifications_api
    # Check if module actually contains required functions
    if not hasattr(notifications_api, 'get_notifications'):
        print("HIBA: notifications_api modult sikerült importálni, de hiányzik a get_notifications függvény")
        NOTIFICATIONS_API_AVAILABLE = False
    else:
        NOTIFICATIONS_API_AVAILABLE = True
        print("SIKER: notifications_api modul sikeresen betöltve")
except (ImportError, ModuleNotFoundError) as e:
    print(f"HIBA: Nem sikerült betölteni a notifications_api modult: {str(e)}")
    NOTIFICATIONS_API_AVAILABLE = False
```

## 11. Component API Reference

### 11.1 Activity Feed Component

```python
def render_activity_feed(max_items=5, show_all_button=True, height=None):
    """
    Értesítési feed megjelenítése.
    
    Args:
        max_items (int): Maximum hány értesítést jelenítsen meg.
        show_all_button (bool): "Összes megjelenítése" gomb megjelenítése.
        height (int): Opcionális paraméter - már nem használt.
    """
```

### 11.2 Authentication Forms

```python
def login_form():
    """
    Bejelentkezési űrlap komponens.
    
    Returns:
        bool: Sikeres bejelentkezés esetén True, egyébként False
    """

def register_form():
    """
    Regisztrációs űrlap komponens.
    
    Returns:
        bool: Sikeres regisztráció esetén True, egyébként False
    """

def password_reset_request_form():
    """
    Jelszó-visszaállítási kérelem űrlap komponens.
    
    Returns:
        bool: Sikeres kérelem esetén True, egyébként False
    """

def password_reset_form(token):
    """
    Jelszó-visszaállítási űrlap komponens.
    
    Args:
        token (str): Jelszó-visszaállítási token
        
    Returns:
        bool: Sikeres visszaállítás esetén True, egyébként False
    """
```

### 11.3 Calendar Components

```python
def render_css_calendar(events, month_date=None):
    """
    Reszponzív CSS alapú naptár megjelenítése
    
    Args:
        events (list): Az események listája
        month_date (datetime.date, optional): A megjelenítendő hónap
    """

def render_interactive_calendar(events, month_date=None):
    """
    Interaktív JavaScript alapú naptár megjelenítése
    
    Args:
        events (list): Az események listája
        month_date (datetime.date, optional): A megjelenítendő hónap
    """

def render_card_calendar(events, date_range):
    """
    Kártya-alapú naptárnézet megjelenítése, különösen hasznos mobil eszközökön.
    
    Args:
        events (list): Az események listája
        date_range (tuple): (start_date, end_date) a megjelenítendő időszak
    """
```

### 11.4 CalendarView Classes

```python
class CalendarView:
    """
    Naptár nézet alaposztály.
    
    Ez az osztály a közös funkcionalitást biztosítja a különböző naptár nézetek számára,
    mint például az események előkészítése, összesítő metrikák megjelenítése és naptár renderelése.
    """
    
    def __init__(self, data, date_range, use_interactive=False, card_only=False):
        """
        Naptár nézet inicializálása.
        
        Args:
            data (pd.DataFrame): Naptári adatok DataFrame-ben
            date_range (tuple): (start_date, end_date) - dátum tartomány
            use_interactive (bool): Interaktív naptár használata
            card_only (bool): Csak kártyaalapú nézet használata (minden eszközön)
        """


class DailyView(CalendarView):
    """Napi naptár nézet"""
    def show(self):
        """Napi nézet megjelenítése."""


class WeeklyView(CalendarView):
    """Heti naptár nézet"""
    def show(self):
        """Heti nézet megjelenítése."""


class MonthlyView(CalendarView):
    """Havi naptár nézet"""
    def show(self):
        """Havi nézet megjelenítése."""


class CustomView(CalendarView):
    """Egyedi időtartamú naptár nézet"""
    def show(self):
        """Egyedi nézet megjelenítése."""
```

### 11.5 Data Display Components

```python
def display_offer_table(offers, with_actions=False, pagination=True):
    """
    Ajánlatok megjelenítése táblázatban.
    
    Args:
        offers (list): Ajánlatok listája
        with_actions (bool): Műveletek megjelenítése
        pagination (bool): Lapozás engedélyezése (alapértelmezetten True)

    Returns:
        pd.DataFrame: Táblázat objektum
    """

def display_status_chart(offers):
    """
    Ajánlatok státusz szerinti megoszlásának megjelenítése kördiagramon.
    
    Args:
        offers (list): Ajánlatok listája
    """

def display_quantity_by_product_chart(offers):
    """
    Termékek szerinti mennyiség megoszlás diagram megjelenítése.
    
    Args:
        offers (list): Ajánlatok listája
    """

def display_monthly_quantity_chart(offers):
    """
    Havi mennyiség diagram megjelenítése.
    
    Args:
        offers (list): Ajánlatok listája
    """
```

### 11.6 Offer Detail Components

```python
def display_offer_details(offer_id: int) -> None:
    """
    Ajánlat részletes megjelenítése modernebb, áttekinthetőbb UI elemekkel.
    Args:
        offer_id (int): Az ajánlat azonosítója
    """

def display_offer_status_card(offer: Dict[str, Any]) -> None:
    """
    Státuszjelző kártya sötét, modern dizájnnal.
    Args:
        offer (dict): Ajánlat adatai
    """

def display_offer_detail_cards(offer: Dict[str, Any], is_mobile: bool = False) -> None:
    """
    Strukturált, sötét témájú információs kártyák (táblázatszerű sorok, mobilbarát).
    Args:
        offer (dict): Ajánlat adatai
        is_mobile (bool, optional): Mobilnézet használata. Defaults to False.
    """

def display_status_history(offer_id: int) -> None:
    """
    Ajánlat állapotváltozásainak történetét megjelenítő komponens.

    Args:
        offer_id (int): Az ajánlat azonosítója.
    """
```

### 11.7 Notification Components

```python
def show_success(message, icon="✅"):
    """
    Sikeres művelet értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """

def show_info(message, icon="ℹ️"):
    """
    Információs értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """

def show_warning(message, icon="⚠️"):
    """
    Figyelmeztető értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """

def show_error(message, icon="❌"):
    """
    Hiba értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """

def show_notification(message, type="info"):
    """
    Értesítés megjelenítése típus alapján.
    
    Args:
        message (str): Megjelenítendő üzenet
        type (str): Értesítés típusa (success, info, warning, error)
    """

def toast_notification(message, type="info"):
    """
    Toast értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        type (str): Értesítés típusa (success, info, warning, error)
    """
```

### 11.8 Sidebar Component

```python
def render_sidebar() -> None:
    """
    Render the sidebar for the application, supporting both desktop and mobile views.
    """

def render_page_button(page_path, button_text, container=st, use_container_width=True, with_favorite=True, section_prefix=None):
    """
    Render a page navigation button with optional favorite star.
    
    Args:
        page_path (str): The path to the page
        button_text (str): Text to display on the button
        container: The container to render the button in
        use_container_width (bool): Whether to use the full container width
        with_favorite (bool): Whether to show the favorite star
        section_prefix (str): Prefix for the button key
    """
```

## 12. LLM Integration Guidelines

When generating or modifying components for this application, follow these guidelines:

### 12.1 Component Structure

1. **Consistent Naming**: Follow the established naming patterns
   - Component rendering functions should use the `render_*` prefix
   - UI display functions should use the `display_*` prefix

2. **Documentation**: Include comprehensive docstrings
   - Document all parameters with types and descriptions
   - Document return values with types and descriptions

3. **State Management**: Use Streamlit's session state for persistent state
   - Initialize state variables at the beginning of components
   - Use unique keys to avoid collisions

4. **Error Handling**: Implement comprehensive error handling
   - Use try/except blocks for API calls and data processing
   - Provide user-friendly error messages
   - Implement fallbacks for graceful degradation

### 12.2 Component Integration

1. **Minimal Coupling**: Components should be loosely coupled
   - Pass data explicitly via parameters
   - Avoid direct dependencies on other components

2. **Responsive Design**: Components should adapt to different device types
   - Check for mobile device with `is_mobile = st.session_state.get("is_mobile", False)`
   - Implement mobile-specific layouts when appropriate

3. **Consistent Return Pattern**: Components should return standardized data structures
   - Form components should return (success, data) tuples
   - Display components should return None or the displayed data

4. **Progressive Enhancement**: Components should work with or without JavaScript
   - Use HTML/CSS for basic styling
   - Use JavaScript for enhanced interactivity only when necessary

### 12.3 Example Component

```python
def render_new_component(data, max_items=5, show_details=True):
    """
    New component description.
    
    Args:
        data (list): The data to display
        max_items (int): Maximum number of items to display
        show_details (bool): Whether to show details
        
    Returns:
        tuple: (bool, list) - Success flag and processed data
    """
    # Check for mobile device
    is_mobile = st.session_state.get("is_mobile", False)
    
    # Initialize component state
    if "component_state" not in st.session_state:
        st.session_state.component_state = []
    
    # Data validation
    if not data or not isinstance(data, list):
        st.info("No data to display")
        return False, []
    
    # Component container
    component_container = st.container(border=True)
    
    with component_container:
        # Header
        st.subheader("Component Title")
        
        # Content
        try:
            # Data processing
            processed_data = []
            for item in data[:max_items]:
                # Process item
                processed_data.append(item)
                
            # Display
            if is_mobile:
                # Mobile layout
                for item in processed_data:
                    st.write(item)
            else:
                # Desktop layout
                cols = st.columns(3)
                for i, item in enumerate(processed_data):
                    with cols[i % 3]:
                        st.write(item)
                        
            # Actions
            if show_details and st.button("Show Details"):
                st.session_state.show_details = not st.session_state.get("show_details", False)
                
            # Save state
            st.session_state.component_state = processed_data
            
            return True, processed_data
        except Exception as e:
            st.error(f"Error: {str(e)}")
            return False, []
```
