# Saved Filters Frontend Documentation

## Overview

This document describes the frontend implementation of the saved filters feature in the Agricultural Product Management System. The feature provides operators with an intuitive interface to save, load, and manage their filter configurations for efficient offer management.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Component Structure](#component-structure)
3. [User Interface Design](#user-interface-design)
4. [API Integration](#api-integration)
5. [State Management](#state-management)
6. [User Experience Flow](#user-experience-flow)
7. [Component Reference](#component-reference)
8. [Customization Guide](#customization-guide)
9. [Troubleshooting](#troubleshooting)

## Architecture Overview

The saved filters frontend follows a modular component architecture integrated with the existing offer management system:

```
┌─────────────────────────────────────────────────────────────┐
│                    Offer Management Page                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │  Filter Panel   │  │  Data Table     │  │  Actions    │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│           │                                                │
│           ▼                                                │
│  ┌─────────────────────────────────────────────────────────┤ │
│  │              Saved Filters Component                    │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │ │
│  │  │   Quick     │ │    Save     │ │     Manage      │   │ │
│  │  │   Access    │ │   Dialog    │ │     Panel       │   │ │
│  │  │             │ │             │ │                 │   │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack

- **Framework**: Streamlit
- **Styling**: Custom CSS with responsive design
- **State Management**: Streamlit session state
- **API Communication**: Python requests library
- **UI Components**: Custom Streamlit components

## Component Structure

### File Organization

```
streamlit_app/
├── api/
│   └── saved_filters.py          # API client for saved filters
├── pages/
│   └── operator/
│       └── offer_management/
│           └── saved_filter_ui.py  # Main UI components
└── utils/
    └── session.py                # Session utilities
```

### Component Hierarchy

```
SavedFiltersManagement
├── QuickAccessBar
│   ├── FilterChip (multiple)
│   └── ManageButton
├── SaveFilterDialog
│   ├── NameInput
│   ├── DescriptionInput
│   └── DefaultCheckbox
└── ManageFiltersModal
    ├── FilterList
    │   └── FilterItem (multiple)
    │       ├── LoadButton
    │       ├── EditButton
    │       ├── DefaultButton
    │       └── DeleteButton
    └── EditForm
```

## User Interface Design

### Quick Access Bar

The quick access bar provides one-click access to saved filters:

```
┌─────────────────────────────────────────────────────────────┐
│ Mentett szűrők: [★ Heti ajánlatok] [Alma szűrők] [⚙️ Kezelés] │
└─────────────────────────────────────────────────────────────┘
```

**Features**:
- Chip-style filter buttons
- Default filters marked with star (★)
- Management button for advanced operations
- Responsive design for mobile devices

### Save Filter Dialog

Modal dialog for saving current filter state:

```
┌─────────────────────────────────────┐
│  💾 Szűrő mentése                   │
├─────────────────────────────────────┤
│  Név: [___________________]         │
│  Leírás: [___________________]      │
│  □ Alapértelmezettként beállítás    │
│                                     │
│  [💾 Mentés] [❌ Mégse]            │
└─────────────────────────────────────┘
```

### Management Modal

Comprehensive filter management interface:

```
┌───────────────────────────────────────────────────────────────┐
│  ⚙️ Mentett szűrők kezelése                            [×]    │
├───────────────────────────────────────────────────────────────┤
│  📝 Jelenlegi szűrő mentése                                   │
│  Név: [___________________] □ Alapértlemezett [💾 Mentés]     │
│                                                               │
│  📚 Mentett szűrők                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Név            │ Létrehozva  │ Alap │ Műveletek          │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │ Heti ajánlatok │ 2025-05-21  │  ✓   │ [Load][Edit][Del] │ │
│  │ Alma szűrők    │ 2025-05-20  │      │ [Load][Edit][Del] │ │
│  └─────────────────────────────────────────────────────────┘ │
└───────────────────────────────────────────────────────────────┘
```

## API Integration

### API Client (`saved_filters.py`)

The API client handles all communication with the backend:

```python
def get_saved_filters(filter_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get all saved filters for the current user"""
    
def create_saved_filter(name: str, description: Optional[str], 
                       filter_type: str, filter_data: Dict[str, Any], 
                       is_default: bool = False) -> Optional[Dict[str, Any]]:
    """Create a new saved filter"""
    
def delete_saved_filter(filter_id: int) -> bool:
    """Delete a saved filter"""
    
def set_default_filter(filter_id: int) -> Optional[Dict[str, Any]]:
    """Set a filter as default"""
```

### Error Handling Strategy

```python
def api_call_with_error_handling(api_function, *args, **kwargs):
    """Wrapper for API calls with consistent error handling"""
    try:
        result = api_function(*args, **kwargs)
        return result, None
    except requests.RequestException as e:
        logger.error(f"Network error: {e}")
        return None, "Hálózati hiba. Kérjük, próbálja újra."
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return None, "Váratlan hiba történt. Kérjük, frissítse az oldalt."
```

### Authentication Integration

```python
def get_auth_token():
    """Get authentication token from session state"""
    return st.session_state.get("token")

def make_authenticated_request(url, method="GET", **kwargs):
    """Make authenticated request to API"""
    token = get_auth_token()
    if not token:
        raise AuthenticationError("No authentication token available")
    
    headers = kwargs.get("headers", {})
    headers["Authorization"] = f"Bearer {token}"
    kwargs["headers"] = headers
    
    return requests.request(method, url, **kwargs)
```

## State Management

### Session State Variables

The component manages several session state variables:

```python
# Filter-related state
st.session_state["selected_user_id"]           # Currently selected user filter
st.session_state["selected_product_type_id"]   # Product type filter
st.session_state["selected_status"]            # Status filter
st.session_state["date_from_filter"]           # Date range start
st.session_state["date_to_filter"]             # Date range end
st.session_state["column_filters"]             # Column-specific filters
st.session_state["complex_filters"]            # Complex filter configurations
st.session_state["search_query"]               # Full-text search query
st.session_state["sort_fields"]                # Sort configuration

# UI state
st.session_state["show_save_dialog"]           # Save dialog visibility
st.session_state["show_manage_modal"]          # Management modal visibility
st.session_state["selected_filter_id"]        # Currently selected filter
st.session_state["filter_applied"]            # Flag for filter changes
```

### Filter State Collection

```python
def collect_current_filter_state() -> Dict[str, Any]:
    """Collect current filter state from session"""
    return {
        "basic_filters": {
            "user_id": st.session_state.get("selected_user_id"),
            "product_type_id": st.session_state.get("selected_product_type_id"),
            "status": st.session_state.get("selected_status"),
            "date_from": format_date(st.session_state.get("date_from_filter")),
            "date_to": format_date(st.session_state.get("date_to_filter"))
        },
        "column_filters": st.session_state.get("column_filters", []),
        "complex_filters": st.session_state.get("complex_filters", {}),
        "search_query": st.session_state.get("search_query", ""),
        "sort_fields": st.session_state.get("sort_fields", [])
    }
```

### Filter State Application

```python
def apply_filter_state(filter_data: Dict[str, Any]) -> None:
    """Apply saved filter state to current session"""
    basic_filters = filter_data.get("basic_filters", {})
    
    # Apply basic filters
    if "user_id" in basic_filters:
        st.session_state["selected_user_id"] = basic_filters["user_id"]
    
    if "product_type_id" in basic_filters:
        st.session_state["selected_product_type_id"] = basic_filters["product_type_id"]
    
    # Convert date strings back to date objects
    if "date_from" in basic_filters and basic_filters["date_from"]:
        st.session_state["date_from_filter"] = parse_date(basic_filters["date_from"])
    
    # Apply other filter types
    st.session_state["column_filters"] = filter_data.get("column_filters", [])
    st.session_state["complex_filters"] = filter_data.get("complex_filters", {})
    st.session_state["search_query"] = filter_data.get("search_query", "")
    st.session_state["sort_fields"] = filter_data.get("sort_fields", [])
    
    # Trigger UI refresh
    st.session_state["filter_applied"] = True
    st.rerun()
```

## User Experience Flow

### Saving a Filter

1. **User configures filters** on the offer management page
2. **User clicks "Save Filter"** button or opens management modal
3. **Save dialog appears** with form fields
4. **User enters name and description**, optionally sets as default
5. **System validates input** and shows error messages if invalid
6. **API call made** to create the filter
7. **Success/error feedback** shown to user
8. **UI refreshes** to show new filter in quick access bar

### Loading a Filter

1. **User sees saved filters** in quick access bar or management modal
2. **User clicks filter chip** or load button
3. **System fetches filter data** from API (if not cached)
4. **Filter configuration applied** to current session state
5. **UI updates** to reflect new filter settings
6. **Data table refreshes** with filtered results
7. **Success message** confirms filter application

### Managing Filters

1. **User clicks management button** (⚙️) in quick access bar
2. **Management modal opens** showing all saved filters
3. **User can perform actions**:
   - Load filters
   - Set as default (global or personal)
   - Edit filter names/descriptions
   - Delete unused filters
4. **Changes reflected immediately** in the interface
5. **Modal closed** when done

## Component Reference

### Main Components

#### `render_saved_filters_management(on_reload=None)`

Main entry point for the saved filters UI.

**Parameters:**
- `on_reload`: Optional callback function triggered when filters are applied

**Usage:**
```python
def handle_filter_reload():
    # Refresh data table or other components
    st.rerun()

render_saved_filters_management(on_reload=handle_filter_reload)
```

#### `render_filter_quick_access_bar(on_reload=None)`

Renders the horizontal quick access bar with filter chips.

**Features:**
- Displays saved filters as clickable chips
- Shows default filters with star indicator
- Responsive design for different screen sizes
- Management button for advanced operations

#### `render_filter_management_modal()`

Renders the comprehensive filter management interface.

**Features:**
- Save current filter functionality
- List all saved filters in table format
- Edit, delete, and default setting operations
- Form validation and error handling

### Utility Functions

#### `generate_widget_key(base_key, *args)`

Generates unique widget keys to prevent Streamlit widget conflicts.

```python
key = generate_widget_key("save_dialog", user_id, filter_type)
# Returns: "saved_filter_a1b2c3d4"
```

#### `collect_current_filter_state()`

Extracts current filter configuration from session state.

#### `apply_filter_state(filter_data)`

Applies saved filter configuration to current session.

### CSS Classes and Styling

#### Filter Chip Styling

```css
.filter-chip {
    display: inline-block;
    padding: 5px 12px;
    margin: 0 6px 6px 0;
    border-radius: 16px;
    background-color: #f0f2f6;
    color: #31333F;
    font-size: 0.85em;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;
}

.filter-chip.default {
    background-color: #e3f2fd;
    border-color: #90caf9;
    font-weight: 500;
}
```

#### Modal Styling

```css
.filter-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.filter-modal-content {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}
```

## Customization Guide

### Adding New Filter Types

1. **Update filter data validation**:
```python
def validate_filter_data(filter_data, filter_type):
    if filter_type == "product":
        validate_product_filter_data(filter_data)
    elif filter_type == "user":
        validate_user_filter_data(filter_data)
```

2. **Extend state collection**:
```python
def collect_filter_state_by_type(filter_type):
    if filter_type == "product":
        return collect_product_filter_state()
    # Add other types...
```

### Customizing UI Components

#### Change Filter Chip Appearance

```python
def render_custom_filter_chip(filter_item):
    st.markdown(f"""
    <div class="custom-filter-chip" onclick="loadFilter('{filter_item['id']}')">
        🔍 {filter_item['name']}
    </div>
    """, unsafe_allow_html=True)
```

#### Add Custom Actions

```python
def render_filter_with_custom_actions(filter_item):
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📥 Load", key=f"load_{filter_item['id']}"):
            apply_filter_state(filter_item['filter_data'])
    
    with col2:
        if st.button("📧 Share", key=f"share_{filter_item['id']}"):
            share_filter(filter_item)
    
    # Add more custom actions...
```

### Internationalization

```python
TRANSLATIONS = {
    "en": {
        "save_filter": "Save Filter",
        "load_filter": "Load Filter",
        "manage_filters": "Manage Filters"
    },
    "hu": {
        "save_filter": "Szűrő mentése",
        "load_filter": "Szűrő betöltése", 
        "manage_filters": "Szűrők kezelése"
    }
}

def get_text(key, lang="hu"):
    return TRANSLATIONS.get(lang, {}).get(key, key)
```

## Troubleshooting

### Common Issues

#### 1. Filters Not Loading

**Symptoms**: Quick access bar empty, no filters in management modal

**Possible Causes**:
- Authentication token expired
- API server unavailable
- Network connectivity issues

**Solutions**:
```python
# Check authentication
if not st.session_state.get("token"):
    st.error("Kérjük, jelentkezzen be újra")
    
# Check API connectivity
try:
    response = requests.get(f"{config.API_BASE_URL}/health")
    if response.status_code != 200:
        st.error("API szerver nem elérhető")
except:
    st.error("Hálózati kapcsolat hiba")
```

#### 2. Widget Key Conflicts

**Symptoms**: Streamlit `DuplicateWidgetID` errors

**Solution**: Ensure unique widget keys
```python
# Use the provided utility function
key = generate_widget_key("widget_name", additional_params)
st.button("Click me", key=key)
```

#### 3. Filter State Not Applying

**Symptoms**: Filters loaded but UI doesn't update

**Solution**: Ensure proper state management
```python
def apply_with_refresh(filter_data):
    apply_filter_state(filter_data)
    st.session_state["force_refresh"] = True
    st.rerun()
```

#### 4. JSON Serialization Errors

**Symptoms**: Error saving filters with complex data types

**Solution**: Proper date/datetime handling
```python
def serialize_for_json(data):
    """Convert non-JSON serializable objects"""
    if isinstance(data, (date, datetime)):
        return data.isoformat()
    return data
```

### Debug Mode

Enable debug logging for troubleshooting:

```python
import logging

# Enable debug mode
if st.sidebar.checkbox("Debug Mode"):
    logging.basicConfig(level=logging.DEBUG)
    st.write("Session State:", st.session_state)
    st.write("Current Filter State:", collect_current_filter_state())
```

### Performance Optimization

#### Minimize API Calls

```python
@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_cached_saved_filters(user_id, filter_type):
    return get_saved_filters(filter_type)
```

#### Optimize Re-renders

```python
# Use session state to prevent unnecessary re-renders
if "filters_initialized" not in st.session_state:
    initialize_filters()
    st.session_state["filters_initialized"] = True
```

---

*This documentation is part of the Agricultural Product Management System documentation suite. For related documentation, see the main [DOCS_INDEX.md](../../DOCS_INDEX.md).*