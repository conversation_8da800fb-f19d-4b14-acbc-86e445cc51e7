# Frontend - streamlit_app Architecture

## 1. Overview

This document outlines the architecture and code organization of a Streamlit-based web application designed for agricultural product offer management. The application follows a multi-layered architecture with clear separation of concerns, modular component design, and consistent patterns across different modules.

The application is built using Streamlit, a Python framework for creating web applications, and follows a role-based access model with different interfaces for producers, operators, and administrators. It manages the procurement of agricultural products through a system of offers, approvals, and status transitions.

### Technology Stack

- **Frontend Framework**: Streamlit
- **Language**: Python 3.11
- **State Management**: Streamlit Session State
- **Data Visualization**: Plotly, Pandas
- **HTTP Client**: Requests
- **Deployment**: Docker

## 2. Directory Structure

```
streamlit_app/
├── .streamlit/              # Streamlit configuration
│   └── config.toml          # Theme and client settings
├── api/                     # API client modules
│   ├── auth.py              # Authentication API
│   ├── offers.py            # Offers API
│   ├── products.py          # Products API
│   └── users.py             # Users API
├── components/              # Reusable UI components
│   ├── auth_forms.py        # Authentication forms
│   ├── data_display.py      # Data visualization components
│   ├── multi_step_form.py   # Multi-step form framework
│   ├── notification.py      # Notification components
│   └── sidebar.py           # Application navigation
├── config/                  # Configuration modules
│   ├── __init__.py          # Configuration initialization
│   └── menu_config.py       # Menu structure configuration
├── pages/                   # Application pages
│   ├── admin/               # Admin pages
│   ├── auth/                # Authentication pages
│   ├── operator/            # Operator pages
│   ├── producer/            # Producer pages
│   └── auth_login.py        # Flat structure redirect files
├── utils/                   # Utility functions
│   ├── api_client.py        # API communication utilities
│   ├── formatting.py        # Data formatting utilities
│   ├── responsive_ui.py     # Responsive design utilities
│   ├── session.py           # Session state management
│   └── validators.py        # Form validation utilities
├── Dockerfile               # Docker configuration
├── api_client.py            # Generic API client
├── app_config.py            # Application configuration
├── config.py                # Legacy configuration (compatibility)
├── create_symlinks.sh       # Script for flat/nested structure
├── main.py                  # Application entry point
└── requirements.txt         # Python dependencies
```

## 3. Architectural Layers

The application follows a multi-layered architecture with clear separation of concerns:

### 3.1 Presentation Layer (Components & Pages)

The presentation layer is responsible for rendering the UI elements and handling user interactions. It consists of:

- **Pages**: Top-level modules representing complete views
- **Components**: Reusable UI elements that encapsulate specific functionality
- **UI Utilities**: Helper functions for UI rendering and management

This layer interacts with the business logic layer to process user inputs and display data.

### 3.2 Business Logic Layer (Page Functions & Handlers)

The business logic layer contains the application's core functionality and enforces business rules. It handles:

- **Data Validation**: Ensuring inputs meet required criteria
- **State Management**: Tracking and updating application state
- **Process Flow**: Managing the sequence of operations
- **Status Transitions**: Handling state changes in business objects

### 3.3 Data Access Layer (API Clients)

The data access layer manages communication with the backend API. It provides:

- **API Clients**: Modules for interacting with various API endpoints
- **Request/Response Handling**: Processing of HTTP requests and responses
- **Error Management**: Handling API errors and communication issues
- **Caching**: Storing frequently accessed data for performance

### 3.4 Infrastructure Layer (Utils & Config)

The infrastructure layer provides foundational services used across the application:

- **Configuration**: Application settings and constants
- **Session Management**: User authentication and session tracking
- **Responsive Design**: Adapting the UI to different device types
- **Formatting**: Standardized data formatting
- **Validation**: Reusable validation logic

## 4. Core Design Patterns

### 4.1 Multi-Page Application Pattern

The application follows Streamlit's multi-page architecture with two parallel structures:

```python
# Nested structure (recommended)
# pages/admin/dashboard.py
def show_admin_dashboard():
    st.title("Admin Dashboard")
    # Implementation
```

```python
# Flat structure (compatibility)
# pages/admin_dashboard.py
admin/dashboard.py  # Symbolic link to the actual implementation
```

This dual structure ensures compatibility with different Streamlit versions while maintaining clean code organization.

### 4.2 Component-Based UI Pattern

UI elements are encapsulated as reusable components with a consistent interface:

```python
def render_offer_table(offers, with_actions=False, pagination=True):
    """
    Renders a table of offers with customizable options.
    
    Args:
        offers (list): List of offer objects
        with_actions (bool): Whether to include action buttons
        pagination (bool): Whether to include pagination controls
        
    Returns:
        pd.DataFrame: The rendered table object
    """
    # Implementation
```

Components follow these principles:
- Self-contained with clear input parameters
- Consistent naming conventions (`render_*`, `display_*`, `show_*`)
- Predictable return values
- Clear documentation of parameters and return types

### 4.3 API Client Pattern

API communication follows a consistent pattern:

```python
def get_offers(params=None):
    """
    Fetches offers from the API.
    
    Args:
        params (dict, optional): Query parameters for filtering
        
    Returns:
        tuple: (success, result) where success is a boolean and result is
              either the offers data or an error message
    """
    try:
        response = make_api_request("GET", "/offers", params=params)
        
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            error_message = process_error_response(response)
            return False, error_message
    except Exception as e:
        return False, str(e)
```

Key aspects of this pattern:
- Consistent return tuple `(success, result)`
- Comprehensive error handling
- Clear documentation of parameters and return values
- Standardized request/response processing

### 4.4 Session State Management Pattern

The application uses Streamlit's session state for persistent data:

```python
# Initialize session state
if "key" not in st.session_state:
    st.session_state.key = default_value

# Read from session state
value = st.session_state.get("key", fallback_value)

# Update session state
st.session_state.key = new_value

# Delete from session state
if "key" in st.session_state:
    del st.session_state.key
```

Session state is used for:
- User authentication state
- Form data persistence
- Caching API responses
- Preserving UI state across reruns

### 4.5 Responsive Design Pattern

The application adapts to different device types (mobile, tablet, desktop):

```python
def create_responsive_layout():
    """Creates a responsive layout based on device type."""
    # Device detection
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Responsive layout
    if is_mobile:
        # Mobile-optimized single column layout
        col1 = st.columns(1)[0]
        with col1:
            # Mobile content
    elif is_tablet:
        # Tablet-optimized two-column layout
        col1, col2 = st.columns(2)
        with col1:
            # Left column content
        with col2:
            # Right column content
    else:
        # Desktop multi-column layout
        col1, col2, col3 = st.columns([2, 1, 1])
        # Content for each column
```

JavaScript injection is used to detect screen size and update session state accordingly.

## 5. Key Components and Modules

### 5.1 `app_config.py` - Configuration Management

The `app_config.py` module provides centralized configuration for the application:

```python
# API settings
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")

# Application settings
APP_NAME = os.getenv("APP_NAME", "Kertész Mester")
COMPANY_NAME = os.getenv("COMPANY_NAME", "Zöldség Világ Kft.")

# Offer statuses with visual styling
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    # Other statuses...
}
```

The configuration module provides:
- Environment variable integration
- Default values for settings
- Consistent access to configuration throughout the application
- Visual styling information for UI elements

### 5.2 `api_client.py` - API Communication

The `api_client.py` module provides a robust client for API communication:

```python
class APIClient:
    """API client class for handling HTTP requests."""
    
    def __init__(self, base_url):
        """Initialize the API client."""
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=API_CLIENT_CONFIG["retry_count"],
            backoff_factor=API_CLIENT_CONFIG["retry_backoff_factor"],
            status_forcelist=API_CLIENT_CONFIG["retry_status_forcelist"],
            allowed_methods=API_CLIENT_CONFIG["retry_allowed_methods"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _make_request(self, method, endpoint, data=None, params=None, headers=None):
        """Execute an HTTP request."""
        # Implementation
    
    def get(self, endpoint, params=None):
        """Execute a GET request."""
        return self._make_request("GET", endpoint, params=params)
    
    def post(self, endpoint, data):
        """Execute a POST request."""
        return self._make_request("POST", endpoint, data=data)
    
    # Other methods...
```

Key features of the API client:
- Retry mechanism for transient failures
- Consistent error handling
- Support for various HTTP methods
- Request/response logging
- Timeout handling

### 5.3 Session Management with `session.py`

The `session.py` module manages user authentication and session state:

```python
def is_authenticated():
    """
    Checks if the user is authenticated.
    
    Returns:
        bool: True if the user is authenticated, False otherwise
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if authentication flag is set
    auth_flag = st.session_state.get(auth_key, False)
    
    # If auth flag is set, make sure we also have a token
    if auth_flag and not st.session_state.get(token_key):
        logger.warning("Authentication flag set but no token found, clearing session")
        clear_session()
        return False
    
    # Check token expiry if available
    if auth_flag and st.session_state.get("token_expiry"):
        if time.time() > st.session_state.get("token_expiry"):
            logger.info("Token expired, clearing session")
            clear_session()
            return False
    
    # Update activity if authenticated
    if auth_flag:
        update_activity()
    
    return auth_flag
```

Key functions in the session module:
- `is_authenticated()`: Checks if the user is logged in
- `get_current_user()`: Retrieves the current user's profile
- `clear_session()`: Logs the user out
- `check_session_timeout()`: Enforces session timeout for security

### 5.4 Form Handling with `multi_step_form.py`

The `multi_step_form.py` component provides a framework for multi-step forms:

```python
def render_multi_step_form(steps, validators, form_id=None, on_save=None):
    """
    Renders a multi-step form with validation.
    
    Args:
        steps (list): List of step functions
        validators (list): List of validator functions
        form_id (str, optional): Form identifier. Defaults to None.
        on_save (function, optional): Save callback function. Defaults to None.
    
    Returns:
        tuple: (current_step, form_data)
    """
    # Initialize form data
    if "form_data" not in st.session_state:
        st.session_state.form_data = {}
    
    if "current_step" not in st.session_state:
        st.session_state.current_step = 0
    
    # Get current step and total steps
    current_step = st.session_state.current_step
    total_steps = len(steps)
    
    # Display progress indicator
    st.progress(current_step / (total_steps - 1))
    
    # Call current step function
    step_data = steps[current_step](st.session_state.form_data)
    
    # Update form data
    st.session_state.form_data.update(step_data)
    
    # Navigation buttons
    col1, col2 = st.columns(2)
    
    with col1:
        if current_step > 0:
            if st.button("Previous"):
                st.session_state.current_step -= 1
                st.rerun()
    
    with col2:
        if current_step < total_steps - 1:
            if st.button("Next"):
                # Validate current step
                is_valid, errors = validators[current_step](st.session_state.form_data)
                
                if is_valid:
                    st.session_state.current_step += 1
                    st.rerun()
                else:
                    for error in errors:
                        st.error(error)
        else:
            if st.button("Save"):
                # Validate final step
                is_valid, errors = validators[current_step](st.session_state.form_data)
                
                if is_valid:
                    if on_save:
                        on_save(st.session_state.form_data)
                else:
                    for error in errors:
                        st.error(error)
    
    return current_step, st.session_state.form_data
```

The multi-step form component:
- Manages the progression through form steps
- Handles validation at each step
- Preserves form data in session state
- Provides navigation controls
- Supports a final save callback

### 5.5 Navigation with `sidebar.py`

The `sidebar.py` component manages application navigation:

```python
def render_sidebar():
    """
    Renders the application sidebar with navigation.
    """
    with st.sidebar:
        st.title(config.APP_NAME)
        
        # Check authentication
        if is_authenticated():
            user = get_current_user()
            role = user.get("role", "").lower()
            
            # Display user info
            st.write(f"👤 {user.get('name', user.get('email', 'User'))}")
            st.write(f"Role: {role.capitalize()}")
            
            # Get menu for user role
            menu_items = ROLE_MENUS.get(role, [])
            
            # Display menu
            st.markdown("### Navigation")
            for item in menu_items:
                if st.sidebar.button(
                    f"{item['icon']} {item['label']}",
                    key=f"nav_{item['label']}",
                    use_container_width=True
                ):
                    st.switch_page(item['path'])
            
            # Logout button
            if st.sidebar.button("🔒 Logout", use_container_width=True):
                clear_session()
                st.rerun()
        else:
            # Display login options
            menu_items = PUBLIC_MENU
            
            for item in menu_items:
                if st.sidebar.button(
                    f"{item['icon']} {item['label']}",
                    key=f"nav_{item['label']}",
                    use_container_width=True
                ):
                    st.switch_page(item['path'])
```

The sidebar component:
- Displays navigation options based on user role
- Shows user information when logged in
- Provides logout functionality
- Handles page navigation
- Adapts to authentication state

## 6. Data Flow and Communication

### 6.1 Authentication Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Login Form   ├─────►│  API Client   ├─────►│  Backend API  │
│               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────┬───────┘
        │                                             │
        │                                             │
┌───────▼───────┐      ┌───────────────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  Session      │◄─────┤  Token +      │◄─────┤  Response     │
│  State Update │      │  User Data    │      │  Processing   │
│               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────────────┘
        │
        │
┌───────▼───────┐
│               │
│  Page         │
│  Redirection  │
│               │
└───────────────┘
```

Authentication Flow Steps:
1. User submits credentials via login form
2. API client sends authentication request to backend
3. Backend validates credentials and returns token + user data
4. Response is processed and validated
5. Session state is updated with token and user info
6. User is redirected to the appropriate dashboard

### 6.2 API Communication Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  UI Component │ ─┬──►│  API Module   ├─────►│  API Client   │
│               │  │   │               │      │               │
└───────────────┘  │   └───────────────┘      └───────┬───────┘
                   │                                  │
                   │                                  │
┌───────────────┐  │   ┌───────────────┐      ┌───────▼───────┐
│               │  │   │               │      │               │
│  Session      │  └──►│  Cache        │◄─────┤  Backend API  │
│  State        │      │  Check        │      │               │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

API Communication Steps:
1. UI component requests data
2. API module checks cache for recent data
3. If cached data is available and fresh, it's returned immediately
4. Otherwise, API client sends request to backend
5. Response is processed and potentially cached
6. Data is returned to the UI component
7. UI component updates with the data
8. Session state may be updated based on the response

### 6.3 Form Submission Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Form Input   ├─────►│  Validation   ├─────►│  API Module   │
│               │      │               │      │               │
└───────────────┘      └───────┬───────┘      └───────┬───────┘
                               │                      │
                               │                      │
┌───────────────┐      ┌───────▼───────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  Feedback     │◄─────┤  Error        │      │  API Client   │
│  Display      │      │  Handling     │      │               │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────┬───────┘
                                                     │
                                                     │
┌───────────────┐      ┌───────────────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  Success      │◄─────┤  Response     │◄─────┤  Backend API  │
│  Processing   │      │  Processing   │      │               │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

Form Submission Steps:
1. User fills out form and submits
2. Client-side validation checks the input data
3. If validation fails, errors are displayed
4. If validation passes, API module prepares the request
5. API client sends the data to the backend
6. Backend processes the request and returns response
7. Response is processed and feedback is displayed
8. On success, additional actions may be triggered (redirect, state update, etc.)

## 7. Common Design Patterns and Coding Conventions

### 7.1 Function Return Pattern

Most functions follow a consistent return pattern:

```python
def some_operation(params):
    """
    Function description.
    
    Args:
        params: Parameters description
        
    Returns:
        tuple: (success, result) where success is a boolean and result is the data or error message
    """
    try:
        # Operation logic
        return True, result_data
    except Exception as e:
        return False, str(e)
```

This pattern enables:
- Consistent error handling
- Easy result checking
- Standardized API across the application

### 7.2 Function Naming Conventions

The application uses consistent function naming conventions:

- `get_*`: Functions that retrieve data
- `set_*`: Functions that set data
- `is_*`: Functions that return a boolean
- `validate_*`: Functions that validate data
- `format_*`: Functions that format data
- `render_*`: Functions that render UI components
- `show_*`: Functions that display pages
- `handle_*`: Functions that handle events or actions
- `init_*`: Functions that initialize state
- `create_*`: Functions that create new data
- `update_*`: Functions that modify existing data
- `process_*`: Functions that transform data

### 7.3 Class Naming Conventions

Classes follow standard Python naming conventions:

- CamelCase for class names
- Classes represent distinct concepts
- Common class patterns include:
  - API clients (e.g., `APIClient`)
  - Data models
  - UI components (e.g., `ActionBar`)
  - Exception types (e.g., `APIError`)

### 7.4 Documentation Conventions

The codebase follows a consistent documentation style:

```python
def function_name(param1, param2=None):
    """
    Short description of the function.
    
    Longer description if needed, explaining the purpose and behavior.
    
    Args:
        param1 (type): Description of param1
        param2 (type, optional): Description of param2. Defaults to None.
        
    Returns:
        type: Description of the return value
        
    Raises:
        ExceptionType: Description of when the exception is raised
    """
    # Implementation
```

Documentation conventions include:
- Docstrings for all modules, classes, and functions
- Explicit parameter types and descriptions
- Return value types and descriptions
- Exception information when applicable
- Examples for complex functions

### 7.5 Error Handling Pattern

Error handling follows a consistent pattern:

```python
try:
    # Operation that may fail
    result = some_operation()
except SomeSpecificException as e:
    # Handle specific exception
    logger.error(f"Specific error: {str(e)}")
    return False, f"A specific error occurred: {user_friendly_message}"
except Exception as e:
    # Handle generic exception
    logger.error(f"Unexpected error: {str(e)}")
    return False, f"An unexpected error occurred: {user_friendly_message}"
```

This pattern ensures:
- Comprehensive error catching
- Detailed logging for debugging
- User-friendly error messages
- Consistent return format for error cases

## 8. Deployment and Environment

The application is containerized using Docker:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy all files
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV API_BASE_URL=http://backend:8000/api

# Fix script line endings and make executable
RUN apt-get update && apt-get install -y dos2unix && dos2unix create_symlinks.sh && chmod +x create_symlinks.sh

# Run the symlink script
WORKDIR /app
RUN bash create_symlinks.sh

EXPOSE 8501

# Start with custom sidebar, disabling automatic page listing
CMD ["streamlit", "run", "main.py", "--client.showSidebarNavigation=false"]
```

The Docker setup includes:
- Base Python image
- Dependency installation from requirements.txt
- Environment variable configuration
- Symlink creation for dual page structure
- Port exposure for web access
- Custom Streamlit startup command

## 9. Module Interactions and Dependencies

### 9.1 Module Dependencies

```
main.py
  ├── app_config
  ├── components.sidebar (render_sidebar)
  ├── pages.auth.login (show_login)
  ├── pages.producer.dashboard (show_producer_dashboard)
  ├── pages.operator.dashboard (show_operator_dashboard)
  ├── pages.admin.dashboard (show_admin_dashboard)
  └── utils.session (init_session_state, is_authenticated, get_current_user, clear_session)

components.sidebar
  ├── app_config (APP_NAME, COMPANY_NAME)
  ├── config.menu_config (ROLE_MENUS, PUBLIC_MENU)
  └── utils.session (is_authenticated, get_current_user, clear_session)

utils.session
  ├── app_config (SESSION_VARS)
  └── streamlit (st.session_state)

api clients
  ├── utils.api_client (APIClient, safe_api_call)
  ├── app_config (API_BASE_URL)
  └── utils.session (get_auth_token)

pages
  ├── components.*
  ├── utils.*
  ├── api.*
  └── app_config
```

### 9.2 Initialization Sequence

The application follows this initialization sequence:

1. **Configuration Loading**: Load settings from environment and config files
2. **Session Initialization**: Set up session state with default values
3. **Page Configuration**: Configure page title, layout, and icon
4. **Sidebar Rendering**: Display navigation based on authentication state
5. **Content Determination**: Decide what to show based on authentication and role
6. **Content Rendering**: Display the appropriate page content
7. **Event Handling**: Process user interactions and form submissions

## 10. Configuration and Customization

### 10.1 Application Configuration

The application can be configured through:

- Environment variables (e.g., `API_BASE_URL`, `APP_NAME`)
- `app_config.py` settings
- Streamlit's configuration system (`.streamlit/config.toml`)

```toml
# .streamlit/config.toml
[theme]
base = "dark"

[client]
showSidebarNavigation = false
```

### 10.2 Menu Configuration

The application's navigation is configured in `menu_config.py`:

```python
# Producer menu items
PRODUCER_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/producer_dashboard.py"
    },
    {
        "label": "Edit Profile",
        "icon": "👤",
        "path": "pages/producer_profile.py"
    },
    # Other menu items...
]

# Role-based menu assignment
ROLE_MENUS = {
    "termelő": PRODUCER_MENU,
    "ügyintéző": OPERATOR_MENU,
    "admin": ADMIN_MENU
}
```

This configuration allows for:
- Role-based navigation
- Consistent menu structure
- Easy modification of navigation options
- Clear visual indicators (icons)

## 11. Responsive Design Implementation

The application implements responsive design to adapt to different device types:

```python
def inject_screen_detection():
    """
    Inject JavaScript to detect screen size and update session state.
    """
    # JavaScript to detect screen size
    st.markdown("""
    <script>
        function detectDeviceType() {
            const width = window.innerWidth;
            const isMobile = width < 768;
            const isTablet = width >= 768 && width < 992;
            
            // Send info to Streamlit
            window.parent.postMessage({
                type: "streamlit:setComponentValue",
                value: {
                    is_mobile: isMobile,
                    is_tablet: isTablet,
                    screen_width: width
                }
            }, "*");
        }
        
        // Run on load and resize
        detectDeviceType();
        window.addEventListener('resize', detectDeviceType);
    </script>
    """, unsafe_allow_html=True)
```

Responsive design considerations:
- Device detection using JavaScript
- Adaptive layouts based on screen size
- Simplified interfaces on smaller screens
- Touch-friendly controls for mobile devices
- Consistent user experience across devices

## 12. Security Considerations

### 12.1 Authentication

The application implements secure authentication:

- JWT-based authentication
- Token storage in session state
- Session timeout mechanism
- Secure token handling
- Role-based access control

### 12.2 Input Validation

All user input is validated:

```python
def validate_required(value, field_name):
    """
    Validates that a required field is not empty.
    
    Args:
        value: The value to validate
        field_name: The name of the field for error messages
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not value:
        return False, f"{field_name} is required."
    return True, ""
```

Input validation includes:
- Required field validation
- Type checking
- Range validation
- Format validation (email, phone, etc.)
- Cross-field validation

### 12.3 API Error Handling

API errors are handled securely:

```python
def handle_api_error(error, operation_type):
    """
    Handles API errors with appropriate user feedback.
    
    Args:
        error: The error object or message
        operation_type: The type of operation that failed
    """
    error_message = str(error)
    
    # Log the error
    logger.error(f"API error during {operation_type}: {error_message}")
    
    # Handle authentication errors
    if "401" in error_message or "Unauthorized" in error_message:
        st.error("Your session has expired. Please log in again.")
        clear_session()
        st.rerun()
    
    # Handle other error types
    elif "404" in error_message:
        st.error(f"The requested resource was not found. ({operation_type})")
    elif "Connection" in error_message:
        st.error(f"A network error occurred. Please check your internet connection. ({operation_type})")
    else:
        st.error(f"An unexpected error occurred: {error_message} ({operation_type})")
```

This ensures:
- Secure handling of authentication failures
- Preventing information leakage
- Appropriate user feedback
- Logging for security monitoring

## 13. Summary

This agricultural product offer management application is built with Streamlit and follows a well-structured, multi-layered architecture. It implements:

1. **Clear separation of concerns**: Distinct layers for UI, business logic, data access, and infrastructure
2. **Consistent design patterns**: Standardized approaches to components, API communication, and state management
3. **Responsive design**: Adaptation to different device types for optimal user experience
4. **Modular organization**: Clean directory structure and code organization
5. **Secure authentication**: JWT-based authentication with session management
6. **Comprehensive error handling**: Consistent approach to error processing and feedback
7. **Configurable settings**: Flexible configuration through environment variables and config files
8. **Role-based access**: Different interfaces for producers, operators, and administrators

The application architecture enables maintainable, extensible development while providing a consistent user experience across different devices and user roles.
