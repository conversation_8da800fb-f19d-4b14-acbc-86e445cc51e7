# Main Configuration Architecture

## 1. Overview

This document provides a comprehensive overview of the application's architecture and configuration system. The application is a multi-layered system consisting of a FastAPI backend and a Streamlit frontend, designed for managing agricultural product offers between producers and a central company. This architecture document focuses on how the configuration system is organized and how it supports the entire application structure.

## 2. System Architecture

The application follows a client-server architecture with two main components:

```
┌───────────────────┐       ┌───────────────────┐
│                   │       │                   │
│  Streamlit        │◄─────►│  FastAPI          │
│  Frontend         │       │  Backend          │
│                   │       │                   │
└───────────────────┘       └───────────────────┘
                                      │
                                      ▼
                            ┌───────────────────┐
                            │                   │
                            │  PostgreSQL       │
                            │  Database         │
                            │                   │
                            └───────────────────┘
```

### 2.1 Technology Stack

- **Frontend**: 
  - Streamlit (Python-based web app framework)
  - Session State for state management
  - Custom components for UI elements

- **Backend**:
  - FastAPI framework
  - SQLAlchemy ORM
  - Pydantic for data validation
  - JWT authentication
  - Alembic for database migrations

- **Database**: 
  - PostgreSQL
  - Alembic migrations for schema management

- **Deployment**:
  - Docker containers
  - Docker Compose for service orchestration

## 3. Configuration System

The application uses a multi-layered configuration approach to manage settings across different environments and components.

### 3.1 Environment Variables

Environment variables serve as the primary source of configuration and are defined in the `docker-compose.yml` file:

```yaml
backend:
  environment:
    APP_NAME: Mezőgazdasági Termékkezelő Rendszer
    ENVIRONMENT: development
    BACKEND_HOST: 0.0.0.0
    BACKEND_PORT: 8000
    DATABASE_URL: **************************************/termelo_db
    DB_HOST: db
    DB_PORT: 5432
    DB_USER: postgres
    DB_PASSWORD: postgres
    DB_NAME: termelo_db
    SECRET_KEY: your-secret-key-here
    JWT_ALGORITHM: HS256
    ACCESS_TOKEN_EXPIRE_MINUTES: 30
    STREAMLIT_HOST: localhost
    STREAMLIT_PORT: 8501

streamlit:
  environment:
    API_HOST: http://backend:8000
    API_BASE_URL: http://backend:8000/api
    APP_NAME: Mezőgazdasági Termékkezelő Rendszer
    COMPANY_NAME: Zöldség Világ Kft.
    API_AUTH_TOKEN: "PASTE_IDE_AZ_ADMIN_TOKEN"
```

These variables are loaded by both the backend and frontend applications to configure their behavior.

### 3.2 Backend Configuration

#### 3.2.1 Core Configuration Module

The backend uses a settings management pattern with Pydantic:

```python
# app/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    APP_NAME: str = "Mezőgazdasági Termékkezelő Rendszer"
    ENVIRONMENT: str = "development"
    
    # Database settings
    DB_HOST: str = "db"
    DB_PORT: int = 5432
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "postgres"
    DB_NAME: str = "termelo_db"
    DATABASE_URL: str = "**************************************/termelo_db"
    
    # Authentication settings
    SECRET_KEY: str = "your-secret-key-here"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # API settings
    BACKEND_HOST: str = "0.0.0.0"
    BACKEND_PORT: int = 8000
    
    # CORS settings
    CORS_ORIGINS: list = ["*"]
    
    # Email settings
    EMAILS_ENABLED: bool = False
    SMTP_HOST: str = ""
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    EMAILS_FROM_EMAIL: str = "<EMAIL>"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

This pattern offers several advantages:
- Type validation of configuration values
- Default values for development
- Environment variable overrides
- Documentation of available settings

### 3.3 Frontend Configuration

#### 3.3.1 App Configuration Module

The Streamlit frontend uses a centralized configuration file:

```python
# streamlit_app/app_config.py
import os

# API settings
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")

# Application settings
APP_NAME = os.getenv("APP_NAME", "Kertész Mester")
COMPANY_NAME = os.getenv("COMPANY_NAME", "Zöldség Világ Kft.")

# Offer statuses with visual styling
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    "CONFIRMED_BY_COMPANY": {
        "name": "Visszaigazolva",
        "color": "#3584e4",
        "description": "Az ajánlat visszaigazolva a cég által."
    },
    "ACCEPTED_BY_USER": {
        "name": "Elfogadva",
        "color": "#33a02c",
        "description": "A termelő elfogadta az ajánlatot."
    },
    "REJECTED_BY_USER": {
        "name": "Elutasítva",
        "color": "#e31a1c",
        "description": "A termelő elutasította az ajánlatot."
    },
    "FINALIZED": {
        "name": "Teljesítve",
        "color": "#6a3d9a",
        "description": "Az ajánlat teljesítve."
    }
}

# Session management
SESSION_TIMEOUT = 1800  # 30 minutes in seconds
SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}

# Debug settings
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

# Mobile detection threshold
MOBILE_WIDTH_THRESHOLD = 768
TABLET_WIDTH_THRESHOLD = 992

# API client configuration
API_CLIENT_CONFIG = {
    "retry_count": 3,
    "retry_backoff_factor": 0.3,
    "retry_status_forcelist": [500, 502, 503, 504],
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "timeout": 10,  # seconds
    "cache_ttl": 300  # seconds
}

# Menu configuration (imported from config/menu_config.py)
```

This configuration approach:
- Centralizes all application settings
- Provides default values that can be overridden by environment variables
- Includes visual styling information
- Defines constant values used throughout the application

#### 3.3.2 Streamlit Configuration

The Streamlit frontend also uses Streamlit's built-in configuration system via `.streamlit/config.toml`:

```toml
[theme]
base = "dark"
primaryColor = "#33a02c"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F5"
textColor = "#262730"

[client]
showSidebarNavigation = false
toolbarMode = "minimal"

[server]
port = 8501
enableCORS = true
enableXsrfProtection = true
```

This configuration controls:
- Application theme and styling
- Server behavior
- UI component visibility
- Security settings

## 4. Configuration Flow and Inheritance

### 4.1 Configuration Hierarchy

The configuration system follows a hierarchical precedence:

1. **Environment Variables**: Highest priority, override all other settings
2. **Configuration Files**: Default values defined in code
3. **Hardcoded Defaults**: Used when no other value is provided

This allows for flexible configuration across different environments:

```
┌───────────────────┐
│                   │
│  Environment      │
│  Variables        │
│                   │
└─────────┬─────────┘
          │
          ▼
┌───────────────────┐
│                   │
│  Configuration    │
│  Files            │
│                   │
└─────────┬─────────┘
          │
          ▼
┌───────────────────┐
│                   │
│  Hardcoded        │
│  Defaults         │
│                   │
└───────────────────┘
```

### 4.2 Configuration Loading Sequence

The configuration loading sequence follows a specific pattern:

1. **Backend**:
   - Load environment variables from `.env` file (if exists)
   - Override with Docker environment variables
   - Initialize Settings object
   - Make settings available globally via dependency injection

2. **Frontend**:
   - Load Streamlit config from `.streamlit/config.toml`
   - Initialize app settings from `app_config.py`
   - Override with Docker environment variables
   - Make settings available to components

## 5. Key Configuration Components

### 5.1 Database Configuration

Database settings are primarily managed in the backend configuration:

```python
# Database configuration parameters
DB_HOST: str = "db"
DB_PORT: int = 5432
DB_USER: str = "postgres"
DB_PASSWORD: str = "postgres"
DB_NAME: str = "termelo_db"
DATABASE_URL: str = "**************************************/termelo_db"
```

These settings are used to create the SQLAlchemy engine and connect to the database:

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

### 5.2 Authentication Configuration

Authentication settings are defined in the backend configuration:

```python
# Authentication settings
SECRET_KEY: str = "your-secret-key-here"
JWT_ALGORITHM: str = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
```

These settings control:
- JWT token generation and validation
- Password hashing
- Token expiration
- Security requirements

### 5.3 API Configuration

API configuration is managed in both backend and frontend:

**Backend**:
```python
# API settings
BACKEND_HOST: str = "0.0.0.0"
BACKEND_PORT: int = 8000
```

**Frontend**:
```python
# API settings
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")
```

This configuration allows the frontend to connect to the backend API regardless of the deployment environment.

### 5.4 UI Configuration

The frontend includes extensive UI configuration:

```python
# Offer statuses with visual styling
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    # Other statuses...
}

# Streamlit theme configuration in .streamlit/config.toml
[theme]
base = "dark"
primaryColor = "#33a02c"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F5"
textColor = "#262730"
```

This configuration ensures consistent visual styling throughout the application.

## 6. Docker Configuration and Environment Management

The application is containerized using Docker with configuration defined in `docker-compose.yml`:

```yaml
version: '3.8'
services:
  db:
    image: postgres:15
    container_name: termelo-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: termelo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - termelo-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend
    ports:
      - "8000:8000"
    environment:
      # Environment variables...
    volumes:
      - ./app:/app/app
      - ./migrations:/app/migrations
    depends_on:
      db:
        condition: service_healthy
    networks:
      - termelo-network
    command: >
      sh -c "chmod +x /app/init-db.sh && /app/init-db.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"

  streamlit:
    build:
      context: ./streamlit_app
      dockerfile: Dockerfile
    container_name: streamlit
    ports:
      - "8501:8501"
    environment:
      # Environment variables...
    volumes:
      - ./streamlit_app:/app
      - ./streamlit_app/.streamlit:/app/.streamlit
    working_dir: /app
    command: streamlit run main.py
    depends_on:
      - backend
    networks:
      - termelo-network
```

This configuration:
- Defines the container services: database, backend, and frontend
- Sets environment variables for each service
- Configures networking between services
- Mounts volumes for code and data persistence
- Defines startup commands
- Manages service dependencies

## 7. Database Migration Configuration

Database migrations are managed using Alembic with configuration in `alembic.ini`:

```ini
# A generic, single database configuration.

[alembic]
# path to migration scripts
script_location = migrations

# Adatbázis URL értékeit a .env fájlból olvassa a migrations/env.py
sqlalchemy.url = **************************************/termelo_db

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic
```

The migration environment is configured in `migrations/env.py`:

```python
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# Importáljuk a Base osztályt és a modelleket
from app.db.base import Base
from app.models import *  # Importáljuk az összes modellt

config = context.config

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata
```

This configuration enables automatic migration generation based on model changes.

## 8. Configuration Usage Patterns

### 8.1 Backend Configuration Access Pattern

The backend uses a dependency injection pattern to access configuration:

```python
# app/api/dependencies.py
from fastapi import Depends

from app.core.config import settings

def get_settings():
    return settings

# Usage in endpoint
@router.get("/info")
def get_api_info(settings = Depends(get_settings)):
    return {
        "app_name": settings.APP_NAME,
        "environment": settings.ENVIRONMENT,
        "version": "1.0.0"
    }
```

This pattern provides:
- Testability through dependency injection
- Consistent access to configuration
- Single source of truth for settings

### 8.2 Frontend Configuration Access Pattern

The frontend uses direct imports for configuration access:

```python
# Direct import
import app_config as config

# Usage
def render_sidebar():
    st.title(config.APP_NAME)
    
    # Use configuration values
    status_color = config.OFFER_STATUSES.get(status, {}).get("color", "#cccccc")
```

Some modules also implement a fallback pattern to handle different import scenarios:

```python
try:
    # Docker environment import
    from pages.utils.session import get_auth_token
    import app_config as config
except ImportError:
    try:
        # Direct import (development environment)
        from utils.session import get_auth_token
        import app_config as config
    except ImportError:
        # Fallback configuration
        config = {
            "API_BASE_URL": "http://localhost:8000/api",
            "APP_NAME": "Dev Environment"
        }
        
        # Fallback function
        def get_auth_token():
            """Fallback auth token getter"""
            return None
```

This pattern ensures the application can run in different environments with appropriate configuration.

## 9. Session Configuration

The application implements session management with configuration:

```python
# Session management configuration
SESSION_TIMEOUT = 1800  # 30 minutes in seconds
SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}
```

This configuration is used in the session management utilities:

```python
def is_authenticated():
    """
    Checks if the user is authenticated.
    
    Returns:
        bool: True if authenticated, False otherwise
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if authentication flag is set
    auth_flag = st.session_state.get(auth_key, False)
    
    # If auth flag is set, make sure we also have a token
    if auth_flag and not st.session_state.get(token_key):
        clear_session()
        return False
    
    # Check token expiry
    if auth_flag and st.session_state.get("token_expiry"):
        if time.time() > st.session_state.get("token_expiry"):
            clear_session()
            return False
    
    # Check session timeout
    check_session_timeout()
    
    return auth_flag
```

## 10. API Client Configuration

The frontend includes configuration for the API client:

```python
# API client configuration
API_CLIENT_CONFIG = {
    "retry_count": 3,
    "retry_backoff_factor": 0.3,
    "retry_status_forcelist": [500, 502, 503, 504],
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "timeout": 10,  # seconds
    "cache_ttl": 300  # seconds
}
```

This configuration is used in the API client implementation:

```python
class APIClient:
    """API client for communicating with the backend API."""
    
    def __init__(self, base_url):
        """Initialize the API client."""
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Configure retry strategy from configuration
        retry_strategy = Retry(
            total=config.API_CLIENT_CONFIG["retry_count"],
            backoff_factor=config.API_CLIENT_CONFIG["retry_backoff_factor"],
            status_forcelist=config.API_CLIENT_CONFIG["retry_status_forcelist"],
            allowed_methods=config.API_CLIENT_CONFIG["retry_allowed_methods"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
```

## 11. Role-Based Configuration

The application includes role-based configuration for UI elements and access control:

```python
# Menu configuration for different roles
PRODUCER_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/producer_dashboard.py"
    },
    # Other menu items...
]

OPERATOR_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/operator_dashboard.py"
    },
    # Other menu items...
]

ADMIN_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/admin_dashboard.py"
    },
    # Other menu items...
]

ROLE_MENUS = {
    "termelő": PRODUCER_MENU,
    "ügyintéző": OPERATOR_MENU,
    "admin": ADMIN_MENU
}
```

This configuration enables role-based navigation and access control:

```python
def render_sidebar():
    """Render the sidebar for navigation."""
    with st.sidebar:
        st.title(config.APP_NAME)
        
        # Check authentication
        if is_authenticated():
            user = get_current_user()
            role = user.get("role", "").lower()
            
            # Get menu for user role
            menu_items = config.ROLE_MENUS.get(role, [])
            
            # Display menu
            st.markdown("### Navigation")
            for item in menu_items:
                if st.sidebar.button(
                    f"{item['icon']} {item['label']}",
                    key=f"nav_{item['label']}",
                    use_container_width=True
                ):
                    st.switch_page(item['path'])
```

## 12. Conclusion

The configuration system of this application is designed to be flexible, maintainable, and environment-aware. It follows several key principles:

1. **Centralization**: Configuration is centralized in dedicated modules
2. **Hierarchy**: Clear precedence of configuration sources
3. **Environment Awareness**: Configuration adapts to different environments
4. **Type Safety**: Validation of configuration values through Pydantic
5. **Defaults**: Sensible defaults for all configuration values
6. **Documentation**: Well-documented configuration options

This approach ensures the application can be easily deployed and maintained across different environments while providing a consistent experience for users and developers.

## 13. Best Practices for Configuration Management

When extending or modifying the application, follow these best practices:

1. **Add new configuration options to the appropriate module**:
   - Backend settings to `app/core/config.py`
   - Frontend settings to `streamlit_app/app_config.py`

2. **Use environment variables for sensitive or environment-specific values**:
   - Add the variable to `docker-compose.yml`
   - Provide a default value in the configuration module

3. **Follow the established naming conventions**:
   - UPPER_CASE for constant configuration values
   - CamelCase for configuration classes
   - snake_case for configuration functions

4. **Document all configuration options**:
   - Include descriptions for Pydantic fields
   - Add comments explaining the purpose and possible values

5. **Use the dependency injection pattern for backend configuration access**:
   - Use `Depends(get_settings)` in FastAPI endpoints
   - Avoid direct imports of settings in business logic

6. **Implement fallback patterns for frontend configuration**:
   - Handle import errors gracefully
   - Provide sensible defaults for all settings

By following these practices, you'll maintain the robustness and flexibility of the configuration system while ensuring consistent behavior across the application.
