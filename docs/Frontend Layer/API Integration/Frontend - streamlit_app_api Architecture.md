# Frontend - streamlit_app/api Architecture Documentation

## 1. Overview

This application is a Streamlit-based web frontend that interfaces with a REST API backend. The system appears to be designed for managing agricultural product offers between producers and a central company, with functionality for user management, product management, offer processing, and reporting.

## 2. System Architecture

### 2.1 High-Level Architecture

The application follows a client-server architecture with clear separation of concerns:

```
┌───────────────────┐       ┌───────────────────┐
│                   │       │                   │
│  Streamlit        │       │  Backend          │
│  Frontend         │◄─────►│  REST API         │
│                   │       │                   │
└───────────────────┘       └───────────────────┘
```

### 2.2 Frontend Architecture

The frontend is built with Streamlit and organized in a modular structure:

```
streamlit_app/
├── api/                  # API client layer
│   ├── auth.py           # Authentication API calls
│   ├── imports.py        # Common imports and utilities
│   ├── notifications.py  # Notifications API
│   ├── offers.py         # Offer management API
│   ├── products.py       # Product management API
│   ├── reports.py        # Reporting API
│   └── users.py          # User management API
├── utils/
│   └── session.py        # Session management utilities
├── pages/                # Streamlit pages
└── app_config.py         # Application configuration
```

## 3. Key Components & Design Patterns

### 3.1 API Client Layer

The API client layer in the `api/` directory follows a consistent pattern across all modules:

#### Pattern: Uniform Response Format

All API functions return a tuple of `(success, result)`, where:
- `success` is a boolean indicating if the API call was successful
- `result` is either the successful response data or an error message

**Example:**
```python
def get_users(params=None):
    """
    Felhasználók listájának lekérdezése (admin/ügyintéző számára).
    
    Args:
        params (dict, optional): Keresési paraméterek (szűrés, lapozás, stb.)
        
    Returns:
        tuple: (bool, list/str) - Sikeres lekérdezés (True/False) és felhasználók listája vagy hibaüzenet
    """
    return _make_api_request("GET", "/users", params=params)
```

#### Pattern: Centralized API Request Handling

A private helper function `_make_api_request` is used in many modules to standardize API calls, error handling, and authentication:

```python
def _make_api_request(method, endpoint, data=None, json=None, params=None):
    """
    Általános API kérés küldése a felhasználókezelési végpontokra.
    
    Args:
        method (str): HTTP metódus (GET, POST, PUT, DELETE)
        endpoint (str): API végpont relatív elérési útja
        data (dict, optional): Form adatok
        json (dict, optional): JSON adatok
        params (dict, optional): Query paraméterek
        
    Returns:
        tuple: (bool, Any) - Sikeres kérés (True/False) és válasz vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    # ...HTTP request handling...
```

#### Pattern: Flexible Import System

The codebase implements a robust import system that works across different environments (local development and Docker):

```python
try:
    # Docker környezetben ez a helyes import
    from pages.utils.session import get_auth_token
except ImportError:
    try:
        # Közvetlen import (fejlesztői környezetben)
        from utils.session import get_auth_token
    except ImportError:
        # Fallback funkció
        def get_auth_token():
            """Fallback auth token getter"""
            return None
```

### 3.2 Authentication System

The authentication system is JWT-based and manages user sessions through Streamlit's session state:

```python
def login(email, password):
    # ...authentication logic...
    
    if response.status_code == 200:
        data = response.json()
        token = data.get("access_token")
        
        # Set session state
        st.session_state[session_vars.get("token", "auth_token")] = token
        st.session_state[session_vars.get("user", "user")] = user_data
        st.session_state[session_vars.get("authenticated", "authenticated")] = True
```

### 3.3 Error Handling Pattern

The application implements consistent error handling with detailed error messages and status code mapping:

```python
if response.status_code == 401:
    return False, "Nincs megfelelő jogosultsága"
elif response.status_code == 404:
    return False, f"Nem található ajánlat ezzel az azonosítóval: {offer_id}"
else:
    try:
        error_data = response.json()
        error_message = error_data.get("detail", "Ajánlat lekérdezési hiba")
    except Exception as e:
        error_message = f"Ajánlat lekérdezési hiba (HTTP {response.status_code})"
    
    return False, error_message
```

## 4. Data Flow

### 4.1 Authentication Flow

```
┌──────────┐      ┌───────────┐      ┌─────────┐      ┌─────────────┐
│          │      │           │      │         │      │             │
│ UI Form  ├─────►│ auth.py   ├─────►│ API     ├─────►│ Session     │
│          │      │ login()   │      │ /login  │      │ Management  │
│          │      │           │      │         │      │             │
└──────────┘      └───────────┘      └─────────┘      └─────────────┘
```

### 4.2 API Request Flow

```
┌──────────┐      ┌───────────────┐      ┌─────────────┐      ┌─────────┐
│          │      │               │      │             │      │         │
│ UI       ├─────►│ API Module    ├─────►│ Auth Token  ├─────►│ Backend │
│ Component│      │ (e.g.,offers) │      │ Injection   │      │ API     │
│          │      │               │      │             │      │         │
└──────────┘      └───────────────┘      └─────────────┘      └─────────┘
```

## 5. Component Details

### 5.1 Authentication API (`auth.py`)

This module handles user authentication, registration, and password management:

- `login(email, password)`: Authenticates user and stores session data
- `register(user_data)`: Registers new users
- `request_password_reset(email)`: Initiates password reset
- `reset_password(token, new_password)`: Completes password reset
- `verify_email(token)`: Verifies user email address

### 5.2 User Management API (`users.py`)

Handles user profile operations and admin-level user management:

- `get_current_user()`: Retrieves current user profile
- `update_current_user(user_data)`: Updates user profile
- `change_password(password_data)`: Changes user password
- `get_users(params)`: Admin function to list all users
- `update_user(user_id, user_data)`: Admin function to update user
- `set_user_role(user_id, role)`: Changes user role
- `activate_user(user_id)` / `deactivate_user(user_id)`: Controls user status

### 5.3 Offer Management API (`offers.py`)

Core business logic for managing product offers:

- `get_offers(params)`: Lists offers with filtering
- `get_offer(offer_id)`: Gets a specific offer
- `create_offer(offer_data)`: Creates new offers
- `update_offer(offer_id, offer_data)`: Modifies existing offers
- `confirm_offer(offer_id, confirmation_data)`: Company confirms offer
- `accept_offer(offer_id)` / `reject_offer(offer_id)`: Producer response
- `finalize_offer(offer_id)`: Completes accepted offer

### 5.4 Product Management API (`products.py`)

Manages product catalog information:

- `get_product_categories(params)`: Lists product categories
- `get_product_types(category_id, params)`: Lists product types
- `get_quality_grades(product_type_id, params)`: Lists quality grades
- CRUD operations for categories, types, and grades

### 5.5 Notifications API (`notifications.py`)

Manages system notifications:

- `get_notifications(include_read, skip, limit)`: Retrieves user notifications
- `mark_notification_read(notification_id)`: Marks notification as read
- `create_notification(message, type, ...)`: Creates new notifications

### 5.6 Reports API (`reports.py`)

Generates system reports:

- `get_stats(report_type, from_date, to_date)`: Retrieves statistical data

## 6. Data Models and Relationships

### 6.1 Key Data Entities

#### User
- Attributes: id, email, password, role, company_name, contact_name, is_active
- Roles: producer (termelő), operator (ügyintéző), admin

#### Product Category
- Attributes: id, name, description

#### Product Type
- Attributes: id, name, description, category_id
- Relationships: belongs to Category

#### Quality Grade
- Attributes: id, name, description, product_type_id
- Relationships: belongs to Product Type

#### Offer
- Attributes: id, user_id, product_type_id, quality_grade_id, quantity_in_kg, price_per_kg, delivery_date, status
- Status flow: CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
- Relationships: belongs to User, Product Type, Quality Grade

#### Notification
- Attributes: id, user_id, message, type, read, created_at
- Types: info, success, warning, error, update

## 7. Session Management

The application uses Streamlit's session state for managing user sessions:

- `auth_token`: Stores the JWT token
- `user`: Stores user profile data
- `authenticated`: Boolean flag for auth status

The `session.py` module provides utilities:
- `get_auth_token()`: Retrieves current token
- `clear_session()`: Logs out user
- `set_user_session(user_data, token)`: Sets up user session
- `get_current_user()`: Gets user profile from session
- `is_authenticated()`: Checks authentication status

## 8. Error Handling Strategy

The application implements a multi-layered error handling approach:

1. **Network Errors**: Handled using try/except for requests exceptions
2. **API Errors**: Parsed from response status and JSON error details
3. **Authentication Errors**: 401 responses clear the session
4. **Validation Errors**: Formatted for display using `format_validation_error`
5. **Fallback Mechanisms**: Graceful degradation with fallback functions

## 9. Environment Adaptability

The codebase is designed to work in multiple environments:

1. **Local Development**: Direct imports
2. **Docker Environment**: Imports through the pages module
3. **Fallback Implementations**: Default values and functions if imports fail

This flexibility is implemented through cascading try/except blocks in imports and configuration loading.

## 10. Debugging and Logging

The application includes comprehensive debugging and logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ...

logger.info(f"Successfully imported session utils from Docker path")
logger.error(f"Error parsing response: {str(e)}")
```

Some modules implement detailed debug logging to files:

```python
log_data = {
    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
    "function": "update_user_default_settings",
    "input_data": settings_data,
    # ...
}

# ...

log_file = os.path.join(log_dir, "api_debug.log")
with open(log_file, "a", encoding="utf-8") as f:
    f.write(json.dumps(log_data, indent=2, ensure_ascii=False))
```

## 11. Configuration Management

The application configuration is managed through an `app_config.py` module:

- `API_BASE_URL`: Backend API endpoint
- `APP_NAME`: Application name
- `DEBUG`: Debug mode flag
- `AUTH_COOKIE_NAME`: Cookie name for auth
- `SESSION_TIMEOUT`: Session timeout in seconds
- `SESSION_VARS`: Session variable mapping

## 12. Naming Conventions

### 12.1 Function Names

- API client functions: verb_noun format (e.g., `get_users`, `update_offer`)
- Helper functions: underscore prefix (e.g., `_make_api_request`)

### 12.2 Variable Names

- API response tuples: `success, result` or `success, error_message`
- Configuration constants: UPPER_CASE (e.g., `API_BASE_URL`)
- Local variables: snake_case (e.g., `user_data`, `error_message`)

### 12.3 File Organization

- API modules grouped by domain (auth, users, offers, products)
- Utility functions in separate modules (session.py, imports.py)

## 13. API Response Processing

The application implements consistent data transformation between the API and UI:

```python
# Example: Enhancing offer data with display names
if isinstance(offers, dict):
    if "id" in offers:
        user = offers.get("user")
        offers["user_name"] = user.get("company_name") if user else ""
        product_type = offers.get("product_type")
        offers["product_name"] = product_type.get("name") if product_type else ""
        
        # Status mapping for display
        status_mapping = {
            "CREATED": "Létrehozva",
            "CONFIRMED_BY_COMPANY": "Megerősítve",
            "ACCEPTED_BY_USER": "Elfogadva",
            "REJECTED_BY_USER": "Elutasítva",
            "FINALIZED": "Teljesítve",
            "MODIFIED": "Módosítva"
        }
        
        if "status" in offers:
            current_status = offers["status"]
            offers["status_display"] = status_mapping[current_status]
```

## 14. Multi-Environment Import System

The application implements a sophisticated import system that works across different environments:

```python
try:
    # Központi import modul használata
    from api.imports import (
        get_auth_token, 
        clear_session, 
        set_user_session,
        config
    )
    logger.info("Successfully imported from common API imports in users.py")
except ImportError:
    try:
        # Docker környezetben ez a helyes import
        from pages.utils.session import get_auth_token, clear_session, set_user_session
        import app_config as config
        logger.info("Successfully imported session utils from Docker path in users.py")
    except ImportError:
        # Additional fallback paths...
```

This pattern ensures the application can run in different deployment contexts without code changes.

## 15. Code Evolution and Bugfixes

The codebase shows evidence of iterative improvement through specific bugfix modules that address issues in the original implementation:

### 15.1 Offers API Bugfixes

Two improved versions of the offer management API exist alongside the original implementation:

#### `offers_fix_minimal.py` 
Contains a minimally invasive fix for the `get_offer` function with these improvements:
- More robust ID type handling to prevent conversion errors
- Enhanced error handling with clearer error messages
- Improved logging for better debugging
- More consistent response processing

```python
def get_offer(offer_id):
    """
    Egy ajánlat lekérdezése API hívás - JAVÍTOTT VERZIÓ
    
    Args:
        offer_id (int/str): Ajánlat azonosítója (rugalmas típuskezeléssel)
        
    Returns:
        tuple: (bool, dict/str) - Sikeres lekérdezés (True/False) és ajánlat adatai vagy hibaüzenet
    """
    try:
        # Egyszerűsített ID konverzió - csak az alapvető típusellenőrzéssel
        original_id = offer_id  # Eredeti érték megőrzése a logoláshoz
        
        if isinstance(offer_id, str):
            try:
                offer_id = int(offer_id.strip())
            except (ValueError, TypeError):
                # Hagyni az eredeti formában, ha nem konvertálható
                # és hagyni az API-t, hogy kezelje a hibát
                pass
        
        # Debug információk
        logger.info(f"GET_OFFER API CALL: Original ID: {original_id}, Converted ID: {offer_id}, Type: {type(offer_id)}")
        
        # ...rest of implementation...
```

#### `offers_fix.py` 
A more comprehensive fix that includes:
- All the improvements from the minimal fix
- New `get_cached_offer()` function to reduce API calls through caching
- `normalize_id()` helper for consistent ID formatting
- `handle_api_error()` for unified error message generation
- More robust status code handling

Example of new caching functionality:
```python
def get_cached_offer(offer_id, max_age_seconds=300):
    """
    Ajánlat lekérése cache-ből, frissítéssel ha túl régi vagy hiányzó.
    
    Args:
        offer_id (int): Ajánlat azonosító
        max_age_seconds (int): Maximum cache élettartam másodpercben
        
    Returns:
        tuple: (success, offer_data/error_message)
    """
    current_time = time.time()
    
    # Inicializáljuk a cache-t, ha még nem létezik
    if "offer_cache" not in st.session_state:
        st.session_state.offer_cache = {}
    
    cache_key = str(offer_id)  # Bizonyosodjunk meg, hogy string kulcs legyen
    
    # Cache ellenőrzése
    if (cache_key in st.session_state.offer_cache and 
        "timestamp" in st.session_state.offer_cache[cache_key] and
        current_time - st.session_state.offer_cache[cache_key]["timestamp"] < max_age_seconds):
        # A cache elég friss
        logger.info(f"Using cached offer data for {offer_id}")
        return True, st.session_state.offer_cache[cache_key]["data"]
    
    # Cache frissítése API-n keresztül
    logger.info(f"Fetching fresh offer data for {offer_id}")
    success, result = get_offer(offer_id)
    
    if success:
        # Cache frissítése
        st.session_state.offer_cache[cache_key] = {
            "data": result,
            "timestamp": current_time
        }
        return True, result
    
    # Hiba esetén töröljük a cache-t, ha létezik
    if cache_key in st.session_state.offer_cache:
        del st.session_state.offer_cache[cache_key]
    
    return False, result
```

### 15.2 Products API Bugfixes

The `products_fixed.py` file contains an improved version of the product management API with:
- Corrected API endpoint usage (e.g., using `/products/grades` instead of `/products/quality-grades`)
- More consistent status code handling, especially for 204 (No Content) responses
- More generic error messages that are easier to maintain
- Better handling of non-JSON responses
- Improved type checking and validation

Example of improved endpoint handling and status code processing:
```python
def delete_quality_grade(grade_id):
    """
    Minőségi besorolás törlése API hívás (csak admin számára)
    
    Args:
        grade_id (int): Besorolás azonosítója
        
    Returns:
        tuple: (bool, str) - Sikeres törlés (True/False) és visszaigazoló vagy hibaüzenet
    """
    token = get_auth_token()
    if not token:
        return False, "Nincs bejelentkezve"
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.delete(
            f"{config.API_BASE_URL}/products/grades/{grade_id}",
            headers=headers
        )
        
        if response.status_code == 204:
            return True, "A minőségi besorolás sikeresen törölve"
        
        try:
            error_data = response.json()
            error_message = error_data.get("detail", "Minőségi besorolás törlési hiba")
        except:
            error_message = f"Törlési hiba: {response.status_code}"
        
        # More specific status code handling
        if response.status_code == 401:
            return False, "Nincs megfelelő jogosultsága"
        
        if response.status_code == 404:
            return False, "A megadott besorolás nem található"
        
        if response.status_code == 400:
            return False, "A besorolás nem törölhető, mert vannak hozzá kapcsolódó elemek"
        
        return False, error_message
    
    # Exception handling...
```

### 15.3 Bugfix Strategy

The presence of these alternative implementations reveals a development strategy that:

1. **Preserves backwards compatibility**: New implementations exist alongside the original ones
2. **Allows isolated testing**: Fixes can be tested independently before integration
3. **Enables progressive adoption**: Teams can migrate to new implementations gradually
4. **Documents the evolution**: The code history is preserved for future reference
5. **Facilitates code review**: Separate files make changes easier to review

This approach is particularly valuable in production environments where immediate replacement of core API modules could introduce risks.

## 16. Recommendations for LLM Context

When providing code snippets from this application to an LLM, consider:

1. Include the relevant API client module to establish context
2. Provide the corresponding utility functions that the API client depends on
3. Include any relevant data structures or type definitions
4. Specify the environment context (local development or Docker)
5. Include error handling patterns for similar operations

Following this approach will help the LLM understand the overall architecture and generate appropriate code that fits the established patterns.
