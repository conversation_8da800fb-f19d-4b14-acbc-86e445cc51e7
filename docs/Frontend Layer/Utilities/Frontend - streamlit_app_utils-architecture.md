# Frontend - streamlit_app_UTILS Architecture

## 1. Overview

This document outlines the architecture and organization of the utility modules (`utils`) within a Streamlit-based web application for agricultural product offer management. The utils package serves as the foundation layer providing common functionality across the application, ensuring consistency, reusability, and separation of concerns.

The application follows a multi-layered architecture with the utils components serving as the infrastructure layer supporting higher-level components including the API clients, UI components, business logic, and presentation layers.

## 2. Directory Structure

```
streamlit_app/utils/
├── __init__.py             # Package initialization
├── api_client.py           # API communication utilities
├── auth_utils.py           # Authentication utilities  
├── config.py               # Configuration utilities
├── formatting.py           # Data formatting utilities
├── navigation.py           # Navigation utilities
├── page_utils.py           # Page setup utilities
├── responsive_ui.py        # Responsive design utilities
├── session.py              # Session state management
├── storage.py              # Local storage utilities
├── user_utils.py           # User-related utilities
└── validators.py           # Data validation utilities
```

## 3. Core Utility Modules

### 3.1 API Client (`api_client.py`)

The API client module provides a standardized interface for communicating with backend APIs, handling authentication, error handling, and response processing.

#### Key Components:

- **`APIClient` Class**: Encapsulates HTTP communication with the backend
- **Caching Mechanism**: Improves performance by caching API responses
- **Error Handling**: Standardized error processing and reporting
- **Retry Logic**: Automatic retries for transient failures

#### Design Patterns:

- **Decorator Pattern**: Uses `@cacheable` decorator for API response caching
- **Adapter Pattern**: Provides a consistent interface over the HTTP API
- **Factory Pattern**: Creates appropriate request configurations

#### Example Usage:

```python
def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """
    Biztonságos API hívás végrehajtása egységes hibakezeléssel.
    
    Args:
        api_function (callable): Az API funkció, amelyet meg kell hívni.
        error_operation_name (str): A művelet neve hibaüzenetekhez.
        *args: További pozíciós argumentumok az API funkcióhoz.
        **kwargs: További kulcsszó argumentumok az API funkcióhoz.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az API hívás eredményét vagy a hibaüzenetet tartalmazza.
    """
    try:
        logger.info(f"Calling API function: {api_function.__name__} with args: {args}, kwargs: {kwargs}")
        success, result = api_function(*args, **kwargs)
        
        if not success:
            logger.error(f"API returned error for {error_operation_name}: {result}")
            handle_api_error(result, error_operation_name)
        
        return success, result
    except Exception as e:
        logger.error(f"Exception in {error_operation_name}: {str(e)}")
        handle_api_error(e, error_operation_name)
        return False, str(e)
```

### 3.2 Session Management (`session.py`)

The session module manages the Streamlit session state, handling user authentication, maintaining application state, and managing timeouts.

#### Key Components:

- **Session Initialization**: Sets up required session variables
- **Authentication State**: Manages user login state
- **Token Management**: Handles JWT token storage and expiration
- **Activity Tracking**: Manages session timeouts

#### Design Patterns:

- **Singleton Pattern**: Ensures a single source of truth for session state
- **Observer Pattern**: Updates activity timestamps on state changes
- **Factory Pattern**: Creates session state with default values

#### Example Usage:

```python
def is_authenticated():
    """
    Ellenőrzi, hogy a felhasználó be van-e jelentkezve.
    
    Returns:
        bool: True, ha a felhasználó be van jelentkezve, egyébként False
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if authentication flag is set
    auth_flag = st.session_state.get(auth_key, False)
    
    # If auth flag is set, make sure we also have a token
    if auth_flag and not st.session_state.get(token_key):
        logger.warning("Authentication flag set but no token found, clearing session")
        clear_session()
        return False
    
    # Check token expiry if available
    if auth_flag and st.session_state.get(SESSION_TOKEN_EXPIRY_KEY):
        if time.time() > st.session_state.get(SESSION_TOKEN_EXPIRY_KEY):
            logger.info("Token expired, clearing session")
            clear_session()
            return False
    
    # Check session timeout
    check_session_timeout()
    
    # Update activity if authenticated
    if auth_flag:
        update_activity()
    
    return auth_flag
```

### 3.3 Responsive UI (`responsive_ui.py`)

The responsive UI module provides utilities for creating adaptive user interfaces that work across different devices (desktop, tablet, mobile).

#### Key Components:

- **Device Detection**: Identifies device type using JavaScript
- **Responsive Helpers**: Utilities for responsive layouts
- **UI Components**: Device-aware UI components
- **Theme Management**: Handles different color themes

#### Design Patterns:

- **Strategy Pattern**: Selects rendering approaches based on device type
- **Factory Pattern**: Creates appropriate UI components for the device
- **Builder Pattern**: Constructs complex UI elements with responsive behavior

#### Example Usage:

```python
def create_responsive_columns(ratios=None, mobile_stack=True):
    """
    Reszponzív oszlopok létrehozása.
    
    Args:
        ratios (list, optional): Oszlop arányok. Defaults to None.
        mobile_stack (bool, optional): Mobilon egymás alá rendezi. Defaults to True.
    
    Returns:
        list: Streamlit oszlopok listája
    """
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Ha mobilnézet és mobile_stack=True, akkor egyenlő 1-es oszlopok
    if is_mobile and mobile_stack:
        return [st.column(1) for _ in (ratios or [1])]
    
    # Tabletnézet esetén egyszerűsített oszlopok, ha sok oszlop van
    if is_tablet and ratios and len(ratios) > 3:
        # Tablet eszközön maximum 3 oszlopot engedünk
        simplified_ratios = ratios[:3]
        # Az arányokat normalizáljuk, hogy 1 legyen az összegük
        total = sum(simplified_ratios)
        normalized_ratios = [r/total for r in simplified_ratios]
        return st.columns(normalized_ratios)
    
    # Egyébként az adott arányokkal, vagy alapértelmezettekkel
    return st.columns(ratios or [1, 1])
```

### 3.4 Validation (`validators.py`)

The validation module provides utilities for validating user input, ensuring data integrity and improving user experience.

#### Key Components:

- **Form Validation**: Validates form input fields
- **Data Type Validation**: Ensures data has the correct type
- **Format Validation**: Checks for specific formats (email, phone, etc.)
- **Range Validation**: Verifies values are within acceptable ranges

#### Design Patterns:

- **Chain of Responsibility**: Multiple validators can be chained
- **Strategy Pattern**: Different validation strategies for different data types
- **Composite Pattern**: Combines multiple validation rules

#### Example Usage:

```python
def validate_numeric(value, field_name, min_value=None, max_value=None):
    """
    Numerikus érték érvényességének ellenőrzése.
    
    Args:
        value: Ellenőrizendő érték
        field_name (str): Mező neve (hibaüzenethez)
        min_value (float): Minimum érték (opcionális)
        max_value (float): Maximum érték (opcionális)
        
    Returns:
        tuple: (bool, str) - True/False és hibaüzenet, ha van
    """
    try:
        num_value = float(value)
        
        if min_value is not None and num_value < min_value:
            return False, f"{field_name} értéke nem lehet kisebb, mint {min_value}."
        
        if max_value is not None and num_value > max_value:
            return False, f"{field_name} értéke nem lehet nagyobb, mint {max_value}."
            
        return True, ""
        
    except (ValueError, TypeError):
        return False, f"{field_name} értékének számnak kell lennie."
```

### 3.5 Formatting (`formatting.py`)

The formatting module provides utilities for formatting data for display, ensuring consistent representation across the application.

#### Key Components:

- **Date Formatting**: Formats dates and times
- **Number Formatting**: Formats numbers, currencies, and quantities
- **Status Formatting**: Formats status codes into human-readable text
- **Validation Error Formatting**: Formats validation errors

#### Design Patterns:

- **Strategy Pattern**: Different formatting strategies for different data types
- **Factory Pattern**: Creates appropriate formatters for different contexts
- **Adapter Pattern**: Converts between different data representations

#### Example Usage:

```python
def format_price(price):
    """
    Ár formázása olvasható formátumra.
    
    Args:
        price (float): Ár
        
    Returns:
        str: Formázott ár (pl. 1 234,56 Ft)
    """
    if price is None:
        return ""
    
    try:
        # Convert to float to ensure numeric formatting
        price_float = float(price)
        return f"{price_float:,.2f} Ft".replace(",", " ").replace(".", ",")
    except (ValueError, TypeError):
        # Return as string if conversion fails
        return f"{price} Ft" if price else ""
```

### 3.6 Navigation (`navigation.py`)

The navigation module provides utilities for managing page navigation and access control.

#### Key Components:

- **Menu Generation**: Creates navigation menus based on user role
- **Access Control**: Enforces role-based access control
- **Page Switching**: Handles page transitions
- **Query Parameter Handling**: Processes URL query parameters

#### Design Patterns:

- **Strategy Pattern**: Different navigation strategies for different roles
- **Chain of Responsibility**: Processes navigation requests through access control
- **Command Pattern**: Encapsulates navigation actions

#### Example Usage:

```python
def check_role_access(allowed_roles):
    """
    Ellenőrzi, hogy a felhasználó rendelkezik-e a szükséges jogosultsággal.
    
    Args:
        allowed_roles (list): Engedélyezett szerepkörök listája
        
    Returns:
        bool: True, ha a felhasználó jogosult, egyébként False
    """
    if not is_authenticated():
        st.error("Ez az oldal bejelentkezést igényel.")
        # Átirányítás a bejelentkezési oldalra
        st.switch_page("main.py")
        return False
    
    user = get_current_user()
    role = user.get("role", "").lower()
    
    if not allowed_roles or role in [r.lower() for r in allowed_roles]:
        return True
    
    st.error(f"Nincs jogosultsága az oldal megtekintéséhez. Az Ön szerepköre: {role}")
    
    # Átirányítás a megfelelő irányítópultra
    if role == "termelő":
        st.switch_page("pages/producer_dashboard.py")
    elif role == "ügyintéző":
        st.switch_page("pages/operator_dashboard.py")
    elif role == "admin":
        st.switch_page("pages/admin_dashboard.py")
    else:
        st.switch_page("main.py")
    
    return False
```

## 4. Common Design Patterns and Conventions

### 4.1 Function Return Pattern

Most functions follow a consistent return pattern:

```python
def some_operation(params):
    """
    Function description.
    
    Args:
        params: Parameters description
        
    Returns:
        tuple: (success, result) where success is a boolean and result is the data or error message
    """
    try:
        # Operation logic
        return True, result_data
    except Exception as e:
        return False, str(e)
```

This pattern enables:
- Consistent error handling
- Easy result checking
- Standardized API across the application

### 4.2 Session State Management

Session state management follows a consistent pattern:

```python
# Initialize state if it doesn't exist
if "state_key" not in st.session_state:
    st.session_state.state_key = default_value

# Read state
current_value = st.session_state.get("state_key", default_value)

# Update state
st.session_state.state_key = new_value
```

### 4.3 Responsive Design Pattern

Responsive design follows a device detection and adaptation pattern:

```python
# Detect device type
is_mobile = st.session_state.get("is_mobile", False)
is_tablet = st.session_state.get("is_tablet", False)

# Adapt UI based on device type
if is_mobile:
    # Mobile-specific UI
    mobile_component()
elif is_tablet:
    # Tablet-specific UI
    tablet_component()
else:
    # Desktop UI
    desktop_component()
```

### 4.4 Error Handling Pattern

Error handling follows a standardized pattern:

```python
try:
    # Operation that may fail
    result = some_operation()
except Exception as e:
    # Log error
    logger.error(f"Error in operation: {str(e)}")
    
    # Show user-friendly message
    st.error(f"Hiba történt: {user_friendly_message}")
    
    # Return failure
    return False, user_friendly_message
```

### 4.5 API Communication Pattern

API communication follows a standardized pattern:

```python
def api_function(params):
    """
    API function description.
    
    Args:
        params: Parameters description
        
    Returns:
        tuple: (success, result)
    """
    try:
        # Prepare API request
        url = f"{API_BASE_URL}/{endpoint}"
        headers = get_auth_headers()
        
        # Make request
        response = requests.get(url, headers=headers, params=params)
        
        # Process response
        if response.status_code == 200:
            return True, response.json()
        else:
            error_message = get_error_message(response)
            return False, error_message
    except Exception as e:
        return False, str(e)
```

## 5. Data Flow Patterns

### 5.1 API Request-Response Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │      │               │
│  UI Component ├─────►│  API Client   ├─────►│  Backend API  ├─────►│  API Client   │
│               │      │               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────────────┘      └───────┬───────┘
        │                                                                    │
        │                                                                    │
┌───────▼───────┐                                                    ┌───────▼───────┐
│               │                                                    │               │
│  User Action  │                                                    │  Result       │
│               │                                                    │  Processing   │
│               │                                                    │               │
└───────────────┘                                                    └───────────────┘
```

1. **User Action**: User initiates an action in the UI
2. **API Client**: Formats the request, adds authentication
3. **Backend API**: Processes the request
4. **API Client**: Processes the response, formats data
5. **Result Processing**: Updates UI with the result

### 5.2 Form Validation Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Form Input   ├─────►│  Validators   ├─────►│  Validation   │
│               │      │               │      │  Results      │
└───────┬───────┘      └───────────────┘      └───────┬───────┘
        │                                             │
        │                                             │
        │                                             │
┌───────▼───────┐                            ┌───────▼───────┐
│               │                            │               │
│  User Input   │                            │  Error        │
│               │                            │  Display      │
│               │                            │               │
└───────────────┘                            └───────────────┘
```

1. **User Input**: User enters data in a form
2. **Form Input**: Data is collected from the form
3. **Validators**: Data is validated against rules
4. **Validation Results**: Results are collected
5. **Error Display**: Errors are displayed if validation fails

### 5.3 Session Management Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  User Login   ├─────►│  API Client   ├─────►│  Backend API  │
│               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────┬───────┘
        │                                             │
        │                                             │
        │                                             │
┌───────▼───────┐                            ┌───────▼───────┐
│               │                            │               │
│  Session      │◄───────────────────────────┤  Token        │
│  State        │                            │  Response     │
│               │                            │               │
└───────┬───────┘                            └───────────────┘
        │
        │
        │
┌───────▼───────┐
│               │
│  Application  │
│  State        │
│               │
└───────────────┘
```

1. **User Login**: User submits login credentials
2. **API Client**: Sends authentication request
3. **Backend API**: Validates credentials, issues token
4. **Token Response**: Token is returned to client
5. **Session State**: Token is stored in session state
6. **Application State**: Application updates based on authenticated state

## 6. Module Interactions

### 6.1 Key Module Dependencies

```
responsive_ui.py
  ├── session.py (for accessing is_mobile state)
  └── config.py (for theme colors)

api_client.py
  ├── session.py (for authentication token)
  └── app_config.py (for API configuration)

session.py
  └── app_config.py (for session configuration)

formatting.py
  └── app_config.py (for status codes and formatting options)

navigation.py
  ├── session.py (for authentication state)
  └── app_config.py (for menu configuration)

page_utils.py
  ├── responsive_ui.py (for setup_responsive_ui)
  └── components.sidebar (for render_sidebar)
```

### 6.2 Common Utility Integration

```python
# Typical page setup integrating multiple utilities
import streamlit as st
from utils.page_utils import set_page_config
from utils.session import is_authenticated, get_current_user
from utils.navigation import check_role_access
from utils.responsive_ui import create_responsive_columns
from utils.formatting import format_currency
from api import offers_api
from utils.api_client import safe_api_call

# Page configuration
set_page_config("Dashboard", "🏠")

# Access control
if not check_role_access(["admin", "operator"]):
    st.stop()

# User information
user = get_current_user()
st.write(f"Welcome, {user.get('contact_name')}")

# Responsive layout
col1, col2 = create_responsive_columns([2, 1])

with col1:
    # API data loading with safe handling
    success, offers = safe_api_call(
        offers_api.get_offers, 
        "ajánlatok lekérése"
    )
    
    if success:
        # Data processing with formatting
        total_value = sum(offer.get("price", 0) * offer.get("quantity", 0) for offer in offers)
        st.metric("Total value", format_currency(total_value))
    else:
        st.error("Failed to load offers")

with col2:
    # Secondary content
    st.write("### Quick actions")
```

## 7. API Response Handling

### 7.1 Standard API Response Format

The application uses a consistent API response format:

```python
# API response format
{
    "data": {...},  # Primary data
    "meta": {       # Metadata
        "total": 123,
        "page": 1,
        "limit": 10
    }
}
```

### 7.2 API Response Processing

```python
def process_api_response(response):
    """
    Process API response with standardized handling.
    
    Args:
        response: API response
        
    Returns:
        tuple: (success, data, meta)
    """
    # Check for errors
    if not response.ok:
        try:
            error_data = response.json()
            error_message = error_data.get("detail", response.reason)
        except:
            error_message = f"HTTP error {response.status_code}: {response.reason}"
        return False, error_message, None
        
    # Process successful response
    try:
        response_data = response.json()
        
        # Handle different response formats
        if isinstance(response_data, dict):
            data = response_data.get("data", response_data)
            meta = response_data.get("meta", {})
            return True, data, meta
        else:
            # Response is a list or other format
            return True, response_data, {}
    except Exception as e:
        return False, f"Error processing response: {str(e)}", None
```

### 7.3 API Error Handling

```python
def handle_api_error(error, operation_type):
    """
    API hibák egységes kezelése részletes hibaüzenetekkel és felhasználói visszajelzéssel.
    
    Args:
        error (str): A hibaüzenet.
        operation_type (str): A művelet típusa (pl. "ajánlatok betöltése").
    """
    error_message = str(error)
    
    # Hibaüzenet naplózása
    logger.error(f"API error during {operation_type}: {error_message}")
    
    # 401/403 hibakódok kezelése (hitelesítési hibák)
    if "401" in error_message or "Unauthorized" in error_message or "403" in error_message:
        st.error("Munkamenetének érvényessége lejárt. Kérjük, jelentkezzen be újra!")
        # Clear auth token
        if 'token' in st.session_state:
            st.session_state.token = None
        st.rerun()
    
    # 404 hibakódok kezelése (nem található erőforrás)
    elif "404" in error_message or "Not Found" in error_message:
        st.error(f"A kért erőforrás nem található. ({operation_type})")
    
    # Kapcsolódási problémák kezelése
    elif "Connection" in error_message or "timeout" in error_message.lower():
        st.error(f"Hálózati hiba történt. Kérjük, ellenőrizze internetkapcsolatát. ({operation_type})")
    
    # Egyéb hibák kezelése
    else:
        st.error(f"Váratlan hiba történt: {error_message} ({operation_type})")
```

## 8. Responsive Design Implementation

### 8.1 Device Detection

The application uses JavaScript to detect the device type:

```python
def detect_mobile():
    """
    Mobileszköz detektálása és tárolása a session state-ben.
    
    Ezt a függvényt az oldal elejére kell helyezni a betöltés során.
    """
    if "is_mobile_detected" not in st.session_state:
        st.session_state.is_mobile_detected = False
        st.session_state.is_mobile = False
        st.session_state.is_tablet = False
        st.session_state.screen_width = 1200  # Default value
        
        # JavaScript a böngésző szélesség ellenőrzésére
        st.markdown("""
        <script>
        // Mobileszköz detektálás és session tárolás
        function checkDeviceType() {
            const width = window.innerWidth;
            const isMobile = width < 768;
            const isTablet = width >= 768 && width < 992;
            
            window.parent.postMessage({
                type: "streamlit:setSessionState", 
                data: {
                    is_mobile: isMobile,
                    is_tablet: isTablet,
                    screen_width: width
                }
            }, "*");
            
            console.log("Device detection:", {isMobile, isTablet, width});
        }
        
        // Betöltéskor és átméretezéskor is ellenőrzünk
        checkDeviceType();
        window.addEventListener('resize', checkDeviceType);
        </script>
        """, unsafe_allow_html=True)
        
        st.session_state.is_mobile_detected = True
```

### 8.2 Responsive Components

The application provides responsive components that adapt to the device type:

```python
def render_responsive_tabs(tabs_content, key_prefix="tab"):
    """
    Reszponzív tabok megjelenítése a képernyőméret alapján.
    Mobilon dropdown menüként, nagyobb képernyőkön tabként.
    
    Args:
        tabs_content (dict): Tab címek és tartalom függvények dictionary-je.
                           pl: {"Tab1": tab1_function, "Tab2": tab2_function}
        key_prefix (str): Egyedi prefix a session state kulcsokhoz
                          
    Returns:
        None
    """
    is_mobile = st.session_state.get('is_mobile', False)
    
    tab_state_key = f"{key_prefix}_selected"
    
    # Inicializáljuk a session state-et, ha még nincs
    if tab_state_key not in st.session_state:
        st.session_state[tab_state_key] = list(tabs_content.keys())[0]
    
    # Mobil eszközön dropdown megjelenítés
    if is_mobile:
        selected_tab = st.selectbox(
            "Válasszon nézetet:",
            options=list(tabs_content.keys()),
            index=list(tabs_content.keys()).index(st.session_state[tab_state_key]),
            key=f"{key_prefix}_selector"
        )
        st.session_state[tab_state_key] = selected_tab
        
        # A kiválasztott tab tartalmának megjelenítése
        tabs_content[selected_tab]()
    
    # Asztali nézetben tab komponens használata
    else:
        tab_objects = st.tabs(list(tabs_content.keys()))
        
        # Minden tab tartalom renderelése a megfelelő tabban
        for i, (tab_name, tab_func) in enumerate(tabs_content.items()):
            with tab_objects[i]:
                tab_func()
```

### 8.3 Responsive Layout

The application provides responsive layouts that adapt to the device type:

```python
def create_responsive_columns(ratios=None, mobile_stack=True):
    """
    Reszponzív oszlopok létrehozása.
    
    Args:
        ratios (list, optional): Oszlop arányok. Defaults to None.
        mobile_stack (bool, optional): Mobilon egymás alá rendezi. Defaults to True.
    
    Returns:
        list: Streamlit oszlopok listája
    """
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    # Ha mobilnézet és mobile_stack=True, akkor egyenlő 1-es oszlopok
    if is_mobile and mobile_stack:
        return [st.column(1) for _ in (ratios or [1])]
    
    # Tabletnézet esetén egyszerűsített oszlopok, ha sok oszlop van
    if is_tablet and ratios and len(ratios) > 3:
        # Tablet eszközön maximum 3 oszlopot engedünk
        simplified_ratios = ratios[:3]
        # Az arányokat normalizáljuk, hogy 1 legyen az összegük
        total = sum(simplified_ratios)
        normalized_ratios = [r/total for r in simplified_ratios]
        return st.columns(normalized_ratios)
    
    # Egyébként az adott arányokkal, vagy alapértelmezettekkel
    return st.columns(ratios or [1, 1])
```

## 9. JavaScript Integration

### 9.1 Session State Manipulation

The application uses JavaScript to manipulate the Streamlit session state:

```python
def toggle_sidebar():
    """
    Oldalsáv be- és kikapcsolása JavaScript segítségével.
    """
    st.markdown("""
    <script>
    function toggleSidebar() {
        const sidebar = parent.document.querySelector('.sidebar');
        
        if (sidebar) {
            const isVisible = !sidebar.classList.contains('collapsed');
            
            // Update session state
            const newState = !isVisible;
            window.parent.postMessage({
                type: "streamlit:setSessionState",
                data: { "sidebar_visible": newState }
            }, "*");
            
            // Toggle sidebar visibility
            if (isVisible) {
                sidebar.classList.add('collapsed');
            } else {
                sidebar.classList.remove('collapsed');
            }
        }
    }
    </script>
    """, unsafe_allow_html=True)
    
    # Toggle button
    if st.button("Toggle Sidebar", key="toggle_sidebar_btn"):
        st.markdown("<script>toggleSidebar();</script>", unsafe_allow_html=True)
```

### 9.2 Toast Notifications

The application uses JavaScript to display toast notifications:

```python
def show_toast(message, type="info", duration=3000):
    """
    Toast üzenet megjelenítése a képernyő tetején.
    
    Args:
        message (str): Üzenet szövege
        type (str): Üzenet típusa: "info", "success", "warning", "error"
        duration (int): Megjelenés időtartama ezredmásodpercben
    """
    # Toast container létrehozása, ha még nem létezik
    if "toast_container" not in st.session_state:
        st.session_state.toast_container = True
        st.markdown("""
        <div class="toast-container"></div>
        """, unsafe_allow_html=True)
    
    # Toast ID generálása
    import uuid
    toast_id = f"toast_{uuid.uuid4().hex}"
    
    # Toast típus CSS osztálya
    toast_class = f"toast toast-{type}"
    
    # Toast HTML
    toast_html = f"""
    <div id="{toast_id}" class="{toast_class}">
        {message}
    </div>
    """
    
    # JavaScript a toast megjelenítéséhez és eltüntetéséhez
    js = f"""
    <script>
        // Toast létrehozása
        (function() {{
            const toastContainer = document.querySelector('.toast-container');
            const toastElement = document.createElement('div');
            toastElement.innerHTML = `{toast_html}`;
            
            // Toast hozzáadása a containerhez
            toastContainer.appendChild(toastElement.firstElementChild);
            
            // Toast eltüntetése a megadott idő után
            setTimeout(() => {{
                const toast = document.getElementById('{toast_id}');
                if (toast) {{
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateY(-20px)';
                    toast.style.transition = 'opacity 0.3s, transform 0.3s';
                    
                    setTimeout(() => {{
                        if (toast && toast.parentNode) {{
                            toast.parentNode.removeChild(toast);
                        }}
                    }}, 300);
                }}
            }}, {duration});
        }})();
    </script>
    """
    
    # JavaScript beszúrása
    st.markdown(js, unsafe_allow_html=True)
```

## 10. Caching Strategies

### 10.1 API Response Caching

The application uses caching to improve performance when making API requests:

```python
def cacheable(func):
    """
    Dekorátor az API válaszok gyorsítótárazásához.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Gyorsítótár kulcs generálása
        cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
        
        # Ha van gyorsítótárazott válasz, azt adjuk vissza
        if "api_cache" in st.session_state and cache_key in st.session_state.api_cache:
            logger.debug(f"Cache hit: {cache_key}")
            return st.session_state.api_cache[cache_key]
        
        # Ha nincs gyorsítótárazott válasz, végrehajtjuk a függvényt
        result = func(*args, **kwargs)
        
        # Gyorsítótárazzuk az eredményt
        if "api_cache" not in st.session_state:
            st.session_state.api_cache = {}
        st.session_state.api_cache[cache_key] = result
        
        return result
    return wrapper
```

### 10.2 Lazy Loading

The application uses lazy loading to improve performance when loading data:

```python
def lazy_load_cache(cache_key: str, data_loader_func: Callable, cache_ttl: int = 300):
    """
    Adatok lekérése gyorsítótár használatával, csak szükség esetén tölti be az adatokat.
    
    Args:
        cache_key (str): Egyedi azonosító a gyorsítótárazott adatnak
        data_loader_func (callable): Adatbetöltő függvény, amely végrehajtásra kerül, ha nincs gyorsítótárazott adat
        cache_ttl (int, optional): A gyorsítótár élettartama másodpercben. Defaults to 300.
        
    Returns:
        tuple: (success, result), ahol success egy boolean értéket és result az eredményt tartalmazza
    """
    # Gyorsítótár inicializálása, ha még nem létezik
    if "data_cache" not in st.session_state:
        st.session_state.data_cache = {}
    
    # Cache időbélyegek inicializálása, ha még nem létezik
    if "data_cache_timestamps" not in st.session_state:
        st.session_state.data_cache_timestamps = {}
    
    # Ellenőrizzük, hogy van-e gyorsítótárazott adat és friss-e
    current_time = time.time()
    if (cache_key in st.session_state.data_cache and 
        cache_key in st.session_state.data_cache_timestamps and
        current_time - st.session_state.data_cache_timestamps[cache_key] < cache_ttl):
        
        logger.debug(f"Using cached data for key: {cache_key}")
        return st.session_state.data_cache[cache_key]
    
    # Ha nincs gyorsítótárazott adat vagy lejárt, betöltjük az adatokat
    logger.debug(f"Loading fresh data for key: {cache_key}")
    try:
        result = data_loader_func()
        
        # Az eredményt eltároljuk a gyorsítótárban
        st.session_state.data_cache[cache_key] = result
        st.session_state.data_cache_timestamps[cache_key] = current_time
        
        return result
    except Exception as e:
        logger.error(f"Error in lazy_load_cache for key {cache_key}: {str(e)}")
        return False, str(e)
```

## 11. Naming Conventions

### 11.1 Function Naming

- `get_*`: Functions that retrieve data
- `set_*`: Functions that set data
- `is_*`: Functions that return a boolean
- `has_*`: Functions that check if something exists
- `validate_*`: Functions that validate data
- `format_*`: Functions that format data
- `handle_*`: Functions that handle events or operations
- `render_*`: Functions that render UI components
- `show_*`: Functions that display UI components
- `init_*`: Functions that initialize data or state
- `load_*`: Functions that load data
- `save_*`: Functions that save data
- `delete_*`: Functions that delete data
- `create_*`: Functions that create new data or components
- `update_*`: Functions that update existing data
- `process_*`: Functions that process data
- `convert_*`: Functions that convert data between formats
- `check_*`: Functions that check conditions

### 11.2 Variable Naming

- `*_data`: Data variables
- `*_info`: Information variables
- `*_list`: List variables
- `*_dict`: Dictionary variables
- `*_map`: Mapping variables
- `*_count`: Count variables
- `*_index`: Index variables
- `*_key`: Key variables
- `*_value`: Value variables
- `*_id`: ID variables
- `*_name`: Name variables
- `*_type`: Type variables
- `*_status`: Status variables
- `*_flag`: Flag variables
- `*_timestamp`: Timestamp variables
- `*_date`: Date variables
- `*_time`: Time variables
- `is_*`: Boolean variables

### 11.3 Constant Naming

- `API_BASE_URL`: All uppercase for constants
- `DEFAULT_SESSION_VARS`: All uppercase for constants
- `SESSION_TIMEOUT`: All uppercase for constants

## 12. Error Handling and Logging

### 12.1 Logging Setup

The application uses Python's logging module for logging:

```python
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
```

### 12.2 Error Logging

The application uses consistent error logging:

```python
try:
    # Operation that may fail
    result = some_operation()
except Exception as e:
    # Log error with context
    logger.error(f"Error in operation: {str(e)}")
    
    # Include stack trace for debugging
    import traceback
    logger.debug(f"Stack trace: {traceback.format_exc()}")
```

### 12.3 User Feedback

The application provides user-friendly error messages:

```python
def show_error(message, icon="❌"):
    """
    Hiba értesítés megjelenítése.
    
    Args:
        message (str): Megjelenítendő üzenet
        icon (str): Emoji ikon
    """
    st.error(f"{icon} {message}")
```

## 13. Security Considerations

### 13.1 Authentication Token Management

The application manages authentication tokens securely:

```python
def get_auth_token():
    """
    Visszaadja a jelenlegi autentikációs tokent.
    
    Returns:
        str: JWT token vagy None, ha nincs bejelentkezve a felhasználó
    """
    print(f"==== GET AUTH TOKEN ====")
    
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    token_key = session_vars.get("token", "auth_token")
    print(f"Token key: {token_key}")
    
    # Check if token exists
    token = st.session_state.get(token_key)
    print(f"Token found: {bool(token)}")
    if token:
        print(f"Token value: {token[:10]}...")
        # Csak session token esetén ellenőrizd a lejáratot!
        expiry = st.session_state.get(SESSION_TOKEN_EXPIRY_KEY)
        if expiry and time.time() > expiry:
            print("Token expired")
            clear_session()
            return None
        print("============================")
        return token
    # Ha nincs session token, próbáljuk környezeti változóból (lejárat ellenőrzés NINCS, az API-ra bízzuk)
    env_token = os.environ.get("API_AUTH_TOKEN")
    print(f"Env token found: {bool(env_token)}")
    if env_token:
        print(f"Env token value: {env_token[:10]}...")
        print("============================")
        return env_token
    print("============================")
    return None
```

### 13.2 Session Timeout

The application implements session timeouts for security:

```python
def check_session_timeout():
    """Check if the session has timed out and log out if necessary"""
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    
    # Skip if not authenticated
    if not st.session_state.get(auth_key, False):
        return
    
    current_time = time.time()
    last_activity = st.session_state.get(SESSION_ACTIVITY_KEY, current_time)
    
    # If session has timed out
    if current_time - last_activity > SESSION_TIMEOUT:
        logger.info("Session timed out, logging out user")
        clear_session()
        # Show message on next page load
        st.session_state.show_timeout_message = True
    else:
        # Update last activity time
        st.session_state[SESSION_ACTIVITY_KEY] = current_time
```

### 13.3 Input Validation

The application validates user input to prevent security issues:

```python
def validate_email(email):
    """
    Ellenőrzi, hogy az e-mail cím formátuma helyes-e.
    
    Args:
        email (str): Ellenőrizendő e-mail cím
        
    Returns:
        bool: True, ha az e-mail formátuma helyes, egyébként False
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None
```

## 14. Conclusion

The utils package provides a comprehensive set of utilities for the Streamlit-based agricultural product offer management application. It follows consistent design patterns and conventions, enabling efficient development and maintenance of the application.

Key features of the utils package include:
- Consistent API communication pattern with error handling
- Session state management with authentication and timeout
- Responsive design utilities for different device types
- Data formatting and validation utilities
- Navigation and access control utilities

This architecture enhances maintainability, scalability, and user experience across the application, providing a solid foundation for the higher-level components.
