# Frontend - streamlit_app_pages_PRODUCER Architecture

## 1. Overview

This document describes the architecture and code organization of the producer module within the Streamlit-based frontend application. The producer module is part of a multi-layered client-server architecture designed for managing agricultural product offers between producers and a central company.

The producer module provides interfaces for agricultural producers to create and manage offers, view statistics, and update their profile settings. It follows a modular design pattern with clear separation of concerns, consistent API usage, and standardized UI components.

## 2. Directory Structure

```
streamlit_app/pages/
├── producer/                       # Producer module directory
│   ├── __init__.py                 # Package initialization
│   ├── create_offer.py             # Offer creation interface
│   ├── dashboard.py                # Producer dashboard
│   ├── offers.py                   # Offer management and listing
│   ├── profile.py                  # Profile and settings management
│   └── statistics.py               # Statistical reports and visualizations
├── producer_create_offer.py        # Redirection file (flat structure)
├── producer_dashboard.py           # Redirection file (flat structure)
├── producer_offers.py              # Redirection file (flat structure)
├── producer_profile.py             # Redirection file (flat structure)
└── producer_statistics.py          # Redirection file (flat structure)
```

## 3. File Structure Pattern

Each producer page follows a consistent file structure pattern:

1. **Module Documentation**: Docstring describing the module purpose
2. **Imports**: Organized by category (Streamlit, standard library, application modules)
3. **Helper Functions**: Utility functions used within the page
4. **Main Page Function**: Primary function that renders the page
5. **Direct Execution Block**: Code executed when the file runs directly

Example pattern from `producer/dashboard.py`:

```python
# Termelői irányítópult
"""
Termelői irányítópult oldal.
"""
import streamlit as st  # Streamlit imports
import datetime         # Standard library imports
from api import offers as offers_api  # Application imports
from components.sidebar import render_sidebar
# ... more imports ...

def load_settings_to_session_state():
    """
    Supporting helper function implementation...
    """
    # Implementation...

def show_producer_dashboard():
    """
    Termelői irányítópult megjelenítése.
    """
    # Page implementation...

# Direct execution block
if __name__ == "__main__":
    # Page config and setup
    st.set_page_config(
        page_title=get_page_title("Termelői Irányítópult"),
        page_icon="🏠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Sidebar rendering
    render_sidebar()
    
    # Display the page
    show_producer_dashboard()
```

## 4. Core Design Patterns

### 4.1 Page Rendering Pattern

All producer pages follow the same page rendering pattern:

1. **Authentication Check**: Verify user is logged in and has producer permissions
2. **Session State Initialization**: Set up any necessary session state variables
3. **Page Setup**: Set title, layout, and other page properties
4. **Content Rendering**: Display page content based on the specific page function
5. **Action Handling**: Process user input and update the system as needed

```python
def show_producer_offers():
    """
    Termelői ajánlatok listázása oldal megjelenítése.
    """
    # Initialize session state
    init_session_state()
    
    # Page title
    st.title("Ajánlataim")
    
    # Authentication check
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Producer role check
    user = get_current_user()
    if not user or user.get("role", "").lower() != "termelő":
        show_error("Ehhez a funkcióhoz termelői jogosultság szükséges.")
        return
    
    # Page content
    # ...content rendering code...
    
    # Action handling
    # ...action handling code...
```

### 4.2 API Integration Pattern

Producer pages interact with the backend through a consistent API integration pattern:

1. **API Call**: Make the API request through the appropriate module
2. **Success/Error Handling**: Check the result of the API call
3. **State Update**: Update the UI state based on the API response
4. **Feedback**: Provide feedback to the user about the operation's success or failure

```python
# Making an API call
success, result = offers_api.get_offers(params=params)

# Success/Error handling
if success:
    offers = result
    # Process the offers...
else:
    show_error(f"Hiba az ajánlatok lekérésekor: {result}")
    return

# State update and feedback
if submit_button:
    success, result = offers_api.create_offer(offer_data)
    
    if success:
        show_success("Ajánlat sikeresen létrehozva!")
        # Navigate to offers page
        st.session_state.navigate_to_offers = True
    else:
        show_error(f"Hiba az ajánlat létrehozásakor: {result}")
```

### 4.3 Form Processing Pattern

Producer pages use a consistent form processing pattern:

1. **Form Creation**: Create a form using Streamlit's form API
2. **Input Collection**: Collect user input through form fields
3. **Validation**: Validate the input when the form is submitted
4. **API Action**: Perform the appropriate API action with the validated data
5. **Feedback**: Provide user feedback and update the UI state

```python
# Form creation
with st.form("offer_form"):
    # Input collection
    delivery_date = st.date_input(
        "Beszállítás dátuma *",
        value=tomorrow,
        min_value=tomorrow,
        help="Az a dátum, amikor a terméket beszállítaná"
    )
    
    # More form fields...
    
    # Submit button
    submit = st.form_submit_button("Ajánlat létrehozása", type="primary", use_container_width=True)
    
    if submit:
        # Validation
        errors = []
        if delivery_date < tomorrow:
            errors.append("A beszállítás dátuma nem lehet múltbeli.")
        # More validation...
        
        if errors:
            for error in errors:
                show_error(error)
        else:
            # API action
            offer_data = {
                "product_type_id": st.session_state.selected_type_id,
                "quality_grade_id": selected_grade_id,
                "quantity_in_kg": quantity * 1000 if unit == "tonna" else quantity,
                "delivery_date": delivery_date.strftime("%Y-%m-%d"),
                "note": note or None
            }
            
            success, result = offers_api.create_offer(offer_data)
            
            # Feedback
            if success:
                show_success("Ajánlat sikeresen létrehozva!")
                st.session_state.navigate_to_offers = True
            else:
                show_error(f"Hiba az ajánlat létrehozásakor: {result}")
```

### 4.4 Cascading Selection Pattern

The producer module extensively uses a cascading selection pattern where the selection of one option affects the available options in dependent dropdowns:

1. **Primary Selection**: User selects a category
2. **API Request**: Fetch related data for the selected primary option
3. **Update Options**: Update dependent dropdown with new options
4. **Repeat**: Continue the cascade for additional levels

```python
# Primary selection
selected_category = st.selectbox(
    "Alapértelmezett termékkategória",
    options=[c.get("id") for c in category_options],
    format_func=lambda x: next((c["name"] for c in category_options if c["id"] == x), "-- Válasszon kategóriát --"),
    index=selected_category_index,
    key="category_selectbox"
)

# Update state based on selection
if st.session_state.selected_category_id != selected_category:
    st.session_state.selected_category_id = selected_category
    st.session_state.selected_type_id = None
    st.session_state.selected_grade_id = None

# Fetch related data for dependent dropdown
product_types = []
if st.session_state.selected_category_id:
    success, types_result = products_api.get_product_types(category_id=st.session_state.selected_category_id)
    if success:
        product_types = types_result
    else:
        show_error(f"Hiba a terméktípusok lekérésekor: {types_result}")

# Dependent dropdown
selected_type = st.selectbox(
    "Alapértelmezett terméktípus",
    options=[t.get("id") for t in type_options],
    format_func=lambda x: next((t["name"] for t in type_options if t["id"] == x), "-- Válasszon terméktípust --"),
    index=selected_type_index,
    key="type_selectbox",
    disabled=not st.session_state.selected_category_id
)
```

### 4.5 Session State Management Pattern

The producer module uses a consistent session state management pattern:

1. **Initialization**: Initialize session state variables if they don't exist
2. **Update Logic**: Update session state based on user interactions
3. **Page Navigation**: Use session state to manage page navigation
4. **Persistence**: Store and retrieve settings from session state

```python
# Initialization
if "needs_rerun" not in st.session_state:
    st.session_state.needs_rerun = False

# Update logic
if selected_category != st.session_state.get("category_select", ""):
    st.session_state.category_select = selected_category
    st.session_state.product_types = product_types

# Page navigation
if st.session_state.get("navigate_to_offers", False):
    st.session_state.navigate_to_offers = False
    if st.button("Tovább az ajánlataimhoz", type="primary"):
        st.switch_page("pages/producer_offers.py")

# Persistence
if success and result:
    # Store user settings in session state
    st.session_state.user_settings = {
        "default_product_type": {
            "id": settings.get("default_product_type_id"),
            "name": settings.get("default_product_type_name", "")
        }
        # More settings...
    }
```

## 5. Producer Module Pages

### 5.1 Dashboard (`dashboard.py`)

The producer dashboard provides an overview of key metrics and quick access to common functions.

**Key Features:**
- Summary statistics of the producer's offers
- Quick action buttons for common tasks
- Activity feed displaying recent notifications
- Default settings overview

**Data Flow:**
1. Fetch user settings from the API and store in session state
2. Fetch offer data from the API
3. Calculate and display summary metrics
4. Display quick action buttons
5. Display waiting offers requiring action

**UI Components:**
- Metric cards for offer statistics
- Status-based color coded indicators
- Two-column layout for information organization
- Action buttons for navigation and offer approval

```python
def show_producer_dashboard():
    """
    Termelői irányítópult megjelenítése.
    """
    # Authentication and role checks...
    
    # Load user settings
    load_settings_to_session_state()
    
    # Fetch offers
    success, result = offers_api.get_offers()
    
    if success:
        offers = result
        
        # Calculate statistics
        total_offers = len(offers)
        pending_offers = sum(1 for o in offers if o.get("status") == "CREATED")
        confirmed_offers = sum(1 for o in offers if o.get("status") == "CONFIRMED_BY_COMPANY")
        
        # Display metrics and quick actions
        col1, col2 = st.columns(2)
        
        with col1:
            # Summary metrics
            # ...
            
            # Quick action buttons
            if st.button("Új ajánlat létrehozása", type="primary"):
                st.switch_page("pages/producer_create_offer.py")
        
        with col2:
            # Display waiting offers that need action
            # ...
```

### 5.2 Create Offer (`create_offer.py`)

The create offer page allows producers to submit new product offers with detailed specifications.

**Key Features:**
- Form for creating new offers
- Cascading selection of product category, type, and quality grade
- Default value population from user settings
- Validation of all offer data

**Data Flow:**
1. Load user default settings from the API
2. Fetch product categories from the API
3. Update product types and quality grades based on selection
4. Collect and validate form input
5. Submit the offer data to the API
6. Provide feedback and navigation options

**UI Components:**
- Multi-field form with appropriate input types
- Cascading dropdown selectors
- Date picker for delivery date
- Quantity input with unit selection
- Submit button and validation feedback

### 5.3 Offers (`offers.py`)

The offers page displays a list of the producer's offers with detailed filtering and management options.

**Key Features:**
- Filtering offers by status, date, and sorting options
- Detailed view of each offer with expandable sections
- Status-based color coding and visual indicators
- Offer actions based on current status

**Data Flow:**
1. Build query parameters based on user-selected filters
2. Fetch offers from the API with the filter parameters
3. Display offers in a table format
4. Fetch detailed offer data for expandable sections
5. Process offer actions (accept/reject) through API calls

**UI Components:**
- Filter controls for status, date, and sorting
- Tabular display of offers with key information
- Expandable sections for detailed offer information
- Action buttons for managing offers
- Status indicators and progress visualization

### 5.4 Profile (`profile.py`)

The profile page allows producers to manage their personal information and default settings.

**Key Features:**
- Tabbed interface for different setting categories
- Personal information management
- Default product settings for offer creation
- Password change functionality

**Data Flow:**
1. Fetch current user data from the API
2. Fetch product data for cascading selection
3. Update user information through API calls
4. Update default settings through API calls
5. Change password through API calls

**UI Components:**
- Tabbed interface for different settings categories
- Forms for each settings category
- Cascading selection for default product settings
- Password change form with validation
- Feedback messages for each operation

### 5.5 Statistics (`statistics.py`)

The statistics page provides detailed statistical analysis of the producer's offers.

**Key Features:**
- Filtering options for time period and product selection
- Summary metrics of offer performance
- Visual charts for different statistical views
- Data export functionality

**Data Flow:**
1. Build query parameters based on selected filters
2. Fetch offers from the API with filter parameters
3. Calculate summary statistics from offer data
4. Generate visual charts from offer data
5. Prepare data for export if requested

**UI Components:**
- Filter controls for time period and product selection
- Metric cards for summary statistics
- Various chart types for data visualization
- Progress indicators for acceptance rates
- Export button for data download

## 6. Common Implementation Patterns

### 6.1 Authentication and Authorization

All producer pages implement the same authentication and authorization pattern:

```python
# Authentication check
if not is_authenticated():
    show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
    st.switch_page("pages/auth_login.py")
    return

# Producer role check
user = get_current_user()
if not user or user.get("role", "").lower() != "termelő":
    show_error("Ehhez a funkcióhoz termelői jogosultság szükséges.")
    return
```

### 6.2 Error Handling

Error handling follows a consistent pattern across all producer pages:

```python
try:
    # API call
    success, result = api_module.api_function(params)
    
    if not success:
        show_error(f"Hiba a művelet során: {result}")
        return
except Exception as e:
    show_error(f"Váratlan hiba történt: {str(e)}")
    return
```

### 6.3 Debug Mode

Producer pages implement a debug mode for development and troubleshooting:

```python
# Debug mode setup (only for admins)
user = get_current_user()
is_admin = user and user.get("role", "").lower() == "admin"

if is_admin:
    # Debug mode toggle
    st.session_state.debug_mode = st.checkbox("🛠️ Debug mód", value=st.session_state.debug_mode)

# Debug information display
if st.session_state.get("debug_mode", False) and is_admin:
    with st.expander("Debug Információ", expanded=False):
        st.write("### API Válasz Debug")
        success, settings = users_api.get_user_default_settings()
        st.write(f"API hívás sikeres: {success}")
        st.write(f"API válasz: {settings}")
```

### 6.4 State Management

All producer pages use Streamlit's session state for persistent state management:

```python
# Initialize session state variable if it doesn't exist
if "selected_category_id" not in st.session_state:
    st.session_state.selected_category_id = None

# Update session state 
if category_changed:
    st.session_state.selected_category_id = selected_category
    
# Read session state
current_category = st.session_state.get("selected_category_id")
```

### 6.5 Responsive Layout

Producer pages implement responsive layouts using Streamlit's column system:

```python
# Two-column layout
col1, col2 = st.columns(2)

with col1:
    # Left column content
    st.metric(label="Összes ajánlat", value=total_offers)

with col2:
    # Right column content
    st.metric(label="Függőben", value=pending_offers)

# Three-column layout for filters
col1, col2, col3 = st.columns(3)

with col1:
    # First filter option
    selected_status = st.selectbox("Státusz", options=status_options)

with col2:
    # Second filter option
    date_filter = st.selectbox("Dátum szerint", options=date_options)
    
with col3:
    # Third filter option
    sort_by = st.selectbox("Rendezés", options=sort_options)
```

## 7. URL Structure and Navigation

The producer module supports two navigation patterns:

### 7.1 Nested Structure

The original structure uses nested modules:

```
pages/producer/dashboard.py -> accessed via "pages/producer/dashboard.py"
```

### 7.2 Flat Structure

The flat structure provides compatibility with older Streamlit versions:

```
pages/producer_dashboard.py -> redirects to "pages/producer/dashboard.py"
```

This redirection is implemented through simple import forwarding:

```python
# Content of pages/producer_dashboard.py
producer/dashboard.py
```

## 8. Integration with Backend API

### 8.1 API Module Usage

Producer pages use the API modules from the `api` package:

```python
from api import users as users_api
from api import products as products_api
from api import offers as offers_api
```

### 8.2 API Response Handling Pattern

All API calls follow the same response handling pattern:

```python
success, result = api_module.api_function(params)

if success:
    # Handle successful response
    data = result
    # Process data...
else:
    # Handle error
    show_error(f"Error message: {result}")
```

## 9. User Default Settings Management

Producer pages implement a comprehensive pattern for managing user default settings:

### 9.1 Loading Default Settings

```python
def load_settings_to_session_state():
    """
    Load settings from backend API to session state.
    """
    success, settings = users_api.get_user_default_settings()
    
    if success and isinstance(settings, dict) and settings:
        # Clean data - convert "NULL" strings to None
        for key in settings:
            if settings[key] == "NULL":
                settings[key] = None
        
        # Store in session state in structured format
        st.session_state.user_settings = {
            "default_product_type": {
                "id": settings.get("default_product_type_id"),
                "name": settings.get("default_product_type_name", "")
            },
            # More settings...
        }
        return True
    else:
        return False
```

### 9.2 Using Default Settings

```python
# Safe getter for default settings
def get_default_category_id():
    """
    Safely get category ID from session state.
    """
    if isinstance(st.session_state.user_settings, dict):
        # Direct access
        category_id = st.session_state.user_settings.get("default_category_id")
        # Check it's not "NULL" string or None
        if category_id and category_id != "NULL":
            return category_id
        # Alternative format
        if "category" in st.session_state.user_settings:
            return st.session_state.user_settings.get("category", {}).get("id")
    return None
```

### 9.3 Updating Default Settings

```python
# Prepare settings data for API
settings_data = {
    "default_category_id": st.session_state.selected_category_id,
    "default_product_type_id": st.session_state.selected_type_id,
    "default_quality_grade_id": st.session_state.selected_grade_id if has_quality_grades else None,
    "default_quantity_unit": selected_unit,
    "default_product_type_name": selected_type_obj.get("name", ""),
    "default_quality_grade_name": selected_grade_obj.get("name", "") if selected_grade_obj else "",
    "default_category_name": selected_category_obj.get("name", ""),
    "has_quality_grades": has_quality_grades
}

# Update settings via API
success, result = users_api.update_user_default_settings(settings_data)

# Update session state with new values
if success:
    st.session_state.user_settings = {
        "default_category_id": st.session_state.selected_category_id,
        "default_product_type_id": st.session_state.selected_type_id,
        # More settings...
    }
```

## 10. Data Model and Relationships

### 10.1 Key Entities

The producer module interacts with these main entities:

#### User
- Attributes: id, email, password, role, company_name, contact_name, is_active
- Default settings: default_category_id, default_product_type_id, default_quality_grade_id, default_quantity_unit

#### Product Category
- Attributes: id, name, description
- Relationships: contains many Product Types

#### Product Type
- Attributes: id, name, description, category_id, has_quality_grades
- Relationships: belongs to Category, contains many Quality Grades

#### Quality Grade
- Attributes: id, name, description, product_type_id
- Relationships: belongs to Product Type

#### Offer
- Attributes: id, user_id, product_type_id, quality_grade_id, quantity_in_kg, price_per_kg, delivery_date, status
- Status workflow: CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
- Relationships: belongs to User, Product Type, Quality Grade

### 10.2 Entity Relationships

```
User (1) --- (*) Offer
ProductCategory (1) --- (*) ProductType
ProductType (1) --- (*) QualityGrade
ProductType (1) --- (*) Offer
QualityGrade (1) --- (*) Offer
```

## 11. State Synchronization Patterns

The producer module implements several patterns to synchronize state between different UI components and with the backend:

### 11.1 Form-External Selection Pattern

For cascading selections outside of Streamlit forms:

```python
# Select outside the form
temp_category = st.selectbox(
    "Termékkategória *",
    options=category_options
)

# Update session state when changed
if temp_category != st.session_state.get("category_select", ""):
    st.session_state.category_select = temp_category
    product_types, error = update_types_by_category(temp_category, categories)
    st.session_state.product_types = product_types
```

### 11.2 Page Rerun Pattern

For updating the UI after state changes:

```python
# Check if rerun is needed
if st.session_state.needs_rerun:
    st.session_state.needs_rerun = False
    st.rerun()

# Set rerun flag after successful operation
if success:
    show_success("Ajánlat sikeresen elfogadva!")
    st.session_state.needs_rerun = True
```

### 11.3 Callback Pattern

For handling selection changes dynamically:

```python
def update_types_by_category(selected_category, categories):
    if not selected_category:
        return [], None
    
    category = next((cat for cat in categories if cat["name"] == selected_category), None)
    if not category:
        return [], None
    
    st.session_state.selected_category_id = category["id"]
    success, types = products_api.get_product_types(category_id=category["id"])
    
    if success:
        return types, None
    else:
        return [], f"Hiba a típusok betöltésekor: {types}"
```

## 12. Example Code Templates

### 12.1 New Producer Page Template

```python
"""
Producer page description.
"""
import streamlit as st
import datetime
from api import module_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
import app_config as config

def show_producer_page():
    """
    Producer page display function.
    """
    st.title("Page Title")
    
    # Authentication check
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Producer role check
    user = get_current_user()
    if not user or user.get("role", "").lower() != "termelő":
        show_error("Ehhez a funkcióhoz termelői jogosultság szükséges.")
        return
    
    # Page content
    success, items = module_api.get_items()
    
    if success:
        # Display data
        # ...
    else:
        show_error(f"Hiba az adatok lekérésekor: {items}")
    
    # Create/Edit form
    with st.form("item_form"):
        # Form fields
        # ...
        
        submit = st.form_submit_button("Mentés", type="primary")
    
    if submit:
        # Validation
        # ...
        
        # API action
        # ...
        
        # Feedback
        # ...

# Direct execution
if __name__ == "__main__":
    st.set_page_config(
        page_title=f"Page Title - {config.APP_NAME}",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    render_sidebar()
    show_producer_page()
```

### 12.2 Cascading Selection Template

```python
# Initialize session state
if "selected_category_id" not in st.session_state:
    st.session_state.selected_category_id = None
if "selected_type_id" not in st.session_state:
    st.session_state.selected_type_id = None

# Get categories
success, categories = products_api.get_product_categories()
if not success:
    show_error(f"Hiba a termékkategóriák lekérésekor: {categories}")
    return

# Category options with empty default
category_options = [{"id": None, "name": "-- Válasszon kategóriát --"}] + categories

# Category selection
selected_category = st.selectbox(
    "Termékkategória",
    options=[c.get("id") for c in category_options],
    format_func=lambda x: next((c["name"] for c in category_options if c["id"] == x), ""),
    key="category_select"
)

# Update state and fetch dependent data
if selected_category != st.session_state.selected_category_id:
    st.session_state.selected_category_id = selected_category
    st.session_state.selected_type_id = None  # Reset dependent selection
    
    # Fetch types for selected category
    product_types = []
    if selected_category:
        success, types_result = products_api.get_product_types(category_id=selected_category)
        if success:
            product_types = types_result
        else:
            show_error(f"Hiba a terméktípusok lekérésekor: {types_result}")
    
    # Store in session state
    st.session_state.product_types = product_types

# Type options with empty default
type_options = [{"id": None, "name": "-- Válasszon terméktípust --"}]
if hasattr(st.session_state, "product_types"):
    type_options += st.session_state.product_types

# Type selection
selected_type = st.selectbox(
    "Terméktípus",
    options=[t.get("id") for t in type_options],
    format_func=lambda x: next((t["name"] for t in type_options if t["id"] == x), ""),
    key="type_select",
    disabled=not selected_category
)

# Update state
if selected_type != st.session_state.selected_type_id:
    st.session_state.selected_type_id = selected_type
```

## 13. Summary

The producer module of the Streamlit application follows a well-structured architecture with:

1. **Modular Organization**: Clear separation of concerns between producer functions
2. **Consistent Patterns**: Authentication, API integration, and UI patterns
3. **Session State Management**: Comprehensive management of user settings and UI state
4. **Cascading Selection**: Intelligent handling of related dropdown selections
5. **Responsive UI**: User-friendly interfaces that adapt to different screen sizes
6. **Error Handling**: Comprehensive error checking and user feedback
7. **Dual Navigation**: Support for both nested and flat navigation structures

This architecture enables efficient development of new producer features while maintaining consistency across the application. The code demonstrates good practices in handling agricultural product data, managing user settings, and providing a responsive user interface.
