# Streamlit Offer Management Application: Visual Elements and Behaviors

## Overview

This document details the visual elements and interactive behaviors of the Streamlit-based offer management application. It explains how the UI components appear to users, their intended functionality, and the interactions they support.

## Main Views and Pages

### 1. Offer List View

![Offer List View](https://via.placeholder.com/800x400?text=Offer+List+View)

#### Visual Elements

The Offer List View is the main entry point of the application, displaying a table of offers with filtering options.

**Header Section:**
- Page title ("Ajánlatok kezelése") with responsive sizing
- Action buttons (including "Új ajánlat" button) in the top-right corner

**Filter Panel:**
- Filter controls organized in a responsive grid
- Producer filter: Dropdown for selecting specific producers
- Status filter: Dropdown showing offer statuses with color coding
- Date range filter: Date pickers with preset options
- Search button: Prominent primary button with search icon
- Reset button: Secondary button to clear all filters

**Active Filters Indicator:**
- Pills/tags showing currently active filters
- Located below the filter panel
- Each pill displays filter type and selected value
- Visual distinction with background color and rounded corners

**Offers Table:**
- Responsive data table with columns for:
  - Offer ID
  - Status (with color-coded indicators)
  - Product name
  - Producer name
  - Quantity
  - Delivery date
- Sortable columns with sort indicators
- Row highlighting on hover
- Pagination controls below the table

**Mobile-Optimized View:**
- On mobile devices, the table transforms into a card layout
- Each offer displayed as a card with key information
- Action buttons adapted for touch interaction

#### Behaviors

**Filtering:**
- Real-time validation of filter inputs
- Updating the filter pills as selections change
- Executing search when the search button is clicked
- Preserving filter state between sessions

**Table Interaction:**
- Clicking a row selects it, highlighting the entire row
- Double-clicking a row navigates to the Offer Detail View
- Table pagination with next/previous page controls and page indicator
- Sorting by clicking on column headers

**Keyboard Navigation:**
- J/K keys for navigating up/down through the table
- Enter key to select the highlighted offer
- Number keys (1-9) for quick selection of the first offers
- Escape key to clear selection

**Responsive Adaptation:**
- Filter panel collapses to single column on mobile
- Table transforms to card view on mobile
- Action buttons reorganize based on available space

### 2. Offer Detail View

![Offer Detail View](https://via.placeholder.com/800x600?text=Offer+Detail+View)

#### Visual Elements

The Offer Detail View shows comprehensive information about a single offer with editing capabilities.

**Action Bar:**
- Sticky header with primary actions
- Back button on the left
- Status change dropdown in the center
- Edit, Export, and More Actions buttons on the right
- Visual indication of available actions based on permissions

**Offer Header:**
- Offer ID and title
- Large status indicator with color-coding
- Last updated timestamp

**Information Panels:**
Organized into cards with consistent styling:

**Basic Info Panel:**
- Two-column layout with labels and values
- Key information including producer, product, quantity, price
- Formatted values with appropriate units

**Timeline Panel:**
- Visual timeline showing offer status progression
- Date indicators for each status change
- Current status highlighted
- Future statuses shown with "planned" indicator

**Product Panel:**
- Detailed product information
- Specifications and quality parameters
- Quantity and price metrics with units
- Visual indicators for special attributes

**Related Entities Panel:**
- Tabs for Producer, Product Details, and Related Offers
- Entity cards with consistent formatting
- Contact information for the producer
- Linked offers with status indicators

**Activities Panel:**
- Tabs for Status History, Attachments, and Audit Log
- Chronological list of status changes with user info
- Attachment list with download options
- Audit trail of all modifications to the offer

**Mobile-Optimized View:**
- Panels stack vertically instead of using columns
- Responsive content sizing
- Touch-optimized controls
- Simplified information hierarchy

#### Behaviors

**Navigation:**
- Back button returns to the offer list
- Bread crumbs for navigating the hierarchy
- Preserving state when moving between views

**Status Management:**
- Status dropdown shows only valid next statuses based on current status
- Status change triggers a confirmation dialog
- Success feedback after status change
- Animation of the timeline when status changes

**Editing:**
- Edit button toggles edit mode
- Form validation with immediate feedback
- Save and cancel buttons appear in edit mode
- Confirmation dialog on unsaved changes

**Document Handling:**
- Uploading attachments with progress indicator
- Downloading attachments with browser download dialog
- Preview of compatible attachment types

**Responsive Interactions:**
- Collapsible panels for better space management
- Swipe gestures on mobile for navigating between panels
- Double-tap to zoom on detailed information
- Long press for context menus on mobile

### 3. Offer Edit Form

![Offer Edit Form](https://via.placeholder.com/800x500?text=Offer+Edit+Form)

#### Visual Elements

The Edit Form appears when creating a new offer or editing an existing one.

**Form Layout:**
- Organized in logical sections with headers
- Required fields marked with an asterisk
- Contextual help icons with tooltips
- Validation state indicators

**Input Controls:**
- Text inputs with appropriate validation
- Numeric inputs with units and formatting
- Date pickers with calendar popup
- Dropdowns for selection from fixed options
- Rich text editor for notes

**Action Buttons:**
- Primary save button
- Secondary cancel button
- Danger-styled delete button (for existing offers)
- Positions adapted for desktop and mobile

**Validation Feedback:**
- Inline error messages below affected fields
- Color-coded borders on input fields
- Summary of all errors at the top of the form
- Warning indicators for unusual but valid values

#### Behaviors

**Form Interaction:**
- Real-time validation as users type
- Tab navigation between fields
- Autofocus on first field
- Auto-complete for appropriate fields

**Data Validation:**
- Client-side validation before submission
- Specific error messages for each validation rule
- Format validation for specialized fields
- Cross-field validation for related values

**Submission Handling:**
- Preventing double submission
- Progress indicator during submission
- Success message after successful submission
- Error handling with recovery options

**Mobile Optimization:**
- Larger touch targets for mobile users
- Simplified layout for small screens
- Keyboard optimizations for mobile input
- Scroll position management

## Common UI Patterns and Components

### 1. Status Indicators

Status indicators appear consistently throughout the application to show the current state of offers.

**Visual Appearance:**
- Color-coded badges (green for approved, yellow for pending, red for rejected, etc.)
- Text label with the status name
- Optional icon representing the status
- Size variants for different contexts

**Behavior:**
- Hover reveals additional status details
- Click opens status history in some contexts
- Consistent colors across the application

### 2. Action Buttons

Action buttons provide consistent interaction points for common operations.

**Visual Appearance:**
- Primary buttons: Blue background, white text
- Secondary buttons: Light background, dark text
- Danger buttons: Red background for destructive actions
- Icon+text combination for clarity
- Size variants (small, medium, large)

**Behavior:**
- Hover effect with slight color change
- Click feedback with animation
- Disabled state for unavailable actions
- Loading state during async operations

### 3. Confirmation Dialogs

Confirmation dialogs prevent accidental actions by requiring explicit confirmation.

**Visual Appearance:**
- Modal overlay with focus trapping
- Title clearly stating the action
- Description of the consequences
- Action buttons with clear labeling
- Warning icon for destructive actions

**Behavior:**
- Appears centered on screen
- Blocks interaction with background content
- Close with confirmation button, cancel button, or ESC key
- Optional text input for destructive confirmations

### 4. Notification System

Notifications provide feedback about operations and system status.

**Visual Appearance:**
- Success notifications: Green background
- Error notifications: Red background
- Info notifications: Blue background
- Warning notifications: Yellow background
- Icon matching the notification type
- Text message with details

**Behavior:**
- Appears in the top-right corner
- Automatically dismisses after timeout
- Can be manually dismissed
- Stacks when multiple notifications occur
- Optional actions within notifications

### 5. Filter Controls

Filter controls allow users to refine the data they see.

**Visual Appearance:**
- Consistent styling for all filter types
- Clear labels and placeholders
- Visual indication of active filters
- Reset option for each filter

**Behavior:**
- Instant feedback as filters are changed
- Typeahead/autocomplete for text filters
- Dependent filters update based on other selections
- Preserving filter state between sessions

### 6. Data Tables

Data tables present structured information in a consistent format.

**Visual Appearance:**
- Header row with column names
- Alternating row colors for readability
- Row highlighting on hover
- Sort indicators in column headers
- Pagination controls at the bottom

**Behavior:**
- Clicking column headers sorts the table
- Row selection via checkbox or row click
- Pagination with page size options
- Column resizing via drag handles
- Bulk actions on selected rows

## Responsive Design Approach

The application follows a responsive design approach to ensure usability across different devices.

### Desktop View (>= 992px)

- Multi-column layouts for efficient space usage
- Advanced data visualization
- Keyboard shortcuts for power users
- Hover states for additional information
- Side-by-side comparison capabilities

### Tablet View (768px - 991px)

- Reduced column counts in layouts
- Touch-optimized control sizes
- Collapsible sections for less-critical information
- Simplified visualizations
- Maintained tabular data views

### Mobile View (< 768px)

- Single column layouts
- Card-based views instead of tables
- Larger touch targets
- Progressive disclosure of information
- Bottom navigation for critical actions

## Interactive Patterns

### 1. Keyboard Shortcuts

The application supports keyboard shortcuts for efficiency:

| Shortcut | Action |
|----------|--------|
| J/K | Navigate down/up in lists |
| N/P | Next/Previous page |
| F | Focus search field |
| 1-9 | Select item by number |
| Esc | Back or cancel |
| Alt+S | Save in edit mode |
| Alt+E | Enter edit mode |
| Alt+R | Refresh current view |

### 2. Drag and Drop

Supported in specific contexts:

- Reordering items in lists
- Uploading files by dragging to drop zones
- Moving items between categories
- Adjusting dates in the timeline

### 3. Contextual Menus

Right-click (or long press on mobile) reveals contextual actions:

- Quick actions relevant to the selected item
- Export options
- Share functionality
- Additional operations not in the primary UI

### 4. Progressive Disclosure

Information is revealed progressively to reduce complexity:

- Expandable sections for detailed information
- "Show more" options for lengthy content
- Drill-down navigation for hierarchical data
- Tooltips for additional context

## Accessibility Considerations

The application includes accessibility features:

- Semantic HTML structure
- ARIA attributes for custom components
- Keyboard navigability
- Sufficient color contrast
- Screen reader compatibility
- Focus management
- Text scaling support

## Visual Styling

### Color Palette

- **Primary Color**: #3584e4 (Blue)
- **Secondary Color**: #33a02c (Green)
- **Accent Color**: #e66100 (Orange)
- **Neutral Colors**: #f0f2f6, #e6e6e6, #6c757d
- **Status Colors**:
  - Success: #28a745
  - Warning: #ffc107
  - Danger: #dc3545
  - Info: #17a2b8

### Typography

- **Primary Font**: System font stack for optimal performance
- **Headings**: Bold weight, slightly reduced line height
- **Body Text**: Regular weight, comfortable line height
- **Font Sizes**: Responsive scaling based on viewport
- **Hierarchy**: Clear visual hierarchy through size and weight

### Visual Styling Elements

- **Cards**: Subtle shadows, rounded corners
- **Buttons**: Clear visual hierarchy with color and sizing
- **Icons**: Consistent icon set throughout
- **Spacing**: Systematic spacing scale
- **Dividers**: Subtle lines for content separation

## Animation and Transitions

The application uses subtle animations for enhanced user experience:

- Page transitions for navigation
- Loading indicators for asynchronous operations
- Expand/collapse animations for toggleable content
- Status change transitions in the timeline
- Notification entrance and exit animations

## Implementation Notes

### Custom Components vs. Streamlit Components

The application uses a mix of native Streamlit components and custom components:

- Native Streamlit components for basic inputs and displays
- Custom components built with HTML/CSS/JS for advanced interactions
- JavaScript injections to enhance Streamlit's capabilities

### HTML/CSS Injection

The application extends Streamlit's capabilities through HTML/CSS injection:

```python
def render_status_indicator(status):
    """
    Renders a custom status indicator with color coding.
    """
    status_colors = {
        "CREATED": "#FFA07A",
        "CONFIRMED_BY_COMPANY": "#FFD700",
        "ACCEPTED_BY_USER": "#98FB98",
        # ...other statuses
    }
    color = status_colors.get(status, "#6c757d")
    
    html = f"""
    <div style="display: flex; align-items: center;">
        <div style="width: 15px; height: 15px; background-color: {color}; 
                    border-radius: 50%; margin-right: 10px;"></div>
        <div style="font-weight: bold;">{format_status(status)}</div>
    </div>
    """
    st.markdown(html, unsafe_allow_html=True)
```

### JavaScript Integration

JavaScript enhances interactive behaviors beyond Streamlit's capabilities:

```python
def inject_keyboard_shortcuts():
    """
    Adds keyboard shortcut support to the application.
    """
    shortcuts_js = """
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        document.addEventListener('keydown', function(e) {
            // Skip if focus is in an input
            if (document.activeElement.tagName === 'INPUT') return;
            
            // Navigation shortcuts
            if (e.key === 'j') navigateList(1);
            if (e.key === 'k') navigateList(-1);
            // ...other shortcuts
        });
    });
    </script>
    """
    st.markdown(shortcuts_js, unsafe_allow_html=True)
```

## User Experience Flows

### 1. New Offer Creation Flow

1. User clicks "New Offer" button
2. Empty form appears with default values
3. User fills required fields
4. Validation occurs as user completes fields
5. User submits the form
6. System validates and creates the offer
7. Success notification appears
8. User is redirected to the offer detail view

### 2. Offer Approval Flow

1. User views offer details
2. User selects "Approve" from status dropdown
3. Confirmation dialog appears
4. User optionally adds approval notes
5. User confirms the action
6. System updates offer status
7. Timeline animates to show new status
8. Success notification appears

### 3. Bulk Update Flow

1. User selects multiple offers via checkboxes
2. Bulk action menu becomes available
3. User selects an action (e.g., "Update Status")
4. Confirmation dialog shows affected items
5. User provides required parameters
6. System processes batch update
7. Progress indicator shows completion
8. Summary notification shows results

## Conclusion

This document details the visual elements and interactive behaviors of the Streamlit-based offer management application. The UI design follows consistent patterns and responsive principles to ensure usability across devices while providing rich functionality for managing agricultural product offers.

The application uses a combination of native Streamlit components and custom enhancements to deliver a cohesive and efficient user experience. All visual elements and behaviors are designed to support the core workflows of offer management, from creation through approval, editing, and export.
