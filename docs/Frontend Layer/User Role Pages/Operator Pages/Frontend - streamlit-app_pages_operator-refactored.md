# Streamlit Application Architecture Documentation

## Overview

This document outlines the architecture and code organization of a Streamlit-based web application focused on offer management within an agricultural product procurement system. The application follows a modular design pattern with clear separation of concerns and is built primarily using the Streamlit framework for the frontend interface.

### Technology Stack

- **Frontend Framework**: Streamlit
- **Language**: Python
- **State Management**: Streamlit Session State
- **UI Components**: Custom Streamlit components and HTML/CSS/JavaScript injections
- **Data Handling**: Pandas for data manipulation

### Application Purpose

The application serves as an operator interface for managing offers in an agricultural product procurement system, allowing users to:

1. View, filter, and search offers
2. Review offer details
3. Process status transitions (approve, reject, etc.)
4. Edit offer information
5. Export offer data

## Directory Structure

The application follows a modular directory structure with clear separation of concerns:

```
streamlit_app/
├── pages/
│   └── operator/
│       └── offer_management/           # Main offer management module
│           ├── __init__.py             # Package initialization
│           ├── action_components.py    # UI components for actions/operations
│           ├── actions.py              # Business logic for actions
│           ├── api_client.py           # API communication layer
│           ├── api_helpers.py          # Helper functions for API calls
│           ├── data_processing.py      # Data transformation and processing
│           ├── detail_components.py    # UI components for displaying details
│           ├── enhanced_ui_components.py # Advanced UI components
│           ├── export_functions.py     # Export functionality (CSV, Excel)
│           ├── fixed_date_filter.py    # Date filter components
│           ├── offer_detail.py         # Offer detail page logic
│           ├── offer_detail_wrapper.py # Wrapper around offer detail
│           ├── responsive_ui.py        # Responsive design utilities
│           ├── state_management.py     # Session state management
│           ├── ui_components.py        # Base UI components
│           ├── utils.py                # Utility functions
│           ├── validation.py           # Input validation
│           └── visual_feedback.py      # Visual feedback components
├── components/                         # Common components
│   ├── notification.py                 # Notification components
│   └── data_display.py                 # Data display components
└── utils/                              # Shared utilities
    ├── formatting.py                   # Formatting utilities
    ├── api_client.py                   # Common API utilities
    └── responsive_ui.py                # Common responsive design utilities
```

## Architectural Layers

### 1. Presentation Layer

#### UI Components (`ui_components.py`, `enhanced_ui_components.py`, `detail_components.py`, `action_components.py`)

This layer is responsible for rendering the UI elements and handling user interactions. It leverages Streamlit's component system but extends it with custom components.

**Key Patterns:**
- Component-based design
- Composition pattern for complex UI elements
- Responsive design adapting to mobile/tablet/desktop
- JavaScript injection for enhanced interactivity

**Example Component:**

```python
class ActionBar:
    """
    Egységes műveleti sáv komponens.
    
    A komponens megjeleníti a legfontosabb műveleteket egy sticky fejlécben.
    """
    
    def __init__(self, offer_id, offer_status=None, permissions=None):
        self.offer_id = offer_id
        self.offer_status = offer_status
        self.permissions = permissions or {}
        self.key_prefix = f"action_bar_{offer_id}_{str(uuid.uuid4())[:6]}"
    
    def render(self, on_back=None, on_status_change=None, on_edit=None, on_export=None, on_more_actions=None):
        # Component rendering logic
```

**Component Interface Pattern:**
Most UI components follow this pattern:
1. Constructor that accepts data and configuration
2. A `render()` method that displays the component
3. Optional helper methods prefixed with `_render_` or `_handle_` 
4. Callback parameters for handling user interactions

#### Pages (`offer_detail.py`)

Pages represent the top-level views in the application. They compose multiple components to create complete interfaces.

**Key Pattern:** Pages typically follow this structure:
1. Import necessary components and utilities
2. Define a main function (e.g., `show_offer_detail`)
3. Load required data
4. Set up the page layout
5. Render components in the appropriate containers

```python
def show_offer_detail(offer_id):
    """
    Ajánlat részleteinek megjelenítése.
    
    Args:
        offer_id (int): Az ajánlat azonosítója
    """
    # Load data
    offer = _load_offer_details(offer_id)
    
    # Handle status changes
    _handle_status_change(offer_id, offer['status'])
    
    # Render action bar
    _render_action_bar(offer_id, offer['status'])
    
    # Render content based on screen size
    if is_mobile:
        # Mobile layout
        _render_basic_info_panel(offer)
        _render_timeline_panel(offer)
        # ...
    else:
        # Desktop layout with columns
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            _render_basic_info_panel(offer)
            # ...
```

### 2. Business Logic Layer

#### Actions (`actions.py`)

This layer contains the business logic for handling user actions and operations on offers. It processes data, validates inputs, and coordinates with the API layer.

**Key Patterns:**
- Function-based design for operations
- Clear separation from UI logic
- Comprehensive error handling
- Status transition handling

**Example:**

```python
def handle_status_transitions(offer, offer_id):
    """
    Ajánlat státusz átmenetek és műveletek kezelése.
    
    Args:
        offer (dict): Az ajánlat adatai
        offer_id (int): Az ajánlat azonosítója
    """
    # Logic for handling status transitions based on current status
```

#### Data Processing (`data_processing.py`)

This layer handles data transformation, filtering, and preparation for display.

**Key Patterns:**
- Pure functions for data transformation
- Filter chain pattern for data processing
- Export functionality

### 3. Data Access Layer

#### API Client (`api_client.py`)

This layer is responsible for communication with the backend services. It handles:
- HTTP requests
- Error handling
- Response parsing
- Authentication

**Key Pattern:** The API client follows a consistent pattern for all endpoints:
1. Function name indicates the operation (e.g., `get_offer`, `update_offer_status`)
2. Returns a tuple of (success, result)
3. Comprehensive error handling and logging
4. Consistent parameter naming

```python
def update_offer_status(offer_id, status, note=None):
    """
    Ajánlat státuszának frissítése az API-n keresztül.
    
    Args:
        offer_id (int/str): Az ajánlat azonosítója
        status (str): Az új státusz
        note (str, optional): Megjegyzés a státuszváltoztatáshoz. Defaults to None.
        
    Returns:
        tuple: (success, result) ahol success egy boolean érték, result a frissített ajánlat vagy hibaüzenet
    """
    try:
        logger.info(f"Updating offer status for ID: {offer_id} to: {status} with note: {note}")
        return safe_api_call(offers_api.update_offer_status, "ajánlat státuszának frissítése", offer_id, status, note)
    except Exception as e:
        logger.error(f"Error updating offer status: {str(e)}")
        return False, f"Hiba az ajánlat státuszának frissítése során: {str(e)}"
```

#### API Helpers (`api_helpers.py`)

Contains helper functions for working with the API, including:
- Retry logic
- Response formatting
- Cache management
- Progress indicators for API calls

### 4. State Management Layer

#### Session State Management (`state_management.py`)

This layer handles the application state using Streamlit's session state. It provides:
- State initialization
- Cache management
- Lazy loading of data
- Keyboard shortcut handling

**Key Patterns:**
- Singleton pattern for session state
- Cache invalidation strategies
- Session state namespacing with prefixes

```python
def lazy_load_cache(cache_key, data_loader_func, cache_ttl=300):
    """
    Általános lusta betöltés cache használatával.
    
    Args:
        cache_key (str): Cache kulcs a betöltött adathoz
        data_loader_func (callable): Függvény, ami betölti az adatokat
        cache_ttl (int, optional): Cache időtartama másodpercben. Defaults to 300.
        
    Returns:
        tuple: (success, data)
    """
    # Cache logic implementation
```

### 5. Utility Layer

#### Utilities (`utils.py`, `validation.py`)

These modules provide common utilities used across the application:
- Data validation
- Formatting functions
- Helper functions
- Shared constants

**Key Pattern:** Utilities are organized by functionality and follow a functional programming approach.

```python
def validate_offer_data(offer_data, is_new=True):
    """
    Ajánlat adatok kliens oldali validálása beküldés előtt.
    
    Args:
        offer_data (dict): Az ellenőrizendő ajánlat adatok.
        is_new (bool, optional): Új ajánlat létrehozása vagy meglévő módosítása. Defaults to True.
    
    Returns:
        tuple: (érvényes, hibák, figyelmeztetések)
    """
    # Validation logic
```

## Key Design Patterns

### 1. Component Pattern

UI elements are encapsulated as reusable components with a consistent interface. Components:
- Have a constructor that accepts configuration
- Provide a `render()` method for displaying the component
- Are self-contained with their own state management
- Accept callbacks for user interactions

### 2. Factory Pattern

Used for creating UI components based on data types or user preferences:

```python
def render_section_card(title, content, color="#3584e4", icon=None, is_mobile=False, key=None, expanded=True):
    """Factory function that creates and renders a section card"""
```

### 3. Adapter Pattern

Used to make the API client work with different types of APIs and handle inconsistencies:

```python
def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """
    Biztonságos API hívás végrehajtása egységes hibakezeléssel.
    
    Args:
        api_function (callable): Az API funkció, amelyet meg kell hívni.
        error_operation_name (str): A művelet neve hibaüzenetekhez.
        *args: További pozíciós argumentumok az API funkcióhoz.
        **kwargs: További kulcsszó argumentumok az API funkcióhoz.
    """
```

### 4. Observer Pattern

Implemented through Streamlit's callback system to handle user interactions:

```python
def on_status_change(new_status):
    """Callback function when status changes"""
    # Handle the status change
```

### 5. Strategy Pattern

Used for adapting the UI based on device type:

```python
def display_offer_table_with_actions(offers, pagination=True):
    """
    Ajánlatok táblázatos megjelenítése műveletgombokkal.
    
    Args:
        offers (list): Az ajánlatok listája
        pagination (bool, optional): Legyen-e lapozás. Defaults to True.
    """
    # Display logic that adapts based on device type
```

## Data Flow

### 1. Data Loading Flow

1. User navigates to a page or applies filters
2. UI components trigger data loading
3. API client makes requests to the backend
4. Data is processed and transformed
5. UI components render the data

Example:
```python
# 1. User action in UI component
if st.button("🔍 Keresés", key=f"search_button_{page_id}"):
    # 2. Prepare filter parameters
    params = prepare_filter_params(selected_producer_id, selected_status, from_date, to_date)
    
    # 3. API request through data processing
    offers = load_offers_with_filters(params)
    
    # 4. Render results
    display_offer_table_with_actions(offers)
```

### 2. Status Transition Flow

1. User initiates a status change
2. Confirmation dialog is displayed
3. On confirmation, API request is made
4. Success/error feedback is shown
5. UI is updated to reflect the new status

### 3. Form Submission Flow

1. User fills a form
2. Client-side validation is performed
3. If valid, data is sent to the API
4. Success/error feedback is shown
5. UI is updated with the new data

## Error Handling Strategy

The application employs a comprehensive error handling strategy:

1. **Client-side validation** to prevent invalid inputs
2. **API error handling** with meaningful messages
3. **Fallback strategies** for missing functionality
4. **Graceful degradation** for unsupported features
5. **Visual feedback** for all errors

Example:
```python
def validate_offer_data(offer_data, is_new=True):
    """Client-side validation"""
    # Validation logic

def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """API error handling wrapper"""
    try:
        success, result = api_function(*args, **kwargs)
        if not success:
            handle_api_error(result, error_operation_name)
        return success, result
    except Exception as e:
        handle_api_error(e, error_operation_name)
        return False, str(e)

def show_inline_error(error_message):
    """Visual error feedback"""
    # Error display implementation
```

## Responsive Design Implementation

The application adapts to different screen sizes:

1. **Detection**: JavaScript detects screen size and reports to Streamlit
2. **Adaptation**: Different layouts for mobile, tablet, and desktop
3. **Component Adjustment**: Components adapt their rendering based on screen size
4. **Fallback**: Desktop view is always available as a fallback

```python
def inject_screen_detection():
    """
    Képernyőméret érzékelés a session state-be.
    """
    # JavaScript to detect screen size
```

## Import System and Dependency Management

The application uses a flexible import system to handle different deployment scenarios:

```python
try:
    # Try absolute import first
    from streamlit_app.utils.formatting import format_status
except ImportError:
    try:
        # Try regular app-relative import
        from utils.formatting import format_status
    except ImportError:
        # Fallback formatting function if import fails
        logging.warning("Could not import format_status function, using fallback")
        format_status = lambda x: x if x else "Ismeretlen"
```

This pattern:
1. Tries multiple import paths
2. Provides fallbacks for missing imports
3. Logs warnings when using fallbacks
4. Ensures the application works in different environments

## Integration with Streamlit Framework

The application deeply integrates with Streamlit's features:

1. **Session State**: Used for persistent data across reruns
2. **Callbacks**: For handling user interactions
3. **Component System**: Extended with custom components
4. **Layout System**: Used for responsive design
5. **Caching**: For performance optimization

```python
# Session state for persistent data
if "selected_offer_id" not in st.session_state:
    st.session_state.selected_offer_id = None

# Layout system for responsive design
col1, col2, col3 = st.columns([1, 1, 1])
with col1:
    # Render content for first column

# Custom components with callbacks
action_bar = ActionBar(offer_id, status, permissions)
action_bar.render(
    on_status_change=lambda status: update_offer_status(offer_id, status),
    on_edit=lambda: set_edit_mode(offer_id)
)
```

## JavaScript Integration

The application enhances Streamlit's capabilities with custom JavaScript:

1. **UI Enhancements**: For advanced interactive elements
2. **Keyboard Shortcuts**: For power users
3. **Custom Styling**: Beyond Streamlit's capabilities
4. **Client-side Interactions**: For responsive feedback

```python
# JavaScript injection for keyboard shortcuts
def inject_keyboard_shortcuts():
    shortcuts_js = """
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        document.addEventListener('keydown', function(e) {
            // Keyboard shortcuts implementation
        });
    });
    </script>
    """
    st.markdown(shortcuts_js, unsafe_allow_html=True)
```

## Conclusion

This Streamlit application follows a modular, component-based architecture with clear separation of concerns. It employs various design patterns to ensure maintainability, extensibility, and robustness. The application handles responsive design, error management, and state management with sophisticated strategies that extend Streamlit's core capabilities.

The application architecture balances simplicity with advanced features, creating a maintainable codebase that can evolve over time. The modular approach allows for easy extension and replacement of components as requirements change.
