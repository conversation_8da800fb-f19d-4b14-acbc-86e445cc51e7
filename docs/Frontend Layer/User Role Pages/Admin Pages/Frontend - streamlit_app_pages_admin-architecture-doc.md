# Frontend - streamlit_app_pages_ADMIN Architecture

## 1. Overview

This document describes the architecture and code organization of the administrative module within the Streamlit-based frontend application. The admin pages are a key part of a client-server architecture designed for managing agricultural product offers between producers and a central company.

The admin module provides interfaces for system administrators to manage users, products, and system settings. It follows a modular design pattern with clear separation of concerns, consistent API usage, and standardized UI components.

## 2. Directory Structure

```
streamlit_app/pages/
├── admin/                           # Admin module directory
│   ├── __init__.py                  # Package initialization
│   ├── dashboard.py                 # Admin dashboard
│   ├── data_generator.py            # Test data generator
│   ├── product_management.py        # Product entities management
│   ├── products.py                  # Simpler product management interface
│   ├── sitemap.py                   # Navigation testing and visualization
│   ├── user_management.py           # Detailed user management 
│   └── users.py                     # User management interface
├── admin_dashboard.py               # Redirection file (flat structure)
├── admin_data_generator.py          # Redirection file (flat structure)
├── admin_product_management.py      # Redirection file (flat structure)
├── admin_products.py                # Redirection file (flat structure)
├── admin_sitemap.py                 # Redirection file (flat structure)
├── admin_user_management.py         # Redirection file (flat structure)
└── admin_users.py                   # Redirection file (flat structure)
```

## 3. File Structure Pattern

Each admin page follows a consistent file structure pattern:

1. **Module Documentation**: Docstring describing the module purpose
2. **Imports**: Organized by category (Streamlit, standard library, application modules)
3. **Main Page Function**: Primary function that renders the page
4. **Supporting Functions**: Helper functions for specific UI sections
5. **Direct Execution Block**: Code executed when the file runs directly

Example pattern from `admin/dashboard.py`:

```python
# Adminisztrátori irányítópult
"""
Adminisztrátori irányítópult oldal.
"""
import streamlit as st  # Streamlit imports
import datetime         # Standard library imports
import pandas as pd
from api import offers as offers_api  # Application imports
from api import users as users_api
# ... more imports ...

def show_admin_dashboard():
    """
    Adminisztrátori irányítópult megjelenítése.
    """
    # Page implementation...

def load_settings_to_session_state():
    """
    Supporting function implementation...
    """
    # Implementation...

# Direct execution block
if __name__ == "__main__":
    # Page config and setup
    st.set_page_config(
        page_title=get_page_title("Adminisztrátori Irányítópult"),
        page_icon="🏠",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Sidebar rendering
    render_sidebar()
    
    # Display the page
    show_admin_dashboard()
```

## 4. Core Design Patterns

### 4.1 Page Rendering Pattern

All admin pages follow the same page rendering pattern:

1. **Authentication Check**: Verify user is logged in and has admin permissions
2. **Page Setup**: Set title, layout, and other page properties
3. **Content Rendering**: Display page content based on the specific page function
4. **Action Handling**: Process user input and update the system as needed

```python
def show_admin_products():
    """
    Admin termékkezelő oldal megjelenítése.
    """
    st.title("Termékek kezelése")
    
    # Authentication check
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Admin role check
    user = get_current_user()
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Page content
    # ...content rendering code...
    
    # Action handling
    # ...action handling code...
```

### 4.2 API Integration Pattern

Admin pages interact with the backend through a consistent API integration pattern:

1. **API Call**: Make the API request through the appropriate module
2. **Success/Error Handling**: Check the result of the API call
3. **State Update**: Update the UI state based on the API response
4. **Feedback**: Provide feedback to the user about the operation's success or failure

```python
# Making an API call
success, result = users_api.get_users()

# Success/Error handling
if success:
    users = result
    # Process the users...
else:
    show_error(f"Hiba a felhasználók lekérésekor: {result}")
    return

# State update
if submit_button:
    success, result = products_api.create_product_category(category_data)
    
    # Feedback
    if success:
        show_success("Kategória sikeresen létrehozva!")
        st.rerun()  # UI refresh
    else:
        show_error(f"Hiba a kategória létrehozásakor: {result}")
```

### 4.3 Form Processing Pattern

Admin pages use a consistent form processing pattern:

1. **Form Creation**: Create a form using Streamlit's form API
2. **Input Collection**: Collect user input through form fields
3. **Validation**: Validate the input when the form is submitted
4. **API Action**: Perform the appropriate API action with the validated data
5. **Feedback**: Provide user feedback and update the UI state

```python
# Form creation
with st.form("new_category_form"):
    # Input collection
    category_name = st.text_input("Kategória neve *", placeholder="pl. Paprika")
    category_description = st.text_area("Leírás", placeholder="Kategória leírása", max_chars=500)
    
    # Submit button
    submit = st.form_submit_button("Kategória létrehozása", type="primary", use_container_width=True)

# Form submission handling
if submit:
    # Validation
    validation_errors = []
    is_valid, error = validate_required(category_name, "Kategória neve")
    if not is_valid:
        validation_errors.append(error)
    
    # Display validation errors
    if validation_errors:
        for error in validation_errors:
            show_error(error)
    else:
        # API action
        category_data = {
            "name": category_name,
            "description": category_description if category_description else None
        }
        success, result = products_api.create_product_category(category_data)
        
        # Feedback
        if success:
            show_success("Termékkategória sikeresen létrehozva!")
            st.rerun()
        else:
            show_error(f"Hiba a termékkategória létrehozásakor: {result}")
```

### 4.4 Data Display Pattern

Admin pages display data in a consistent manner:

1. **Data Preparation**: Format and transform API data for display
2. **Column Configuration**: Define column display properties
3. **Display Options**: Set display options like width, index visibility, etc.
4. **Interactive Elements**: Add selection and action elements for the displayed data

```python
# Data preparation
user_data = []
for u in users:
    user_data.append({
        "ID": u.get("id"),
        "Név": u.get("contact_name", ""),
        "Email": u.get("email", ""),
        "Cégnév": u.get("company_name", ""),
        "Szerepkör": format_role(u.get("role", "")),
        "Státusz": "Aktív" if u.get("is_active") else "Inaktív",
        "Regisztráció dátuma": format_datetime(u.get("created_at", ""))
    })

# Create DataFrame
df = pd.DataFrame(user_data)

# Display with column configuration and options
st.dataframe(
    df,
    use_container_width=True,
    column_config={
        "ID": st.column_config.NumberColumn("ID", format="%d"),
        "Név": st.column_config.TextColumn("Név"),
        "Email": st.column_config.TextColumn("Email"),
        "Cégnév": st.column_config.TextColumn("Cégnév"),
        "Szerepkör": st.column_config.TextColumn("Szerepkör"),
        "Státusz": st.column_config.TextColumn("Státusz"),
        "Regisztráció dátuma": st.column_config.TextColumn("Regisztráció dátuma")
    },
    hide_index=True
)

# Interactive selection for the displayed data
selected_user_id = st.selectbox(
    "Válasszon felhasználót a részletes kezeléshez:",
    options=[u.get("id") for u in users],
    format_func=lambda x: next((f"{u.get('contact_name')} ({u.get('email')})" for u in users if u.get("id") == x), "")
)
```

## 5. Admin Module Pages

### 5.1 Dashboard (`dashboard.py`)

The admin dashboard provides an overview of system statistics and status.

**Key Features:**
- System statistics (users, products, offers)
- Quick access to main admin functions
- System status monitoring
- Maintenance functions

**Data Flow:**
1. Fetch user statistics, product statistics, and offer statistics
2. Display statistics in a dashboard layout with cards
3. Provide quick action buttons for maintenance tasks

**UI Components:**
- Statistical metrics cards
- System status indicators
- Quick action buttons
- Responsive layout with columns

### 5.2 User Management (`users.py` and `user_management.py`)

The user management pages allow administrators to view, filter, create, edit, and delete users.

**Key Features:**
- User listing with filtering and sorting
- User creation form
- User role management
- User activation/deactivation
- User deletion

**Data Flow:**
1. Fetch all users from the API
2. Apply filtering and sorting
3. Display users in a table format
4. Enable actions like edit, activate/deactivate, and delete

**UI Components:**
- Filterable user table
- User creation form
- Role selection dropdown
- Action buttons for user management

### 5.3 Product Management (`products.py` and `product_management.py`)

The product management pages allow administrators to manage product categories, types, and quality grades.

**Key Features:**
- Three-level product hierarchy management:
  - Categories (e.g., "Paprika")
  - Types (e.g., "TV paprika")
  - Quality Grades (e.g., "Extra")
- Creation, editing, and deletion of each level
- Filtering and selection of related entities

**Data Flow:**
1. Fetch product categories, types, or grades as needed
2. Display in appropriate tables
3. Enable creation, editing, and deletion actions
4. Update the backend via API calls

**UI Components:**
- Tabbed interface for categories, types, and grades
- Data tables for each entity type
- Forms for creation and editing
- Cascading selection for related entities

### 5.4 Data Generator (`data_generator.py`)

A specialized admin tool for generating test data for the system.

**Key Features:**
- Bulk generation of users, products, and offers
- Creation of realistic test scenarios with seasonal and regional variations
- Configuration of data generation parameters
- Statistical analysis of generated data

**Data Flow:**
1. Configure data generation parameters
2. Generate data through API calls
3. Display progress and results
4. Optionally show statistical analysis of generated data

**UI Components:**
- Parameter configuration forms
- Progress indicators
- Statistical charts of generated data
- Tabbed interface for different generation scenarios

### 5.5 Sitemap (`sitemap.py`)

A tool for visualizing and testing the application's navigation structure.

**Key Features:**
- Complete list of application pages
- Navigation testing buttons
- Role-based page organization
- Alternate URL format testing

**Data Flow:**
1. Display pages organized by role
2. Provide navigation buttons
3. Test navigation using different URL formats

**UI Components:**
- Hierarchical page listing
- Navigation buttons
- Role-based sections

## 6. Common Implementation Patterns

### 6.1 Authentication and Authorization

All admin pages implement the same authentication and authorization pattern:

```python
# Authentication check
if not is_authenticated():
    show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
    st.switch_page("pages/auth_login.py")
    return

# Authorization check
user = get_current_user()
if user.get("role") != "admin":
    show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
    return
```

### 6.2 Error Handling

Error handling follows a consistent pattern across all admin pages:

```python
try:
    # API call
    success, result = api_module.api_function(params)
    
    if not success:
        show_error(f"Hiba a művelet során: {result}")
        return
except Exception as e:
    show_error(f"Váratlan hiba történt: {str(e)}")
    return
```

### 6.3 Confirmation Dialogs

Important actions require confirmation through a consistent pattern:

```python
if delete_button:
    # Megerősítés kérése
    if st.session_state.get(f"confirm_delete_type_{type_id}", False):
        # Confirmed - perform the action
        success, result = products_api.delete_product_type(type_id)
        
        if success:
            show_success("Terméktípus sikeresen törölve!")
            st.session_state[f"confirm_delete_type_{type_id}"] = False
            st.rerun()
        else:
            show_error(f"Hiba a terméktípus törlésekor: {result}")
            st.session_state[f"confirm_delete_type_{type_id}"] = False
    else:
        # Request confirmation
        st.warning(f"Biztosan törölni szeretné a '{selected_type_data.get('name')}' terméktípust? Ez a művelet nem vonható vissza!")
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("Igen, törlés", key=f"confirm_yes_type_{type_id}", type="primary", use_container_width=True):
                st.session_state[f"confirm_delete_type_{type_id}"] = True
                st.rerun()
        
        with col2:
            if st.button("Mégsem", key=f"confirm_no_type_{type_id}", use_container_width=True):
                st.session_state[f"confirm_delete_type_{type_id}"] = False
                st.rerun()
```

### 6.4 State Management

All admin pages use Streamlit's session state for persistent state management:

```python
# Initialize session state variable if it doesn't exist
if "confirm_delete_type_{type_id}" not in st.session_state:
    st.session_state[f"confirm_delete_type_{type_id}"] = False

# Update session state 
if button_clicked:
    st.session_state[f"confirm_delete_type_{type_id}"] = True
    
# Read session state
is_confirmed = st.session_state.get(f"confirm_delete_type_{type_id}", False)
```

### 6.5 Responsive Layout

Admin pages implement responsive layouts using Streamlit's column system:

```python
# Two-column layout
col1, col2 = st.columns(2)

with col1:
    # Left column content
    st.subheader("Left Column")
    # ...more content...

with col2:
    # Right column content
    st.subheader("Right Column")
    # ...more content...

# Three-column layout for dashboard stats
stat_col1, stat_col2, stat_col3 = st.columns(3)

with stat_col1:
    st.metric("Total Users", user_count)

with stat_col2:
    st.metric("Total Products", product_count)
    
with stat_col3:
    st.metric("Total Offers", offer_count)
```

## 7. URL Structure and Navigation

The admin module supports two navigation patterns:

### 7.1 Nested Structure

The original structure uses nested modules:

```
pages/admin/dashboard.py -> accessed via "pages/admin/dashboard.py"
```

### 7.2 Flat Structure

The flat structure provides compatibility with older Streamlit versions:

```
pages/admin_dashboard.py -> redirects to "pages/admin/dashboard.py"
```

This redirection is implemented through simple import forwarding:

```python
# Content of pages/admin_dashboard.py
admin/dashboard.py
```

## 8. Integration with Backend API

### 8.1 API Module Usage

Admin pages use the API modules from the `api` package:

```python
from api import users as users_api
from api import products as products_api
from api import offers as offers_api
from api import auth as auth_api
```

### 8.2 API Response Handling Pattern

All API calls follow the same response handling pattern:

```python
success, result = api_module.api_function(params)

if success:
    # Handle successful response
    data = result
    # Process data...
else:
    # Handle error
    show_error(f"Error message: {result}")
```

## 9. Page Initialization Pattern

All admin pages use the same initialization pattern when run directly:

```python
if __name__ == "__main__":
    # Configure page
    st.set_page_config(
        page_title=f"Page Title - {config.APP_NAME}",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Render sidebar
    render_sidebar()
    
    # Show main content
    show_page_function()
```

## 10. Recommendations for LLM Context

When generating code for this application's admin module, consider:

1. **Authentication First**: Always start with authentication and authorization checks
2. **Consistent API Pattern**: Follow the `success, result = api_function()` pattern
3. **Form Structure**: Use Streamlit's form API with validation before submission
4. **Error Handling**: Provide clear error messages and fallbacks
5. **State Management**: Use session state for persistent UI state
6. **Responsive Design**: Structure layout with columns for different screen sizes
7. **Confirmation Flow**: Implement confirmation dialogs for destructive actions
8. **Naming Conventions**: Follow the established Hungarian naming conventions in UI text

## 11. Sample Code Templates

### 11.1 New Admin Page Template

```python
"""
Admin page description.
"""
import streamlit as st
import pandas as pd
from api import module_api
from components.sidebar import render_sidebar
from components.notification import show_info, show_error, show_success
from utils.session import is_authenticated, get_current_user
import app_config as config

def show_admin_page():
    """
    Admin page display function.
    """
    st.title("Page Title")
    
    # Authentication check
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
    
    # Authorization check
    user = get_current_user()
    if user.get("role") != "admin":
        show_error("Az oldal megtekintéséhez adminisztrátori jogosultság szükséges.")
        return
    
    # Page content
    success, items = module_api.get_items()
    
    if success:
        # Display data
        # ...
    else:
        show_error(f"Hiba az adatok lekérésekor: {items}")
    
    # Create/Edit form
    with st.form("item_form"):
        # Form fields
        # ...
        
        submit = st.form_submit_button("Mentés", type="primary")
    
    if submit:
        # Validation
        # ...
        
        # API action
        # ...
        
        # Feedback
        # ...

# Direct execution
if __name__ == "__main__":
    st.set_page_config(
        page_title=f"Page Title - {config.APP_NAME}",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    render_sidebar()
    show_admin_page()
```

### 11.2 CRUD Operation Template

```python
# Create operation
def create_item():
    with st.form("create_form"):
        # Form fields
        name = st.text_input("Name *")
        description = st.text_area("Description")
        
        submit = st.form_submit_button("Create", type="primary")
    
    if submit:
        # Validation
        if not name:
            show_error("Name is required")
            return
        
        # API call
        success, result = api_module.create_item({
            "name": name,
            "description": description
        })
        
        # Feedback
        if success:
            show_success("Item created successfully!")
            st.rerun()
        else:
            show_error(f"Error creating item: {result}")

# Update operation
def update_item(item_id, item_data):
    with st.form(f"update_form_{item_id}"):
        # Pre-filled form fields
        name = st.text_input("Name *", value=item_data.get("name", ""))
        description = st.text_area("Description", value=item_data.get("description", ""))
        
        col1, col2 = st.columns(2)
        with col1:
            update_button = st.form_submit_button("Update", type="primary")
        with col2:
            delete_button = st.form_submit_button("Delete", type="secondary")
    
    if update_button:
        # Validation
        if not name:
            show_error("Name is required")
            return
        
        # API call
        success, result = api_module.update_item(item_id, {
            "name": name,
            "description": description
        })
        
        # Feedback
        if success:
            show_success("Item updated successfully!")
            st.rerun()
        else:
            show_error(f"Error updating item: {result}")
    
    if delete_button:
        # Confirmation
        if st.session_state.get(f"confirm_delete_{item_id}", False):
            # API call
            success, result = api_module.delete_item(item_id)
            
            # Feedback
            if success:
                show_success("Item deleted successfully!")
                st.session_state[f"confirm_delete_{item_id}"] = False
                st.rerun()
            else:
                show_error(f"Error deleting item: {result}")
                st.session_state[f"confirm_delete_{item_id}"] = False
        else:
            st.warning("Are you sure you want to delete this item? This action cannot be undone.")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("Yes, delete", key=f"confirm_yes_{item_id}"):
                    st.session_state[f"confirm_delete_{item_id}"] = True
                    st.rerun()
            with col2:
                if st.button("Cancel", key=f"confirm_no_{item_id}"):
                    st.session_state[f"confirm_delete_{item_id}"] = False
                    st.rerun()
```

## 12. Summary

The admin module of the Streamlit application follows a well-structured architecture with:

1. **Modular Organization**: Clear separation of concerns between admin functions
2. **Consistent Patterns**: Authentication, API integration, and UI patterns
3. **Responsive UI**: User-friendly interfaces that adapt to different screen sizes
4. **Robust Error Handling**: Comprehensive error checking and user feedback
5. **Dual Navigation**: Support for both nested and flat navigation structures

This architecture enables efficient development of new admin features while maintaining consistency across the application.
