# Saved Filters API Documentation

## Overview

The Saved Filters API provides comprehensive functionality for managing user-specific filter configurations in the Agricultural Product Management System. This feature allows operators to save, load, and manage their frequently used filter configurations for efficient offer management.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Data Model](#data-model)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Authorization](#authentication--authorization)
5. [Request/Response Schemas](#requestresponse-schemas)
6. [Filter Data Structure](#filter-data-structure)
7. [Error Handling](#error-handling)
8. [Usage Examples](#usage-examples)
9. [Security Considerations](#security-considerations)

## Architecture Overview

The Saved Filters feature follows the established architectural patterns of the system:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │    │                 │
│   API Layer     │    │  Service Layer  │    │   CRUD Layer    │    │  Database       │
│   (FastAPI)     │◄──►│   (Business     │◄──►│   (Data Access) │◄──►│   (PostgreSQL)  │
│                 │    │    Logic)       │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
       │
       ▼
┌─────────────────┐
│                 │
│   Frontend      │
│   (Streamlit)   │
│                 │
└─────────────────┘
```

### Layer Responsibilities

- **API Layer**: HTTP endpoints, request validation, authentication
- **Service Layer**: Business logic, default filter management, data validation
- **CRUD Layer**: Database operations, query optimization
- **Database**: Data persistence, constraints, relationships

## Data Model

### UserSavedFilter Table

The `user_saved_filters` table stores all saved filter configurations:

| Column Name | Type | Constraints | Description |
|-------------|------|-------------|-------------|
| `id` | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| `user_id` | INTEGER | FOREIGN KEY (users.id), NOT NULL | Reference to the user |
| `name` | VARCHAR(100) | NOT NULL | Human-readable filter name |
| `description` | VARCHAR(255) | NULLABLE | Optional filter description |
| `filter_type` | VARCHAR(50) | NOT NULL | Type of filter (e.g., "offer") |
| `is_default` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether this is the default filter |
| `filter_data` | JSON | NOT NULL | Filter configuration data |
| `created_at` | TIMESTAMP | NOT NULL, AUTO | Record creation timestamp |
| `updated_at` | TIMESTAMP | NOT NULL, AUTO | Last modification timestamp |

### Relationships

- **User → UserSavedFilter**: One-to-many relationship with cascade delete
- **Foreign Key Constraint**: `user_id` references `users.id`

### Indexes

- Primary key index on `id`
- Foreign key index on `user_id`
- Composite index on `(user_id, filter_type, is_default)` for default filter queries

## API Endpoints

### Base URL

All endpoints are prefixed with `/api/saved-filters`

### 1. List Saved Filters

**GET** `/api/saved-filters/`

Retrieves all saved filters for the authenticated user.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `filter_type` | string | No | Filter by specific type (e.g., "offer") |
| `skip` | integer | No | Number of records to skip (pagination) |
| `limit` | integer | No | Maximum records to return (default: 100) |

#### Response

```json
[
  {
    "id": 1,
    "user_id": 123,
    "name": "Weekly Offers",
    "description": "Filters for weekly offer reviews",
    "filter_type": "offer",
    "is_default": true,
    "filter_data": { ... },
    "created_at": "2025-05-21T10:00:00Z",
    "updated_at": "2025-05-21T10:00:00Z"
  }
]
```

### 2. Get Single Saved Filter

**GET** `/api/saved-filters/{filter_id}`

Retrieves a specific saved filter by ID.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `filter_id` | integer | Yes | ID of the filter to retrieve |

#### Response

```json
{
  "id": 1,
  "user_id": 123,
  "name": "Weekly Offers",
  "description": "Filters for weekly offer reviews",
  "filter_type": "offer",
  "is_default": true,
  "filter_data": { ... },
  "created_at": "2025-05-21T10:00:00Z",
  "updated_at": "2025-05-21T10:00:00Z"
}
```

### 3. Create Saved Filter

**POST** `/api/saved-filters/`

Creates a new saved filter for the authenticated user.

#### Request Body

```json
{
  "name": "Weekly Offers",
  "description": "Filters for weekly offer reviews",
  "filter_type": "offer",
  "is_default": false,
  "filter_data": {
    "basic_filters": { ... },
    "column_filters": [ ... ],
    "complex_filters": { ... },
    "search_query": "",
    "sort_fields": [ ... ]
  }
}
```

#### Response

```json
{
  "id": 1,
  "user_id": 123,
  "name": "Weekly Offers",
  "description": "Filters for weekly offer reviews",
  "filter_type": "offer",
  "is_default": false,
  "filter_data": { ... },
  "created_at": "2025-05-21T10:00:00Z",
  "updated_at": "2025-05-21T10:00:00Z"
}
```

### 4. Update Saved Filter

**PUT** `/api/saved-filters/{filter_id}`

Updates an existing saved filter.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `filter_id` | integer | Yes | ID of the filter to update |

#### Request Body

```json
{
  "name": "Updated Weekly Offers",
  "description": "Updated description",
  "is_default": true,
  "filter_data": { ... }
}
```

#### Response

```json
{
  "id": 1,
  "user_id": 123,
  "name": "Updated Weekly Offers",
  "description": "Updated description",
  "filter_type": "offer",
  "is_default": true,
  "filter_data": { ... },
  "created_at": "2025-05-21T10:00:00Z",
  "updated_at": "2025-05-21T12:00:00Z"
}
```

### 5. Delete Saved Filter

**DELETE** `/api/saved-filters/{filter_id}`

Deletes a saved filter.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `filter_id` | integer | Yes | ID of the filter to delete |

#### Response

- **Status Code**: 204 No Content
- **Body**: Empty

### 6. Set Default Filter

**POST** `/api/saved-filters/{filter_id}/set-default`

Sets a filter as the default for the user (and removes default status from other filters of the same type).

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `filter_id` | integer | Yes | ID of the filter to set as default |

#### Response

```json
{
  "id": 1,
  "user_id": 123,
  "name": "Weekly Offers",
  "description": "Filters for weekly offer reviews",
  "filter_type": "offer",
  "is_default": true,
  "filter_data": { ... },
  "created_at": "2025-05-21T10:00:00Z",
  "updated_at": "2025-05-21T12:00:00Z"
}
```

## Authentication & Authorization

### Authentication

All endpoints require authentication via JWT Bearer token:

```
Authorization: Bearer <jwt_token>
```

### Authorization Rules

1. **Users can only access their own filters**
2. **Admins can access all filters**
3. **Only the owner or admin can modify/delete filters**

### Permission Matrix

| Operation | User (Owner) | User (Non-Owner) | Admin |
|-----------|--------------|------------------|-------|
| List own filters | ✅ | ❌ | ✅ |
| Get filter detail | ✅ | ❌ | ✅ |
| Create filter | ✅ | ❌ | ✅ |
| Update filter | ✅ | ❌ | ✅ |
| Delete filter | ✅ | ❌ | ✅ |
| Set default | ✅ | ❌ | ✅ |

## Request/Response Schemas

### SavedFilterBase

```json
{
  "name": "string",
  "description": "string | null",
  "filter_type": "string",
  "is_default": "boolean",
  "filter_data": "object"
}
```

### SavedFilterCreate

Inherits from `SavedFilterBase` with all fields required.

### SavedFilterUpdate

```json
{
  "name": "string | null",
  "description": "string | null",
  "is_default": "boolean | null",
  "filter_data": "object | null"
}
```

### SavedFilterResponse

```json
{
  "id": "integer",
  "user_id": "integer",
  "name": "string",
  "description": "string | null",
  "filter_type": "string",
  "is_default": "boolean",
  "filter_data": "object",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## Filter Data Structure

The `filter_data` field contains the complete filter configuration. For offer filters, the structure is:

```json
{
  "basic_filters": {
    "user_id": "integer | null",
    "product_type_id": "integer | null",
    "status": "string | null",
    "date_from": "string | null",
    "date_to": "string | null"
  },
  "column_filters": [
    ["column_name", "operator", "value"]
  ],
  "complex_filters": {
    "operator": "AND | OR",
    "conditions": [
      {
        "type": "condition | group",
        "data": {
          "column": "string",
          "operator": "string",
          "value": "any"
        }
      }
    ]
  },
  "search_query": "string",
  "sort_fields": [
    {
      "field": "string",
      "direction": "asc | desc"
    }
  ]
}
```

### Filter Data Validation

The system validates filter data structure to ensure:

1. **JSON Validity**: Data must be valid JSON
2. **Required Keys**: `basic_filters` is required for offer filters
3. **Type Consistency**: Arrays and objects are properly typed
4. **Field Validation**: Sort fields contain required `field` and `direction` keys

## Error Handling

### HTTP Status Codes

| Status Code | Description | Example Scenario |
|-------------|-------------|------------------|
| 200 | OK | Successful GET, PUT requests |
| 201 | Created | Successful POST request |
| 204 | No Content | Successful DELETE request |
| 400 | Bad Request | Invalid filter data, validation errors |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Filter doesn't exist |
| 500 | Internal Server Error | Server-side errors |

### Error Response Format

```json
{
  "detail": "Error message description"
}
```

### Common Error Scenarios

1. **Authentication Errors**
   ```json
   {
     "detail": "Not authenticated"
   }
   ```

2. **Authorization Errors**
   ```json
   {
     "detail": "Not enough permissions to access this filter"
   }
   ```

3. **Validation Errors**
   ```json
   {
     "detail": "Invalid filter data format: missing required key 'basic_filters'"
   }
   ```

4. **Not Found Errors**
   ```json
   {
     "detail": "Saved filter not found"
   }
   ```

## Usage Examples

### Creating a New Filter

```bash
curl -X POST "http://localhost:8000/api/saved-filters/" \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Apple Offers",
       "description": "Filters for apple product offers",
       "filter_type": "offer",
       "is_default": false,
       "filter_data": {
         "basic_filters": {
           "product_type_id": 1,
           "status": "CREATED"
         },
         "column_filters": [],
         "search_query": "",
         "sort_fields": [
           {"field": "created_at", "direction": "desc"}
         ]
       }
     }'
```

### Loading User's Filters

```bash
curl -X GET "http://localhost:8000/api/saved-filters/?filter_type=offer" \
     -H "Authorization: Bearer <token>"
```

### Setting a Default Filter

```bash
curl -X POST "http://localhost:8000/api/saved-filters/1/set-default" \
     -H "Authorization: Bearer <token>"
```

## Security Considerations

### Data Security

1. **Input Validation**: All filter data is validated before storage
2. **SQL Injection Prevention**: Parameterized queries and ORM usage
3. **XSS Prevention**: JSON data is properly escaped
4. **Authentication Required**: All endpoints require valid JWT tokens

### Access Control

1. **User Isolation**: Users can only access their own filters
2. **Admin Override**: Admins have full access when needed
3. **Ownership Verification**: All operations verify user ownership

### Rate Limiting

Consider implementing rate limiting for these endpoints:

- **Create Filter**: 10 requests per minute per user
- **Update Filter**: 20 requests per minute per user
- **Delete Filter**: 5 requests per minute per user

### Data Retention

- **Automatic Cleanup**: Consider implementing cleanup of old unused filters
- **Cascade Deletion**: Filters are automatically deleted when users are deleted
- **Audit Logging**: Consider logging filter operations for security audits

---

*This documentation is part of the Agricultural Product Management System documentation suite. For related documentation, see the main [DOCS_INDEX.md](../../DOCS_INDEX.md).*