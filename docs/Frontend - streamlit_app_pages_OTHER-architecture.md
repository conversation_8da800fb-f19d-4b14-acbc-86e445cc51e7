# Frontend - streamlit_app_OTHER Architecture

## 1. Overview

This document outlines the architecture and organization of auxiliary and supplementary modules within a Streamlit-based web application for agricultural product offer management. These modules include authentication pages, information pages, demonstration components, and specialized utilities that don't fit neatly into the core application structure but provide essential functionality.

The application follows a multi-layered architecture with these auxiliary components complementing the main business features by providing user authentication, onboarding, educational content, and experimental or utility features.

## 2. Directory Structure

```
streamlit_app/pages/
├── __init__.py                 # Package initialization
├── auth/                       # Authentication module directory
│   ├── __init__.py             # Auth package initialization
│   ├── login.py                # Login page implementation
│   ├── register.py             # Registration page implementation
│   └── reset_password.py       # Password reset page implementation
├── auth_login.py               # Redirect file to auth/login.py
├── auth_register.py            # Redirect file to auth/register.py
├── auth_reset_password.py      # Redirect file to auth/reset_password.py
├── info_app_guide.py           # Application guide/tutorial page
├── saved_forms.py              # Saved form management utility
├── tovabb_button_demo.py       # Continue button demo
├── tovabb_demo.py              # Multi-step form demo
└── tovacvv_demo.py             # Validation rule demo
```

## 3. Authentication Module

The authentication module provides user authentication functionality including login, registration, and password reset features.

### 3.1 Module Structure

The authentication module uses a dual organization approach:
- **Nested Structure**: Primary implementation in `pages/auth/` directory
- **Flat Structure**: Redirect files in the main `pages/` directory for compatibility

This pattern ensures compatibility with different Streamlit versions and deployment environments while maintaining clean code organization.

### 3.2 Login Page Implementation (`login.py`)

The login page provides user authentication functionality with a consistent pattern:

```python
def show_login():
    """
    Bejelentkezési oldal megjelenítése.
    """
    st.title("Bejelentkezés")
    
    # Két hasábos elrendezés
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Bejelentkezési űrlap
        success = login_form()
        
        # Ha sikeres a bejelentkezés, frissítjük az oldalt
        if success:
            st.rerun()
        
        # "Elfelejtett jelszó" link
        st.write("---")
        st.page_link("pages/auth_reset_password.py", label="Elfelejtett jelszó?")
    
    with col2:
        # Információs panel
        st.info("""
        ### Még nincs fiókja?
        
        Regisztráljon, hogy hozzáférjen a rendszerhez.
        """)
        
        # Átirányítás a regisztrációs oldalra
        if st.button("Regisztráció", type="secondary", use_container_width=True):
            st.switch_page("pages/auth_register.py")
```

This pattern demonstrates several key architectural principles:
- **Separation of concerns**: Page structure separate from form implementation
- **Responsive design**: Multi-column layout with responsive behavior
- **Component-based architecture**: Reuse of form components
- **Result handling pattern**: Processing success/failure of form submissions

### 3.3 Page Initialization Pattern

Authentication pages follow a consistent initialization pattern:

```python
# Ha ezt a modult közvetlenül futtatjuk
if __name__ == "__main__":
    # Beállítjuk az oldal címét
    st.set_page_config(
        page_title=get_page_title("Bejelentkezés"),
        page_icon="🔑",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Az eredeti sidebar renderelése
    render_sidebar()
    
    # Megjelenítjük a bejelentkezési oldalt
    show_login()
```

Key elements of this pattern:
- **Direct execution check**: Only configures the page when run directly
- **Consistent page configuration**: Standard parameters for page setup
- **Sidebar integration**: Renders the navigation sidebar
- **Main content display**: Calls the primary display function

## 4. Information and Guide Pages

### 4.1 Application Guide Implementation (`info_app_guide.py`)

The application guide presents educational content about the application using a visual approach:

```python
def show_guide():
    """
    Alkalmazás funkcióinak vizuális bemutatása.
    """
    st.title("🌿 Alkalmazás bemutatása")
    st.caption("Segítség új felhasználóknak – Lépésről lépésre")

    # Content sections
    st.markdown("...")
    
    # Two-column presentation pattern
    st.subheader("🟢 1. Regisztráció")
    col1, col2 = st.columns([2, 1])
    with col1:
        st.write("""...""")  # Descriptive text
    with col2:
        st.image("...", caption="Regisztráció lépései")  # Visual aid
```

Key patterns in information pages:
- **Visual documentation**: Combines text with images/GIFs for clarity
- **Structured sections**: Clear organization with headers and dividers
- **Two-column layout**: Text description alongside visual demonstration
- **Progressive disclosure**: Information presented in a logical sequence

## 5. Demonstration Pages

The application includes several demonstration pages that showcase specific components or patterns.

### 5.1 Multi-Step Form Demo (`tovabb_demo.py`)

The multi-step form demonstration showcases a complete implementation of a step-by-step form:

```python
# Lépés függvények definiálása
def step_personal_data(form_data):
    """Személyes adatok lépés"""
    st.subheader("1. Személyes adatok")
    
    # Form mezők
    first_name = st.text_input("Keresztnév", value=form_data.get("first_name", ""))
    last_name = st.text_input("Vezetéknév", value=form_data.get("last_name", ""))
    birth_date = st.date_input("Születési dátum", value=form_data.get("birth_date", None))
    
    # Adatok visszaadása
    return {
        "first_name": first_name,
        "last_name": last_name,
        "birth_date": birth_date
    }

# Validációs függvények definiálása
def validate_personal_data(form_data):
    """Személyes adatok validálása"""
    errors = []
    
    # Keresztnév ellenőrzése
    is_valid, error = validate_required(form_data.get("first_name"), "Keresztnév")
    if not is_valid:
        errors.append(error)
    
    # (Additional validation logic)
    
    return len(errors) == 0, errors

# Többlépéses űrlap megjelenítése
current_step, form_data = render_multi_step_form(
    steps, 
    validators, 
    form_id="registration_form",
    on_save=on_save
)
```

This pattern demonstrates:
- **Step function pattern**: Each step defined as a separate function
- **Validation function pattern**: Validation logic separated from display logic
- **Data flow pattern**: Data passed between steps and preserved in session state
- **Component composition**: Reuse of form components within each step

### 5.2 Continue Button Demo (`tovabb_button_demo.py`)

The continue button demo shows the implementation of a simple action button pattern:

```python
# Űrlap mezők
st.subheader("Adatok megadása")
name = st.text_input("Név")
email = st.text_input("E-mail cím")
message = st.text_area("Üzenet")

# TOVÁBB gomb megjelenítése
if render_continue_button():
    # Adatok mentése
    if name and email:
        st.success(f"Az adatok sikeresen mentve: {name}, {email}")
        
        # Adatok megjelenítése
        st.subheader("Mentett adatok")
        st.json({
            "name": name,
            "email": email,
            "message": message
        })
    else:
        st.error("A név és e-mail cím megadása kötelező!")
```

Key patterns:
- **Conditional execution**: Action triggered by button click
- **Simple validation pattern**: Checking required fields
- **Feedback pattern**: Visual feedback for success/error states
- **Component reuse**: Standardized button component

### 5.3 Validation Demo (`tovacvv_demo.py`)

The validation demo showcases specialized validation rules:

```python
# TOVACVV form megjelenítése
success, form_data = render_tovacvv_form()

# Eredmény megjelenítése
if success:
    st.markdown("---")
    st.subheader("Form adatok")
    st.json(form_data)
```

This demonstrates:
- **Specialized component usage**: Custom validation component
- **Result tuple pattern**: Consistent (success, data) return pattern
- **Conditional display**: Showing results only on successful submission

## 6. Utility Pages

### 6.1 Saved Forms Management (`saved_forms.py`)

The saved forms utility provides functionality to manage and resume partially completed forms:

```python
# Mentett űrlapok lekérése
saved_forms = get_saved_forms()

if not saved_forms:
    st.info("Nincsenek mentett űrlapok.")
else:
    # Mentett űrlapok listázása
    st.subheader("Mentett űrlapok")
    
    for form_id, form_info in saved_forms.items():
        with st.expander(f"Űrlap: {form_info['name']} (ID: {form_id})"):
            st.write(f"Utoljára módosítva: {form_info['last_modified']}")
            st.write(f"Lépés: {form_info['current_step'] + 1}/{form_info['total_steps']}")
            
            # Űrlap betöltése gomb
            if st.button("Űrlap betöltése", key=f"load_{form_id}"):
                # Űrlap betöltése
                form_data = load_form(form_id)
                
                # Űrlap adatok mentése a session state-be
                st.session_state.form_data = form_data
                st.session_state.current_step = form_info['current_step']
                
                # Átirányítás az űrlap oldalára
                st.markdown(f"[Átirányítás az űrlap oldalára](/tovabb_demo)")
```

This showcases:
- **Data retrieval pattern**: Loading saved data from storage
- **Expandable list pattern**: Using expanders for compact display
- **Dynamic key generation**: Creating unique keys for multiple buttons
- **Session state storage**: Storing loaded data in session state for later use
- **Page navigation pattern**: Redirecting to the form page

## 7. Common Design Patterns

### 7.1 Page Navigation Pattern

Page navigation is implemented consistently using Streamlit's navigation functions:

```python
# Direct navigation
st.switch_page("pages/auth_register.py")

# Link-based navigation
st.page_link("pages/auth_reset_password.py", label="Elfelejtett jelszó?")

# Post-action navigation with custom link
st.markdown(f"[Átirányítás az űrlap oldalára](/tovabb_demo)")
```

### 7.2 Two-Column Layout Pattern

Many pages use a two-column layout for efficient space utilization:

```python
# Két hasábos elrendezés
col1, col2 = st.columns([2, 1])  # Ratio-based width

with col1:
    # Primary content
    # ...

with col2:
    # Secondary content
    # ...
```

### 7.3 Form Success/Failure Pattern

Form components follow a consistent success/failure pattern:

```python
# Form rendering
success = login_form()

# Result handling
if success:
    # Success actions
    st.rerun()
else:
    # Form remains visible for retry
```

### 7.4 Page Configuration Pattern

Pages use a consistent configuration pattern:

```python
# Direct method approach
st.set_page_config(
    page_title=get_page_title("Page Name"),
    page_icon="🔑",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Utility function approach
set_page_config("Page Name", "🔑")
```

### 7.5 Component Return Pattern

Components follow a consistent return pattern:

```python
# Boolean success
if render_continue_button():
    # Action on success
    
# Tuple with success and data
success, form_data = render_tovacvv_form()

# Tuple with state and data
current_step, form_data = render_multi_step_form(steps, validators)
```

## 8. Integration with Main Application

### 8.1 Integration Points

These auxiliary pages integrate with the main application through several mechanisms:

1. **Shared Components**: Using common components like `sidebar.py`
2. **Shared Utilities**: Leveraging utilities like `page_utils.py`
3. **Session State**: Storing and retrieving data in Streamlit's session state
4. **Navigation Flow**: Directing users between different parts of the application
5. **Configuration**: Accessing shared configuration via `app_config.py`

### 8.2 Sidebar Integration

The sidebar provides consistent navigation across the application:

```python
# Az eredeti sidebar renderelése
render_sidebar()
```

## 9. Implementation Patterns

### 9.1 Module Organization Pattern

The codebase follows a dual-organization pattern:

```python
# Nested structure - pages/auth/login.py
def show_login():
    # Implementation

# Flat structure - pages/auth_login.py
auth/login.py  # Redirect file
```

This enables:
- **Clear organization**: Logically grouped modules in subdirectories
- **Compatibility**: Support for older Streamlit versions that require flat structure
- **Simplified imports**: Direct access to pages via redirect files

### 9.2 Step Function Pattern

Multi-step forms use a consistent step function pattern:

```python
def step_name(form_data):
    """Step description"""
    # UI elements
    field1 = st.text_input("Field1", value=form_data.get("field1", ""))
    
    # Return updated data
    return {
        "field1": field1,
        # Other fields
    }
```

### 9.3 Validation Function Pattern

Validation functions follow a consistent pattern:

```python
def validate_step_data(form_data):
    """Validation description"""
    errors = []
    
    # Validation logic
    is_valid, error = validate_required(form_data.get("field"), "Field")
    if not is_valid:
        errors.append(error)
    
    # Return validation result
    return len(errors) == 0, errors
```

### 9.4 Storage Access Pattern

Storage functions follow a consistent pattern:

```python
# Data retrieval
saved_forms = get_saved_forms()

# Data loading
form_data = load_form(form_id)

# Data deletion
delete_form(form_id)
```

## 10. Data Flow Patterns

### 10.1 Authentication Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Login Form   ├─────►│  API Call     ├─────►│  Session      │
│               │      │               │      │  Update       │
└───────────────┘      └───────────────┘      └───────┬───────┘
                                                     │
                                                     ▼
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Dashboard    │◄─────┤  Page         │◄─────┤  Success      │
│               │      │  Navigation   │      │  Handling     │
└───────────────┘      └───────────────┘      └───────────────┘
```

### 10.2 Multi-Step Form Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Step         ├─────►│  Data         ├─────►│  Validation   │
│  Function     │      │  Collection   │      │               │
└───────┬───────┘      └───────────────┘      └───────┬───────┘
        │                                             │
        │                                             │
┌───────▼───────┐      ┌───────────────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  Session      │◄─────┤  Next Step    │◄─────┤  Success      │
│  Update       │      │  Navigation   │      │  Handling     │
└───────────────┘      └───────────────┘      └───────────────┘
```

### 10.3 Form Save/Load Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Form         ├─────►│  Storage      ├─────►│  Success      │
│  Data         │      │  API          │      │  Message      │
└───────────────┘      └───────────────┘      └───────────────┘


┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Load         ├─────►│  Session      ├─────►│  Page         │
│  Request      │      │  Update       │      │  Navigation   │
└───────────────┘      └───────────────┘      └───────────────┘
```

## 11. Module Interactions

### 11.1 Key Module Dependencies

```
auth/login.py
  ├── components.auth_forms (login_form)
  ├── utils.config (get_page_title)
  └── components.sidebar (render_sidebar)

info_app_guide.py
  ├── components.sidebar (render_sidebar)
  └── app_config (APP_NAME)

tovabb_demo.py
  ├── components.multi_step_form (render_multi_step_form)
  ├── utils.page_utils (set_page_config)
  └── utils.validators (validate_required, validate_phone, validate_length)

saved_forms.py
  ├── utils.page_utils (set_page_config)
  └── utils.storage (get_saved_forms, load_form, delete_form)
```

## 12. Session State Usage

The application uses Streamlit's session state to maintain state across page reloads:

```python
# Form data storage
st.session_state.form_data = form_data
st.session_state.current_step = form_info['current_step']

# Page state
if "form_data" in st.session_state and st.session_state.form_data:
    st.info("Egy mentett űrlap betöltve. Folytathatja a kitöltést.")
```

## 13. UI Component Pattern

The application uses a consistent UI component pattern:

```python
# Component rendering
success = login_form()
current_step, form_data = render_multi_step_form(steps, validators)
if render_continue_button():
    # Action on button click
```

## 14. Page Reloading Pattern

The application uses a consistent pattern for reloading pages:

```python
# Simple reload
st.rerun()

# Conditional reload
if success:
    st.rerun()
```

## 15. Conclusion

The auxiliary modules in this Streamlit application provide essential supporting functionality for the main application features. They follow consistent architectural patterns that ensure code organization, maintainability, and user experience consistency.

Key architectural features include:
- Clear module organization with dual structure for compatibility
- Consistent page initialization patterns
- Standardized form handling and validation
- Systematic approach to multi-step processes
- Shared component usage across different pages
- Consistent session state management

These patterns enable efficient development and maintenance while ensuring a coherent user experience throughout the application.

## 16. Recommendations for LLM Context

When generating code for this application, consider:
1. **Follow the established return patterns**: Boolean or tuple returns with success and data
2. **Use the standard page setup pattern**: Include config, sidebar, and main content display
3. **Implement proper session state management**: Initialize and update session state consistently
4. **Maintain the two-column layout pattern** where appropriate for form/info pages
5. **Separate display and validation logic** for cleaner code organization
6. **Use the existing component APIs** rather than reimplementing functionality
7. **Follow the established naming conventions** for functions and variables
8. **Maintain the dual module structure** for compatibility across environments
