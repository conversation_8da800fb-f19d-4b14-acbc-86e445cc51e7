# Main Configuration Architecture

## 1. Overview

This document provides a comprehensive overview of the application's architecture and configuration system. The application is a multi-layered system consisting of a FastAPI backend and a Streamlit frontend, designed for managing agricultural product offers between producers and a central company. This architecture document focuses on how the configuration system is organized and how it supports the entire application structure.

## 2. System Architecture

The application follows a client-server architecture with two main components:

```
┌───────────────────┐       ┌───────────────────┐
│                   │       │                   │
│  Streamlit        │◄─────►│  FastAPI          │
│  Frontend         │       │  Backend          │
│                   │       │                   │
└───────────────────┘       └───────────────────┘
                                      │
                                      ▼
                            ┌───────────────────┐
                            │                   │
                            │  PostgreSQL       │
                            │  Database         │
                            │                   │
                            └───────────────────┘
```

### 2.1 Technology Stack

- **Frontend**: 
  - Streamlit (Python-based web app framework)
  - Session State for state management
  - Custom components for UI elements

- **Backend**:
  - FastAPI framework
  - SQLAlchemy ORM
  - Pydantic for data validation
  - JWT authentication
  - Alembic for database migrations

- **Database**: 
  - PostgreSQL
  - Alembic migrations for schema management

- **Deployment**:
  - Docker containers
  - Docker Compose for service orchestration

## 3. Configuration System

The application uses a multi-layered configuration approach to manage settings across different environments and components.

### 3.1 Environment Variables

Environment variables serve as the primary source of configuration and are defined in the `docker-compose.yml` file:

```yaml
backend:
  environment:
    APP_NAME: Mezőgazdasági Termékkezelő Rendszer
    ENVIRONMENT: development
    BACKEND_HOST: 0.0.0.0
    BACKEND_PORT: 8000
    DATABASE_URL: **************************************/termelo_db
    DB_HOST: db
    DB_PORT: 5432
    DB_USER: postgres
    DB_PASSWORD: postgres
    DB_NAME: termelo_db
    SECRET_KEY: your-secret-key-here
    JWT_ALGORITHM: HS256
    ACCESS_TOKEN_EXPIRE_MINUTES: 30
    STREAMLIT_HOST: localhost
    STREAMLIT_PORT: 8501

streamlit:
  environment:
    API_HOST: http://backend:8000
    API_BASE_URL: http://backend:8000/api
    APP_NAME: Mezőgazdasági Termékkezelő Rendszer
    COMPANY_NAME: Zöldség Világ Kft.
    API_AUTH_TOKEN: "PASTE_IDE_AZ_ADMIN_TOKEN"
```

These variables are loaded by both the backend and frontend applications to configure their behavior.

### 3.2 Backend Configuration

#### 3.2.1 Core Configuration Module

The backend uses a settings management pattern with Pydantic:

```python
# app/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    APP_NAME: str = "Mezőgazdasági Termékkezelő Rendszer"
    ENVIRONMENT: str = "development"
    
    # Database settings
    DB_HOST: str = "db"
    DB_PORT: int = 5432
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "postgres"
    DB_NAME: str = "termelo_db"
    DATABASE_URL: str = "**************************************/termelo_db"
    
    # Authentication settings
    SECRET_KEY: str = "your-secret-key-here"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # API settings
    BACKEND_HOST: str = "0.0.0.0"
    BACKEND_PORT: int = 8000
    
    # CORS settings
    CORS_ORIGINS: list = ["*"]
    
    # Email settings
    EMAILS_ENABLED: bool = False
    SMTP_HOST: str = ""
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    EMAILS_FROM_EMAIL: str = "<EMAIL>"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

This pattern offers several advantages:
- Type validation of configuration values
- Default values for development
- Environment variable overrides
- Documentation of available settings

### 3.3 Frontend Configuration

#### 3.3.1 App Configuration Module

The Streamlit frontend uses a centralized configuration file:

```python
# streamlit_app/app_config.py
import os

# API settings
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")

# Application settings
APP_NAME = os.getenv("APP_NAME", "Kertész Mester")
COMPANY_NAME = os.getenv("COMPANY_NAME", "Zöldség Világ Kft.")

# Offer statuses with visual styling
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    "CONFIRMED_BY_COMPANY": {
        "name": "Visszaigazolva",
        "color": "#3584e4",
        "description": "Az ajánlat visszaigazolva a cég által."
    },
    "ACCEPTED_BY_USER": {
        "name": "Elfogadva",
        "color": "#33a02c",
        "description": "A termelő elfogadta az ajánlatot."
    },
    "REJECTED_BY_USER": {
        "name": "Elutasítva",
        "color": "#e31a1c",
        "description": "A termelő elutasította az ajánlatot."
    },
    "FINALIZED": {
        "name": "Teljesítve",
        "color": "#6a3d9a",
        "description": "Az ajánlat teljesítve."
    }
}

# Session management
SESSION_TIMEOUT = 1800  # 30 minutes in seconds
SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}

# Debug settings
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

# Mobile detection threshold
MOBILE_WIDTH_THRESHOLD = 768
TABLET_WIDTH_THRESHOLD = 992

# API client configuration
API_CLIENT_CONFIG = {
    "retry_count": 3,
    "retry_backoff_factor": 0.3,
    "retry_status_forcelist": [500, 502, 503, 504],
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "timeout": 10,  # seconds
    "cache_ttl": 300  # seconds
}

# Menu configuration (imported from config/menu_config.py)
```

This configuration approach:
- Centralizes all application settings
- Provides default values that can be overridden by environment variables
- Includes visual styling information
- Defines constant values used throughout the application

#### 3.3.2 Streamlit Configuration

The Streamlit frontend also uses Streamlit's built-in configuration system via `.streamlit/config.toml`:

```toml
[theme]
base = "dark"
primaryColor = "#33a02c"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F5"
textColor = "#262730"

[client]
showSidebarNavigation = false
toolbarMode = "minimal"

[server]
port = 8501
enableCORS = true
enableXsrfProtection = true
```

This configuration controls:
- Application theme and styling
- Server behavior
- UI component visibility
- Security settings

## 4. Configuration Flow and Inheritance

### 4.1 Configuration Hierarchy

The configuration system follows a hierarchical precedence:

1. **Environment Variables**: Highest priority, override all other settings
2. **Configuration Files**: Default values defined in code
3. **Hardcoded Defaults**: Used when no other value is provided

This allows for flexible configuration across different environments:

```
┌───────────────────┐
│                   │
│  Environment      │
│  Variables        │
│                   │
└─────────┬─────────┘
          │
          ▼
┌───────────────────┐
│                   │
│  Configuration    │
│  Files            │
│                   │
└─────────┬─────────┘
          │
          ▼
┌───────────────────┐
│                   │
│  Hardcoded        │
│  Defaults         │
│                   │
└───────────────────┘
```

### 4.2 Configuration Loading Sequence

The configuration loading sequence follows a specific pattern:

1. **Backend**:
   - Load environment variables from `.env` file (if exists)
   - Override with Docker environment variables
   - Initialize Settings object
   - Make settings available globally via dependency injection

2. **Frontend**:
   - Load Streamlit config from `.streamlit/config.toml`
   - Initialize app settings from `app_config.py`
   - Override with Docker environment variables
   - Make settings available to components

## 5. Key Configuration Components

### 5.1 Database Configuration

Database settings are primarily managed in the backend configuration:

```python
# Database configuration parameters
DB_HOST: str = "db"
DB_PORT: int = 5432
DB_USER: str = "postgres"
DB_PASSWORD: str = "postgres"
DB_NAME: str = "termelo_db"
DATABASE_URL: str = "**************************************/termelo_db"
```

These settings are used to create the SQLAlchemy engine and connect to the database:

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

### 5.2 Authentication Configuration

Authentication settings are defined in the backend configuration:

```python
# Authentication settings
SECRET_KEY: str = "your-secret-key-here"
JWT_ALGORITHM: str = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
```

These settings control:
- JWT token generation and validation
- Password hashing
- Token expiration
- Security requirements

### 5.3 API Configuration

API configuration is managed in both backend and frontend:

**Backend**:
```python
# API settings
BACKEND_HOST: str = "0.0.0.0"
BACKEND_PORT: int = 8000
```

**Frontend**:
```python
# API settings
API_HOST = os.getenv("API_HOST", "http://backend:8000")
API_BASE_URL = os.getenv("API_BASE_URL", f"{API_HOST}/api")
```

This configuration allows the frontend to connect to the backend API regardless of the deployment environment.

### 5.4 UI Configuration

The frontend includes extensive UI configuration:

```python
# Offer statuses with visual styling
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra."
    },
    # Other statuses...
}

# Streamlit theme configuration in .streamlit/config.toml
[theme]
base = "dark"
primaryColor = "#33a02c"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F5"
textColor = "#262730"
```

This configuration ensures consistent visual styling throughout the application.

## 6. Docker Configuration and Environment Management

The application is containerized using Docker with configuration defined in `docker-compose.yml`:

```yaml
version: '3.8'
services:
  db:
    image: postgres:15
    container_name: termelo-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: termelo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - termelo-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend
    ports:
      - "8000:8000"
    environment:
      # Environment variables...
    volumes:
      - ./app:/app/app
      - ./migrations:/app/migrations
    depends_on:
      db:
        condition: service_healthy
    networks:
      - termelo-network
    command: >
      sh -c "chmod +x /app/init-db.sh && /app/init-db.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"

  streamlit:
    build:
      context: ./streamlit_app
      dockerfile: Dockerfile
    container_name: streamlit
    ports:
      - "8501:8501"
    environment:
      # Environment variables...
    volumes:
      - ./streamlit_app:/app
      - ./streamlit_app/.streamlit:/app/.streamlit
    working_dir: /app
    command: streamlit run main.py
    depends_on:
      - backend
    networks:
      - termelo-network
```

This configuration:
- Defines the container services: database, backend, and frontend
- Sets environment variables for each service
- Configures networking between services
- Mounts volumes for code and data persistence
- Defines startup commands
- Manages service dependencies

## 7. Database Migration Configuration

Database migrations are managed using Alembic with configuration in `alembic.ini`:

```ini
# A generic, single database configuration.

[alembic]
# path to migration scripts
script_location = migrations

# Adatbázis URL értékeit a .env fájlból olvassa a migrations/env.py
sqlalchemy.url = **************************************/termelo_db

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic
```

The migration environment is configured in `migrations/env.py`:

```python
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# Importáljuk a Base osztályt és a modelleket
from app.db.base import Base
from app.models import *  # Importáljuk az összes modellt

config = context.config

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata
```

This configuration enables automatic migration generation based on model changes.

## 8. Configuration Usage Patterns

### 8.1 Backend Configuration Access Pattern

The backend uses a dependency injection pattern to access configuration:

```python
# app/api/dependencies.py
from fastapi import Depends

from app.core.config import settings

def get_settings():
    return settings

# Usage in endpoint
@router.get("/info")
def get_api_info(settings = Depends(get_settings)):
    return {
        "app_name": settings.APP_NAME,
        "environment": settings.ENVIRONMENT,
        "version": "1.0.0"
    }
```

This pattern provides:
- Testability through dependency injection
- Consistent access to configuration
- Single source of truth for settings

### 8.2 Frontend Configuration Access Pattern

The frontend uses direct imports for configuration access:

```python
# Direct import
import app_config as config

# Usage
def render_sidebar():
    st.title(config.APP_NAME)
    
    # Use configuration values
    status_color = config.OFFER_STATUSES.get(status, {}).get("color", "#cccccc")
```

Some modules also implement a fallback pattern to handle different import scenarios:

```python
try:
    # Docker environment import
    from pages.utils.session import get_auth_token
    import app_config as config
except ImportError:
    try:
        # Direct import (development environment)
        from utils.session import get_auth_token
        import app_config as config
    except ImportError:
        # Fallback configuration
        config = {
            "API_BASE_URL": "http://localhost:8000/api",
            "APP_NAME": "Dev Environment"
        }
        
        # Fallback function
        def get_auth_token():
            """Fallback auth token getter"""
            return None
```

This pattern ensures the application can run in different environments with appropriate configuration.

## 9. Session Configuration

The application implements session management with configuration:

```python
# Session management configuration
SESSION_TIMEOUT = 1800  # 30 minutes in seconds
SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}
```

This configuration is used in the session management utilities:

```python
def is_authenticated():
    """
    Decorator for API caching.
    
    Args:
        ttl (int, optional): Cache time-to-live in seconds. Defaults to API_CLIENT_CONFIG["cache_ttl"].
        
    Returns:
        callable: Decorated function with caching
    """
    def decorator(func):
        cache = {}
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate unique cache key based on function args
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # Get actual TTL value
            actual_ttl = ttl or config.API_CLIENT_CONFIG.get("cache_ttl", 300)
            
            # Check if cached and not expired
            now = time.time()
            if key in cache and now - cache[key]["timestamp"] < actual_ttl:
                logger.debug(f"Cache hit for {key}")
                return cache[key]["result"]
            
            # Call original function
            result = func(*args, **kwargs)
            
            # Cache result
            cache[key] = {
                "result": result,
                "timestamp": now
            }
            
            # Limit cache size
            if len(cache) > config.API_CLIENT_CONFIG.get("cache_max_size", 100):
                # Remove oldest entry
                oldest_key = min(cache.keys(), key=lambda k: cache[k]["timestamp"])
                del cache[oldest_key]
            
            return result
        
        return wrapper
    
    return decorator
```

## 6. Docker Configuration and Environment Management

The application uses Docker for containerization and environment management, with detailed configuration in `docker-compose.yml` and individual `Dockerfile` files for each service.

### 6.1 Docker Compose Configuration

The `docker-compose.yml` file orchestrates the entire application environment:

```yaml
version: '3.8'
services:
  # PostgreSQL Database Service
  db:
    image: postgres:15
    container_name: termelo-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: termelo_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Mapped to non-default port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - termelo-network

  # FastAPI Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      # Application identification
      APP_NAME: Mezőgazdasági Termékkezelő Rendszer
      ENVIRONMENT: development
      
      # Network configuration
      BACKEND_HOST: 0.0.0.0
      BACKEND_PORT: 8000
      
      # Database connection
      DATABASE_URL: **************************************/termelo_db
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: termelo_db
      
      # Security settings
      SECRET_KEY: your-secret-key-here
      JWT_ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
      # Frontend integration
      STREAMLIT_HOST: localhost
      STREAMLIT_PORT: 8501
      STREAMLIT_BROWSER_SERVER_ADDRESS: http://localhost:8000
      
      # Admin token for special operations
      API_AUTH_TOKEN: "JWT_TOKEN"
    volumes:
      - ./app:/app/app  # Live code reloading
      - ./migrations:/app/migrations
      - ./init-db.sh:/app/init-db.sh
      - ./tests:/app/tests
    depends_on:
      db:
        condition: service_healthy
    networks:
      - termelo-network
    command: >
      sh -c "chmod +x /app/init-db.sh && /app/init-db.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"

  # Streamlit Frontend Service
  streamlit:
    build:
      context: ./streamlit_app
      dockerfile: Dockerfile
    container_name: streamlit
    restart: always
    ports:
      - "8501:8501"
    environment:
      # API connection
      API_HOST: http://backend:8000
      API_BASE_URL: http://backend:8000/api
      
      # Application branding
      APP_NAME: Mezőgazdasági Termékkezelő Rendszer
      COMPANY_NAME: Zöldség Világ Kft.
      
      # Development settings
      DEBUG: "False"
      DEVELOPMENT_MODE: "True"
      
      # Authentication for special operations
      API_AUTH_TOKEN: "PASTE_IDE_AZ_ADMIN_TOKEN"
    volumes:
      - ./streamlit_app:/app  # Live code reloading
      - ./streamlit_app/.streamlit:/app/.streamlit
    working_dir: /app
    command: streamlit run main.py
    depends_on:
      - backend
    networks:
      - termelo-network

  # Debug Service for Development
  debug:
    build:
      context: ./debug
      dockerfile: Dockerfile
    container_name: debug-tools
    restart: "no"
    volumes:
      - ./debug:/app/debug
      - /var/run/docker.sock:/var/run/docker.sock
      - ./logs:/app/logs
    environment:
      CONTAINER_STREAMLIT: streamlit
      CONTAINER_BACKEND: backend
      CONTAINER_DB: termelo-db
      DOCKER_HOST: unix:///var/run/docker.sock
      DEBUG_CONFIG_FILE: /app/debug/config/log_paths.json
    depends_on:
      - streamlit
      - backend
      - db
    networks:
      - termelo-network
    tty: true
    stdin_open: true
    user: "0:0"

# Network Configuration
networks:
  termelo-network:
    driver: bridge

# Volume Configuration
volumes:
  postgres_data:
```

This configuration provides:
- **Service Definition**: Clear definition of each component
- **Environment Variables**: Service-specific configuration
- **Volume Mounting**: Code and data persistence
- **Network Configuration**: Isolated communication
- **Health Checking**: Dependency management
- **Resource Allocation**: CPU, memory, and port allocation
- **Command Definition**: Service startup procedures

### 6.2 Backend Dockerfile

The backend service uses a dedicated Dockerfile:

```dockerfile
# Base Image
FROM python:3.11-slim

# Working Directory
WORKDIR /app

# System Dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Python Configuration
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH=/app

# Install Dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Add Pydantic Settings
RUN pip install --no-cache-dir pydantic-settings

# Copy Application Code
COPY . .

# Expose Port
EXPOSE 8000

# Start Command
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

This Dockerfile follows best practices:
- **Base Image**: Slim Python image for reduced size
- **Working Directory**: Clear separation of application
- **System Dependencies**: Required libraries for PostgreSQL
- **Environment Variables**: Python runtime configuration
- **Dependencies**: Separate layer for better caching
- **Application Code**: Copied after dependencies
- **Port Exposure**: Documentation of network requirements
- **Start Command**: Clear application startup procedure

### 6.3 Frontend Dockerfile

The frontend service has its own Dockerfile:

```dockerfile
# Base Image
FROM python:3.11-slim

# Working Directory
WORKDIR /app

# System Dependencies
RUN apt-get update && apt-get install -y \
    curl \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Python Configuration
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install Dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy Application Code
COPY . .

# Create symlinks for compatibility
RUN bash create_symlinks.sh && chmod +x create_symlinks.sh

# Fix Line Endings
RUN find . -type f -name "*.py" -exec dos2unix {} \;

# Expose Port
EXPOSE 8501

# Start Command
CMD ["streamlit", "run", "main.py", "--server.headless", "true", "--server.fileWatcherType", "none", "--client.showSidebarNavigation=false"]
```

This Dockerfile implements:
- **Base Image**: Same as backend for consistency
- **Working Directory**: Clear separation of application
- **Dependencies**: Python packages from requirements.txt
- **Symlinking**: File structure compatibility with all Streamlit versions
- **Line Ending Fixes**: Cross-platform compatibility
- **Port Exposure**: Network requirements documentation
- **Start Command**: Streamlit configuration

### 6.4 Environment-Specific Configuration

The application supports multiple environments (development, staging, production) through environment-specific configuration:

#### Development Environment
```yaml
environment:
  ENVIRONMENT: development
  DEBUG: "True"
  DEVELOPMENT_MODE: "True"
  LOG_LEVEL: "DEBUG"
```

#### Staging Environment
```yaml
environment:
  ENVIRONMENT: staging
  DEBUG: "False"
  DEVELOPMENT_MODE: "False"
  LOG_LEVEL: "INFO"
```

#### Production Environment
```yaml
environment:
  ENVIRONMENT: production
  DEBUG: "False"
  DEVELOPMENT_MODE: "False"
  LOG_LEVEL: "WARNING"
  # Use Docker Swarm or Kubernetes for high availability
```

This approach enables:
- **Environment-Specific Behavior**: Tailored settings per environment
- **Security Differentiation**: Different security settings by environment
- **Feature Toggling**: Enabling/disabling features by environment
- **Debugging Control**: More verbose logging in development

### 6.5 Database Initialization

The database is initialized using a custom script:

```bash
#!/bin/bash
# init-db.sh

# Wait for database availability
echo "Várakozás az adatbázis elérhetőségére..."
until PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c '\q'; do
  echo "Adatbázis még nem elérhető, várakozás..."
  sleep 2
done

echo "Adatbázis elérhető, migrációk futtatása..."

# Create migration directory if not exists
mkdir -p migrations/versions

# Create migration files if they don't exist
if [ ! -f "migrations/env.py" ]; then
    echo "Migrációs környezet fájl létrehozása..."
    cat > migrations/env.py << 'EOL'
# Migration environment configuration
from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
from app.db.base import Base
from app.models import *

config = context.config

if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline() -> None:
    url = f"**************************************/termelo_db"
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = f"**************************************/termelo_db"
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
EOL
fi

# Run migrations
echo "Migrációk futtatása..."
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head

echo "Adatbázis inicializálása kész!"
```

This script ensures:
- **Database Readiness**: Waits for database availability
- **Migration Setup**: Creates necessary migration files
- **Schema Creation**: Generates and applies migrations
- **Idempotency**: Safe to run multiple times

## 7. Database Migration Configuration

Database schema evolution is managed through Alembic migrations with detailed configuration.

### 7.1 Alembic Configuration

The `alembic.ini` file controls migration behavior:

```ini
[alembic]
# Migration script location
script_location = migrations

# Template used for migration files
file_template = %%(rev)s_%%(slug)s

# Maximum length for slug field
truncate_slug_length = 40

# Database URL (overridden in migrations/env.py)
sqlalchemy.url = **************************************/termelo_db

[post_write_hooks]
# Format migrations after generation
# hooks=black
# black.type=console_scripts
# black.entrypoint=black
# black.options=-l 79

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
```

This configuration specifies:
- **Migration Location**: Where migration files are stored
- **Naming Convention**: How migration files are named
- **Database Connection**: How to connect to the database
- **Logging Configuration**: Controls migration logging
- **Hooks**: Optional post-generation processing

### 7.2 Migration Environment

The `migrations/env.py` file configures the migration environment:

```python
from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context

# Import models to include in migrations
from app.db.base import Base
from app.models import *  # Import all models for discovery

# Alembic config object
config = context.config

# Logging configuration
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Model metadata for migration generation
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode, generating SQL scripts.
    """
    url = f"**************************************/termelo_db"
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """
    Run migrations in 'online' mode, directly against database.
    """
    # Override URL from config
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = f"**************************************/termelo_db"
    
    # Configure connection with pooling
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

# Choose appropriate migration mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

This configuration provides:
- **Model Detection**: Automatic inclusion of all models
- **Database Connection**: Dynamic database URL configuration
- **Migration Modes**: Both online and offline options
- **Transaction Handling**: Proper transaction boundaries

### 7.3 Migration Workflow

The database migration workflow follows a specific sequence:

1. **Generate Migration**:
   ```bash
   alembic revision --autogenerate -m "Description of changes"
   ```

2. **Review Migration**:
   Manually review the generated migration script in `migrations/versions/`

3. **Apply Migration**:
   ```bash
   alembic upgrade head
   ```

4. **Rollback if Needed**:
   ```bash
   alembic downgrade -1
   ```

This workflow ensures:
- **Automatic Detection**: Changes to models are detected automatically
- **Version Control**: All schema changes are tracked
- **Rollback Support**: Changes can be safely undone
- **Documentation**: Changes are described and recorded

## 8. Configuration Access Patterns

The application implements several patterns for accessing configuration across different components.

### 8.1 Backend Configuration Access Patterns

#### 8.1.1 Dependency Injection Pattern

The backend uses FastAPI's dependency injection system for configuration access:

```python
# app/api/dependencies.py
from fastapi import Depends
from functools import lru_cache

from app.core.config import settings

@lru_cache()
def get_settings():
    """
    Returns cached application settings.
    
    This function is cached to avoid repeated instantiation of the Settings object.
    
    Returns:
        Settings: Application settings
    """
    return settings

# Usage in endpoint
@router.get("/info")
def get_api_info(settings = Depends(get_settings)):
    """
    API info endpoint using dependency injection for settings.
    
    Args:
        settings: Application settings (injected)
        
    Returns:
        dict: API information
    """
    return {
        "app_name": settings.APP_NAME,
        "environment": settings.ENVIRONMENT,
        "version": settings.VERSION
    }
```

This pattern provides:
- **Testability**: Easy to mock for unit tests
- **Consistency**: Single point of access
- **Performance**: Cached for efficiency
- **Reusability**: Available across all endpoints

#### 8.1.2 Direct Import Pattern

Some backend components use direct imports for simplicity:

```python
# app/some_module.py
from app.core.config import settings

def some_function():
    """Function that directly uses settings."""
    database_url = settings.DATABASE_URL
    # Use the configuration value
```

This pattern is used when:
- **Simplicity** is more important than testability
- The component is **not part of the API layer**
- The configuration is **unlikely to need mocking**

#### 8.1.3 Environment Configuration Pattern

Some backend scripts access configuration directly from environment:

```python
# app/scripts/create_admin.py
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Access configuration
admin_email = os.getenv("FIRST_ADMIN_EMAIL", "<EMAIL>")
admin_password = os.getenv("FIRST_ADMIN_PASSWORD", "admin")

# Use configuration values
```

This pattern is used for:
- **Standalone scripts** outside the main application
- **Command-line tools** and utilities
- **Initialization processes** that run before the app

### 8.2 Frontend Configuration Access Patterns

#### 8.2.1 Direct Import Pattern

The frontend primarily uses direct imports for configuration:

```python
# streamlit_app/components/sidebar.py
import app_config as config

def render_sidebar():
    """Render application sidebar."""
    st.title(config.APP_NAME)
    
    # Use other configuration values
    status_color = config.OFFER_STATUSES.get(status, {}).get("color", "#cccccc")
```

This pattern is used for:
- **UI components** that need configuration
- **Static configuration** that doesn't change at runtime
- **Visual styling** and branding elements

#### 8.2.2 Environment Variable Pattern

Some frontend components access environment variables directly:

```python
# streamlit_app/utils/api_client.py
import os

# Get API token from environment
API_TOKEN = os.getenv("API_AUTH_TOKEN")

def special_operation():
    """Function that uses the token."""
    headers = {"Authorization": f"Bearer {API_TOKEN}"}
    # Use the token for API calls
```

This pattern is used for:
- **Sensitive information** like tokens
- **Environment-specific values** that might change
- **Runtime configuration** not suitable for session state

#### 8.2.3 Session State Pattern

The frontend uses Streamlit's session state for runtime configuration:

```python
# streamlit_app/utils/session.py
import streamlit as st

def save_user_preference(key, value):
    """
    Save user preference to session state.
    
    Args:
        key (str): Preference key
        value: Preference value
    """
    preference_key = f"preference_{key}"
    st.session_state[preference_key] = value

def get_user_preference(key, default=None):
    """
    Get user preference from session state.
    
    Args:
        key (str): Preference key
        default: Default value if not found
        
    Returns:
        Value of preference or default
    """
    preference_key = f"preference_{key}"
    return st.session_state.get(preference_key, default)
```

This pattern is used for:
- **User preferences** that can change
- **UI state** that persists across page reloads
- **Dynamic configuration** modified at runtime

#### 8.2.4 Multi-Environment Import Pattern

The frontend implements a fallback pattern for different environments:

```python
# streamlit_app/components/some_component.py
try:
    # Docker environment import
    from pages.utils.session import get_auth_token
    import app_config as config
except ImportError:
    try:
        # Direct import (development environment)
        from utils.session import get_auth_token
        import app_config as config
    except ImportError:
        # Fallback configuration
        config = {
            "API_BASE_URL": "http://localhost:8000/api",
            "APP_NAME": "Dev Environment"
        }
        
        # Fallback function
        def get_auth_token():
            """Fallback auth token getter."""
            return None
```

This pattern ensures:
- **Environment Compatibility**: Works in different folder structures
- **Development Flexibility**: Functions in both Docker and direct execution
- **Graceful Degradation**: Provides fallbacks when imports fail
- **Error Prevention**: Avoids crashing on missing dependencies

## 9. Session Management Configuration

The application implements detailed session management for user authentication and state persistence.

### 9.1 Session State Configuration

Session state is configured through several parameters:

```python
# Session timeout and activity tracking
SESSION_TIMEOUT = 1800  # 30 minutes in seconds
SESSION_ACTIVITY_KEY = "last_activity"
SESSION_TOKEN_EXPIRY_KEY = "token_expiry"

# Session variable mappings for flexibility
SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}

# Maximum inactive time before auto-logout
MAX_INACTIVE_TIME = 3600  # 1 hour in seconds

# Session storage options
SESSION_PERSISTENCE = True
```

These settings control:
- **Session Duration**: How long sessions remain valid
- **Inactivity Handling**: When inactive sessions expire
- **Variable Naming**: Flexible variable names for different environments
- **Persistence Options**: Whether sessions persist across restarts

### 9.2 Authentication Session Management

Authentication state is managed through session variables:

```python
# streamlit_app/utils/session.py
import streamlit as st
import time
import logging
import app_config as config

logger = logging.getLogger(__name__)

# Default session variable names if not configured
DEFAULT_SESSION_VARS = {
    "token": "auth_token",
    "user": "user",
    "authenticated": "authenticated"
}

def init_session_state():
    """
    Initialize session state variables if they don't exist.
    """
    # Get session variable mappings with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Initialize authentication variables
    if session_vars.get("token") not in st.session_state:
        st.session_state[session_vars.get("token")] = None
    
    if session_vars.get("user") not in st.session_state:
        st.session_state[session_vars.get("user")] = None
    
    if session_vars.get("authenticated") not in st.session_state:
        st.session_state[session_vars.get("authenticated")] = False
    
    # Initialize activity tracking
    if config.SESSION_ACTIVITY_KEY not in st.session_state:
        st.session_state[config.SESSION_ACTIVITY_KEY] = time.time()

def is_authenticated():
    """
    Check if user is authenticated.
    
    Returns:
        bool: Authentication status
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if authentication flag is set
    auth_flag = st.session_state.get(auth_key, False)
    
    # Ensure we have a token if authenticated
    if auth_flag and not st.session_state.get(token_key):
        logger.warning("Authentication flag set but no token found, clearing session")
        clear_session()
        return False
    
    # Check token expiry
    if auth_flag and config.SESSION_TOKEN_EXPIRY_KEY in st.session_state:
        if time.time() > st.session_state.get(config.SESSION_TOKEN_EXPIRY_KEY):
            logger.info("Token expired, clearing session")
            clear_session()
            return False
    
    # Check session timeout
    check_session_timeout()
    
    # Update activity timestamp if authenticated
    if auth_flag:
        update_activity()
    
    return auth_flag

def get_current_user():
    """
    Get current authenticated user.
    
    Returns:
        dict: User data or None
    """
    if not is_authenticated():
        return None
    
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    return st.session_state.get(session_vars.get("user"))

def get_auth_token():
    """
    Get authentication token.
    
    Returns:
        str: JWT token or None
    """
    if not is_authenticated():
        return None
    
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    return st.session_state.get(session_vars.get("token"))

def clear_session():
    """Clear all session data for logout."""
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Clear authentication variables
    if session_vars.get("token") in st.session_state:
        st.session_state[session_vars.get("token")] = None
    
    if session_vars.get("user") in st.session_state:
        st.session_state[session_vars.get("user")] = None
    
    if session_vars.get("authenticated") in st.session_state:
        st.session_state[session_vars.get("authenticated")] = False
    
    # Clear token expiry
    if config.SESSION_TOKEN_EXPIRY_KEY in st.session_state:
        del st.session_state[config.SESSION_TOKEN_EXPIRY_KEY]
    
    logger.info("Session cleared")

def check_session_timeout():
    """
    Check if session has timed out due to inactivity.
    
    Returns:
        bool: True if session is still valid, False if timed out
    """
    # Skip check if not authenticated
    if not is_authenticated():
        return True
    
    # Get last activity time
    last_activity = st.session_state.get(config.SESSION_ACTIVITY_KEY, time.time())
    current_time = time.time()
    
    # Check if session has timed out
    if current_time - last_activity > config.SESSION_TIMEOUT:
        logger.info(f"Session timed out after {config.SESSION_TIMEOUT} seconds of inactivity")
        clear_session()
        return False
    
    return True

def update_activity():
    """Update last activity timestamp."""
    st.session_state[config.SESSION_ACTIVITY_KEY] = time.time()
```

This implementation provides:
- **Comprehensive Authentication**: Complete session lifecycle
- **Secure Token Handling**: Proper JWT management
- **Timeout Management**: Inactivity detection and handling
- **Stateful Sessions**: Persistent authentication state
- **Logging**: Diagnostic information for troubleshooting

### 9.3 Role-Based Session Management

The application implements role-based session management:

```python
def get_user_role():
    """
    Get current user role.
    
    Returns:
        str: User role or None
    """
    user = get_current_user()
    return user.get("role").lower() if user and "role" in user else None

def has_role(required_roles):
    """
    Check if current user has any of the required roles.
    
    Args:
        required_roles (list): List of roles to check
        
    Returns:
        bool: True if user has any required role
    """
    user_role = get_user_role()
    if not user_role:
        return False
    
    # Convert to list if string
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    # Check if user role is in required roles
    return user_role in [r.lower() for r in required_roles]

def require_role(required_roles):
    """
    Enforce role requirement or redirect.
    
    Args:
        required_roles (list): List of roles to check
        
    Returns:
        bool: True if requirement is met
    """
    if not is_authenticated():
        st.error("Bejelentkezés szükséges az oldal megtekintéséhez.")
        st.switch_page("pages/auth_login.py")
        return False
    
    if not has_role(required_roles):
        st.error(f"Nincs megfelelő jogosultsága az oldal megtekintéséhez.")
        user_role = get_user_role()
        
        # Redirect based on role
        if user_role == "termelő":
            st.switch_page("pages/producer_dashboard.py")
        elif user_role == "ügyintéző":
            st.switch_page("pages/operator_dashboard.py")
        elif user_role == "admin":
            st.switch_page("pages/admin_dashboard.py")
        else:
            st.switch_page("main.py")
        
        return False
    
    return True
```

This implementation provides:
- **Role Checking**: Verification of user roles
- **Permission Enforcement**: Gatekeeping for protected pages
- **Appropriate Redirection**: Role-based navigation
- **Friendly Messages**: Clear explanation of access issues

## 10. Offer Status Configuration

The application implements a sophisticated status configuration system for offers:

```python
# Domain-specific configuration for offer status
OFFER_STATUSES = {
    "CREATED": {
        "name": "Létrehozva",
        "color": "#FFA500",  # Orange
        "icon": "🔄",
        "description": "Az ajánlat létrehozva, de még nem került visszaigazolásra.",
        "next_statuses": ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"],
        "actions": {
            "operator": ["confirm"],
            "producer": ["edit", "cancel"],
            "admin": ["confirm", "edit", "cancel", "delete"]
        }
    },
    "CONFIRMED_BY_COMPANY": {
        "name": "Visszaigazolva",
        "color": "#3584e4",  # Blue
        "icon": "✅",
        "description": "Az ajánlat visszaigazolva a cég által.",
        "next_statuses": ["ACCEPTED_BY_USER", "REJECTED_BY_USER"],
        "actions": {
            "operator": ["finalize"],
            "producer": ["accept", "reject"],
            "admin": ["finalize", "edit", "delete"]
        }
    },
    "ACCEPTED_BY_USER": {
        "name": "Elfogadva",
        "color": "#33a02c",  # Green
        "icon": "👍",
        "description": "A termelő elfogadta az ajánlatot.",
        "next_statuses": ["FINALIZED"],
        "actions": {
            "operator": ["finalize"],
            "producer": ["view"],
            "admin": ["finalize", "edit", "delete"]
        }
    },
    "REJECTED_BY_USER": {
        "name": "Elutasítva",
        "color": "#e31a1c",  # Red
        "icon": "❌",
        "description": "A termelő elutasította az ajánlatot.",
        "next_statuses": ["CREATED"],
        "actions": {
            "operator": ["create_new"],
            "producer": ["view"],
            "admin": ["edit", "delete"]
        }
    },
    "FINALIZED": {
        "name": "Teljesítve",
        "color": "#6a3d9a",  # Purple
        "icon": "🏁",
        "description": "Az ajánlat teljesítve.",
        "next_statuses": [],
        "actions": {
            "operator": ["view", "export"],
            "producer": ["view", "export"],
            "admin": ["view", "export", "delete"]
        }
    }
}
```

This configuration provides:
- **Visual Representation**: Colors and icons for each status
- **Workflow Definition**: Available transitions between statuses
- **Role-Based Permissions**: Actions allowed for each role
- **Documentation**: Descriptions of each status
- **UI Integration**: Consistent styling across the application

### 10.1 Status Transition Management

The application manages offer status transitions based on this configuration:

```python
def get_valid_next_statuses(current_status, user_role):
    """
    Get valid next statuses for an offer based on current status and user role.
    
    Args:
        current_status (str): Current offer status
        user_role (str): User role
        
    Returns:
        list: Valid next statuses
    """
    # Get status configuration
    status_config = config.OFFER_STATUSES.get(current_status, {})
    
    # Get possible next statuses
    next_statuses = status_config.get("next_statuses", [])
    
    # Check role permissions
    role_actions = status_config.get("actions", {}).get(user_role, [])
    
    # Filter statuses based on permissions
    allowed_transitions = {
        "confirm": "CONFIRMED_BY_COMPANY",
        "accept": "ACCEPTED_BY_USER",
        "reject": "REJECTED_BY_USER",
        "finalize": "FINALIZED",
        "create_new": "CREATED"
    }
    
    # Keep only statuses allowed by role actions
    valid_statuses = [
        status for status in next_statuses
        if any(action for action in role_actions if allowed_transitions.get(action) == status)
    ]
    
    return valid_statuses

def can_transition_to(current_status, target_status, user_role):
    """
    Check if offer can transition to target status.
    
    Args:
        current_status (str): Current offer status
        target_status (str): Target offer status
        user_role (str): User role
        
    Returns:
        bool: True if transition is allowed
    """
    valid_statuses = get_valid_next_statuses(current_status, user_role)
    return target_status in valid_statuses
```

This implementation ensures:
- **Workflow Integrity**: Only valid transitions are allowed
- **Role Enforcement**: Actions restricted by user role
- **Configuration-Driven**: Changes to flow require only configuration updates
- **Clear Permissions**: Explicit permission model

## 11. Security Configuration

The application implements comprehensive security configuration across all components.

### 11.1 Authentication Security

Backend authentication is configured for security:

```python
# app/core/security.py

# JWT configuration
SECRET_KEY = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# Password security
PASSWORD_HASHING_ALGORITHM = "bcrypt"
BCRYPT_ROUNDS = 12  # Higher is more secure but slower

# Login security
MAX_FAILED_LOGIN_ATTEMPTS = 5
ACCOUNT_LOCKOUT_MINUTES = 15
PASSWORD_RESET_TOKEN_EXPIRE_HOURS = 24

# Create password context with security settings
pwd_context = CryptContext(
    schemes=[PASSWORD_HASHING_ALGORITHM],
    deprecated="auto",
    bcrypt__rounds=BCRYPT_ROUNDS
)
```

This configuration provides:
- **Secure Defaults**: Strong security even without customization
- **Algorithm Selection**: Modern hashing algorithms
- **Configurable Security**: Adjustable security levels
- **Brute Force Protection**: Limited login attempts

### 11.2 API Security

API security is configured to protect endpoints:

```python
# CORS configuration for API security
CORS_ORIGINS = ["*"]  # Should be restricted in production
ALLOW_CREDENTIALS = True
ALLOW_METHODS = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
ALLOW_HEADERS = ["Authorization", "Content-Type", "Accept"]

# Rate limiting
RATE_LIMIT_PER_MINUTE = 60
RATE_LIMIT_BURST = 100

# API key protection for special endpoints
API_KEY_HEADER = "X-API-Key"
API_KEY = os.getenv("API_KEY", "")  # Should be set in production
```

This configuration ensures:
- **Cross-Origin Protection**: Controlled access from different origins
- **Rate Limiting**: Protection from excessive requests
- **Method Restrictions**: Limited to necessary HTTP methods
- **Header Controls**: Only required headers are allowed

### 11.3 Database Security

Database security is configured for data protection:

```python
# Database connection security
DB_SSL_REQUIRED = os.getenv("DB_SSL_REQUIRED", "False").lower() in ("true", "1", "t")
DB_SSL_CERT = os.getenv("DB_SSL_CERT", "")
DB_CONNECT_TIMEOUT = 10  # seconds

# Query security
DB_QUERY_TIMEOUT = 30  # seconds
DB_MAX_QUERY_SIZE = 1000  # maximum items in a query

# Database access control
DB_READONLY_USER = os.getenv("DB_READONLY_USER", "")
DB_READONLY_PASSWORD = os.getenv("DB_READONLY_PASSWORD", "")
```

This configuration provides:
- **Transport Security**: Optional SSL for database connections
- **Timeout Protection**: Limits on connection and query times
- **Query Limiting**: Prevents excessive resource usage
- **Access Control**: Separate users with different permissions

## 12. Logging Configuration

The application implements comprehensive logging configuration for debugging and monitoring.

### 12.1 Backend Logging Configuration

```python
# app/core/logging_config.py
import logging
import os
from logging.config import dictConfig

# Base logging level from environment
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

# Logging format
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# Log file configuration
LOG_FILE = os.getenv("LOG_FILE", "app.log")
LOG_DIR = os.getenv("LOG_DIR", "logs")
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE_PATH = os.path.join(LOG_DIR, LOG_FILE)

# Configure logging
logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": LOG_FORMAT,
            "datefmt": DATE_FORMAT
        },
    },
    "handlers": {
        "console": {
            "level": LOG_LEVEL,
            "class": "logging.StreamHandler",
            "formatter": "default",
        },
        "file": {
            "level": LOG_LEVEL,
            "class": "logging.handlers.RotatingFileHandler",
            "formatter": "default",
            "filename": LOG_FILE_PATH,
            "maxBytes": 10485760,  # 10 MB
            "backupCount": 5,
            "encoding": "utf8"
        }
    },
    "loggers": {
        "app": {
            "handlers": ["console", "file"],
            "level": LOG_LEVEL,
        },
        "uvicorn": {
            "handlers": ["console", "file"],
            "level": LOG_LEVEL,
        },
        "sqlalchemy.engine": {
            "handlers": ["console", "file"],
            "level": "WARNING",
        }
    }
}

# Apply configuration
dictConfig(logging_config)

# Create logger for application
logger = logging.getLogger("app")
```

This configuration provides:
- **Multiple Handlers**: Console and file logging
- **Rotation**: Log file management to prevent disk fills
- **Format Control**: Consistent log formatting
- **Level Control**: Different log levels for components
- **Environment Awareness**: Configuration via environment

### 12.2 Frontend Logging Configuration

```python
# streamlit_app/utils/logging_config.py
import logging
import os
import sys

# Base logging level from environment
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

# Logging format
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# Configure root logger
logging.basicConfig(
    level=LOG_LEVEL,
    format=LOG_FORMAT,
    datefmt=DATE_FORMAT,
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Configure file logging if enabled
LOG_TO_FILE = os.getenv("LOG_TO_FILE", "False").lower() in ("true", "1", "t")
if LOG_TO_FILE:
    LOG_DIR = os.getenv("LOG_DIR", "logs")
    os.makedirs(LOG_DIR, exist_ok=True)
    LOG_FILE = os.path.join(LOG_DIR, "streamlit.log")
    
    file_handler = logging.handlers.RotatingFileHandler(
        LOG_FILE,
        maxBytes=10485760,  # 10 MB
        backupCount=5,
        encoding="utf8"
    )
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    logging.getLogger().addHandler(file_handler)

# Create app logger
logger = logging.getLogger("streamlit_app")
logger.info(f"Logging initialized at level {LOG_LEVEL}")

# Set level for specific loggers
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)
```

This configuration ensures:
- **Standard Output**: Logging to console for Docker integration
- **Optional File Logging**: File output when configured
- **Third-Party Control**: Limiting noise from libraries
- **Initialization Logging**: Confirmation of setup
- **Level Configuration**: Environment-controlled log levels

## 13. Conclusion

The configuration system of this application follows a comprehensive, layered approach that enables flexibility, security, and maintainability. Key architectural principles include:

1. **Centralized Configuration**: Core settings in dedicated modules
2. **Environment Adaptation**: Dynamic configuration based on environment
3. **Type Safety**: Validation and type checking for configuration values
4. **Default Values**: Sensible defaults for all settings
5. **Clear Hierarchy**: Well-defined precedence for configuration sources
6. **Documentation**: Thorough documentation of configuration options
7. **Role-Based Configuration**: Different settings based on user roles
8. **Security Enforcement**: Strong security defaults with customization
9. **Container Integration**: Seamless Docker configuration
10. **Schema Evolution**: Robust database migration system

These principles ensure the application can be easily deployed, maintained, and extended across different environments while providing a consistent and secure experience for users and developers.

## 14. Best Practices for Configuration Management

When extending or modifying the application's configuration, follow these best practices:

### 14.1 Adding New Configuration Options

1. **Choose the appropriate module**:
   - Backend settings: Add to `app/core/config.py`
   - Frontend settings: Add to `streamlit_app/app_config.py`
   - Environment-specific: Add to `docker-compose.yml`

2. **Follow the established pattern**:
   ```python
   # Backend Pydantic setting
   NEW_SETTING: str = "default_value"
   
   # Frontend setting
   NEW_SETTING = os.getenv("NEW_SETTING", "default_value")
   ```

3. **Add validation if needed**:
   ```python
   # Type validation
   PORT: int = Field(8000, ge=1024, le=65535)
   
   # Value validation
   @validator('ENVIRONMENT')
   def environment_must_be_valid(cls, v):
       if v not in ['development', 'staging', 'production']:
           raise ValueError('must be development, staging or production')
       return v
   ```

### 14.2 Security Best Practices

1. **Never hardcode secrets**:
   - Use environment variables for all secrets
   - Generate strong defaults when secrets are missing
   - Document required secrets clearly

2. **Implement secure defaults**:
   - CORS restrictions by default
   - Strong password policies
   - Reasonable timeouts
   - Limited permissions

3. **Validate sensitive configuration**:
   - Check secret lengths and complexity
   - Validate security-related settings
   - Provide warnings for insecure configurations

### 14.3 Documentation Best Practices

1. **Document each setting**:
   - Purpose and usage
   - Acceptable values and constraints
   - Default value and rationale
   - Environment variable name

2. **Group related settings**:
   - Database settings together
   - Authentication settings together
   - UI settings together

3. **Include examples**:
   - Example values for each setting
   - Example environment variables
   - Example configuration for different environments

### 14.4 Backward Compatibility

1. **Maintain fallbacks for renamed settings**:
   ```python
   # Support both old and new names
   OLD_NAME = os.getenv("OLD_NAME")
   NEW_NAME = os.getenv("NEW_NAME", OLD_NAME or "default_value")
   ```

2. **Version configuration changes**:
   - Document when settings are added, changed, or removed
   - Include version numbers in comments
   - Maintain compatibility layers when needed

3. **Implement graceful degradation**:
   - Handle missing or invalid configuration
   - Provide sensible fallbacks
   - Log warnings for deprecated settings

By following these practices, you'll maintain the robustness and flexibility of the configuration system while ensuring consistent behavior across the application's lifecycle and deployment environments.
Checks if the user is authenticated.
    
    Returns:
        bool: True if authenticated, False otherwise
    """
    # Get session variables with fallback
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    auth_key = session_vars.get("authenticated", "authenticated")
    token_key = session_vars.get("token", "auth_token")
    
    # Check if authentication flag is set
    auth_flag = st.session_state.get(auth_key, False)
    
    # If auth flag is set, make sure we also have a token
    if auth_flag and not st.session_state.get(token_key):
        clear_session()
        return False
    
    # Check token expiry
    if auth_flag and st.session_state.get("token_expiry"):
        if time.time() > st.session_state.get("token_expiry"):
            clear_session()
            return False
    
    # Check session timeout
    check_session_timeout()
    
    return auth_flag
```

## 10. API Client Configuration

The frontend includes configuration for the API client:

```python
# API client configuration
API_CLIENT_CONFIG = {
    "retry_count": 3,
    "retry_backoff_factor": 0.3,
    "retry_status_forcelist": [500, 502, 503, 504],
    "retry_allowed_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "timeout": 10,  # seconds
    "cache_ttl": 300  # seconds
}
```

This configuration is used in the API client implementation:

```python
class APIClient:
    """API client for communicating with the backend API."""
    
    def __init__(self, base_url):
        """Initialize the API client."""
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Configure retry strategy from configuration
        retry_strategy = Retry(
            total=config.API_CLIENT_CONFIG["retry_count"],
            backoff_factor=config.API_CLIENT_CONFIG["retry_backoff_factor"],
            status_forcelist=config.API_CLIENT_CONFIG["retry_status_forcelist"],
            allowed_methods=config.API_CLIENT_CONFIG["retry_allowed_methods"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
```

## 11. Role-Based Configuration

The application includes role-based configuration for UI elements and access control:

```python
# Menu configuration for different roles
PRODUCER_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/producer_dashboard.py"
    },
    # Other menu items...
]

OPERATOR_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/operator_dashboard.py"
    },
    # Other menu items...
]

ADMIN_MENU = [
    {
        "label": "Dashboard",
        "icon": "🏠",
        "path": "pages/admin_dashboard.py"
    },
    # Other menu items...
]

ROLE_MENUS = {
    "termelő": PRODUCER_MENU,
    "ügyintéző": OPERATOR_MENU,
    "admin": ADMIN_MENU
}
```

This configuration enables role-based navigation and access control:

```python
def render_sidebar():
    """Render the sidebar for navigation."""
    with st.sidebar:
        st.title(config.APP_NAME)
        
        # Check authentication
        if is_authenticated():
            user = get_current_user()
            role = user.get("role", "").lower()
            
            # Get menu for user role
            menu_items = config.ROLE_MENUS.get(role, [])
            
            # Display menu
            st.markdown("### Navigation")
            for item in menu_items:
                if st.sidebar.button(
                    f"{item['icon']} {item['label']}",
                    key=f"nav_{item['label']}",
                    use_container_width=True
                ):
                    st.switch_page(item['path'])
```

## 12. Conclusion

The configuration system of this application is designed to be flexible, maintainable, and environment-aware. It follows several key principles:

1. **Centralization**: Configuration is centralized in dedicated modules
2. **Hierarchy**: Clear precedence of configuration sources
3. **Environment Awareness**: Configuration adapts to different environments
4. **Type Safety**: Validation of configuration values through Pydantic
5. **Defaults**: Sensible defaults for all configuration values
6. **Documentation**: Well-documented configuration options

This approach ensures the application can be easily deployed and maintained across different environments while providing a consistent experience for users and developers.

## 13. Best Practices for Configuration Management

When extending or modifying the application, follow these best practices:

1. **Add new configuration options to the appropriate module**:
   - Backend settings to `app/core/config.py`
   - Frontend settings to `streamlit_app/app_config.py`

2. **Use environment variables for sensitive or environment-specific values**:
   - Add the variable to `docker-compose.yml`
   - Provide a default value in the configuration module

3. **Follow the established naming conventions**:
   - UPPER_CASE for constant configuration values
   - CamelCase for configuration classes
   - snake_case for configuration functions

4. **Document all configuration options**:
   - Include descriptions for Pydantic fields
   - Add comments explaining the purpose and possible values

5. **Use the dependency injection pattern for backend configuration access**:
   - Use `Depends(get_settings)` in FastAPI endpoints
   - Avoid direct imports of settings in business logic

6. **Implement fallback patterns for frontend configuration**:
   - Handle import errors gracefully
   - Provide sensible defaults for all settings

By following these practices, you'll maintain the robustness and flexibility of the configuration system while ensuring consistent behavior across the application.
