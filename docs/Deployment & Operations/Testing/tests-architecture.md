# Tests Architecture

## 1. Overview

This document details the testing architecture used in the Streamlit-based agricultural product offer management system. The testing system is designed to support both backend (FastAPI) and frontend (Streamlit) components, with a focus on unit tests, integration tests, and component tests that run within a Docker containerized environment.

The testing framework follows these core principles:
- All tests run within Docker containers to ensure consistent testing environments
- Test structure mirrors the application's module organization
- Tests are implemented using pytest for both frontend and backend components
- Special handling is required for Streamlit-specific tests due to session state management

## 2. Directory Structure

```
tests/
├── __init__.py                           # Package initialization
├── components/                           # Tests for Streamlit UI components
│   ├── test_data_display.py              # Tests for data display components
│   ├── test_date_filter.py               # Tests for date filter component
│   └── test_sidebar.py                   # Tests for sidebar component
├── integration/                          # Integration tests
│   └── test_notification_flow.py         # End-to-end notification flow test
├── notification/                         # Notification-specific tests
│   ├── check_notifications.py            # Utility to check notification data
│   └── test_crud_notification.py         # Tests for notification CRUD operations
├── pages/                                # Tests for specific pages
│   └── operator/
│       └── test_offer_management_detail.py # Tests for offer detail page
├── backend/                              # Backend API tests (implied)
├── README.md                             # Testing documentation
├── test_entrypoint.py                    # Intelligent test runner
└── test_offers_api.py                    # Tests for offers API endpoints
```

## 3. Test Execution Framework

### 3.1 Test Entrypoint Script (`test_entrypoint.py`)

The core of the testing framework is an intelligent entrypoint script that:

1. Automatically discovers test files throughout the project
2. Analyzes test files to determine if they need Streamlit-specific environment setup
3. Configures the appropriate PYTHONPATH for each test execution
4. Provides an interactive or command-line interface for test selection

```python
def requires_streamlit_app(test_file):
    """Check if the test file imports streamlit_app (by simple static analysis)."""
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read(5120)  # Only check first 5k chars for performance
    return (
        'from streamlit_app' in content or 'import streamlit_app' in content
    )
```

This script ensures that Streamlit-specific tests can import the necessary modules by automatically extending the PYTHONPATH when needed.

### 3.2 Test Execution Commands

Tests are executed within Docker containers using one of these approaches:

```bash
# Using the test entrypoint script (recommended)
docker compose exec backend python3 tests/test_entrypoint.py --test tests/components/test_sidebar.py

# Interactive test selection
docker compose exec backend python3 tests/test_entrypoint.py

# Direct pytest execution (legacy)
docker compose exec backend pytest -v --maxfail=3 --disable-warnings tests/components/test_sidebar.py
```

## 4. Testing Types and Patterns

### 4.1 Component Tests

Component tests verify the behavior of individual UI components in isolation, focusing on rendering, state management, and user interactions.

```python
# Example component test for sidebar
def test_sidebar_desktop_and_mobile_branch():
    """A render_sidebar() desktop/mobil ágat helyesen választja."""
    st.session_state.clear()
    if "mobile_view" not in st.session_state:
        st.session_state.mobile_view = True
    if "user_favorites" not in st.session_state:
        st.session_state.user_favorites = []
    # Mobil nézet
    st.session_state.mobile_view = True
    with patch("streamlit_app.components.sidebar.is_authenticated", return_value=True), \
         patch("streamlit_app.components.sidebar.get_current_user", return_value={"role": "admin"}):
        sidebar.render_sidebar()
```

**Key Patterns:**
- Explicit initialization of Streamlit's `session_state` variables
- Patching of authentication and user-related functions
- Testing responsive design behavior for different device types

### 4.2 Page Tests

Page tests verify entire page controllers, focusing on their integration with multiple components and API services.

```python
# Example page test with device type parameterization
@pytest.mark.parametrize("device_type", ["desktop", "tablet", "mobile"])
def test_show_offer_detail(monkeypatch, mock_api_functions, device_type):
    """
    Teszteli az ajánlat részletek megjelenítését különböző eszköztípusokon.
    """
    # Streamlit mock beállítása
    mock_st = MockStreamlit()
    # ... setup code ...
    
    # Session state beállítása
    mock_st.session_state = {
        'is_mobile': device_type == "mobile",
        'is_tablet': device_type == "tablet",
        'loading_logs': False,
        'loading_attachments': False,
        'loading_related': False
    }
    monkeypatch.setattr("streamlit.session_state", mock_st.session_state)
    
    # Test execution
    show_offer_detail(1)
    
    # Verification
    mock_api_functions["get_offer_details"].assert_called_once_with(1)
```

**Key Patterns:**
- Comprehensive mocking of Streamlit UI functions
- Parameterized tests for different device types
- Mocking of API service functions
- Verification of API calls with correct parameters

### 4.3 API Tests

API tests verify the backend endpoints using FastAPI's TestClient.

```python
# Example API test
def test_offers_response_contains_user_and_product_type():
    """
    Teszteli, hogy a /offers végpont válasza tartalmazza-e a user és product_type objektumokat.
    """
    response = client.get("/offers")
    assert response.status_code == 200
    offers = response.json()
    assert isinstance(offers, list)
    if offers:
        offer = offers[0]
        assert "user" in offer, "A válaszban nincs user mező!"
        assert isinstance(offer["user"], dict)
```

**Key Patterns:**
- Direct use of FastAPI's TestClient
- Verification of response structure and content
- Checking for specific fields in nested objects

### 4.4 Integration Tests

Integration tests verify end-to-end flows across multiple system components.

```python
# Example integration test for notification flow
def test_notification_end_to_end():
    headers = {"Authorization": f"Bearer {TEST_USER_TOKEN}"}
    
    # 1. Értesítés létrehozása
    payload = { /* ... */ }
    resp = requests.post(f"{API_BASE_URL}/notifications/", json=payload, headers=headers)
    assert resp.status_code == 200, f"Létrehozás sikertelen: {resp.text}"
    notification = resp.json()
    notification_id = notification["id"]
    
    # 2. Saját értesítések lekérése
    resp = requests.get(f"{API_BASE_URL}/notifications/me", headers=headers)
    assert resp.status_code == 200, f"Lekérés sikertelen: {resp.text}"
    items = resp.json().get("items", [])
    assert any(n["id"] == notification_id for n in items)
```

**Key Patterns:**
- Tests follow a complete user workflow
- Each step depends on the successful completion of previous steps
- Direct API calls using the requests library
- Environment variables for configuration

### 4.5 CRUD Operation Tests

Tests for basic Create, Read, Update, Delete operations on backend models.

```python
# Example CRUD test for notifications
def test_create_notification_requires_related_entity_type():
    """
    Teszt: Ha a related_entity_type mező nincs kitöltve, a create_notification ValueError-t dob.
    """
    db = DummyDB()
    # Hiányzó related_entity_type
    obj_in = NotificationCreate(
        user_id=1,
        type="info",
        message="Test message",
        detail=None,
        target_roles=None,
        related_entity_type=None,
        related_entity_id=None
    )
    with pytest.raises(ValueError) as excinfo:
        crud_notification.create_notification(db, obj_in)
    assert "related_entity_type" in str(excinfo.value)
```

**Key Patterns:**
- Testing validation rules and constraints
- Using dummy database objects for isolation
- Explicit testing of error cases
- Verification of error messages

## 5. Streamlit-Specific Testing Patterns

### 5.1 Session State Management

Streamlit's session state presents a unique challenge for testing, as it doesn't work natively in pytest environments. The testing framework addresses this with several patterns:

```python
# Initialize session state for testing
st.session_state.clear()
if "mobile_view" not in st.session_state:
    st.session_state.mobile_view = False
if "user_favorites" not in st.session_state:
    st.session_state.user_favorites = []
```

**Key Approaches:**
- Explicit initialization of all required session state variables
- Clearing session state before each test
- Setting required default values for each component

### 5.2 Mocking Streamlit Functions

Since Streamlit's UI functions can't be directly called in test environments, extensive mocking is used:

```python
# Comprehensive Streamlit mock object
class MockStreamlit:
    def __init__(self):
        self.session_state = {}
        self.columns_return = [MagicMock(), MagicMock(), MagicMock()]
        self.tabs_return = [MagicMock(), MagicMock(), MagicMock()]

    def markdown(self, *args, **kwargs):
        pass

    def title(self, *args, **kwargs):
        pass
    
    # ... more mock methods ...
```

**Key Patterns:**
- Complete mocking of all used Streamlit functions
- Returning appropriate mock objects for container-style functions (columns, tabs)
- Tracking function calls for verification

### 5.3 AppTest for Component Testing

For simpler component tests, Streamlit's built-in AppTest class is used:

```python
# Using Streamlit's AppTest for testing components
def test_display_offer_table_renders_html(tmp_path, offers):
    # AppTest a Streamlit teszteléshez
    at = AppTest.from_function(lambda: data_display.display_offer_table(offers))
    at.run()
    # Ellenőrizzük, hogy van HTML táblázat a státusz színezéssel
    html_tables = [el for el in at.markdowns if '<table' in el.value and 'background:' in el.value]
    assert html_tables, "A státusz színezett HTML táblázat nem jelent meg a komponensben."
```

**Key Patterns:**
- Using AppTest to run component functions in isolation
- Inspecting the rendered output for expected content
- Checking for specific HTML elements and attributes

## 6. Testing Responsive Design

The application's responsive design is extensively tested across different device types:

```python
@pytest.mark.parametrize("device_type", ["desktop", "tablet", "mobile"])
def test_show_offer_detail(monkeypatch, mock_api_functions, device_type):
    # Session state beállítása
    mock_st.session_state = {
        'is_mobile': device_type == "mobile",
        'is_tablet': device_type == "tablet",
        # ... other state variables ...
    }
```

**Key Approaches:**
- Parameterized tests for different device types
- Setting appropriate session state variables for each device type
- Verifying that the correct rendering approach is used based on device type

## 7. API Mocking Strategies

API functions are consistently mocked across tests to isolate components from network dependencies:

```python
@pytest.fixture
def mock_api_functions():
    """Mock API függvények létrehozása a tesztekhez"""
    with patch('streamlit_app.pages.operator.offer_management.offer_detail.get_offer_details') as mock_get_details, \
         patch('streamlit_app.pages.operator.offer_management.offer_detail.get_offer_logs') as mock_get_logs:
        
        # Mock visszatérési értékek beállítása
        mock_get_details.return_value = (True, mock_offer_data())
        mock_get_logs.return_value = (True, [
            # ... mock data ...
        ])
        
        yield {
            "get_offer_details": mock_get_details,
            "get_offer_logs": mock_get_logs,
            # ... other mocks ...
        }
```

**Key Patterns:**
- Using pytest fixtures for reusable mocks
- Consistent return value format `(success, result)`
- Providing rich test data that mirrors production data structures
- Yielding a dictionary of mocks for easy access in tests

## 8. Test Data Management

Test data is provided through fixtures and factory functions:

```python
@pytest.fixture
def mock_offer_data():
    return {
        "id": 1,
        "status": "CONFIRMED_BY_COMPANY",
        "created_at": datetime.now() - timedelta(days=5),
        # ... more fields ...
        "product_type": {
            "id": 2,
            "name": "Bio alma",
            "category": {
                "id": 1,
                "name": "Gyümölcsök"
            }
        }
    }
```

**Key Approaches:**
- Using pytest fixtures for reusable test data
- Creating nested objects that match API response structures
- Using realistic data values (dates, IDs, statuses)
- Including all fields that will be accessed by tested code

## 9. Testing Workflows and User Interactions

Complex user workflows are tested by simulating user interactions:

```python
def test_status_change_workflow(monkeypatch, mock_api_functions):
    # Session state for status change dialog
    mock_st.session_state = {
        # ... other state ...
        'show_status_dialog_1': True,
        'new_status_1': 'ACCEPTED_BY_USER'
    }
    
    # Override button mock for confirmation
    def mock_button_confirm(*args, **kwargs):
        if args and "Megerősítés" in args[0]:
            return True
        return False
    monkeypatch.setattr("streamlit.button", mock_button_confirm)
    
    # Execute test
    show_offer_detail(1)
    
    # Verify API call
    mock_api_functions["update_offer_status"].assert_called_once()
    assert mock_api_functions["update_offer_status"].call_args[0][0] == 1  # offer_id
    assert mock_api_functions["update_offer_status"].call_args[0][1] == 'ACCEPTED_BY_USER'  # new_status
```

**Key Patterns:**
- Setting up session state to represent specific UI states
- Mocking user interactions (button clicks, selections)
- Verifying the resulting API calls
- Checking session state changes after the interaction

## 10. Error Handling and Edge Case Testing

Tests explicitly verify error handling and edge cases:

```python
def test_load_offer_details_error(monkeypatch, mock_api_functions):
    # API error simulation
    mock_api_functions["get_offer_details"].return_value = (False, "API hiba történt")
    
    # Track error messages
    error_messages = []
    def mock_error(message):
        error_messages.append(message)
    monkeypatch.setattr("streamlit.error", mock_error)
    
    # Execute test
    show_offer_detail(1)
    
    # Verify error message
    assert len(error_messages) > 0
    assert any("nem található vagy hiba történt" in msg for msg in error_messages)
```

**Key Approaches:**
- Explicitly simulating API errors and edge cases
- Tracking error messages for verification
- Testing both happy and unhappy paths
- Verifying appropriate user feedback for errors

## 11. Testing Component Class Hierarchies

Object-oriented component classes are tested for proper initialization and behavior:

```python
def test_component_classes(monkeypatch):
    # Import component classes
    from streamlit_app.pages.operator.offer_management.detail_components import (
        DetailContainer,
        StatusIndicator,
        EntityCard,
        Timeline,
        ActivityLog
    )
    
    # Test constructors
    detail_container = DetailContainer(title="Teszt panel", icon="📝")
    status_indicator = StatusIndicator(status="CONFIRMED_BY_COMPANY")
    
    # Verify properties
    assert detail_container.title == "Teszt panel"
    assert detail_container.icon == "📝"
    assert status_indicator.status == "CONFIRMED_BY_COMPANY"
```

**Key Patterns:**
- Testing class constructors with various parameters
- Verifying object properties are correctly set
- Testing inheritance relationships
- Checking component relationships and compositions

## 12. Best Practices and Guidelines

The testing framework enforces several best practices:

1. **Docker Environment**: All tests must run within the Docker container to ensure consistency
2. **PYTHONPATH Management**: The entrypoint script handles PYTHONPATH to support cross-module imports
3. **Session State Initialization**: All Streamlit tests must explicitly initialize required session state variables
4. **API Mocking**: External dependencies should be mocked for test isolation
5. **Test Naming**: Test functions should have descriptive names indicating what they verify
6. **Assertions**: Tests should include clear assertions with helpful error messages
7. **Test Structure**: Test structure should mirror the application's module structure

## 13. Conclusion

The testing architecture is designed to support comprehensive testing of both frontend (Streamlit) and backend (FastAPI) components within a Docker containerized environment. Special attention is given to handling Streamlit's unique challenges, particularly session state management and UI function mocking.

The entrypoint script provides an intelligent way to run tests with the appropriate environment configuration, while the various testing patterns ensure consistent and thorough verification of component behavior, API interactions, and user workflows.

By following these patterns and leveraging the testing infrastructure, developers can ensure that new features and changes maintain the application's quality and functionality across all components.
