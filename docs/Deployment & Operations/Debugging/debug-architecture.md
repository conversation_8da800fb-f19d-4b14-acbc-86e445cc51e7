# Debug Architecture - Mezőgazdasági Termékkezel<PERSON> Rendszer

## Overview

The debug system for the Mezőgazdasági Termékkezelő Rendszer (Agricultural Product Management System) is responsible for handling, analyzing, and displaying logs. The system consists of several key components:

- Log management and analysis
- Docker container management
- Docker Compose log handling
- Interactive command-line interface

The architecture follows a modular design pattern, with clear separation of concerns and responsibilities between components. The system is containerized to ensure consistent behavior across different environments.

## System Architecture

![Architecture Diagram](https://via.placeholder.com/800x500)

### Core Components

1. **Logger Module** (`logger.py`)
   - Foundation of the logging infrastructure
   - Provides structured, traceable JSON-formatted logs
   - Handles different log levels and formatting options

2. **Container Manager** (`container.py`)
   - Interfaces with Docker to retrieve container logs
   - Manages container operations (start/stop/restart)
   - Handles container status monitoring

3. **Log Analyzer** (`analyzer.py`)
   - Analyzes log files to identify patterns and issues
   - Categorizes errors based on predefined patterns
   - Generates statistics and suggestions

4. **Docker Compose Manager** (`debug_compose.py`)
   - Provides Docker Compose specific log handling
   - Manages real-time log streaming with color coding
   - Controls container operations through Docker Compose

5. **Command Line Interface** (`cli.py`)
   - Main user interface for the debug system
   - Processes command-line arguments
   - Dispatches commands to appropriate components

6. **Interactive Menu** (`debug_menu.sh`)
   - Provides a user-friendly shell-based menu
   - Simplifies access to debugging functionality

## Data Flow

1. **Log Generation & Collection**
   - Application components (Streamlit, Backend, Database) generate logs
   - Logs are stored in predefined locations within containers
   - Container Manager collects logs from various sources

2. **Log Processing & Analysis**
   - Raw logs are parsed and structured
   - Log Analyzer identifies patterns and categorizes issues
   - Timestamps and trace IDs enable tracking related events

3. **Presentation & User Interaction**
   - CLI and Menu provide access to log data
   - Color-coded output highlights important information
   - Interactive filtering and searching capabilities

## Module Details

### Logger (`logger.py`)

The Logger module is the foundation of the system, providing a structured logging framework:

- **Structured JSON Logging**: All logs follow a consistent JSON format
- **Timestamps**: Every log entry includes precise timestamps
- **Trace IDs**: Enables tracking related events across components
- **Contextual Data**: Additional context can be attached to logs
- **Color-Coded Console Output**: Visual distinction between log levels
- **Log File Support**: Writes logs to files for persistence

```python
# Example logger usage
from debug.logger import Logger, LogLevel

logger = Logger(
    service_name="backend",
    log_file="debug/logs/backend.log",
    console_output=True,
    json_output=True,
    min_level=LogLevel.INFO
)

# Set context for all subsequent logs
logger.set_context(user_id="12345", session="abc123")

# Log at different levels
logger.info("System started successfully")
logger.warning("Resource usage approaching threshold", resource="memory", usage=85)

try:
    # Some operation that might fail
    result = some_operation()
except Exception as e:
    logger.exception("Operation failed", e, operation="some_operation")
```

### Container Manager (`container.py`)

The Container Manager handles interactions with Docker containers:

- **Container Log Retrieval**: Fetches logs directly from containers
- **Command Execution**: Runs commands inside containers
- **Container Status Monitoring**: Tracks container health and state
- **Container Operations**: Starts, stops, and restarts containers
- **Log File Management**: Handles log files within containers
- **Log Rotation**: Manages log size through rotation

```python
# Example container manager usage
from debug.container import ContainerManager

container_manager = ContainerManager()

# Get logs from a specific service
logs = container_manager.get_container_logs(
    service_name="backend",
    tail=100,
    since="2h"
)

# Execute a command in a container
return_code, stdout, stderr = container_manager.exec_command(
    service_name="backend",
    command=["ls", "-la", "/app/logs"]
)

# Check container statuses
statuses = container_manager.refresh_container_statuses()
```

### Log Analyzer (`analyzer.py`)

The Log Analyzer identifies patterns and issues in logs:

- **Pattern Recognition**: Identifies known error patterns
- **Error Categorization**: Groups errors by type (API, database, auth, etc.)
- **Hourly Error Statistics**: Tracks error frequency over time
- **Problem Grouping**: Groups related errors using trace IDs
- **Suggestion Generation**: Provides recommended actions

```python
# Example log analyzer usage
from debug.analyzer import LogAnalyzer

analyzer = LogAnalyzer()

# Analyze logs from a specific service
results = analyzer.analyze_logs(logs="...", service_name="backend")

# Access analysis results
error_categories = results["categories"]
identified_issues = results["identified_issues"]
suggestions = results["suggestions"]
```

### Docker Compose Manager (`debug_compose.py`)

The Docker Compose Manager handles Docker Compose specific operations:

- **Real-Time Log Monitoring**: Streams container logs in real-time
- **Color-Coded Log Display**: Visually distinguishes different services
- **Structured Log Formatting**: Formats logs for better readability
- **Error Highlighting**: Distinguishes different types of error messages
- **Container Status Querying**: Checks container states
- **Container Operations**: Executes container operations via Docker Compose

```python
# Example Docker Compose manager usage
from debug.debug_compose import DockerComposeManager

compose_manager = DockerComposeManager()

# Follow logs in real-time
compose_manager.get_container_logs(
    service_name="backend",
    follow=True
)

# Filter logs for specific patterns
compose_manager.set_container_filter(
    service_name="backend",
    filter_pattern="error"
)

# Restart a container
compose_manager.restart_container("backend")
```

### Command Line Interface (`cli.py`)

The CLI is the main interface for accessing the debug system:

- **Log Viewing and Filtering**: Access and filter logs
- **Log Analysis**: Analyze logs for patterns and issues
- **Docker Compose Operations**: Manage Docker Compose
- **Container Operations**: Manage containers
- **Debug Mode Management**: Control debug mode

```bash
# View logs
python -m debug.cli logs streamlit
python -m debug.cli logs backend --tail 100
python -m debug.cli logs db --json

# Analyze logs
python -m debug.cli analyze streamlit
python -m debug.cli analyze backend --detailed

# Follow logs in real-time
python -m debug.cli follow streamlit
python -m debug.cli follow backend --filter "error"

# Manage containers
python -m debug.cli status
python -m debug.cli compose restart backend
```

## Configuration

The system uses a JSON-based configuration system:

```json
{
    "log_paths": {
        "streamlit": [
            "/app/logs/streamlit.log",
            "/app/.streamlit/logs"
        ],
        "backend": [
            "/app/logs/backend.log",
            "/var/log/backend"
        ],
        "db": [
            "/var/log/postgresql/postgresql-15-main.log",
            "/var/lib/postgresql/data/log"
        ]
    },
    "containers": {
        "streamlit": "streamlit",
        "backend": "backend",
        "db": "termelo-db"
    },
    "use_colors": true,
    "log_rotation": {
        "max_size_mb": 10,
        "backup_count": 5
    }
}
```

Configuration can be updated via the CLI:

```bash
# Show current configuration
python -m debug.cli config show

# Update log paths
python -m debug.cli config update --log-path streamlit "/new/path/to/logs"

# Update container names
python -m debug.cli config update --container backend "new-backend-container"

# Update log rotation settings
python -m debug.cli config update --max-size 20 --backup-count 10
```

## Deployment

The debug system is containerized using Docker:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install basic packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    bash \
    curl \
    procps \
    gcc \
    python3-dev \
    libyaml-dev \
    libc6-dev \
    apt-transport-https \
    ca-certificates \
    gnupg-agent \
    software-properties-common \
    vim \
    net-tools \
    iputils-ping \
    docker.io \
    docker-compose \
    && rm -rf /var/lib/apt/lists/*

# Install Cython
RUN pip install --no-cache-dir Cython

# Install dependencies
COPY requirements.txt /app/debug/
RUN pip install --no-cache-dir -r /app/debug/requirements.txt

# Copy debug module
COPY . /app/debug/

# Create necessary directories
RUN mkdir -p /app/debug/logs /app/debug/config

# Set permissions
RUN chmod +x /app/debug/debug_menu.sh

# Set environment variables
ENV PYTHONPATH=/app
ENV API_BASE_URL=http://backend:8000/api
ENV DEBUG_CONFIG_FILE=/app/debug/config/log_paths.json
ENV DOCKER_HOST=unix:///var/run/docker.sock

# Entry point
ENTRYPOINT ["bash", "/app/debug/debug_menu.sh"]
```

## Error Patterns and Detection

The system recognizes several error patterns:

1. **API Errors**:
   - HTTP 4xx/5xx status codes
   - API timeout errors
   - Failed API requests

2. **Database Errors**:
   - Connection failures
   - Query execution errors
   - Database locks

3. **Authentication Errors**:
   - Authentication failures
   - Token expiration
   - Authorization issues

4. **Validation Errors**:
   - Missing required fields
   - Invalid input values
   - Format validation failures

5. **Server Errors**:
   - Internal server errors
   - Out of memory conditions
   - High CPU usage

## Log Rotation

The system includes log rotation functionality:

- **Size-Based Rotation**: Rotates logs when they exceed a defined size
- **Backup Management**: Maintains a configurable number of backups
- **Compression**: Optionally compresses rotated logs
- **Cleanup**: Removes old log files based on retention policies

## Extensibility

The system is designed to be extensible:

1. **Adding New Error Patterns**:
   - Update the `ERROR_PATTERNS` dictionary in `analyzer.py`
   - Add corresponding detection logic in `_identify_issues`

2. **Supporting New Services**:
   - Update the `log_paths` and `containers` entries in the configuration
   - Ensure the service follows consistent logging patterns

3. **Adding New Commands**:
   - Extend the argument parser in `cli.py`
   - Implement corresponding functionality in the relevant modules
   - Update the menu in `debug_menu.sh`

## Troubleshooting

Common issues and solutions:

1. **Docker Not Available**
   - Check Docker service status
   - Verify Docker socket permissions
   - Check environment variables (DOCKER_HOST)

2. **Log Files Not Readable**
   - Verify file permissions
   - Check log directory existence
   - Ensure correct file paths in configuration

3. **Containers Not Accessible**
   - Verify container identifiers
   - Check Docker Compose status
   - Ensure Docker daemon is running

## Development Guidelines

When extending the system:

1. **Code Standards**:
   - Follow PEP 8
   - Use type hints for parameters and return values
   - Include docstrings for all classes and functions
   - Follow Clean Code principles

2. **Testing**:
   - Write unit tests for new functionality
   - Test in isolated environment before integration
   - Verify backward compatibility

3. **Documentation**:
   - Update docstrings
   - Update README with new features
   - Document configuration changes

## Security Considerations

The debug system has access to sensitive information and powerful operations:

1. **Docker Socket Access**:
   - The system requires access to Docker socket for container management
   - This grants significant privileges and should be restricted

2. **Log Content**:
   - Logs may contain sensitive information
   - Consider implementing log sanitization

3. **Authentication**:
   - Consider adding authentication for debug operations
   - Implement role-based access control for different operations

## Future Enhancements

Potential improvements to the debug system:

1. **Web Interface**:
   - Develop a web-based UI for easier interaction
   - Add visualizations for error patterns and trends

2. **Advanced Analytics**:
   - Implement machine learning for anomaly detection
   - Add predictive maintenance capabilities

3. **Integration**:
   - Connect with external monitoring systems
   - Add alerting capabilities

4. **Expanded Coverage**:
   - Support additional services and containers
   - Add network traffic monitoring

## Conclusion

The debug system provides a comprehensive solution for log management, analysis, and container operations in the Agricultural Product Management System. Its modular architecture ensures maintainability and extensibility, while the interactive interfaces make it accessible to developers and operations staff.
