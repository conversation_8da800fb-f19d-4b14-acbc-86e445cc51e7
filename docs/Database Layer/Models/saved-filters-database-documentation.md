# Saved Filters Database Documentation

## Overview

This document describes the database schema, relationships, and data management for the saved filters feature in the Agricultural Product Management System. The saved filters functionality allows users to persist their filter configurations for efficient reuse.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Table Relationships](#table-relationships)
3. [Data Model](#data-model)
4. [Indexes and Performance](#indexes-and-performance)
5. [Migration History](#migration-history)
6. [Data Validation](#data-validation)
7. [Query Patterns](#query-patterns)
8. [Maintenance Operations](#maintenance-operations)

## Database Schema

### user_saved_filters Table

The primary table for storing saved filter configurations.

```sql
CREATE TABLE user_saved_filters (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    filter_type VARCHAR(50) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    filter_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

#### Column Specifications

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | SERIAL | PRIMARY KEY | Auto-incrementing unique identifier |
| `user_id` | INTEGER | NOT NULL, FOREIGN KEY | Reference to owning user |
| `name` | VARCHAR(100) | NOT NULL | Human-readable filter name |
| `description` | VARCHAR(255) | NULLABLE | Optional detailed description |
| `filter_type` | VARCHAR(50) | NOT NULL | Type categorization (e.g., "offer") |
| `is_default` | BOOLEAN | NOT NULL, DEFAULT FALSE | Default filter flag |
| `filter_data` | JSONB | NOT NULL | Serialized filter configuration |
| `created_at` | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT NOW() | Creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT NOW() | Last modification timestamp |

#### Constraints and Indexes

```sql
-- Primary key
ALTER TABLE user_saved_filters ADD CONSTRAINT pk_user_saved_filters PRIMARY KEY (id);

-- Foreign key constraint
ALTER TABLE user_saved_filters ADD CONSTRAINT fk_user_saved_filters_user_id 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Indexes for performance
CREATE INDEX idx_user_saved_filters_user_id ON user_saved_filters(user_id);
CREATE INDEX idx_user_saved_filters_filter_type ON user_saved_filters(filter_type);
CREATE INDEX idx_user_saved_filters_is_default ON user_saved_filters(is_default);
CREATE INDEX idx_user_saved_filters_created_at ON user_saved_filters(created_at);

-- Composite index for default filter queries
CREATE INDEX idx_user_saved_filters_user_type_default 
    ON user_saved_filters(user_id, filter_type, is_default);

-- Unique constraint for default filters (only one default per user per type)
CREATE UNIQUE INDEX idx_user_saved_filters_unique_default 
    ON user_saved_filters(user_id, filter_type) 
    WHERE is_default = true;
```

## Table Relationships

### Entity Relationship Diagram

```
┌─────────────────┐         ┌─────────────────────────┐
│     users       │         │  user_saved_filters     │
├─────────────────┤         ├─────────────────────────┤
│ id (PK)         │◄────────┤ id (PK)                 │
│ email           │         │ user_id (FK)            │
│ password_hash   │         │ name                    │
│ role            │         │ description             │
│ ...             │         │ filter_type             │
└─────────────────┘         │ is_default              │
                            │ filter_data (JSONB)     │
                            │ created_at              │
                            │ updated_at              │
                            └─────────────────────────┘
```

### Relationship Details

1. **Users to SavedFilters**: One-to-Many
   - **Cardinality**: 1:N
   - **Referential Integrity**: CASCADE DELETE
   - **Business Rule**: Each user can have multiple saved filters

2. **Default Filter Constraint**: 
   - **Rule**: Only one default filter per user per filter type
   - **Implementation**: Unique partial index
   - **Business Logic**: Managed by application layer

## Data Model

### SQLAlchemy Model Definition

```python
class UserSavedFilter(Base, TimestampMixin):
    """
    User saved filter configurations for the offer management page
    """
    __tablename__ = "user_saved_filters"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    filter_type = Column(String(50), nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    filter_data = Column(JSON, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="saved_filters")
```

### User Model Extension

```python
class User(Base, TimestampMixin):
    # ... existing fields ...
    
    # Relationships
    saved_filters = relationship(
        "UserSavedFilter", 
        back_populates="user", 
        cascade="all, delete-orphan"
    )
```

## Indexes and Performance

### Index Strategy

1. **Primary Access Patterns**:
   - Get all filters for a user: `user_id`
   - Get filters by type: `filter_type`
   - Get default filters: `is_default`

2. **Composite Indexes**:
   - User + Type + Default: Optimizes default filter queries
   - User + Created: Optimizes chronological listing

3. **JSONB Indexes**:
   ```sql
   -- Index for filter_data queries (if needed)
   CREATE INDEX idx_user_saved_filters_filter_data_gin 
       ON user_saved_filters USING GIN (filter_data);
   ```

### Query Performance Considerations

| Query Type | Index Used | Performance |
|------------|------------|-------------|
| Get user filters | `idx_user_saved_filters_user_id` | O(log n) |
| Get default filter | `idx_user_saved_filters_user_type_default` | O(1) |
| Filter by type | `idx_user_saved_filters_filter_type` | O(log n) |
| Recent filters | `idx_user_saved_filters_created_at` | O(log n) |

## Migration History

### Initial Migration (acb08797414a_add_user_saved_filters_table.py)

```python
"""add_user_saved_filters_table

Revision ID: acb08797414a
Revises: 0be44b9f90e6
Create Date: 2025-05-21 22:01:45.123456

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'acb08797414a'
down_revision = '0be44b9f90e6'
branch_labels = None
depends_on = None

def upgrade():
    # Create user_saved_filters table
    op.create_table('user_saved_filters',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.Column('filter_type', sa.String(length=50), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=False),
        sa.Column('filter_data', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_saved_filters_id'), 'user_saved_filters', ['id'], unique=False)

def downgrade():
    op.drop_index(op.f('ix_user_saved_filters_id'), table_name='user_saved_filters')
    op.drop_table('user_saved_filters')
```

### Migration Application

```bash
# Generate migration
alembic revision --autogenerate -m "add_user_saved_filters_table"

# Apply migration
alembic upgrade head

# Verify migration
psql -d termelo_db -c "SELECT * FROM user_saved_filters LIMIT 1;"
```

## Data Validation

### Database-Level Validation

1. **NOT NULL Constraints**: Enforced on required fields
2. **Foreign Key Constraints**: Ensures referential integrity
3. **Unique Constraints**: Prevents duplicate default filters
4. **JSON Validation**: PostgreSQL validates JSON structure

### Application-Level Validation

```python
def validate_filter_data(filter_data: Dict[str, Any], filter_type: str) -> None:
    """Validate filter data structure"""
    
    # Basic JSON validation
    try:
        json.loads(json.dumps(filter_data))
    except (TypeError, ValueError) as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid filter data format: {str(e)}"
        )
    
    # Type-specific validation
    if filter_type == "offer":
        validate_offer_filter_data(filter_data)

def validate_offer_filter_data(filter_data: Dict[str, Any]) -> None:
    """Validate offer-specific filter data"""
    
    # Required keys
    if "basic_filters" not in filter_data:
        raise HTTPException(
            status_code=400,
            detail="Missing required key: basic_filters"
        )
    
    # Structure validation
    if not isinstance(filter_data["basic_filters"], dict):
        raise HTTPException(
            status_code=400,
            detail="basic_filters must be an object"
        )
    
    # Additional validations...
```

## Query Patterns

### Common Query Examples

#### 1. Get User's Saved Filters

```sql
SELECT * FROM user_saved_filters 
WHERE user_id = $1 
  AND filter_type = $2 
ORDER BY is_default DESC, name ASC;
```

#### 2. Get Default Filter for User

```sql
SELECT * FROM user_saved_filters 
WHERE user_id = $1 
  AND filter_type = $2 
  AND is_default = true 
LIMIT 1;
```

#### 3. Set New Default Filter

```sql
-- First, unset existing default
UPDATE user_saved_filters 
SET is_default = false, updated_at = NOW() 
WHERE user_id = $1 AND filter_type = $2 AND is_default = true;

-- Then set new default
UPDATE user_saved_filters 
SET is_default = true, updated_at = NOW() 
WHERE id = $3;
```

#### 4. Filter Statistics Query

```sql
SELECT 
    filter_type,
    COUNT(*) as total_filters,
    COUNT(CASE WHEN is_default THEN 1 END) as default_filters,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_age_days
FROM user_saved_filters 
WHERE user_id = $1 
GROUP BY filter_type;
```

### SQLAlchemy Query Examples

```python
# Get user's filters
filters = db.query(UserSavedFilter)\
    .filter(UserSavedFilter.user_id == user_id)\
    .filter(UserSavedFilter.filter_type == filter_type)\
    .order_by(UserSavedFilter.is_default.desc(), UserSavedFilter.name)\
    .all()

# Get default filter
default_filter = db.query(UserSavedFilter)\
    .filter(
        UserSavedFilter.user_id == user_id,
        UserSavedFilter.filter_type == filter_type,
        UserSavedFilter.is_default == True
    )\
    .first()

# Complex filter data query
filters_with_status = db.query(UserSavedFilter)\
    .filter(UserSavedFilter.user_id == user_id)\
    .filter(UserSavedFilter.filter_data['basic_filters']['status'].astext == 'CREATED')\
    .all()
```

## Maintenance Operations

### Regular Maintenance Tasks

#### 1. Cleanup Old Unused Filters

```sql
-- Identify old unused filters (not accessed in 90 days)
SELECT id, name, created_at 
FROM user_saved_filters 
WHERE updated_at < NOW() - INTERVAL '90 days'
  AND is_default = false;

-- Delete old unused filters (consider moving to archive first)
DELETE FROM user_saved_filters 
WHERE updated_at < NOW() - INTERVAL '90 days'
  AND is_default = false;
```

#### 2. Data Integrity Checks

```sql
-- Check for orphaned filters (users that don't exist)
SELECT COUNT(*) 
FROM user_saved_filters usf
LEFT JOIN users u ON usf.user_id = u.id
WHERE u.id IS NULL;

-- Check for multiple default filters per user/type
SELECT user_id, filter_type, COUNT(*) 
FROM user_saved_filters 
WHERE is_default = true 
GROUP BY user_id, filter_type 
HAVING COUNT(*) > 1;

-- Check for invalid JSON in filter_data
SELECT id, name 
FROM user_saved_filters 
WHERE filter_data IS NULL 
   OR NOT (filter_data::text ~ '^{.*}$');
```

#### 3. Performance Monitoring

```sql
-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'user_saved_filters';

-- Check table statistics
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup
FROM pg_stat_user_tables 
WHERE tablename = 'user_saved_filters';
```

### Backup and Recovery

#### 1. Table-Specific Backup

```bash
# Backup saved filters table
pg_dump -h localhost -U postgres -d termelo_db \
    --table=user_saved_filters \
    --data-only \
    --inserts > user_saved_filters_backup.sql

# Restore saved filters table
psql -h localhost -U postgres -d termelo_db < user_saved_filters_backup.sql
```

#### 2. User-Specific Export

```sql
-- Export specific user's filters
COPY (
    SELECT * FROM user_saved_filters 
    WHERE user_id = 123
) TO '/tmp/user_123_filters.csv' WITH CSV HEADER;
```

### Optimization Recommendations

1. **Regular ANALYZE**: Update table statistics
   ```sql
   ANALYZE user_saved_filters;
   ```

2. **Monitor JSON queries**: Consider additional GIN indexes for complex JSON queries

3. **Partition consideration**: For large datasets, consider partitioning by user_id

4. **Archive strategy**: Implement archiving for old unused filters

---

*This documentation is part of the Agricultural Product Management System documentation suite. For related documentation, see the main [DOCS_INDEX.md](../../DOCS_INDEX.md).*