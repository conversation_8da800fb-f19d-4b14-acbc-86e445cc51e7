# Database Documentation for Agricultural Product Management System

## SQLAlchemy ORM

### Model Definitions

The application uses several interconnected models to represent the agricultural product management system entities.

#### User Models

##### User
```python
class User(Base, TimestampMixin):
    """User model - System users (producers, operators, admins)"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)  # "termelő", "ügyintéző", "admin"
    company_name = Column(String(255))
    tax_id = Column(String(50))
    contact_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=False, nullable=False)
    activation_token = Column(String(255))
    
    # Relationships
    offers = relationship("Offer", foreign_keys="Offer.user_id", back_populates="user")
    created_offers = relationship("Offer", foreign_keys="Offer.created_by_user_id", back_populates="created_by_user")
    default_settings = relationship("UserDefaultSettings", back_populates="user", uselist=False)
    password_reset_tokens = relationship("PasswordResetToken", back_populates="user")
    offer_logs = relationship("OfferLog", foreign_keys="OfferLog.changed_by", back_populates="user")
    notifications = relationship("Notification", back_populates="user")
```

##### UserDefaultSettings
```python
class UserDefaultSettings(Base, TimestampMixin):
    """User default settings - Frequently used default values for users"""
    __tablename__ = "user_default_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    default_product_type_id = Column(Integer, ForeignKey("product_types.id"))
    default_quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    default_quantity_unit = Column(String(10), default="kg", nullable=False)  # 'kg' or 'tonna'
    default_product_type_name = Column(String(100), nullable=True)
    default_quality_grade_name = Column(String(100), nullable=True)
    default_category_id = Column(Integer, nullable=True)
    default_category_name = Column(String(100), nullable=True)
    has_quality_grades = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="default_settings")
    default_product_type = relationship("ProductType")
    default_quality_grade = relationship("QualityGrade")
```

##### PasswordResetToken
```python
class PasswordResetToken(Base, TimestampMixin):
    """Password reset token"""
    __tablename__ = "password_reset_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token = Column(String(255), nullable=False, unique=True)
    expires_at = Column(DateTime, nullable=False, default=lambda: func.now())
    is_used = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="password_reset_tokens")
```

#### Product Models

##### ProductCategory
```python
class ProductCategory(Base, TimestampMixin):
    """Product category model - Main categories (Paprika, Tomato, etc.)"""
    __tablename__ = "product_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    
    # Relationships
    product_types = relationship("ProductType", back_populates="category")
```

##### ProductType
```python
class ProductType(Base, TimestampMixin):
    """Product type model - Subcategories (TV paprika, Kápia paprika, etc.)"""
    __tablename__ = "product_types"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, ForeignKey("product_categories.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    has_quality_grades = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    category = relationship("ProductCategory", back_populates="product_types")
    quality_grades = relationship("QualityGrade", back_populates="product_type")
    offers = relationship("Offer", back_populates="product_type")
```

##### QualityGrade
```python
class QualityGrade(Base, TimestampMixin):
    """Quality grade model - Quality classes (Extra, I. class, II. class, etc.)"""
    __tablename__ = "quality_grades"
    
    id = Column(Integer, primary_key=True, index=True)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    name = Column(String(50), nullable=False)
    min_shoulder_diameter = Column(Numeric(5, 2))
    max_shoulder_diameter = Column(Numeric(5, 2))
    min_length = Column(Numeric(5, 2))
    max_length = Column(Numeric(5, 2))
    description = Column(Text)
    
    # Relationships
    product_type = relationship("ProductType", back_populates="quality_grades")
    offers = relationship("Offer", back_populates="quality_grade")
```

#### Offer Models

##### Offer
```python
class Offer(Base, TimestampMixin):
    """Offer model - Producer offers data"""
    __tablename__ = "offers"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    quality_grade_id = Column(Integer, ForeignKey("quality_grades.id"))
    
    quantity_in_kg = Column(Numeric(10, 2), nullable=False)
    delivery_date = Column(Date, nullable=False)
    
    status = Column(
        String(50), 
        nullable=False,
        default="CREATED",
    )
    
    confirmed_quantity = Column(Numeric(10, 2))
    confirmed_price = Column(Numeric(10, 2))
    note = Column(Text)
    
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="offers")
    created_by_user = relationship("User", foreign_keys=[created_by_user_id], back_populates="created_offers")
    product_type = relationship("ProductType", back_populates="offers")
    quality_grade = relationship("QualityGrade", back_populates="offers")
    logs = relationship("OfferLog", back_populates="offer")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('quantity_in_kg > 0', name='check_quantity_positive'),
        CheckConstraint(
            "status IN ('CREATED', 'CONFIRMED_BY_COMPANY', 'ACCEPTED_BY_USER', 'REJECTED_BY_USER', 'FINALIZED')",
            name='check_status_values'
        ),
        CheckConstraint('confirmed_quantity IS NULL OR confirmed_quantity > 0', name='check_confirmed_quantity'),
        CheckConstraint('confirmed_price IS NULL OR confirmed_price > 0', name='check_confirmed_price'),
        {'sqlite_autoincrement': True},
    )
```

##### OfferLog
```python
class OfferLog(Base, TimestampMixin):
    """Offer log model - Logging status changes for offers"""
    __tablename__ = "offer_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    offer_id = Column(Integer, ForeignKey("offers.id"), nullable=False)
    old_status = Column(String(50))
    new_status = Column(String(50), nullable=False)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    note = Column(Text)
    
    # Relationships
    offer = relationship("Offer", back_populates="logs")
    user = relationship("User", foreign_keys=[changed_by], back_populates="offer_logs")
```

#### Notification Model

##### Notification
```python
class Notification(Base, TimestampMixin):
    """Notification model - User notifications"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    type = Column(String(50), nullable=False)
    message = Column(String(255), nullable=False)
    detail = Column(Text)
    is_read = Column(Boolean, nullable=False, default=False)
    target_roles = Column(String(255))
    related_entity_type = Column(String(50))
    related_entity_id = Column(Integer)

    # Relationships
    user = relationship("User", back_populates="notifications")
```

#### Common Mixin Class

##### TimestampMixin
```python
class TimestampMixin:
    """Mixin for created_at and updated_at fields in all models"""
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
```

### Database Connection

The database connection is configured through environment variables in the Docker Compose configuration. The application uses pydantic-settings to load these variables into a Settings object.

#### Connection Configuration in settings.py
```python
class Settings(BaseSettings):
    # Database settings
    DATABASE_URL: Optional[PostgresDsn] = None
    DB_HOST: str
    DB_PORT: int
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str) and v:
            return v
            
        # Use safe default values
        host = values.get("DB_HOST", "db")
        port = values.get("DB_PORT", 5432)
        user = values.get("DB_USER", "postgres")
        password = values.get("DB_PASSWORD", "postgres")
        db = values.get("DB_NAME", "termelo_db")
        
        return f"postgresql://{user}:{password}@{host}:{port}/{db}"
```

#### Connection Format for PostgreSQL
The PostgreSQL connection string follows the format:
```
postgresql://username:password@host:port/database_name
```

#### Database Engine and Session
In `app/db/session.py`, the SQLAlchemy engine and session are set up:

```python
# Create database engine from configured URL
engine = create_engine(
    str(settings.DATABASE_URL),
    pool_pre_ping=True,  # Prevents "connection has gone away" errors
)

# Create session factory
# autocommit=False: Transactions must be committed manually 
# autoflush=False: Does not automatically flush before every query
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """
    Database session dependency for FastAPI routes
    Ensures session is closed after request completion
    
    Yields:
        SQLAlchemy session object
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

## Alembic Migrations

### Migration Workflow

The application uses Alembic for database schema migrations, configured in the `migrations` directory.

#### How to Generate a New Migration Script
```bash
alembic revision --autogenerate -m "description of changes"
```
This command analyzes the differences between the current database schema and the SQLAlchemy models, generating a migration script automatically.

#### How to Apply Migrations
```bash
alembic upgrade head
```
This applies all pending migrations to bring the database schema up to the latest version.

#### How to Downgrade Migrations
```bash
# Downgrade by one migration
alembic downgrade -1

# Downgrade to a specific revision
alembic downgrade revision_id
```

#### How to Check the Current Database Revision
```bash
alembic current
```

### Migration File Structure

The migration system is organized as follows:
- `migrations/env.py`: Configuration for the migration environment
- `migrations/script.py.mako`: Template for generating migration scripts
- `migrations/versions/`: Directory containing individual migration files
- `alembic.ini`: Main configuration file for Alembic

Individual migration files follow a standard format:
```python
"""Migration description

Revision ID: unique_identifier
Revises: previous_revision_id
Create Date: timestamp

"""
# imports

# revision identifiers
revision = 'unique_identifier'
down_revision = 'previous_revision_id'

def upgrade() -> None:
    # Commands to apply the migration
    pass

def downgrade() -> None:
    # Commands to revert the migration
    pass
```

### Important Migrations

The application includes an initial migration that sets up the Notifications table in `migrations/versions/c1f4d9a2e5b7_add_notifications_table.py`:

```python
def upgrade() -> None:
    # Create notifications table
    op.create_table('notifications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('message', sa.String(length=255), nullable=False),
        sa.Column('detail', sa.Text(), nullable=True),
        sa.Column('is_read', sa.Boolean(), nullable=False, default=False),
        sa.Column('target_roles', sa.String(length=255), nullable=True),
        sa.Column('related_entity_type', sa.String(length=50), nullable=True),
        sa.Column('related_entity_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()'), onupdate=sa.text('now()')),
        sa.CheckConstraint("type IN ('info', 'success', 'warning', 'error', 'update')", name='check_notification_type'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
```

## PostgreSQL Specifics

### Database Initialization

The system uses an `init-db.sh` script to automatically initialize the database on container startup:

```bash
#!/bin/bash
# Wait for database to be available
until PGPASSWORD=postgres psql -h db -U postgres -d termelo_db -c '\q'; do
  echo "Waiting for database..."
  sleep 2
done

# Create migration directories if they don't exist
mkdir -p migrations/versions

# Create migration templates if they don't exist
# [template creation code]

# Run migrations
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

### Database Population

The application includes a system to initialize default data in `app/db/init_db.py`. This creates:
1. Default admin user
2. Default producer and operator users
3. Default product categories, types, and quality grades

```python
def init_system_data(db: Session) -> None:
    # Create admin user
    create_admin_user(db)

    # Create default producer and operator users
    create_default_users(db)
    
    # Create product categories, types and quality grades
    create_product_data(db)
```

### Connection Configuration in Docker Compose

The database connection is configured in the `docker-compose.yml` file:

```yaml
db:
  image: postgres:15
  container_name: termelo-db
  restart: always
  environment:
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    POSTGRES_DB: termelo_db
  volumes:
    - postgres_data:/var/lib/postgresql/data
  ports:
    - "5433:5432"
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U postgres"]
    interval: 5s
    timeout: 5s
    retries: 5
```

The backend service is configured to depend on the database service, ensuring proper startup order:

```yaml
backend:
  # [other configuration]
  environment:
    # [other environment variables]
    DATABASE_URL: **************************************/termelo_db
    DB_HOST: db
    DB_PORT: 5432
    DB_USER: postgres
    DB_PASSWORD: postgres
    DB_NAME: termelo_db
  depends_on:
    db:
      condition: service_healthy
```

This configuration ensures that the database is ready before the application attempts to connect to it.