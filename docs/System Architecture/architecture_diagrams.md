# Software System Architecture Diagrams

## 1. Overall System Architecture Diagram

```mermaid
graph TD
    User[Client/User]
    StreamlitFE[Streamlit Frontend Container]
    FastAPIBE[FastAPI Backend Container]
    PostgresDB[PostgreSQL DB Container]
    DebugTools[Debug Tools Container]
    
    User -->|Interacts with| StreamlitFE
    StreamlitFE -->|API Requests| FastAPIBE
    FastAPIBE -->|SQL Queries| PostgresDB
    FastAPIBE -->|Returns Data| StreamlitFE
    DebugTools -->|Monitors| StreamlitFE
    DebugTools -->|Monitors| FastAPIBE
    DebugTools -->|Monitors| PostgresDB
    
    subgraph Docker Compose Network
        StreamlitFE
        FastAPIBE
        PostgresDB
        DebugTools
    end
    
    classDef container fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    classDef database fill:#f68d2e,stroke:#fff,stroke-width:1px,color:#fff;
    classDef client fill:#68bc71,stroke:#fff,stroke-width:1px,color:#fff;
    
    class StreamlitFE,FastAPIBE,DebugTools container;
    class PostgresDB database;
    class User client;
```

## 2. FastAPI Backend Layered Architecture Diagram

```mermaid
graph TD
    subgraph API Layer
        Endpoints[API Endpoints]
        Dependencies[API Dependencies]
        Routing[Router Configuration]
        RequestValidation[Request Validation]
        ResponseFormatting[Response Formatting]
    end
    
    subgraph Service Layer
        BusinessLogic[Business Logic]
        OfferService[Offer Service]
        UserService[User Service]
        ProductService[Product Service]
        Validation[Business Validation]
    end
    
    subgraph Data Access Layer
        ORM[SQLAlchemy ORM]
        Models[Database Models]
        CRUD[CRUD Operations]
        QueryConstruction[Query Construction]
        Transactions[Transaction Management]
    end
    
    subgraph Infrastructure Layer
        Config[Configuration Module]
        SessionManagement[DB Session Management]
        Security[Security Utilities]
        Email[Email Services]
        Logging[Logging Configuration]
    end
    
    API Layer --> Service Layer
    Service Layer --> Data Access Layer
    Data Access Layer --> Infrastructure Layer
    
    classDef layerStyle fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    class API,Service,Data,Infrastructure layerStyle;
```

## 3. Streamlit Frontend Architecture Diagram

```mermaid
graph TD
    subgraph Pages
        AdminPages[Admin Pages]
        AuthPages[Auth Pages]
        OperatorPages[Operator Pages]
        ProducerPages[Producer Pages]
    end
    
    subgraph Components
        AuthForms[Auth Forms]
        DataDisplay[Data Display]
        MultiStepForm[Multi-Step Form]
        Notification[Notification]
        Sidebar[Sidebar]
    end
    
    subgraph API_Clients
        AuthAPI[Auth API Client]
        OffersAPI[Offers API Client]
        ProductsAPI[Products API Client]
        UsersAPI[Users API Client]
    end
    
    subgraph Utils
        ApiClient[API Client Utilities]
        Formatting[Formatting Utilities]
        Session[Session Management]
        Responsive[Responsive UI Utilities]
        Validators[Form Validators]
    end
    
    subgraph Config
        AppConfig[Application Configuration]
        MenuConfig[Menu Configuration]
    end
    
    Pages --> Components
    Pages --> API_Clients
    Components --> Utils
    API_Clients --> Utils
    Pages --> Utils
    Components --> Config
    API_Clients --> Config
    
    classDef moduleStyle fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    class Pages,Components,API_Clients,Utils,Config moduleStyle;
```

## 4. API Request/Response Flow (Sequence Diagram)

```mermaid
sequenceDiagram
    participant User as User
    participant UI as Streamlit UI Component
    participant API_Client as Streamlit API Client
    participant Router as FastAPI Router
    participant Auth as JWT Authentication
    participant Validation as Pydantic Validation
    participant Service as Service Function
    participant ORM as SQLAlchemy ORM
    participant DB as PostgreSQL Database
    
    User->>UI: Interact with component
    UI->>API_Client: Request data
    API_Client->>Router: HTTP Request
    Router->>Auth: Validate JWT token
    Auth-->>Router: Token valid
    Router->>Validation: Validate request data
    Validation-->>Router: Data valid
    Router->>Service: Execute business logic
    Service->>ORM: Database query
    ORM->>DB: SQL query
    DB-->>ORM: Query results
    ORM-->>Service: ORM objects
    Service->>Validation: Validate response data
    Validation-->>Service: Data validated
    Service-->>Router: Return result
    Router-->>API_Client: HTTP Response
    API_Client-->>UI: Processed data
    UI-->>User: Updated view
```

## 5. User Authentication Flow (Sequence Diagram)

```mermaid
sequenceDiagram
    participant User as User
    participant LoginPage as Streamlit Login Page
    participant APIClient as Streamlit API Client
    participant AuthEndpoint as Backend /auth/login
    participant JWT as JWT Service
    participant DB as User Table in Database
    
    User->>LoginPage: Enter credentials
    LoginPage->>APIClient: Send login request
    APIClient->>AuthEndpoint: POST /auth/login
    AuthEndpoint->>DB: Verify credentials
    DB-->>AuthEndpoint: User exists
    AuthEndpoint->>JWT: Generate token
    JWT-->>AuthEndpoint: JWT token
    AuthEndpoint-->>APIClient: Return token + user data
    APIClient->>LoginPage: Store in session state
    LoginPage-->>User: Redirect to dashboard
```

## 6. Database Schema (Entity Relationship Diagram)

```mermaid
erDiagram
    User ||--o{ Offer : "creates"
    User ||--o{ OfferLog : "changes"
    User ||--o{ Notification : "receives"
    User ||--|| UserDefaultSettings : "has"
    User ||--o{ PasswordResetToken : "requests"
    
    ProductCategory ||--o{ ProductType : "contains"
    ProductType ||--o{ QualityGrade : "has"
    ProductType ||--o{ Offer : "is offered"
    QualityGrade ||--o{ Offer : "qualifies"
    
    Offer ||--o{ OfferLog : "logs changes"
    
    User {
        int id PK
        string email UK
        string password_hash
        string role
        string company_name
        string tax_id
        string contact_name
        string phone_number
        boolean is_active
        string activation_token
        datetime created_at
        datetime updated_at
    }
    
    UserDefaultSettings {
        int id PK
        int user_id FK
        int default_product_type_id FK
        int default_quality_grade_id FK
        string default_quantity_unit
        string default_product_type_name
        string default_quality_grade_name
        int default_category_id
        string default_category_name
        boolean has_quality_grades
    }
    
    PasswordResetToken {
        int id PK
        int user_id FK
        string token
        datetime expires_at
        boolean is_used
    }
    
    ProductCategory {
        int id PK
        string name
        text description
    }
    
    ProductType {
        int id PK
        int category_id FK
        string name
        text description
        boolean has_quality_grades
    }
    
    QualityGrade {
        int id PK
        int product_type_id FK
        string name
        decimal min_shoulder_diameter
        decimal max_shoulder_diameter
        decimal min_length
        decimal max_length
        text description
    }
    
    Offer {
        int id PK
        int user_id FK
        int product_type_id FK
        int quality_grade_id FK
        decimal quantity_in_kg
        date delivery_date
        string status
        decimal confirmed_quantity
        decimal confirmed_price
        text note
        int created_by_user_id FK
    }
    
    OfferLog {
        int id PK
        int offer_id FK
        string old_status
        string new_status
        int changed_by FK
        text note
    }
    
    Notification {
        int id PK
        int user_id FK
        string type
        string message
        text detail
        boolean is_read
        string target_roles
        string related_entity_type
        int related_entity_id
    }
```

## 7. Docker Compose Service Orchestration Diagram

```mermaid
graph LR
    subgraph Docker Compose Services
        db[db<br>postgres:15]
        backend[backend<br>Custom FastAPI Image]
        streamlit[streamlit<br>Custom Streamlit Image]
        debug[debug<br>Custom Debug Tools Image]
    end
    
    subgraph Volumes
        postgres_data[(postgres_data)]
        app_mount[App Source Code]
    end
    
    subgraph Network
        termelo_network{termelo-network}
    end
    
    db --> postgres_data
    backend --> app_mount
    streamlit --> app_mount
    debug --> app_mount
    
    db --> termelo_network
    backend --> termelo_network
    streamlit --> termelo_network
    debug --> termelo_network
    
    debug -.-> |monitors| backend
    debug -.-> |monitors| streamlit
    debug -.-> |monitors| db
    
    backend -.-> |depends on| db
    streamlit -.-> |depends on| backend
    debug -.-> |depends on| streamlit
    
    classDef service fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    classDef volume fill:#f68d2e,stroke:#fff,stroke-width:1px,color:#fff;
    classDef net fill:#68bc71,stroke:#fff,stroke-width:1px,color:#fff;
    
    class db,backend,streamlit,debug service;
    class postgres_data,app_mount volume;
    class termelo_network net;
```

## 8. Offer Status Transition Flow

```mermaid
stateDiagram-v2
    [*] --> CREATED: Producer creates offer
    
    CREATED --> CONFIRMED_BY_COMPANY: Operator confirms offer
    CREATED --> REJECTED_BY_USER: Producer rejects own offer
    
    CONFIRMED_BY_COMPANY --> ACCEPTED_BY_USER: Producer accepts offer
    CONFIRMED_BY_COMPANY --> REJECTED_BY_USER: Producer rejects offer
    
    ACCEPTED_BY_USER --> FINALIZED: Operator finalizes offer
    
    REJECTED_BY_USER --> CREATED: Create new offer
    
    FINALIZED --> [*]
    
    note right of CREATED
        Initial state when an offer is created
    end note
    
    note right of CONFIRMED_BY_COMPANY
        Operator confirms price and quantity
    end note
    
    note right of ACCEPTED_BY_USER
        Producer agrees to confirmed terms
    end note
    
    note right of REJECTED_BY_USER
        Producer disagrees with terms
    end note
    
    note right of FINALIZED
        Offer has been completed
    end note
```

## 9. Debug System Architecture Diagram

```mermaid
graph TD
    subgraph Debug System
        Logger[Logger Module]
        ContainerManager[Container Manager]
        LogAnalyzer[Log Analyzer]
        DockerComposeManager[Docker Compose Manager]
        CLI[CLI Interface]
        InteractiveMenu[Interactive Menu]
    end
    
    subgraph Application Containers
        Backend[Backend Container]
        Streamlit[Streamlit Container]
        Database[Database Container]
    end
    
    Logger --> ContainerManager
    Logger --> LogAnalyzer
    
    ContainerManager --> Backend
    ContainerManager --> Streamlit
    ContainerManager --> Database
    
    LogAnalyzer --> Logger
    
    DockerComposeManager --> ContainerManager
    
    CLI --> Logger
    CLI --> ContainerManager
    CLI --> LogAnalyzer
    CLI --> DockerComposeManager
    
    InteractiveMenu --> CLI
    
    classDef debugComponent fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    classDef appContainer fill:#f68d2e,stroke:#fff,stroke-width:1px,color:#fff;
    
    class Logger,ContainerManager,LogAnalyzer,DockerComposeManager,CLI,InteractiveMenu debugComponent;
    class Backend,Streamlit,Database appContainer;
```

## 10. Configuration System Overview Diagram

```mermaid
graph TD
    subgraph Environment Variables
        DockerEnv[Docker Compose Environment Variables]
        OSEnv[Operating System Environment Variables]
        DotEnv[.env File Variables]
    end
    
    subgraph Backend Configuration
        PydanticSettings[Pydantic BaseSettings]
        ConfigModule[config.py Module]
        Validators[Configuration Validators]
    end
    
    subgraph Frontend Configuration
        AppConfig[app_config.py]
        StreamlitConfig[.streamlit/config.toml]
        SessionConfig[Session State Config]
    end
    
    DockerEnv --> PydanticSettings
    OSEnv --> PydanticSettings
    DotEnv --> PydanticSettings
    
    PydanticSettings --> ConfigModule
    ConfigModule --> Validators
    
    DockerEnv --> AppConfig
    OSEnv --> AppConfig
    
    AppConfig --> SessionConfig
    StreamlitConfig --> SessionConfig
    
    ConfigModule -.-> |API Communication| AppConfig
    
    classDef envVars fill:#68bc71,stroke:#fff,stroke-width:1px,color:#fff;
    classDef backendConfig fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff;
    classDef frontendConfig fill:#f68d2e,stroke:#fff,stroke-width:1px,color:#fff;
    
    class DockerEnv,OSEnv,DotEnv envVars;
    class PydanticSettings,ConfigModule,Validators backendConfig;
    class AppConfig,StreamlitConfig,SessionConfig frontendConfig;
```
