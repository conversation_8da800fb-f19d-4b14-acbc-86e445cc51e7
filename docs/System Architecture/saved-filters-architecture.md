# Saved Filters System Architecture

## Overview

This document describes the comprehensive architecture of the saved filters feature in the Agricultural Product Management System. The saved filters functionality provides users with the ability to save, manage, and reuse complex filter configurations across sessions.

## Table of Contents

1. [System Architecture Overview](#system-architecture-overview)
2. [Component Architecture](#component-architecture)
3. [Data Flow Architecture](#data-flow-architecture)
4. [Database Architecture](#database-architecture)
5. [API Architecture](#api-architecture)
6. [Frontend Architecture](#frontend-architecture)
7. [Security Architecture](#security-architecture)
8. [Performance Architecture](#performance-architecture)
9. [Deployment Architecture](#deployment-architecture)
10. [Integration Points](#integration-points)

## System Architecture Overview

The saved filters feature follows a multi-layered architecture pattern that integrates seamlessly with the existing system:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │  Streamlit UI   │  │  React.js       │  │   Mobile    │ │
│  │   Components    │  │   Components    │  │    App      │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      API LAYER                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │    FastAPI      │  │  Authentication │  │   Rate      │ │
│  │   Endpoints     │  │   Middleware    │  │  Limiting   │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LOGIC LAYER                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │    Services     │  │   Validation    │  │  Business   │ │
│  │                 │  │     Logic       │  │    Rules    │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   DATA ACCESS LAYER                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │      CRUD       │  │   SQLAlchemy    │  │   Query     │ │
│  │   Operations    │  │      ORM        │  │ Optimization│ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    PERSISTENCE LAYER                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │   PostgreSQL    │  │     Redis       │  │   File      │ │
│  │    Database     │  │     Cache       │  │   Storage   │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Architectural Principles

1. **Separation of Concerns**: Each layer has distinct responsibilities
2. **Loose Coupling**: Layers communicate through well-defined interfaces
3. **High Cohesion**: Related functionality is grouped together
4. **Scalability**: Horizontal scaling capabilities built-in
5. **Maintainability**: Modular design for easy updates and debugging

## Component Architecture

### Backend Components

```
app/
├── models/
│   └── user_saved_filter.py          # Data model definition
├── schemas/
│   └── saved_filter.py               # Pydantic validation schemas
├── crud/
│   └── crud_user_saved_filter.py     # Database operations
├── services/
│   └── user_saved_filter_service.py  # Business logic
└── api/
    └── endpoints/
        └── saved_filters.py          # API endpoints
```

#### Component Responsibilities

| Component | Responsibility | Dependencies |
|-----------|----------------|--------------|
| **Models** | Data structure definition, ORM relationships | SQLAlchemy, Base classes |
| **Schemas** | Input/output validation, serialization | Pydantic, typing |
| **CRUD** | Database operations, query optimization | SQLAlchemy, models |
| **Services** | Business logic, validation, workflows | CRUD, schemas, models |
| **API** | HTTP endpoints, request handling | FastAPI, services, dependencies |

### Frontend Components

```
streamlit_app/
├── api/
│   └── saved_filters.py              # API client
├── pages/
│   └── operator/
│       └── offer_management/
│           └── saved_filter_ui.py    # UI components
└── utils/
    └── session.py                    # Session management
```

#### Component Interaction

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   UI Component  │◄──►│   API Client    │◄──►│  Session Utils  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │
│ Session State   │    │ HTTP Requests   │
│                 │    │                 │
└─────────────────┘    └─────────────────┘
```

## Data Flow Architecture

### Filter Creation Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │    │             │
│  User Input │───►│  Frontend   │───►│  API Layer  │───►│  Business   │
│             │    │ Validation  │    │ Validation  │    │   Logic     │
│             │    │             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                │
                                                                ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │    │             │
│  Database   │◄───│  CRUD Layer │◄───│ Data Access │◄───│ Persistence │
│   Storage   │    │             │    │             │    │   Layer     │
│             │    │             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Filter Retrieval Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │    │             │
│ User Request│───►│  Frontend   │───►│  API Layer  │───►│ Auth Check  │
│             │    │             │    │             │    │             │
│             │    │             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                │
                                                                ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │    │             │
│  Response   │◄───│  Frontend   │◄───│ Serialized  │◄───│  Database   │
│  to User    │    │ Processing  │    │   Data      │    │   Query     │
│             │    │             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Filter Application Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │
│ Filter Load │───►│  Session    │───►│  UI State   │
│   Request   │    │ State Update│    │   Update    │
│             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
                                                │
                                                ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│             │    │             │    │             │
│   UI        │◄───│  Data       │◄───│  Filter     │
│  Refresh    │    │ Filtering   │    │ Application │
│             │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
```

## Database Architecture

### Table Structure

```sql
-- Primary table for saved filters
CREATE TABLE user_saved_filters (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    filter_type VARCHAR(50) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    filter_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### Indexing Strategy

```sql
-- Performance indexes
CREATE INDEX idx_user_saved_filters_user_id ON user_saved_filters(user_id);
CREATE INDEX idx_user_saved_filters_filter_type ON user_saved_filters(filter_type);
CREATE INDEX idx_user_saved_filters_is_default ON user_saved_filters(is_default);

-- Composite index for complex queries
CREATE INDEX idx_user_saved_filters_user_type_default 
    ON user_saved_filters(user_id, filter_type, is_default);

-- Unique constraint for default filters
CREATE UNIQUE INDEX idx_user_saved_filters_unique_default 
    ON user_saved_filters(user_id, filter_type) 
    WHERE is_default = true;

-- JSONB index for filter data queries
CREATE INDEX idx_user_saved_filters_filter_data_gin 
    ON user_saved_filters USING GIN (filter_data);
```

### Relationships

```
┌─────────────────┐         ┌─────────────────────────┐
│     users       │         │  user_saved_filters     │
├─────────────────┤         ├─────────────────────────┤
│ id (PK)         │◄────────┤ id (PK)                 │
│ email           │  1:N    │ user_id (FK)            │
│ role            │         │ name                    │
│ ...             │         │ filter_data (JSONB)     │
└─────────────────┘         │ is_default              │
                            │ created_at              │
                            │ updated_at              │
                            └─────────────────────────┘
```

## API Architecture

### RESTful Endpoint Design

```
GET    /api/saved-filters/              # List user's filters
GET    /api/saved-filters/{id}          # Get specific filter
POST   /api/saved-filters/              # Create new filter
PUT    /api/saved-filters/{id}          # Update existing filter
DELETE /api/saved-filters/{id}          # Delete filter
POST   /api/saved-filters/{id}/set-default  # Set as default
```

### Request/Response Patterns

#### Request Structure

```json
{
  "name": "string",
  "description": "string | null",
  "filter_type": "string",
  "is_default": "boolean",
  "filter_data": {
    "basic_filters": { ... },
    "column_filters": [ ... ],
    "complex_filters": { ... },
    "search_query": "string",
    "sort_fields": [ ... ]
  }
}
```

#### Response Structure

```json
{
  "id": "integer",
  "user_id": "integer",
  "name": "string",
  "description": "string | null",
  "filter_type": "string",
  "is_default": "boolean",
  "filter_data": { ... },
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Error Handling Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Input Error    │───►│  Validation     │───►│  400 Bad        │
│                 │    │   Error         │    │   Request       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Auth Error     │───►│  Authentication │───►│  401 Unauthorized│
│                 │    │   Error         │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Permission Error│───►│  Authorization  │───►│  403 Forbidden  │
│                 │    │   Error         │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Frontend Architecture

### Component Hierarchy

```
SavedFiltersManagement
├── QuickAccessBar
│   ├── FilterChip[]
│   └── ManageButton
├── SaveFilterDialog
│   ├── FilterForm
│   │   ├── NameInput
│   │   ├── DescriptionInput
│   │   └── DefaultCheckbox
│   └── ActionButtons
└── ManageFiltersModal
    ├── SaveCurrentSection
    ├── FilterList
    │   └── FilterItem[]
    │       ├── FilterInfo
    │       └── ActionButtons
    └── EditForm
```

### State Management Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Component State │───►│ Session State   │───►│ API State       │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  UI Updates     │    │ Filter Config   │    │ HTTP Requests   │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Responsive Design Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│    Desktop      │    │     Tablet      │    │     Mobile      │
│   (>1200px)     │    │   (768-1200px)  │    │    (<768px)     │
│                 │    │                 │    │                 │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ Full UI         │    │ Compact UI      │    │ Minimal UI      │
│ All features    │    │ Hidden details  │    │ Essential only  │
│ Side panels     │    │ Collapsed menus │    │ Stacked layout  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Security Architecture

### Authentication Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ User Request    │───►│ JWT Token       │───►│ Token           │
│                 │    │ Extraction      │    │ Validation      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Request         │◄───│ User Context    │◄───│ User            │
│ Processing      │    │ Injection       │    │ Identification  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Authorization Matrix

| Resource | Owner | Admin | Other User |
|----------|-------|-------|------------|
| List own filters | ✅ | ✅ | ❌ |
| View filter details | ✅ | ✅ | ❌ |
| Create filter | ✅ | ✅ | ❌ |
| Update own filter | ✅ | ✅ | ❌ |
| Delete own filter | ✅ | ✅ | ❌ |
| Set default filter | ✅ | ✅ | ❌ |

### Data Protection

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Input Validation│───►│ SQL Injection   │───►│ XSS Protection  │
│                 │    │   Prevention    │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Output Encoding │◄───│ Data Sanitization│◄───│ JSON Validation │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Performance Architecture

### Caching Strategy

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Application     │───►│ Redis Cache     │───►│ Database        │
│ Layer           │    │ (60s TTL)       │    │ Queries         │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Fast Response   │    │ Reduced Load    │    │ Optimization    │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Database Optimization

```sql
-- Query optimization using indexes
EXPLAIN ANALYZE SELECT * FROM user_saved_filters 
WHERE user_id = 123 AND filter_type = 'offer' AND is_default = true;

-- Result: Index Scan using idx_user_saved_filters_user_type_default
--         (cost=0.29..8.31 rows=1 width=...) (actual time=0.042..0.043 rows=1 loops=1)
```

### Scalability Considerations

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Load Balancer   │───►│ App Server 1    │    │ App Server N    │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Connection      │    │ Database        │    │ Cache Cluster   │
│ Pooling         │    │ Replication     │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Deployment Architecture

### Container Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      Docker Host                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │   Frontend      │  │    Backend      │  │ PostgreSQL  │ │
│  │  Container      │  │   Container     │  │  Container  │ │
│  │  (Streamlit)    │  │   (FastAPI)     │  │             │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│           │                     │                   │       │
│           └─────────────────────┼───────────────────┘       │
│                                 │                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │                 │  │                 │  │             │ │
│  │     Redis       │  │     Nginx       │  │   Volumes   │ │
│  │   Container     │  │   Container     │  │             │ │
│  │                 │  │                 │  │             │ │
│  │                 │  │                 │  │             │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Network Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Internet        │───►│ Load Balancer   │───►│ Application     │
│                 │    │   (Nginx)       │    │  Servers        │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Database        │◄───│ Internal        │◄───│ Cache Layer     │
│ Servers         │    │ Network         │    │   (Redis)       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Environment Configuration

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./app
    environment:
      - DATABASE_URL=******************************/termelo_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    
  frontend:
    build: ./streamlit_app
    environment:
      - API_BASE_URL=http://backend:8000
    depends_on:
      - backend
    
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=termelo_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7
    volumes:
      - redis_data:/data
```

## Integration Points

### External System Integration

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Authentication  │───►│ Saved Filters   │───►│ Offer           │
│    System       │    │    System       │    │ Management      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ User Context    │    │ Filter Config   │    │ Data Filtering  │
│                 │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### API Integration Points

#### Authentication Integration

```python
# Dependencies injection
def get_current_user(token: str = Depends(oauth2_scheme)):
    # Validate JWT token and return user
    return validate_token(token)

# Usage in endpoints
@router.get("/saved-filters/")
def list_filters(current_user: User = Depends(get_current_user)):
    return service.get_user_saved_filters(current_user.id)
```

#### Session Integration

```python
# Streamlit session integration
def apply_saved_filter(filter_data):
    # Update session state with filter data
    st.session_state.update(filter_data)
    
    # Trigger UI refresh
    st.rerun()
```

### Database Integration

```python
# ORM relationship integration
class User(Base):
    saved_filters = relationship(
        "UserSavedFilter", 
        back_populates="user",
        cascade="all, delete-orphan"
    )

# Query integration
def get_user_with_filters(user_id: int):
    return db.query(User).options(
        joinedload(User.saved_filters)
    ).filter(User.id == user_id).first()
```

## Monitoring and Observability

### Logging Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Application     │───►│ Structured      │───►│ Log             │
│ Logs            │    │ Logging         │    │ Aggregation     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│ Error Tracking  │    │ Performance     │    │ Business        │
│                 │    │ Metrics         │    │ Analytics       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Metrics Collection

```python
# Performance metrics
@router.get("/saved-filters/")
@metrics.track_performance("list_saved_filters")
def list_saved_filters(current_user: User = Depends(get_current_user)):
    start_time = time.time()
    try:
        filters = service.get_user_saved_filters(current_user.id)
        metrics.counter("saved_filters_listed").inc()
        return filters
    except Exception as e:
        metrics.counter("saved_filters_errors").inc()
        raise
    finally:
        duration = time.time() - start_time
        metrics.histogram("saved_filters_duration").observe(duration)
```

---

*This documentation is part of the Agricultural Product Management System documentation suite. For related documentation, see the main [DOCS_INDEX.md](../DOCS_INDEX.md).*