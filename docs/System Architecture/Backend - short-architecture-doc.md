# Frontend - streamlit_app_backend Architecture

## 1. Overview

This document details the architecture and organization of a FastAPI-based backend for an agricultural product offer management system. The system is designed to facilitate the procurement of agricultural products through a workflow of offers, approvals, and status transitions between producers, operators, and administrators.

The backend follows a structured, layered architecture with clear separation of concerns, consistent patterns across modules, and a comprehensive model for managing offers, products, and user data. It serves as the foundation for a Streamlit frontend application, providing a robust API for data access and business logic.

### Technology Stack

- **Framework**: FastAPI
- **Language**: Python 3.x
- **Database ORM**: SQLAlchemy
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Pydantic
- **Email Services**: Jinja2 (templates)
- **Deployment**: Docker

## 2. Architectural Layers

The application follows a layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────┐
│               API Layer                     │
│ (API Routes, Request/Response Handling)     │
└───────────────────┬─────────────────────────┘
                    │
┌───────────────────▼─────────────────────────┐
│            Service Layer                    │
│ (Business Logic, Validation, Coordination)  │
└───────────────────┬─────────────────────────┘
                    │
┌───────────────────▼─────────────────────────┐
│            Data Access Layer                │
│ (ORM Models, Database Interactions)         │
└───────────────────┬─────────────────────────┘
                    │
┌───────────────────▼─────────────────────────┐
│        Infrastructure Layer                 │
│ (Database Setup, Utilities, Configuration)  │
└─────────────────────────────────────────────┘
```

### 2.1 API Layer

The API layer handles HTTP requests, routing, request validation, and response formatting:

- **Endpoints**: Organized by resource type (users, products, offers, etc.)
- **Request Validation**: Uses Pydantic models to validate and parse incoming requests
- **Authentication**: JWT-based authentication with role-based access control
- **Error Handling**: Consistent error responses across all endpoints
- **Response Formatting**: Standardized response structures

### 2.2 Service Layer

The service layer contains business logic and coordinates operations:

- **Service Functions**: Specialized by domain (user service, offer service, etc.)
- **Validation**: Business rule validation beyond basic schema validation
- **Coordination**: Orchestrates operations across multiple entities
- **Event Handling**: Manages events like status transitions

### 2.3 Data Access Layer

The data access layer manages database interactions:

- **ORM Models**: SQLAlchemy models representing database entities
- **Relationships**: Entity relationships and constraints
- **Query Construction**: Complex queries and data filtering
- **Transaction Management**: ACID transaction handling

### 2.4 Infrastructure Layer

The infrastructure layer provides core functionality and configuration:

- **Database Setup**: Connection pool management, migrations
- **Configuration**: Environment-based configuration management
- **Utilities**: Common utilities like logging, email templates
- **Security**: Password hashing, token generation

## 3. Directory Structure

```
app/
├── api/                    # API layer components
│   ├── endpoints/          # API endpoint modules by resource
│   │   ├── admin.py        # Admin-specific endpoints
│   │   ├── auth.py         # Authentication endpoints
│   │   ├── notifications.py# Notification endpoints
│   │   ├── offers.py       # Offer management endpoints
│   │   ├── products.py     # Product management endpoints
│   │   └── users.py        # User management endpoints
│   ├── api.py              # API router configuration
│   └── dependencies.py     # Endpoint dependencies (auth, etc.)
│
├── core/                   # Core components
│   ├── config.py           # Application configuration
│   ├── events.py           # Application lifecycle events
│   └── security.py         # Security utilities
│
├── crud/                   # Data access components
│   ├── notification.py     # Notification CRUD operations
│   └── ...                 # (Future CRUD modules)
│
├── db/                     # Database components
│   ├── base.py             # Base model imports for migrations
│   ├── base_class.py       # Base model class
│   ├── init_db.py          # Database initialization
│   └── session.py          # Database session management
│
├── models/                 # Data models
│   ├── common.py           # Common model mixins
│   ├── model_base.py       # Base model exports
│   ├── notification.py     # Notification model
│   ├── offer.py            # Offer model
│   ├── product.py          # Product model
│   └── user.py             # User model
│
├── schemas/                # Pydantic schemas
│   ├── notification.py     # Notification schemas
│   ├── offer.py            # Offer schemas
│   ├── product.py          # Product schemas
│   └── user.py             # User and auth schemas
│
├── services/               # Business services
│   ├── email_service.py    # Email services
│   ├── offer_log_service.py# Offer logging services
│   ├── offer_service.py    # Offer business services
│   ├── product_service.py  # Product business services
│   └── user_service.py     # User management services
│
├── utils/                  # Utility functions
│   ├── email.py            # Email utilities
│   ├── logging.py          # Logging configuration
│   └── security.py         # Security utilities
│
└── main.py                 # Application entry point
```

## 4. Key Components

### 4.1 Models and Database Schema

The application uses SQLAlchemy ORM models with a consistent structure:

```python
class TimestampMixin:
    """Common timestamp fields for all models"""
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class User(Base, TimestampMixin):
    """User model - System users (producers, operators, admins)"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)
    # Other fields...
```

Key models include:

- **User**: System users with different roles (producer, operator, admin)
- **ProductCategory**: Top-level product categories (e.g., "Paprika")
- **ProductType**: Product types within categories (e.g., "TV paprika")
- **QualityGrade**: Quality grades for products (e.g., "Extra", "I. Class")
- **Offer**: Offers from producers with status workflow
- **OfferLog**: Log of offer status changes
- **Notification**: System notifications to users

### 4.2 Schema Validation

The application uses Pydantic for request/response validation with a consistent pattern:

```python
class OfferBase(BaseModel):
    """Base schema for offer data"""
    product_type_id: int = Field(..., description="Product type ID")
    quality_grade_id: Optional[int] = Field(None, description="Quality grade ID")
    quantity_in_kg: Decimal = Field(..., description="Quantity in kg")
    delivery_date: date = Field(..., description="Planned delivery date")
    note: Optional[str] = Field(None, description="Note")
    
    @validator('quantity_in_kg')
    def check_positive_quantity(cls, v):
        """Validate positive quantity"""
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v

class OfferCreate(OfferBase):
    """Schema for offer creation"""
    pass

class OfferResponse(OfferBase):
    """Schema for offer response"""
    id: int
    user_id: int
    created_by_user_id: int
    status: str
    confirmed_quantity: Optional[Decimal]
    confirmed_price: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True
```

Schemas follow a consistent inheritance pattern:
- Base schemas: Common fields and validators
- Create schemas: Fields for creating new resources
- Update schemas: Optional fields for updating resources
- Response schemas: Fields for API responses, with ORM mode enabled

### 4.3 API Endpoints

API endpoints follow a consistent pattern with dependency injection for authentication and database access:

```python
@router.get("", response_model=List[ProductCategoryResponse])
def read_categories(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
) -> Any:
    """
    List product categories
    """
    return get_product_categories(db, skip=skip, limit=limit, search=search)
```

Key endpoint patterns:
- **Authentication Dependencies**: Different access levels (user, operator, admin)
- **Database Session Dependency**: Consistent database access pattern
- **Response Models**: Explicit response typing with Pydantic models
- **Detailed Docstrings**: Comprehensive documentation for each endpoint
- **Standardized Parameters**: Consistent patterns for pagination, filtering, etc.

### 4.4 Service Layer

Services implement business logic with a consistent return pattern:

```python
def get_offer(db: Session, offer_id: int) -> Optional[Offer]:
    """
    Get offer by ID
    
    Args:
        db: Database session
        offer_id: Offer ID
        
    Returns:
        Optional[Offer]: Offer or None if not found
    """
    query = db.query(Offer).options(
        joinedload(Offer.user),
        joinedload(Offer.product_type),
        joinedload(Offer.quality_grade),
        joinedload(Offer.created_by_user)
    ).filter(Offer.id == offer_id).first()
    
    return query
```

Key service patterns:
- **Database Session Parameter**: Services are passed a database session
- **Function Documentation**: Comprehensive docstrings
- **Error Handling**: Exceptions with descriptive messages
- **Explicit Return Types**: Clear return type annotations
- **Business Rule Validation**: Complex validation beyond schema validation

### 4.5 Authentication and Security

The application uses JWT-based authentication with role-based access control:

```python
def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    Get current user from token
    
    Args:
        db: Database session
        token: JWT token
        
    Returns:
        User: Current user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    # Token validation logic...
    
    user = db.query(User).filter(User.id == token_data.user_id).first()
    
    if user is None:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    
    return user
```

Role-based dependencies enforce access control:

```python
def get_current_operator(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Check operator or admin role
    
    Args:
        current_user: Current user
        
    Returns:
        User: Current user if role is operator or admin
        
    Raises:
        HTTPException: If user doesn't have required role
    """
    if current_user.role not in ["ügyintéző", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    return current_user
```

## 5. Key Design Patterns

### 5.1 Dependency Injection

The application uses FastAPI's dependency injection system for:

- **Database Sessions**: Consistent database access pattern
- **Authentication**: User authentication and role-based access control
- **Request Validation**: Automatic request validation with Pydantic

```python
@router.post("/me/change-password", response_model=UserResponse)
def update_current_user_password(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    password_data: UserChangePassword,
) -> Any:
    """Change password for current user"""
    # Implementation...
```

### 5.2 Repository Pattern

The application implements a repository pattern (sometimes called "service" pattern in this codebase) to abstract database operations:

```python
def get_product_categories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None
) -> List[ProductCategory]:
    """
    List product categories
    
    Args:
        db: Database session
        skip: Number of items to skip (for pagination)
        limit: Maximum number of items (for pagination)
        search: Search term (for filtering)
        
    Returns:
        List[ProductCategory]: List of categories
    """
    query = db.query(ProductCategory)
    
    # Search by name
    if search:
        query = query.filter(ProductCategory.name.ilike(f"%{search}%"))
    
    # Pagination and result
    return query.order_by(ProductCategory.name).offset(skip).limit(limit).all()
```

### 5.3 Factory Pattern

The application uses factory functions to create complex objects:

```python
def create_user(db: Session, user_data: UserCreate) -> User:
    """
    Create new user
    
    Args:
        db: Database session
        user_data: User creation data
        
    Returns:
        User: Created user
        
    Raises:
        ValueError: If user already exists
    """
    # Check if user already exists
    db_user = db.query(User).filter(User.email == user_data.email).first()
    if db_user:
        raise ValueError(f"User already exists: {user_data.email}")
    
    # Create user with hashed password
    hashed_password = get_password_hash(user_data.password)
    
    # Generate activation token
    activation_token = secrets.token_urlsafe(32)
    
    # Create new user
    db_user = User(
        email=user_data.email,
        password_hash=hashed_password,
        role="termelő",  # Default role
        company_name=user_data.company_name,
        tax_id=user_data.tax_id,
        contact_name=user_data.contact_name,
        phone_number=user_data.phone_number,
        is_active=False,  # Initially inactive
        activation_token=activation_token
    )
    
    # Save to database
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user
```

### 5.4 State Machine Pattern

The offer workflow implements a state machine pattern with explicit transitions:

```python
def confirm_offer(
    db: Session, 
    offer_id: int, 
    confirm_data: OfferConfirm, 
    operator_id: int
) -> Optional[Offer]:
    """
    Confirm offer (operator function)
    
    Args:
        db: Database session
        offer_id: Offer ID
        confirm_data: Confirmation data
        operator_id: Operator ID
        
    Returns:
        Optional[Offer]: Confirmed offer or None if not found
        
    Raises:
        ValueError: If offer cannot be confirmed (not in CREATED status)
    """
    # Get offer
    db_offer = get_offer(db, offer_id)
    if not db_offer:
        return None
    
    # Check if offer can be confirmed
    if db_offer.status != "CREATED":
        raise ValueError(f"Offer cannot be confirmed because it is not in 'CREATED' status: {db_offer.status}")
    
    # Update data
    old_status = db_offer.status
    db_offer.status = "CONFIRMED_BY_COMPANY"
    db_offer.confirmed_quantity = confirm_data.confirmed_quantity
    db_offer.confirmed_price = confirm_data.confirmed_price
    
    if confirm_data.note:
        db_offer.note = confirm_data.note
    
    # Save to database
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Create log entry
    create_offer_log(
        db=db,
        offer_id=db_offer.id,
        old_status=old_status,
        new_status=db_offer.status,
        changed_by=operator_id,
        note=f"Offer confirmed: {confirm_data.confirmed_quantity} kg, {confirm_data.confirmed_price} Ft/kg"
    )
    
    # Create notification for the user
    notification = NotificationCreate(
        user_id=db_offer.user_id,
        type="success",
        message="Your offer has been confirmed by the operator",
        detail=f"Offer ID: {db_offer.id}, confirmed quantity: {db_offer.confirmed_quantity} kg, price: {db_offer.confirmed_price} Ft/kg",
        target_roles=None,
        related_entity_type="offer",
        related_entity_id=db_offer.id
    )
    create_notification(db, notification)
    
    return db_offer
```

The offer status flow follows a specific sequence:
```
CREATED → CONFIRMED_BY_COMPANY → ACCEPTED_BY_USER/REJECTED_BY_USER → FINALIZED
```

### 5.5 Observer Pattern

The application implements an observer pattern for notifications on status changes:

```python
# Create notification for the user when offer is rejected
notification = NotificationCreate(
    user_id=None,
    type="info",
    message="Producer rejected the offer",
    detail=f"Offer ID: {db_offer.id}{f', note: {note}' if note else ''}",
    target_roles="ügyintéző",
    related_entity_type="offer",
    related_entity_id=db_offer.id
)
create_notification(db, notification)
```

## 6. Data Flow Patterns

### 6.1 API Request Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Client       ├─────►│  FastAPI      ├─────►│  Pydantic     │
│  Request      │      │  Router       │      │  Validation   │
│               │      │               │      │               │
└───────────────┘      └───────┬───────┘      └───────┬───────┘
                               │                      │
                               │                      │
┌───────────────┐      ┌───────▼───────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  SQLAlchemy   │◄─────┤  Service      │◄─────┤  Dependency   │
│  Database     │      │  Layer        │      │  Injection    │
│               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────────────┘
        │
        │
┌───────▼───────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Response     │◄─────┤  Pydantic     │◄─────┤  Service      │
│  Generation   │      │  Serialization│      │  Result       │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

1. **Client Request**: Client sends HTTP request
2. **FastAPI Router**: Routes request to appropriate endpoint
3. **Pydantic Validation**: Validates request data
4. **Dependency Injection**: Injects dependencies (DB session, current user, etc.)
5. **Service Layer**: Processes business logic
6. **SQLAlchemy Database**: Performs database operations
7. **Service Result**: Returns result from service function
8. **Pydantic Serialization**: Serializes result to response format
9. **Response Generation**: Returns HTTP response

### 6.2 Authentication Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Login        ├─────►│  authenticate_│─────►│  verify_      │
│  Request      │      │  user()       │      │  password()   │
│               │      │               │      │               │
└───────────────┘      └───────┬───────┘      └───────────────┘
                               │
                               │
┌───────────────┐      ┌───────▼───────┐      ┌───────────────┐
│               │      │               │      │               │
│  JWT Token    │◄─────┤  create_user_ │◄─────┤  User         │
│  Response     │      │  token()      │      │  Object       │
│               │      │               │      │               │
└───────────────┘      └───────────────┘      └───────────────┘
```

1. **Login Request**: Client sends credentials
2. **authenticate_user()**: Validates credentials
3. **verify_password()**: Verifies password hash
4. **User Object**: Retrieves authenticated user
5. **create_user_token()**: Creates JWT token
6. **JWT Token Response**: Returns token to client

### 6.3 Offer Status Transition Flow

```
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│               │      │               │      │               │
│  Status       ├─────►│  Status       ├─────►│  Database     │
│  Change       │      │  Validation   │      │  Update       │
│  Request      │      │               │      │               │
└───────────────┘      └───────┬───────┘      └───────┬───────┘
                               │                      │
                               │                      │
┌───────────────┐      ┌───────▼───────┐      ┌───────▼───────┐
│               │      │               │      │               │
│  Notification │◄─────┤  Event        │◄─────┤  Log          │
│  Creation     │      │  Processing   │      │  Creation     │
│               │      │               │      │               │
└───────┬───────┘      └───────────────┘      └───────────────┘
        │
        │
┌───────▼───────┐
│               │
│  Email        │
│  Notification │
│  (optional)   │
└───────────────┘
```

1. **Status Change Request**: Client requests status change
2. **Status Validation**: Validates status transition
3. **Database Update**: Updates offer status
4. **Log Creation**: Creates status change log
5. **Event Processing**: Processes status change event
6. **Notification Creation**: Creates notifications for relevant users
7. **Email Notification**: Optionally sends email notifications

## 7. Database Schema

The database schema follows a relational model with clear entity relationships:

```
┌─────────────────┐        ┌─────────────────┐       ┌─────────────────┐
│     users       │        │     offers      │       │  product_types   │
├─────────────────┤        ├─────────────────┤       ├─────────────────┤
│ id              │◄───────┤ user_id         │       │ id              │
│ email           │        │ product_type_id │◄──────┤ category_id     │
│ password_hash   │        │ quality_grade_id│       │ name            │
│ role            │        │ quantity_in_kg  │       │ description     │
│ company_name    │        │ delivery_date   │       │ has_quality_grad│
│ tax_id          │        │ status          │       └─────────┬───────┘
│ contact_name    │        │ confirmed_quanti│                 │
│ phone_number    │        │ confirmed_price │                 │
│ is_active       │        │ note            │                 │
│ activation_token│        │ created_by_user_│                 │
└─────────┬───────┘        └─────────┬───────┘                 │
          │                          │                         │
          │                          │                         │
┌─────────▼───────┐        ┌─────────▼───────┐       ┌─────────▼───────┐
│ user_default_   │        │    offer_logs   │       │  quality_grades  │
│     settings    │        ├─────────────────┤       ├─────────────────┤
├─────────────────┤        │ id              │       │ id              │
│ id              │        │ offer_id        │       │ product_type_id │
│ user_id         │        │ old_status      │       │ name            │
│ default_product_│        │ new_status      │       │ min_shoulder_dia│
│ default_quality_│        │ changed_by      │       │ max_shoulder_dia│
│ default_quantit_│        │ note            │       │ min_length      │
└─────────────────┘        └─────────────────┘       │ max_length      │
                                                     │ description     │
                                                     └─────────────────┘
                                                     
┌─────────────────┐        ┌─────────────────┐
│ product_        │        │ notifications   │
│    categories   │        ├─────────────────┤
├─────────────────┤        │ id              │
│ id              │        │ user_id         │
│ name            │        │ type            │
│ description     │        │ message         │
└─────────────────┘        │ detail          │
                           │ is_read         │
                           │ target_roles    │
                           │ related_entity_t│
                           │ related_entity_i│
                           └─────────────────┘
```

Key relationships:
- Users have many Offers
- ProductCategories have many ProductTypes
- ProductTypes have many QualityGrades
- ProductTypes have many Offers
- QualityGrades have many Offers
- Offers have many OfferLogs
- Users have many Notifications

## 8. Code Patterns

### 8.1 Function Documentation Pattern

Functions follow a consistent documentation pattern:

```python
def function_name(param1: Type1, param2: Type2) -> ReturnType:
    """
    Brief description of function purpose
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        ReturnType: Description of return value
        
    Raises:
        ExceptionType: Description of when this exception occurs
    """
    # Implementation
```

### 8.2 Error Handling Pattern

Error handling follows a consistent pattern:

```python
try:
    # Operation that may fail
    result = some_operation()
except SpecificError as e:
    # Handle specific error
    logger.error(f"Specific error: {str(e)}")
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=str(e)
    )
except Exception as e:
    # Handle general errors
    logger.error(f"Unexpected error: {str(e)}")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"Unexpected error: {str(e)}"
    )
```

### 8.3 Function Return Pattern

Service functions follow a consistent return pattern:

```python
def some_operation(db: Session, param1: Type1) -> Optional[ResultType]:
    """
    Function description
    
    Args:
        db: Database session
        param1: Parameter description
        
    Returns:
        Optional[ResultType]: Result description
    """
    # Implementation
    if not result:
        return None
    return result

def boolean_operation(db: Session, param1: Type1) -> bool:
    """
    Function description
    
    Args:
        db: Database session
        param1: Parameter description
        
    Returns:
        bool: Whether the operation was successful
    """
    # Implementation
    if success:
        return True
    return False
```

### 8.4 Validation Pattern

Validation follows a consistent pattern:

```python
@validator('quantity_in_kg')
def check_positive_quantity(cls, v):
    """Validate positive quantity"""
    if v <= 0:
        raise ValueError("Quantity must be positive")
    return v
```

## 9. Integration Points

### 9.1 Frontend Integration

The backend is designed to be consumed by a Streamlit frontend, providing these integration points:

- **Authentication API**: Login, registration, password reset
- **User Management API**: Profile management, user listing (admin)
- **Product Management API**: Categories, types, grades management
- **Offer Management API**: Offer creation, confirmation, acceptance, rejection
- **Reporting API**: Statistical data for dashboard
- **Notification API**: User notifications

### 9.2 Email Integration

The backend includes an email service for sending notifications:

```python
async def send_offer_confirmation_email(
    background_tasks: BackgroundTasks,
    email_to: EmailStr,
    offer_id: int,
    product_name: str,
    confirmed_quantity: float,
    confirmed_price: float,
):
    """
    Send offer confirmation email
    
    Args:
        background_tasks: FastAPI BackgroundTasks
        email_to: Recipient email
        offer_id: Offer ID
        product_name: Product name
        confirmed_quantity: Confirmed quantity
        confirmed_price: Confirmed price
    """
    # Email implementation...
```

Email templates are rendered using Jinja2:

```python
def _send_email_task(
    email_to: List[EmailStr],
    subject: str,
    template_name: str,
    template_data: dict,
):
    """
    Email sending task execution
    
    Args:
        email_to: Recipient email addresses
        subject: Subject
        template_name: Template name (.html extension not included)
        template_data: Template data
    """
    try:
        # Load template and render
        template = env.get_template(f"{template_name}.html")
        html_content = template.render(**template_data)
        
        # Simple solution for development environment: just log email data
        if settings.ENVIRONMENT == "development":
            logger.info(f"Email sending (DEVELOPMENT): {subject} -> {email_to}")
            logger.debug(f"Email content: {html_content}")
            return
        
        # TODO: Configure SMTP client and send email
        # Here, a real SMTP client should be used
        # for example: FastAPI-Mail or similar library
        
        logger.info(f"Email sent: {subject} -> {email_to}")
    
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        raise
```

## 10. Security Considerations

### 10.1 Password Handling

Passwords are securely hashed using bcrypt:

```python
# Password hashing configuration
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies that the provided password matches the hashed password
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if passwords match, False if not
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Creates a password hash
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)
```

### 10.2 JWT Authentication

JWT tokens are used for authentication:

```python
def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT token for user authentication"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    # Check subject type and handle appropriately
    if isinstance(subject, dict):
        # If dictionary, place fields directly in token payload
        to_encode = {"exp": expire, **subject}
    else:
        # Otherwise put in sub field
        to_encode = {"exp": expire, "sub": str(subject)}
    
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
    )
    return encoded_jwt
```

### 10.3 Role-Based Access Control

Role-based access control is enforced through dependencies:

```python
def get_current_admin(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Check admin role
    
    Args:
        current_user: Current user
        
    Returns:
        User: Current user if role is admin
        
    Raises:
        HTTPException: If user is not admin
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    return current_user
```

## 11. Application Initialization

The application initialization in `main.py` follows a structured approach:

```python
app = FastAPI(
    title="Agricultural Product Management API",
    description="Producer offer management",
    version="1.0.0"
)

# CORS settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add API router
app.include_router(api_router, prefix="/api")

def create_tables_if_not_exist():
    """
    Checks for the existence of all required tables, 
    and creates them if any are missing.
    """
    # Implementation...

@app.get("/")
async def root():
    return JSONResponse(
        content={
            "message": "Agricultural Product Management API",
            "docs": "/docs",
            "status": "running"
        }
    )

@app.on_event("startup")
def startup_event():
    """
    Operations to perform when the application starts.
    
    This function runs when the application starts.
    It checks and creates missing database tables,
    then loads initial data.
    """
    # First check and create missing tables
    create_tables_if_not_exist()
    
    # Load initial data
    init_db()
```

The `init_db.py` module handles database initialization:

```python
def init_db() -> None:
    """
    Initialize database: load basic system data
    - Create default admin user if it doesn't exist
    - Create basic product categories and types if they don't exist
    """
    db = SessionLocal()
    init_system_data(db)
    db.close()


def init_system_data(db: Session) -> None:
    """
    Initialize system data
    
    Args:
        db: SQLAlchemy session object
    """
    # Create admin user
    create_admin_user(db)

    # Create default producer and operator users
    create_default_users(db)
    
    # Create default product categories, types and quality grades
    create_product_data(db)
```

## 12. Summary

The backend application follows a well-structured, layered architecture with clear separation of concerns. It provides a comprehensive API for managing agricultural product offers, with robust authentication, validation, and business logic.

Key architectural features:
- **Layered Architecture**: API, Service, Data Access, and Infrastructure layers
- **Dependency Injection**: FastAPI's dependency injection for database access and authentication
- **Comprehensive Models**: SQLAlchemy ORM models with clear relationships
- **Consistent Patterns**: Documentation, error handling, function returns, validation
- **Security**: Password hashing, JWT authentication, role-based access control

The architecture enables maintainable, extensible development while providing a robust foundation for the Streamlit frontend application.
