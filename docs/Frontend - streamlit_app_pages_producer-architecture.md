# Streamlit Agricultural Offer Management System: Technical Architecture

## 1. System Overview

The application is a web-based Offer Management System built using Streamlit, designed for agricultural product procurement. It provides interfaces for operators to manage offers from producers, track deliveries, and generate reports. The system follows a modular, component-based architecture with clear separation of concerns.

### 1.1 Technology Stack

- **Frontend Framework**: Streamlit (Python-based web app framework)
- **Language**: Python 3.x
- **State Management**: Streamlit Session State
- **Data Visualization**: Plotly, Pandas
- **UI Enhancement**: Custom HTML/CSS/JavaScript injections
- **Data Format**: JSON for API communication

### 1.2 Application Purpose

The application serves as a platform for managing agricultural product offers with the following core functionalities:

1. Viewing, filtering, and searching product offers
2. Managing offer details and status transitions
3. Calendar views for delivery scheduling
4. Reporting and analytics
5. User role-based access control (operators, producers, admins)

## 2. Architectural Patterns

### 2.1 Component-Based Architecture

The application follows a component-based design pattern where UI elements and functionality are encapsulated in reusable components. Each component:

- Has a constructor that accepts configuration parameters
- Provides a `render()` method for displaying the component
- May contain helper methods with specific naming patterns (e.g., `_render_*`, `_handle_*`)
- Accepts callbacks for handling user interactions

```python
class ActionBar:
    """Component for displaying action buttons in a sticky header."""
    
    def __init__(self, offer_id, offer_status=None, permissions=None):
        self.offer_id = offer_id
        self.offer_status = offer_status
        self.permissions = permissions or {}
        self.key_prefix = f"action_bar_{offer_id}_{str(uuid.uuid4())[:6]}"
    
    def render(self, on_back=None, on_status_change=None, on_edit=None, on_export=None):
        """Renders the action bar with configured callbacks."""
        # Rendering logic here
```

### 2.2 Layered Architecture

The application implements a layered architecture with these distinct layers:

1. **Presentation Layer**: Streamlit UI components and pages
2. **Business Logic Layer**: Action handlers and data processing
3. **Data Access Layer**: API clients and data fetching
4. **State Management Layer**: Session state handling
5. **Utility Layer**: Helper functions and shared utilities

### 2.3 Responsive Design Pattern

The application implements responsive design patterns to adapt to different device types:

```python
def setup_responsive_ui():
    """Configures the UI for responsive behavior."""
    # JavaScript for screen size detection
    st.markdown("""
    <script>
        var screenWidth = window.innerWidth;
        var isMobile = screenWidth < 768;
        var isTablet = screenWidth >= 768 && screenWidth < 992;
        
        // Send info to Streamlit
        window.parent.postMessage({
            type: "streamlit:setComponentValue",
            value: {
                screen_width: screenWidth,
                is_mobile: isMobile,
                is_tablet: isTablet
            }
        }, "*");
    </script>
    """, unsafe_allow_html=True)
```

### 2.4 Factory Pattern

Used for creating UI components based on data types or user preferences:

```python
def render_section_card(title, content, color="#3584e4", icon=None, key=None, expanded=True):
    """Factory function that creates and renders a section card with consistent styling."""
    # Card rendering logic
```

### 2.5 Adapter Pattern

Used for making the API client compatible with different backend APIs:

```python
def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """Adapter for safe API calls with standardized error handling."""
    try:
        success, result = api_function(*args, **kwargs)
        if not success:
            handle_api_error(result, error_operation_name)
        return success, result
    except Exception as e:
        handle_api_error(e, error_operation_name)
        return False, str(e)
```

## 3. Directory Structure

The application follows a modular directory structure:

```
streamlit_app/
├── pages/                        # Page definitions
│   ├── operator/                 # Operator-specific pages
│   │   ├── __init__.py           # Package initialization
│   │   ├── calendar.py           # Calendar view
│   │   ├── create_offer.py       # Offer creation page
│   │   ├── dashboard.py          # Operator dashboard
│   │   ├── offer_management.py   # Main offer management
│   │   ├── offer_management/     # Modular components for offer management
│   │   │   ├── __init__.py
│   │   │   ├── ui_components.py
│   │   │   ├── data_processing.py
│   │   │   ├── api_client.py
│   │   │   └── ... (other modules)
│   │   ├── reports.py            # Reporting functionality
│   ├── auth_login.py             # Authentication pages
│   ├── operator_*.py             # Re-export modules for Streamlit navigation
├── components/                   # Shared UI components
│   ├── notification.py           # Notification system
│   ├── sidebar.py                # Navigation sidebar 
│   ├── calendar_component.py     # Calendar visualization
│   └── data_display.py           # Data display utilities
├── utils/                        # Shared utilities
│   ├── formatting.py             # Text/data formatting
│   ├── session.py                # Session state utilities
│   ├── validators.py             # Input validation
│   └── responsive_ui.py          # Responsive design utilities
├── api/                          # API client modules
│   ├── offers.py                 # Offers API
│   ├── products.py               # Products API
│   └── users.py                  # Users API
└── app_config.py                 # Application configuration
```

## 4. Core Architectural Layers

### 4.1 Presentation Layer

The presentation layer is responsible for rendering UI elements and handling user interactions.

#### 4.1.1 UI Components

UI components are defined in modules like `ui_components.py`, `enhanced_ui_components.py`, and `responsive_ui.py`. These components follow a consistent pattern:

```python
def display_offer_table_with_actions(offers, pagination=True):
    """
    Renders a table of offers with action buttons.
    
    Args:
        offers (list): List of offer objects
        pagination (bool): Whether to include pagination controls
    """
    # Check if mobile view is enabled
    is_mobile = st.session_state.get('is_mobile', False)
    
    if is_mobile:
        # Mobile optimized view - cards instead of table
        for idx, offer in enumerate(offers):
            display_mobile_offer_card(offer, idx)
    else:
        # Desktop view - full table
        if offers:
            # Create dataframe for display
            df = pd.DataFrame(offers)
            
            # Configure columns and display
            st.dataframe(
                df,
                column_config={
                    "id": "Azonosító",
                    "status": st.column_config.TextColumn(
                        "Állapot",
                        help="Az ajánlat jelenlegi állapota"
                    ),
                    # Additional column config...
                },
                hide_index=True,
                use_container_width=True
            )
            
            # Pagination controls if needed
            if pagination:
                render_pagination_controls()
```

#### 4.1.2 Page Structure

Pages are top-level modules that compose multiple components to create complete views. They follow this pattern:

```python
def show_operator_dashboard():
    """Main function to display the operator dashboard."""
    # Page title and setup
    st.title("Ügyintézői Irányítópult")
    
    # Authentication check
    if not is_authenticated():
        show_error("Az oldal megtekintéséhez bejelentkezés szükséges.")
        st.switch_page("pages/auth_login.py")
        return
        
    # Access control based on user role
    user = get_current_user()
    if user.get("role") not in ["ügyintéző", "admin"]:
        show_error("Az oldal megtekintéséhez ügyintézői jogosultság szükséges.")
        return
    
    # Content sections
    st.write(f"### Üdvözöljük, {user.get('contact_name')}!")
    
    # Dashboard components with layout
    col1, col2 = st.columns(2)
    
    with col1:
        # Render quick overview section
        with st.container(border=True):
            st.subheader("Gyors áttekintés")
            # Component rendering
    
    with col2:
        # Render urgent offers section
        with st.container(border=True):
            st.subheader("Sürgős ajánlatok")
            # Component rendering
```

### 4.2 Business Logic Layer

The business logic layer contains domain-specific logic, data transformations, and operation handlers.

#### 4.2.1 Action Handlers

Action handlers are functions that process user actions, validate inputs, and coordinate with the data layer.

```python
def handle_status_transitions(offer, offer_id):
    """
    Handles offer status transitions based on current status.
    
    Args:
        offer (dict): The offer data
        offer_id (int): The offer ID
    """
    # Status handling logic
    current_status = offer.get("status")
    available_transitions = {
        "CREATED": ["CONFIRMED_BY_COMPANY", "REJECTED_BY_USER"],
        "CONFIRMED_BY_COMPANY": ["ACCEPTED_BY_USER", "REJECTED_BY_USER"],
        "ACCEPTED_BY_USER": ["FINALIZED"],
        "REJECTED_BY_USER": ["CREATED"],
        "FINALIZED": []
    }
    
    possible_transitions = available_transitions.get(current_status, [])
    
    # UI for status transitions
    if possible_transitions:
        selected_status = st.selectbox(
            "Új státusz",
            options=possible_transitions,
            format_func=lambda x: config.OFFER_STATUSES.get(x, {}).get("name", x)
        )
        
        # Handle confirmation data if needed
        confirmation_data = None
        if selected_status == "CONFIRMED_BY_COMPANY":
            # Collect confirmation data
            
        # Handle the transition
        if st.button("Státusz frissítése"):
            success, result = offers_api.update_offer_status(
                offer_id, 
                selected_status,
                confirmation_data=confirmation_data
            )
            
            if success:
                show_success("Az ajánlat státusza sikeresen frissítve!")
                # Update cached data
                # Refresh UI
```

#### 4.2.2 Data Processing

Data processing functions transform raw data for display or API requests:

```python
def process_calendar_events(response):
    """
    Processes calendar events from API response.
    
    Args:
        response (tuple): The API response (success, data)
        
    Returns:
        list: Processed events
    """
    # Error handling
    if not response or not isinstance(response, tuple):
        return []
    
    success, data = response
    if not success or not data:
        return []
    
    processed_events = []
    
    # Process according to data format
    if isinstance(data, dict):
        for date, offers in data.items():
            # Processing logic for dictionary format
    elif isinstance(data, list):
        for offer in data:
            # Processing logic for list format
    
    return processed_events
```

### 4.3 Data Access Layer

The data access layer handles communication with backend services through API clients.

#### 4.3.1 API Clients

API clients abstract backend communication and provide a consistent interface:

```python
def get_offer_details(offer_id):
    """
    Fetches detailed information about an offer.
    
    Args:
        offer_id (int): The offer ID
        
    Returns:
        tuple: (success, data) where success is a boolean and data is the offer details or error message
    """
    try:
        # Prepare API endpoint
        endpoint = f"{config.API_BASE_URL}/api/offers/{offer_id}"
        
        # Make HTTP request
        response = requests.get(
            endpoint,
            headers=get_auth_headers(),
            timeout=10
        )
        
        # Process response
        if response.status_code == 200:
            return True, response.json()
        else:
            logger.error(f"API Error: {response.status_code} - {response.text}")
            return False, f"Hiba az ajánlat lekérésekor: {response.status_code}"
    except Exception as e:
        logger.error(f"Exception in get_offer_details: {str(e)}")
        return False, f"Kommunikációs hiba: {str(e)}"
```

#### 4.3.2 Safe API Calling Pattern

The application implements a safe API calling pattern with standardized error handling:

```python
def safe_api_call(api_func, params, fallback_func=None):
    """
    Safely calls an API function with fallback option.
    
    Args:
        api_func (function): API function to call
        params (dict): API parameters
        fallback_func (function, optional): Fallback API function
        
    Returns:
        tuple: (success, result)
    """
    try:
        success, result = api_func(params)
        if success and result:
            return True, result
    except Exception as e:
        logger.error(f"API call error: {e}")
    
    if fallback_func:
        try:
            fallback_success, fallback_result = fallback_func(params)
            if fallback_success and fallback_result:
                return True, fallback_result
        except Exception as e:
            logger.error(f"Fallback API call error: {e}")
    
    return False, []
```

### 4.4 State Management Layer

The state management layer handles application state using Streamlit's session state.

#### 4.4.1 Session State Management

Session state management functions initialize, update, and access session state:

```python
def init_page_state():
    """Initializes the session state for a page."""
    # Generate unique page ID if not exists
    if "page_uuid" not in st.session_state:
        st.session_state.page_uuid = str(uuid.uuid4())
    
    # Initialize filter state
    if "producer_filter_om" not in st.session_state:
        st.session_state.producer_filter_om = None
    
    if "status_filter_om" not in st.session_state:
        st.session_state.status_filter_om = None
    
    # Initialize date filters with default values (last 30 days)
    today = datetime.now().date()
    thirty_days_ago = today - timedelta(days=30)
    
    if "from_date_filter_om" not in st.session_state:
        st.session_state.from_date_filter_om = thirty_days_ago
    
    if "to_date_filter_om" not in st.session_state:
        st.session_state.to_date_filter_om = today
```

#### 4.4.2 Cache Management

The application implements a time-based caching mechanism to improve performance:

```python
def lazy_load_cache(cache_key, data_loader_func, cache_ttl=300):
    """
    Loads data with caching.
    
    Args:
        cache_key (str): Cache key
        data_loader_func (callable): Function to load data
        cache_ttl (int, optional): Cache TTL in seconds. Defaults to 300.
        
    Returns:
        tuple: (success, data)
    """
    # Check if data is in cache and not expired
    if cache_key in st.session_state:
        cache_data = st.session_state[cache_key]
        if time.time() - cache_data['timestamp'] < cache_ttl:
            return True, cache_data['data']
    
    # Load fresh data
    success, data = data_loader_func()
    
    # Cache the result if successful
    if success:
        st.session_state[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    return success, data
```

### 4.5 Utility Layer

The utility layer provides helper functions used across the application.

#### 4.5.1 Formatting Utilities

Formatting utilities ensure consistent data presentation:

```python
def format_status(status):
    """
    Formats a status code into human-readable text.
    
    Args:
        status (str): Status code
        
    Returns:
        str: Formatted status text
    """
    status_map = {
        "CREATED": "Létrehozva",
        "CONFIRMED_BY_COMPANY": "Visszaigazolva",
        "ACCEPTED_BY_USER": "Elfogadva",
        "REJECTED_BY_USER": "Elutasítva",
        "FINALIZED": "Véglegesítve"
    }
    return status_map.get(status, status or "Ismeretlen")

def format_quantity(quantity):
    """
    Formats a quantity value with unit.
    
    Args:
        quantity (float): Quantity value
        
    Returns:
        str: Formatted quantity
    """
    return f"{quantity:.2f} kg"

def format_price(price):
    """
    Formats a price value with currency.
    
    Args:
        price (float): Price value
        
    Returns:
        str: Formatted price
    """
    return f"{price:,.0f} Ft".replace(",", " ")
```

#### 4.5.2 Validation Utilities

Validation utilities ensure data integrity:

```python
def validate_offer_data(offer_data, is_new=True):
    """
    Validates offer data before submission.
    
    Args:
        offer_data (dict): Offer data to validate
        is_new (bool): Whether this is a new offer
        
    Returns:
        tuple: (is_valid, errors, warnings)
    """
    errors = []
    warnings = []
    
    # Required fields
    required_fields = ["product_type_id", "quantity_in_kg", "delivery_date"]
    for field in required_fields:
        if field not in offer_data or not offer_data[field]:
            errors.append(f"A(z) {field} mező kitöltése kötelező.")
    
    # Numeric validation
    if "quantity_in_kg" in offer_data:
        try:
            quantity = float(offer_data["quantity_in_kg"])
            if quantity <= 0:
                errors.append("A mennyiségnek pozitív számnak kell lennie.")
        except (ValueError, TypeError):
            errors.append("A mennyiségnek számnak kell lennie.")
    
    # Date validation
    if "delivery_date" in offer_data:
        try:
            delivery_date = datetime.strptime(offer_data["delivery_date"], "%Y-%m-%d").date()
            today = datetime.now().date()
            
            if delivery_date < today:
                errors.append("A szállítási dátum nem lehet a múltban.")
        except (ValueError, TypeError):
            errors.append("Érvénytelen szállítási dátum.")
    
    return (len(errors) == 0, errors, warnings)
```

## 5. Key Design Patterns and Conventions

### 5.1 Component-Based UI Building

The UI is built as a hierarchy of components that follow consistent patterns:

1. Pages (top level) - defined in main Python modules
2. Sections (mid level) - logical groupings within pages
3. Components (low level) - reusable UI elements

```python
# Page level
def show_offer_list():
    """Displays the offer list page."""
    st.title("Ajánlatok kezelése")
    
    # Section level
    with st.container():
        st.subheader("Szűrési lehetőségek")
        # Component level
        render_offer_filters("om")
```

### 5.2 Streamlit-Specific Patterns

#### 5.2.1 Layout Management

The application uses Streamlit's column system for layout:

```python
# Create responsive columns with proper sizing
col1, col2, col3 = st.columns([2, 1, 1])

with col1:
    st.write("### Offer Details")
    # Content for first column

with col2:
    st.write("### Status")
    # Content for second column 

with col3:
    st.write("### Actions")
    # Content for third column
```

#### 5.2.2 State Management

The application leverages Streamlit's session state for persistence:

```python
# Initialize state
if "selected_offer_id" not in st.session_state:
    st.session_state.selected_offer_id = None

# Update state
if st.button("View Offer"):
    st.session_state.selected_offer_id = offer_id
    st.rerun()

# Check state for conditional rendering
if "selected_offer_id" in st.session_state:
    show_offer_detail(st.session_state.selected_offer_id)
else:
    show_offer_list()
```

### 5.3 JavaScript Integration

The application extends Streamlit with custom JavaScript:

```python
def inject_keyboard_shortcuts():
    """Injects JavaScript for keyboard shortcuts."""
    st.markdown("""
    <script>
        document.addEventListener('keydown', function(e) {
            // Skip if focus is in an input
            if (document.activeElement.tagName === 'INPUT') return;
            
            // Navigation shortcuts
            if (e.key === 'j') {
                // Select next item
                window.parent.postMessage({
                    type: "streamlit:setComponentValue",
                    value: { action: "select_next" }
                }, "*");
            }
            
            if (e.key === 'k') {
                // Select previous item
                window.parent.postMessage({
                    type: "streamlit:setComponentValue",
                    value: { action: "select_prev" }
                }, "*");
            }
        });
    </script>
    """, unsafe_allow_html=True)
```

### 5.4 Error Handling

The application implements consistent error handling:

```python
def show_inline_error(message):
    """Displays an inline error message."""
    st.markdown(f"""
    <div style="color: white; background-color: #ff5252; padding: 10px; border-radius: 5px;">
        ⚠️ <strong>Hiba:</strong> {message}
    </div>
    """, unsafe_allow_html=True)

def safe_api_call(api_function, error_operation_name, *args, **kwargs):
    """Safe API call with standardized error handling."""
    try:
        # Make API call
        success, result = api_function(*args, **kwargs)
        
        # Handle API-level error
        if not success:
            logger.error(f"API error in {error_operation_name}: {result}")
            return False, f"Hiba a(z) {error_operation_name} során: {result}"
        
        return success, result
    except Exception as e:
        # Handle exception
        logger.error(f"Exception in {error_operation_name}: {str(e)}")
        return False, f"Váratlan hiba a(z) {error_operation_name} során: {str(e)}"
```

## 6. Data Flow Patterns

### 6.1 Loading and Filtering Data

```python
def show_offers_with_filters():
    """Displays filtered offers."""
    # 1. Get filter values from UI or session state
    producer_id = st.session_state.get("producer_filter", None)
    status = st.session_state.get("status_filter", None)
    
    # 2. Prepare filter parameters
    params = {}
    if producer_id:
        params["user_id"] = producer_id
    if status:
        params["status"] = status
    
    # 3. Load data through API
    success, offers = offers_api.get_offers(params=params)
    
    # 4. Handle errors
    if not success:
        show_error(f"Hiba az ajánlatok betöltésekor: {offers}")
        return
    
    # 5. Display data
    if offers:
        display_offer_table(offers)
    else:
        st.info("Nincs megjeleníthető ajánlat a megadott szűrési feltételekkel.")
```

### 6.2 Form Submission Pattern

```python
def create_offer_form():
    """Displays and processes the offer creation form."""
    # 1. Display form
    with st.form("create_offer_form"):
        # Form fields
        product_type = st.selectbox("Termék típusa", options=product_types)
        quantity = st.number_input("Mennyiség (kg)", min_value=0.1)
        delivery_date = st.date_input("Szállítási dátum")
        
        # Submit button
        submitted = st.form_submit_button("Ajánlat létrehozása")
    
    # 2. Process form submission
    if submitted:
        # 3. Validate input data
        errors = validate_offer_data(product_type, quantity, delivery_date)
        
        if errors:
            # 4. Display validation errors
            for error in errors:
                show_inline_error(error)
        else:
            # 5. Prepare data for API
            offer_data = {
                "product_type_id": product_type,
                "quantity_in_kg": quantity,
                "delivery_date": delivery_date.strftime("%Y-%m-%d")
            }
            
            # 6. Submit to API
            success, result = offers_api.create_offer(offer_data)
            
            # 7. Handle response
            if success:
                show_success("Ajánlat sikeresen létrehozva!")
                # 8. Clear form or redirect
                st.rerun()
            else:
                show_error(f"Hiba az ajánlat létrehozásakor: {result}")
```

### 6.3 Status Transition Pattern

```python
def handle_offer_status_transition(offer_id, current_status):
    """Handles offer status transitions."""
    # 1. Determine valid next statuses
    next_statuses = get_valid_next_statuses(current_status)
    
    # 2. Display status selection
    if next_statuses:
        selected_status = st.selectbox(
            "Új státusz",
            options=next_statuses,
            format_func=format_status
        )
        
        # 3. Collect additional data if needed
        confirmation_data = None
        if selected_status == "CONFIRMED_BY_COMPANY":
            confirmed_price = st.number_input("Visszaigazolt ár (Ft/kg)")
            confirmed_quantity = st.number_input("Visszaigazolt mennyiség (kg)")
            confirmation_data = {
                "confirmed_price": confirmed_price,
                "confirmed_quantity": confirmed_quantity
            }
        
        # 4. Process transition
        if st.button("Státusz frissítése"):
            # 5. Call API
            success, result = offers_api.update_offer_status(
                offer_id, 
                selected_status,
                confirmation_data=confirmation_data
            )
            
            # 6. Handle response
            if success:
                show_success("Státusz sikeresen frissítve!")
                # 7. Update cache and UI
                st.rerun()
            else:
                show_error(f"Hiba a státusz frissítésekor: {result}")
    else:
        st.info("Nincs elérhető státuszváltás ebből az állapotból.")
```

## 7. Common Naming Conventions

### 7.1 Module Naming

- `*_components.py`: UI component definitions
- `*_api.py`: API client modules
- `*_utils.py`: Utility functions
- Files named after their primary function (e.g., `dashboard.py`, `calendar.py`) 

### 7.2 Function Naming

- `show_*`: Top-level page rendering functions
- `render_*`: Component rendering functions
- `display_*`: Data visualization functions
- `handle_*`: Event/action handling functions
- `validate_*`: Validation functions
- `format_*`: Formatting functions
- `process_*`: Data processing functions
- `get_*`: Data retrieval functions
- `init_*`: Initialization functions

### 7.3 Variable Naming

- `*_id`: Identifiers
- `*_data`: Raw data structures
- `*_df`: Pandas DataFrames
- `*_filter`: Filter variables
- `selected_*`: Selected values from UI elements
- `is_*`: Boolean flags

## 8. Integration Points and Extensions

### 8.1 JavaScript Integration

The application extends Streamlit with custom JavaScript for enhanced UI features:

```python
def inject_screen_detection():
    """Injects JavaScript for device and screen detection."""
    st.markdown("""
    <script>
        function updateScreenInfo() {
            var screenWidth = window.innerWidth;
            var isMobile = screenWidth < 768;
            var isTablet = screenWidth >= 768 && screenWidth < 992;
            
            // Send to Streamlit
            window.parent.postMessage({
                type: "streamlit:setComponentValue",
                value: {
                    screen_width: screenWidth,
                    is_mobile: isMobile,
                    is_tablet: isTablet
                }
            }, "*");
        }
        
        // Run on load
        updateScreenInfo();
        
        // Run on resize
        window.addEventListener('resize', updateScreenInfo);
    </script>
    """, unsafe_allow_html=True)
```

### 8.2 Third-party Libraries Integration

The application integrates with several third-party libraries:

```python
# Plotly for interactive charts
import plotly.express as px
import plotly.graph_objects as go

# Using Plotly for visualization
def render_status_distribution_chart(offers):
    """Renders a pie chart showing status distribution."""
    # Count offers by status
    status_counts = {}
    for offer in offers:
        status = offer.get("status", "UNKNOWN")
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Create dataframe for Plotly
    df = pd.DataFrame({
        "status": list(status_counts.keys()),
        "count": list(status_counts.values())
    })
    
    # Add formatted status names
    df["status_name"] = df["status"].apply(format_status)
    
    # Create pie chart
    fig = px.pie(
        df, 
        values="count", 
        names="status_name", 
        title="Ajánlatok státusz szerinti megoszlása",
        color_discrete_sequence=px.colors.qualitative.Bold
    )
    
    # Display the chart
    st.plotly_chart(fig, use_container_width=True)
```

## 9. Responsive Design Implementation

The application adapts to different device types (desktop, tablet, mobile):

```python
def render_responsive_calendar(events, view_type="month"):
    """
    Renders a responsive calendar based on device type.
    
    Args:
        events (list): Calendar events
        view_type (str): Calendar view type (month, week, day)
    """
    # Check device type
    is_mobile = st.session_state.get("is_mobile", False)
    is_tablet = st.session_state.get("is_tablet", False)
    
    if is_mobile:
        # Mobile version - Card-based view
        render_mobile_calendar_cards(events, view_type)
    elif is_tablet:
        # Tablet version - Simplified grid
        render_tablet_calendar_grid(events, view_type)
    else:
        # Desktop version - Full calendar
        render_desktop_calendar(events, view_type)
```

## 10. Common Design Patterns

### 10.1 Factory Pattern for UI Components

```python
def create_status_badge(status):
    """
    Factory function to create a status badge with appropriate styling.
    
    Args:
        status (str): Status code
        
    Returns:
        str: HTML for the status badge
    """
    status_config = {
        "CREATED": {"color": "#FFA726", "icon": "🆕", "name": "Létrehozva"},
        "CONFIRMED_BY_COMPANY": {"color": "#42A5F5", "icon": "✅", "name": "Visszaigazolva"},
        "ACCEPTED_BY_USER": {"color": "#66BB6A", "icon": "👍", "name": "Elfogadva"},
        "REJECTED_BY_USER": {"color": "#EF5350", "icon": "❌", "name": "Elutasítva"},
        "FINALIZED": {"color": "#7E57C2", "icon": "🏁", "name": "Véglegesítve"}
    }
    
    config = status_config.get(status, {"color": "#9E9E9E", "icon": "❓", "name": status or "Ismeretlen"})
    
    return f"""
    <div style="
        display: inline-block;
        padding: 4px 8px;
        background-color: {config['color']};
        color: white;
        border-radius: 4px;
        font-weight: bold;
    ">
        {config['icon']} {config['name']}
    </div>
    """
```

### 10.2 Observer Pattern for Status Updates

```python
def register_status_change_handlers():
    """
    Sets up event handlers for status changes using the observer pattern.
    """
    # Define handlers for each status transition
    def on_confirm(offer_id, confirmation_data):
        """Handler for confirmation events."""
        if confirmation_data and "confirmed_price" in confirmation_data:
            success, result = offers_api.confirm_offer(offer_id, confirmation_data)
            return success, result
        return False, "Hiányzó visszaigazolási adatok"
    
    def on_accept(offer_id):
        """Handler for acceptance events."""
        success, result = offers_api.accept_offer(offer_id)
        return success, result
    
    def on_reject(offer_id, reason=None):
        """Handler for rejection events."""
        success, result = offers_api.reject_offer(offer_id, reason)
        return success, result
    
    # Register handlers in session state for later use
    st.session_state.status_handlers = {
        "confirm": on_confirm,
        "accept": on_accept,
        "reject": on_reject
    }
```

### 10.3 Strategy Pattern for Data Display

```python
def display_offer_data(offer, display_strategy="table"):
    """
    Displays offer data using the specified strategy.
    
    Args:
        offer (dict): Offer data
        display_strategy (str): Display strategy (table, card, compact)
    """
    # Strategy implementations
    strategies = {
        "table": display_as_table,
        "card": display_as_card,
        "compact": display_as_compact_summary
    }
    
    # Get the appropriate strategy
    display_func = strategies.get(display_strategy, display_as_table)
    
    # Execute the strategy
    display_func(offer)
```

## 11. API Response Handling

### 11.1 Standard API Response Pattern

API responses follow a consistent pattern with a tuple of `(success, result)`:

```python
def handle_api_response(response_tuple, success_message=None, error_prefix=None):
    """
    Standardized handler for API responses.
    
    Args:
        response_tuple (tuple): (success, result) from API call
        success_message (str, optional): Message to display on success
        error_prefix (str, optional): Prefix for error messages
        
    Returns:
        bool: Whether the operation was successful
    """
    success, result = response_tuple
    
    if success:
        if success_message:
            show_success(success_message)
        return True
    else:
        error_msg = f"{error_prefix or 'Hiba'}: {result}"
        show_error(error_msg)
        return False
```

## 12. Conclusion

This agricultural product offer management application is structured as a modular, component-based Streamlit web application. It follows clear architectural patterns to maintain separation of concerns across presentation, business logic, data access, and utility layers.

The architecture is optimized for:

1. **Modularity**: Components and functionality are isolated for easier maintenance
2. **Reusability**: Common patterns are abstracted into reusable functions
3. **Responsiveness**: The UI adapts to different device types
4. **Extensibility**: The system can be extended with new features without major restructuring

This documentation provides comprehensive context for understanding how the different components interact, the flow of data through the system, and the design patterns employed. When working with partial code from this system, you can use this understanding to infer the structure and purpose of code you haven't seen based on naming conventions, function signatures, and architectural patterns described here.
