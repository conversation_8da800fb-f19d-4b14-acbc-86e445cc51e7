# IV. Deployment & Operations Documentation

## Docker

### `Dockerfile` Explanation

The backend service is built using a Python 3.11 slim image with the following configuration:

```dockerfile
# Official Python base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \  # For PostgreSQL client tools
    build-essential \    # For compiling Python packages
    libpq-dev \          # PostgreSQL development libraries
    && rm -rf /var/lib/apt/lists/*  # Clean up to reduce image size

# Environment variables
ENV PYTHONDONTWRITEBYTECODE 1  # Prevents Python from writing .pyc files
ENV PYTHONUNBUFFERED 1         # Ensures Python output is sent straight to terminal
ENV PYTHONPATH=/app            # Sets Python path to the app directory

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Add Pydantic-settings separately
RUN pip install --no-cache-dir pydantic-settings

# Copy project files
COPY . .

# Expose port for the FastAPI application
EXPOSE 8000

# Start the FastAPI server with hot reload enabled
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### Building the Docker Image

To build the backend image manually:

```bash
docker build -t termelo-backend .
```

### Running the Docker Container (Standalone)

To run the backend container standalone:

```bash
docker run -d -p 8000:8000 \
  -e DATABASE_URL=postgresql://postgres:<EMAIL>:5433/termelo_db \
  -e SECRET_KEY=your-secret-key-here \
  -e ENVIRONMENT=development \
  --name termelo-backend \
  termelo-backend
```

## Streamlit Frontend Dockerfile

The Streamlit frontend is built with its own Dockerfile:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy all files
COPY . .

# Debug file structure
RUN echo "=== App directory structure ===" && \
    ls -la /app && \
    echo "=== Pages directory structure ===" && \
    ls -la /app/pages && \
    echo "=== Components directory structure ===" && \
    ls -la /app/components && \
    echo "=== Utils directory structure ===" && \
    ls -la /app/utils

# Set environment variables
ENV PYTHONPATH=/app
ENV API_BASE_URL=http://backend:8000/api

# Fix Windows line endings and make script executable
RUN apt-get update && apt-get install -y dos2unix && dos2unix create_symlinks.sh && chmod +x create_symlinks.sh

# Run symlink creation script
WORKDIR /app
RUN bash create_symlinks.sh

# Debug configuration files
RUN echo "=== Config file contents ===" && \
    cat /app/config.py
RUN echo "=== Session file contents ===" && \
    cat /app/utils/session.py

EXPOSE 8501

# Start Streamlit with custom sidebar configuration
CMD ["streamlit", "run", "main.py", "--client.showSidebarNavigation=false"]
```

## Docker Compose

### `docker-compose.yml` Explanation

The application uses Docker Compose to orchestrate multiple services:

#### Database Service (`db`)
- **Image**: PostgreSQL 15
- **Container Name**: termelo-db
- **Environment Variables**: Sets up database credentials
- **Volumes**: Persistent PostgreSQL data storage
- **Ports**: 5433:5432 (exposed to host at port 5433)
- **Healthcheck**: Ensures database is ready before starting dependent services
- **Network**: termelo-network

#### Backend Service (`backend`)
- **Build**: Uses local Dockerfile
- **Container Name**: backend
- **Ports**: 8000:8000 (FastAPI service)
- **Environment Variables**: Configuration for FastAPI application
- **Volumes**: Mounts local app directory for development
- **Dependencies**: Waits for the database to be healthy
- **Command**: Runs initialization script and starts uvicorn server
- **Network**: termelo-network

#### Streamlit Frontend Service (`streamlit`)
- **Build**: Uses Dockerfile in streamlit_app directory
- **Container Name**: streamlit
- **Ports**: 8501:8501 (Streamlit web interface)
- **Environment Variables**: API configuration and application settings
- **Volumes**: Mounts local streamlit application for development
- **Dependencies**: Requires backend service
- **Network**: termelo-network

#### Debug Service (`debug`)
- **Build**: Uses Dockerfile in debug directory
- **Container Name**: debug-tools
- **Volumes**: Mounts debug tools and Docker socket
- **Environment Variables**: Container references and logging configuration
- **Dependencies**: All other services
- **Network**: termelo-network
- **Interactive Mode**: Enabled for debugging

### Docker Compose Commands

#### Starting the Application

```bash
docker-compose up -d
```

This starts all services in detached mode. The application will be available at:
- Backend API: http://localhost:8000
- Streamlit Frontend: http://localhost:8501

#### Stopping the Application

```bash
docker-compose down
```

To remove volumes when stopping:

```bash
docker-compose down -v
```

#### Viewing Logs

View logs from all services:

```bash
docker-compose logs -f
```

View logs from a specific service:

```bash
docker-compose logs -f backend
docker-compose logs -f streamlit
docker-compose logs -f db
```

#### Rebuilding Services

Rebuild all services:

```bash
docker-compose build
```

Rebuild a specific service:

```bash
docker-compose build backend
docker-compose build streamlit
```

#### Executing Commands Within Containers

Run database migrations:

```bash
docker-compose exec backend alembic upgrade head
```

Create a new migration:

```bash
docker-compose exec backend alembic revision --autogenerate -m "migration description"
```

Open PostgreSQL CLI:

```bash
docker-compose exec db psql -U postgres -d termelo_db
```

Execute Python scripts in the backend:

```bash
docker-compose exec backend python -m app.scripts.your_script
```

## Environment Variables

### Backend Service

| Variable | Description | Example Value |
|----------|-------------|---------------|
| APP_NAME | Application display name | Mezőgazdasági Termékkezelő Rendszer |
| ENVIRONMENT | Deployment environment | development |
| BACKEND_HOST | Host IP for backend server | 0.0.0.0 |
| BACKEND_PORT | Port for backend server | 8000 |
| DATABASE_URL | SQLAlchemy connection string | **************************************/termelo_db |
| DB_HOST | Database hostname | db |
| DB_PORT | Database port | 5432 |
| DB_USER | Database username | postgres |
| DB_PASSWORD | Database password | postgres |
| DB_NAME | Database name | termelo_db |
| SECRET_KEY | JWT signing key | your-secret-key-here |
| JWT_ALGORITHM | Algorithm for JWT | HS256 |
| ALGORITHM | Alternative name for JWT algorithm | HS256 |
| ACCESS_TOKEN_EXPIRE_MINUTES | JWT token expiration | 30 |
| STREAMLIT_HOST | Host for Streamlit frontend | localhost |
| STREAMLIT_PORT | Port for Streamlit frontend | 8501 |
| STREAMLIT_BROWSER_SERVER_ADDRESS | Backend URL for Streamlit | http://localhost:8000 |
| API_AUTH_TOKEN | Authentication token for API | JWT_TOKEN |

### Streamlit Frontend Service

| Variable | Description | Example Value |
|----------|-------------|---------------|
| API_HOST | Backend API host with protocol | http://backend:8000 |
| API_BASE_URL | Complete API base URL | http://backend:8000/api |
| APP_NAME | Application display name | Mezőgazdasági Termékkezelő Rendszer |
| COMPANY_NAME | Company name for branding | Zöldség Világ Kft. |
| API_AUTH_TOKEN | Authentication token for API calls | PASTE_IDE_AZ_ADMIN_TOKEN |

### Database Service

| Variable | Description | Example Value |
|----------|-------------|---------------|
| POSTGRES_USER | PostgreSQL username | postgres |
| POSTGRES_PASSWORD | PostgreSQL password | postgres |
| POSTGRES_DB | PostgreSQL database name | termelo_db |

## Database Setup

### PostgreSQL Configuration

The PostgreSQL database is configured through environment variables in the `docker-compose.yml` file:

```yaml
environment:
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: termelo_db
```

### Data Persistence

PostgreSQL data is stored in a Docker volume:

```yaml
volumes:
  - postgres_data:/var/lib/postgresql/data
```

This ensures data persists across container restarts. The volume is defined at the bottom of the `docker-compose.yml` file:

```yaml
volumes:
  postgres_data:
```

### Database Initialization

The application includes a database initialization script (`init-db.sh`) that runs when the backend container starts:

```yaml
command: >
  sh -c "chmod +x /app/init-db.sh && /app/init-db.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"
```

This script likely performs:
1. Waiting for the database to be ready
2. Running Alembic migrations
3. Possibly seeding initial data

## Alembic Database Migrations

The project uses Alembic for database migrations, configured in `alembic.ini`:

```ini
[alembic]
script_location = migrations
sqlalchemy.url = **************************************/termelo_db
```

### Creating a New Migration

```bash
docker-compose exec backend alembic revision --autogenerate -m "description"
```

### Applying Migrations

```bash
docker-compose exec backend alembic upgrade head
```

### Rolling Back Migrations

```bash
docker-compose exec backend alembic downgrade -1
```

## Deployment Strategy

### Development Deployment

The provided configuration is suitable for development with:
- Hot-reloading enabled
- Local directory mounting for quick code changes
- Exposed debug ports

### Production Deployment Recommendations

For production deployment:

1. **Security Enhancements**:
   - Use environment file or secrets management instead of hardcoded values
   - Generate a strong SECRET_KEY
   - Change default database credentials
   - Consider using environment-specific Docker Compose files

2. **Performance Optimization**:
   - Disable debug modes and hot-reloading
   - Consider using gunicorn with multiple workers for backend
   - Use a proper reverse proxy (Nginx/Traefik) for HTTPS and load balancing

3. **CI/CD Pipeline Integration**:
   - Add Dockerfiles to an automated build pipeline
   - Run tests before deployment
   - Automate database migrations with safety checks

4. **Example Production Docker Compose Adjustments**:
   ```yaml
   # Add to backend service for production
   command: gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
   ```

## Monitoring & Logging

### Accessing Logs

View application logs:

```bash
docker-compose logs -f backend
docker-compose logs -f streamlit
```

### Debug Tools

The project includes a dedicated debug service:

```yaml
debug:
  build:
    context: ./debug
    dockerfile: Dockerfile
  container_name: debug-tools
  volumes:
    - ./debug:/app/debug
    - /var/run/docker.sock:/var/run/docker.sock
    - ./logs:/app/logs
  environment:
    CONTAINER_STREAMLIT: streamlit
    CONTAINER_BACKEND: backend
    CONTAINER_DB: termelo-db
    DOCKER_HOST: unix:///var/run/docker.sock
    DEBUG_CONFIG_FILE: /app/debug/config/log_paths.json
```

This service appears to provide advanced debugging and logging capabilities.

### Health Checks

The database service includes a health check:

```yaml
healthcheck:
  test: ["CMD-SHELL", "pg_isready -U postgres"]
  interval: 5s
  timeout: 5s
  retries: 5
```

## Troubleshooting

### Common Issues and Solutions

1. **Database Connection Failures**:
   - Issue: Backend cannot connect to database
   - Solution: Check if the database container is running and healthy
   ```bash
   docker-compose ps
   docker-compose logs db
   ```

2. **Migration Errors**:
   - Issue: Alembic migrations fail
   - Solution: Check migration scripts for errors and database connection
   ```bash
   docker-compose exec backend alembic current
   ```

3. **Port Conflicts**:
   - Issue: Services fail to start due to port conflicts
   - Solution: Change the exposed ports in docker-compose.yml or stop conflicting services
   ```bash
   # Check for processes using the ports
   lsof -i :8000
   lsof -i :8501
   lsof -i :5433
   ```

4. **Container Build Errors**:
   - Issue: Failed to build container images
   - Solution: Check Dockerfile syntax and ensure all referenced files exist
   ```bash
   docker-compose build --no-cache <service_name>
   ```

5. **Streamlit-Backend Communication Issues**:
   - Issue: Streamlit frontend cannot connect to backend API
   - Solution: Verify network configuration and API_BASE_URL environment variable
   ```bash
   # Check if backend is accessible from streamlit container
   docker-compose exec streamlit curl -I http://backend:8000
   ```

### Diagnostic Steps

1. **Check Container Status**:
   ```bash
   docker-compose ps
   ```

2. **Inspect Container Logs**:
   ```bash
   docker-compose logs -f <service_name>
   ```

3. **Verify Network Connectivity**:
   ```bash
   docker network inspect termelo-network
   ```

4. **Check Resource Usage**:
   ```bash
   docker stats
   ```

5. **Execute Commands Within Containers**:
   ```bash
   docker-compose exec <service_name> <command>
   ```

6. **Restart Specific Services**:
   ```bash
   docker-compose restart <service_name>
   ```

7. **Rebuild and Force Recreation**:
   ```bash
   docker-compose up -d --build --force-recreate <service_name>
   ```
