#!/usr/bin/env python3
"""
Test script to validate Dict import fixes
"""

import ast
import sys
import os

def test_file_syntax_and_imports(file_path):
    """Test file syntax and Dict import handling"""
    print(f"\n🔍 Testing {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test syntax
        try:
            ast.parse(content)
            print("  ✅ Valid Python syntax")
        except SyntaxError as e:
            print(f"  ❌ Syntax error: {e}")
            return False
        
        # Check for Dict usage
        has_dict_usage = "Dict[" in content
        has_dict_import = "from typing import" in content and "Dict" in content
        has_fallback = "Dict = dict" in content
        
        if has_dict_usage:
            print("  📋 Found Dict type hint usage")
            if has_dict_import:
                print("  ✅ Has typing import for Dict")
            else:
                print("  ❌ Missing typing import for Dict")
                return False
                
            if has_fallback:
                print("  ✅ Has Dict fallback mechanism")
            else:
                print("  ⚠️ No Dict fallback mechanism")
        else:
            print("  ℹ️ No Dict usage found")
        
        # Check for safety mechanisms
        if "try:" in content and "except ImportError:" in content:
            print("  ✅ Has import error handling")
        elif has_dict_usage:
            print("  ⚠️ Dict usage without import error handling")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing file: {e}")
        return False

def test_critical_files():
    """Test the most critical files that were fixed"""
    
    base_path = "streamlit_app/pages/operator/offer_management"
    
    critical_files = [
        "data_coordinator.py",
        "api_client.py"
    ]
    
    print("🚨 TESTING CRITICAL FILES FOR DICT IMPORT FIXES")
    print("=" * 60)
    
    all_passed = True
    
    for file_name in critical_files:
        file_path = os.path.join(base_path, file_name)
        if os.path.exists(file_path):
            if not test_file_syntax_and_imports(file_path):
                all_passed = False
        else:
            print(f"\n❌ File not found: {file_path}")
            all_passed = False
    
    return all_passed

def validate_specific_fixes():
    """Validate specific fixes made to the files"""
    print("\n🔧 VALIDATING SPECIFIC FIXES")
    print("=" * 40)
    
    # Check data_coordinator.py
    dc_path = "streamlit_app/pages/operator/offer_management/data_coordinator.py"
    try:
        with open(dc_path, 'r') as f:
            dc_content = f.read()
        
        checks = [
            ("validate_typing_imports function", "def validate_typing_imports():"),
            ("Typing import with try/except", "try:\n    from typing import Dict"),
            ("Dict fallback", "Dict = dict"),
            ("Safety check for Dict", "if 'Dict' not in globals():"),
            ("Validation call in constructor", "validate_typing_imports()")
        ]
        
        print("📋 data_coordinator.py checks:")
        for check_name, pattern in checks:
            if pattern in dc_content:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                return False
    
    except Exception as e:
        print(f"❌ Error checking data_coordinator.py: {e}")
        return False
    
    # Check api_client.py
    ac_path = "streamlit_app/pages/operator/offer_management/api_client.py"
    try:
        with open(ac_path, 'r') as f:
            ac_content = f.read()
        
        checks = [
            ("Typing import with try/except", "try:\n    from typing import Dict"),
            ("Dict fallback", "Dict = dict"),
            ("Safety check for Dict", "if 'Dict' not in globals():"),
        ]
        
        print("\n📋 api_client.py checks:")
        for check_name, pattern in checks:
            if pattern in ac_content:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                return False
    
    except Exception as e:
        print(f"❌ Error checking api_client.py: {e}")
        return False
    
    return True

def simulate_dict_import_test():
    """Simulate the Dict import scenarios"""
    print("\n🧪 SIMULATING DICT IMPORT SCENARIOS")
    print("=" * 45)
    
    # Test scenario 1: Normal import
    try:
        from typing import Dict, List, Any
        print("✅ Scenario 1: Normal typing import successful")
        
        # Test type hint
        test_var: Dict[str, Any] = {"test": "value"}
        print("✅ Dict type hint works correctly")
        
    except Exception as e:
        print(f"❌ Scenario 1 failed: {e}")
        
        # Test scenario 2: Fallback
        try:
            Dict = dict
            test_var: Dict[str, Any] = {"test": "value"}  # This might not work but shouldn't crash
            print("✅ Scenario 2: Dict fallback works")
        except Exception as e2:
            print(f"❌ Scenario 2 also failed: {e2}")
    
    return True

if __name__ == "__main__":
    print("🚨 DICT IMPORT ERROR FIX VALIDATION")
    print("=" * 50)
    
    success = True
    
    # Test critical files
    if not test_critical_files():
        success = False
    
    # Validate specific fixes
    if not validate_specific_fixes():
        success = False
    
    # Simulate import scenarios
    simulate_dict_import_test()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL DICT IMPORT FIXES VALIDATED SUCCESSFULLY!")
        print("🔧 Key improvements:")
        print("   ✅ Python version-independent typing imports")
        print("   ✅ Dict fallback mechanisms in place")
        print("   ✅ Import error handling")
        print("   ✅ Runtime validation functions")
        print("\n🚀 The 'name Dict is not defined' error should be resolved!")
    else:
        print("❌ VALIDATION FAILED!")
        print("   Some Dict import issues remain")
    
    sys.exit(0 if success else 1)