# Application Architecture Overview

This document provides a comprehensive overview of the Agricultural Product Management System (Mezőgazdasági Termékkezelő Rendszer), a three-tier web application built with FastAPI backend, Streamlit frontend, and PostgreSQL database.

## 1. High-Level Architecture

The application follows a modern three-tier architecture pattern:

```mermaid
graph TB
    subgraph "Frontend Layer"
        ST[Streamlit App<br/>Port 8501]
        PAGES[Pages & Components]
        API_CLIENT[API Client]
    end
    
    subgraph "Backend Layer"
        FA[FastAPI Server<br/>Port 8000]
        AUTH[Authentication]
        ENDPOINTS[API Endpoints]
        SERVICES[Business Logic Services]
    end
    
    subgraph "Database Layer"
        PG[(PostgreSQL<br/>Port 5432)]
        MODELS[SQLAlchemy Models]
        MIGRATIONS[Alembic Migrations]
    end
    
    ST --> API_CLIENT
    API_CLIENT -->|HTTP/JSON| FA
    FA --> SERVICES
    SERVICES --> MODELS
    MODELS --> PG
    
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef database fill:#e8f5e8
    
    class ST,PAGES,API_CLIENT frontend
    class FA,AUTH,ENDPOINTS,SERVICES backend
    class PG,MODELS,MIGRATIONS database
```

### Architecture Principles

- **Separation of Concerns**: Clear separation between presentation (Streamlit), business logic (FastAPI), and data persistence (PostgreSQL)
- **API-First Design**: Frontend communicates with backend exclusively through REST API
- **Role-Based Access Control**: Three user roles (Producer, Operator, Admin) with different permissions
- **Containerized Deployment**: Docker-based deployment with docker-compose orchestration

## 2. Component Deep Dive

### 2.1. Backend (FastAPI)

#### API Endpoints

The backend exposes a comprehensive REST API organized by functional domains:

| Endpoint Group | Base Path | Purpose | Authentication Required |
|---------------|-----------|---------|------------------------|
| Authentication | `/api/auth` | User login, registration, password reset | Partial |
| Users | `/api/users` | User management operations | Yes |
| Products | `/api/products` | Product catalog management | Yes |
| Offers | `/api/offers` | Offer lifecycle management | Yes |
| Admin | `/api/admin` | Administrative functions | Admin only |
| Notifications | `/api/notifications` | User notification system | Yes |
| Saved Filters | `/api/saved-filters` | User filter preferences | Yes |
| AI Compatibility | `/api/ai` | AI/ML integration endpoints | Yes |

#### Authentication Flow

The application implements JWT-based authentication using `python-jose` and `passlib`:

**Token Creation Process:**
```python
# app/services/user_service.py
def create_user_token(user: User) -> str:
    """Create JWT token for authenticated user"""
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    token_data = {
        "user_id": user.id,
        "role": user.role,
        "exp": datetime.utcnow() + access_token_expires
    }
    
    return jwt.encode(
        token_data, 
        settings.SECRET_KEY, 
        algorithm=settings.JWT_ALGORITHM
    )
```

**Password Hashing:**
```python
# app/utils/security.py
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)
```

**Token Validation:**
```python
# app/api/dependencies.py
def get_current_user(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """Extract and validate user from JWT token"""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
        )
        token_data = TokenData(user_id=payload.get("user_id"))
        
        if token_data.user_id is None:
            raise credentials_exception
            
        user = db.query(User).filter(User.id == token_data.user_id).first()
        if user is None or not user.is_active:
            raise credentials_exception
            
        return user
    except (JWTError, ValidationError):
        raise credentials_exception
```

#### Database Layer

**SQLAlchemy Models and Relationships:**

The application uses SQLAlchemy 2.0 with declarative base classes:

```python
# app/models/user.py
class User(Base, TimestampMixin):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, index=True)
    company_name = Column(String(255))
    contact_name = Column(String(255), nullable=False)
    phone_number = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    offers = relationship("Offer", foreign_keys="Offer.user_id", back_populates="user")
    notifications = relationship("Notification", back_populates="user")
    saved_filters = relationship("UserSavedFilter", back_populates="user")
```

```python
# app/models/offer.py
class Offer(Base, TimestampMixin):
    __tablename__ = "offers"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_type_id = Column(Integer, ForeignKey("product_types.id"), nullable=False)
    quantity_in_kg = Column(Numeric(10, 2), nullable=False)
    delivery_date = Column(Date, nullable=False)
    status = Column(String(50), nullable=False, default="CREATED")
    confirmed_quantity = Column(Numeric(10, 2))
    confirmed_price = Column(Numeric(10, 2))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="offers")
    product_type = relationship("ProductType", back_populates="offers")
    logs = relationship("OfferLog", back_populates="offer")
```

**Alembic Migration Management:**

Database schema changes are managed through Alembic migrations:

```python
# migrations/env.py
from app.db.base import Base
target_metadata = Base.metadata

def run_migrations_online():
    """Run migrations in 'online' mode with database connection"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    
    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )
        
        with context.begin_transaction():
            context.run_migrations()
```

#### Core Services

Business logic is encapsulated in service classes:

```python
# app/services/user_service.py
def create_user(db: Session, user_in: UserCreate) -> User:
    """Create new user with hashed password"""
    # Check if user already exists
    existing_user = get_user_by_email(db, user_in.email)
    if existing_user:
        raise ValueError("User with this email already exists")
    
    # Hash password and create user
    hashed_password = hash_password(user_in.password)
    activation_token = secrets.token_urlsafe(32)
    
    db_user = User(
        email=user_in.email,
        password_hash=hashed_password,
        role=user_in.role,
        contact_name=user_in.contact_name,
        phone_number=user_in.phone_number,
        company_name=user_in.company_name,
        tax_id=user_in.tax_id,
        activation_token=activation_token,
        is_active=False
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user
```

### 2.2. Frontend (Streamlit)

#### Page Structure

The Streamlit frontend is organized into role-based page hierarchies:

```
streamlit_app/
├── main.py                 # Application entry point
├── pages/
│   ├── auth/              # Authentication pages
│   │   ├── login.py
│   │   ├── register.py
│   │   └── reset_password.py
│   ├── producer/          # Producer role pages
│   │   ├── dashboard.py
│   │   ├── create_offer.py
│   │   └── offers.py
│   ├── operator/          # Operator role pages
│   │   ├── dashboard.py
│   │   ├── offer_management.py
│   │   └── reports.py
│   └── admin/             # Admin role pages
│       ├── dashboard.py
│       ├── user_management.py
│       └── product_management.py
├── components/            # Reusable UI components
├── api/                  # API client modules
└── utils/                # Utility functions
```

#### State Management

Streamlit session state is used for authentication and user data persistence:

```python
# streamlit_app/utils/session.py
DEFAULT_SESSION_VARS = {
    "user": "user",
    "token": "auth_token",
    "authenticated": "authenticated",
}

def set_user_session(user_data, token):
    """Set user session data after successful login"""
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    st.session_state[session_vars.get("user", "user")] = user_data
    st.session_state[session_vars.get("token", "auth_token")] = token
    st.session_state[session_vars.get("authenticated", "authenticated")] = True
    
    # Set token expiry
    if token:
        expiry = time.time() + (60 * 60)  # 1 hour
        st.session_state["token_expiry"] = expiry

def is_authenticated():
    """Check if user is currently authenticated"""
    session_vars = getattr(config, 'SESSION_VARS', DEFAULT_SESSION_VARS)
    
    # Check if authenticated flag is set
    if not st.session_state.get(session_vars.get("authenticated", "authenticated"), False):
        return False
    
    # Check token expiry
    token_expiry = st.session_state.get("token_expiry")
    if token_expiry and time.time() > token_expiry:
        clear_session()
        return False
    
    return True
```

#### API Interaction

The frontend communicates with the backend through a centralized API client:

```python
# streamlit_app/api_client.py
class APIClient:
    """Centralized API client for HTTP requests"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _make_request(self, method: str, endpoint: str, **kwargs):
        """Execute HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"API request failed: {e}")
            raise APIError(f"Request failed: {e}")
```

**Authentication API Calls:**
```python
# streamlit_app/api/auth.py
def login(email, password):
    """Authenticate user and establish session"""
    try:
        # Login request
        response = requests.post(
            f"{config.API_BASE_URL}/auth/login",
            data={"username": email, "password": password},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            
            # Get user data
            me_response = requests.get(
                f"{config.API_BASE_URL}/auth/me",
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if me_response.status_code == 200:
                user_data = me_response.json()
                
                # Set session state
                set_user_session(user_data, token)
                return True, user_data
        
        return False, "Authentication failed"
        
    except requests.RequestException as e:
        return False, f"Network error: {str(e)}"
```

## 3. End-to-End Workflows

### 3.1. User Login and Session Creation

**Step-by-step authentication flow:**

1. **Frontend Login Form** (`streamlit_app/components/auth_forms.py`):
```python
def login_form():
    with st.form("login_form"):
        email = st.text_input("E-mail cím")
        password = st.text_input("Jelszó", type="password")
        submit = st.form_submit_button("Bejelentkezés")
    
    if submit:
        success, result = auth.login(email, password)
        if success:
            st.success("Sikeres bejelentkezés!")
            return True
        else:
            st.error(f"Bejelentkezési hiba: {result}")
```

2. **API Client Request** (`streamlit_app/api/auth.py`):
```python
def login(email, password):
    response = requests.post(
        f"{config.API_BASE_URL}/auth/login",
        data={"username": email, "password": password},
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
```

3. **Backend Authentication** (`app/api/endpoints/auth.py`):
```python
@router.post("/login", response_model=Token)
def login(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
):
    # Authenticate user
    user = authenticate_user(db, form_data.username, form_data.password)
    
    if not user or not user.is_active:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Create JWT token
    access_token = create_user_token(user)
    
    return {"access_token": access_token, "token_type": "bearer"}
```

4. **User Service Authentication** (`app/services/user_service.py`):
```python
def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.password_hash):
        return None
    return user
```

5. **Session State Update** (`streamlit_app/utils/session.py`):
```python
def set_user_session(user_data, token):
    st.session_state["user"] = user_data
    st.session_state["auth_token"] = token
    st.session_state["authenticated"] = True
    st.session_state["token_expiry"] = time.time() + 3600
```

### 3.2. Offer Creation Workflow

**Complete offer creation flow from frontend to database:**

1. **Frontend Form** (`streamlit_app/pages/producer/create_offer.py`):
```python
def create_offer_form():
    with st.form("create_offer"):
        product_type = st.selectbox("Termék típus", options=product_types)
        quantity = st.number_input("Mennyiség (kg)", min_value=1)
        delivery_date = st.date_input("Szállítási dátum")
        submit = st.form_submit_button("Ajánlat létrehozása")
    
    if submit:
        offer_data = {
            "product_type_id": product_type["id"],
            "quantity_in_kg": quantity,
            "delivery_date": delivery_date.isoformat()
        }
        
        success, result = offers_api.create_offer(offer_data)
```

2. **API Client Call** (`streamlit_app/api/offers.py`):
```python
def create_offer(offer_data):
    token = get_auth_token()
    
    response = requests.post(
        f"{config.API_BASE_URL}/offers",
        json=offer_data,
        headers={"Authorization": f"Bearer {token}"}
    )
    
    if response.status_code == 201:
        return True, response.json()
    else:
        return False, response.json().get("detail", "Error creating offer")
```

3. **Backend Endpoint** (`app/api/endpoints/offers.py`):
```python
@router.post("/", response_model=OfferResponse)
def create_offer(
    *,
    db: Session = Depends(get_db),
    offer_in: OfferCreate,
    current_user: User = Depends(get_current_user)
):
    # Validate input data
    if offer_in.delivery_date <= date.today():
        raise HTTPException(400, "Delivery date must be in the future")
    
    # Create offer through service
    offer = offer_service.create_offer(db, offer_in, current_user)
    
    return offer
```

4. **Service Layer** (`app/services/offer_service.py`):
```python
def create_offer(db: Session, offer_in: OfferCreate, user: User) -> Offer:
    # Create offer instance
    db_offer = Offer(
        user_id=user.id,
        product_type_id=offer_in.product_type_id,
        quality_grade_id=offer_in.quality_grade_id,
        quantity_in_kg=offer_in.quantity_in_kg,
        delivery_date=offer_in.delivery_date,
        status="CREATED",
        created_by_user_id=user.id
    )
    
    # Save to database
    db.add(db_offer)
    db.commit()
    db.refresh(db_offer)
    
    # Log the creation
    log_offer_status_change(db, db_offer, None, "CREATED", user.id)
    
    return db_offer
```

5. **Database Persistence** (SQLAlchemy ORM):
```python
# The ORM automatically generates SQL:
# INSERT INTO offers (user_id, product_type_id, quantity_in_kg, delivery_date, status, created_by_user_id, created_at, updated_at)
# VALUES (?, ?, ?, ?, ?, ?, ?, ?)
```

## 4. Dependencies and Setup

### 4.1. Key Libraries

**Backend Dependencies:**
- **FastAPI 0.104.1**: Modern, fast web framework for building APIs
- **SQLAlchemy 2.0.22**: Python SQL toolkit and Object-Relational Mapping library
- **Alembic 1.12.1**: Database migration tool for SQLAlchemy
- **Pydantic >=2.0.0**: Data validation and settings management using Python type annotations
- **python-jose 3.3.0**: JavaScript Object Signing and Encryption library for JWT handling
- **passlib 1.7.4**: Password hashing library with bcrypt support
- **psycopg2-binary 2.9.9**: PostgreSQL adapter for Python
- **aiosmtplib 3.0.1**: Asynchronous SMTP client for email functionality

**Frontend Dependencies:**
- **Streamlit 1.32.0**: Framework for building data applications
- **pandas 2.2.0**: Data manipulation and analysis library
- **plotly 5.17.0**: Interactive plotting library
- **httpx 0.25.0**: HTTP client library for API communication
- **requests 2.31.0**: HTTP library for Python

### 4.2. Environment Setup

**Development Environment:**

1. **Clone Repository:**
```bash
git clone <repository-url>
cd agricultural-product-management
```

2. **Environment Variables:**
Create `.env` file with required configuration:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=termelo_db

# JWT Configuration
SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_BASE_URL=http://localhost:8000
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000

# Streamlit Configuration
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

3. **Docker Deployment:**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

4. **Manual Setup:**
```bash
# Backend setup
cd app/
pip install -r requirements.txt
alembic upgrade head
uvicorn main:app --host 0.0.0.0 --port 8000

# Frontend setup (in new terminal)
cd streamlit_app/
pip install -r requirements.txt
streamlit run main.py --server.port 8501
```

**Docker Configuration:**

```yaml
# docker-compose.yml
version: '3.8'
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: termelo_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  backend:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=**************************************/termelo_db

  frontend:
    build: ./streamlit_app
    ports:
      - "8501:8501"
    depends_on:
      - backend
    environment:
      - API_BASE_URL=http://backend:8000

volumes:
  postgres_data:
```

### 4.3. Database Initialization

**Automatic Migration on Startup:**
```python
# app/main.py
@app.on_event("startup")
def startup_event():
    """Initialize database on application startup"""
    create_tables_if_not_exist()
    init_db()  # Load initial data

def create_tables_if_not_exist():
    """Create missing database tables"""
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    model_tables = [table.name for table in Base.metadata.tables.values()]
    
    missing_tables = set(model_tables) - set(existing_tables)
    if missing_tables:
        Base.metadata.create_all(bind=engine)
```

**Manual Migration Commands:**
```bash
# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

This architecture provides a robust, scalable foundation for the agricultural product management system, with clear separation of concerns, comprehensive authentication, and maintainable code organization.