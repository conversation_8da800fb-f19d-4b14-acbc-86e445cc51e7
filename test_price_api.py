#!/usr/bin/env python3
"""
Test script az átlagár API funkciók teszteléséhez
"""
import sys
import os
import logging

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'streamlit_app'))
sys.path.insert(0, os.path.join(current_dir, 'streamlit_app', 'pages', 'operator', 'offer_management'))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_price_api():
    """Test the average price API functions"""
    print("🚀 ÁTLAGÁR API TESZT KEZDÉS")
    print("=" * 50)
    
    try:
        # Import API functions
        print("📦 API függvények importálása...")
        
        try:
            from streamlit_app.pages.operator.offer_management.api_client import get_average_confirmed_price, get_offers
            print("✅ API függvények sikeresen importálva")
        except ImportError as e:
            print(f"❌ Import hiba: {e}")
            # Próbáljuk alternatív útvonalon
            try:
                sys.path.append('/app/streamlit_app/pages/operator/offer_management')
                from api_client import get_average_confirmed_price, get_offers
                print("✅ API függvények alternatív útvonalon importálva")
            except ImportError as e2:
                print(f"❌ Alternatív import is sikertelen: {e2}")
                return
        
        # Test data based on debug info
        print("\n📊 TESZT ADATOK:")
        print("Product ID: 7 (Pritamin paprika)")
        print("Quality Grade ID: 19 (I. Osztály)")
        print("Category ID: 1 (Paprika)")
        
        print("\n🔍 1. ÖSSZES AJÁNLAT LEKÉRÉSE (státusz nélkül)")
        success, all_offers = get_offers()
        if success and all_offers:
            print(f"✅ Összes ajánlat: {len(all_offers)} db")
            
            # Analyze offers structure
            if all_offers and len(all_offers) > 0:
                sample_offer = all_offers[0]
                print(f"📋 Minta ajánlat mezői: {list(sample_offer.keys())}")
                
                # Check price fields
                price_fields = []
                for field in ['price', 'confirmed_price', 'unit_price', 'base_price']:
                    if field in sample_offer:
                        price_fields.append(f"{field}: {sample_offer[field]}")
                print(f"💰 Ár mezők: {price_fields}")
                
                # Count offers by status
                status_count = {}
                for offer in all_offers:
                    status = offer.get('status', 'UNKNOWN')
                    status_count[status] = status_count.get(status, 0) + 1
                print(f"📈 Státusz eloszlás: {status_count}")
        else:
            print(f"❌ Ajánlatok lekérése sikertelen: {all_offers}")
            return
        
        print("\n🔍 2. VISSZAIGAZOLT AJÁNLATOK LEKÉRÉSE")
        confirmed_statuses = ["CONFIRMED_BY_COMPANY", "ACCEPTED_BY_USER", "FINALIZED"]
        for status in confirmed_statuses:
            success, offers = get_offers({"status": status})
            if success and offers:
                print(f"✅ {status}: {len(offers)} db ajánlat")
            else:
                print(f"❌ {status}: {offers}")
        
        print("\n🔍 3. PRITAMIN PAPRIKA AJÁNLATOK (product_type_id=7)")
        success, product_offers = get_offers({"product_type_id": 7})
        if success and product_offers:
            print(f"✅ Pritamin paprika ajánlatok: {len(product_offers)} db")
            for offer in product_offers:
                offer_id = offer.get('id')
                status = offer.get('status')
                confirmed_price = offer.get('confirmed_price')
                price = offer.get('price')
                print(f"  - Ajánlat #{offer_id}: status={status}, confirmed_price={confirmed_price}, price={price}")
        else:
            print(f"❌ Pritamin paprika ajánlatok: {product_offers}")
        
        print("\n🔍 4. ÁTLAGÁR TESZTEK")
        
        # Test 1: Product + Quality Grade
        print("\n4.1. Termék + Minőségi osztály (product_id=7, quality_grade_id=19)")
        success, avg_price = get_average_confirmed_price(product_id=7, quality_grade_id=19)
        print(f"Eredmény: success={success}, avg_price={avg_price}")
        
        # Test 2: Only Product
        print("\n4.2. Csak termék (product_id=7)")
        success, avg_price = get_average_confirmed_price(product_id=7)
        print(f"Eredmény: success={success}, avg_price={avg_price}")
        
        # Test 3: Only Category
        print("\n4.3. Csak kategória (category_id=1)")
        success, avg_price = get_average_confirmed_price(category_id=1)
        print(f"Eredmény: success={success}, avg_price={avg_price}")
        
        print("\n🔍 5. MANUAL ÁTLAGÁR SZÁMÍTÁS")
        # Let's manually calculate average from confirmed offers
        all_confirmed_prices = []
        for offer in all_offers:
            if offer.get('status') in confirmed_statuses:
                for price_field in ['confirmed_price', 'price']:
                    price = offer.get(price_field)
                    if price is not None:
                        try:
                            price_float = float(price)
                            if price_float > 0:
                                all_confirmed_prices.append(price_float)
                                print(f"  Ajánlat #{offer.get('id')}: {price_field}={price_float}")
                                break
                        except (ValueError, TypeError):
                            continue
        
        if all_confirmed_prices:
            manual_avg = sum(all_confirmed_prices) / len(all_confirmed_prices)
            print(f"🧮 Manuális átlag: {manual_avg:.2f} Ft/kg ({len(all_confirmed_prices)} árból)")
        else:
            print("❌ Nincsenek visszaigazolt árak")
        
        print("\n" + "=" * 50)
        print("🎯 TESZT BEFEJEZVE")
        
    except Exception as e:
        print(f"💥 KRITIKUS HIBA: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_price_api()